<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.ThirdFeePaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tyt.mybatis.mapper.model.ThirdFeePaymentDO">
        <id column="id" property="id" />
        <result column="fee_apply_id" property="feeApplyId" />
        <result column="order_id" property="orderId" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="third_biz_no" property="thirdBizNo" />
        <result column="payment_status" property="paymentStatus" />
        <result column="paid_time" property="paidTime" />
        <result column="total_amount" property="totalAmount" />
        <result column="addition_amount" property="additionAmount" />
        <result column="driver_amount" property="driverAmount" />
        <result column="invoice_service_code" property="invoiceServiceCode" />
        <result column="fee_type" property="feeType" />
        <result column="comment" property="comment" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fee_apply_id, order_id, ts_order_no, third_biz_no, payment_status, paid_time, total_amount, addition_amount, driver_amount, invoice_service_code, fee_type, comment, create_time, modify_time, del_status
    </sql>
    <select id="getPaidAmountByOrderId" resultType="java.math.BigDecimal">
        select sum(total_amount)
        from tyt_third_fee_payment
        where order_id = #{orderId}
        and payment_status = 'HAS_RECEIVED'
        and fee_type = 'PRE_PAY'
        and del_status = 0
    </select>

    <select id="getByOrderId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_third_fee_payment
        where order_id = #{orderId}
        and del_status = 0
    </select>

    <select id="getByFeeType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_third_fee_payment
        where order_id = #{orderId}
        and fee_type = #{feeType}
        and del_status = 0
        order by id desc limit 1
    </select>
    <select id="getByThirdBizNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_third_fee_payment
        where third_biz_no = #{thirdBizNo}
        and del_status = 0 order by id desc limit 1
    </select>

</mapper>
