<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytWxUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytWxUserInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="wx_appid" jdbcType="VARCHAR" property="wxAppid" />
    <result column="wx_nick_name" jdbcType="VARCHAR" property="wxNickName" />
    <result column="wx_phone" jdbcType="VARCHAR" property="wxPhone" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="avatar_url" jdbcType="VARCHAR" property="avatarUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="share_phone" jdbcType="VARCHAR" property="sharePhone" />
  </resultMap>

  <select id="getByWxPhone" resultMap="BaseResultMap">
    select
    *
    from tyt_wx_user_info where wx_phone = #{phone} order by id desc limit 1
  </select>
</mapper>