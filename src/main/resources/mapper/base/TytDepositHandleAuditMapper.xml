<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytDepositHandleAuditMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytDepositHandleAudit">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_audit_id" jdbcType="BIGINT" property="applyAuditId" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_stage" jdbcType="INTEGER" property="auditStage" />
    <result column="audit_opinion" jdbcType="VARCHAR" property="auditOpinion" />
    <result column="audit_user_id" jdbcType="BIGINT" property="auditUserId" />
    <result column="audit_user_name" jdbcType="VARCHAR" property="auditUserName" />
    <result column="audit_cell_phone" jdbcType="VARCHAR" property="auditCellPhone" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <select id="getDepositHandleAuditList" resultType="com.tyt.manager.bean.DepositHandleAudit">
    SELECT
    a.id as id,
    a.user_id as userId,
    a.ex_id as exId,
    a.user_apply_time userApplyTime,
    a.customer_service_apply_time customerServiceApplyTime,
    a.ts_order_no tsOrderNo,
    a.pub_cell_phone pubCellPhone,
    a.pay_cell_phone payCellPhone,
    a.refund_amount refundAmount,
    a.refund_reason refundReason,
    a.ensure_amount_type ensureAmountType,
    a.handle_status handleStatus,
    a.final_audit_status finalAuditStatus,
    a.ctime ctime,
    a.mtime mtime,
    a.audit_status auditStatus,
    a.audit_stage auditStage,
    a.remark remark,
    a.remark_operate_user_name remarkOperateUserName,
    a.remark_operate_id remarkOperateId,
    a.remark_operate_time remarkOperateTime,
    a.refund_apply_num refundApplyNum,
    a.already_deduct_num alreadyDeductNum,
    b.audit_opinion auditOpinion,
    b.audit_user_id auditUserId,
    b.audit_user_name auditUserName,
    b.audit_cell_phone auditCellPhone,
    b.audit_time auditTime,
    d.ctime as userFirstPayDepositTime
    FROM
    tyt_deposit_apply_audit a
    LEFT JOIN ( SELECT * FROM ( SELECT tdha.* FROM tyt_deposit_handle_audit tdha ORDER BY tdha.audit_time DESC ) c GROUP BY c.apply_audit_id ) b ON a.id = b.apply_audit_id
    LEFT JOIN (select * from tyt_deposit_flow group by user_id order by ctime asc) d ON d.user_id=a.user_id
    WHERE
    1 = 1
    AND (a.final_audit_status is null or a.final_audit_status in (0,1))
    <if test="userId !=null">
      and a.user_id=#{userId}
    </if>
    <if test="userApplyStartTimeStr !=null and userApplyStartTimeStr !='' ">
      and a.user_apply_time >= #{userApplyStartTimeStr}
    </if>
    <if test="userApplyEndTimeStr !=null and userApplyEndTimeStr !='' ">
      and a.user_apply_time <![CDATA[ <= ]]> #{userApplyEndTimeStr}
    </if>
    <if test="customerServiceApplyStartTimeStr !=null and customerServiceApplyStartTimeStr !='' ">
      and a.customer_service_apply_time >= #{customerServiceApplyStartTimeStr}
    </if>
    <if test="customerServiceApplyEndTimeStr !=null and customerServiceApplyEndTimeStr !=''">
      and a.customer_service_apply_time <![CDATA[ <= ]]> #{customerServiceApplyEndTimeStr}
    </if>
    <if test="auditStage !=null and auditStatus != null">
      and a.audit_stage=#{auditStage}
      and a.audit_status=#{auditStatus}
    </if>
    <if test="handleStatus !=null">
      and a.handle_status=#{handleStatus}
    </if>
    <if test="tsOrderNo !=null and tsOrderNo !='' ">
      and a.ts_order_no=#{tsOrderNo}
    </if>
    <if test="pubCellPhone !=null and pubCellPhone !='' ">
      and a.pub_cell_phone=#{pubCellPhone}
    </if>
    ORDER BY
    a.mtime DESC,b.mtime DESC
  </select>

  <select id="getDetailByExId" resultType="com.tyt.manager.bean.DepositHandleAudit">
    SELECT
    a.id as id,
    a.user_id as userId,
    a.ex_id as exId,
    a.user_apply_time userApplyTime,
    a.customer_service_apply_time customerServiceApplyTime,
    a.ts_order_no tsOrderNo,
    a.pub_cell_phone pubCellPhone,
    a.pay_cell_phone payCellPhone,
    a.refund_amount refundAmount,
    a.refund_reason refundReason,
    a.ensure_amount_type ensureAmountType,
    a.handle_status handleStatus,
    a.final_audit_status finalAuditStatus,
    a.ctime ctime,
    a.mtime mtime,
    a.audit_status auditStatus,
    a.audit_stage auditStage,
    b.audit_opinion auditOpinion,
    b.audit_user_id auditUserId,
    b.audit_user_name auditUserName,
    b.audit_cell_phone auditCellPhone,
    b.audit_time auditTime
    FROM
    tyt_deposit_apply_audit a
    LEFT JOIN ( SELECT * FROM ( SELECT tdha.* FROM tyt_deposit_handle_audit tdha ORDER BY tdha.audit_time DESC ) c GROUP BY c.apply_audit_id ) b ON a.id = b.apply_audit_id
    AND a.audit_stage = b.audit_stage
    WHERE
    1 = 1
      and a.ex_id = #{exId}
    ORDER BY
    a.mtime DESC,b.mtime DESC limit 1
  </select>
</mapper>