<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytTransportOrderFollowRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytTransportOrderFollowRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="ts_order_no" jdbcType="VARCHAR" property="tsOrderNo" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="del_status" jdbcType="TINYINT" property="delStatus" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>


  <select id="listByOrderIds"
          resultMap="BaseResultMap" parameterType="java.util.List">
    select * from tyt_transport_order_follow_record where type = 1 and order_id  in
    <foreach item="item" collection="orderIdList" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>