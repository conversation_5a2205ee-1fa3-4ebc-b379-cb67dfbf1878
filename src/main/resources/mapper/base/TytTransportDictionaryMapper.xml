<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytTransportDictionaryMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytTransportDictionary">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="rate" jdbcType="INTEGER" property="rate" />
    <result column="correct_word" jdbcType="VARCHAR" property="correctWord" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap" resultType="com.tyt.manager.entity.base.TytTransportDictionary">
    select
      * from tyt_transport_dictionary where status = 1
    <if test="keyword != null and keyword != ''">
      and keyword  like CONCAT('%',#{keyword},'%')
    </if>
      order by id desc
      limit #{page},#{size}
  </select>
  <update id="updateDeleteById">
    update tyt_transport_dictionary set status = 0
    where id = #{id}
  </update>

  <select id="getKeyWord" resultMap="BaseResultMap" resultType="com.tyt.manager.entity.base.TytTransportDictionary">
    select
    * from tyt_transport_dictionary where
    <if test="status != null">
      <if test="status != 1">
        status = 0
      </if>
      <if test="status == 1">
        status = 1
      </if>
    </if>

    and keyword = #{keyword} limit 1
  </select>
  <select id="getByid" resultMap="BaseResultMap" resultType="com.tyt.manager.entity.base.TytTransportDictionary">
    select
      id,keyword,rate,correct_word,status,create_time from tyt_transport_dictionary where id = #{id}
  </select>

  <select id="total"  resultType="java.lang.Integer">
    select
      count(0) from tyt_transport_dictionary where status = 1
        <if test="keyword != null and keyword != ''">
           and keyword  like CONCAT('%',#{keyword},'%')
        </if>
  </select>
</mapper>