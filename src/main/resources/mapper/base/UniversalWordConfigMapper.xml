<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.UniversalWordConfigMapper">

    <resultMap id="BaseResultMap" type="com.tyt.universalWordConfig.bean.UniversalWordConfigInfo">
        <id column="id" property="id" jdbcType="BIGINT"></id>
        <result column="title" property="title" jdbcType="VARCHAR"></result>
        <result column="site" property="site" jdbcType="INTEGER"></result>
        <result column="code" property="code" jdbcType="VARCHAR"></result>
        <result column="content" property="content" jdbcType="VARCHAR"></result>
        <result column="remark" property="remark" jdbcType="VARCHAR"></result>
        <result column="type" property="type" jdbcType="INTEGER"></result>
        <result column="delete_status" property="deleteStatus" jdbcType="INTEGER"></result>
        <result column="status" property="status" jdbcType="INTEGER"></result>
        <result column="modify_user_id" property="modifyUserId" jdbcType="BIGINT"></result>
        <result column="modify_user_name" property="modifyUserName" jdbcType="VARCHAR"></result>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"></result>
        <result column="mtime" property="mtime" jdbcType="TIMESTAMP"></result>
    </resultMap>

    <select id="getUniversalWordConfigInfoList" resultMap="BaseResultMap" parameterType="com.tyt.universalWordConfig.bean.UniversalWordConfigInfoReq">
        select id, title, site, code, content, remark, type, delete_status, status, modify_user_id, modify_user_name, ctime, mtime
        from tyt_universal_word_config tuwc
        where delete_status = 0
        <if test="req.id != null">
            and tuwc.id = #{req.id}
        </if>
        <if test="req.title != null and req.title != ''">
            and tuwc.title like CONCAT('%',#{req.title},'%')
        </if>
        <if test="req.content != null and req.content != ''">
            and tuwc.content like CONCAT('%',#{req.content},'%')
        </if>
        <if test="req.code != null and req.code != ''">
            and tuwc.code like CONCAT('%',#{req.code},'%')
        </if>
        <if test="req.deleteStatus != null">
            and tuwc.delete_status = #{req.deleteStatus}
        </if>
        <if test="req.status != null">
            and tuwc.status = #{req.status}
        </if>
        order by mtime desc
    </select>

    <select id="getUniversalWordConfigInfoByid" resultMap="BaseResultMap" parameterType="long">
        select id, title, site, code, content, remark, type, delete_status, status, modify_user_id, modify_user_name, ctime, mtime
        from tyt_universal_word_config where id = #{universalWordConfigId} and delete_status = 0
    </select>

    <update id="updateUniversalWordConfigById" parameterType="com.tyt.universalWordConfig.bean.UniversalWordConfigInfo">
        update tyt_universal_word_config set
             site = #{universalWordConfigInfo.site},
             content = #{universalWordConfigInfo.content},
             remark = #{universalWordConfigInfo.remark},
             modify_user_id = #{universalWordConfigInfo.modifyUserId},
             modify_user_name = #{universalWordConfigInfo.modifyUserName},
             mtime = now()
        where id = #{universalWordConfigInfo.id}
    </update>

</mapper>