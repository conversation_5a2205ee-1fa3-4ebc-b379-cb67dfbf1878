<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.UserNegativeLabelConfigMapper">

    <resultMap id="BaseResultMap" type="com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigInfo">
        <id column="id" property="id" jdbcType="BIGINT"></id>
        <result column="user_id" property="userId" jdbcType="BIGINT"></result>
        <result column="user_phone" property="userPhone" jdbcType="VARCHAR"></result>
        <result column="show_feedback_label_id" property="showFeedbackLabelId" jdbcType="BIGINT"></result>
        <result column="is_show" property="isShow" jdbcType="INTEGER"></result>
        <result column="modify_user_id" property="modifyUserId" jdbcType="BIGINT"></result>
        <result column="modify_user_name" property="modifyUserName" jdbcType="VARCHAR"></result>
        <result column="create_time" property="createTime" jdbcType="DATE"></result>
        <result column="update_time" property="updateTime" jdbcType="DATE"></result>
        <result column="user_type" property="userType" jdbcType="INTEGER"></result>
    </resultMap>

    <resultMap id="VOResultMap" type="com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigInfoVO">
        <id column="id" property="id" jdbcType="BIGINT"></id>
        <result column="user_id" property="userId" jdbcType="BIGINT"></result>
        <result column="user_phone" property="userPhone" jdbcType="VARCHAR"></result>
        <result column="show_feedback_label_id" property="showFeedbackLabelId" jdbcType="BIGINT"></result>
        <result column="is_show" property="isShow" jdbcType="INTEGER"></result>
        <result column="modify_user_id" property="modifyUserId" jdbcType="BIGINT"></result>
        <result column="modify_user_name" property="modifyUserName" jdbcType="VARCHAR"></result>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"></result>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"></result>
        <result column="user_type" property="userType" jdbcType="INTEGER"></result>
        <collection property="feedbackLabelCountInfoCountList" javaType="java.util.ArrayList" ofType="com.tyt.feedback.label.bean.FeedbackLabelCountVO">
            <id column="label_id" property="id" jdbcType="BIGINT"></id>
            <result column="label_name" property="labelName" jdbcType="VARCHAR"></result>
            <result column="count" property="count" jdbcType="INTEGER"></result>
        </collection>
    </resultMap>

    <select id="getUserNegativeLabelConfigInfoVOListCount" resultType="integer" parameterType="com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigReq">
        select count(1)
        from (
            select tab.*
            from (
                select funlc.id, funlc.user_id, funlc.user_phone, funlc.show_feedback_label_id, funlc.is_show, funlc.modify_user_id, funlc.modify_user_name, funlc.create_time, funlc.update_time
                , ful_count.label_id, ful_count.label_name, ful_count.count, funlc.user_type from feedback_user_negative_label_config funlc
                left join (select ful.receive_user_id, fl.id as label_id, fl.label_name, count(1) as count from feedback_user_label ful left join feedback_label fl on ful.label_id = fl.id
                where ful.del_flag = 0 and fl.feedback_type = 3 and ful.create_time >= date_sub(NOW(), interval 1 YEAR) group by ful.receive_user_id, ful.label_id order by count desc) ful_count
                on funlc.user_id = ful_count.receive_user_id
                where 1 = 1
                <if test="req.id != null">
                    and funlc.id = #{req.id}
                </if>
                <if test="req.userId != null">
                    and funlc.user_id = #{req.userId}
                </if>
                <if test="req.userPhone != null and req.userPhone != ''">
                    and funlc.user_phone = #{req.userPhone}
                </if>
                <if test="req.isShow != null">
                    and funlc.is_show = #{req.isShow}
                </if>
                <if test="req.count != null">
                    and ful_count.count >= #{req.count}
                </if>
                <if test="req.userType != null">
                    and funlc.user_type = #{req.userType}
                </if>
                order by funlc.create_time desc
            ) tab group by tab.user_id
        ) tab_new
    </select>

    <select id="getUserNegativeLabelConfigInfoVOGroupByUserIdList" resultMap="BaseResultMap" parameterType="com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigReq">
        select tab.user_id as user_id
        from (
            select funlc.id, funlc.user_id, funlc.user_phone, funlc.show_feedback_label_id, funlc.is_show, funlc.modify_user_id, funlc.modify_user_name, funlc.create_time, funlc.update_time
            , ful_count.label_id, ful_count.label_name, ful_count.count, funlc.user_type from feedback_user_negative_label_config funlc
            left join (select ful.receive_user_id, fl.id as label_id, fl.label_name, count(1) as count from feedback_user_label ful left join feedback_label fl on ful.label_id = fl.id
            where ful.del_flag = 0 and fl.feedback_type = 3 and ful.create_time >= date_sub(NOW(), interval 1 YEAR) group by ful.receive_user_id, ful.label_id order by count desc) ful_count
            on funlc.user_id = ful_count.receive_user_id
            where 1 = 1
            <if test="req.id != null">
                and funlc.id = #{req.id}
            </if>
            <if test="req.userId != null">
                and funlc.user_id = #{req.userId}
            </if>
            <if test="req.userPhone != null and req.userPhone != ''">
                and funlc.user_phone = #{req.userPhone}
            </if>
            <if test="req.isShow != null">
                and funlc.is_show = #{req.isShow}
            </if>
            <if test="req.count != null">
                and ful_count.count >= #{req.count}
            </if>
            <if test="req.userType != null">
                and funlc.user_type = #{req.userType}
            </if>
            order by funlc.create_time desc
        ) tab group by tab.user_id limit #{req.startRowNum}, #{req.pageSize}
    </select>

    <select id="getUserNegativeLabelConfigInfoVOList" resultMap="VOResultMap" parameterType="com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigReq">
        select funlc.id, funlc.user_id, funlc.user_phone, funlc.show_feedback_label_id, funlc.is_show, funlc.modify_user_id, funlc.modify_user_name, funlc.create_time, funlc.update_time
             , ful_count.label_id, ful_count.label_name, ful_count.count, funlc.user_type from feedback_user_negative_label_config funlc
            left join (select ful.receive_user_id, fl.id as label_id, fl.label_name, count(1) as count from feedback_user_label ful left join feedback_label fl on ful.label_id = fl.id
                        where ful.del_flag = 0 and fl.feedback_type = 3 and ful.create_time >= date_sub(NOW(), interval 1 YEAR) group by ful.receive_user_id, ful.label_id order by count desc) ful_count
            on funlc.user_id = ful_count.receive_user_id
        where 1 = 1
        <if test="req.id != null">
            and funlc.id = #{req.id}
        </if>
        and funlc.user_id in (
            <foreach collection="req.userIdList" item="userId" separator=",">
                #{userId}
            </foreach>
        )
        <if test="req.userPhone != null and req.userPhone != ''">
            and funlc.user_phone = #{req.userPhone}
        </if>
        <if test="req.isShow != null">
            and funlc.is_show = #{req.isShow}
        </if>
        <if test="req.count != null">
            and ful_count.count >= #{req.count}
        </if>
        <if test="req.userType != null">
            and funlc.user_type = #{req.userType}
        </if>
        order by funlc.create_time desc
    </select>

    <select id="getUserNegativeLabelConfigInfoById" resultMap="BaseResultMap" parameterType="long">
        select funlc.id, funlc.user_id, funlc.user_phone, funlc.show_feedback_label_id, funlc.is_show, funlc.modify_user_id, funlc.modify_user_name, funlc.create_time, funlc.update_time
        from feedback_user_negative_label_config funlc where id = #{id}
    </select>

    <update id="updateUserNegativeLabelConfigIsShowById">
        update feedback_user_negative_label_config set is_show = #{isShow}, update_time = now() where id = #{id}
    </update>

    <update id="updateUserNegativeLabelConfigShowFeedbackLabelIdById">
        update feedback_user_negative_label_config set show_feedback_label_id = #{showFeedbackLabelId}, update_time = now() where id = #{id}
    </update>

    <select id="getPhoneNumById" resultType="java.lang.String" parameterType="long">
        select user_phone from feedback_user_negative_label_config where id = #{id}
    </select>

    <update id="updateUserNegativeLabelConfigBatch">
        update feedback_user_negative_label_config set is_show = #{isShow}, show_feedback_label_id = #{showFeedbackLabelId}, update_time = now() where id = #{id}
    </update>

</mapper>