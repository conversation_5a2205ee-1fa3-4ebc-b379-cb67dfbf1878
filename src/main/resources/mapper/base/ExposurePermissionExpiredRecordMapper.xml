<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.ExposurePermissionExpiredRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.ExposurePermissionExpiredRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="expired_before_num" jdbcType="INTEGER" property="expiredBeforeNum" />
    <result column="expired_num" jdbcType="INTEGER" property="expiredNum" />
    <result column="expired_after_num" jdbcType="INTEGER" property="expiredAfterNum" />
    <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime" />
  </resultMap>
    <select id="getExposurePermissionExpiredRecord"
            resultType="com.tyt.exposurePermission.vo.ExposurePermissionRecordVo">
      SELECT id,user_id userId, expired_num num,expired_time ctime,'' AS remark FROM
        exposure_permission_expired_record WHERE user_id = #{userId} AND expired_time &gt;=#{startTime} AND expired_time &lt;= #{endTime} order by id desc limit #{pageNumber},#{pageSize}
    </select>
</mapper>