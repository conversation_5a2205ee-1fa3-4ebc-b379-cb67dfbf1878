<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.PromoCouponWriteoffMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.PromoCouponWriteoff">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="coupon_id" jdbcType="INTEGER" property="couponId" />
    <result column="coupon_name" jdbcType="VARCHAR" property="couponName" />
    <result column="coupon_type_id" jdbcType="INTEGER" property="couponTypeId" />
    <result column="coupon_amount" jdbcType="DECIMAL" property="couponAmount" />
    <result column="coupon_status" jdbcType="TINYINT" property="couponStatus" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>

  <select id="getCouponWriteOffByOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select id, user_id, order_id, order_amount, coupon_id,coupon_name,coupon_type_id,coupon_amount,coupon_status,mtime,ctime from promo_coupon_writeoff where order_id = #{orderId} and coupon_status=2 order by ctime desc limit 1
  </select>

  <select id="getCouponWriteOffByCouponId" parameterType="long" resultMap="BaseResultMap">
    select id, user_id, order_id, order_amount, coupon_id,coupon_name,coupon_type_id,coupon_amount,coupon_status,mtime,ctime from promo_coupon_writeoff where coupon_id = #{couponId} and coupon_status=2 order by ctime desc limit 1
  </select>

  <select id="listPromoCouponWriteoffByOrderIdList" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT id, user_id, order_id, order_amount, coupon_id, coupon_name, coupon_type_id,
    coupon_amount, coupon_status, mtime, ctime
    FROM promo_coupon_writeoff
    WHERE order_id IN
      <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
        #{item}
      </foreach>
    AND coupon_status = 2
  </select>

</mapper>