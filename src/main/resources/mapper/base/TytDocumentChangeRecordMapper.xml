<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytDocumentChangeRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytDocumentChangeRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="document_type" jdbcType="VARCHAR" property="documentType" />
    <result column="before_url" jdbcType="VARCHAR" property="beforeUrl" />
    <result column="after_url" jdbcType="VARCHAR" property="afterUrl" />
    <result column="modify_id" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
</mapper>