<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.ExcellentGoodsGroupUserMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.ExcellentGoodsGroupUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="param">
    id,
    user_id as userId,
    user_name as userName,
    group_id as groupId,
    ctime,
    operate_name as operateName
  </sql>
  <select id="findById"  resultType="com.tyt.manager.entity.base.ExcellentGoodsGroupUser">
    select group_id from excellent_goods_group_user where user_id=#{userId} and is_del = 1
  </select>

  <delete id="deleteByIds">
      delete from excellent_goods_group_user where  user_id in
      <foreach collection="ids" item="id" separator="," open="(" close=")">
        #{id}
      </foreach>
  </delete>

  <select id="findByIds" resultType="com.tyt.manager.entity.base.ExcellentGoodsGroupUser">
    select
    group_id as groupId,user_id as userId
    from excellent_goods_group_user
    where is_del = 1 and user_id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="findAllByCondition" parameterType="com.tyt.manager.bean.ExcellentGoodsUserBean" resultType="com.tyt.manager.entity.base.ExcellentGoodsGroupUser">
     select
     <include refid="param"></include>
      from excellent_goods_group_user
      <where>
        <if test='userId != null'>
          user_id = #{userId}
        </if>
        <if test='beginTime != null'>
          AND ctime <![CDATA[ >= ]]> #{beginTime}
       </if>
        <if test='beginTime != null and endTime != null'>
          AND ctime between #{beginTime} and #{endTime}
        </if>
        <if test='groupId != null'>
         and  group_id = #{groupId}
        </if>
      </where>
      <if test='pageSize != null or pageSize != 0'>
          limit #{pageIndex},#{pageSize}
      </if>
  </select>

    <select id="selectTotal" resultType="java.lang.Integer">
        SELECT count(id) from excellent_goods_group_user where group_id=#{groupId}
    </select>
</mapper>