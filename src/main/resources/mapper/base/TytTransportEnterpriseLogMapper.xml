<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytTransportEnterpriseLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytTransportEnterpriseLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="legal_person_phone" jdbcType="VARCHAR" property="legalPersonPhone" />
    <result column="legal_person_card" jdbcType="VARCHAR" property="legalPersonCard" />
    <result column="legal_person_card_url_g" jdbcType="VARCHAR" property="legalPersonCardUrlG" />
    <result column="legal_person_card_url_t" jdbcType="VARCHAR" property="legalPersonCardUrlT" />
    <result column="enterprise_credit_code" jdbcType="VARCHAR" property="enterpriseCreditCode" />
    <result column="enterprise_type" jdbcType="VARCHAR" property="enterpriseType" />
    <result column="enterprise_business_scope" jdbcType="VARCHAR" property="enterpriseBusinessScope" />
    <result column="enterprise_home_address" jdbcType="VARCHAR" property="enterpriseHomeAddress" />
    <result column="enterprise_detail_address" jdbcType="VARCHAR" property="enterpriseDetailAddress" />
    <result column="license_url" jdbcType="VARCHAR" property="licenseUrl" />
    <result column="license_start_time" jdbcType="TIMESTAMP" property="licenseStartTime" />
    <result column="license_end_time" jdbcType="TIMESTAMP" property="licenseEndTime" />
    <result column="transport_license_url" jdbcType="VARCHAR" property="transportLicenseUrl" />
    <result column="sign_type" jdbcType="INTEGER" property="signType" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="contract_start_time" jdbcType="TIMESTAMP" property="contractStartTime" />
    <result column="contract_end_time" jdbcType="TIMESTAMP" property="contractEndTime" />
    <result column="certigier_user_id" jdbcType="BIGINT" property="certigierUserId" />
    <result column="certigier_user_name" jdbcType="VARCHAR" property="certigierUserName" />
    <result column="certigier_user_phone" jdbcType="VARCHAR" property="certigierUserPhone" />
    <result column="enterprise_tax_rate" jdbcType="DECIMAL" property="enterpriseTaxRate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="invoice_subject_id" jdbcType="BIGINT" property="invoiceSubjectId" />
    <result column="service_provider_code" jdbcType="VARCHAR" property="serviceProviderCode" />
    <result column="assign_car_tel" jdbcType="VARCHAR" property="assignCarTel" />
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
    <result column="consignee_tel" jdbcType="VARCHAR" property="consigneeTel" />
    <result column="consignee_enterprise_name" jdbcType="VARCHAR" property="consigneeEnterpriseName" />
    <result column="prepaid_price" jdbcType="DECIMAL" property="prepaidPrice" />
    <result column="collected_price" jdbcType="DECIMAL" property="collectedPrice" />
    <result column="receipt_price" jdbcType="DECIMAL" property="receiptPrice" />
  </resultMap>
  <select id="queryEnterpriseLogBySrcMsgId" resultMap="BaseResultMap">
    select * from tyt_transport_enterprise_log where src_msg_id = #{srcMsgId} order by id desc limit 1
  </select>
</mapper>