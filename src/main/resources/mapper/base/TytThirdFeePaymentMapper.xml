<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytThirdFeePaymentMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytThirdFeePayment">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fee_apply_id" jdbcType="BIGINT" property="feeApplyId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="ts_order_no" jdbcType="VARCHAR" property="tsOrderNo" />
    <result column="third_biz_no" jdbcType="VARCHAR" property="thirdBizNo" />
    <result column="payment_status" jdbcType="VARCHAR" property="paymentStatus" />
    <result column="paid_time" jdbcType="TIMESTAMP" property="paidTime" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="addition_amount" jdbcType="DECIMAL" property="additionAmount" />
    <result column="driver_amount" jdbcType="DECIMAL" property="driverAmount" />
    <result column="invoice_service_code" jdbcType="VARCHAR" property="invoiceServiceCode" />
    <result column="fee_type" jdbcType="VARCHAR" property="feeType" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="del_status" jdbcType="TINYINT" property="delStatus" />
  </resultMap>

  <select id="getByOrderId" resultMap="BaseResultMap">
    select *
    from tyt_third_fee_payment
    where order_id = #{orderId}
    and del_status = 0
  </select>
</mapper>