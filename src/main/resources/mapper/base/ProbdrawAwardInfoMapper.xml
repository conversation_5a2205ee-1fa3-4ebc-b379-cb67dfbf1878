<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.ProbdrawAwardInfoMapper">

    <select id="getRelationActivityList" resultType="com.tyt.probdraw.bean.RelationActivityListVO">
        SELECT
            ai.`id` AS activityId,
            ai.activity_name AS activityName
        FROM draw_activity_info ai
        WHERE ai.is_delete = 1
        ORDER BY ai.`id` DESC
    </select>

    <select id="getAwardInfoList" resultType="com.tyt.probdraw.bean.ProbAwardListVO" parameterType="java.lang.Integer">
        SELECT
            pc.id AS probCouponId,
            pc.draw_activity_info_id AS drawActivityInfoId,
            pc.promo_activity_id AS promoActivityId,
            pc.activity_name AS activityName,
            pc.award_type AS awardType,
            pc.award_prob AS awardProb,
            pc.award_total_amount AS awardTotalAmount,
            pc.award_used_amount AS awardUsedAmount,
            pc.sort AS sort
        FROM prob_coupon pc
        WHERE pc.is_delete = 0
        <if test="null != activityId">
          AND pc.draw_activity_info_id = #{activityId}
        </if>
        ORDER BY pc.sort
    </select>

    <select id="getAwardNameList" resultType="com.tyt.probdraw.bean.AwardNameListVO">
        SELECT
            pa.award_id AS promoActivityId,
            pa.award_name AS activityName,
            pa.award_type AS awardType
        FROM
            lottery_prize pa
    </select>

    <insert id="saveProbCouponInfo">
        INSERT INTO prob_coupon (
            draw_activity_info_id,
            promo_activity_id,
            activity_name,
            award_type,
            award_prob,
            award_total_amount,
            award_used_amount,
            sort,
            create_by,
            create_time,
            update_by,
            update_time
        )
        VALUES
        <foreach collection="probCoupons" item="probCoupon" separator=",">
            (
            #{probCoupon.drawActivityInfoId},
            #{probCoupon.promoActivityId},
            #{probCoupon.activityName},
            #{probCoupon.awardType},
            #{probCoupon.awardProb},
            #{probCoupon.awardTotalAmount},
            #{probCoupon.awardUsedAmount},
            #{probCoupon.sort},
            #{probCoupon.createBy},
            #{probCoupon.createTime},
            #{probCoupon.updateBy},
            #{probCoupon.updateTime}
            )
        </foreach>
    </insert>

    <update id="updateProbCouponInfo" parameterType = "com.tyt.model.ProbCoupon">
        update prob_coupon
        <set>
            <if test="null != probCoupon.drawActivityInfoId">
                draw_activity_info_id = #{probCoupon.drawActivityInfoId},
            </if>
            <if test="null != probCoupon.promoActivityId">
                promo_activity_id = #{probCoupon.promoActivityId},
            </if>
            <if test="null != probCoupon.activityName and '' != probCoupon.activityName">
                activity_name = #{probCoupon.activityName},
            </if>
            <if test="null != probCoupon.awardType">
                award_type = #{probCoupon.awardType},
            </if>
            <if test="null != probCoupon.awardProb">
                award_prob = #{probCoupon.awardProb},
            </if>
            <if test="null != probCoupon.awardTotalAmount">
                award_total_amount = #{probCoupon.awardTotalAmount},
            </if>
            <if test="null != probCoupon.updateTime">
                update_time = #{probCoupon.updateTime},
            </if>
            <if test="null != probCoupon.updateBy">
                update_by = #{probCoupon.updateBy},
            </if>
        </set>
        where id = #{probCoupon.id}
    </update>

</mapper>