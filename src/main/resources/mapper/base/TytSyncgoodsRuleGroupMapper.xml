<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytSyncgoodsRuleGroupMapper">

    <select id="selectSyncgoodsRuleList" resultType="com.tyt.web.back.internal.bean.SyncgoodsRuleList">
        SELECT
            tsr.id,
            tsr.rule_name AS ruleName,
            tsr.ctime,
            tsr.`status`,
            ( SELECT count(*) FROM tyt_syncgoods_user WHERE is_delete = 0 AND rule_group_id = tsr.id ) AS userNum,
            ( SELECT group_concat(excellent_goods, '-',publish_type) FROM tyt_syncgoods_rule WHERE is_delete = 0
            AND status =1 AND rule_group_id = tsr.id group by rule_group_id ) AS goodsType
        FROM tyt_syncgoods_rule_group tsr
        WHERE tsr.is_delete = 0
    </select>

    <select id="selectRuleByRuleName" resultType="com.tyt.manager.entity.base.TytSyncgoodsRuleGroup">
        select * from tyt_syncgoods_rule_group where is_delete = 0 and rule_name = #{ruleName,jdbcType=VARCHAR}
    </select>

    <select id="countRule" resultType="int">
        select count(*) from tyt_syncgoods_rule_group where is_delete = 0
    </select>
</mapper>
