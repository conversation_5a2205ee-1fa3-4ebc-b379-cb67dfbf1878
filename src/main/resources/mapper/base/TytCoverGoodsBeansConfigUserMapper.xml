<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytCoverGoodsBeansConfigUserMapper">
    <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytCoverGoodsBeansConfigUser">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="total_num" jdbcType="INTEGER" property="totalNum"/>
        <result column="used_num" jdbcType="INTEGER" property="usedNum"/>
        <result column="beans_name" jdbcType="VARCHAR" property="beansName"/>
        <result column="validate_time" jdbcType="TIMESTAMP" property="validateTime"/>
        <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId"/>
        <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="beans_status" jdbcType="INTEGER" property="beansStatus"/>
        <result column="dial_config_id" jdbcType="BIGINT" property="dialConfigId"/>
    </resultMap>

    <select id="selectByConfigIdAndUserIdOrderByUpdateDesc" resultMap="BaseResultMap">
        select *
        from tyt_cover_goods_beans_config_user where dial_config_id = #{configId}
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        ORDER BY update_time DESC,
        id DESC
    </select>

    <delete id="deleteByConfigIdAndUserIds">
        delete
        from tyt_cover_goods_beans_config_user
        where dial_config_id = #{configId}
        <if test="userIds != null and userIds.size() != 0">
            and user_id in
            <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
    </delete>


    <select id="selectByConfigIdAndUserIdOrderByUpdateAsc" resultMap="BaseResultMap">
        select *
        from tyt_cover_goods_beans_config_user where dial_config_id = #{configId}
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        ORDER BY update_time ASC,
        id ASC
    </select>


    <select id="selectUserIdByConfigIdAndIdStart" resultMap="BaseResultMap">
        select id, user_id
        from tyt_cover_goods_beans_config_user
        where dial_config_id = #{configId}
          and id > #{idStart}
        order by id asc limit #{limit}
    </select>

    <select id="selectTotalBeansLeftNumByUserId"  resultType="com.tyt.covergoods.bean.resp.CoverGoodsUserBeansVO">
        select user_id as userId, sum(left_num) as totalLeftNum
        from tyt_cover_goods_beans_config_user
        where user_id = #{userId}
          and beans_status = 1
    </select>

    <update id="updateLeftNumByUserIdsAndConfigId">
        update tyt_cover_goods_beans_config_user set left_num=total_num where dial_config_id = #{configId}
        <if test="userIds != null and userIds.size() != 0">
            and user_id in
            <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
    </update>

    <update id="updateLeftNumByUserIdAndConfigId">
        update tyt_cover_goods_beans_config_user set left_num=total_num where dial_config_id = #{configId} and user_id=#{userId}
    </update>

    <select id="selectTotalBeansLeftNumByUserIds" resultType="com.tyt.covergoods.bean.resp.CoverGoodsUserBeansVO">
        select user_id as userId,sum(left_num) as totalLeftNum from tyt_cover_goods_beans_config_user where
        beans_status=1
        <if test="userIds != null and userIds.size() != 0">
            and user_id in
            <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        group by user_id
    </select>

</mapper>