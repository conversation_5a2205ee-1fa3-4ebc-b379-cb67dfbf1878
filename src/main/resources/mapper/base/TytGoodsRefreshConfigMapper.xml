<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytGoodsRefreshConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytGoodsRefreshConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="del" jdbcType="INTEGER" property="del"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
        <result column="config_type" jdbcType="TINYINT" property="configType"/>
        <result column="excellent_goods" jdbcType="TINYINT" property="excellentGoods"/>
        <result column="goods_price_type" jdbcType="TINYINT" property="goodsPriceType"/>
        <result column="instant_grab" jdbcType="TINYINT" property="instantGrab"/>
        <result column="user_goods_type" jdbcType="VARCHAR" property="userGoodsType"/>
        <result column="effective_dimension" property="effectiveDimension" jdbcType="INTEGER" />
        <result column="extra_config" property="extraConfig" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="base_column_sql">
        id,code,name,content,status, del,create_time,create_name,modify_time,modify_name,config_type,excellent_goods,
        goods_price_type,instant_grab,user_goods_type,effective_dimension,extra_config
    </sql>

    <select id="getRefreshConfigList" resultMap="BaseResultMap">
        select c.*
        from tyt_goods_refresh_config c
        <if test="userId != null">
            join tyt_goods_refresh_user u on c.code = u.refresh_code
        </if>
        where c.del = 0
        <if test="code != null and code != ''">
            and c.code = #{code}
        </if>
        <if test="name != null and name != ''">
            and c.name like concat('%',#{name},'%')
        </if>
        <if test="status != null">
            and c.status = #{status}
        </if>
        <if test="configType != null">
            and c.config_type = #{configType}
        </if>
        <if test="excellentGoods != null">
            and c.excellent_goods = #{excellentGoods}
        </if>
        <if test="goodsPriceType != null">
            and c.goods_price_type = #{goodsPriceType}
        </if>
        <if test="instantGrab != null">
            and c.instant_grab = #{instantGrab}
        </if>
        <if test="userId != null">
            and u.user_id = #{userId}
        </if>
        <if test="excludeCode != null and excludeCode != ''">
            and c.code != #{excludeCode}
        </if>
        <if test="effectiveDimension != null">
            and c.effective_dimension = #{effectiveDimension}
        </if>
        order by c.modify_time desc
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="base_column_sql"/>
        from tyt_goods_refresh_config
        where del = 0
        and code = #{code}
    </select>

</mapper>