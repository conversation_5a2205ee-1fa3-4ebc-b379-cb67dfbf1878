<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.PcUpgradeTaskMapper">
  <resultMap id="BaseResultMap" type="com.tyt.pcupgrade.bean.PcUpgradeTask">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="upgrade_model" jdbcType="INTEGER" property="upgradeModel" />
    <result column="upgrade_type" jdbcType="INTEGER" property="upgradeType" />
    <result column="sdk_depend_version" jdbcType="VARCHAR" property="sdkDependVersion" />
    <result column="is_specific_upgrade" jdbcType="INTEGER" property="isSpecificUpgrade" />
    <result column="upgrade_version" jdbcType="VARCHAR" property="upgradeVersion" />
    <result column="upgrade_start_time" jdbcType="TIMESTAMP" property="upgradeStartTime" />
    <result column="upgrade_end_time" jdbcType="TIMESTAMP" property="upgradeEndTime" />
    <result column="upgrage_url" jdbcType="VARCHAR" property="upgrageUrl" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_name, upgrade_model, upgrade_type, sdk_depend_version, is_specific_upgrade,
    upgrade_version, upgrade_start_time, upgrade_end_time, upgrage_url, msg, task_status,
    ctime, utime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    id,
    task_name,
    upgrade_model,
    upgrade_type,
    sdk_depend_version,
    is_specific_upgrade,
    upgrade_version,
    upgrade_start_time,
    upgrade_end_time,
    upgrage_url,
    msg,
    <!--        进行中-->
    CASE WHEN upgrade_start_time <![CDATA[ <= ]]> NOW() and upgrade_end_time >= NOW() and task_status in (0, 1) THEN 1
    <!--      未开始-->
    WHEN upgrade_start_time >NOW() and task_status =0 THEN 0
    <!--      已结束-->
    WHEN upgrade_end_time <![CDATA[ < ]]> NOW() and task_status in (0,1,2) THEN 2
    <!--      已终止-->
    WHEN task_status = 3 THEN 3
    ELSE task_status END as task_status,
    ctime,
    utime
    from tyt_pc_upgrade_task
    where id = #{id,jdbcType=INTEGER} and is_delete =0
  </select>
    <select id="selectUpgradeTaskList" resultMap="BaseResultMap" parameterType="com.tyt.pcupgrade.dto.QueryCondition">
      select
      id,
      task_name,
      upgrade_model,
      upgrade_type,
      sdk_depend_version,
      is_specific_upgrade,
      upgrade_version,
      upgrade_start_time,
      upgrade_end_time,
      upgrage_url,
      msg,
      <!--        进行中-->
      CASE WHEN upgrade_start_time <![CDATA[ <= ]]> NOW() and upgrade_end_time >= NOW() and task_status in (0, 1) THEN 1
      <!--      未开始-->
      WHEN upgrade_start_time >NOW() and task_status =0 THEN 0
      <!--      已结束-->
      WHEN upgrade_end_time <![CDATA[ < ]]> NOW() and task_status in (0,1,2) THEN 2
      <!--      已终止-->
      WHEN task_status = 3 THEN 3
      ELSE task_status END as task_status,
      ctime,
      utime
      from tyt_pc_upgrade_task
      <where>
        <if test="taskName !=null and taskName !=''">
          task_name like CONCAT(#{taskName,jdbcType=VARCHAR},'%')
        </if>
        <if test="upgradeModel !=null">
          and upgrade_model = #{upgradeModel,jdbcType=INTEGER}
        </if>
        <if test="upgradeType !=null">
          and upgrade_type =#{upgradeType,jdbcType=INTEGER}
        </if>
        <if test="upgradeStartTime !=null and upgradeStartTime !=''">
          and upgrade_start_time >= #{upgradeStartTime,jdbcType=VARCHAR}
        </if>
        <if test="upgradeEndTime !=null and upgradeEndTime !=''">
          and upgrade_end_time <![CDATA[ <= ]]> #{upgradeEndTime,jdbcType=VARCHAR}
        </if>
        and is_delete =0 order by id desc
      </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    update tyt_pc_upgrade_task set is_delete =1
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.tyt.pcupgrade.bean.PcUpgradeTask" useGeneratedKeys="true" keyProperty="id">
    insert into tyt_pc_upgrade_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskName != null and taskName !=''">
        task_name,
      </if>
      <if test="upgradeModel != null">
        upgrade_model,
      </if>
      <if test="upgradeType != null">
        upgrade_type,
      </if>
      <if test="sdkDependVersion != null and sdkDependVersion !=''">
        sdk_depend_version,
      </if>
      <if test="isSpecificUpgrade != null">
        is_specific_upgrade,
      </if>
      <if test="upgradeVersion != null and upgradeVersion !=''">
        upgrade_version,
      </if>
      <if test="upgradeStartTime != null">
        upgrade_start_time,
      </if>
      <if test="upgradeEndTime != null">
        upgrade_end_time,
      </if>
      <if test="upgrageUrl != null and upgrageUrl !=''">
        upgrage_url,
      </if>
      <if test="msg != null and msg !=''">
        msg,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="utime != null">
        utime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskName != null and taskName !=''">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="upgradeModel != null">
        #{upgradeModel,jdbcType=INTEGER},
      </if>
      <if test="upgradeType != null">
        #{upgradeType,jdbcType=INTEGER},
      </if>
      <if test="sdkDependVersion != null and sdkDependVersion !=''">
        #{sdkDependVersion,jdbcType=VARCHAR},
      </if>
      <if test="isSpecificUpgrade != null">
        #{isSpecificUpgrade,jdbcType=INTEGER},
      </if>
      <if test="upgradeVersion != null and upgradeVersion !=''">
        #{upgradeVersion,jdbcType=VARCHAR},
      </if>
      <if test="upgradeStartTime != null">
        #{upgradeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="upgradeEndTime != null">
        #{upgradeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="upgrageUrl != null and upgrageUrl !=''">
        #{upgrageUrl,jdbcType=VARCHAR},
      </if>
      <if test="msg != null and msg !=''">
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        #{utime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tyt.pcupgrade.bean.PcUpgradeTask">
    update tyt_pc_upgrade_task
    <set>
      <if test="taskName != null and taskName !=''">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="upgradeModel != null">
        upgrade_model = #{upgradeModel,jdbcType=INTEGER},
      </if>
      <if test="upgradeType != null">
        upgrade_type = #{upgradeType,jdbcType=INTEGER},
      </if>
        sdk_depend_version = #{sdkDependVersion,jdbcType=VARCHAR},
      <if test="isSpecificUpgrade != null">
        is_specific_upgrade = #{isSpecificUpgrade,jdbcType=INTEGER},
      </if>
      <if test="upgradeVersion != null and upgradeVersion !=''">
        upgrade_version = #{upgradeVersion,jdbcType=VARCHAR},
      </if>
      <if test="upgradeStartTime != null">
        upgrade_start_time = #{upgradeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="upgradeEndTime != null">
        upgrade_end_time = #{upgradeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="upgrageUrl != null and upgrageUrl !=''">
        upgrage_url = #{upgrageUrl,jdbcType=VARCHAR},
      </if>
      <if test="msg != null and msg !=''">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        utime = #{utime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateTaskStatus">
    update tyt_pc_upgrade_task set task_status = #{taskStatus,jdbcType=INTEGER}
    where id = #{taskId}
  </update>

  <select id="selectTaskValidity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tyt_pc_upgrade_task
    where id =#{taskId,jdbcType=INTEGER} and task_status =1 and is_delete =0
  </select>

  <select id="selectByTaskName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tyt_pc_upgrade_task where task_name =#{taskName,jdbcType=VARCHAR} and is_delete =0
  </select>

  <select id="selectByDateInterval" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    tyt_pc_upgrade_task
    <where>
      ((upgrade_start_time >= #{upgradeStartTime,jdbcType=TIMESTAMP} and upgrade_start_time <![CDATA[ <= ]]> #{upgradeEndTime,jdbcType=TIMESTAMP})
      OR (upgrade_end_time >= #{upgradeStartTime,jdbcType=TIMESTAMP} and upgrade_end_time <![CDATA[ <= ]]>   #{upgradeEndTime,jdbcType=TIMESTAMP})
      OR (upgrade_start_time <![CDATA[ <= ]]>  #{upgradeStartTime,jdbcType=TIMESTAMP} and upgrade_end_time>= #{upgradeEndTime,jdbcType=TIMESTAMP}))
      AND task_status NOT IN ( 2, 3)
      AND upgrade_type = #{upgradeType,jdbcType=INTEGER}
      <if test="taskId !=null">
        and id !=#{taskId,jdbcType=INTEGER}
      </if>
      AND is_delete = 0
      ORDER BY
      ctime DESC
      LIMIT 1
    </where>
  </select>
</mapper>
