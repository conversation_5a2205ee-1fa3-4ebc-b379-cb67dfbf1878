<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.TytAbTestUserMapper">

    <resultMap id="BaseResultMap" type="com.tyt.mybatis.mapper.model.TytAbtestConfigUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="abtest_id" property="abtestId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="modify_employee_id" property="modifyEmployeeId" jdbcType="BIGINT"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
    </resultMap>


    <select id="getABTestUserList" resultMap="BaseResultMap" parameterType="com.tyt.abtest.bean.AbTestUserReq">
        select *
        from tyt_abtest_config_user tacu
        where tacu.abtest_id = #{req.abtestId}
        <if test="req.id != null">
            and tacu.id = #{req.id}
        </if>
        <if test="req.userId != null">
            and tacu.user_id = #{req.userId}
        </if>
        <if test="req.type != null">
            and tacu.type = #{req.type}
        </if>
        order by create_time desc
    </select>
    <select id="getByConfigAndUserId" resultMap="BaseResultMap">
        select *
        from tyt_abtest_config_user
        where abtest_id = #{abTestConfigId}
          and user_id = #{userId}
    </select>


    <delete id="delByConfigAndUser">
        delete from tyt_abtest_config_user where abtest_id = #{abTestConfigId} and user_id in (
        <foreach collection="userIds" item="userId" separator=",">
            #{userId}
        </foreach>
        )
    </delete>


</mapper>