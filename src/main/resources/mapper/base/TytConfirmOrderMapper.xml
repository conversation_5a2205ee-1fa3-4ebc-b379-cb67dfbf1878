<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytConfirmOrderMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytConfirmOrder">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="confirm_content" jdbcType="VARCHAR" property="confirmContent" />
    <result column="confirm_status" jdbcType="BIT" property="confirmStatus" />
    <result column="success_channel" jdbcType="BIT" property="successChannel" />
    <result column="confirm_channel" jdbcType="BIT" property="confirmChannel" />
    <result column="inner_trade_no" jdbcType="VARCHAR" property="innerTradeNo" />
    <result column="confirm_trade_no" jdbcType="VARCHAR" property="confirmTradeNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
</mapper>