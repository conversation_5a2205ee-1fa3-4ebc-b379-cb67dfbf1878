<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.OrderFreightMapper">


    <select id="queryById" parameterType="com.tyt.invoice.bean.ReqParam" resultType="com.tyt.mybatis.mapper.model.OrderFreightVO">
    select pay_time payTime,trade_no tradeNo,amount  from tyt_transport_orders_freight
         where order_id = #{orderId}
         <if test="opType != null">
             and confirm_flag = 1
         </if>
         order by id desc
    </select>
</mapper>
