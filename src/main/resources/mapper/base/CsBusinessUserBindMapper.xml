<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.CsBusinessUserBindMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.CsBusinessUserBind">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cs_account" jdbcType="VARCHAR" property="csAccount" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="login_phone_no" jdbcType="VARCHAR" property="loginPhoneNo" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="leader_id" jdbcType="BIGINT" property="leaderId" />
    <result column="subordinate_id" jdbcType="BIGINT" property="subordinateId" />
    <result column="is_valid" jdbcType="INTEGER" property="isValid" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="last_updater" jdbcType="BIGINT" property="lastUpdater" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="cs_department_id" jdbcType="VARCHAR" property="csDepartmentId" />
    <result column="last_updater_name" jdbcType="VARCHAR" property="lastUpdaterName" />
    <result column="small_phone" jdbcType="VARCHAR" property="smallPhone" />
    <result column="dept_top_id" jdbcType="BIGINT" property="deptTopId" />
    <result column="join_poll_order" jdbcType="INTEGER" property="joinPollOrder" />
  </resultMap>

  <select id="getActivityPrize" resultMap="BaseResultMap">
        select  id,real_name from cs_business_user_bind where role_id = 18 or role_id = 21
  </select>
</mapper>