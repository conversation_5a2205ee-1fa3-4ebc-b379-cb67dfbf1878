<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.ExposurePermissionUsedRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.ExposurePermissionUsedRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="start_point" jdbcType="VARCHAR" property="startPoint" />
    <result column="dest_point" jdbcType="VARCHAR" property="destPoint" />
    <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, user_id, src_msg_id, start_point, dest_point, task_content,ctime
  </sql>

    <select id="getExposurePermissionList"
            resultType="com.tyt.manager.entity.base.ExposurePermissionUsedRecord">
            select <include refid="Base_Column_List"></include>
            from exposure_permission_used_record where src_msg_id = #{srcMsgId} order by id desc limit #{pageNumber},#{pageSize}

    </select>
  <select id="getExposurePermissionUsedRecord"
          resultType="com.tyt.exposurePermission.vo.ExposurePermissionRecordVo">
    SELECT id,user_id userId, 1 AS num,ctime,'' AS remark FROM
    exposure_permission_used_record WHERE user_id = #{userId} AND ctime &gt;=#{startTime} AND ctime &lt;= #{endTime} order by id desc limit #{pageNumber},#{pageSize}
  </select>
</mapper>