<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytApkManageMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytApkManage">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="client_type" jdbcType="INTEGER" property="clientType" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getApkList" resultMap="BaseResultMap">
    select * from tyt_apk_manage
    where 1=1
    <if test="clientType != null and clientType != ''">
      and client_type = #{clientType,jdbcType=INTEGER}
    </if>
    <if test="version != null and version != ''">
      and version = #{version,jdbcType=VARCHAR}
    </if>
    order by id desc
  </select>

  <select id="getByVersionAndClientType" resultMap="BaseResultMap">
    select * from tyt_apk_manage where version = #{version} and client_type = #{clientType} limit 1
  </select>

  <update id="updateById">
    update tyt_apk_manage set modify_time = now(), operator = #{operator} where id = #{id}
  </update>

</mapper>