var tyt_position ="";
var tyt_oBDiv ="";
var tyt_sheng ="";
var tyt_shi ="";
var tyt_xian="" ;
var tyt_type="0" ;//0是不带市
var iframeNbr="tyt_area_iframe";
function tyt_qd(){
	document.getElementById(tyt_oBDiv).style.display="none";

}


function tyt_open_area(tyt_type2,position2,oBDiv2,sheng2,shi2,xian2,iframeNbr2){
		if(tyt_oBDiv!=''){
			document.getElementById(tyt_oBDiv).style.display="none";
		}
 tyt_position = position2;
   tyt_oBDiv = oBDiv2;
    tyt_sheng =sheng2;
     tyt_shi = shi2;
	  tyt_xian = xian2;
		tyt_type=tyt_type2;
		iframeNbr=iframeNbr2;
		setAreaValues2(document.getElementById(tyt_sheng).value,document.getElementById(tyt_shi).value,document.getElementById(tyt_xian).value);
		document.getElementById(tyt_oBDiv).style.display="block";
}

function tyt_close(){
document.getElementById(tyt_oBDiv).style.display="none";
}


function tyt_fuzhi(sheng1,shi1,xian1){
	if("请选择"!=sheng1){
		document.getElementById(tyt_sheng).value=sheng1;
		document.getElementById(tyt_shi).value='';
		document.getElementById(tyt_xian).value='';		
	}
	if("请选择"!=shi1){
		document.getElementById(tyt_shi).value=shi1;
		document.getElementById(tyt_xian).value='';
	}
	else{
		if(tyt_type=='1'){
			document.getElementById(tyt_sheng).value='';
			document.getElementById(tyt_shi).value='';
			document.getElementById(tyt_xian).value='';
		}
	}
	if("请选择"!=xian1){
		if(tyt_type=='0'){
			if(xian1!=shi1){
				document.getElementById(tyt_xian).value=xian1;
			}
		}else{
				document.getElementById(tyt_xian).value=xian1;
		}
	}
	document.getElementById(tyt_position).value=document.getElementById(tyt_sheng).value+" "+document.getElementById(tyt_shi).value+" "+document.getElementById(tyt_xian).value
	document.getElementById(tyt_oBDiv).style.display="none";
	n=false;
}
function setAreaValues2(sheng1,shi1,xian1){
	//window.tyt_area_iframe.setAreaValues(sheng1,shi1,xian1);

	$("#"+iframeNbr)[0].contentWindow.setAreaValues(sheng1,shi1,xian1);
}
