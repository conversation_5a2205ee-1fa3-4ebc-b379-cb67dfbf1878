/*var title = `<span style="color: #f33;font-size: 14px;font-weight: 600;vertical-align: middle;">田老师</span>
	<span style="vertical-align: middle;font-size: 14px;color: #333;">已取消您为"</span><a style="cursor: pointer;
	text-decoration: none;color: #3961b2;font-weight: 600;font-size: 12px;"
	 onmouseover="this.style.textDecoration='underline'" onmouseout="this.style.textDecoration='none'" 
	 onclick="openWinAuto('','checkmessage',1200,600)">200挖机马上走诚实守信</a>
	<span style="vertical-align: middle;font-size: 14px;color: #333;">"的跟单人资格</span>`;
var content = `<span style="font-size: 12px;color: #666;word-wrap: break-word;line-height: 18px;">
车主同意以12000元价格承运，需要客服帮助创建运单后支付信息费，货源15公里，"
"预计2小时后到达装货点，完成后给大家买棒棒糖, 哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈</span>`;

*/
var website = getRootPath_web();
//动画载入
function initFrame(id_index, title, content) {
	var main = `<div class="frameNews frameNews_${id_index}">
		<h2 class="frameNewsTitle">${title}</h2>
		<p class="pageContent" onclick="goToPub('${website}/employeeMessage/getList','employeeMessage/getList')">${content}</p>
		<i class="closeFrame" onclick="closeFrame($('.frameNews_${id_index}'),${id_index},0)"></i>
		</div>`;

	$('body').append(main);
	$(".frameNews_" + id_index).animate({
		'right': '20px'
	}, 600);

	closeFrame($(".frameNews_" + id_index), id_index, 10000)

}

//约定地妙后关闭对应的提示框
function closeFrame(_this, id_index, speend) {
	setTimeout(function() {
		$(".frameNews_" + id_index).animate({
			'right': '-430px'
		}, 600, function() {
			_this.remove();
		});
	}, speend)
}


function goToPub(url, thisSplit) {
	// window.location.href = url;
	$("#myiframe").attr('src', url)
	$(".tree_page_nav").find('a').removeClass('clickActive');
	$(".tree_page_nav .tree_tit").find('a').removeClass('clickActive');
	$(".tree_page_nav .tree_one").hide();
	$(".tree_page_nav").find('a').each(function(index, el) {
		var _href = $(this).attr('href');
		if ($(this).attr('href') != 'javascript:;' && _href.indexOf(thisSplit) > 0) {
			$(this).addClass('clickActive');
			$(this).addClass('color_style');
			$(this).parents('.tree_one').siblings('.tree_tit').find('a').addClass('clickActive');
			$(this).parents('.tree_one').show(400);
		}
	});
}

function getRootPath_web() {
	//获取当前网址，如： http://localhost:8083/uimcardprj/share/meun.jsp
	var curWwwPath = window.document.location.href;
	//获取主机地址之后的目录，如： uimcardprj/share/meun.jsp
	var pathName = window.document.location.pathname;
	var pos = curWwwPath.indexOf(pathName);
	//获取主机地址，如： http://localhost:8083
	var localhostPaht = curWwwPath.substring(0, pos);
	//获取带"/"的项目名，如：/uimcardprj
	var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
	return (localhostPaht + projectName);
}



//测试   --- 可删除
/*setTimeout(function(index) {
	initFrame(1, title, content);
}, 2000);
setTimeout(function(index) {
	initFrame(2, title, content);
}, 13000);*/