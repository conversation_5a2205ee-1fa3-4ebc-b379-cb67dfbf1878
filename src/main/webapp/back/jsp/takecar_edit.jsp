<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>带车信息管理-PC-编辑</title>
<link href="<%=request.getContextPath()%>/back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/detailPages.css" type="text/css" rel="stylesheet">
<script src="${pageContext.request.contextPath}/back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
	
	<div class="contentBox">

		<div class="titUrl cf">
			<h1 class=" fl">
				<span>当前所在位置：</span><span>车辆方服务</span><span>&nbsp;&gt;&nbsp;</span><span>带车信息管理-PC</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">编辑</span>
			</h1>
		</div>
		<form name="employeeForm" action="<%=request.getContextPath()%>/admin/updatetakecar" method="post">
			<input type="hidden" name="pageNo" value="<c:out value="${pageNo}"/>">
			<input type="hidden" value="${takeCar.id}" name="id" size="8"/>
			            <div class="coreDataManagement">
			            	<ul class="cf">
			            		<li>
			            			<label for="">发布日期：</label>
			            			<fmt:formatDate value="${takeCar.ctime}" type="both"/>
			            		</li>	
			            		<li>
			            			<label for="">保险广告：</label>
			            			<input class="sec_input" type="text" name="title" size="20" value="${takeCar.title}"/>
			            		</li>	
			            		<li>
			            			<label for="">联系人：</label>
			            			<input class="sec_input" type="text" name="telName" size="8" value="${takeCar.telName}">
			            		</li>	
			            		<li>
			            			<label for="">联系电话：</label>
			            			<input class="sec_input" type="text" name="telephone" size="20" value="${takeCar.telephone}">
			            		</li>	
			            		<li>
			            			<label for="">省：</label>
									<input class="sec_input" type="text" name="province" size="10" value="${takeCar.province}" />
			            		</li>	
			            		<li>
			            			<label for="">市：</label>
									<input class="sec_input" type="text" name="city" size="10" value="${takeCar.city}"/>
			            		</li>	
			            		<li>
			            			<label for="">县：</label>
									<input class="sec_input" type="text" name="county" size="10" value="${takeCar.county}" />
			            		</li>
			            		<li>
			            			<label for="">详细描述：</label>
			            			<input class="sec_input" type="text" name="detail" size="20" value="${takeCar.detail}">
			            		</li>	
			            		<li>
			            			<label for="">发布账号：</label>
			            			<input class="sec_input" type="text" name="cellPhone" size="20" value="${takeCar.cellPhone}">
			            		</li>	
			            		<li>
			            			<label for="">状态：</label>
			            			<select name="status">
										<option value="">----</option>
										<option value="<%=Constant.INFO_STATUS_DISABLE %>" <c:if test="${takeCar.status == 0}">selected</c:if>>无效信息</option>
									    <option value="<%=Constant.INFO_STATUS_WAIT %>" <c:if test="${takeCar.status == 1}">selected</c:if>>待审核</option>
									    <option value="<%=Constant.INFO_STATUS_PASS %>" <c:if test="${takeCar.status == 2}">selected</c:if>>审核通过</option>
									    <option value="<%=Constant.INFO_STATUS_FAILURE %>" <c:if test="${takeCar.status == 3}">selected</c:if>>审核未通过</option>
						   			</select>
			            		</li>			
			            	</ul>
			            </div>
                		<p class="errorBlock" id="errorSpan">${msg}</p>
                		<div class="tc mt60"><input  id="btn" class="button" type="submit" value="修改" onclick="query('common',${pageNo},-1);"></div>

				</tr>
			</table>
		</form>
	</div>



</body>
</html>