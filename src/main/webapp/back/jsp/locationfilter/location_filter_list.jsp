<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    String path = request.getContextPath();
    String basePath = "https://" + request.getServerName() + path + "/";
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>定位过滤用户管理</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
	<link href="<%=request.getContextPath()%>/back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
	<link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
	<link href="<%=request.getContextPath()%>/back/model/css/tableModel.css" type="text/css" rel="stylesheet">
	<link href="<%=request.getContextPath()%>/back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
	<link href="<%=request.getContextPath()%>/back/model/css/validate.css" type="text/css" rel="stylesheet">
	<link href="back/model/css/upgrade.css" type="text/css" rel="stylesheet">
	<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/jquery-1.7.2.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/back/jurisdiction/jsp/js/common.js"></script>
</head>

<body>
    <div class="contentBox">
        <div class="titUrl cf">
            <h1 class=" fl">
                <span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">定位过滤用户管理</span>
            </h1>
        </div>
       <div class="pageMessage">
		<div class="searchBtn">
			<ul class="btnBox cf">
				<li><input class="bulkImport" type="button" value="导入限制用户"></li>
			</ul>
		</div>
		<div class="clearfix"></div>
      
        <div class="main-content">
            <!--内容列表-->
            <div class="dataTable">
                <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
                    <tr class="tableTh">
	                    <th width="300">用户ID</th>
						<th width="300">备注</th>
						<th width="300">创建时间</th>
						<th width="180">操作</th>
                    </tr>
                    <c:forEach items="${locationFilterList }" var="locationFilter">
                        <tr>
							<td width="300">${locationFilter.filterUserId }</td>
							<td width="300">${locationFilter.remark }</td>
							<td><fmt:formatDate value="${locationFilter.ctime }" pattern="yyyy-MM-dd HH:mm:ss" /></td>
							<td width="180">
								<a class="info_detail ver_block removeBlock" onclick="del(${locationFilter.id })">删除</a>
							</td>
						</tr>
                    </c:forEach>
                </table>
            </div>
        </div>
    </div>
		<div class="outLook excelBox" style="display: none;">
			<div class="innerLook">
				<h2>请选择文件</h2>
				<p class="alertContent">请点击“浏览”选择需要导入的模板文件</p>
				<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
					<div class="fileBtnBox fl">
						<input class="fileBtn" id = "fileField" type="file" name="fileField"  accept=".csv" onchange="fileInfo(this,$(this))">
						<button class="rule_true">浏览</button>
					</div>
					<button id = "uploadFile" onclick="uploadFile()" class="close_block fr">上传</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>
	</div>
</body>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/easy_validator.pack.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/jsp/locationfilter/js/location_filter.js"></script>
<script type="text/javascript">
function del(id) {
	if (confirm('确定删除吗？')) {
		$.post("locationfilter/deleteSingleLocationFilter", {
			id : id
		}, function(result) {
			// alert(result.msg);
			if (result.code == 200) {
				// 刷新当前页面
				window.location.reload();
			}
		});
	}
}

function search(){
    var userIds = $("#cellPhone1").val();
	window.location.href = "singleUserCalllimit/getList?userIds="+userIds;
}

$(".tableBox").colResizable({
	liveDrag: true,
	draggingClass: "dragging",
	resizeMode: 'flex',
	minWidth: 80
});
</script>
</html>