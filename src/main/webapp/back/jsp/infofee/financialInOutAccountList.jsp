<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta charset="utf-8">
<base href="<%=basePath%>">
<title>财务-财务出入账查询</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">

<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">

<link href="back/jsp/infofee/css/finance.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript" src="back/model/js/common_tanchuang.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script>
<script type="text/javascript">
function myQuery(){
	document.forms[0].pageNo.value=1;
	document.forms[0].submit();
}

/**
 * 数据导出
 */
function exportData() {
	var checkResult = validExportCookie("financialInOutAccountList");
	
	if(checkResult == undefined ||checkResult == "" || checkResult == "-1"){
		alert("请求异常，请稍后重试！");
		return ;
	}
	
	if(checkResult != "0"){
		alert(checkResult);
		return ;
	}
	var form=document.forms[0];
	form.action="manage/infofee/financial/excel/exportInOut";
	form.submit();
	form.action="manage/infofee/financial/getInOutAccountList";
}
</script>

<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
  <script src="http://cdn.bootcss.com/html5shiv/3.7.2/html5shiv.min.js"></script>
  <script src="js/respond.min.js"></script>
<![endif]-->
    
</head>
<body>


<div class="contentBox">
  <div class="titUrl cf">
    <h1 class=" fl">
      <span>当前所在位置：</span><span>财务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">入账出账记录</span>
    </h1>
  </div>
  <div class="main-content"> 
    
    <!--搜索条件-->
    <form action="manage/infofee/financial/getInOutAccountList" method="post">
    <input type="hidden" name="pageNo" value="${pageNo }">
    <input type="hidden" name="menuId" value="${curMenu.id }">
    <div class="searchBox" id="searchCondition">
      <ul class="searchUl searchUlNone cf">
        <li>
          <label>交易日期：</label>
          <input value='<fmt:formatDate pattern='yyyy-MM-dd' value="${inOutAccount.tradeTime}"/>' class="input180" name="tradeTime" type="text" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
          
        </li>
        <li>
          <label>用户姓名：</label>
          <input class="input180" name="trueName" type="text" value="${inOutAccount.trueName }">
        </li>
        <li>
          <label>手机号：</label>
          <input class="input180" name="cellPhone" type="text" value="${inOutAccount.cellPhone }">
        </li>
        <li>
          <label>运单号：</label>
          <input class="input180" name="waybillNumber" type="text" value="${inOutAccount.waybillNumber}">
        </li>
        <li>
          <label>交易类型：</label>
          <select class="select180" name="accountType">
            <option value="">请选择</option>
            <option <c:if test="${inOutAccount.accountType==1 }">selected</c:if> value="1">信息费支付</option>
            <option <c:if test="${inOutAccount.accountType==2 }">selected</c:if> value="2">退回信息费</option>
            <option <c:if test="${inOutAccount.accountType==3 }">selected</c:if> value="3">提现</option>
            <option <c:if test="${inOutAccount.accountType==4 }">selected</c:if> value="4">信息费手续费支付</option>
            <option <c:if test="${inOutAccount.accountType==5 }">selected</c:if> value="5">支付会员费</option>
          </select>
        </li>
        <li>
          <label>付款方式：</label>
          <select class="select180" name="payType">
            <option value="">请选择</option>
            <option value="1" <c:if test="${inOutAccount.payType==1 }">selected</c:if>>支付宝</option>
            <option value="2" <c:if test="${inOutAccount.payType==2 }">selected</c:if>>微信</option>
            <option value="3" <c:if test="${inOutAccount.payType==3 }">selected</c:if>>易宝</option>
            <option value="4" <c:if test="${inOutAccount.payType==4 }">selected</c:if>>线下信息费</option>
          </select>
        </li>
        <li>
          <label>出账方式：</label>
          <select class="select180" name="transferType">
			  <!-- 1 微信 2 支付宝 3 银行卡 4 其他 -->
              <option value="0">请选择</option>
              <option value="1" <c:if test="${inOutAccount.transferType==1 }">selected</c:if>>微信</option>
              <option value="2" <c:if test="${inOutAccount.transferType==2 }">selected</c:if>>支付宝</option>
              <option value="3" <c:if test="${inOutAccount.transferType==3 }">selected</c:if>>银行卡</option>
              <option value="4" <c:if test="${inOutAccount.transferType==4 }">selected</c:if>>其他</option>
          </select>
        </li>
        <li>
          <label>付款银行：</label>
          <select class="select180" name="bankName">
          <option value="">请选择</option>
          
          <c:forEach items="${distinctBankName }" var="bankName">
          <option value="${bankName}" <c:if test="${inOutAccount.bankName==bankName }">selected</c:if> >${bankName}</option>
          </c:forEach>
          </select>
        </li>
        <li>
          <label>提现账号：</label>
          <input class="input180" name="bankCardNumber" type="text" value="${inOutAccount.bankCardNumber }">
        </li>
        <li>
          <label>操作人：</label>
          <input class="input180" name="operator" type="text" value="${inOutAccount.operator }">
        </li>
        <li>
      </ul>
    </div>
     </form>
    <!--页面信息-->
    <div class="pageMessage">
      <!-- <div class="messageLeft">
        <ul>
          <li>
            <input type="button" class="button" value="查询" onclick="myQuery();">
          </li>
           <li>
            <input type="button" class="button" value="导出" onclick="exportData();">
          </li>
        </ul>
      </div> -->
       <c:if test="${! empty subMenus }">
          <div class="searchBtn">
                <ul class="btnBox cf">
             <c:forEach items="${subMenus }" var="menu">
               <!-- 2链接；3submit;4reset;5button 6底部按钮-->
				<c:if test="${menu.type==3 }"><li><input class="button" type="submit" value="${menu.jurisdictionName }"></li> </c:if>
				<c:if test="${menu.type==4 }"><li><input class="button" type="reset" value="${menu.jurisdictionName }"></li></c:if>
				<c:if test="${menu.type==5 }"><li><input class="button" type="button" value="${menu.jurisdictionName }" onclick="${menu.url}"></li></c:if>
				</c:forEach>
                </ul>
                </div>
            </c:if>
      <div class="clearfix"></div>
    </div>
    
       <!--内容列表-->

      <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
       <div class="dataTable">  
        <table width="100%" border="0" class="tableBox">
          <tr class="tableTh">
            <th>ID</th><th>用户姓名</th><th>手机号</th><th>交易日期</th><th>交易类型</th><th>金额(元)</th><th>操作</th>
          </tr>
          <c:forEach items="${financialInOutAccountList }" var="inOutAccount">
          <tr>  
            <td>${inOutAccount.id }</td>
            <td>${inOutAccount.trueName }</td>
            <td class="phoneHide">${inOutAccount.cellPhone }</td>
            <td>
            <fmt:formatDate type="both" value="${inOutAccount.tradeTime}"/>
            </td>
            <td class="clType" <c:if test="${inOutAccount.accountType==1 ||inOutAccount.accountType==2||inOutAccount.accountType==4||inOutAccount.accountType==5 }">data-type="0"</c:if>
            <c:if test="${inOutAccount.accountType==3 }">data-type="1"</c:if>>
            <c:if test="${inOutAccount.accountType==1 }">信息费支付</c:if>
            <c:if test="${inOutAccount.accountType==2 }">退回信息费</c:if>
            <c:if test="${inOutAccount.accountType==3 }">提现</c:if>
            <c:if test="${inOutAccount.accountType==4 }">信息费手续费支付</c:if>
            <c:if test="${inOutAccount.accountType==5 }">支付会员费</c:if>
            </td>
            <td>
            ${inOutAccount.moneySymbol }
            <fmt:formatNumber type="number" value="${inOutAccount.account/100} " maxFractionDigits="2"/>
            </td>
            <td class="IDvalue" style="display: none" value="${inOutAccount.id}"></td>
            <c:forEach items="${subMenus }" var="menu">
                          <!-- 2链接；3submit;4reset;5button 6底部按钮-->
        		 <c:if test="${menu.type==2&&menu.jurisdictionName=='查看详情'}">
        		 <td class="lookBtn" ><a href="javascript:void(0);">${menu.jurisdictionName}</a></td>
        		 </c:if>
        		 <c:if test="${menu.type==2&&menu.jurisdictionName!='查看详情'}"><td></td></c:if>
        		 </c:forEach>
                </tr>
                </c:forEach>
        </table>
      </div>
    <jsp:include page="../footer.jsp"></jsp:include>
  </div>
 </div>
<div class="outlookbox" style="display: none;"></div>
<div class="innerLookbox" style="display: none;">
  <h3>财务详情</h3>
  <div class="innerTit wx_Tit" style="display: none;">
    <p><em>交易类型：</em><span class="typeind_name" id="boxwxAccountType">支付信息费/退信息费</span></p>
    <p><em>运单号：</em><span id="boxwxWayBillNumber">E12113131313</span></p>
    <p><em>付款方式：</em><span id="boxwxPayMethod">微信/支付宝/易宝</span></p>
    <p><em>操作人：</em><span id="boxwxOperate">系统自动操作</span></p>
  </div>
  <div class="innerTit tx_Tit">
    <p><em>交易类型：</em><span class="typeind_name" id="boxtxAccountType">提现</span></p>
    <p><em>银行卡号：</em><span id="boxtxBankCardNumber">533314131313131313</span></p>
    <p><em>出账方式：</em><span id="transferType">533314131313131313</span></p>
    <p><em>操作人：</em><span id="boxtxOperate">闫文超</span></p>
  </div>
  <div class="tc"><button class="button financeBtn">关闭</button></div>
  
</div>
</body>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
<script type="text/javascript">
  $(function(){
      var mar_left  = -$(".innerLookbox").width() / 2,
          mar_top = -$(".innerLookbox").height() / 2;
          $(".innerLookbox").css({'margin-top':mar_top,'margin-left':mar_left});

        $(".lookBtn").click(function(){
          $(".outlookbox,.innerLookbox").show();
            var typeId = $(this).siblings('.clType').attr('data-type');
            $(".typeind_name").html($(this).siblings('.clType').html());
            var inOutAccountId=$(this).siblings('.IDvalue').attr('value');
            getInOutAccountDetail(typeId,inOutAccountId);
        });
        $(".financeBtn").click(function(){
          $(".outlookbox,.innerLookbox").hide();
        })

        

  })
  
  /*详情查询接口*/
  function getInOutAccountDetail(box,inOutAccountId){
	  $.ajax({  
          type : "POST",  //提交方式  
          url : "manage/infofee/financial/getInOutAccountDetail",//路径  
          data : {  
              "inOutAccountId" : inOutAccountId  
          },//数据，这里使用的是Json格式进行传输  
          success : function(result) {//返回数据根据结果进行相应的处理  
              if ( result.code==200 ) {  
            	  outLookPage(box,result.data);
              } else {  
            	  alert(result.msg);   
              }  
          }  
      });  
  }
  /*详情弹出框显示*/
  function outLookPage(box,data){
      if(box==0){/*支付信息费、退信息费*/
        $("#boxwxWayBillNumber").text(data.waybillNumber);
        $("#boxwxPayMethod").text(getChinesePayType(data.payType));
        //$("#boxwxOperate").text(FilterEmpty(data.operator));
        $(".wx_Tit").show();
        $(".tx_Tit").hide();
      }else if(box==1){/*提现*/
    	$("#boxtxBankCardNumber").text(FilterEmpty(data.bankCardNumber));
    	$("#boxtxOperate").text(FilterEmpty(data.operator));
    	$("#transferType").text(FilterEmpty(data.transferTypeName));
        $(".wx_Tit").hide();
        $(".tx_Tit").show();
      }
  }
  /*支付方式转化为中文*/
  function getChinesePayType(payType){
	  if(payType==1){
      	return "支付宝";
      }else if(payType==2){
    	  return "微信";
      }else if(payType==3){
    	  return "易宝";
      }else if(payType==4){
	      return "线下信息费";
      }
	  return "未知";
  }
  /*为空的处理*/
  function FilterEmpty(str){
	  if(str==null){
		  return "未知";
	  }
	  if(str.trim().length<=0){
		  return "未知";
	  }
	  return str;
  }
</script>
</html>
