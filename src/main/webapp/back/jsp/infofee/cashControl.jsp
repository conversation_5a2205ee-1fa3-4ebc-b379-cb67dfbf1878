<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html>
<head>
<base href="<%=basePath%>">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
 <link href="<%=request.getContextPath()%>/back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
 <link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
 <link href="<%=request.getContextPath()%>/back/model/css/tableModel.css" type="text/css" rel="stylesheet">
 <link href="<%=request.getContextPath()%>/back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery.js"></script>
<title>提现风险预警参数</title>
 <style>
  .widthInp {width: 85%;
   height: 30px;
   padding: 0 6px;
   text-align: center;
   border: 1px solid #ccc;
  }
 </style>
</head>
<body>
<%-- <tr><td>发货条数:<input name="counts" value="${counts}">条/月</td></tr>
 --%>
<div class="contentBox">
 <h1 class="titUrl">
  <span>当前所在位置：</span><span>财务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">提现风险预警参数</span>
 </h1>
 <div class="dataTable">
  <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
   <tr class="tableTh">
    <th width="40%">参数名称</th>
    <th>值</th>
    <th width="100">操作</th>
   </tr>
   <c:forEach items="${map}" var="config" varStatus="statu">
    <tr>
     <form action="boss/config/cashControl/update" method="post" id="form_${config.value.id }">
      <input type="hidden" name="key" value="${config.key}">
      <td>${config.value.remark}</td>
      <td><input class="text widthInp" type="text" size="5" name="value"  value="${config.value.value}"></td>
      <td><a class="info_detail ver_block" href="javascript:void(0);" onclick="checkArg('form_${config.value.id }')">修改</a></td>
     </form>
    </tr>
   </c:forEach>
  </table>
 </div>

</div>

<script type="text/javascript">
var num = /^[0-9]*$/;
function checkArg(obj){
	var flag=true;
/**	
	$("input[name='value']").each(function(i){
       if(!num.test(this.value)){
    	   flag=false;
    	   alert("请填写正确的数值！");
    	   return false;
       };
    });
**/	
	if(flag){
		if(confirm("您确认修改吗？"))$("#"+obj).submit();
	}
		
}

var parendId = "${parendId}";
var subTypeId = "${subTypeId}";

$(document).ready(function (){
  if(parendId != "" && parendId != "null"){
      $("#parendId").val(parendId);
  }
  if(subTypeId != "" && subTypeId != "null"){
      $("#subTypeId").val(subTypeId);
  }
});
</script>
</body>
</html>