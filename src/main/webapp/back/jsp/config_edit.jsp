<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link href="<%=request.getContextPath()%>/back/css/dictmng.css" rel="stylesheet"
	type="text/css">
<title>属性管理-管理后台</title>
</head>
<body>

	<form action="<%=request.getContextPath()%>/admin/configedit"
		name="userForm" method="post">
		<input name="id" value="${config.id}" type="hidden">
		<table width="700" border="0" cellpadding="0" cellspacing="0"
			align="center">
			<tr bgcolor="#BFBFBF">
				<td>
					<table width="100%" border="0" cellpadding="2" cellspacing="1">

						<tr class="SolidRow">
							<td>用户+销售信息上传: <input type="text" name="infoUploadStopAll"
								value="<c:out value="${config.infoUploadStopAll }"/>" size="5">0:开启&nbsp;1:关闭
							</td>
						</tr>
						<tr class="SolidRow">
							<td>用户信息上传: <input type="text" name="infoUploadStopUser"
								value="<c:out value="${config.infoUploadStopUser }"/>" size="5">0:开启&nbsp;1:关闭
							</td>
						</tr>
						<tr class="SolidRow">
							<td>QQ屏蔽: <input type="text" name="qqBlackListStop"
								value="<c:out value="${config.qqBlackListStop }"/>" size="5">0:开启&nbsp;1:关闭
							</td>
						</tr>

						<tr class="SolidRow">
							<td>信息查询:<input type="text" name="infoQueryStop"
								value="<c:out value="${config.infoQueryStop }"/>" size="5">
								0:开启&nbsp;1:关闭
							</td>
						</tr>
						<tr class="SolidRow">
							<td>试用用户信息查询:<input type="text"
								name="infoQueryStopOfTrialUser"
								value="<c:out value="${config.infoQueryStopOfTrialUser }"/>"
								size="5"> 0:开启&nbsp;1:关闭
							</td>
						</tr>
						<tr class="SolidRow">
							<td>收费用户信息查询:<input type="text"
								name="infoQueryStopOfVipUser"
								value="<c:out value="${config.infoQueryStopOfVipUser }"/>"
								size="5"> 0:开启&nbsp;1:关闭
							</td>
						</tr>
						<tr class="SolidRow">
							<td>信息查询缓存:<input type="text" name="infoQueryCacheStop"
								value="<c:out value="${config.infoQueryCacheStop }"/>" size="5">
								0:开启&nbsp;1:关闭
							</td>
						</tr>
						<tr class="SolidRow">
							<td>信息查询缓存失效时间:<input type="text"
								name="infoQueryCacheExpire"
								value="<c:out value="${config.infoQueryCacheExpire }"/>"
								size="5"> 秒
							</td>
						</tr>
						<tr class="SolidRow">

							<td>试用用户信息查询间隔:<input type="text"
								name="queryIntervalOfTrialUser"
								value="<c:out value="${config.queryIntervalOfTrialUser }"/>"
								size="8">秒
							</td>
						</tr>
						<tr class="SolidRow">
							<td>收费用户信息查询间隔:<input type="text"
								name="queryIntervalOfVipUser"
								value="<c:out value="${config.queryIntervalOfVipUser }"/>"
								size="8">秒
							</td>
						</tr>
						<tr class="SolidRow">
							<td>无效信息查询间隔:<input type="text"
								name="queryIntervalUpperLimit"
								value="<c:out value="${config.queryIntervalUpperLimit }"/>"
								size="8">秒
							</td>

						</tr>
						<tr class="SolidRow">

							<td>手机试用用户信息查询间隔:<input type="text"
								name="queryIntervalOfTrialUserPhone"
								value="<c:out value="${config.queryIntervalOfTrialUserPhone }"/>"
								size="8">秒
							</td>
						</tr>
						<tr class="SolidRow">
							<td>手机收费用户信息查询间隔:<input type="text"
								name="queryIntervalOfVipUserPhone"
								value="<c:out value="${config.queryIntervalOfVipUserPhone }"/>"
								size="8">秒
							</td>
						</tr>
						<tr class="SolidRow">
							<td>手机无效信息查询间隔:<input type="text"
								name="queryIntervalOfInvalidateInfoPhone"
								value="<c:out value="${config.queryIntervalOfInvalidateInfoPhone }"/>"
								size="8">秒
							</td>

						</tr>
						
						<tr class="SolidRow">
							<td>服务拆分(预留功能、请不要设置):<input type="text" name="switchBackup"
								value="<c:out value="${config.switchBackup }"/>" size="5">0:主服务tyt8&nbsp;1:服务切分
								tyt6&nbsp; tyt8&nbsp; tyt10
							</td>

						</tr>
						<tr class="SolidRow">
							<td>系统通知: <textarea name="notice" rows="4" cols="60"><c:out
										value="${config.notice }" /></textarea>
							</td>

						</tr>
                        <tr class="SolidRow">
							<td>发货消息: <textarea name="sendGoodsNotice" rows="4" cols="60"><c:out
										value="${config.sendGoodsNotice}" /></textarea>
							</td>

						</tr>
						<tr class="SolidRow">
							<td>找货消息: <textarea name="searchGoodsNotice" rows="4" cols="60"><c:out
										value="${config.searchGoodsNotice}" /></textarea>
							</td>

						</tr>
						<tr class="SolidRow">
							<td>版权消息: <textarea name="copyRightGoodsNotice" rows="4" cols="60"><c:out
										value="${config.copyRightGoodsNotice}" /></textarea>
							</td>

						</tr>


						<tr class="SolidRow">
							<td align="center"><input type="submit" value="确定" />
								&nbsp;&nbsp; <input type="reset" value="重置" />&nbsp;&nbsp;</td>
						</tr>
						<tr>
							<td colspan="2" align="center"><c:out value="${msg }" />&nbsp;</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>

	</form>
</body>
</html>