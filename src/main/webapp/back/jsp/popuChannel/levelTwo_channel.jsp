<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<base href="<%=basePath%>">
	<title>二级渠道维护</title>
  <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">

  <script type="text/javascript" src="back/js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
  <style>
		.alert{
			width:410px
		}
		.one{
			line-height: 25px\9;
			width: 210px;
			min-width: 126px;
			height: 30px;
			border: 1px solid #ccc;
		}
  </style>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span>APP渠道管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">二级渠道维护</span>
		</h1>
		
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="addLevelTwo" type="button" value="添加二级渠道" onclick="saveTwoChannel()"></li>
				</ul>
				<input type="hidden" id="pageNoId" name="pageNo" value="${pageNo}">
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>${rowCount }</em>条信息，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页
            </div>
		</div>
		<div class="dataTable">
		<form action="<%=basePath %>/appChannel/levelTwoList " method="post" id="">
		<input type="hidden" id="pageNoId" name="pageNo" value="${pageNo}">
		</form>
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th>一级渠道</th>
					<th>二级渠道（ID）</th>
					<th>更新时间</th>
					<th width="240">操作</th>
				</tr>
				<c:forEach items="${twoList }" var="list">
				<tr>
					<td>${list.oneName }</td>
					<td>${list.twoName }（${list.twoId }）</td>
					<td><fmt:formatDate value="${list.mtime }" pattern="yyyy-MM-dd HH:mm" /></td>
					<td><a class="info_detail ver_block" onclick="editTwoButton(${list.twoId},${list.oneId },'${list.twoName }')">编辑</a></td>
				</tr>
				</c:forEach>
		    </table>
		</div>
		
		<div class="alert alert-danger fade in alert-height" style="display: none;" id="outlook_danger_add">
			<div class="innerLook">
				<h2 id='addUpdateTitle'>添加一级渠道</h2>
				<div class="addWhiteUser cf">
					<ul>
						<li>
							<label for="">
								<i>*</i>一级渠道：
							</label>
							<select name="channelName" id="channelName" class="one">
								<option value="">请选择</option>
								<c:forEach items="${oneList }" var="one">
									<option value="${one.id }"<c:if test="${pid==one.id }">selected</c:if>>${one.name }</option>
								</c:forEach>
							</select>
						</li>
						<li>
							<label for="">
								<i>*</i>二级渠道：
							</label>
							<input type="text" placeholder="请填写二级渠道名称" id="channelTwoName" maxlength="50" style="padding-left: 5px;">
							<span style="color:#999999;">不能超过50字</span>
						</li>
						<li><span style="color:#999999; padding-left: 52px;">注：二级渠道的ID系统自动生成</span></li>
					</ul>
					<div class="makeSureBtn" style="width:68px;height:32px;margin: 0 auto;">
						<input class="button" type="button" id="saveTwoButton" value="保存">
					</div>				
				</div>				
			</div>
			<i class="closelookBtn">×</i>
		</div>
		<!--此处引入footer.jsp-->
    	<jsp:include page="/back/jsp/footer.jsp" flush="true"/>
		</div>
		
</body>
<script type="text/javascript" src="back/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/js/infoMessage.js"></script>
<script type="text/javascript" src="back/jsp/popuChannel/js/popuChannelSaveEdit.js"></script>
</html>