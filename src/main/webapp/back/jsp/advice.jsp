<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ page import="com.tyt.model.User"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<base href="<%=basePath%>">
	<title>特运通用户反馈--管理后台</title>
	<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
	<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
	<link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
	<link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">

	<script type="text/javascript" src="back/js/jquery-1.7.1.js"></script>
	<script type="text/javascript" src="back/jsp/js/ajax_source3.js"></script>
	<script type="text/javascript" src="back/jsp/js/ajax_source2.js"></script>
	<script type="text/javascript" src="back/js/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="back/model/js/tyt_area/js/tyt_area.js"></script>
	<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
	<style type="text/css">
	.blocker_note {
	  border:1px solid #ccc;
	  width: 100px;
	  margin-right: 10px;
	}
	.blueColor {
	  color:#3961b2;
	}
	.contentBox{
		position: relative;
	}
	.detailList-box{
		width: 400px;
		height: 250px;
		border-radius: 10px;
		position: absolute;
		top: 250px;
		left: 50%;
		transform: translate(-200px, 0px);
		z-index: 1100;
		background: #ffffff;
	}
	.opt-box{
		display: flex;
		align-items: center;
		justify-content:center;
	}
	.work-form {
		height: 50px;
    	text-align: center;
	}
	.work-form select {
		width: 45%!important;
	}
	.opt-box button{
		margin: 15px;
		width: 90px;
		height: 30px;
		color: #ffffff;
		background-color: #477de4;
		border-radius: 5px;
		font-size: 14px;
		cursor: pointer;
	}
	.title-top{
		width: 100%;
		height: 50px;
		line-height: 50px;
		background: #d4e2ff;
		color: #4168b9;
		font-size: 16px;
		border-radius: 10px 10px 0 0;
		text-align: center;
	}
	.close{
		position: absolute;
		right: 18px;
		top: 11px;
		font-size: 39px;
		font-weight: 300;
		width: 24px;
		height: 24px;
		line-height: 24px;
		font-size: 30px;
		line-height: 1;
		color: #4168b9;
	}
	.outBgColor {
		width: 100%;
		height: 100%;
		position: fixed;
		left: 0;
		top: 0;
		bottom: 0;
		right: 0;
		background: #000;
		opacity: 0.5;
		z-index: 1000;
	}
	.jump-btn{
		color: #428bca;
		cursor: pointer;
		margin-left: 10px;
	}
	.tip{
		height: 80px;
		line-height: 80px;
		text-align: center;
	}
	.add-box{
		width: 600px;
		height: 400px;
		border-radius: 10px;
		position: absolute;
		top: 250px;
		left: 50%;
		transform: translate(-300px, 0px);
		z-index: 1200;
		background: #ffffff;
	}
	.add-box-con{
		width: 100%;
	}
	.con-ul{
		width: 100%;
		height: 290px;
		display: flex;
		flex-wrap: wrap;
		padding: 20px;
		box-sizing: border-box;
	}
	.con-ul li{
		width: 50%;
		height: 40px;
		display: flex;
		align-items: center;
	}
	.con-ul li i{
		color: red;
	}

	.con-ul li label{
		width: 30%;
		text-align: right;
		margin-right: 10px;
	}
	.con-ul li input,select{
		width: 60%;
		height: 30px;
		border: 1px solid #000;
		padding: 0px 10px;
		box-sizing: border-box;
		border-radius: 3px;
	}
	.li-textarea textarea{
		height: 60px;
		padding: 10px;
		box-sizing: border-box;
	}
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}

	input[type="number"]{
		-moz-appearance: textfield;
	}
	</style>

	<script type="text/javascript">
		function adviceQuery(pageNo,operate,id) {
			if(pageNo>=${maxPage})pageNo=${maxPage};
			var form = document.forms[0];
			form.action="<%=request.getContextPath()%>/admin/advicelist?editId="+id+"&operate="+operate;
			form.pageNo.value = pageNo;
			form.submit();
            form.pageNo.value = "0";
        }


		function getAllSuggestType(){
            $.ajax({
                type: "post",
                url: "admin/advice/getSuggestTypeListAll_ajx",
                data: {"suggestId":1},
                cache: false,
                dataType: "json",
                success: function(data) {
                    if (data.code == 200) {
                        //0未认证1通过2认证中3认证失败
                        var users = data.data;
                        console.info(users.length);
                        var selectInfo = $("#suggestType");
                        var suggestTypeId =  $("#suggestTypeId").val();
                        var flag = false;
                        for (var i = 0; i < users.length; i++) {
                            var sugg = users[i];
                            if(suggestTypeId == sugg.value){
                                selectInfo.append("<option value='"+sugg.value+"' selected='selected' data='"+sugg.id+"'>"+sugg.name+"</option>");
                                flag =true;
							}else{
                                selectInfo.append("<option value='"+sugg.value+"' data='"+sugg.id+"'>"+sugg.name+"</option>");
							}
                        }

                        if(flag){
                            getAllSuggestTypeDetailByStId();
						}

                    } else {
                        //alert(data.msg);
						console.info(data.msg);
                    }
                }
            });
		}
		//二级联动
        function getAllSuggestTypeDetailByStId(){

            var stId = $("#suggestType").find("option:selected").attr("data");
		    if(stId == null || stId == ""){
		        console.info("stId为空");
		        return;
			}
            $.ajax({
                type: "post",
                url: "admin/advice/getSuggestTypeDetailListAll_ajx",
                data: {"stId":stId},
                cache: false,
                dataType: "json",
                success: function(data) {
                    if (data.code == 200) {
                        //0未认证1通过2认证中3认证失败
                        var users = data.data;
                        console.info(users.length);
                        var selectInfo = $("#suggestTypeDetail");
                        var suggestTypeDetailId =  $("#suggestTypeDetailId").val();
                        selectInfo.empty();
                        selectInfo.append("<option value=''>--------</option>");
                        for (var i = 0; i < users.length; i++) {
                            var sugg = users[i];
                            if(suggestTypeDetailId == sugg.value){
                                selectInfo.append("<option selected='selected' value='"+sugg.value+"'>"+sugg.name+"</option>");
							}else{
                                selectInfo.append("<option value='"+sugg.value+"'>"+sugg.name+"</option>");
							}
                        }
                    } else {
                        //alert(data.msg);
                        console.info(data.msg);
                    }
                }
            });
        }

		// 工单分配人
		function getOpUserList(){
            $.ajax({
                type: "post",
                url: "complaint/opUserList",
                dataType: "json",
                success: function(data) {
                    if (data.code == 200) {
                        var users = data.data;
						var curId =  "${userId}"
                        var selectInfo = $("#opUserId");
                        for (var i = 0; i < users.length; i++) {
                            var sugg = users[i];
							var selected = sugg.id == curId ? ' selected' : ''
                            selectInfo.append("<option value='" + sugg.id + "'" + selected + ">" + sugg.name + "</option>");
                        }
                    } else {
                        //alert(data.msg);
						console.info(data.msg);
                    }
                }
            });
		}
		


	</script>
</head>
<style>
	.blue {
		color: #428bca;
		cursor: pointer;
	}
</style>
<body>
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>用户反馈汇总管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">用户意见反馈</span>
    </h1>
    <div class="main-content">
		<form name="adviceForm"
				action="" method="post">
				<input type="hidden" name="pageNo" value="<c:out value="${pageNo}"/>">
			<input type="hidden" id="suggestTypeId" value="${advice.suggestType }" />
			<input type="hidden" id="suggestTypeDetailId" value="${advice.suggestTypeDetail }" />
			<input type="hidden" id="identityTypeId" value="${advice.identityType }" />
			<input type="hidden" id="deliverTypeOneId" value="${advice.deliverTypeOne }" />
			<input type="hidden" id="deliverTypeTwoId" value="${advice.deliverType }" />
      <!--搜索条件-->
    <div class="searchBox" id="searchCondition">
       <ul class="searchUl searchUlNone cf" id="form1">
		   <li><label>注册电话：</label><input type="text" name="cellPhone"value="${advice.cellPhone}" /></li>
		   <li><label>联系电话：</label><input type="text" name="telPhone"value="${advice.telPhone}" /></li>
		   <li>
			   <label>用户注册身份：</label>
			   <select name="identityType"
					   id="identity_type_select_lable_id" class="select180"></select>
		   </li>
		   <li><label>审核一级身份：</label> <select name="deliverTypeOne"
											  id="deliver_type_one_select_lable_id" class="select180"></select>
		   </li>
		   <li><label>审核二级身份：</label>
			   <select name="deliverType"
					   id="deliver_type_two_select_lable_id" class="select180"></select>
		   </li>
		   <li>
			   <label>用户状态：</label>
			   <select name="userType"
					   id="userType" class="select180">
				   <option value="">----</option>
				   <option value="<%=User.USER_TYPE_TRIAL%>"
						   <c:if test="${advice.userType == 0}">selected</c:if>>试用</option>
				   <option value="<%=User.USER_TYPE_VIP%>"
						   <c:if test="${advice.userType == 1}">selected</c:if>>付费</option>
				   <option value="<%=User.USER_TYPE_TRIAL_NOT_VERIFY%>"
						   <c:if test="${advice.userType == 2}">selected</c:if>>
					   未激活</option>
			   </select>
		   </li>
		   <li><label>内容：</label><input type="text" name="content"value="${advice.content}" /></li>
		   <li>
			   <label>意见类型：</label>
			   <select name="suggestType" id="suggestType" class="select180" onchange="getAllSuggestTypeDetailByStId();">
				   <option value="">--------</option>
			   </select>
		   </li>
		   <li><label>类型详情：</label>
			   <select name="suggestTypeDetail" id="suggestTypeDetail" class="select180">
				   <option value="">--------</option>
			   </select>
		   </li>
		   <li>
			   <label>终端来源：</label>
			   <select name="platId">
				   <option value="">----</option>
				   <option value="<%=Constant.PLAT_PC%>"<c:if test="${advice.platId==1}">selected</c:if>>PC</option>
				   <option value="<%=Constant.PLAT_ANDROID%>"<c:if test="${advice.platId==2}">selected</c:if>>Android</option>
				   <option value="<%=Constant.PLAT_IOS%>"<c:if test="${advice.platId==3}">selected</c:if>>Ios</option>
				   <option value="<%=Constant.PLAT_APAD%>"<c:if test="${advice.platId==4}">selected</c:if>>Apad</option>
				   <option value="<%=Constant.PLAT_IPAD%>"<c:if test="${advice.platId==5}">selected</c:if>>Ipad</option>
				   <option value="<%=Constant.PLAT_WEB%>"<c:if test="${advice.platId==6}">selected</c:if>>Web</option>
				   <option value="<%=Constant.PLAT_ANDROID_CAR%>"
						   <c:if test="${advice.platId == 21}">selected</c:if>>Android_Car
				   </option>
				   <option value="<%=Constant.PLAT_ANDROID_GOODS%>"
						   <c:if test="${advice.platId == 22}">selected</c:if>>Android-车
				   </option>
				   <option value="<%=Constant.PLAT_IOS_CAR%>"
						   <c:if test="${advice.platId == 31}">selected</c:if>>IOS-车
				   </option>
				   <option value="<%=Constant.PLAT_IOS_GOODS%>"
						   <c:if test="${advice.platId == 32}">selected</c:if>>IOS-货
				   </option>
				   <option value="<%=Constant.PLAT_WEB_GOODS%>"
						   <c:if test="${advice.platId == 62}">selected</c:if>>Web-货
				   </option>
			   </select>
		   </li>
		   <li><label>版本：</label><input type="text" name="version"value="${advice.version}" /></li>
		   <li style="height: 24px">
			   <div style="position: absolute; z-index: 1000">
				   <label style="position: absolute;">归属地：</label>
				   <div class="fl areaDiv twoDiv"  style="position: absolute; left: 98px;">
					   <input id="position" type="text" class="input120" placeholder="请选择地区" onclick="tyt_open_area('1','position','boxDiv','sheng','shi','xian','tyt_area_iframe','3');"
							  readonly class="positionInput addressIcon" value="<c:if test='${!fn:startsWith(advice.city,advice.province)}'>${advice.province}&nbsp;</c:if>${advice.city}<c:if test="${advice.county!=advice.city }">&nbsp;${advice.county}</c:if>">
					   <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
							id="boxDiv" class="boxDiv">
						   <iframe name="tyt_area_iframe" id="tyt_area_iframe" src="back/model/js/tyt_area/area.html"
								   frameborder="0" width="240" scrolling="no" height="400" leftmargin="0" topmargin="0" allowtransparency="true"></iframe>
					   </div>
					   <p class="noneInput mtl20"><input id="sheng" name="province" type="text" value="${advice.province }" class="positionInput"></p>
					   <p class="noneInput mtl20"><input id="shi" name="city" type="text" value="${advice.city }"  class="positionInput"></p>
					   <p class="noneInput mtl20"><input id="xian" name="county" type="text" value="${advice.county }"  class="positionInput"></p>
					   <div class="cb"></div>
				   </div>
			   </div>
		   </li>
		   <li><label>处理状态：</label><select name="adviceStatus">
			   <option value="">----</option>
			   <option value="1"<c:if test="${advice.adviceStatus==1 }">selected</c:if>>未处理</option>
			   <option value="3"<c:if test="${advice.adviceStatus==3 }">selected</c:if>>处理中</option>
			   <option value="2"<c:if test="${advice.adviceStatus==2 }">selected</c:if>>处理完成</option>
		   </select>
		   </li>
		   <li>
			   <label>反馈日期：</label>
			   <input class="pinfoTable" type="text"
					  name="createBeginTime"
					  value="${advice.createBeginTime }"
					  onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" /><span>-</span><input
				   class="pinfoTable" type="text" name="createEndTime"
				   value="${advice.createEndTime }"
				   onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
		   </li>
		   <li>
			   <label>最后处理时间：</label>
			   <input class="pinfoTable" type="text"
					  name="modifyBeginTime"
					  value="${advice.modifyBeginTime }"
					  onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" /><span>-</span><input
				   class="pinfoTable" type="text" name="modifyEndTime"
				   value="${advice.modifyEndTime }"
				   onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
		   </li>
		   <li><label>处理人：</label><input name="dutyMan" value="${advice.dutyMan}" size="5"/></li>
		   <!--source-->
          <li>
            <label>来源：</label>
            <select name="source">
                <option value="">----</option>
                <option value="1"<c:if test="${advice.source==1}">selected</c:if>>400热线</option>
                <option value="2"<c:if test="${advice.source==2}">selected</c:if>>微信</option>
                <option value="3"<c:if test="${advice.source==3}">selected</c:if>>市场部反馈</option>
                <option value="4"<c:if test="${advice.source==4}">selected</c:if>>调研回访</option>
                <option value="5"<c:if test="${advice.source==5}">selected</c:if>>管理层反馈</option>
                <option value="6"<c:if test="${advice.source==6}">selected</c:if>>用户自行反馈</option>
            </select>
        </li>
        <li><label>创建人：</label><input name="cName" value="${advice.cName}" size="5"/></li>

        </ul>
        <div style="clear:both"></div>
    </div>

    <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                    	<li><input type="submit" class="queryListCheck" value="查询" onclick="adviceQuery(${pageNo},'common','-1');"></li>
						<li><input type="reset" value="重置" onclick="tytTool.resetAllForm($('.searchUl'),$('.queryListCheck'))"></li>
						<li><input value="新增" style="width:52px" onclick="addAdviceShow('show')"></li>
                    </ul>
                </div>
                <div class="clearfix"></div>
                <div class="messageRight fr">

        <c:out value="${msg }" />
		查询到:<em>
		<c:out value="${rowCount }" />
		</em>
		条用户反馈, 当前第<span>
		<c:out value="${pageNo }" /></span>
		页,总共
		<span><c:out value="${maxPage }" /></span>
		页
                </div>
            </div>

	</form>

	<!-- 可拖拽表格 -->
    <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
    <div class="dataTable">

	<table border="0" cellpadding=0 cellspacing=0 class="tableBox" id="table1">
		<tr class="tableTh">
			<th>ID</th>
			<th>注册电话</th>
			<th>联系电话</th>
			<th>反馈日期</th>
			<th>用户注册身份</th>
			<th>审核身份</th>
			<th>用户状态</th>
			<th>归属地</th>
			<th>问题类型</th>
			<th>内容</th>
			<th>意见类型</th>
			<th>类型详情</th>
			<th>终端来源</th>
			<th>版本</th>
			<th>处理状态</th>
			<th>最新处理时间</th>
            <th>来源</th>
			<th>创建人</th>
			<th>处理人</th>
			<th>操作</th>
		</tr>
		<c:if test="${!empty advices }">
			<c:forEach items="${advices }" var="advice" varStatus="state">
				<tr class="${state.index % 2 == 0 ? 'EvenRow' : 'OddRow'}" onClick="change()" >
					<td>${advice.id }</td>
					<td class="blue" onclick="javascript:phoneSoHfn($(this),'${advice.id }','意见反馈记录','special','cellPhone')">查看电话</td>
					<td class="blue" onclick="javascript:phoneSoHfn($(this),'${advice.id }','意见反馈记录','special','telPhone')">查看电话</td>
					<td><fmt:formatDate value="${advice.ctime}" type="both"/></td>
					<td>
						<c:if test="${!empty advice.userClass&&!empty advice.identityType}">
							<c:choose>
								<c:when test="${advice.userClass==1 }">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_1',${advice.identityType})" var="sf" />${sf.name}
								</c:when>
								<c:when test="${advice.userClass==2 }">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_2',${advice.identityType})" var="sf" />${sf.name}
								</c:when>
							</c:choose>
						</c:if>
					</td>
					<td>
						<c:if test="${! empty  advice.deliverTypeOne}">
							<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_one',${advice.deliverTypeOne})" var="sf" />${sf.name}
						</c:if> / <c:if
							test="${! empty  advice.deliverTypeOne&&!empty advice.deliverType}">
						<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_two',${advice.deliverType})" var="sf" />${sf.name}
						</c:if>
					</td>
					<td>
						<c:if test="${advice.userType == 0}">试用</c:if>
						<c:if test="${advice.userType == 1}">付费</c:if>
						<c:if test="${advice.userType == 2}">未激活</c:if>
					</td>
					<td>
						<c:out value="${advice.province }" />
						<c:out value="${advice.city }" />
						<c:out value="${advice.county }" />
					</td>
					<td>
						<c:out value="${advice.typeStr }" />
					</td>
					<td>
                            <c:out value="${advice.content }" />
                    </td>
					<td>
						<c:if test="${! empty  advice.suggestType}">
							<c:forEach items="${suggestTypeList }" var="suggestTypeTemp">
								<c:if test="${suggestTypeTemp.value == advice.suggestType}">
									${suggestTypeTemp.name}
								</c:if>
							</c:forEach>
						</c:if>
					</td>
					<td>
						<c:if test="${! empty  advice.suggestTypeDetail}">
							<c:forEach items="${suggestTypeDetails }" var="suggestTypeDetailTemp">
								<c:if test="${suggestTypeDetailTemp.value == advice.suggestTypeDetail}">
									${suggestTypeDetailTemp.name}
								</c:if>
							</c:forEach>
						</c:if>
					<td>
						<c:if test="${advice.platId==1 || advice.platId==12}">PC</c:if>
						<c:if test="${advice.platId==2}">Android</c:if>
						<c:if test="${advice.platId==3}">Ios</c:if>
						<c:if test="${advice.platId==4}">Apad</c:if>
						<c:if test="${advice.platId==5}">Ipad</c:if>
						<c:if test="${advice.platId==6}">Web</c:if>
						<c:if test="${advice.platId==21}">Android-车</c:if>
						<c:if test="${advice.platId==31}">IOS-车</c:if>
						<c:if test="${advice.platId==22}">Android-货</c:if>
						<c:if test="${advice.platId==32}">IOS-货</c:if>
						<c:if test="${advice.platId==62}">Web-货</c:if>
					</td>
					<td>${advice.version }</td>
					<td>
						<c:if test="${advice.adviceStatus==1 }">未处理</c:if>
						<c:if test="${advice.adviceStatus==2 }">处理完成</c:if>
						<c:if test="${advice.adviceStatus==3 }">处理中</c:if>
					</td>
					<td><fmt:formatDate value="${advice.mtime}" type="both"/></td>
                   <td>
						<c:if test="${advice.source==1}">400热线</c:if>
						<c:if test="${advice.source==2}">微信</c:if>
						<c:if test="${advice.source==3}">市场部反馈</c:if>
						<c:if test="${advice.source==4}">调研回访</c:if>
						<c:if test="${advice.source==5}">管理层反馈</c:if>
						<c:if test="${advice.source==6}">用户自行反馈</c:if>
					</td>
					<td>${advice.cName}</td>
					<td>${advice.dutyMan}</td>
					<td>
						<c:if test="${advice.adviceStatus==1 or advice.adviceStatus==3 }"><a class="info_detail ver_block" onclick="openWinAuto('back/model/html/advice/back_idea.html?adviceId=${advice.id }','editPage',1100,600)">编辑</a></c:if>
						<c:if test="${advice.adviceStatus==2 }"><a class="info_detail ver_block"  onclick="openWinAuto('back/model/html/advice/back_check.html?adviceId=${advice.id }','checkPage',1100,600)">查看</a></c:if>
                        <c:if test="${advice.isMakeWorkorder==1 }"><a class="info_detail ver_block"  onclick="showHideItem('show','${advice.id }')">生成工单</a></c:if>
					</td>
				</tr>
			</c:forEach>
		</c:if>
	</table>
<input type="hidden" id="max_page"  name="max_page" value="${maxPage}">
</div>
    <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
</div>
<div id="pageNumberBigBox" class="cf">
	<ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
</div>
<div class="outBgColor" style="display:none;"> </div>
<div class="detailList-box" style="display: none;">
	<div class="title-top">
		<span>提示</span>
		<span class="close" onclick="showHideItem('hide')">×</span>
	</div>
	<p class="tip">你确定要生成工单？</p>
	<div class="work-form">
		<label for="">选择工单分配人:</label>
		<select name="opUserId" id="opUserId">
			<option value="">--------</option>
		</select>（非必填）
	</div>
	<div class="opt-box">
		<button class="order-btn" onclick="submitWorkOrder()">确定</button>
		<button onclick="showHideItem('hide')">取消</button>
	</div>
</div>
<div class="add-box"  style="display: none">
		<div class="title-top">
			<span>新增反馈</span>
			<span class="close" onclick="addAdviceShow('hide')">×</span>
		</div>
		<div class="add-box-con">
			<ul class="con-ul">
				<li>
					<label><i>*</i>注册电话:</label>
					<input type="number" tname="cellPhone" placeholder="请输入注册电话" maxLength="11" oninput="if(value.length>11)value=value.slice(0,11)"/>
				</li>
				<li>
					<label><i>*</i>联系电话:</label>
					<input type="number" tname="telPhone" placeholder="请输入联系电话" maxLength="11" oninput="if(value.length>11)value=value.slice(0,11)"/>
				</li>
				<li>
					<label><i>*</i>来源:</label>
					<select tname="source">
						<option value="">请选择来源</option>
						<option value="1">400热线</option>
						<option value="2">微信</option>
						<option value="3">市场部反馈</option>
						<option value="4">调研回访</option>
						<option value="5">管理层反馈</option>
						<option value="6">用户自行反馈</option>
					</select>
				</li>
				<li>
					<label>终端来源:</label>
					<select tname="platId">
						<option value="">请选择终端来源</option>
						<option value="1">PC</option>
						<option value="2">Android</option>
						<option value="3">Ios</option>
						<option value="4">Apad</option>
						<option value="5">Ipad</option>
						<option value="6">Web</option>
						<option value="21">Android-车</option>
						<option value="31">IOS-车</option>
						<option value="22">Android-货</option>
						<option value="32">IOS-货</option>
						<option value="62">Web-货</option>
					</select>
				</li>
				<li>
					<label>版本:</label>
					<input type="text" tname="version"  placeholder="请输入版本"/>
				</li>
				<li class="li-textarea" style="width: 100%" >
					<label style="width: 15%"><i>*</i>反馈内容:</label>
					<textarea class="textarea-content" style="width: 85%" tname="content" type="text" maxlength="200" placeholder="请输入反馈内容" ></textarea>
				</li>
			</ul>
		</div>
		<div class="opt-box">
			<button class="order-btn" onclick="addAdvice()">保存</button>
		</div>
	</div>
</div>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>
<script type="text/javascript">
    $(document).ready(function(){
        getAllSuggestType();
		getOpUserList();
    });
    //改变选中状态函数
    function changeSpanSelected(obj){
        var currentClassName=obj.className;
        if(currentClassName.indexOf("selected")>0){
            obj.className=currentClassName.replace("selected","");
        }else{
            $("#shenfenDiv ul li span").attr("class","btn btn-default");
            obj.className=currentClassName+" selected";
        }
    }
	function showHideItem(_type,id ){
		console.log(_type)
		console.log(id)
		if(_type == 'hide'){
			$(".outBgColor,.detailList-box").hide()
			$(".order-btn").attr('orderId','')
		}else if(_type == 'show'){
			$(".order-btn").attr('orderId',id)
			$(".outBgColor,.detailList-box").show()
			var defaultOpUser = $("#opUserId option[selected]").val();
			$("#opUserId").val(defaultOpUser || '');
		}
    }
	function submitWorkOrder(){
		var _id =  $(".order-btn").attr('orderId')
		var _opUserVal = $("select[name='opUserId'] option:selected");
		var _opUserId = _opUserVal.val() - 0 || '';
		var _opUser = _opUserId && _opUserVal.html();
		
		$.ajax({
                type: "post",
                url: "/manage_new/complaint/createWordOrder",
                data: {
					"id": _id,
					"complaintSource":'2',
					"opUserId": _opUserId,
					"opUser": _opUser
				},
                cache: false,
                dataType: "json",
                success: function(data) {
                    if (data.code == 200) {
						tytTool.toastShow(data.msg, 4000);
						showHideItem('hide')
                    } else {
						tytTool.toastShow(data.msg, 4000);
						showHideItem('hide')
                        //alert(data.msg);
						console.info(data.msg);
                    }
                }
            });

    }
    function addAdviceShow(_type){
		if(_type == 'hide'){
			$(".outBgColor,.add-box").hide()
			$('.textarea-content').val('')
			tytTool.resetAllForm($(".con-ul"))
		}else if(_type == 'show'){
			$(".outBgColor,.add-box").show()
		}
	}
	function addAdvice(){
		var data = tytTool.htmlGetTname($(".con-ul"))
		if (data.cellPhone == ''){
            tytTool.toastShow('请输入注册电话')
            return false
		}
        if (data.telPhone ==''){
            tytTool.toastShow('请输入联系电话')
            return false
		}
        if (data.source ==''){
            tytTool.toastShow('请选择来源')
            return false
		}
        if (data.content ==''){
            tytTool.toastShow('请输入反馈内容')
            return false
		}
		$.ajax({
			type: "post",
			url: "/manage_new/admin/addAdvice",
			data: data,
			cache: false,
			dataType: "json",
			success: function(data) {
				if (data.code == 200) {
					tytTool.toastShow(data.msg, 4000);
					window.location.reload();
					addAdviceShow('hide');
				} else if (data.code == 2001) {
					tytTool.toastShow(data.msg, 4000);
				} else {
					tytTool.toastShow(data.msg, 4000);
					addAdviceShow('hide')
				}
			}
		});
	}

</script>
</body>
</html>
