<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page import="com.tyt.model.Apply" %>
<%
    String path = request.getContextPath();
    String basePath = "https://" + request.getServerName() + path + "/";
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>申请列表-管理后台</title>
	<link href="<%=request.getContextPath()%>/back/css/dictmng.css" rel="stylesheet" type="text/css">
	<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/jquery-1.7.1.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/changeColor.js"></script>
	<script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
	<script type="text/javascript">
        function query(pageNo){
            var form = document.forms[0];
            form.pageNo.value = pageNo;
            form.submit();
        }
        function changeStatus(id){
        	openWin("<%=request.getContextPath()%>/admin/applyedit?id="+id);
        }
    </script>
</head>
<body>

<table width="900" border="0" cellpadding="0" cellspacing="0">
	<tr bgcolor="#BFBFBF">
	  <td>
<form name="applyForm" action="<%=request.getContextPath()%>/admin/applylist" method="post">
    <input type="hidden" name="pageNo" value="<c:out value="${pageNo}"/>" >
    <input type="hidden" name="menuId" value="${curMenu.id }">
<table width="100%" border="0" cellpadding="2" cellspacing="1">
	  <tr class="SolidRow">
  <td>
   手机号：<input type="text" name="cellPhone" value="<c:out value="${cellPhone }"/>" size="10"/>
  </td>
  <td>
  QQ号： <input type="text" name="qq" value="<c:out value="${qq }"/>" size="12"/>
  </td>
 <td>
        真实姓名:<input type="text" name="trueName" size="5" value="<c:out value="${trueName }"/>" /> 
      状态 :
      <select name="status">
                 <option value="-1">----</option>
                 <option value="<%=Apply.STATUS_DEFAULT %>" <c:if test="${status == 0}">selected</c:if>>未处理</option>
                 <option value="<%=Apply.STATUS_HANDLED %>" <c:if test="${status == 1}">selected</c:if>>已处理</option>
             </select>         
  </td> 
 </tr>
<tr class="SolidRow">
 <td colspan="3">
    <%-- 每页显示: <input type="text" name="pageSize" size="3" value="<c:out value="${pageSize}"/>" > --%>   
    <!-- <input type="hidden" name="pageSize" size="3" value="20"> -->
  <!-- <input type="submit" value="查询">
  <input type="reset" value="重置"> -->
  <c:forEach items="${subMenus }" var="menu">
               <!-- 2链接；3submit;4reset;5button 6底部按钮-->
				<c:if test="${menu.type==3 }"><input class="button" type="submit" value="${menu.jurisdictionName }"></c:if>
				<c:if test="${menu.type==4 }"><input class="button" type="reset" value="${menu.jurisdictionName }"></c:if>
				<c:if test="${menu.type==5 }"><input class="button" type="button" value="${menu.jurisdictionName }" onclick="${menu.url}"></c:if>
				</c:forEach>
  
  </td>
 </tr>
</table>
</form>

</td></tr></table>
<h6>
   <c:out value="${msg }"/>
   查询到：<c:out value="${rowCount }"/>个申请， 当前第  <c:out value="${pageNo }"/>页，总共<c:out value="${maxPage }"/>页
   
</h6>

<table width="900" border="0" cellpadding=0 cellspacing=0>
		<tr>
<td>

  <table width="100%" border="0" cellpadding="2" cellspacing="1" id="table1">
    <tr class="TitleRow">
        <td>ID</td>
        <td>手机号</td>
        <td>QQ号</td>
        <td>真实姓名</td>
        <td>申请时间</td>
        <td>状态</td>
        <td>操作</td>
        
	  
	  
    </tr>

    <c:if test="${!empty applyList }">
  <%
int i = 0;
%>
    
        <c:forEach items="${applyList }" var="apply">
        
        <%
if ( ++i % 2 == 0 )
{
%>
		  <tr class="EvenRow" onClick="change()"   onMouseOut="out()">
<%
}
else
{
%>
		  <tr class="OddRow" onClick="change()"   onMouseOut="out()">
<%
}
%>
		  	<td>
		  	  <c:out value="${apply.id }"/>
		  	</td>
        
          

                <td><c:out value="${apply.cellPhone }"/></td>
                <td>   <c:out value="${apply.qq }"/>   </td>
                <td>  <c:out value="${apply.trueName }"/>  </td>
                <td>
                   <fmt:formatDate value="${apply.ctime}" type="both" dateStyle="default"/>
                </td>
                <td>
                     <c:if test="${apply.status == 0}">未处理</c:if>
                     <c:if test="${apply.status == 1}">已处理</c:if>
                </td>
                <td>
                    <%-- <a href="<%=request.getContextPath()%>/admin/applyedit?id=<c:out value="${apply.id}"/>" target="_blank">变更状态</a> &nbsp;&nbsp; --%>
                <c:forEach items="${subMenus }" var="menu">
                        <!-- 2链接；3submit;4reset;5button 6底部按钮-->
				        <c:if test="${menu.type==2 }">
				        <a onclick="${menu.url}${apply.id})">${menu.jurisdictionName}</a></c:if>
				        </c:forEach>
                </td>
            </tr>
        </c:forEach>
    </c:if>
</table>
</td></tr></table>
<input type="hidden" id="max_page"  name="max_page" value="${maxPage}">	 
<jsp:include  page="page.jsp" flush="true"/>
</body>
</html>