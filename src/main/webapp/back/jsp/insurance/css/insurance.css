.modalCovr {
	display: none;/*hide by default*/
	position: fixed;/*only relative to body*/
	left: 0%;
	top: 0%;
	width: 100%;
	height: 100%;
	background-color: #ccc;
	z-index: 1010;
	-moz-opacity: 0.6;
	opacity: .60;
	filter: alpha(opacity=60);
}
.insuranceEdit {
	display: none;
	margin: -174px 0 0 -285px;
	position: fixed;
	left: 50%;
	top: 50%;
	width: 570px;
/* 	height: 280px; */
	border: 1px solid #D0CDC7;
    background-color: #FFF;
    padding:20px;
    z-index: 1011;
}
.insuranceEdit h3 {
	border-bottom: 1px solid #D0CDC7;
	height: 35px;
	text-align: center;
	line-height: 35px;
	font-size: 16px;
}
.insuranceEdit table {
	width: 100%;
	border-collapse: separate; 
/* 	border-spacing: 5px; */
}
.insuranceEdit table td.optButtons {
	text-align: center;
}
.searchCondition ul li {
	width: auto;
}


.insuranceEdit ul li {
	width: 50%;
	float: left;
	line-height: 26px;
	margin-top:10px;
}

.insuranceEdit ul li.remarks label{
	vertical-align: top;
}

.insuranceEdit ul li label{
	text-align: right;
	width: 100px;
	vertical-align: middle;
}

.insuranceEdit ul li .input175 {
   width: 160px;
   height: 26px;
   line-height: 26px;
   border: 1px solid #ccc;
  
}

.insuranceEdit ul li .textareaBox {
	padding:0;
	width: 160px;
	resize: none;
	border: 1px solid #ccc;
}

.btn-white {
	color:#428bca;
	background-color:#fff;
	border-color: #428bca;
}

.mt10 {
	margin-top: 10px;
}

.mt20 {
	margin-top: 20px;
}

.ml10 {
	margin-left: 10px;
}
.tc {
	text-align: center;
}