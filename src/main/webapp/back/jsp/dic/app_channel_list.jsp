<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<link href="../back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="../back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="../back/model/css/tableModel.css" type="text/css" rel="stylesheet">
<link href="../back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="../back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="../back/js/My97DatePicker/WdatePicker.js" ></script>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>特运通后台管理-渠道管理</title>
<script type="text/javascript">
</script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">渠道管理</span>
		</h1>
		<form action="appChannelList" name="channelForm">
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li>
					<label for="">渠道名称：</label>
					<input type="text" value="${channelName }" name="channelName"/>
				</li>
			</ul>
		</div>
		</form>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
				<li>
					<input class="" type="button" onclick="query();" value="查询">
				</li>
				<li>
					<input class="add_line" type="button" value="添加">
				</li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
        <!-- 查询到<em>16</em>条信息，当前是第<span>1</span>页，共<span>1</span>页 -->
      </div>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th>渠道名称</th>
					<th>渠道号</th>
					<th>渠道简介</th>
					<th>描述</th>
					<th width="160">操作</th>
				</tr>
				<c:forEach items="${appChannelSourceSourceList }" var="appChannelSourceSource">
				<tr>
					<td>
						<input class="sec_input" type="text" value="${appChannelSourceSource.name }" id="channelName_${appChannelSourceSource.id }">
					</td>
					<td>${appChannelSourceSource.value }</td>
					<td>
						<input class="sec_input" type="text" value="${appChannelSourceSource.shortName }" id="shortName_${appChannelSourceSource.id }">
					</td>
					<td>
						<input class="sec_input" type="text" value="${appChannelSourceSource.remark }" id="remark_${appChannelSourceSource.id }">
					</td>
					<td>
						<a class="info_detail ver_block" href="javascript:void(0);" onclick="modify(${appChannelSourceSource.id })">修改</a>
						<span class="ver_block ver_span">|</span>
						<a class="info_detail ver_block" href="javascript:void(0);" onclick="del(${appChannelSourceSource.id })">删除</a>
					</td>
				</tr>
				</c:forEach>
		    </table>
		</div>

		<div class="alert alert-danger fade in alert-height" style="display: none;" id="outlook_danger_add">
			<div class="innerLook">
				<h2>渠道添加</h2>
				<div class="addWhiteUser cf">
					<ul>
						<li>
							<label for="">
								<i>*</i>渠道名称
							</label>
							<input type="text" placeholder="输入渠道名称（必填）" id="channelNameAddId">
						</li>
						<li>
							<label for="">
								<i>*</i>渠道简介
							</label>
							<input type="text" placeholder="输入渠道简介（必填）" id="shortNameAddId">
						</li>
						<li>
							<label for="">
								<i>*</i>描述
							</label>
							<input type="text" placeholder="输入渠道描述（必填）" id="remarkAddId">
						</li>
					</ul>
					<p class="errorMsg" style="display: none;">不是注册用户，不能添加</p>
					<div class="makeSureBtn">
						<input class="button" type="button" onclick="add();" value="添加">
					</div>				
				</div>				
			</div>
			<i class="closelookBtn">×</i>
		</div>
	</div>
</body>
<script type="text/javascript" src="../back/model/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../back/model/js/infoMessage.js"></script>
<script>
	$(".closelookBtn").click(function(event) {
		$(".alert").hide();
	}); 
	$(".add_line").click(function(event) {
		$(".alert").show();
	});

	$(document).click(function(event) {
		$(".alert").hide();
	});
	$(".add_line,.alert").click(function(event) {
		event.stopPropagation();
	});
	function modify(id) {
		var channelNameValue = $.trim($('#channelName_' + id).val()),
			shortNameValue = $.trim($('#shortName_' + id).val()),
			remarkValue = $.trim($('#remark_' + id).val());
		if (!channelNameValue) {
			alert('渠道名称不能为空！');
			return;
		}
		if (!shortNameValue) {
			alert('渠道简介不能为空！');
			return;
		}
		if (!remarkValue) {
			alert('描述不能为空！');
			return;
		}
		$.post("update", { channelName: channelNameValue, shortName: shortNameValue, remark: remarkValue, id: id },
			  function(data){
				result = eval("(" + data + ")");
				alert(result.msg);
			  	if(result.code==200){
				   //刷新当前页面
				   window.location.reload();
				}
			  } 
		);
	}
	function del(id) {
		if (window.confirm("确定删除吗？")) {
			$.post("delete", { id: id },
				  function(data){
					result = eval("(" + data + ")");
					alert(result.msg);
				  	if(result.code==200){
					   //刷新当前页面
					   window.location.reload();
					}
				  } 
			);
		}
	}
	function query() {
		document.forms['channelForm'].submit();
	}
	function add() {
		var channelNameValue = $.trim($('#channelNameAddId').val()),
			shortNameValue = $.trim($('#shortNameAddId').val()),
			remarkValue = $.trim($('#remarkAddId').val());
		if (!channelNameValue) {
			alert('渠道名称不能为空！');
			return;
		}
		if (!shortNameValue) {
			alert('渠道简介不能为空！');
			return;
		}
		if (!remarkValue) {
			alert('描述不能为空！');
			return;
		}
		$.post("add", { channelName: channelNameValue, shortName: shortNameValue, remark: remarkValue },
			  function(data){
				result = eval("(" + data + ")");
				alert(result.msg);
			  	if(result.code==200){
				   //刷新当前页面
				   window.location.reload();
				}
			  } 
		);
	}
</script>
</html>