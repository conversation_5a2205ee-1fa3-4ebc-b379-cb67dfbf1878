<!DOCTYPE html>  
<html lang="en" >  
    <head>  
        <meta charset="utf-8" />  
        <title>IE10 下利用HTML5实现文件上传实例教程--liwei3gjob</title>  
        <link href="css/main.css" rel="stylesheet" type="text/css" />  
        <script src="js/script.js"></script>  
    </head>  
    <body>  
        <header>  
            <h2>html5文件上传</h2>  
        </header>  
        <div class="container">  
            <div class="contr"><h2>选择文件</h2></div>  
  
            <div class="upload_form_cont">  
                <form id="upload_form" enctype="multipart/form-data" method="post" action="upload.php">  
                    <div>  
                        <div><label for="image_file">选择图片</label></div>  
                        <div><input type="file" name="image_file" id="image_file" onchange="fileSelected();" /></div>  
                    </div>  
                    <div>  
                        <input type="button" value="上传" onclick="startUploading()" />  
                    </div>  
                    <div id="fileinfo">  
                        <div id="filename"></div>  
                        <div id="filesize"></div>  
                        <div id="filetype"></div>  
                        <div id="filedim"></div>  
                    </div>  
                    <div id="error">只能上传图片格式!</div>  
                    <div id="error2">上传文件发生错误！</div>  
                    <div id="abort">网络断开，无法继续上传！</div>  
                    <div id="warnsize">文件太大，无法上传！</div>  
  
                    <div id="progress_info">  
                        <div id="progress"></div>  
                        <div id="progress_percent"> </div>  
                        <div class="clear_both"></div>  
                        <div>  
                            <div id="speed"> </div>  
                            <div id="remaining"> </div>  
                            <div id="b_transfered"> </div>  
                            <div class="clear_both"></div>  
                        </div>  
                        <div id="upload_response"></div>  
                    </div>  
                </form>  
  
                <img id="preview" />  
            </div>  
        </div>  
    </body>  
</html>  