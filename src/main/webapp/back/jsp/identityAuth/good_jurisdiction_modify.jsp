<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<title>找货权限修改</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/jsp/identityAuth/css/identity.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="back/model/js/bootstrap.js"></script>
</head>
<body>
 <div class="contentBox">
		<div class="titUrl cf">
			<h1 class=" fl">
				<span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">找货权限修改</span>
			</h1>
		</div>
		<div class="main-content">
		   <input type="hidden" id="callPhoneLimitId" value="${callPhoneLimit.id }">
		   <input type="hidden" id="callPhoneLimitType" value="${callPhoneLimit.userClass }">
	       <p class="useCareful">使用说明：本功能支持按不同的<span>注册一级身份</span>，对拨打货源电话按照【身份认证】，【车辆认证】，【会员缴费】进行不同顺序，不同条数的控制</p>
		    <!--搜索条件-->
		    <div class="searchCondition">
		      <c:if test="${callPhoneLimit.userClass != -1 }">
		      <ul class="o-hidden hiddenList">
			      <li class="useSelect">
			        <label>用户注册身份</label>
			        <select id="userClassId">
			          <option value="2" <c:if test="${callPhoneLimit.userClass == 2 }">selected</c:if>>车辆方</option>
			          <option value="1" <c:if test="${callPhoneLimit.userClass == 1 }">selected</c:if>>发货方</option>
			        </select>
			        <select id="identityTypeId">
			          <c:forEach items="${carSources }" var="source">
			          <option value="${source.value }"  <c:if test="${callPhoneLimit.identityType == source.value }">selected</c:if>>${source.name }</option>
			          </c:forEach>
			        </select>
			      </li>
		      </ul>
		      </c:if>
			    <div class="orderList cf">
			    	<div class="fl">
						<label>身份认证控制顺序</label>
						<input type="text" id="identitySortId" value="${callPhoneLimit.identitySor }">
						<i class="mustRed">*</i>
			    	</div>
			    	<div class="fl">
						<label>会员缴费控制顺序</label>
						<input type="text" id="memberSortId" value="${callPhoneLimit.memberSort }">
						<i class="mustRed">*</i>
			    	</div>
			    	<div class="fl">
						<label>车辆认证控制顺序</label>
						<input type="text" id="carSortId" value="${callPhoneLimit.carSort }">
						<i class="mustRed">*</i>
			    	</div>
			    </div>
			    <div class="numList cf">
			    	<div class="fl">
						<label>身份认证控制条数</label>
						<input type="text" id="identityNumberId" value="${callPhoneLimit.identityNumber }">
						<i class="mustRed">*</i>
			    	</div>
			    	<div class="fl">
						<label>会员缴费控制条数</label>
						<input type="text" id="memberNumberId" value="${callPhoneLimit.memberNumber }">
						<i class="mustRed">*</i>
			    	</div>
			    	<div class="fl">
						<label>车辆认证控制条数</label>
						<input type="text" id="carNumberId" value="${callPhoneLimit.carNumber }">
						<i class="mustRed">*</i>
			    	</div>
			    </div>
			    <p class="useHint">注：控制顺序只能输入0，1，2，3，输入0时为无效</p>
		    </div>
		    <div class="errBox" id="errBoxId"></div>
		    <div class="subBtn">
		      <button class="button" onclick="updateCallPhoneLimit();" style="background-color: #3961b2">保存</button>
		    </div>
		</div>
	</div>
</body>
<script type="text/javascript" src="back/jsp/identityAuth/js/goodJurisdiction.js"></script>
</html>
