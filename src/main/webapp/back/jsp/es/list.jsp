<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta charset="utf-8">
<base href="<%=basePath%>">
<title>ES-管理页面</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="back/model/js/bootstrap.js"></script>
<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
  <script src="http://cdn.bootcss.com/html5shiv/3.7.2/html5shiv.min.js"></script>
  <script src="js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
function myQuery(){
	var transId=$("#transId").val();
	if(!transId){
		alert("请输入货物ID");
		return ;
	}
	document.forms[0].submit();
}

/**
 * 刷新数据
 */
function refreshEsTransport() {
    var transId=$("#transId").val();
    if(!transId){
        alert("请输入货物ID");
        return ;
    }
	var form=document.forms[0];
	form.action="es/admin/refreshEsById";
	form.submit();
	form.action="es/admin/getByTransId";
}
var flag = "${transportMapList == null}";
var msg = "${msg}";

$(document).ready(function (){
    if(flag == "false"){
        $("#dataTable .dataFlag").each(function(){
            var text = $("#"+this.id+"_1").text();
            if(text != $(this).text()){
                $(this).css("color","red");
                $("#"+this.id+"_1").css("color","red");
            }
        });
    }
    if(msg != ""){
        alert(msg);
    }
})
</script>  
</head>
<body>
<div class="contentBox">
  <div class="titUrl cf">
    <h1 class=" fl">
      <span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">ES数据管理</span>
    </h1>
  </div>
  <div class="main-content"> 
    
    <!--搜索条件-->
    <form action="es/admin/getByTransId" method="post">
    <input type="hidden" name="menuId" value="${curMenu.id }">


    <div class="searchBox" id="searchCondition">
      <ul class="searchUl searchUlNone cf">
        <li>
          <label>货物ID：</label>
          <input class="input180" name="transId" id="transId" type="text" value="${transId }">
        </li>
      </ul>
      <!--<div class="div-button"><input type="button" class="button" value="查询"><input type="button" class="button" value="重置"></div>--> 
    </div>

    </form>
    <!--页面信息-->
    <div class="pageMessage">
          <div class="searchBtn">
                <ul class="btnBox cf">
				<li><input class="button" type="submit" value="查询" onclick="myQuery()"></li>
				<li><input class="button" type="button" value="刷新ES" onclick="refreshEsTransport()"></li>
                </ul>
                </div>
      <div class="clearfix"></div>
    </div>
       <!--内容列表-->

       <div class="dataTable" id="dataTable">
        <table width="50%" border="0" style="float:left; width: 40%;" class="tableBox">
          <tr class="tableTh">
            <th colspan="2">DB</th>
          </tr>
        <tr class="tableTh">
            <th>属性名称</th><th>属性值</th>
          </tr>
        <c:forEach items="${transportMapList }" var="tmap">
          <tr>
            <td>${tmap.name }</td>
            <td class="dataFlag" id="${tmap.name }" ref="values" >${tmap.value }</td>
          </tr>
        </c:forEach>
        </table>
         <table width="50%" border="0" style="float:left; width: 40%;" class="tableBox">
           <tr class="tableTh">
             <th colspan="2">ES</th>
           </tr>
           <tr class="tableTh">
             <th>属性名称</th><th>属性值</th>
           </tr>
           <c:forEach items="${esTransportMapList }" var="tmap1">
             <tr>
               <td>${tmap1.name }</td>
               <td id="${tmap1.name }_1" ref="values" >${tmap1.value }</td>
             </tr>
           </c:forEach>

         </table>
      </div>
  </div>
 </div>

</body>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
</html>
