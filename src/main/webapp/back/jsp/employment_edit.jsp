<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>特运通司机招聘--编辑后台</title>
<link href="<%=request.getContextPath()%>/back/css/dictmng.css" rel="stylesheet"
	type="text/css">
</head>
<body>
<form name="employeeForm" action="<%=request.getContextPath()%>/admin/updatemp" method="post">
					<table width="50%" align="center" border="0" cellpadding="2" cellspacing="1">
						<tr class="SolidRow">
						<td>标题: <input type="text" name="title" value="${employee.title}" /></td>
						</tr>
                                                
						<tr class="SolidRow"><td>姓名:<input type="text" name="telName" value="${employee.telName }" size="5" /></tr>
						<tr class="SolidRow"><td>	性别:<select name="sex">
							        <option value="">---</option>
									<option value="<%=Constant.SEX1 %>" <c:if test="${employee.sex == 1}">selected</c:if>>男</option>
									<option value="<%=Constant.SEX2 %>" <c:if test="${employee.sex == 2}">selected</c:if>>女</option></select> 
								</td></tr>
						<tr class="SolidRow"><td> 
                                                                    来源:<select name="identity">
                                    <option value="">-----</option>
									<option value="<%=Constant.IDENTITY1 %>" <c:if test="${employee.identity == 1}">selected</c:if>>公司</option>
									<option value="<%=Constant.IDENTITY2 %>" <c:if test="${employee.identity == 2}">selected</c:if>>个人</option></select><td></tr>
					             <tr class="SolidRow"><td> 身份:<select name="distinguish">
					                <option value="">--------</option>
									<option value="<%=Constant.DISTINGUISH1 %>" <c:if test="${employee.distinguish == 1}">selected</c:if>>招聘者</option>
									<option value="<%=Constant.DISTINGUISH2 %>" <c:if test="${employee.distinguish == 2}">selected</c:if>>求职者</option>
									</select>
									</td></tr>
									<tr class="SolidRow"><td>
									 职务:<select name="duty">
					                <option value="">--------</option>
									<option value="<%=Constant.DUTY1%>" <c:if test="${employee.duty == 1}">selected</c:if>>主司机</option>
									<option value="<%=Constant.DUTY2 %>" <c:if test="${employee.duty == 2}">selected</c:if>>副司机</option>
									</select>
									</td></tr>
					    
					    <tr class="SolidRow"><td>
					          学历:<select name="education">
									<option value="<%=Constant.EDUCATION0%>" <c:if test="${employee.education == 0}">selected</c:if>>无要求</option>
									<option value="<%=Constant.EDUCATION1%>" <c:if test="${employee.education == 1}">selected</c:if>>高中</option>
									<option value="<%=Constant.EDUCATION2%>" <c:if test="${employee.education == 2}">selected</c:if>>技校</option>
									<option value="<%=Constant.EDUCATION3%>" <c:if test="${employee.education == 3}">selected</c:if>>中专</option>
									<option value="<%=Constant.EDUCATION4%>" <c:if test="${employee.education == 4}">selected</c:if>>大专</option>
									<option value="<%=Constant.EDUCATION5%>" <c:if test="${employee.education == 5}">selected</c:if>>本科</option>
									<option value="<%=Constant.EDUCATION6%>" <c:if test="${employee.education == 6}">selected</c:if>>其它</option>
									</select> 
					    </td></tr>
						<tr class="SolidRow">
						<input type="hidden" name="id" id="id" value="${employee.id }" size="4"/>
							<tr class="SolidRow"><td>薪资: 
							<select name="salary">
							        <option value="">------------</option>
									<option value="<%=Constant.SALARY1%>" <c:if test="${employee.salary == 1}">selected</c:if>>面议          </option>
									<option value="<%=Constant.SALARY2%>" <c:if test="${employee.salary == 2}">selected</c:if>>5000以下 </option>
									<option value="<%=Constant.SALARY3%>" <c:if test="${employee.salary == 3}">selected</c:if>>5000-6000</option>
									<option value="<%=Constant.SALARY4%>" <c:if test="${employee.salary == 4}">selected</c:if>>6000-7000</option>
									<option value="<%=Constant.SALARY5%>" <c:if test="${employee.salary == 5}">selected</c:if>>7000-8000</option>
									<option value="<%=Constant.SALARY6%>" <c:if test="${employee.salary == 6}">selected</c:if>>8000以上      </option>
									</select> <td></tr>
							<tr class="SolidRow"><td>司机类型:<select name="position">
							        <option value="">------------</option>
									<option value="<%=Constant.POSITION1%>" <c:if test="${employee.position == 1}">selected</c:if>>13.5米大板车司机</option>
									<option value="<%=Constant.POSITION2%>" <c:if test="${employee.position == 2}">selected</c:if>>17.5米平板车司机</option>
									<option value="<%=Constant.POSITION3%>" <c:if test="${employee.position == 3}">selected</c:if>>6.8米/9.6米单机板车司机   </option>
									<option value="<%=Constant.POSITION4%>" <c:if test="${employee.position == 4}">selected</c:if>>特种车司机</option>
                                    <option value="<%=Constant.POSITION5%>" <c:if test="${employee.position == 5}">selected</c:if>>临时司机 </option>
									<option value="<%=Constant.POSITION6%>" <c:if test="${employee.position == 6}">selected</c:if>>其他车辆司机</option>

									</select> </td></tr>
						<tr class="SolidRow"><td>招聘人数: <input type="text" name="count" size="4" value="${employee.count}"/></td></tr>
						<tr class="SolidRow">
							<td>联系电话: <input type="text" name="telephone" size="15" value="${employee.telephone}"></td></tr>
						<tr class="SolidRow">
							<td>发布账号: <input type="text" name="cellPhone" size="15" value="${employee.cellPhone}"></td></tr>	
							<tr class="SolidRow"><td>福利:
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY1%>" <c:if test="${fn:contains(employee.subsidy,'五险一金')}">checked</c:if>><a class="list07">五险一金</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY2 %>" <c:if test="${fn:contains(employee.subsidy,'包吃')}">checked</c:if>><a class="list07">包吃</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY3 %>" <c:if test="${fn:contains(employee.subsidy,'包住')}">checked</c:if>><a class="list08" >包住</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY4 %>" <c:if test="${fn:contains(employee.subsidy,'年底双薪')}">checked</c:if>><a  class="list04">年底双薪</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY5 %>" <c:if test="${fn:contains(employee.subsidy,'周末双休')}">checked</c:if>><a  class="list04">周末双休</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY6 %>" <c:if test="${fn:contains(employee.subsidy,'交通补助')}">checked</c:if>><a  class="list04">交通补助</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY7 %>" <c:if test="${fn:contains(employee.subsidy,'加班补助')}">checked</c:if>><a  class="list04">加班补助</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY8 %>" <c:if test="${fn:contains(employee.subsidy,'餐补')}">checked</c:if>><a  class="list04">餐补</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY9 %>" <c:if test="${fn:contains(employee.subsidy,'话补')}">checked</c:if>><a  class="list04">话补</a>
								<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY10 %>" <c:if test="${fn:contains(employee.subsidy,'房补')}">checked</c:if>><a  class="list04">房补</a>
							</td></tr>
							<tr class="SolidRow"><td>公司名称: <input value="${employee.company}" name="company"/></td></tr>
							<tr class="SolidRow"><td>发布日期:<fmt:formatDate value="${employee.ctime}" type="both"/></td></tr>
							<c:choose>
					        <c:when test="${employee.distinguish==1 }">
							<tr class="SolidRow"><td>年龄: 
							<select name="age">
							<option value="<%=Constant.AGE0%>" <c:if test="${employee.age==0}">selected</c:if> >无要求</option>
							<option value="<%=Constant.AGE1%>" <c:if test="${employee.age==1}">selected</c:if>>18-30岁</option>
							<option value="<%=Constant.AGE2%>" <c:if test="${employee.age==2}">selected</c:if>>30-40岁</option>
							<option value="<%=Constant.AGE3%>" <c:if test="${employee.age==3}">selected</c:if>>40-50岁</option>
							<option value="<%=Constant.AGE4%>" <c:if test="${employee.age==4}">selected</c:if>>50岁以上</option>
					        </select></td></tr></c:when>
					        <c:otherwise>
					        <tr><td>年龄：<input name="age" value="${employee.age}" size="5">岁</td></tr>
					        </c:otherwise>
					        </c:choose>
					<c:choose>
					<c:when test="${employee.distinguish==1 }">
					<tr class="SolidRow"><td>驾龄:<select name="years">
					<option value="<%=Constant.YEARS0%>" <c:if test="${employee.years==0}">selected</c:if> >无要求</option>
					<option value="<%=Constant.YEARS1%>" <c:if test="${employee.years==1}">selected</c:if> >5年以下</option>
					<option value="<%=Constant.YEARS2%>" <c:if test="${employee.years==2}">selected</c:if> >5-10年</option>
					<option value="<%=Constant.YEARS3%>" <c:if test="${employee.years==3}">selected</c:if> >10年以上</option>
					</select></td></tr>
					</c:when>
					<c:otherwise>
					<tr><td>驾龄：<input name="years" value="${employee.years}" size="5">年</td></tr>
					</c:otherwise>
					</c:choose>
							<tr class="SolidRow"><td>
							归属地:
								<input type="text" name="province" size="10" value="${employee.province}" />
								<input type="text" name="city" size="10" value="${employee.city}"/>
								<input type="text" name="county" size="10" value="${employee.county}" />
							</td></tr>
							<tr class="SolidRow"><td>
							籍贯:
								<input type="text" name="birthProvince" size="10" value="${employee.birthProvince}" />
								<input type="text" name="birthCity" size="10" value="${employee.birthCity}"/>
								<input type="text" name="birthCounty" size="10" value="${employee.birthCounty}" />
							</td></tr>
						<tr class="SolidRow">
						<td>QQ: <input type="text" name="qq" size="10" value="${employee.qq}"></td></tr>
					    <tr class="SolidRow"><td>
							薪资描述: 
							<textarea rows="5" cols="50" name="sDescribe">${employee.sDescribe}</textarea>
							</td></tr>	
					    <tr class="SolidRow"><td>
							职位描述:<textarea rows="5" cols="50" name="pDescribe">${employee.pDescribe}</textarea>
							</td></tr>
					    <tr class="SolidRow">
						<td>驾驶经历:
						<textarea name="self" rows="5" cols="50">${employee.self}</textarea>
						</td></tr>
						<tr class="SolidRow"><td>
					         状态:<select name="status">
						<option value="">------------</option>
						<option value="<%=Constant.INFO_STATUS_DISABLE %>" <c:if test="${employee.status == 0}">selected</c:if>>无效信息</option>
					    <option value="<%=Constant.INFO_STATUS_WAIT %>" <c:if test="${employee.status == 1}">selected</c:if>>待审核</option>
					    <option value="<%=Constant.INFO_STATUS_PASS %>" <c:if test="${employee.status == 2}">selected</c:if>>审核通过</option>
					    <option value="<%=Constant.INFO_STATUS_FAILURE %>" <c:if test="${employee.status == 3}">selected</c:if>>审核未通过</option>
					    </select>			
					    </td></tr>
						<tr class="SolidRow">
							<td><input type="submit" value="修改">&nbsp;&nbsp;&nbsp;<span style="color: red">${msg}</span>
							</td>
						</tr>
					</table>
				</form>

</body>
</html>