<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <base href="<%=basePath%>">
  <title>推广用户管理</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<%-- <script type="text/javascript" src="back/js/jquery-1.11.1.min.js"></script> --%>
<%-- <script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script> --%>
<%-- <script type="text/javascript" src="back/jsp/js/ajax_source3.js"></script> --%>
<%-- <script type="text/javascript" src="back/jsp/js/ajax_source2.js"></script> --%>
<%-- <script type="text/javascript" src="back/js/common.js"></script> --%>


 <script type="text/javascript" src="back/js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="back/js/My97DatePicker/WdatePicker.js"></script>
  <script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script>
  <script type="text/javascript" src="back/jsp/js/ajax_source3.js"></script>
<script type="text/javascript" src="back/jsp/js/ajax_source2.js"></script>
  <script type="text/javascript" src="back/js/common_openwin.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>推广功能管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">推广用户管理</span>
		</h1>
		<!--搜索条件-->
   		<form action="admin/friend/getRecommenderList" method="post" id="form1">
   		<input type="hidden" id="deliverTypeOneId" value="${queryBean.deliverTypeOne }" /> 
   		<input type="hidden" id="deliverTypeTwoId" value="${queryBean.deliverType }" />
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li><label for="">推荐人ID：</label>
					<input type="text" name="userId" value="${queryBean.userId }"></li>
				<li><label for="">推荐人姓名：</label>
					<input type="text" name="userName" value="${queryBean.userName }"></li>
				<li><label for="">推荐人手机号：</label>
					<input type="text" name="cellPhone" value="${queryBean.cellPhone }"></li>
				<li>
					<label for="">审核一级：</label>
					<select name="deliverTypeOne"
							id="deliver_type_one_select_lable_id" class="select180"></select>		
				</li>
				<li>
					<label for="">审核二级：</label>
					<select name="deliverType"
							id="deliver_type_two_select_lable_id" class="select180"></select>			
				</li>
				<li>
					<label for="">推荐人状态：</label>
					<select name="isRecommend" id="">
						<option value="0">请选择</option>
						<option value="1" <c:if test="${queryBean.isRecommend==1 }">selected</c:if>>开通</option>
						<option value="2" <c:if test="${queryBean.isRecommend==2 }">selected</c:if>>关闭</option>
						<option value="3" <c:if test="${queryBean.isRecommend==3 }">selected</c:if>>无效</option>
					</select>			
				</li>
				<li>
					<label for="">活动状态：</label>
					<select name="activeState" id="">
						<option value="">请选择</option>
						<option value="2" <c:if test="${activeState==2 }">selected</c:if>>进行中</option>
						<option value="1" <c:if test="${activeState==1 }">selected</c:if>>未关联</option>
						<option value="3" <c:if test="${activeState==3 }">selected</c:if>>未开始</option>
					</select>			
				</li>
			</ul>
		</div>
		</form>
		<div class="pageMessage" style="position: relative;">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="" type="button" value="查询" onclick="queryList();"></li>
					<li><input class="" type="button" value="重置" onclick="javascript:$('#form1')[0].reset()"></li>
					<li><input type="button" value="导出" onclick="excelRecExport();"/></li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>${rowCount }</em>条信息，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页
            </div>
            <!--添加msg返回值,提示删除成功 , 并刷新-->
            <b class="delete_msg_b"><c:out value="${msg }"/></b>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th>ID</th>
					<th>姓名</th>
					<th>手机号</th>
					<th>审核身份</th>
					<th>用户状态</th>
					<th>拉新总人数</th>
					<th>总认证人数</th>
					<th>认证车方人数</th>
					<th>认证货方人数</th>
					<th>付费总人数</th>
					<th>付费一年总人数</th>
					<th>付费两年总人数</th>
					<th>付费三年总人数</th>
					<th>已获得奖励总金额（元）</th>
					<th>关联活动状态</th>
					<th width="260">操作</th>
				</tr>
			<c:forEach items="${mapList }" var="rec">
				<tr>
					<td>${rec.userId}</td>
					<td>${rec.userName}</td>
					<!-- <td class="phoneHide">${rec.cellPhone}</td> -->
					<td class="blue" onclick="javascript:phoneSoHfn($(this),'${rec.cellPhone}','推广用户管理')"><c:out value="查看电话" /></td>
					<td>
						<c:if test="${! empty  rec.deliverTypeOne}">
							<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_one',${rec.deliverTypeOne})"
								var="sf" />${sf.name}
						</c:if> / <c:if
							test="${! empty  rec.deliverTypeOne&&!empty rec.deliverType}">
							<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_two',${rec.deliverType})"
								var="sf" />${sf.name}
						</c:if>
					</td>
					<td>
						<c:if test="${rec.isRecommend==1 }">开通</c:if>
			            <c:if test="${rec.isRecommend==2 }">关闭</c:if>
			            <c:if test="${rec.isRecommend==3 }">无效(${rec.nullifyReason})</c:if>
					</td>
					<td>${rec.newAddNum }</td>
					<td>${rec.totalAuthNum }</td>
					<td>${rec.authCarNum }</td>
					<td>${rec.authTransNum }</td>
					<td>${rec.moneyTotalNum }</td>
					<td>${rec.moneyOneYearNum }</td>
					<td>${rec.moneyTwoYearNum }</td>
					<td>${rec.moneyThreeYearNum }</td>
					<td><fmt:formatNumber type="number" value="${rec.getMoneyTotalPrice }"  pattern="0.00"/></td>
					<td>
						<c:if test="${rec.activeState==1 }">未关联</c:if>
						<c:if test="${rec.activeState==3 }">未开始</c:if>
						<c:if test="${rec.activeState==2 }">进行中</c:if>
					</td>
					<td>
						<c:if test="${rec.isRecommend==1 }">
							<c:if test="${rec.activeState==1 }"><!--已开通未关联 -->
								<a class="info_detail ver_block" href="javascript:closeRecom(${rec.id})">资格关闭</a>
								<span class="ver_block ver_span">|</span>
								<a class="info_detail ver_block changlookBtn" onclick="openWinAuto('back/model/html/popularizeUser/active_setting.html?isRecommend=1&userId='+${rec.userId},'active_setting',1100,600)">关联活动</a>
								<span class="ver_block ver_span">|</span>
								<a class="info_detail ver_block invalidBtn" href="javascript:invalidRecom(${rec.id})">置为无效</a>
							</c:if>
							<c:if test="${rec.activeState==3 || rec.activeState==2 }"><!--已开通未开始 -->
								<a class="info_detail ver_block" href="javascript:closeRecom(${rec.id})">资格关闭</a>
								<span class="ver_block ver_span">|</span>
								<a class="info_detail ver_block changlookBtn" onclick="openWinAuto('back/model/html/popularizeUser/active_setting.html?isRecommend=1&userId='+${rec.userId},'active_setting',1100,600)">修改关联活动</a>
								<span class="ver_block ver_span">|</span>
								<a class="info_detail ver_block invalidBtn" href="javascript:invalidRecom(${rec.id})">置为无效</a>
							</c:if>
						</c:if>
						<c:if test="${rec.isRecommend==2 }"><!--已关闭 -->
							<a class="info_detail ver_block" href="javascript:openRecom(${rec.id})">资格开通</a>
							<span class="ver_block ver_span">|</span>
							<a class="info_detail ver_block changlookBtn" onclick="openWinAuto('back/model/html/popularizeUser/active_setting.html?isRecommend=2&userId='+${rec.userId},'active_setting',1100,600)">查看活动</a>
							<span class="ver_block ver_span">|</span>
							<a class="info_detail ver_block invalidBtn" href="javascript:invalidRecom(${rec.id})">置为无效</a>
						</c:if>
						<c:if test="${rec.isRecommend==3 }"><!--无效 -->
							<a class="info_detail ver_block changlookBtn" onclick="openWinAuto('back/model/html/popularizeUser/active_setting.html?isRecommend=3&userId='+${rec.userId},'active_setting',1100,600)">查看活动</a>
						</c:if>
					</td>
				</tr>
			</c:forEach>
			</table>
		</div>
		<!--此处引入footer.jsp-->
        <jsp:include page="/back/jsp/footer.jsp" flush="true"/>


		<!--推广-置为无效窗口-->
		<div class="outLook unvalidType" style="display: none;" >
			<div class="innerLook">
				<h2>设置无效</h2>
				<ul class="ideaLook">
					<li class="cf">
							<label class="fl" for="">无效原因：</label>
							<textarea class="fl" name="nullifyReason" id="nullifyReason" maxlength="50" cols="30" rows="10" style="width: 210px;height: 90px;padding:6px" placeholder="最多输入50个字"></textarea>
							
					</li>
				</ul>
				<p class="unvalidBlock">用户置为无效后将不可恢复推荐人身份</p>
				<div class="ideabtnlabel cf" style="width: 200px;">
				<input type="hidden" id="recomID">
					<button class="fl" onclick="invalidRecomSubmit()">确定</button>
					<button id="look_angin" class="fr">取消</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>
	</div>
</body>

<script type="text/javascript" src="back/model/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/model/js/infoMessage.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>  
<script>
	var website = getRootPath_web();
	function invalidRecom(id){
		$("#nullifyReason").val("");
		$("#recomID").val(id);
		$(".unvalidType").show();
	}
	//	$(".invalidBtn").click(function(event) {
	//	});	
	$("#look_angin,.closelookBtn").click(function(event) {
		$(".outLook").hide();
		$(".unvalidType .unvalidBlock").html('用户置为无效后将不可恢复推荐人身份');
	});
	
	$(document).click(function(event) {
		$(".outLook").hide();
	});
	$(".outLook,.invalidBtn").click(function(event) {
		event.stopPropagation();
	

	$("#nullifyReason").focus(function(event) {
		$(".unvalidType .unvalidBlock").html('用户置为无效后将不可恢复推荐人身份');
	});
	
	clearTimeout(times);
	var times = setTimeout(function(){
	  $(".delete_msg_b").fadeOut(1000);
	},2000);
	});

  function queryList(){
	  var form=document.forms[0];
// 		$("#pageNo").val(1);
		form.submit();
  }
//资格关闭
  function closeRecom(id){
	  if(confirm("确定关闭该用户的推广资格?")){
		  $.ajax({
	          type: "post",
	          url: "admin/friend/closeRecommend",
	          async: false,
	          data: {id: id},
	          dataType: "json",
	          success: function (data) {
	              if (data.code==200) {
	              	location.reload();
	              } else {
	                  alert(data.msg);
	              }
	          },
	          error: function (XHR, textStatus, errorThrown) {
	              alert("网络有点慢，请重试！");
	          }
	      });
	  }
  }
  //资格开通
  function openRecom(id){
	  if(confirm("确定开通该用户的推广资格?")){
		  $.ajax({
	          type: "post",
	          url: "admin/friend/openRecommend",
	          async: false,
	          data: {id: id},
	          dataType: "json",
	          success: function (data) {
	              if (data.code==200) {
	              	location.reload();
	              } else {
	                  alert(data.msg);
	              }
	          },
	          error: function (XHR, textStatus, errorThrown) {
	              alert("网络有点慢，请重试！");
	          }
	      });
	  }
  }
  //无效提交
 function invalidRecomSubmit(){
	  var reason=$("#nullifyReason").val();
	  if(reason==null || reason==""){
		  // alert("请填写无效原因");
		  $(".unvalidType .unvalidBlock").html('请填写无效原因')
		  return ;
	  }
	  var id=$("#recomID").val();
	  $.ajax({
          type: "post",
          url: "admin/friend/invalidRecommend",
          async: false,
          data: {id: id,reason:reason},
          dataType: "json",
          success: function (data) {
              if (data.code==200) {
              	location.reload();
              } else {
                  alert(data.msg);
              }
          },
          error: function (XHR, textStatus, errorThrown) {
              alert("网络有点慢，请重试！");
          }
      });
  }
  function excelRecExport(){ 
		var checkResult = validExportCookie("recommend_list_excelRecExport");
		if(checkResult == undefined ||checkResult == "" || checkResult == "-1"){
			alert("请求异常，请稍后重试！");
			return ;
		}
		if(checkResult 	!= "0"){
			alert(checkResult);
			return ;
		}
		var form=document.forms[0];
		form.action="<%=request.getContextPath()%>/admin/friend/excelRecommenderExport";
		form.submit();
		form.action="<%=request.getContextPath()%>/admin/friend/getRecommenderList";
	}
  function getRootPath_web() {
		//获取当前网址，如： http://localhost:8083/uimcardprj/share/meun.jsp
		var curWwwPath = window.document.location.href;
		//获取主机地址之后的目录，如： uimcardprj/share/meun.jsp
		var pathName = window.document.location.pathname;
		var pos = curWwwPath.indexOf(pathName);
		//获取主机地址，如： http://localhost:8083
		var localhostPaht = curWwwPath.substring(0, pos);
		//获取带"/"的项目名，如：/uimcardprj
		var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
		return (localhostPaht + projectName);
	}

</script>
</html>
