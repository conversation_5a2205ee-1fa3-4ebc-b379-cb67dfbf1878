<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags"%>
<%@ page import="com.tyt.model.User"%>
<%@ page import="com.tyt.util.Constant"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <base href="<%=basePath%>">
  <title>推广日志</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="back/model/js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="back/js/My97DatePicker/WdatePicker.js"></script>
  <script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script>
  <script type="text/javascript" src="back/jsp/js/ajax_source3.js"></script>
<script type="text/javascript" src="back/jsp/js/ajax_source2.js"></script>
  <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>

<script type="text/javascript">
function logList() {
	 var form=document.forms[0];
	form.submit();
}

function excelLogExport(){ 
	
	var checkResult = validExportCookie("log_list_excelLogExport");
	
	if(checkResult == undefined ||checkResult == "" || checkResult == "-1"){
		alert("请求异常，请稍后重试！");
		return ;
	}
	if(checkResult 	!= "0"){
		alert(checkResult);
		return ;
	}
	var form=document.forms[0];
	form.action="<%=request.getContextPath()%>/admin/friend/excelLogExport";
	form.submit();
	form.action="<%=request.getContextPath()%>/admin/friend/logList";
}
</script>

</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>推广功能管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">推广日志</span>
		</h1>
		<form name="friendLogForm" id="friendLogForm"
		action="<%=request.getContextPath()%>/admin/friend/logList" method="post">
		<input type="hidden" id="pageNoId" name="pageNo" value="${pageNo}"> 
		<inputtype="hidden" name="pageSize" value="${pageSize}">
		<input type="hidden" id="deliverTypeOneId" value="${friend.recDeliverTypeOne }" /> 
		<input type="hidden" id="identityTypeId" value="${friend.recUserSign }" />
		<div class="searchBox searchBoxBt">
			<h2 class="searchTit">推荐人</h2>
			<ul class="searchUl searchUlNone cf">
				<li><label>用户ID：</label><input type="text" name="userId" value="${friend.userId }"></li>
				<li><label>姓名：</label><input type="text" name="userName" value="${friend.userName }"></li>
				<li><label>手机号：</label><input type="text" name="cellPhone" value="${friend.cellPhone }"></li>
				<li><label for="">推广时间：</label><input readonly  onfocus=this.blur() onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="pinfoTable" type="text" name="ctimeStart" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${friend.ctimeStart }"/>" ><span>-</span><input  onfocus=this.blur() readonly onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="pinfoTable" type="text" name="ctimeEnd" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${friend.ctimeEnd }"/>"></li>
			</ul>
		</div>
		<div class="searchBox">
			<h2 class="searchTit">被推荐人</h2>
			<ul class="searchUl searchUlNone cf">
				<li><label for="">手机号：</label><input type="text" name="recCellPhone" value="${friend.recCellPhone }"></li>
				<li>
					<label for="">注册身份：</label>
					<select name="recUserSign"
							id="identity_type_select_lable_id" class="select180"></select>			
				</li>
				<li>
					<label for="">审核一级：</label>
					<select name="recDeliverTypeOne"
							id="deliver_type_one_select_lable_id" class="select180"></select>			
				</li>
				<li>
					<label for="">认证状态：</label>
					<select name="isAuth" id="">
						<option value="">----</option>
						<option value="0"
							<c:if test="${friend.isAuth == 0}">selected</c:if>>未认证</option>
						<option value="1"
							<c:if test="${friend.isAuth == 1}">selected</c:if>>已认证</option>
					</select>			
				</li>
				<li>
					<label for="">开通付费年限：</label>
					<select name="moneyYear" class="select180">
								<option value="">----</option>
								<option value="1"
									<c:if test="${friend.moneyYear ==1}">selected</c:if>>1</option>
								<option value="2"
									<c:if test="${friend.moneyYear == 2}">selected</c:if>>2</option>
								<option value="3"
									<c:if test="${friend.moneyYear == 3}">selected</c:if>>3</option>
								<option value="0"
									<c:if test="${friend.moneyYear == 0}">selected</c:if>>未付费</option>
							</select>
				</li>
				<li>
					<label for="">终端来源：</label>
					<select name="platId"
							class="select180">
								<option value="">----</option>
								<option value="1"
									<c:if test="${friend.platId == 1}">selected</c:if>>PC</option>
								<option value="2"
									<c:if test="${friend.platId == 2}">selected</c:if>>Android</option>
								<option value="3"
									<c:if test="${friend.platId == 3}">selected</c:if>>IOS</option>
								<option value="4"
									<c:if test="${friend.platId == 4}">selected</c:if>>APAD</option>
								<option value="5"
									<c:if test="${friend.platId == 5}">selected</c:if>>IPAD</option>
								<option value="6"
									<c:if test="${friend.platId == 6}">selected</c:if>>WEB</option>
								<option value="7"
									<c:if test="${friend.platId == 7}">selected</c:if>>PHONE WEB</option>

							</select>			
				</li>
			</ul>
		</div>
		</form>
		<div class="pageMessage" style="position: relative;">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="" type="button" value="查询" onclick="logList();"></li>
					<li><input class="" type="button" value="重置" onclick="javascript:$('#friendLogForm')[0].reset()"></li>
					<li><input type="button" value="导出" onclick="excelLogExport();"/></li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>${rowCount }</em>条信息，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页
            </div>
            <!--添加msg返回值,提示删除成功 , 并刷新-->
            <b class="delete_msg_b"><c:out value="${msg }"/></b>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th width="60">推荐人ID</th>
					<th>推荐人姓名</th>
					<th>推荐人手机号</th>
					<th>推广时间</th>
					<th>被推荐人手机号</th>
					<th>被推荐人注册身份</th>
					<th>认证状态</th>
					<th>审核身份</th>
					<th>是否付费</th>
					<th>付费详情</th>
					<th>终端来源</th>
					<th>奖励金额</th>
				</tr>
				<c:if test="${!empty friends }">
					<c:forEach items="${friends }" var="friend">
						<tr>
							<td><c:out value="${friend.userId }" /></td>
							<td><c:out value="${friend.userName }" /></td>
							<!-- <td class="phoneHide"><c:out value="${friend.cellPhone }" /></td> -->
							<td class="blue"onclick="javascript:phoneSoHfn($(this),'${friend.id}','推广日志','special','cellPhone')">查看电话</td>
							<td><fmt:formatDate value="${friend.ctime }" pattern="yyyy-MM-dd HH:mm:ss" /></td>
							<!-- <td class="phoneHide"><c:out value="${friend.recCellPhone }" /></td> -->
							<td class="blue"onclick="javascript:phoneSoHfn($(this),'${friend.id}','推广日志','special','recCellPhone')">查看电话</td>
							<td><c:if test="${friend.isAuth == 1 }"><c:if test="${!empty friend.recUserSign}"><c:choose><c:when test="${friend.recUserSign==1 || friend.recUserSign==2 || friend.recUserSign==3 || friend.recUserSign==4 || friend.recUserSign==5 }"><s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_1',${friend.recUserSign})" var="sf" />${sf.name}</c:when><c:when test="${friend.recUserSign==6 || friend.recUserSign==7 || friend.recUserSign==8 ||friend.recUserSign==9 }"><s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_2',${friend.recUserSign})" var="sf" />${sf.name}</c:when></c:choose></c:if></c:if>
								<c:if test="${friend.isAuth != 1 }">未知身份</c:if>
							</td>
							<td><c:if test="${friend.isAuth == 0}">未认证</c:if><c:if
									test="${friend.isAuth == 1}">已认证</c:if>
							<td><c:if test="${! empty  friend.recDeliverTypeOne}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_one',${friend.recDeliverTypeOne})"
										var="sf" />${sf.name}
								</c:if></td>
							<td><c:if test="${friend.isMoney == 0}">否</c:if><c:if
									test="${friend.isMoney == 1}">是</c:if></td>
							<td><c:if test="${friend.isMoney == 0}">未付费</c:if><c:if
									test="${friend.moneyYear == 1}">一年</c:if><c:if
									test="${friend.moneyYear == 2}">两年</c:if><c:if
									test="${friend.moneyYear == 3}">三年</c:if></td>
							<td><c:if test="${friend.platId == 1}">PC</c:if>
			                    <c:if test="${friend.platId == 2}">Android</c:if>
			                    <c:if test="${friend.platId == 3}">IOS</c:if>
			                    <c:if test="${friend.platId == 4}">APad</c:if>
			                    <c:if test="${friend.platId == 5}">IPAD</c:if>
			                    <c:if test="${friend.platId == 6}">WEB</c:if>
			                    <c:if test="${friend.platId == 7}">PHONE WEB</c:if>
			                </td>
							<td><fmt:formatNumber value="${friend.recMoney }" type="number"  pattern="#.00"/></td>
						</tr>
					</c:forEach>
				</c:if>
			</table>
		</div>
		<!--此处引入footer.jsp-->
        <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
	</div>
</body>

<script type="text/javascript" src="back/model/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/model/js/infoMessage.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>   

<!-- <script type="text/javascript" src="back/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/js/infoMessage.js"></script>
</html>-->
