$(function(){
	/*初始化用户渠道*/
	// getSourceByGroupCode("app_channel",$("#searchChannelCode").val(),$("#channelSelect"),true);
	/*初始化用户注册身份*/
	getSourceByGroupCode("user_identity_type_1",$("#identityTypeId").val(),$("#identity_type_select_lable_id"),true);
	getSourceByGroupCode("user_identity_type_2",$("#identityTypeId").val(),$("#identity_type_select_lable_id"),false);
	/*初始化销售审核一级身份*/
	getSourceByGroupCode("user_deliver_type_one",$("#deliverTypeOneId").val(),$("#deliver_type_one_select_lable_id"),true);
	/*初始化销售审核二级身份*/
	if($("#deliverTypeOneId").val()>-1){
    	getSubSourceByGroupCode("user_deliver_type_one",$("#deliverTypeOneId").val(),$("#deliverTypeTwoId").val(),$("#deliver_type_two_select_lable_id"),true);
    }
	/*初始化审核备注*/
	if($("#deliverTypeTwoId").val()>-1){
		getSub3SourceByGroupCode("user_deliver_type_two",$("#deliverTypeTwoId").val(),$("#deliverTypeThreeId").val(),$("#deliver_type_three_select_lable_id"),true);
	}

	/*销售审核一级、二级身份级联操作*/
	$("#deliver_type_one_select_lable_id").change(function(){
    	getSubSourceByGroupCode("user_deliver_type_one",this.value,$("#deliverTypeTwoId").val(),$("#deliver_type_two_select_lable_id"),true);
		getSub3SourceByGroupCode("user_deliver_type_two",this.value,$("#deliverTypeThreeId").val(),$("#deliver_type_three_select_lable_id"),true);
	});
	if($("#deliverTypeOneId").val()==""){
		$("#deliver_type_one_select_lable_id").val("13");
		getSubSourceByGroupCode("user_deliver_type_one","13",$("#deliverTypeTwoId").val(),$("#deliver_type_two_select_lable_id"),true);
		$("#deliver_type_two_select_lable_id").val("29");
	}
	/*销售审核二级、审核备注级联操作*/
	$("#deliver_type_two_select_lable_id").change(function(){
		getSub3SourceByGroupCode("user_deliver_type_two",this.value,$("#deliverTypeThreeId").val(),$("#deliver_type_three_select_lable_id"),true);
	});
	if($("#deliverTypeTwoId").val()==""){
		$("#deliver_type_two_select_lable_id").val("29");
		getSub3SourceByGroupCode("user_deliver_type_two","29",$("#deliverTypeThreeId").val(),$("#deliver_type_three_select_lable_id"),true);
		$("#deliver_type_three_select_lable_id").val("6");
	}
	//货主审核主体、身份
	getSourceByGroupCode("audit_goods_type_first", -1, $("#auditGoodsTypeFirst"), true);
	getSourceByGroupCode("audit_goods_type_second", -1, $("#auditGoodsTypeSecond"), true);

});
/**
 * 根据groupCode生成select下拉框选项
 * @param groupCode：
 * @param defaultValue：页面默认值
 * @param selectObj：select标签对象
 * @param emptyFlag：是否初始化select标签。因为用户注册身份就是两个groupCode的组合值
 */
function getSourceByGroupCode(groupCode,defaultValue,selectObj,emptyFlag){
	$.ajax({
		async:false, 
		type:"post",
	    url:"boss/tytsource/getByGroupCode",
	    data:"groupCode="+groupCode,
	    cache: true,
	    dataType:"json",
	    success:function(data){
			if(groupCode == "audit_goods_type_first" || groupCode == "audit_goods_type_second"){
				if(emptyFlag)selectObj.empty().append("<option value='0'>------</option>");
			}else{
				if(emptyFlag)selectObj.empty().append("<option value='-1'>------</option>");
			}
			if (emptyFlag) $('#deliver_type_three_select_lable_id').empty().append("<option value='-1'>------</option>");
			for(var i=0;i<data.length;i++){
		    	if(data[i].remark != '废弃') {
					if(!isNaN(defaultValue)&&parseInt(defaultValue) == parseInt(data[i].value)){
						selectObj.append("<option selected value='"+data[i].value+"'>"+data[i].name+"</option>");
					}else{

						if(isNaN(defaultValue)&& parseInt(data[i].value)==parseInt(data[data.length-1].value)){
							selectObj.append("<option selected value='"+data[i].value+"'>"+data[i].name+"</option>");

						}else{
							selectObj.append("<option value='"+data[i].value+"'>"+data[i].name+"</option>");
						}
					}
				}
		    }
	    },
	    error: function (jqXHR, textStatus, errorThrown) {
            alert("error:getSourceByGroupCode");
        }
	  });
}
/**
 * 根据父级信息获取相应的子级下拉框值
 * @param groupCode：父级groupCode
 * @param parentValue:父级值
 * @param subValue：子级默认值
 * @param selectObj：select对象
 * @param emptyFlag：是否empty
 */
function getSubSourceByGroupCode(groupCode,parentValue,subValue,selectObj,emptyFlag){
	$.ajax({
		async:false, 
		type:"post",
	    url:"boss/tytsource/getByParent",
	    data:"groupCode="+groupCode+"&value="+parentValue,
	    cache: true,
	    dataType:"json",
	    success:function(data){
	    	if(emptyFlag)selectObj.empty().append("<option value='-1'>------</option>");
			if (emptyFlag) $('#deliver_type_three_select_lable_id').empty().append("<option value='-1'>------</option>");
		    for(var i=0;i<data.length;i++){
				if(data[i].remark != '废弃') {
					if(parseInt(subValue) == parseInt(data[i].value)){
						selectObj.append("<option selected value='"+data[i].value+"'>"+data[i].name+"</option>");
					}else{
						selectObj.append("<option value='"+data[i].value+"'>"+data[i].name+"</option>");
					}
				}
		    }
	    },
	    error: function (jqXHR, textStatus, errorThrown) {
            alert("error:getSubSourceByGroupCode");
        }
	  });
}
/**
 * 审核备注根据父级信息获取相应的子级下拉框值
 * @param groupCode：父级groupCode
 * @param parentValue:父级值
 * @param subValue：子级默认值
 * @param selectObj：select对象
 * @param emptyFlag：是否empty
 */
function getSub3SourceByGroupCode(groupCode,parentValue,subValue,selectObj,emptyFlag){
	$.ajax({
		async:false,
		type:"post",
		url:"boss/tytsource/getByParent",
		data:"groupCode="+groupCode+"&value="+parentValue,
		cache: true,
		dataType:"json",
		success:function(data){
			if(emptyFlag)selectObj.empty().append("<option value='-1'>------</option>");
			for(var i=0;i<data.length;i++){
				if(data[i].remark != '废弃') {
					if(parseInt(subValue) == parseInt(data[i].value)){
						selectObj.append("<option selected value='"+data[i].value+"'>"+data[i].name+"</option>");
					}else{
						selectObj.append("<option value='"+data[i].value+"'>"+data[i].name+"</option>");
					}
				}
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			alert("error:getSubSourceByGroupCode");
		}
	});
}
