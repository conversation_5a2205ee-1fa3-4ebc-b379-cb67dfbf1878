<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<title>缴费流水查询</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="back/model/js/bootstrap.js"></script>
<script src="back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script>
<script type="text/javascript">
function exportAccounts(){
	var checkResult = validExportCookie("success_account_list");
	
	if(checkResult == undefined ||checkResult == "" || checkResult == "-1"){
		alert("请求异常，请稍后重试！");
		return ;
	}
	
	if(checkResult != "0"){
		alert(checkResult);
		return ;
	}
	var form=document.forms[0];
	form.action="account/excel";
	form.submit();
	form.action="account/get";
}

function doQuery() {
	var form = document.forms[0];
	form.pageNo.value = 1;
	form.submit();
}
</script>
</head>
<body>
    <div class="contentBox">
        <div class="titUrl cf">
          <h1 class=" fl">
            <span>当前所在位置：</span><span>财务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">缴费流水查询</span>
          </h1>
        </div>
        <div class="main-content">
          <!--搜索条件-->
          <form id="form" action="account/get" method="post">
          <input type="hidden" name="pageNo" value="<c:out value="${pageNo}"/>">
        <div class="searchBox" id="searchCondition">
          <ul class="searchUl searchUlNone cf">
                <li>
                <label>用户姓名：</label><input class="input180" name="trueName" type="text" value="${account.trueName}">
                </li>
                <li><label>手机号：</label><input class="input180" name="cellPhone" type="text" value="${account.cellPhone}"></li>
                <li><label>转账银行：</label><input class="input180" name="bank" type="text" value="${account.bank}"></li>
                 <li>
                <label>缴费渠道：</label>
                <select name="channel" class="select180">
                <option value="">---------------</option>
                <c:forEach items="${payChannels}" var="channel">
                <option value="${channel.value}" <c:if test="${account.channel==channel.value}">selected</c:if>>${channel.name}</option>
                </c:forEach>
                </select></li>
                <li>
                <label>开通年限：</label>
                <select name="years" class="select180">
                <option value="">---</option>
                <option value="1" <c:if test="${account.years==1}">selected</c:if>>1</option>
                <option value="2" <c:if test="${account.years==2}">selected</c:if>>2</option>
                <option value="3" <c:if test="${account.years==3}">selected</c:if>>3</option>
                </select>
                </li>
                <li><label>缴费金额：</label><input id="money" class="input180" name="money" type="text" value="${account.money}"></li>
                <li><label>缴费次第：</label>
                <input class="pinfoTable" name="number" type="text" value="${account.number}">
                <span>-</span><input class="pinfoTable" name="numberEnd" type="text" value="${account.numberEnd}">
                </li>
                 <li><label>缴费时间：</label>
                <input class="pinfoTable" name="startPayDate" type="text" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${account.startPayDate }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                <span>-</span><input class="pinfoTable" name="endPayDate" type="text" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${account.endPayDate }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"></li>
                <li>
                <label>维护人：</label><input class="input180" name="maintainMan" type="text" value="${account.maintainMan}">
                </li>
               <%--  <li>
                	<label>每页显示：</label><input class="input180" name="pageSize" type="text" value="${pageSize}">
                	<input type="hidden" name="pageSize" class="input180" value="20">
                </li> --%>
             </ul>
          </div>
         
          <!--页面信息-->
      <div class="pageMessage">
          <div class="searchBtn">
                <ul class="btnBox cf">
                  <li><input type="button" onclick="doQuery();" class="button" value="查询"></li>
                  <li><input type="reset" class="button" value="重置"></li>
                  <li><input type="button" class="button" value="导出" onclick="exportAccounts();"></li>
               </ul>
              </div>
             <div class="messageRight fr">
             查询到<em>${rowCount }</em>条数据，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页。
             </div>
          </div>
           </form>

         <!--内容列表-->
        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
         <div class="dataTable">
        <table width="100%" border="0" class="tableBox">
  <tr class="tableTh">
    <th>ID</th><th>用户姓名</th><th width="80">手机号</th><th>缴费日期</th><th>销售人员</th><th>维护人</th><th>缴费类型</th>
    <th>缴费次第</th>
    <th>缴费渠道</th><th>转账银行</th><th>缴费金额</th><th>开通年限</th><th>到期日期</th>
      <th>优惠金额</th>
      <th>优惠券名称</th>
  </tr>


  <c:forEach items="${accounts}" var="accountss">
    <tr>
    <td>${accountss.id}</td>
    <td>${accountss.trueName}</td>
    <td class="blue" onclick="javascript:phoneSoHfn($(this),'${accountss.cellPhone}','会员缴费流水查询')">查看电话</td>
    <td><fmt:formatDate pattern="yyyy-MM-dd HH:mm:ss" value="${accountss.payDate}"/></td>
    <td>${accountss.saleName}</td>
    <td>${accountss.maintainMan}</td>
    <td>
    <c:if test="${!empty accountss.type}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('account_pay_type',${accountss.type})" var="sf" />
                                    <p>${sf.name}</p>
                                    </c:if>
    </td>
    <td>${accountss.number}</td>
    <td> <c:if test="${!empty accountss.channel}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('account_pay_channel',${accountss.channel})" var="sf" />
                                    <p>${sf.name}</p>
                                    </c:if></td>
    <td>${accountss.bank}</td>
    <td>
    <c:if test="${! empty accountss.money}">
    <fmt:formatNumber type="number" value="${accountss.money/100} " maxFractionDigits="2"/>
    </c:if>
    </td>
    <td>${accountss.years}</td>
    <td><fmt:formatDate pattern="yyyy-MM-dd" value="${accountss.endDate}"/></td>
        <td><fmt:formatNumber type="number" value="${accountss.couponAmount}" maxFractionDigits="2"/> </td>
        <td>${accountss.couponName}</td>

    </tr>
  </c:forEach>
</table>
</div>
<jsp:include  page="footer.jsp" flush="true"/>
</div>
</div>
</body>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>
</html>
