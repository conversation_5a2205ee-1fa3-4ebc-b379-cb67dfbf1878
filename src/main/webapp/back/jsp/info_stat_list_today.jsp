<%@page import="java.util.Date"%>
<%@page import="com.tyt.util.TimeUtil"%>
<%@page import="com.tyt.model.User"%>
<%@page import="com.tyt.web.qbean.InfoStatBean"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ page import="com.tyt.model.Transport" %>
<%@ page import="com.tyt.util.Constant" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>运输信息统计列表-管理后台</title>
    <script type="text/javascript" src="<%=request.getContextPath()%>/back/js/changeColor.js"></script>
	<link href="<%=request.getContextPath()%>/back/css/dictmng.css" rel="stylesheet" type="text/css">
	<script language="JavaScript" type="text/javascript" src="<%=request.getContextPath()%>/back/js/jquery-1.7.1.js"></script>
	<%-- <script type="text/javascript">
        function query(pageNo){
            var form = document.forms[0];
            form.pageNo.value = pageNo;
            form.submit();
        }
        function toAction(act){
            var form = document.forms[0];
            form.act.value = act;
            form.submit();
        }
        
        function toReview(status){
            var form = document.forms[1];
            form.status.value = status;
            form.submit();
        }
    </script> --%>
    <script type="text/javascript">
</script>
</head>
<body>
<form name="infoForm" action="<%=request.getContextPath()%>/admin/infostatlisttoday" method="post"  onsubmit="return checkEndTime();">
<table width="1300" border="0" cellpadding="0" cellspacing="0">
	<tr bgcolor="#BFBFBF">
	  <td>

    <input type="hidden" name="pageNo" value="<c:out value="${pageNo}"/>" >
    <input type="hidden" name="act" value="0" >
<table width="100%" border="0" cellpadding="2" cellspacing="1">
 <tr class="SolidRow">
  <td id="tel">注册电话:<input type="text" name="tel"  value="<c:out value="${infoStatQueryBean.tel }"/>" size="10" /></td>
  <td id="pubQQ">QQ号:<input type="text" name="pubQQ"  value="<c:out value="${infoStatQueryBean.pubQQ }"/>" size="10" /></td>
  <td>用户状态: <select name="userType">
                 <option value="">----</option>
                 <option value="<%=User.USER_TYPE_TRIAL%>" <c:if test="${infoStatQueryBean.userType == 0}">selected</c:if>  >试用</option>
                 <option value="<%=User.USER_TYPE_VIP%>" <c:if test="${infoStatQueryBean.userType == 1}">selected</c:if>>付费</option>
             </select>
             <c:if test="${userSign==8}">&nbsp;&nbsp;&nbsp;销售: <input type="text" name="sales" size="4" value="<c:out value="${infoStatQueryBean.sales}"/>" ></c:if>
  </td>
 </tr>
<c:if test="${userSign==5||userSign==6||userSign==9}">
<tr class="SolidRow">
 <td> 销售: <input type="text" name="sales" size="4" value="<c:out value="${infoStatQueryBean.sales}"/>" ></td>
<td>
     剩余天数: <input type="text" name="serveDaysBegin" size="4" value="<c:out value="${infoStatQueryBean.serveDaysBegin}"/>">~ <input type="text" name="serveDaysEnd" size="4" value="<c:out value="${infoStatQueryBean.serveDaysEnd}"/>" >&nbsp;  
</td>
<td>备注:<input type="text" name="note" size="5" value="<c:out value="${infoStatQueryBean.note }"/>" />
</td>
</tr>
 </c:if>
 <tr class="SolidRow">
 <td colspan="3">
     <input type="submit" value="查询" onclick="toAction(0)">
     <input type="reset" value="重置">
 </td>
 </tr>
</table>
</td></tr></table>
</form>
<h6>
    查询到:<c:out value="${rowCount}"/>个统计信息<%-- ， 当前第  <c:out value="${pageNo }"/>页，总共<c:out value="${maxPage }"/>页 --%>
</h6>

<table width="1300" border="0" cellpadding=0 cellspacing=0>
		<tr>
<td>
  <table width="100%" border="0" cellpadding="2" cellspacing="1" id="table1">
    <tr class="TitleRow">
        <td>ID</td>
        <td>真实姓名</td>
        <td>销售</td>
        <td>备注</td>
        <td>状态</td>
        <td>剩余天数</td>
        <td>用户注册电话</td>
        <td>发布信息账号</td>
        <td>信息联系电话</td>
        <td>QQ</td>
        <td>昵称</td>
        <td>人工有效</td>
        <td>人工成交</td>
        <td>人工总数</td>
        <td>自动有效</td>
        <td>人工/自动</td>
        <td>提示信息</td>
    </tr>

    <c:if test="${!empty infoStats }">
  <%
int i = 0;
%>
    
        <c:forEach items="${infoStats }" var="transportStatBean">
        
        <%
if ( ++i % 2 == 0 )
{
%>
		  <tr class="EvenRow" onClick="change()"   onMouseOut="out()">
<%
}
else
{
%>
		  <tr class="OddRow" onClick="change()"   onMouseOut="out()">
<%
}
%>
                <td>${transportStatBean.id}</td>
                <td>${transportStatBean.trueName}</td>
                <td>${transportStatBean.sales}</td>
                <td>${transportStatBean.note}</td>
                 <td>
                     <%-- <c:if test="${transportStatBean.userType == 0}">试用</c:if>
                     <c:if test="${transportStatBean.userType == 1}">付费</c:if>
                     <c:if test="${transportStatBean.userType == 2} ">未激活</c:if> --%>
                     <c:if test="${fn:contains(transportStatBean.userType, 0)}">试用</c:if>
                     <c:if test="${fn:contains(transportStatBean.userType, 1)}">付费</c:if>
                     <c:if test="${fn:contains(transportStatBean.userType, 2)}">未激活</c:if>
                </td>
                <td>${transportStatBean.serveDays}</td>
                <td> 
                <%-- <c:if test="${transportStatBean.cellPhone!=0}">${transportStatBean.cellPhone}</c:if> --%>
                ${transportStatBean.cellPhone}
                </td>
                <td>${transportStatBean.uploadCellPhone}</td>
                <td>${transportStatBean.tel}</td>
                <td>
                <%-- <c:if test="${transportStatBean.qq == 0}">无</c:if>
                <c:if test="${transportStatBean.qq != 0}">${transportStatBean.qq}</c:if>  --%> 
                ${transportStatBean.qq} 
                </td>
                <td>${transportStatBean.nickName}</td>
                <td> ${transportStatBean.mnualEnable}</td>
                <td>${transportStatBean.manualDeal} </td>
                <td> ${transportStatBean.manualTotal}</td>
                 <td>
                   ${transportStatBean.autoEnable}
                </td>
                <td>
                  ${transportStatBean.manualTotal}:${transportStatBean.autoEnable}
                </td>
               <td>
                 <c:if test="${transportStatBean.alertInfomation=='未注册'}"><span style="color: red">${transportStatBean.alertInfomation}</span></c:if>
                <c:if test="${transportStatBean.alertInfomation=='付费使用'}"><span style="color: #009acd">${transportStatBean.alertInfomation}</span></c:if>
                <c:if test="${transportStatBean.alertInfomation=='已注册试用'}"><span style="color: #8b0000">${transportStatBean.alertInfomation}</span></c:if>
                <c:if test="${transportStatBean.alertInfomation=='付费用户，未用注册手机发布'}"><span style="color: #8a2be2">${transportStatBean.alertInfomation}</span></c:if>
                <c:if test="${transportStatBean.alertInfomation=='付费,未使用'}"><span style="color: #00008b">${transportStatBean.alertInfomation}</span></c:if>
               </td>
            </tr>
            
            
           
            
        
        </c:forEach>
    </c:if>
    
    <tr class="TitleRow">
       <td colspan="17"> 
          <c:forEach items="${pageNoList }" var="pageNo">
            <input type="button" value="<c:out value="${pageNo }"/>" onClick="query('<c:out value="${pageNo }"/>');">&nbsp;
            <%--<c:if test="${pageNo % 50  ==0 }"> <br/></c:if>--%>
          </c:forEach>  
       </td>
    </tr>
</table>

</td></tr></table>


</body>
</html>