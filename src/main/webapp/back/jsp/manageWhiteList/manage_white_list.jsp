<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%><%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <base href="<%=basePath%>">
  <title>后台登录IP管理</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
 <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="back/jsp/waybillOrderQuery/css/way_bill_list.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="back/model/js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
  <script type="text/javascript" src="back/js/My97DatePicker/WdatePicker.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>企业员工管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">后台登录IP管理</span>
		</h1>
		<form action="<%=basePath%>/jurisdiction/whiteList/getWhiteList" method="post" id="form1">
		<input type="hidden" id="pageNo" value="${pageNo}" name="pageNo">
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li><label for="">ID：</label>
				<input type="text" name="id" value="${queryBean.id }" maxlength="10"></li>
				<li><label for="">登录帐号：</label>
				<input type="text" name="loginPhone" value="${queryBean.loginPhone }" maxlength="11"></li>
				<li><label for="">IP：</label>
				<input type="text" name="ip" value="${queryBean.ip }" maxlength="20"></li>
				
			</ul>
		</div>
			</form>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="" type="button" value="查询" onclick="queryList();"></li>
					<li><input class="" type="button" value="重置" onclick="javascript:$('#form1')[0].reset()"></li>
					<li><input class="" type="button" value="新增" onclick="saveWhitelist();"></li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>${rowCount }</em>条信息，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页
            </div>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th>ID</th>
					<th>登录帐号</th>
					<th>IP</th>
					<th>创建时间</th>
					<th>操作人</th>
					<th>操作</th>
				</tr>
				<c:forEach items="${list }" var="bean">
				<tr>
					<td>${bean.id }</td>
					<td>${bean.loginPhone }</td>
					<td>${bean.ip }</td>
					<td><fmt:formatDate value="${bean.ctime }" pattern="yyyy-MM-dd HH:mm:ss" /></td>
					<td>${bean.operatorName }</td>
					<td>
						<a class="info_detail ver_block awardBtn" href="javascript:delWhitelist(${bean.id })">删除</a>
						<a class="info_detail ver_block awardBtn" href="javascript:editWhitelist(${bean.id },'${bean.loginPhone }','${bean.ip }')">编辑</a>
					</td>
				</tr>
				</c:forEach>
		    </table>
		    <input type="hidden" id="max_page"  name="max_page" value="${maxPage}">
		</div>
		<!--此处引入footer.jsp-->
    	 <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
    	 <!--新增白名单-->
    	<div class="alert alert-danger fade in alert-height" style="display: none;" id="outlook_danger_add">
			<div class="innerLook">
				<h2 id='addUpdateTitle'>添加白名单</h2>
				<div class="addWhiteUser cf">
					<ul>
						<li>
							<label for=""><i>*</i>登录帐号：</label>
							<input type="text" placeholder="请填写登录帐号" id="loginPhone" maxlength="11" style="padding-left: 5px;"><br/>
						</li>
						<li>
							<label for=""><i>*</i>IP：</label>
							<input type="text" placeholder="请填写IP" id="ip" maxlength="50" style="padding-left: 5px;">
						</li>
					</ul>
					<div class="makeSureBtn" style="width:68px;height:32px;margin: 0 auto;">
						<input class="button" type="button" id="saveOneButton" value="保存">
					</div>				
				</div>				
			</div>
			<i class="closelookBtn">×</i>
		</div> 
	</div>

</body>
<script type="text/javascript" src="back/model/js/colResizable-1.6.min.js"></script>
<%-- <script type="text/javascript" src="back/menu/js/infoMessage.js"></script> --%>
<script>
  var website = getRootPath_web();
	function queryList(){
		  var form=document.forms[0];
			$("#pageNo").val(1);
			form.submit();
	}
	function getRootPath_web() {
	//获取当前网址，如： http://localhost:8083/uimcardprj/share/meun.jsp
	var curWwwPath = window.document.location.href;
	//获取主机地址之后的目录，如： uimcardprj/share/meun.jsp
	var pathName = window.document.location.pathname;
	var pos = curWwwPath.indexOf(pathName);
	//获取主机地址，如： http://localhost:8083
	var localhostPaht = curWwwPath.substring(0, pos);
	//获取带"/"的项目名，如：/uimcardprj
	var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
	return (localhostPaht + projectName);
	}
	function showAddUpdateTip(title){
		$("#addUpdateTitle").html(title);
		$("#outlook_danger_add").show();
	}
	//添加白名单
	function saveWhitelist(){
		 showAddUpdateTip("添加白名单");
	}
	var whiteId='';
	//编辑白名单
	function editWhitelist(_id,_loginPhone,_ip) {
		whiteId=_id;
		showAddUpdateTip("编辑白名单");
		$("#loginPhone").val(_loginPhone);
		$("#ip").val(_ip);
	}
	$("#saveOneButton").click(function () {
		var loginPhone = $.trim($('#loginPhone').val());
		var ip = $.trim($('#ip').val());
		var flg={"id":whiteId,"loginPhone":loginPhone, "ip":ip};
		if (!loginPhone) {
			alert("请填写登录帐号");
			return ; 
		}
		if (!/^1\d{10}$/.test(loginPhone)) {
            alert("请输入正确的手机号码");
            return ;
        }
// 		if (!(/^1[3456789]\d{9}$/.test(loginPhone))){
// 			alert("请输入正确的手机号码");
// 			return;
// 		}
		if (!ip) {
			alert("请填写IP");
			return ;
		}
		if(!/^(([0-9]|([1-9]\d)|(1\d\d)|(2([0-4]\d|5[0-5])))\.)(([0-9]|([1-9]\d)|(1\d\d)|(2([0-4]\d|5[0-5])))\.){2}([0-9]|([1-9]\d)|(1\d\d)|(2([0-4]\d|5[0-5])))$/.test(ip)){
			alert("请输入正确的IP地址");
	        return ;
		}
		save(flg);
	});
	function save(flg) {
		$.ajax({
	        url: '' + website + '/jurisdiction/whiteList/saveWhitelist',
	        type: "post",
	        contentType: 'application/json; charset=utf-8',
	        data: JSON.stringify(flg),
	        dataType: "json",
	        success: function (data) {
	            if (data.code==200) {
	            	alert(data.msg);
	            	location.reload();
	            	hideAddUpdateTip();
	            } 
	             else {
	            	alert(data.msg);
	            }
	        },
	        error: function (XHR, textStatus, errorThrown) {
	            alert("网络有点慢，请重试！");
	        }
	    });
	}
	$(".closelookBtn").click(function () {
		hideAddUpdateTip();
	});
	function hideAddUpdateTip(){
		$("#loginPhone").val("");
		$("#ip").val("");
		$("#outlook_danger_add").hide();
	}
	//删除白名单
	function delWhitelist(_id) {
		if(confirm("确定要删除该条信息？")){
			$.ajax({
		        url: '' + website + '/jurisdiction/whiteList/deleteWhitelist',
		        type: "post",
		        data: {id:_id},
		        dataType: "json",
		        success: function (data) {
		            if (data.code==200) {
		            	alert(data.msg);
		            	location.reload();
		            } 
		             else {
		            	alert(data.msg);
		            }
		        },
		        error: function (XHR, textStatus, errorThrown) {
		            alert("网络有点慢，请重试！");
		        }
		    });
		}
	}
</script>
</html>
