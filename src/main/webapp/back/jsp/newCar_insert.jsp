<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/cityinsert.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-upload.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/imgvalidate.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/insert_submit.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/newCarValidate.js"></script>
<!-- <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/fabu.css"> -->
<link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/detailPages.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/detailPagesAdd.css" type="text/css" rel="stylesheet">
<title>特运通新车资讯--发布后台</title>
</head>
<body>
	<div class="contentBox">
	    <h1 class="titUrl">
	        <span>当前所在位置：</span><span>车辆方服务</span><span>&nbsp;&gt;&nbsp;</span><span>新车资讯管理-PC</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">新车发布</span>
	    </h1>
	    <div class="main-content">
			<div class="bj">&nbsp;&nbsp;&nbsp;&nbsp;<span style="color: red;font-size: 18px">${duplicate}</span>
				<form action="<%=request.getContextPath()%>/admin/newcarpub"
					method="post" name="publishForm1" enctype="multipart/form-data"
					onsubmit="return checkAll()">
					<div class="employeeOutbox">
						<ul>
							<li>
								<label for=""><img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">发布账号：</label>
								<input class="pro_city_coun cursor_none" type="text" id="cellPhone" name="cellPhone" value="${cellPhone}" disabled/>
								<input type="hidden" id="cellPhone" name="cellPhone" value="${cellPhone}"/>
							</li>
							<li>
								<label for=""><img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">新车广告：</label>
								<input class="pro_city_coun" type="text" id="title" name="title" value="包含公司名称" onfocus="clearTitle(this)" onblur="javascript:checkTitle()" />
							</li>
							<li>
								<label for="">类型：</label>
								<input type="radio" name="model" onclick="javascript:showCarName1()" value="<%=Constant.MODEL1%>" checked="checked">牵引头<input type="radio" name="model" onclick="javascript:showCarName2()" value="<%=Constant.MODEL2%>">挂车<input type="radio" name="model" onclick="javascript:showCarName3()" value="<%=Constant.MODEL3%>">牵引头加挂车
							</li>
							<li id="carName1">
								<label for="">品牌：</label>
								<input type="radio" name="carName" value="<%=Constant.CARNAME1%>">德龙
								<input type="radio" name="carName" value="<%=Constant.CARNAME2%>">欧曼
								<input type="radio" name="carName" value="<%=Constant.CARNAME3%>">东风
								<input type="radio" name="carName" value="<%=Constant.CARNAME4%>">北奔
								<input type="radio" name="carName" value="<%=Constant.CARNAME5%>">解放
								<input type="radio" name="carName" value="<%=Constant.CARNAME6%>">一汽重卡
								<input type="radio" name="carName" value="<%=Constant.CARNAME7%>">其它
							</li>
							<li>
								<label for="">类别：</label>
								<input type="radio" name="category" value="<%=Constant.CATEGORY1%>" checked="checked">在售车
								<input type="radio" name="category" value="<%=Constant.CATEGORY2%>">积压车
							</li>
							<li>
								<label for=""><img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">车源地址：</label>
								<select id="province" name="province"  onblur="javascript:checkAddress()" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
									<option value="">选择省份</option>
								</select>
		                		<select id="city" name="city"  style="margin-right:10px;width:100px; height:25px; font-size:14px;">
		                			<option value="">选择市</option>
		                		</select>
		                		<select id="county" name="county" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
		                			<option value="">选择县区</option>
		                		</select>
							    <input type="hidden" id="province00">
							    <input type="hidden" id="city00">
							    <input type="hidden" id="county00">
							</li>
							<li>
								<label for=""><img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">联系人：</label>
								<input class="pro_city_coun" type="text" id="telName" name="telName" value="请写真实姓名" onfocus="clearTelName(this)" onblur="javascript:checkTelName()">
							</li>
							<li>
								<label for=""><img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">联系电话：</label>
								<input class="pro_city_coun" type="text" id="telephone" name="telephone" value="无" onfocus="clearTelephone(this)" onblur="javascript:checkTelephone()">
							</li>
							<li>
								<label for="">图片上传：</label>
								<input type="file" name="images" id="images" onchange="preview5(this)" /> 
								<input type="button" value="增加" onclick="addFile();" style="background-color:#3961b2; border:0px;color:#fff; margin-top:3px;font-size: 14px;line-height: 32px;height: 32px;padding: 0 12px;color: #fff;border: none 0;border-radius: 4px;">
								<div  id="more" style="margin:16px 0 16px 120px;"></div>
							</li>
							<li style="height: 100px;">
								<label for="" style="vertical-align: middle;">经营范围：</label>
								<textarea style="vertical-align: middle;" id="range" name="scope" rows="6" cols="50" placeholder="300字以内"></textarea>
							</li>
							<li style="height: 100px;">
								<label for="" style="vertical-align: middle;">公司描述：</label>
								<textarea style="vertical-align: middle;" id="detail" name="detail" rows="6" cols="50" placeholder="300字以内"></textarea>
							</li>
						</ul>
					</div>
					<p class="errorBlock errorBlockNomal" id="errorSpan"></p>
	                <div class="tc mt60" style="  	text-align: left;margin: 30px 0 60px 120px;">
	                	<input id="" type="button" class="button" onclick="go();" id="fabu" value="立即发布">
						<input type="reset" class="button"  value="重置" id="resetId">
	                </div>
				</form>
			</div>
	    </div>
	</div>

	


	
</body>
</html>