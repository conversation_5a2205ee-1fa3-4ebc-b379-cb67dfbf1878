<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>特运通用户电话本--管理后台</title>
<link href="back/css/dictmng.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="back/js/changeColor.js"></script>
<script type="text/javascript" src="back/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="back/js/usertel.js"></script>
</head>
<body>
				<table width="400px" style="margin-top: 30px;" align="center" cellpadding="2" cellspacing="1"  id="table1">
				<tr class="TitleRow">
		        <td>联系电话</td>
		        <td>添加时间</td>
		        <td>状态</td>
		        <td>操作</td>
				</tr>
					<c:if test="${!empty tels }">
						<%int i = 0;%>
						<c:forEach items="${tels }" var="tel">
                       <%if ( ++i % 2 == 0 ){%>
							<tr class="EvenRow" onClick="change()"   onMouseOut="out()">
								<%
}
else
{
%>
							<tr class="OddRow" onClick="change()"   onMouseOut="out()">
								<%
}
%>                              
								<td>${tel.tell}<input value="${tel.id}" type="hidden"></td>
								<td><fmt:formatDate value="${tel.createTime}" type="both"/></td>
								<td id="status_value">
								<c:if test="${tel.status==0}">无效</c:if>
								<c:if test="${tel.status==1}">有效</c:if>
								<c:if test="${tel.status==2}">用户主动删除</c:if>
								</td>
								<td>
								<c:if test="${tel.status!=2}">
								<input  id="${tel.id}" type="button" value="状态变更">
								</c:if>
								</td>
							</tr>
						</c:forEach>
					</c:if>
				</table>
</body>
</html>