<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<base href="<%=basePath%>">
	<title>手机号发货屏蔽控制</title>
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/upgrade.css" type="text/css" rel="stylesheet">

  <script type="text/javascript" src="back/js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
  <script type="text/javascript" src="back/model/js/common_cookie.js"></script>
  
</head>
<style>
	.blue {
		color: #428bca;
		cursor: pointer;
	}
</style>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>货源信息管理 </span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">手机号发货屏蔽控制</span>
		</h1>
		<!-- 搜索条件 -->
		<form action="<%=basePath%>/compete/list" method="post" id="">
		<input type="hidden" id="pageNoId" name="pageNo" value="${pageNo}">
		
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li><label for="">手机号：</label>
				<input type="text" name="cellPhone" maxlength="11" onkeyup="value=value.replace(/[^\d]/g,'')" value="${queryBean.cellPhone }"></li>
				<li><label for="">平台：</label>
				<input type="text" name="platform" maxlength="20" value="${queryBean.platform }"></li>
				<li><label for="">状态：</label>
						<select name="status" id="">
							<option value="">请选择</option>
							<option value="1" <c:if test="${queryBean.status==1 }">selected</c:if>>启用</option>
							<option value="2" <c:if test="${queryBean.status==0 }">selected</c:if>>停用</option>
						</select>
				</li>
			</ul>
		</div>
		</form>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="sbWidth" type="button" value="查询" onclick="queryList()"></li>
					<li><input class="sbWidth" type="button" value="添加" onclick="showAddUpdateTip()"></li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>${rowCount }</em>条信息，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页
            </div>
		</div>
		<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		<div class="dataTable">
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th>编号</th>
					<th>手机号</th>
					<th>平台</th>
					<th>原因</th>
					<th>状态</th>
					<th>操作人</th>
					<th>更新时间</th>
					<th>操作</th>
					
				</tr>
				<c:forEach items="${competeList }" var="list">
				<tr>
					<td>${list.id }</td>
					<td class="blue" onclick="javascript:phoneSoHfn($(this),'${list.id }','手机号发货屏蔽控制','special','cellPhone')">查看电话</td>
					<td>${list.platform }</td>
					<td>${list.reason }</td>
					<td>
						<c:if test="${list.status==1 }">启用</c:if>
						<c:if test="${list.status==2 }">停用</c:if>
					</td>
					<td>${list.operaUserName }</td>
					<td><fmt:formatDate value="${list.mtime }" pattern="yyyy-MM-dd HH:mm:ss" /></td>
					<td>
						<a style="cursor:pointer;" onclick="showUpdateTip('${list.id }','${list.status }')"><c:if test="${list.status==1 }">停用</c:if></a>
						<a style="cursor:pointer;" onclick="showUpdateTip('${list.id }','${list.status }')"><c:if test="${list.status==2 }">启用</c:if></a>
					</td>
				</tr>
				</c:forEach>
		    </table>
		</div>
		<div class="alert alert-danger fade in alert-height" style="display: none;" id="outlook_danger_add">
			<div class="innerLook">
				<h2 id='addTitle'>添加手机号</h2>
				<div class="addWhiteUser cf">
					<ul>
						<li>
							<label for="">
								<i>*</i>手机号：
							</label>
							<input type="text" placeholder="请填写手机号" id="cellPhone" onkeyup="value=value.replace(/[^\d]/g,'')" maxlength="11" style="padding-left: 5px;">
						</li>
						<li>
							<label for="">
								<i>*</i>平台：
							</label>
							<input type="text" placeholder="请填写平台" id="platform" maxlength="20" style="padding-left: 5px;">
						</li>
						<li>
							<label for="">
								添加原因：
							</label>
							<input type="text" placeholder="请填写原因" id="reason" maxlength="50" style="padding-left: 5px;">
						</li>
					</ul>
					<div class="makeSureBtn" style="width:68px;height:32px;margin: 0 auto;">
						<input class="button" type="button" id="saveButton" value="保存">
					</div>				
				</div>				
			</div>
			<i class="closelookBtn">×</i>
		</div>
		
		<div class="alert alert-danger fade in alert-height" style="display: none;" id="outlook_danger_update">
			<div class="innerLook">
				<h2 id='updateTitle'>确认停用此手机号？</h2>
				<div class="addWhiteUser cf">
					<ul>
						<li>
							<label for="">
								修改原因：
							</label>
							<input type="text" placeholder="请填写原因" id="updateReason" maxlength="50" style="padding-left: 5px;">
						</li>
					</ul>
					<div class="makeSureBtn" style="width:68px;height:32px;margin: 0 auto;">
						<input class="button" type="button" id="updateButton" value="确定">
					</div>				
				</div>				
			</div>
			<i class="closelookBtn">×</i>
		</div>
		
			<!--此处引入footer.jsp-->
   			 <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
		</div>
</body>
<script type="text/javascript" src="back/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/js/infoMessage.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>
<script type="text/javascript">
function queryList() {
	 var form=document.forms[0];
	form.submit();
}
$(".closelookBtn").click(function () {
	hideAddTip();
	hideUpdateTip();
});
function showAddUpdateTip(){
	$("#outlook_danger_add").show();
}
function hideAddTip(){
	$("#cellPhone").val("");
	$("#platform").val("");
	$("#reason").val("");
	$("#outlook_danger_add").hide();
}

$("#saveButton").click(function () {
	var cellPhone = $.trim($('#cellPhone').val());
	var platform = $.trim($('#platform').val());
	if (!cellPhone) {
		alert("请填写手机号");
		return;
	}
	if (!platform) {
		alert("请填写平台名称");
		return;
	}
	$.ajax({
        url: "compete/save",
        type: "post",
        async: false,
        data: {cellPhone:cellPhone,platform:platform,reason:$('#reason').val()},
        dataType: "json",
        success: function (data) {
            if (data.code==200) {
            	alert("保存成功");
            	location.reload();
            	hideAddTip();
            } else if(data.code==1003) {
                alert("用户登陆超时，请重新登陆");
            } else if(data.code==201) {
            	alert(data.msg);
            }
        },
        error: function (XHR, textStatus, errorThrown) {
            alert("网络有点慢，请重试！");
        }
    });
});


function hideUpdateTip(){
	$("#updateReason").val("");
	$("#outlook_danger_update").hide();
}
var competeId='';
function showUpdateTip(_id,_status){
	if (_status==1) {
		$("#updateTitle").html("确认停用该手机号？");
	}else if(_status==2){
		$("#updateTitle").html("确认启用该手机号？");
	}
	competeId=_id;
	$("#outlook_danger_update").show();
}
$("#updateButton").click(function () {
	$.ajax({
        url: "compete/updateStatus",
        type: "post",
        async: false,
        data: {id:competeId,reason:$('#updateReason').val()},
        dataType: "json",
        success: function (data) {
            if (data.code==200) {
            	alert("修改成功");
            	location.reload();
            	hideUpdateTip();
            } else if(data.code==1003) {
                alert("用户登陆超时，请重新登陆");
            }
        },
        error: function (XHR, textStatus, errorThrown) {
            alert("网络有点慢，请重试！");
        }
    });
});


</script>
</html>