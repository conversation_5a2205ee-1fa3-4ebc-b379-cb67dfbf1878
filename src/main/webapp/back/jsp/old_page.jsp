<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<script type="text/javascript">
	function query(pageNo) {
		var maxPage=document.getElementById("max_page").value;
		if(0<pageNo<=maxPage){
					var form = document.forms[0];
					form.pageNo.value = pageNo;
					form.submit();
				}	
	}
</script>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>back-page</title>
</head>
<body>
<div style="width:500px; margin:0 auto; margin-top:20px; padding-bottom:60px;">
						<input type="button" value="首页" onclick="query(1)"/>
						<input type="button" value="上一页" onclick="query(${pageNo-1})"/>
						<input type="button" value="下一页" onclick="query(${pageNo+1})"/>
						<input type="button" value="尾页" onclick="query(${maxPage})"/>
						&nbsp;&nbsp;&nbsp;&nbsp;
						第
						<select onchange="query(this.value)">
						<c:forEach items="${pageNoList }" var="page">
						<option value="${page }" <c:if test="${pageNo==page }">selected</c:if>>${page }</option>
						</c:forEach>
						</select>页
</div>
</body>
</html>