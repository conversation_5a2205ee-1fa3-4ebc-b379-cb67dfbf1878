<%@page import="com.tyt.util.AppConfig"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<title>车辆认证录入</title>
<!-- <link href="<%=request.getContextPath()%>/back/css/dictmng.css" rel="stylesheet" type="text/css"> -->
<link media=screen href="<%=request.getContextPath()%>/fore/js/jquery.alerts.css" type=text/css rel=stylesheet>
<link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/detailPages.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/detailPagesAdd.css" type="text/css" rel="stylesheet">

<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>

<script src="<%=request.getContextPath()%>/fore/js/jquery.ui.draggable.js" type=text/javascript></script>
<!-- 对话框核心JS文件和对应的CSS文件-->
<script src="<%=request.getContextPath()%>/fore/js/jquery.alerts.js" type=text/javascript></script>
<!-- 示例代码 -->
<script src="<%=request.getContextPath()%>/back/js/uploadPreview.js" type="text/javascript"></script>
<!-- 表单校验加载 -->
<!-- <link type="text/css" rel="stylesheet" href="<%=request.getContextPath()%>/fore/js/formValidator/style/validator.css" /> -->
<!-- <script src="<%=request.getContextPath()%>/fore/js/formValidator/formValidator.js" type="text/javascript"></script> -->
<!-- <script src="<%=request.getContextPath()%>/fore/js/formValidator/formValidatorRegex.js" type="text/javascript"></script> -->

<title>车辆认证</title>
<style>
	.detailUlimg{
		display: flex;
		width: 100%;
	}
	.detailUlimg li {
		width: 33%;
	}
	.detailUlimg li .headDrive {
		width: 100%;
	}
	.detailUlimg li img {
		width: 90%;
		height: auto;
	}
	.detailUl li{
		width: 31%;
	}
	.detailBox .detailUlcar label {
		width: auto;
	}
	.tipMessage{
		color: #7b7b7b;
		padding: 0 0 10px 15px;
		font-weight: bolder;
	}
</style>
<script type="text/javascript">

	$(function() {
		$("#tailDrivingPic").uploadPreview({
			Img : "ImgPr2",
			Width : 200,
			Height : 200,
			Callback:showTailMsg
		});
		$("#headDrivingPic").uploadPreview({
			Img : "ImgPr",
			Width : 200,
			Height : 200,
			Callback:showHeadMsg
		});

		$("#tailDrivingOtherPic").uploadPreview({
			Img : "ImgPr3",
			Width : 200,
			Height : 200,
			Callback:showTailMsgBack
		});

		//校验代码begin========
		$("#headNo").blur(function(event) {
			headNoCheck($(this))
		});
		$("#tailNo").blur(function(event) {
			tailNoCheck($(this))
		});

		// $("#source").change(function(){
		// 	var opt=$("#source").val();
		// 	if(opt == null||opt == ''){
		// 		errorSpanId('source','errorSpanId','请选择来源',1)
		// 	}else {
		// 		errorSpanId('source','errorSpanId','恭喜，来源选择正确',0)
		// 	}
		// });
		$("#carType").change(function(){
			var opt=$("#carType").val();
			if(opt == null||opt == ''){
				errorSpanId('carType','errorSpanId','挂车型号未选择',1)
			}else {
				errorSpanId('carType','errorSpanId','恭喜，挂车型号选择正确',0)
			}
		});
		var msg=$("#message").val();
		errorSpanId('','errorSpanId',msg,0)
	});

	function headNoCheck(_this){
		if(_this.val() == null||_this.val() == ''){
			errorSpanId('headNo','errorSpanId','车头车牌号码不能为空',1)
		}else if(trim(_this.val()).length < 5){
			errorSpanId('headNo','errorSpanId','车头车牌号码长度不能少于5位',1)
		}else if(trim(_this.val()).length >= 5&&trim(_this.val()).length <= 10) {
			errorSpanId('headNo','errorSpanId','恭喜，车头车牌号码输入正确',0)
		}
	}
	// function sourceCheck(_this){
	// 	if(_this.val() == null||_this.val() == ''){
	// 		errorSpanId('source','errorSpanId','请选择来源',1)
	// 	}
	// }
	function carTypeCheck(_this){
		if(_this.val() == null||_this.val() == ''){
			errorSpanId('carType','errorSpanId','挂车型号未选择',1)
		}
	}
	function tailNoCheck(_this){
		if(_this.val() == null||_this.val() == ''){
			errorSpanId('tailNo','errorSpanId','挂车车牌号码不能为空',1)
		}else if(trim(_this.val()).length < 5){
			errorSpanId('tailNo','errorSpanId','挂车车牌号码长度不能少于5位',1)
		}else if(trim(_this.val()).length >= 5&&trim(_this.val()).length <= 10) {
			errorSpanId('tailNo','errorSpanId','恭喜，挂车车牌号码输入正确',0)
		}
	}
	function errorSpanId(obj,errorbox,tit,stuste){
		$("#"+errorbox).html(tit);
		if(stuste == 1) {
			$("#"+obj).attr('data-stuste','nopush');
			return false;
		}else {
			$("#"+obj).attr('data-stuste','push');
			return true;
		}
	}
	function formValidator(){
		var headNo =  $("#headNo").attr('data-stuste');
		var tailNo =  $("#tailNo").attr('data-stuste');
		var ImgPr =  $("#ImgPr").attr('data-stuste');
		var ImgPr2 =  $("#ImgPr2").attr('data-stuste');
		var ImgPr3 =  $("#ImgPr3").attr('data-stuste');
		// var source = $("#source").attr('data-stuste');
		var carType = $("#carType").attr('data-stuste');
		if(headNo == 'nopush') {
			headNoCheck($("#headNo"))
			return false;
		} else if(tailNo == 'nopush'){
			tailNoCheck($("#tailNo"))
			return false;
		}else if(carType == 'nopush'){
			carTypeCheck($("#carType"))
			return false;
		}
		// else if(source == 'nopush'){
		// 	sourceCheck($("#source"))
		// 	return false;
		// }
		 else if(ImgPr == 'nopush'){
			errorSpanId('ImgPr','errorSpanId','请选择车头行驶本照片',1)
			return false;
		} else if(ImgPr2 == 'nopush'){
			errorSpanId('ImgPr2','errorSpanId','请选择挂车行驶本照片',1)
			return false;
		} else if(ImgPr3 == 'nopush'){
			errorSpanId('ImgPr3','errorSpanId','请选择挂车行驶本反面照片',1)
			return false;
		}else {
// 			errorSpanId('','errorSpanId','车辆录入成功',0)
			setTimeout(function(){
				$("#form1").submit();
				$("#headNo").attr('data-stuste','nopush');
				$("#tailNo").attr('data-stuste','nopush');
				// $("#source").attr('data-stuste','nopush');
				$("#carType").attr('data-stuste','nopush');
				$("#ImgPr").attr('data-stuste','nopush');
				$("#ImgPr2").attr('data-stuste','nopush');
				$("#ImgPr3").attr('data-stuste','nopush');
				return true;
			},2000);
		}

	}

	function showHeadMsg() {
		if (tijiao2('headDrivingPic', 'ImgPr', '车头行驶本照片')) {
			errorSpanId('ImgPr','errorSpanId','车头行驶本照片选择正确',0)
		} 
	}
	function showTailMsg() {
		if (tijiao2('tailDrivingPic', 'ImgPr2', '挂车行驶本照片')) {
			errorSpanId('ImgPr2','errorSpanId','挂车行驶本照片选择正确',0)
		}
	}
    function showTailMsgBack(){
		if (tijiao2('tailDrivingOtherPic', 'ImgPr3', '挂车行驶本反面照片')) {
			errorSpanId('ImgPr3','errorSpanId','挂车行驶本反面照片选择正确',0)
		}
	}

	function trim(str) {
    if (str == null || typeof str == "undefined") {
        return "";
    }
    return str.replace(/(^\s*)|(\s*$)/g, "");
	};

	function tijiao() {
		if (tijiao2('headDrivingPic', 'ImgPr', '车头行驶本照片')) {
			if ("" != $("#tailCity").val() && "" != $("#tailNo").val()
					&& tijiao2('tailDrivingPic', 'ImgPr2', '车头行驶本照片')) {
				$("#errorSpanId").removeClass();
				$("#errorSpanId").html("");
				$("#errorSpanId").removeClass();
				$("#errorSpanId").html("");
				return true;
			} else {
				if ("" == $("#tailCity").val() && "" == $("#tailNo").val()
						&& !tijiao2('tailDrivingPic', 'ImgPr2', '车头行驶本照片')) {
					$("#errorSpanId").removeClass();
					$("#errorSpanId").html("");
					$("#errorSpanId").removeClass();
					$("#errorSpanId").html("");
					return true;
				} else {
					if ("" == $("#tailCity").val()) {
						$("#errorSpanId").removeClass();
						$("#errorSpanId").addClass("onError");
						$("#errorSpanId").html("挂车车牌头字母不能为空");
						$("#errorSpanId").removeClass();
						$("#errorSpanId").html("");
						return false;
					}
					if ("" == $("#tailNo").val()) {
						$("#errorSpanId").removeClass();
						$("#errorSpanId").addClass("onError");
						$("#errorSpanId").html("挂车号码不能为空");
						$("#errorSpanId").removeClass();
						$("#errorSpanId").html("");
						return false;
					} else if ($("#tailNo").val().length < 5
							|| $("#tailNo").val().length > 10) {
						$("#errorSpanId").removeClass();
						$("#errorSpanId").addClass("onError");
						$("#errorSpanId").html("输入长度5-10位,请确认");
						$("#errorSpanId").removeClass();
						$("#errorSpanId").html("");
						return false;
					}

					if (!tijiao2('tailDrivingPic', 'ImgPr2', '车头行驶本照片')) {
						$("#errorSpanId").removeClass();
						$("#errorSpanId").html("");
						return false;
					}
				}
			}

		}
		return false;
	}
	function tijiao2(fileuploade, tempimg, msg) {

		var maxsize = 10 * 1024 * 1024;//2M
		var errMsg = "上传的附件文件不能超过2M！！！";
		var tipMsg = "您的浏览器暂不支持计算上传文件的大小，确保上传文件不要超过2M，建议使用IE、FireFox、Chrome浏览器。";
		var browserCfg = {};
		var ua = window.navigator.userAgent;
		if (ua.indexOf("MSIE") >= 1) {
			browserCfg.ie = true;
		} else if (ua.indexOf("Firefox") >= 1) {
			browserCfg.firefox = true;
		} else if (ua.indexOf("Chrome") >= 1) {
			browserCfg.chrome = true;
		}
		try {
			var obj_file = document.getElementById(fileuploade);
			if (obj_file.value == "") {
				$("#" + fileuploade + "Tip").removeClass();
				$("#" + fileuploade + "Tip").addClass("onError");
				$("#" + fileuploade + "Tip").html("请先选择" + msg + "");
				//jAlert('请先选择上传文件', '图片选择错误');
				return false;
			}
			var filesize = 0;
			if (browserCfg.firefox || browserCfg.chrome) {
				filesize = obj_file.files[0].size;
			} else if (browserCfg.ie) {
				var obj_img = document.getElementById(tempimg);
				obj_img.dynsrc = obj_file.value;
				filesize = obj_img.fileSize;
			} else {
				// alert(tipMsg);
				//jAlert(tipMsg, '图片选择错误');
				return true;
			}
			if (filesize == -1) {
				//alert(tipMsg);
				//jAlert(tipMsg, '图片选择错误');
				return true;
			} else if (filesize > maxsize) {
				// alert(errMsg);
				$("#" + fileuploade + "Tip").removeClass();
				$("#" + fileuploade + "Tip").addClass("onError");
				$("#" + fileuploade + "Tip").html("请先选择" + msg + "'图片选择错误'");
				//jAlert(errMsg, '图片选择错误');
				return false;
			} else {
				//alert("文件大小符合要求");
				//jAlert('文件大小符合要求', '图片选择错误');  
				return true;
			}
		} catch (e) {
			return true;
		}
	}

	//以下脚本暂未使用

	function qingkong(){		
		$('#headNo').val('');
		$('#tailNo').val('');
		$('#headDrivingPic').val('');
		$('#tailDrivingPic').val('');
		$('#ImgPr2').attr('src','<%=request.getContextPath()%>/fore/image/2.jpg');
		$('#ImgPr').attr('src','<%=request.getContextPath()%>/fore/image/2.jpg');
		$("#tailCity").val('');
		$("#headCity").val("");
		// $("#source").val("");
		$("#headDrivingPic").after($("#headDrivingPic").clone().val(""));
		$("#headDrivingPic").remove();
		$("#tailDrivingPic").after($("#tailDrivingPic").clone().val(""));
		$("#tailDrivingPic").remove();
	}
</script>

</head>
<body>
	
	<div class="contentBox">
	    <h1 class="titUrl">
	        <span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span>用户信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">车辆认证录入</span>
	    </h1>
	    <div class="main-content">
			<form action="<%=request.getContextPath()%>/boss/car/save" name="carForm" method="post" enctype="multipart/form-data" id="form1">
				<div class="driveMessage" style="display: none;">${message}</div>
				<input type="hidden" name="userId" value="${userId} " />
				<input type="hidden" id="message" value="${message} " />
				
				<div class="detailBox">
					<h2 class="detailTit">个人信息</h2>
					<ul class="detailUl detailUlcar cf">
						<li>
							<label for="">账户：</label>
							<span>${phone}</span>
						</li>
						<li>
							<label for="">姓名：</label>
							<span>${userName}</span>
						</li>
						<li>
							<label for=""><i style="color: red;">*</i>车头车牌号码：</label>
							<select name="headCity" id="headCity" style="width: 40px;" >
								<%-- <option value="">选择</option> --%>
								<option value="京">京</option>
								<option value="津">津</option>
								<option value="冀">冀</option>
								<option value="晋">晋</option>
								<option value="蒙">蒙</option>
								<option value="辽">辽</option>
								<option value="吉">吉</option>
								<option value="黑">黑</option>
								<option value="沪">沪</option>
								<option value="苏">苏</option>
								<option value="浙">浙</option>
								<option value="皖">皖</option>
								<option value="闽">闽</option>
								<option value="赣">赣</option>
								<option value="鲁">鲁</option>
								<option value="豫">豫</option>
								<option value="湘">湘</option>
								<option value="鄂">鄂</option>
								<option value="粤">粤</option>
								<option value="琼">琼</option>
								<option value="桂">桂</option>
								<option value="甘">甘</option>
								<option value="陕">陕</option>
								<option value="新">新</option>
								<option value="青">青</option>
								<option value="宁">宁</option>
								<option value="渝">渝</option>
								<option value="川">川</option>
								<option value="贵">贵</option>
								<option value="云">云</option>
								<option value="藏">藏</option>
								<option value="台">台</option>
								<option value="澳">澳</option>
								<option value="港">港</option>
							</select><input style="width: 65px;" type="text" id="headNo" name="headNo" data-stuste='nopush' value="${car.headNo}" maxlength="10" />
						</li>
						<li>
							<label for=""><i style="color: red;">*</i>挂车车牌号码：</label>
							<select name="tailCity" id="tailCity" style="width: 40px;">
								<%-- <option value="">选择</option> --%>
								<option value="京">京</option>
								<option value="津">津</option>
								<option value="冀">冀</option>
								<option value="晋">晋</option>
								<option value="蒙">蒙</option>
								<option value="辽">辽</option>
								<option value="吉">吉</option>
								<option value="黑">黑</option>
								<option value="沪">沪</option>
								<option value="苏">苏</option>
								<option value="浙">浙</option>
								<option value="皖">皖</option>
								<option value="闽">闽</option>
								<option value="赣">赣</option>
								<option value="鲁">鲁</option>
								<option value="豫">豫</option>
								<option value="湘">湘</option>
								<option value="鄂">鄂</option>
								<option value="粤">粤</option>
								<option value="琼">琼</option>
								<option value="桂">桂</option>
								<option value="甘">甘</option>
								<option value="陕">陕</option>
								<option value="新">新</option>
								<option value="青">青</option>
								<option value="宁">宁</option>
								<option value="渝">渝</option>
								<option value="川">川</option>
								<option value="贵">贵</option>
								<option value="云">云</option>
								<option value="藏">藏</option>
								<option value="台">台</option>
								<option value="澳">澳</option>
								<option value="港">港</option>
							</select><input style="width: 65px;" type="text" name="tailNo" id="tailNo" data-stuste='nopush' value="${car.tailNo}" maxlength="10" />
						</li>
						<!--<li>
							<label for=""><i style="color: red;">*</i>来源：</label>
							<select name="source" id="source" style="width: 100px;" data-stuste='nopush'>
								<option value="">请选择</option>
								<option value="0">市场</option>
								<option value="1">客服</option>
							</select>
						</li>-->
						<li>
							<label data-value="${carTypeList}" for=""><i style="color: red;">*</i>挂车型号：</label>
							<select name="carType" id="carType" data-stuste='nopush'>
								<option value="">请选择</option>
								<c:forEach items="${carTypeList}" var="t">
									<option value="${t.value }" <c:if test="${t.value==car.carType }">selected</c:if>>${t.name }</option>
								</c:forEach>
							</select>
						</li>
					</ul>
					<div class="tipMessage">注：单机版车型 挂车车牌号、行驶本可与车头信息填写一致即可</div>
				</div>
				
				<div class="detailBox">
					<h2 class="detailTit">车辆行驶本</h2>
					<ul class="detailUlimg cf">
						<li>
							<h3><i color="red">*</i><span>车头行驶本</span></h3>
							<div class="headDrive">
								<img id="ImgPr" width="200" height="200" src="<%=request.getContextPath()%>/fore/image/2.jpg" data-stuste="nopush" />
							</div>
							<div class="fileBtn">
								<span>选择文件</span>
								<input type="file" name="headDrivingPic" id="headDrivingPic" value="" />
							</div>
							
						</li>
						<li>
							<h3><i color="red">*</i><span>挂车行驶本</span></h3>
							<div class="headDrive">
								<img id="ImgPr2" width="200" height="200" src="<%=request.getContextPath()%>/fore/image/2.jpg" data-stuste="nopush"/>
							</div>
							<div class="fileBtn">
								<span>选择文件</span>
								<input type="file" id="tailDrivingPic" name="tailDrivingPic" value="" />
							</div>
						</li>
						<li>
							<h3><i style="color: red;">*</i><span>挂车行驶本反面</span></h3>
							<div class="headDrive">
								<img id="ImgPr3" width="200" height="200" src="<%=request.getContextPath()%>/fore/image/2.jpg" data-stuste="nopush"/>
							</div>
							<div class="fileBtn">
								<span>选择文件</span>
								<input type="file" id="tailDrivingOtherPic" name="tailDrivingOtherPic" value="" />
							</div>
						</li>
					</ul>
				</div>

				
				<div class="carbtnBox">
					<span class="errorCar" id="errorSpanId"></span>
					<div class="innercarBtn">
						<input type="button" id="FormValidInput" onclick="formValidator()" class="btnTrun"  value="提交" />
							<%-- <input class="btnTrun"  type="reset" value="清空" onclick="qingkong()" /> --%>
						<a href="<%=request.getContextPath()%>/boss/car/list?userId=${userId}" target="_blank">查看车辆列表</a>
					</div>
					
				</div>
			</form>
		</div>
	</div>

</body>
</html>