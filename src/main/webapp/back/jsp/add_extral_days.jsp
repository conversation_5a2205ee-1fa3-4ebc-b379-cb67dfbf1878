<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!doctype html>
<html>
<head>
<base href="<%=basePath%>">
<meta charset="utf-8">
<title>缴费页面</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/detailPages.css" type="text/css" rel="stylesheet">
<link href="back/model/css/detailPagesAdd.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="back/jqueryvalidate/jquery.js" ></script>
<script type="text/javascript" src="back/jqueryvalidate/jquery.validate.js" ></script>
<script type="text/javascript" src="back/jqueryvalidate/messages_zh.js" ></script>
<script type="text/javascript" src="back/jqueryvalidate/additional.js" ></script>
<script type="text/javascript" src="back/jqueryvalidate/common.js" ></script>

<!-- <script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
 -->
<script type="text/javascript" src="back/model/js/bootstrap.js"></script>
<script src="back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript">
function showType(value){
	if(value==2){
		$("#bujiao").show();
		
	}else{
		$("#bujiao").hide();
	}
	$("#error").html("");
}
</script>
</head>

<body>

    <div class="contentBox">

    <h1 class="titUrl">
                <span>当前所在位置：</span><span>财务管理</span><span>&nbsp;&gt;&nbsp;</span><span>会员缴费管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">变更天数</span>
            </h1>
        <div class="main-content">
           <!--系统设置  -->
           <form id="form" name="myform" action="account/user/add/extralDays" method="post">
           <input name="userId" type="hidden" class="input126" value="${user.id}">

           <div class="systemParameter">
               <ul class="cf">
                 <li><label>电话：</label><input name="cellPhone" readonly type="text" class="input180" value="${user.cellPhone}"></li>
                 <li><label>剩余天数：</label><input readonly value="${user.serveDays}" name="serveDays" type="text" class="input180">
                 <li><label>赠送天数：</label><input id="extralDays" placeholder="最大10000" value="${extralDays}" name="extralDays" type="text" class="input180">
                 <li><label>变更类型：</label>
	                 <select id="changeType"name="changeType"class="input180">
	                 	<option>请选择</option>
	                 	<option value="1">平台缴费变更</option>
	                 	<option value="2">后台缴费变更</option>
	                 	<option value="3">异常扣减变更</option>
	                 	<option value="4">vip延期变更</option>
	                 	<option value="5">活动加赠变更</option>
	                 	<option value="6">其他加赠变更</option>
	                 </select>
                 <li><label>加时备注：</label><textarea style="width: 180px;height: 80px;border: 1px solid #ccc;" name="note" id="note" class="input500" maxlength="50"></textarea></li>
<%--                  <input id="note"  value="${user.homePhone}" placeholder="最多50个字符" name="note" type="text" class="input500"> --%>
              </ul>
              <div id="error" class="errorSpan" style="color:red;"><c:if test="${!empty msg}"><span>${msg}</span></c:if></div>
              <div class="divButton">
               <input type="button" class="button" value="提交" onClick="checkForm(this.form)">
               <input type="reset" class="button" value="重置"></div>
           </div>
           </form>
         
<!--<div class="footer">
<ul class="pagination">
  <li><a href="#">&laquo;</a></li>
  <li><a href="#">1</a></li>
  <li><a href="#">2</a></li>
  <li class="active"><a href="#">3</a></li>
  <li><a href="#">4</a></li>
  <li><a href="#">5</a></li>
  <li><a href="#">&raquo;</a></li>
</ul>
</div>-->

</div>
</div>
</body>

<script type="text/javascript">
$(function(){
  var msg="${msg}";
  if(msg.indexOf("成功")>0){
    window.opener=null; 
    window.open('','_self'); 
    window.close(); 
  };

});

function checkForm(form){
  if(form.changeType.value=='请选择') {
    var error = document.getElementById("error");
    error.innerHTML = '请选择变更类型';
    return false;
  }
  document.myform.submit();
}

</script>
</html>
