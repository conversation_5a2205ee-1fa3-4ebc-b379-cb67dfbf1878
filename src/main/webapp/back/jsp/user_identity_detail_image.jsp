<%@page import="com.tyt.util.AppConfig"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>实名认证</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/jquery.rotate.js"></script>
<script type="text/javascript">
function rotateImg(){
	var rotateValue=parseInt($("#rotateValue").val())+45;
	$("#rotateValue").val(rotateValue);
	$('#displayImage').css('rotate',rotateValue);
}
</script>
</head>
<body class="theme-blue">
  <img id="displayImage" alt="" src="<%=request.getParameter("image_url")%>">
  <input type="hidden" id="rotateValue" value="0">
  <br>
  <img onclick="rotateImg();" alt="" width="30px" height="30px" src="<%=request.getContextPath()%>/back/image/rotate.png">
</body>
</html>
