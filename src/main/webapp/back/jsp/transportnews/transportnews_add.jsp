<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>添加资讯</title>
<link href="<%=request.getContextPath()%>/back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/infoTake.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/My97DatePicker/WdatePicker.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>车辆方服务</span><span>&nbsp;&gt;&nbsp;</span><span>货物资讯管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">添加</span>
		</h1>
    <div class="main-content">
      <!--系统设置-->
      <div class="systemParameter">
      	<form action="saveTransportNew" name="saveTransportNewForm" method="post" enctype="multipart/form-data">
        <ul class="transportNews cf">
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>资讯标题：</label>
	          <input id="transportTit" class="info_input" type="text" maxlength="40" name="newsTitle" placeholder="不超过40个字符">
            <span class="lineError"></span>
	        </li>
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>原文链接：</label>
	          <input id="transportUrl" class="info_input" name="openUrl" type="text" placeholder="" maxlength="500">
            <span class="lineError"></span>
	        </li>
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>来源：</label>
	          <input id="transportCome" class="info_input" name="source" type="text" maxlength="16" placeholder="不超过16个字符">
            <span class="lineError"></span>
	        </li>
					<li>
						<label for=""><i class="xing">*</i>发布日期：</label>
						<input id="beginTime" class="info_input" name="publishTimeStr" readonly onclick="WdatePicker({
							isShowClear:false,
							readOnly:true,
							dateFmt:'yyyy-MM-dd HH:mm:ss',
							maxDate:'%y-%M-%d'
							})" type="text">
            <span class="lineError"></span>
					</li>
          <li class="cf">
            <label for=""><i class="xing">*</i>封面图文件：</label>
            <div class="photoWare nomarginL fl">
              <h4><span>预览</span></h4>
              <div class="imgBox"><img class="" src="<%=request.getContextPath()%>/back/model/images/default.jpg" alt="封面图文件"></div>
              <span>文件名</span>
              <div class="uploadImgbox">
              <input id="fileField" class="fileBtn" name="coverPicturePic" accept="image/bmp,image/jpeg,image/png" type="file" onchange="fileInfo(this,$(this))" data-redtit="2">
                <button class="rule_true" type="button">浏览</button>
              </div>
            </div>
            <span class="lineError"></span>
          </li>
          <li>
          	<label></label>
        		<span>图片尺寸要求： 240*164px，文件大小不超过2M<br/>文件格式：JPG、PNG、BMP</span>
            <span class="lineError"></span>
          </li>
        </ul>
        <p class="errorBlock errorBlockNomal" id=""></p>
        <div class="divButton gengButton">
        	<input type="button" onclick="submitForm()" class="button updateBtn" value="保存">
        </div>
        </form>
    </div>
  </div>
	</div>
</body>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/checkType.js"></script>
<script>
	function submitForm() {
		if (validate()) {
			document.forms['saveTransportNewForm'].submit();
		}
	}

</script>
</html>