<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%><%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<base href="<%=basePath%>">
	<title>保险-订单管理</title>
	 <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/insuranceList.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="back/model/js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
  <script type="text/javascript" src="back/js/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script>
</head>
<style>
	.blue {
		color: #428bca;
		cursor: pointer;
	}
</style>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>保险业务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">保险-订单管理</span>
		</h1>
		<form action="ts/insurance/getList" method="post" id="form1">
		<input type="hidden" name="menuId" value="${curMenu.id }">
		<input type="hidden" id="pageNo" value="${pageNo}" name="pageNo">
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li>
					<label for="">客户手机：</label>
					<input type="text" name="costumerPhone" maxlength="11" value="${queryBean.costumerPhone }">
				</li>
				<li>
					<label for="">ID：</label>
					<input type="text" name="id" maxlength="10" value="${queryBean.id }">
				</li>
				<li>
					<label for="">保险单号：</label>
					<input type="text" name="number" maxlength="20" value="${queryBean.number }">
				</li>
				<li>
					<label for="">保单状态：</label>
					<select name="status" id="">
						<option value="">请选择</option>
						<option value="0" <c:if test="${queryBean.status==0 }">selected</c:if>>待支付</option>
						<option value="1" <c:if test="${queryBean.status==1 }">selected</c:if>>生效中</option>
						<option value="2" <c:if test="${queryBean.status==2 }">selected</c:if>>已退保</option>
						<option value="3" <c:if test="${queryBean.status==3 }">selected</c:if>>已失效</option>
						<option value="4" <c:if test="${queryBean.status==4 }">selected</c:if>>待生效</option>
					</select>
				</li>
				<li>
					<label for="">保单提交时间：</label>
					<input id="ctimeStart" onclick="WdatePicker({readOnly:true,dateFmt:'yyyy-MM-dd', maxDate:'#F{$dp.$D(\'ctimeEnd\')}'})" class="pinfoTable" type="text" name="ctimeStart" value="${queryBean.ctimeStart }"><span>-</span><input id="ctimeEnd" onclick="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd', minDate:'#F{$dp.$D(\'ctimeStart\')}', startDate:'#F{$dp.$D(\'ctimeStart\')}' })" class="pinfoTable" type="text" name="ctimeEnd" value="${queryBean.ctimeEnd }">
				</li>
				<li>
					<label for="">保险公司：</label>
					<select name="type">
						<option value="">请选择</option>
						<option value="1" <c:if test="${queryBean.type==1 }">selected</c:if>>华泰财产保险有限公司</option>
						<option value="2" <c:if test="${queryBean.type==2 }">selected</c:if>>中国人民财产保险股份有限公司</option>
						<option value="3" <c:if test="${queryBean.type==3 }">selected</c:if>>平安单程货运险</option>
					</select>
				</li>
			</ul>
		</div>
		</form>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="sbWidth" type="button" value="查询" onclick="queryList();"></li>
					<li><input class="sbWidth" type="button" value="重置" onclick="javascript:$('#form1')[0].reset()"></li>
                    <li><input class="sbWidth" type="button" value="导出" onclick="exportList();"></li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>${rowCount }</em>条信息，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页
            </div>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
					<tr class="tableTh">
						<th width="70">操作</th>
						<th width="80">ID</th>
						<th>保险单号</th>
						<th width="90">注册手机号</th>
						<th width="90">投保人姓名</th>
						<th width="90">投保人电话号</th>
						<th>保单提交时间</th>
						<th>车头牌照号</th>
						<th width="100">被保人姓名</th>
						<th width="90">被保人电话号</th>
						<th>保险类型</th>
						<th width="90">剩余有效期(天)</th>
						<th width="100">保单状态</th>
					</tr>
					<c:forEach items="${list }" var="bean">
					<tr>
						<td class="positionTd">
							<a></a>
							<div class="positionDiv">
									<i class="tbtopcur"></i>
									<c:if test="${! empty subMenus}">
									<ul class="editorBox">
									<c:forEach items="${subMenus }" var="menu">
										<c:if test="${menu.type==2}">
											<c:if test="${(bean.status==0 || bean.status==1 || bean.status==2 || bean.status==3 || bean.status==4) && (menu.jurisdictionName=='查看详情' || menu.jurisdictionName=='沟通记录')}">
												<li>
													<a onclick="openWinAuto('${menu.url}'+${bean.id },'${menu.jurisdictionName }',1200,600)">${menu.jurisdictionName }</a>
												</li>
											</c:if>
											<c:if test="${bean.status!=0 && (menu.jurisdictionName=='下载保单')}">
												<li>
												<c:if test="${not empty bean.pdfUrl && bean.type!=3}">
													<a target="_blank" href="${bean.pdfUrl }" >${menu.jurisdictionName }</a>
												</c:if>
                                                    <c:if test="${not empty bean.imgUrl && bean.type==3}">
                                                        <a target="_blank" href="${bean.imgUrl }" >${menu.jurisdictionName }</a>
                                                    </c:if>
												<c:if test="${empty bean.pdfUrl && bean.type!=3 }">
													<a href="javascript:void(noPdfUrl())">${menu.jurisdictionName }</a>
												</c:if>
                                                    <c:if test="${empty bean.imgUrl && bean.type==3 }">
                                                        <a href="javascript:void(noPdfUrl())">${menu.jurisdictionName }</a>
                                                    </c:if>
												</li>
											</c:if>
											<c:if test="${bean.status==4 && menu.jurisdictionName=='退&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;保'}">
												<li>
													<a class="outInsurance" data-id='${bean.id }'>${menu.jurisdictionName }</a>
												</li>
											</c:if>
										</c:if>
									</c:forEach>
									</ul>
									</c:if>
									
								</div>
						</td>
						<td>${bean.id }</td>
						<td>${bean.number }</td>
						<td class="blue" onclick="javascript:phoneSoHfn($(this),'${bean.id }','在线订单管理','special','cellPhone')">查看电话</td>
						<td>${bean.applicantName }</td>
						<td class="blue" onclick="javascript:phoneSoHfn($(this),'${bean.id }','在线订单管理','special','applicantPhone')">查看电话</td>
						<td><fmt:formatDate value="${bean.ctime }" pattern="yyyy-MM-dd HH:mm:ss" /></td>
						<td>${bean.headNo }</td>
						<td>${bean.insuredName }</td>
						<td class="blue" onclick="javascript:phoneSoHfn($(this),'${bean.id }','在线订单管理','special','insuredPhone')">查看电话></td>
						<td>
							<c:if test="${bean.type==1 }">华泰单程货运险</c:if>
							<c:if test="${bean.type==3 }">平安单程货运险</c:if>
						</td>
						<td>
							<c:if test="${bean.status==0 }"></c:if>
							<c:if test="${bean.status==1 }">${bean.restDay }</c:if>
							<c:if test="${bean.status==2 }">0</c:if>
							<c:if test="${bean.status==3 }">0</c:if>
							<c:if test="${bean.status==4 }">30</c:if>
						</td>
						<td>
							<c:if test="${bean.status==0 }">待支付</c:if>
							<c:if test="${bean.status==1 }">生效中</c:if>
							<c:if test="${bean.status==2 }">已退保</c:if>
							<c:if test="${bean.status==3 }">已失效</c:if>
							<c:if test="${bean.status==4 }">待生效</c:if>
						</td>
					</tr>
					</c:forEach>
		    </table>
		</div>
		<!--此处引入footer.jsp-->
        <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
				
	</div>

	<div class="outBgColor" style="display: none;"></div>
	<div class="innerTitBox insuranceOut" style="display: none;">
		<i class="closeOutBox"></i>
		<div class="titInnerBox">
			<h3 class="makedTit">确定退保吗？</h3>
			<p class="makedDetil">提示：进行此操作前请与保险公司及用户分别确认已经线下退保完成，该操作不会影响保险公司的真实保单状态</p>
			<div class="outInp cf">
				<label class="fl" for="">退款金额（元）：</label>
				<input class="yuan fl" type="text" id="" maxlength="7" placeholder="元" onkeyup="value=value.replace(/[^\d]/g,'')" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))">
				<span class="fl">•</span>
				<input class="jiaofen fl" type="text" id="retreat" maxlength="2" placeholder="角分" onkeyup="value=value.replace(/[^\d]/g,'')" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))">
			</div>
			<em class="errorLineTit"></em>
			<div class="makedBtn">
				<button class="trueBtn" onclick="">确定</button>
				<button class="falseBtn">取消</button>
			</div>
		</div>
	</div>

</body>
<script type="text/javascript" src="back/model/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/model/js/infoMessage.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>
<script>
	//如果新建属于本页的js , 以下可以剪切过去; 以下js别剪切到infoMessage.js中 
	$(document).on('click','.outInsurance',function(event){
	var _id = $(this).attr('data-id');
	$(".insuranceOut .trueBtn").attr('data-id',_id);
		$(".errorLineTit").html('');
		$(".outBgColor,.insuranceOut").show();
	})
	$(".insuranceOut .closeOutBox,.insuranceOut .falseBtn,.outBgColor").click(function(event) {
		$(".outBgColor,.insuranceOut").hide();
		$(".outInp input").val('');
	});

	$(".outInp .jiaofen,.outInp .yuan").focus(function(event) {
		$(".errorLineTit").html('');
	});
	$(".outInp .jiaofen,.outInp .yuan").blur(function(event) {
		var reg = /^[0-9]*$/;
	  if (!reg.test($(this).val())) {
			$(".errorLineTit").html('');
			$(this).val('')
	  	return false;
	  }
	})
	$(".outInp .jiaofen,.outInp .yuan").keydown(function(event) {
	  if (event.keyCode == 32) {
	  	return false;
	  }
	});


	$(".insuranceOut .trueBtn").click(function(event) {
		var endVal,
			yaun = $(".outInp .yuan").val(),
			jiaofen = $(".outInp .jiaofen").val();

		if(yaun == '' && jiaofen==''){
			$(".errorLineTit").html('退款金额不能为空');
			return false;
		}else	if((yaun==0||yaun=='')&&jiaofen==0){
			$(".errorLineTit").html('请输入大于0的数字');
			return false;
		}else if(yaun!='' && jiaofen==''){//有元无分
			endVal = yaun+'.00';
		}else if(yaun=='' && jiaofen!=''){//无元有分
			if (jiaofen.length==1) {
				endVal = '0.'+jiaofen+'0';
			}else{
				endVal = '0.'+jiaofen;
			}
		}else if(yaun!='' && jiaofen!=''){//有元有分

			if (jiaofen.length==1) {
				endVal = yaun+'.'+jiaofen+'0';
			}else{
				endVal = yaun+'.'+jiaofen;
			}
		}
		// alert(endVal)
		var _id = $(".insuranceOut .trueBtn").attr('data-id');
		updateStatus(_id,endVal);

	});
	function queryList(){
		var form=document.forms[0];
		$("#pageNo").val(1);
		form.submit();
	}
	function updateStatus(id,retreat) {
		$.ajax({
	        url: "ts/insurance/updateRetreatStatus",
	        type: "post",
	        async: false,
	        data: {
	        	insuranceId:id,
	        	retreatCurrency:retreat
	        	},
	        dataType: "json",
	        success: function (data) {
	            if (data.code==200) {
	            	alert("退保成功");
	            	location.reload();
	            	$(".outBgColor,.insuranceOut").hide();
	            } else if(data.code==1003) {
	                alert("用户登陆超时，请重新登陆");
	            } else{
	            	alert(data.msg);
	            }
	        },
	        error: function (XHR, textStatus, errorThrown) {
	            alert("网络有点慢，请重试！");
	        }
	    });
	}
	function noPdfUrl(){
		alert("当前保单暂不支持下载");
	}
    //导出
    function exportList(){
        //判断验证导出区间不能超过３５天
        var startTime = $("#ctimeStart").val();
        var endTime = $("#ctimeEnd").val();
        if(startTime== "" || endTime== ""){
            alert("导出功能仅能导出保单提交时间35天内的数据，请选择您要导出的数据的具体时间");
            return false;
        }else{
            start=new Date(Date.parse(startTime.replace(/-/g,"/")));
            end=new Date(Date.parse(endTime.replace(/-/g,"/")));
            var days2 = Math.floor(Math.abs(start.getTime()-end.getTime())/(24*3600*1000));
            console.info(days2);
            if (days2>35) {
                alert("导出功能仅能导出保单提交时间35天内的数据，请选择您要导出的数据的具体时间");
                return false;
            }
        }
        var checkResult = validExportCookie("insurance_list_export");
        if(checkResult == undefined ||checkResult == "" || checkResult == "-1"){
            alert("请求异常，请稍后重试！");
            return ;
        }
        if(checkResult 	!= "0"){
            alert(checkResult);
            return ;
        }

        var form=document.forms[0];
        form.action="<%=request.getContextPath()%>/ts/insurance/excelExport";
        form.submit();
        form.action="<%=request.getContextPath()%>/ts/insurance/getList";
    }
</script>
</html>
