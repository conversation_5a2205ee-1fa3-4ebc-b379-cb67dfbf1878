<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="com.tyt.model.User"%>
<%@ page import="com.tyt.util.Constant"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>用户列表-管理后台</title>
<link href="<%=request.getContextPath()%>/back/css/dictmng.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/changeColor.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/jquery-1.7.1.js"></script>
<script src="${pageContext.request.contextPath}/back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script>
<script type="text/javascript">
$(document).ready(function(){
	 $("#userType").change(function(){
		 var type=this.value;
		 if(type!=1){
		 	 $('#payStatus option:first').attr('selected','selected');}
	 });
});
</script>
<script type="text/javascript">
function excelUserExport() {
	var checkResult = validExportCookie("user_list_online");
	
	if(checkResult == undefined ||checkResult == "" || checkResult == "-1"){
		alert("请求异常，请稍后重试！");
		return ;
	}
	
	if(checkResult != "0"){
		alert(checkResult);
		return ;
	}
	var form=document.forms[0];
	form.action="<%=request.getContextPath()%>/admin/excelExportUser";
	form.submit();
}
function find(){
	var form=document.forms[0];
	form.action="<%=request.getContextPath()%>/admin/userlist";
	form.submit();
}
</script>
</head>
<body>
	<table width="1250" border="0" cellpadding="0" cellspacing="0">
		<tr bgcolor="#BFBFBF">
			<td>
				<form name="userForm"
					action="<%=request.getContextPath()%>/admin/userlist" method="post">
					<input type="hidden" name="pageNo"
						value="<c:out value="${pageNo}"/>">
					<table width="100%" border="0" cellpadding="2" cellspacing="1">
					<c:if test="${userSign==5||userSign==6||userSign==9}">
					<tr class="SolidRow">
					<td>用户权限身份：<%-- <select
								name="userSign">
									<option value="">----</option>
									<option value="<%=User.USER_SIGN_CAR_OWNER %>"
										<c:if test="${user.userSign == 1}">selected</c:if>>车主</option>
									<option value="<%=User.USER_SIGN_EXCHANGE_STATION %>"
										<c:if test="${user.userSign == 2}">selected</c:if>>配货站</option>
									<option value="<%=User.USER_SIGN_GOODS_OWNER %>"
										<c:if test="${user.userSign == 3}">selected</c:if>>货主</option>
									<option value="<%=User.USER_SIGN_DRIVER %>"
										<c:if test="${user.userSign == 7}">selected</c:if>>司机</option>
									<option value="<%=User.USER_SIGN_SALES %>"
										<c:if test="${user.userSign == 4}">selected</c:if>>销售
									</option>
									<option value="<%=User.USER_SIGN_SALES_MANAGER %>"
										<c:if test="${user.userSign == 8}">selected</c:if>>销售主管
									</option>
									<option value="<%=User.USER_SIGN_DATA %>"
										<c:if test="${user.userSign == 9}">selected</c:if>>数据部
									</option>
									<option value="<%=User.USER_SIGN_WEB %>"
										<c:if test="${user.userSign == 10}">selected</c:if>>网站部
									</option>
									<c:if test="${userSign==5}">
										<option value="<%=User.USER_SIGN_ADMIN %>"
											<c:if test="${user.userSign == 5}">selected</c:if>>超级管理员
										</option>
										<option value="<%=User.USER_SIGN_ADMIN_GENERAL %>"
											<c:if test="${user.userSign == 6}">selected</c:if>>普通管理员
										</option>
									</c:if>
									<c:if test="${userSign==6}">
										<option value="<%=User.USER_SIGN_ADMIN_GENERAL %>"
											<c:if test="${user.userSign == 6}">selected</c:if>>普通管理员
										</option>
									</c:if>
                                    <option value="<%=User.USER_SIGN_CLEAN %>"
										<c:if test="${user.userSign == 11}">selected</c:if>>信息清理员
									</option>
							</select>  --%>
							<select name="userSign">
									<option value="">----</option>
									<c:forEach items="${rightSignList}" var="list">
									<option value="${list.value}"
										<c:if test="${user.userSign == list.value}">selected</c:if>>
										${list.name}
										</option>
									</c:forEach>
							</select> 
							</td>
							<td>销售审核身份：
							<select name="deliverType">
							<option value="-1">----</option>
							<c:forEach items="${checkSignList}" var="list">
							<option <c:if test="${user.deliverType==list.value}">selected</c:if> value="${list.value }">${list.name}</option>
							</c:forEach>
							</select>
							</td>
					<td>信息上传： <select name="infoUploadFlag">
									<option value="">--</option>
									<option value="<%=User.INFO_UPLOAD_RESERVED%>"
										<c:if test="${user.infoUploadFlag == 0}">selected</c:if>>待定</option>
									<option value="<%=User.INFO_UPLOAD_DISABLE%>"
										<c:if test="${user.infoUploadFlag == 1}">selected</c:if>>禁止</option>
									<option value="<%=User.INFO_UPLOAD_ENABLE%>"
										<c:if test="${user.infoUploadFlag == 2}">selected</c:if>>
										可用</option></select></td>
							 <td>信息发布： <select name="infoPublishFlag">
									<option value="">----</option>
									<option value="<%=User.INFO_PUBLISH_DISABLE%>"
										<c:if test="${user.infoPublishFlag == 1}">selected</c:if>>禁止</option>
									<option value="<%=User.INFO_PUBLISH_ENABLE%>"
										<c:if test="${user.infoPublishFlag == 2}">selected</c:if>>可用</option>
										
							</select>
							</td>
							<td>身份认证： <select name="verifyFlag">
									<option value="">----</option>
									<option value="<%=User.VERIFY_DISABLE%>"
										<c:if test="${user.verifyFlag == 0}">selected</c:if>>未认证</option>
									<option value="<%=User.VERIFY_ENABLE%>"
										<c:if test="${user.verifyFlag == 1}">selected</c:if>>通过</option>
									<option value="<%=User.VERIFY_WAIT%>"
										<c:if test="${user.verifyFlag == 2}">selected</c:if>>认证中</option></select>
						   </td>
					<td>
							归属地：<input type="text" name="province" size="5"
								value="${user.province}" />省<input
								type="text" name="city" size="5"
								value="${user.city}"/>市
								<%-- <input type="text" name="county" size="5"
								value="${user.county}" />县 --%>
							</td>
							
					</tr>
					<tr class="SolidRow">
					<td>
						 手机开通：<select name="phoneOpenFlag">
									<option value="">----</option>
									<option value="<%=User.PHONE_OPEN_DISABLE%>"
										<c:if test="${user.phoneOpenFlag== 0}">selected</c:if>>否</option>
									<option value="<%=User.PHONE_OPEN_ENABLE%>"
										<c:if test="${user.phoneOpenFlag == 1}">selected</c:if>>是</option>
							</select> </td>
							<td>消息盒子： <select name="qqBoxFlag">
									<option value="">----</option>
									<option value="<%=User.QQ_BOX_FLAG_CLOSE%>"
										<c:if test="${user.qqBoxFlag== 0}">selected</c:if>>关闭</option>
									<option value="<%=User.QQ_BOX_FLAG_OPEN%>"
										<c:if test="${user.qqBoxFlag == 1}">selected</c:if>>打开</option>
							</select>
							</td>
					<td>
							开通年限：<select name="renewalYears">
								    <option value="">----</option>
									<option value="<%=User.RENEWAL_YEARS_ONE %>"
										<c:if test="${user.renewalYears ==1}">selected</c:if>>1</option>
									<option value="<%=User.RENEWAL_YEARS_TWO %>"
										<c:if test="${user.renewalYears == 2}">selected</c:if>>2</option>
									<option value="<%=User.RENEWAL_YEARS_THREE %>"
										<c:if test="${user.renewalYears == 3}">selected</c:if>>3</option>
							</select></td><td>
							用户状态： <select name="userType" id="userType">
									<option value="">----</option>
									<option value="<%=User.USER_TYPE_TRIAL%>"
										<c:if test="${user.userType == 0}">selected</c:if>>试用</option>
									<option value="<%=User.USER_TYPE_VIP%>"
										<c:if test="${user.userType == 1}">selected</c:if>>付费</option>
									<option value="<%=User.USER_TYPE_TRIAL_NOT_VERIFY%>"
										<c:if test="${user.userType == 2}">selected</c:if>>
										未激活</option>
							</select>
							</td>
							<td>终端来源： <select name="platId">
									<option value="">----</option>
									<option value="<%=Constant.PLAT_PC%>"
										<c:if test="${user.platId == 1}">selected</c:if>>PC</option>
									<option value="<%=Constant.PLAT_ANDROID%>"
										<c:if test="${user.platId == 2}">selected</c:if>>Android</option>
									<option value="<%=Constant.PLAT_IOS%>"
										<c:if test="${user.platId == 3}">selected</c:if>>IOS</option>
									<option value="<%=Constant.PLAT_APAD%>"
										<c:if test="${user.platId == 4}">selected</c:if>>APAD</option>
									<option value="<%=Constant.PLAT_IPAD%>"
										<c:if test="${user.platId == 5}">selected</c:if>>IPAD</option>
									<option value="<%=Constant.PLAT_WEB%>"
										<c:if test="${user.platId == 6}">selected</c:if>>WEB</option>
									<option value="<%=Constant.PLAT_ANDROID_CAR%>"
											<c:if test="${user.platId == 21}">selected</c:if>>Android-车
									</option>
									<option value="<%=Constant.PLAT_ANDROID_GOODS%>"
											<c:if test="${user.platId == 22}">selected</c:if>>Android-货
									</option>
									<option value="<%=Constant.PLAT_IOS_CAR%>"
											<c:if test="${user.platId == 31}">selected</c:if>>IOS-车
									</option>
									<option value="<%=Constant.PLAT_IOS_GOODS%>"
											<c:if test="${user.platId == 32}">selected</c:if>>IOS-货
									</option>
									<option value="<%=Constant.PLAT_WEB_GOODS%>"
											<c:if test="${user.platId == 62}">selected</c:if>>Web-货
									</option>
							</select></td>
							<td>付费状态：
								<select id="payStatus" name="payStatus">
									<option value="">----</option>
									<option value="<%=User.PAY_STATUS_FIRST%>"
										<c:if test="${user.payStatus == 1}">selected</c:if>>首次</option>
									<option value="<%=User.PAY_STATUS_RENEWAL%>"
										<c:if test="${user.payStatus == 2}">selected</c:if>>续费</option>
							</select></td>
					</tr>
					</c:if >
					<c:if test="${userSign==5||userSign==6||userSign==4||userSign==8||userSign==9}">
					<tr class="SolidRow">
						<td  colspan="2">付费日期：<input type="text" name="payDateBegin" size="7" value="${user.payDateBegin }"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
								~ <input type="text" name="payDateEnd" size="7" value="${user.payDateEnd }" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
							</td>
						<td colspan="2">
						注册日期：<input type="text" name="ctimeBegin" size="7"
								value="<c:out value="${user.ctimeBegin }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>~ <input
								type="text" name="ctimeEnd" size="7"
								value="<c:out value="${user.ctimeEnd }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
							</td>
						<td colspan="2">续费日期：<input type="text" name="renewalDateBegin" size="5"
								value="<c:out value="${user.renewalDateBegin }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>~<input
								type="text" name="renewalDateEnd" size="5"
								value="<c:out value="${user.renewalDateEnd }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/></td>
						</tr>
						</c:if>
						<tr class="SolidRow">
							<td>用户名：<input type="text" name="userName" size="5" value="${user.userName }"/></td>
							<td>姓名:  <input type="text" name="trueName" size="5" value="${user.trueName }" /></td>
							<td>备注：<input type="text" name="note" size="5" value="<c:out value="${user.note }"/>" /></td>
							<td>手机号：<input type="text" name="cellPhone" size="15"
								value="<c:out value="${user.cellPhone }"/>" />
							</td>
                            <td>QQ号： <input type="text" name="qq" size="15"
								value="<c:out value="${user.qq }"/>" />
							</td>
                            <td>推荐人手机号：<input type="text" name="recommenderTel" size="15"
								value="${user.recommenderTel}"/></td>
						</tr>
						<tr class="SolidRow"> 
						<td>ID：<input type="text" name="id"
								value="<c:out value="${user.id }"/>" size="5" /> </td>
						<td>剩余天数： <input type="text" name="serveDaysBegin" size="4"
								value="<c:out value="${user.serveDaysBegin}"/>">~ <input
								type="text" name="serveDaysEnd" size="4"
								value="<c:out value="${user.serveDaysEnd}"/>"></td>
						<td>
							<%-- 每页显示： <input type="text" name="pageSize" size="4" value="<c:out value="${pageSize}"/>"> --%>
							<input type="hidden" name="pageSize" size="4" value="20"> 
						</td>
						<td>
						<c:if test="${!empty userSign&&userSign!=10&&userSign!=4}">销售：
							<%-- <c:choose>
							<c:when test="${userSign==4}"><select name="sales" ><option value="">------</option><option value="${cur_sales}" <c:if test="${user.sales == cur_sales}">selected</c:if>>${cur_sales}</option></select></c:when> 
							<c:otherwise><input type="text" size="5" name="sales" value="<c:out value="${user.sales}"/>" /></c:otherwise> </c:choose>
							 --%>
							 <%-- <select name="sales" ><option value="">------</option>
							 <c:forEach items="${saleList}" var="sale"><option value="${sale}" <c:if test="${user.sales == sale}">selected</c:if> >${sale}</option></c:forEach></select> --%>
							 <input type="text" size="5" name="sales" value="<c:out value="${user.sales}"/>" />
							 </c:if>
							 <c:if test="${userSign==4 }">销售：
							 <select name="sales" ><option value="">------</option><option value="${cur_sales}" <c:if test="${user.sales == cur_sales}">selected</c:if>>${cur_sales}</option></select>
							 </c:if>
						</td>
						<td>
						<%-- 手机试用天数：<input type="text" name="phoneServeDays" size="5" value="${user.phoneServeDays}" />(仅手机不开通有效) --%>
						</td>
						<td></td>
						</tr>
						<c:if test="${userSign==4||userSign==8}">
						<tr class="SolidRow"><td >用户状态： <select name="userType" id="userType">
									<option value="">----</option>
									<option value="<%=User.USER_TYPE_TRIAL%>"
										<c:if test="${user.userType == 0}">selected</c:if>>试用</option>
									<option value="<%=User.USER_TYPE_VIP%>"
										<c:if test="${user.userType == 1}">selected</c:if>>付费</option>
									<option value="<%=User.USER_TYPE_TRIAL_NOT_VERIFY%>"
										<c:if test="${user.userType == 2}">selected</c:if>>
										未激活</option>
							</select>
							</td>
							<td >付费状态：
								<select id="payStatus" name="payStatus">
									<option value="">----</option>
									<option value="<%=User.PAY_STATUS_FIRST%>"
										<c:if test="${user.payStatus == 1}">selected</c:if>>首次</option>
									<option value="<%=User.PAY_STATUS_RENEWAL%>"
										<c:if test="${user.payStatus == 2}">selected</c:if>>续费</option>
							</select></td>
							
							<td>用户权限身份：<%-- <select
								name="userSign">
									<option value="">----</option>
									<option value="<%=User.USER_SIGN_CAR_OWNER %>"
										<c:if test="${user.userSign == 1}">selected</c:if>>车主</option>
									<option value="<%=User.USER_SIGN_EXCHANGE_STATION %>"
										<c:if test="${user.userSign == 2}">selected</c:if>>配货站</option>
									<option value="<%=User.USER_SIGN_GOODS_OWNER %>"
										<c:if test="${user.userSign == 3}">selected</c:if>>货主</option>
									<option value="<%=User.USER_SIGN_DRIVER %>"
										<c:if test="${user.userSign == 7}">selected</c:if>>司机</option>
									<option value="<%=User.USER_SIGN_SALES %>"
										<c:if test="${user.userSign == 4}">selected</c:if>>销售
									</option>
									<option value="<%=User.USER_SIGN_SALES_MANAGER %>"
										<c:if test="${user.userSign == 8}">selected</c:if>>销售主管
									</option>
									<option value="<%=User.USER_SIGN_DATA %>"
										<c:if test="${user.userSign == 9}">selected</c:if>>数据部
									</option>
									<option value="<%=User.USER_SIGN_WEB %>"
										<c:if test="${user.userSign == 10}">selected</c:if>>网站部
									</option>
									<c:if test="${userSign==5}">
										<option value="<%=User.USER_SIGN_ADMIN %>"
											<c:if test="${user.userSign == 5}">selected</c:if>>超级管理员
										</option>
										<option value="<%=User.USER_SIGN_ADMIN_GENERAL %>"
											<c:if test="${user.userSign == 6}">selected</c:if>>普通管理员
										</option>
									</c:if>
									<c:if test="${userSign==6}">
										<option value="<%=User.USER_SIGN_ADMIN_GENERAL %>"
											<c:if test="${user.userSign == 6}">selected</c:if>>普通管理员
										</option>
									</c:if>
                                    <option value="<%=User.USER_SIGN_CLEAN %>"
										<c:if test="${user.userSign == 11}">selected</c:if>>信息清理员
									</option>
							</select>  --%>
							<select name="userSign">
									<option value="">----</option>
									<c:forEach items="${rightSignList}" var="list">
									<option value="${list.value}"
										<c:if test="${user.userSign == list.value}">selected</c:if>>
										${list.name}
										</option>
									</c:forEach>
							</select> 
							</td>
							<td>销售审核身份：
							<select name="deliverType">
							<option value="-1">----</option>
							<c:forEach items="${checkSignList}" var="list">
							<option <c:if test="${user.deliverType==list.value}">selected</c:if> value="${list.value }">${list.name}</option>
							</c:forEach>
							</select>
							</td>
							<td></td>
							<td></td>
							</tr>
						</c:if>
						<tr class="SolidRow">
						    <td>用户来源：
						    <select name="source">
						    <option value="-1">---</option>
						    <c:forEach items="${sourceList}" var="list">
						    <option value="${list.value}" 
						    <c:if test="${user.source==list.value}">selected</c:if> >
						    ${list.name}</option>
						    </c:forEach>
						    </select>
						    </td>
						    <td>来源备注：
						    <select name="sourceRemark">
						    <option value="-1">---</option>
						    <c:forEach items="${sourceRemarkList }" var="list">
						    <option value="${list.value}" 
						    <c:if test="${user.sourceRemark==list.value}">selected</c:if>
						    >${list.name}</option>
						    </c:forEach>
						    </select>
						    </td>
						    <td colspan="2">到期时间：<input name="endTime" size="7" value="${user.endTime}" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/></td>
						    <td colspan="2">预约时间：<input name="appointTime" size="7" value="${user.appointTime}"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/></td>
						</tr>
						<tr class="SolidRow">
						    <td>银行：<input name="bank" size="7" value="${user.bank}"/></td>
						    <td>金额：<input name="money" size="7" value="${user.money}"/></td>
						<td>未付(续)费原因：
						<select name="paymentReason">
						<option value="-1">---</option>
						<c:forEach  items="${paymentList}" var="list">
						<option value="${list.value}" 
						<c:if test="${user.paymentReason==list.value}">selected</c:if> 
						>${list.name}</option>
						</c:forEach>
						</select>
						</td>
						<td>近<select name="noLoginDays">
						<option value="">---</option>
									<c:forEach begin="1" end="15" var="i">
										<option value="${i}"
											<c:if test="${i==user.noLoginDays}">selected</c:if>>${i}</option>
									</c:forEach>
							</select>天未登录
							</td>
						<td></td>
						<td></td>
						</tr>
						<tr class="SolidRow">
							<td colspan="6">
							<input type="submit" value="查询" onclick="find();"> <input
								type="reset" value="重置"> 
								<c:if test="${!empty userSign&&userSign!=10}">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
                                <input type="button" value="用户登录统计" onclick="javascript:window.open('<%=request.getContextPath()%>/admin/userloginlist?first=0')">
								</c:if>
								<c:if test="${userSign==4}">
								<input style="background-color: #7ac5cd;"  type="button" value="使用说明"  onclick="javascript:window.open('<%=request.getContextPath()%>/back/jsp/user_help.jsp','newwindow','height=150,width=400,top=200,left=330,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no')"/>
								</c:if>
								<c:if test="${userSign==9}">
								<input type="button" style="color: red;" value="Excel导出" onclick="excelUserExport();"/>
								</c:if>
								</td>
						</tr>
					</table>
				</form>
			</td>
		</tr>
	</table>
	<h6>
		<c:out value="${msg }" />
		查询到：
		<c:out value="${rowCount }" />
		个用户， 当前第
		<c:out value="${pageNo }" />
		页，总共
		<c:out value="${maxPage }" />
		页

	</h6>

	<table width="1250" border="0" cellpadding=0 cellspacing=0>
		<tr>
			<td>

				<table width="100%" border="0" cellpadding="2" cellspacing="1" id="table1">
				<tr class="TitleRow" style="background-color: #838b8b;">
				<td colspan="5">用户注册信息</td>
				<td colspan="6">销售编辑信息</td>
				<td colspan="15">用户状态信息</td>
				<td colspan="6">其他备注信息</td>
				</tr>
					<tr class="TitleRow">
						<td>ID</td>
						<td>真实姓名</td>
						<!-- <td>推荐人电话</td> -->
						<td>手机号</td>
						<td>用户名</td>
						<td>QQ号</td>
						<td>销售</td>
						<td  style="width:80px;"  >沟通详情</td>
						<td  style="width:120px;"  >操作</td>
						<td>权限身份</td>
						<td>核准身份</td>
						<td>归属地</td>
						<td>性别</td>
						<td>用户状态</td>
						<td>付费状态</td>
						<td>剩余天数</td>
						<td>身份认证</td>
						<td>车辆认证</td>
						
						<td>手机开通</td>
						<td>信息发布</td>
						<td>QQ消息盒子</td>
						<td>终端来源</td>
						<!--    <td>家庭电话</td> -->
						
						<td>注册日期</td>
						<td>付费日期</td>
						<td>续费日期</td>
						<td>开通年限</td>
						<td>到期时间</td>
						
						<td>用户来源</td>
						<td>来源备注</td>
						<td>未付(续)费原因</td>
						<td>银行</td>
						<td>金额</td>
						<!-- <td>预约时间</td> -->
						<!-- <td>备注</td> -->
						<td>加时备注</td>
						
						<!-- <td>信息上传</td>
						
						<td>联系人数</td>
						
						<td>手机试用天数</td> -->
						
						
						
						
						
						
					</tr>

					<c:if test="${!empty userList }">
						<%
int i = 0;
%>

						<c:forEach items="${userList }" var="user">

							<%
if ( ++i % 2 == 0 )
{
%>
							<tr class="EvenRow" onClick="change()"   onMouseOut="out()">
								<%
}
else
{
%>
							
							<tr class="OddRow" onClick="change()"   onMouseOut="out()">
								<%
}
%>
								<td><c:out value="${user.id }" /></td>
								<td><c:out value="${user.trueName }" /></td>
                                <%-- <td>${user.recommenderTel}</td> --%>
								<td><c:out value="${user.cellPhone }" /></td>
								
								<td><c:out value="${user.userName }" /></td>
								<td><c:out value="${user.qq }" /></td>
								<td><c:out value="${user.sales }" /></td>
								<td><c:out value="${user.note }" /></td>
								<td>
									<%-- <input type="button" value="编辑" onClick="location.href='<%=request.getContextPath()%>/admin/userview?id=<c:out value="${user.id }"/>';"> --%>
                                    <table border="1" width="120px">
                                    <tr>
                                    <td><a href="<%=request.getContextPath()%>/admin/userview?id=<c:out value="${user.id }"/>"target="_blank">编辑</a></td>
                                    <td><a href="<%=request.getContextPath()%>/admin/loglist?diff=-15&userId=<c:out value="${user.id }"/>"target="_blank">日志</a></td>
                                    </tr>
                                    <c:if test="${userSign==5 || userSign==6||userSign==9||userSign==4||userSign==8}">
                                    <tr>
                                    <td colspan="1"><a href="<%=request.getContextPath()%>/boss/user/telbook/add?userId=${user.id }&first=1" target="_blank">添加电话</a></td>
                                     <td colspan="1"><a href="<%=request.getContextPath()%>/boss/car/saveInit?userId=${user.id }" target="_blank">添加车辆</a></td>
                                    </tr>
                                    </c:if>
                                    </table>
								</td>
								<td>
								    <c:if test="${!empty user.userSign}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_sign',${user.userSign})" var="sf" />
                                    <p>${sf.name}</p>
                                    </c:if>
								<td>
								<c:if test="${!empty user.deliverType}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('deliver_type',${user.deliverType})" var="sf" />
		                            <p>${sf.name}</p>
		                        </c:if>
								</td>
								<td><c:out value="${user.province }" /><c:out
										value="${user.city }" /><c:out
										value="${user.county }" /></td>
								<td>
								<c:if test="${user.sex==0}">女</c:if>
								<c:if test="${user.sex==1}">男</c:if>
								</td>
								<td><c:if test="${user.userType == 0}">试用</c:if> <c:if
										test="${user.userType == 1}">付费</c:if> 
										<c:if test="${user.userType == 2}">未激活</c:if>
										</td>
								<td><c:if test="${user.payStatus == 1}">首次</c:if> <c:if
										test="${user.payStatus == 2}">续费</c:if> 
										</td>
								<td><c:out value="${user.serveDays }" /></td>
								<td>
								<c:if test="${user.verifyFlag == 0}">未认证</c:if>
								<c:if test="${user.verifyFlag == 1}">通过</c:if>
								<c:if test="${user.verifyFlag == 2}">认证中</c:if>
								</td>
								<td>
								<c:if test="${user.isCar == 0}">未完善</c:if>
								<c:if test="${user.isCar == 1}">已完善</c:if>
								</td>
								<td><c:if test="${user.phoneOpenFlag == 0}">否</c:if> <c:if
										test="${user.phoneOpenFlag == 1}">是</c:if></td>
								<td><c:if test="${user.infoPublishFlag == 1}">发布禁止</c:if> <c:if
								test="${user.infoPublishFlag == 2}">发布可用</c:if></td>
								<td><c:if test="${user.qqBoxFlag == 0}">关闭</c:if> <c:if
										test="${user.qqBoxFlag == 1}">打开</c:if></td>
								<td><c:if test="${user.platId == 1}">PC</c:if> <c:if
										test="${user.platId == 2}">Android</c:if> <c:if
										test="${user.platId == 3}">IOS</c:if> <c:if
										test="${user.platId == 4}">APad</c:if> <c:if
										test="${user.platId == 5}">IPAD</c:if> <c:if
										test="${user.platId == 6}">WEB</c:if>
									<c:if test="${user.platId == 21}">Android-车</c:if>
									<c:if test="${user.platId == 22}">Android-货</c:if>
									<c:if test="${user.platId == 31}">IOS-车</c:if>
									<c:if test="${user.platId == 32}">IOS-货</c:if>
									<c:if test="${user.platId == 62}">Web-货</c:if>
								</td>

								<td><fmt:formatDate value="${user.ctime}" type="both"
										dateStyle="default" /></td>
								<td><c:if test="${null!=user.payDate}">
										<fmt:formatDate value="${user.payDate}" type="both"
											dateStyle="default" />
									</c:if></td>
									<td><c:if test="${null!=user.renewalDate}">
										<fmt:formatDate value="${user.renewalDate}" type="both"
											dateStyle="default" />
									</c:if></td>
									<td>${user.renewalYears}</td>
									<td><fmt:formatDate value="${user.endTime}" type="date"/></td>
								
								    <td>
								    <c:choose>
								    <c:when test="${empty user.source}">&nbsp; </c:when>
								    <c:otherwise>${user.source}</c:otherwise>
								    </c:choose>
								    </td>
									<td>
									<c:choose>
								    <c:when test="${empty user.sourceRemark}">&nbsp; </c:when>
								    <c:otherwise>
								    <c:if test="${!empty user.sourceRemark}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('source_remark',${user.sourceRemark})" var="sf" />
		                            <p>${sf.name}</p>
		                            </c:if>
								    </c:otherwise>
								    </c:choose>
									</td>
									<td>
									<c:choose>
								    <c:when test="${empty user.paymentReason}">&nbsp;</c:when>
								    <c:otherwise>
								    <c:if test="${!empty user.paymentReason}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('payment_reason',${user.paymentReason})" var="sf" />
		                            ${sf.name}
		                            </c:if>
								    </c:otherwise>
								    </c:choose>
									</td>
									<td>${user.bank}</td>
									<td>${user.money}</td>
								<%-- <td><fmt:formatDate value="${user.appointTime}" type="both"/></td> --%>
								<%-- <td><c:out value="${user.note }" /></td> --%>
								<td><c:out value="${user.homePhone }" /></td>
								
								<%-- <td><c:if test="${user.infoUploadFlag == 0}">信息待定</c:if> <c:if
										test="${user.infoUploadFlag == 1}">信息禁止</c:if> <c:if
										test="${user.infoUploadFlag == 2}">信息可用</c:if></td>
								
								<td><c:out value="${user.contactNum }" /></td>
								
                                <td><c:out value="${user.phoneServeDays }" /></td> --%>
								
							</tr>
						</c:forEach>
					</c:if>
				</table>
 
			</td>
		</tr>
	</table>
<input type="hidden" id="max_page"  name="max_page" value="${maxPage}">	 
<jsp:include  page="page.jsp" flush="true"/>
</body>
</html>