<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<style>
 	.cf:after {
		display: block;
		content: '';
		clear: both;
 	}
 	.cf {
 		zoom: 1;
 	}
 	.fl {
 		float: left;
 	}
 	.fr {
 		float: right;
 	}
 	input,button {
 		border: none 0;
 		outline: none;
 	}
	.pageOutbox {
		margin: 30px 0 60px;
	}
	.paginationRight {
		display: inline-block;
		margin: 30px 0 60px 14px;
	}
	.pageOutbox li {
		line-height: 30px;
		float: left;
		margin-right: 6px;
		border: 1px solid #ddd;
		border-radius: 5px;
		background: #fff;
	}
	.pageOutbox a {
		display: block;
		width: 36px;
		height: 28px;
		text-align: center;
		color: #333;
		font-size: 14px;
		border-radius: 5px;
	}
	.pageOutbox a:hover {
	}
	.pageOutbox a:hover,.pageOutbox a.active {
		background: #3961b2;
		border-radius: 5px;
		color: #fff;
		font-size: 14px;
	}
	.firstPage, .nextPage, .prevPage {
		font-size: 14px;
		line-height: 30px;
		display: block;
		height: 28px;
		color: #333;
		background: #fff;
	}
	.pageOutbox .firstPage {
		width: 60px;
	}
	.pageOutbox .nextPage, .pageOutbox .prevPage {
		width: 66px;
		height: 28px;
	}
	.dontClick {
		cursor: not-allowed;
	}
	.pageOutbox .dontClick:hover {
		background: #fff;
		color: #333;
	}
	.jump_page {
		width: 50px;
		height: 30px;
		border: 1px solid #ddd;
		border-radius: 5px;
		background: #fff;
		text-align: center;
		line-height: 30px;
		color: #333;
		margin: 0 10px;
	}
	.btn-default {
		padding: 0;
		display: inline-block;
		width: 60px;
		height: 30px;
		line-height: 30px;
		background: #fff;
		vertical-align: middle;
	}
	.morePagebox {
		vertical-align: middle;
		margin-right: 10px;
	}
	.btn-default:hover {
		background: #3961b2;
		border-radius: 5px;
		color: #fff;
		font-size: 14px;
	}
</style>
<head>
<script type="text/javascript">
	function query(pageNo) {
		var maxPage=document.getElementById("max_page").value;
		if(0 < pageNo && pageNo <= parseInt(maxPage)){
				var form = document.forms[0];
				form.pageNo.value = pageNo;
				form.submit();
		}
	}
</script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/common/js/common.js"></script>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>back-page</title>
</head>
<body>
<input type="hidden" id="max_page"  name="max_page" value="${maxPage}">	 
<div class="pageBox cf">
	<ul class="pageOutbox fl"> 

		<!-- 上一页 -->
		<c:choose>
		<c:when test="${pageNo<=1 }"><li><a class="firstPage dontClick" href="javascript:void(0);">首页</a></li></c:when>
		<c:otherwise><li><a class="firstPage" href="javascript:query(1)">首页</a></li></c:otherwise>
		</c:choose>
		<c:choose>
			<c:when test="${pageNo<=1}">
				<li class="disabled" ><a class="prevPage dontClick" href="javascript:void(0);">上一页</a></li>
			</c:when>
			<c:otherwise>
				<li><a class="prevPage" href="javascript:query(${pageNo-1})">上一页</a></li>
			</c:otherwise>
		</c:choose>

		<!-- 前3页的显示 -->
		<c:choose>
			<c:when test="${maxPage>3}">
				<c:forEach begin="1" end="3" varStatus="i">
					<li>
					<a href="javascript:query(${i.index})" <c:if test="${pageNo==i.index}">class="active"</c:if>>${i.index}</a>
					</li>
				</c:forEach>
				<c:if test="${pageNo>=4}">
					<c:choose>
						<c:when test="${pageNo<=8}">
							<c:forEach begin="4" end="${pageNo}" varStatus="i">
								<li><a href="javascript:query(${i.index})" <c:if test="${pageNo==i.index}">class="active"</c:if>>${i.index}</a></li>
							</c:forEach>
						</c:when>
						<c:otherwise>
							<li class="disabled"><a class="dontClick" href="javascript:void(0);">...</a></li>
							<c:choose>
								<c:when test="${pageNo+1>=maxPage}">
									<c:forEach begin="${maxPage-4}" end="${maxPage}" varStatus="i">
										<li><a href="javascript:query(${i.index})" <c:if test="${pageNo==i.index}">class="active"</c:if>>${i.index}</a>
										</li>
									</c:forEach>
								</c:when>
								<c:otherwise>
									<c:forEach begin="${pageNo-2}" end="${pageNo+2}" varStatus="i">
										<c:if test="${i.index<=maxPage }">
										<li><a  href="javascript:query(${i.index})" <c:if test="${pageNo==i.index}">class="active"</c:if>>${i.index}</a>
										</li></c:if>
									</c:forEach>
								</c:otherwise>
							</c:choose>

						</c:otherwise>
					</c:choose>
				</c:if>
				<c:if test="${pageNo+2<maxPage}">
					<li class="disabled"><a class="dontClick" href="javascript:void(0);">...</a></li>
				</c:if>
			</c:when>
			<c:otherwise>
				<c:forEach begin="1" end="${maxPage}" varStatus="i">
					<li><a href="javascript:query(${i.index})" <c:if test="${pageNo==i.index}">class="active"</c:if>>${i.index}</a>
					</li>
				</c:forEach>
			</c:otherwise>
		</c:choose>
		<!-- 下一页 -->
		<c:choose>
		<c:when test="${pageNo>=maxPage}">
		<li class="disabled "><a class="nextPage dontClick" href="javascript:void(0);">下一页</a></li>
		</c:when>
		<c:otherwise>
		<li><a class="nextPage" href="javascript:query(${pageNo+1})">下一页</a></li>
		</c:otherwise>
		</c:choose>
<!-- 		<li><a>共${maxPage}页，到第<input id="input_current_page" value="${pageNo}" size="1">页&nbsp;&nbsp;&nbsp;&nbsp;
		<input type="button" value="确定" onclick="query($('#input_current_page').val())"></a></li> -->
	</ul>
 <div class="paginationRight fl">
	<span class="morePagebox">共<em class="morePage">${maxPage}</em>页，到第<input type="text" class="jump_page input90 mlr3" id="input_current_page" value="${pageNo}" size="1">页</span><input type="button" class="btn btn-default mt-5" value="确定" onclick="query($('#input_current_page').val())">
</div> 
<div class="clearfix"></div>
</div>
</body>
<!-- <script type="text/javascript" src="/back/model/js/jquery-1.11.1.min.js"></script> -->
<script>
	$("#input_current_page").keyup(function(event) {
		if(isNaN($(this).val())){
			alert('请输入正确数值')
			$(this).val(${pageNo})
		}
		if(parseInt($(this).val())>parseInt($(".morePage").html())||parseInt($(this).val())<1){
			alert('你输入的页码不在范围')
			$(this).val(${pageNo})
		}
	});
</script>
</html>