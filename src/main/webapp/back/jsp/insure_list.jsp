<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>特运通保险广告--管理后台</title>
<link href="<%=request.getContextPath()%>/back/css/dictmng.css" rel="stylesheet"
	type="text/css">
<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/changeColor.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/js/jquery-1.7.1.js"></script>
 <script src="${pageContext.request.contextPath}/back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript">
	function query(pageNo) {
		if(pageNo>=${maxPage})pageNo=${maxPage};
		var form = document.forms[0];
		form.pageNo.value = pageNo;
		form.submit();
	}
</script>
</head>
<body>
	<table width="1300" border="0" cellpadding="0" cellspacing="0">
		<tr bgcolor="#BFBFBF">
			<td>
				<form name="employeeForm"
					action="<%=request.getContextPath()%>/admin/insurelist" method="post">
					<input type="hidden" name="pageNo"
						value="<c:out value="${pageNo}"/>">
					<table width="100%" border="0" cellpadding="2" cellspacing="1">
						<tr class="SolidRow">
							<td>
							ID: <input value="${insure.id}" name="id" size="8"/>
							</td>
							<td>
							发布日期:
							<input type="text" size="10" name="ctime" value="${insure.ctime}" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>&nbsp;&nbsp;
							</td>
							<td>
							公司名称:
							<select name="company">
							<option value="<%=Constant.INSURECOMPANY0%>"<c:if  test="${insure.company==0}">selected</c:if>>------</option>
							<option value="<%=Constant.INSURECOMPANY1%>"<c:if test="${insure.company==1}">selected</c:if>>中国人寿</option>
							<option value="<%=Constant.INSURECOMPANY2%>"<c:if test="${insure.company==2}">selected</c:if>>中国人保</option>
							</select>
							&nbsp;&nbsp;
							  状态:<select name="status">
						<option value="">--------</option>
						<option value="<%=Constant.INFO_STATUS_DISABLE %>" <c:if test="${insure.status == 0}">selected</c:if>>无效信息</option>
					    <option value="<%=Constant.INFO_STATUS_WAIT %>" <c:if test="${insure.status == 1}">selected</c:if>>待审核</option>
					    <option value="<%=Constant.INFO_STATUS_PASS %>" <c:if test="${insure.status == 2}">selected</c:if>>审核通过</option>
					    <option value="<%=Constant.INFO_STATUS_FAILURE %>" <c:if test="${insure.status == 3}">selected</c:if>>审核未通过</option>
					    </select>
							</td>
						</tr>
						<tr class="SolidRow">
						<td>							保险广告: <input type="text" name="title" size="20" value="${insure.title}"/>
												</td>
					    <td>
					        联系人: <input type="text" name="telName" size="8" value="${insure.telName}">&nbsp;&nbsp;
					    </td>
					    <td> 联系电话: <input type="text" name="telephone" size="20" value="${insure.telephone}">
					    &nbsp;
					    </td>
						</tr>
						<tr class="SolidRow">
						<td>详细描述: <input type="text" name="detail" size="20" value="${insure.detail}"></td>
						<td>
						地址:
								<input type="text" name="province" size="10" value="${insure.province}" />-
								<input type="text" name="city" size="10" value="${insure.city}"/>-
								<input type="text" name="county" size="10" value="${insure.county}" />
						
						</td>
						<td>
							发布账号: <input type="text" name="cellPhone" size="20" value="${insure.cellPhone}">				</td>
						</tr>
						<tr class="SolidRow"><td colspan="3">
						险种:
                    <input type="checkbox" name="kind" value="<%=Constant.KIND1 %>" <c:if test="${fn:contains(insure.kind,'交强险')}">checked</c:if> >       交强险 
					<input type="checkbox" name="kind" value="<%=Constant.KIND2 %>" <c:if test="${fn:contains(insure.kind,'第三者责任险')}">checked</c:if> >    第三者责任险 
					<input type="checkbox" name="kind" value="<%=Constant.KIND3 %>" <c:if test="${fn:contains(insure.kind,'车辆损失险')}">checked</c:if> >     车辆损失险 
					<input type="checkbox" name="kind" value="<%=Constant.KIND4 %>" <c:if test="${fn:contains(insure.kind,'不计免赔特约险')}">checked</c:if> >   不计免赔特约险 
					<input type="checkbox" name="kind" value="<%=Constant.KIND5 %>" <c:if test="${fn:contains(insure.kind,'盗抢险')}">checked</c:if> >           盗抢险 
					<input type="checkbox" name="kind" value="<%=Constant.KIND6 %>" <c:if test="${fn:contains(insure.kind,'车上座位责任险')}">checked</c:if> >     车上座位责任险 
					<input type="checkbox" name="kind" value="<%=Constant.KIND7 %>" <c:if test="${fn:contains(insure.kind,'玻璃单独破碎险')}">checked</c:if> >      玻璃单独破碎险
					<input type="checkbox" name="kind" value="<%=Constant.KIND8 %>" <c:if test="${fn:contains(insure.kind,'自燃险')}">checked</c:if> >            自燃险 
					<input type="checkbox" name="kind" value="<%=Constant.KIND9 %>" <c:if test="${fn:contains(insure.kind,'第三者责任险')}">checked</c:if> >        新增设备损失险
						
						</td></tr>
						<tr class="SolidRow">
							<td colspan="3"><input type="submit" value="查询" onclick="query(${pageNo});"> <input
								type="reset" value="重置"> &nbsp;&nbsp;&nbsp;&nbsp;
								<%-- 每页显示: <input type="text" name="pageSize" size="4" value="<c:out value="${pageSize}"/>"> --%>
								<!-- <input type="hidden" name="pageSize" size="4" value="20"> -->
								 &nbsp;
								<input type="button" value="保险发布" style="background-color: #fd8000" onclick="window.open('<%=request.getContextPath()%>/admin/toinsert?to=insure')"/>
								</td>
						</tr>
					</table>
				</form>
			</td>
		</tr>
	</table>
	<h6>
		<c:out value="${msg }" />
		查询到:
		<c:out value="${rowCount }" />
		条信息, 当前第
		<c:out value="${pageNo }" />
		页,总共
		<c:out value="${maxPage }" />
		页

	</h6>

	<table width="1300" border="0" cellpadding=0 cellspacing=0>
		<tr>
			<td>

				<table width="100%" border="0" cellpadding="2" cellspacing="1"  id="table1">
					<tr class="TitleRow">
		        <td>ID</td>
		        <td>联系人</td>
		        <td>发布账号</td>
		        <td>联系电话</td>
		        <td>保险广告</td>
		        <td>公司名称</td>
		        <td>险种</td>
		        <td>详细描述</td>
		        <td>地址</td>
		        <td>发布时间</td>
		        <td>状态</td>
		        <td>操作</td>
					</tr>

					<c:if test="${!empty insures }">
						<%
int i = 0;
%>

						<c:forEach items="${insures }" var="insure">

							<%
if ( ++i % 2 == 0 )
{
%>
							<tr class="EvenRow" onClick="change()"   onMouseOut="out()">
								<%
}
else
{
%>
							
							<tr class="OddRow" onClick="change()"   onMouseOut="out()">
								<%
}
%>
								<td>${insure.id }</td>
								<td>${insure.telName }</td>
								<td>${insure.cellPhone}</td>
								<td>${insure.telephone }</td>
								<td>${insure.title }</td>
								<td>
								<c:if test="${insure.company==1}">中国人寿</c:if> 
								<c:if test="${insure.company==2}">中国平安</c:if> 
								<c:if test="${insure.company==3}">泰康人寿</c:if>
								<c:if test="${insure.company==4}">太平洋保险</c:if>
								</td>
								<td>${insure.kind }</td>
                                <td>${insure.detail }</td>
                                <td>${insure.province}${insure.county}${insure.city}</td>
                                <td><fmt:formatDate value="${insure.ctime}" type="both"/></td>
                                <td>
                                <c:if test="${insure.status==0}">无效信息</c:if>
                                <c:if test="${insure.status==1}"><span style="color: red">待审核</span></c:if>
                                <c:if test="${insure.status==2}">审核通过</c:if>
                                <c:if test="${insure.status==3}">审核未通过</c:if>
                                </td>
								<td>
								<div><nobr>
								<a href="<%=request.getContextPath()%>/admin/findinsurebyid?id=${insure.id}" target="_blank" ><img  id="edit" alt="编辑" src="<%=request.getContextPath()%>/back/image/edit.gif" title="编辑"  ></a>
								</nobr>
								</div>
								</td>
							</tr>
						</c:forEach>
					</c:if>

					<tr class="TitleRow">
						<td colspan="25">
						<input type="button" value="首页" onclick="query(1)"/>
						<input type="button" value="上一页" onclick="query(${pageNo-1})"/>
						<input type="button" value="下一页" onclick="query(${pageNo+1})"/>
						<input type="button" value="尾页" onclick="query(${maxPage})"/>
						&nbsp;&nbsp;&nbsp;&nbsp;
						第
						<select onchange="query(this.value)">
						<c:forEach items="${pageNoList }" var="page">
						<option value="${page }" <c:if test="${pageNo==page }">selected</c:if>>${page }</option>
						</c:forEach>
						</select>页
						</td>
					</tr>
				</table>

			</td>
		</tr>
	</table>


</body>
</html>