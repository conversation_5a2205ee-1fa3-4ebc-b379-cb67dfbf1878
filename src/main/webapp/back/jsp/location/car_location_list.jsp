<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%><%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <base href="<%=basePath%>">
  <title>车辆定位管理</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
 <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="back/jsp/waybillOrderQuery/css/way_bill_list.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="back/model/js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
  <script type="text/javascript" src="back/js/My97DatePicker/WdatePicker.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>车辆方服务</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">车辆定位管理</span>
		</h1>
		<form action="<%=basePath%>/manage/location/getCarLocationList" method="post" id="form1">
		<input type="hidden" id="pageNo" value="${pageNo}" name="pageNo">
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li><label for="">车辆ID：</label>
				<input type="text" name="carId" value="${queryBean.carId }" maxlength="10"></li>
				<li><label for="">车主姓名：</label>
				<input type="text" name="carOwnerName" value="${queryBean.carOwnerName }" maxlength="10"></li>
				<li><label for="">平台帐号：</label>
				<input type="text" name="carOwnerCellphone" value="${queryBean.carOwnerCellphone }" maxlength="11"></li>
				<li><label for="">支持北斗定位：</label>
					<select name="useBeidou" id="">
						<option value="">------</option>
						<option value="1" <c:if test="${queryBean.useBeidou==1 }">selected</c:if>>是</option>
						<option value="3" <c:if test="${queryBean.useBeidou==3 }">selected</c:if>>否</option>
						<option value="2" <c:if test="${queryBean.useBeidou==2 }">selected</c:if>>待定</option>
					</select>
				<li><label for="">车头号码：</label>
				<input type="text" name="headCityAndNo" value="${queryBean.headCityAndNo }" maxlength="7"></li>
				<li><label for="">挂车号码：</label>
				<input type="text" name="tailCityAndNo" value="${queryBean.tailCityAndNo }" maxlength="7"></li>
				<li><label for="">最新定位方式：</label>
					<select name="locaitonType" id="">
						<option value="">------</option>
						<option value="1" <c:if test="${queryBean.locaitonType==1 }">selected</c:if>>北斗定位</option>
						<option value="3" <c:if test="${queryBean.locaitonType==3 }">selected</c:if>>APP定位</option>
						<option value="2" <c:if test="${queryBean.locaitonType==2 }">selected</c:if>>待定</option>
					</select>
				<li><label for="">跟车型车辆：</label>
					<select name="followCarStatus" id="">
						<option value="">------</option>
						<option value="1" <c:if test="${queryBean.followCarStatus==1 }">selected</c:if>>是</option>
						<option value="2" <c:if test="${queryBean.followCarStatus==2 }">selected</c:if>>否</option>
						<option value="3" <c:if test="${queryBean.followCarStatus==3 }">selected</c:if>>待定</option>
					</select>
				
			</ul>
		</div>
			</form>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="" type="button" value="查询" onclick="queryList();"></li>
					<li><input class="" type="button" value="重置" onclick="javascript:$('#form1')[0].reset()"></li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>${rowCount }</em>条信息，当前是第<span>${pageNo }</span>页，共<span>${maxPage }</span>页
            </div>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th>车辆ID</th>
					<th>车主姓名</th>
					<th>平台帐号</th>
					<th>车头号码</th>
					<th>挂车号码</th>
					<th>使用北斗定位?</th>
					<th>北斗定位正常?</th>
					<th>跟车型车辆</th>
					<th>主司机电话</th>
					<th>副司机电话</th>
					<th>随车电话</th>
					<th>最新定位时间</th>
					<th>最新定位地址</th>
					<th>最新定位方式</th>
				</tr>
				<c:forEach items="${list }" var="bean">
				<tr>
					<td>${bean.carId }</td>
					<td>${bean.carOwnerName }</td>
					<td class="phoneHide">${bean.carOwnerCellphone }</td>
					<td>${bean.headCity }${bean.headNo }</td>
					<td>${bean.tailCity }${bean.tailNo }</td>
					<td>
						<c:if test="${bean.useBeidou==1 }">是</c:if>
						<c:if test="${bean.useBeidou==2 }">待定</c:if>
						<c:if test="${bean.useBeidou==3 }">否</c:if>
					</td>
					<td>
						<c:if test="${bean.beidouStatus==1 }">是</c:if>
						<c:if test="${bean.beidouStatus==2 }">否</c:if>
						<c:if test="${bean.beidouStatus==3 }">待定</c:if>
					</td>
					<td>
						<c:if test="${bean.followCarStatus==1 }">是</c:if>
						<c:if test="${bean.followCarStatus==2 }">否</c:if>
						<c:if test="${bean.followCarStatus==3 }">待定</c:if>
					</td>
					<td>${bean.firstDriverPhone }</td>
					<td>${bean.secondCarPhone }</td>
					<td>${bean.followCarPhone }</td>
					<td><fmt:formatDate value="${bean.newLocationTime }" pattern="yyyy-MM-dd HH:mm:ss" /></td>
					<td>${bean.newLocation }</td>
					<td>
						<c:if test="${bean.locaitonType==1 }">北斗定位</c:if>
						<c:if test="${bean.locaitonType==2 }">待定</c:if>
						<c:if test="${bean.locaitonType==3 }">APP定位</c:if>
					</td>
				</tr>
				</c:forEach>
		    </table>
		    <input type="hidden" id="max_page"  name="max_page" value="${maxPage}">
		</div>
		<!--此处引入footer.jsp-->
    	 <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
    	 
	</div>

</body>
<script type="text/javascript" src="back/model/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
<script>
  var website = getRootPath_web();
	function queryList(){
		  var form=document.forms[0];
			$("#pageNo").val(1);
			form.submit();
	}
	function getRootPath_web() {
	//获取当前网址，如： http://localhost:8083/uimcardprj/share/meun.jsp
	var curWwwPath = window.document.location.href;
	//获取主机地址之后的目录，如： uimcardprj/share/meun.jsp
	var pathName = window.document.location.pathname;
	var pos = curWwwPath.indexOf(pathName);
	//获取主机地址，如： http://localhost:8083
	var localhostPaht = curWwwPath.substring(0, pos);
	//获取带"/"的项目名，如：/uimcardprj
	var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
	return (localhostPaht + projectName);
}

</script>
</html>
