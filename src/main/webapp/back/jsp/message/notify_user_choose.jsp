<%@page import="com.tyt.util.Constant"%>
<%@page import="com.tyt.model.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!doctype html>
<html>
<head>
<base href="<%=basePath%>">
<meta charset="utf-8">
<title>通知栏推送消息-群发范围选择</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<link href="back/model/css/fileinput.css" media="all" rel="stylesheet" type="text/css" >
<script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="back/model/js/bootstrap.js"></script>
<script src="back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript" src="back/model/js/fileinput.js"></script>
<script type="text/javascript">
$(function(){
	 if(${notify.sendRange==0||notify.sendRange==2||notify.sendRange==3})showContent(0);
	 if(${!empty importFlag&&importFlag==1})$("#userForm").hide();
	 //用户范围选择，决定显示隐藏
	 $("input[name=sendRange]").click(function(){
	  showContent(this.value);
	 });
	 
	 $("#sendBtn").click(sendMessage);
	 
	});
function showContent(value){
	if(value==0){
		$("#userForm").hide();
		$("#div2").hide();
		$("#userTable").hide();
	}else if(value==1){
		$("#userForm").show();
		$("#div2").show();
		$("#userTable").show();
	}
}
function sendMessage(){
	if(checkSendMessage())
	$.ajax({
		timeout: 300000,
        cache: false,
        type: "POST",
        url:"boss/message/notify/sendNotify",
        data:$('#messageForm').serialize()+"&"+$('#tableForm').serialize()+"&"+$('#userForm').serialize(),// 你的formid
        async: false,
        dataType:"json",
        beforeSend:function(XMLHttpRequest){//加载之前调用进度显示
/*             $("#loading").html("<img src='back/model/images/loading.jpg' width='60' height='60' alt=''>");
 */
        	errorMsg("发送中，请稍等......");
        	$("#sendBtn").unbind("click");
 },
        error: function(request) {
        	errorMsg("发送中，请稍等......");
        	window.opener=null; 
        	window.close(); 
        },
        success: function(data) {
        	if(data.code==200){
        		errorMsg(data.msg);
            	window.opener=null; 
            	window.close();
        	}else{
        		alert(data.msg);
        	} 
        },
        complete:function(XMLHttpRequest,textStatus){//加载完成以后停止进度显示
            $("#loading").empty();
          }
    });
}
function checkSendMessage(){
	var sendRange=$('input[name="sendRange"]:checked').val();
	var originPush = $('input[name="originPush"]:checked').val();
    var carPush = $('input[name="carPush"]:checked').val();
    var goodsPush = $('input[name="goodsPush"]:checked').val();
	var idList=$('#tableForm').serialize();
	if(sendRange==1&&idList.length<=0){
		errorMsg("请选择发送范围");
		return false;
	}
	
	return true;
}
function errorMsg(msg){
	var obj = $("#errorMsg");
	obj.text(msg);
	obj.fadeIn();
	setTimeout(function() {obj.fadeOut();}, 3000);
}
//全选
function checkAll(name)
{   
	var number=0;
    var el = document.getElementsByTagName('input');
    var len = el.length;
    for(var i=0; i<len; i++)
    {
        if((el[i].type=="checkbox") && (el[i].name==name))
        {
            el[i].checked = true;
            number+=1;
        }
    }
    setChooseNumber(number);
}
function reverseAll(name)
{
	var number=0;
    var el = document.getElementsByTagName('input');
    var len = el.length;
    for(var i=0; i<len; i++)
    {
        if((el[i].type=="checkbox") && (el[i].name==name))
        {
           el[i].checked = !el[i].checked;
           if(el[i].checked)number+=1;
        }
    }
    setChooseNumber(number);
}
function checkSingle(value){
	var number=$("#chooseUserNumber").html();
	if(typeof(number)!="undefined"&&number!=null){
		number=parseInt(number); 
	}else{
		number=0;
	}
	if(value){
		number+=1;
		}else {
			number-=1;
		}
	setChooseNumber(number);
}
function setChooseNumber(number){
	$("#chooseUserNumber").html(number);
}
</script>
<script type="text/javascript">
function importUser(obj){
	var file=obj.value;
	var point = file.lastIndexOf(".");
	var type = file.substr(point);
	  if(type==".xls"||type==".xlsx"){
	     $("#importForm").submit();
	  }else{
		  errorMsg("文件类型必须是EXCEL类型"); 
	  }
}

</script>
<style type="text/css">

.searchBox .userChoose {
  padding:0 20px 20px 20px;
  white-space: nowrap;
}

.searchBox .userChoose label {
  font-weight: normal;
}
.searchUl li {
    float: left;
    width: 220px !important;
    display: inline-block;
    margin: 0 0 14px 16px;
    *word-spacing: -1px;
}
.searchBox .userChoose input{
   width: auto;
   margin-left: 10px;
  }
.searchBox .userChoose ul li {
  float: left;
}

.searchBox .userChoose ul li .endTimeInput {
  width:180px;
  height: 24px;
  line-height: 24px;
  border: 1px solid #ccc;
}

.searchBox .userChoose ul li.errorBlock {
  color: #f00;
  margin-left: 10px;
  max-width:400px;
  overflow: hidden;
}

.searchBox .userChoose ul li.sendBtn {
  float: right;
  margin-right: 30px;
}

.buttonDiv {
  text-align: center;
  padding: 10px 0 20px 0;
}

.ml15 {
  margin-left: 15px;
}

.file-input .button:hover {
  color: #fff;
}
.import{
    color: red !important;
}

</style>

<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
  <script src="http://cdn.bootcss.com/html5shiv/3.7.2/html5shiv.min.js"></script>
  <script src="js/respond.min.js"></script>
<![endif]-->
    
</head>
<body>
<input type="hidden" id="deliverTypeOneId" value="${deliverTypeOne }">
<input type="hidden" id="deliverTypeTwoId" value="${deliverTypeTwo }">

<div class="contentBox">
<div class="titUrl cf">
  <h1 class=" fl">
        <span>当前所在位置：</span><span>APP-消息管理</span><span>&nbsp;&gt;&nbsp;</span><span>通知栏消息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">群发范围选择</span>
      </h1>
    </div>
  <div class="main-content"> 
    
    <!--搜索条件-->  
    <div class="searchBox">
        <form id="messageForm" method="post" class="userChoose">
           <input type="hidden" value="${fileName}" name="fileName">
           <input type="hidden" value="${notify.id}" name="notifyId">
           <span class="messageInfo">信息发送说明：
           <c:choose>
              <c:when test="${fn:length(notify.remarks) > 20}">
                 <c:out value="${fn:substring(notify.remarks, 0, 20)}" />......
              </c:when>
              <c:otherwise>
                 <c:out value="${notify.remarks}" />
              </c:otherwise>
           </c:choose>
           </span>
          <%--
           <ul class="cf">
               <div class="infoTemplateP">
                   <span class="import">*</span>
                   <label for="">选择发端</label>
                   <input type="checkbox" name="originPush" value="1" class="radioBtn" <c:if test="${notify.originPush==1 }">checked</c:if>>特运通老APP端
                   <input type="checkbox" name="carPush" value="1" class="radioBtn" <c:if test="${notify.carPush==1 }">checked</c:if>>车主端
                   <input type="checkbox" name="goodsPush" value="1" class="radioBtn" <c:if test="${notify.goodsPush==1 }">checked</c:if>>货主端
               </div>
           </ul>
          --%>
           <ul class="cf">
            <li style="width:500px">
              <label>信息发送范围：</label>
              <input name="sendRange" type="radio" value="0" <c:if test="${notify.sendRange==0 }">checked</c:if>>系统全部用户
              <input name="sendRange" type="radio" value="1" <c:if test="${notify.sendRange==1 }">checked</c:if>>系统指定用户
            </li>
            <li class="errorBlock"><span id="errorMsg"></span></li>
            <li class="sendBtn"><input id="sendBtn" type="button" class="button" value="发送"></li>
           </ul>
        </form>
        <form id="userForm" method="post"  action="boss/message/notify/getUser">
        <input type="hidden" value="${notify.id}" name="notifyId">
        <ul class="searchUl searchUlNone cf">
        <li>
			<label for="deliverType">销售审核一级：</label>
			<select name="deliverTypeOne" id="deliver_type_one_select_lable_id"></select>
		</li>
		<li>
			<label for="deliverType">销售审核二级：</label>
			<select name="deliverType" id="deliver_type_two_select_lable_id"></select>
			</select>
		</li>
        <li>
          <label>注册时间：</label>
          <input class="pinfoTable" name="ctimeBegin" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.ctimeBegin }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
          <span>-</span>
          <input class="pinfoTable" name="ctimeEnd"   value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.ctimeEnd }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
        </li>
        <li>
          <label>缴费次第：</label>
		  <input name="payNumber" value="${user.payNumber}" class="pinfoTable" >
							 <span>-</span><input name="payNumberEnd" value="${user.payNumberEnd}" class="pinfoTable" >
        </li>
        <li>
        <label>未付(续)费原因：</label>
		<select name="paymentReason" class="select180">
		<option value="-1">请选择</option>
		<c:forEach  items="${paymentList}" var="list">
		<option value="${list.value}" 
		<c:if test="${user.paymentReason==list.value}">selected</c:if> 
		>${list.name}</option>
		</c:forEach>
		</select>
        </li>
         <li>
          <label>剩余天数：</label>
          <input class="pinfoTable" name="serveDaysBegin" value="${user.serveDaysBegin}">
		  <span>-</span>
		  <input class="pinfoTable" name="serveDaysEnd" value="${user.serveDaysEnd}">天
        </li>
        <li>
          <label>连续未登录：</label>
          <input class="pinfoTable" name="noLoginDaysBegin" type="text" value="${user.noLoginDaysBegin}">
          <span>-</span>
          <input class="pinfoTable" name="noLoginDaysEnd" type="text" value="${user.noLoginDaysEnd}">天
        </li>
        <li>
          <label>终端来源：</label>
          <select name="platId" class="select180">
			<option value="">请选择</option>
			<c:forEach  items="${clientSignList}" var="list">
		    <option value="${list.value}" 
		    <c:if test="${user.platId==list.value}">selected</c:if> >${list.name}</option>
		    </c:forEach>
		  </select>
        </li>
        <li>
        <label>版本号：</label>
        <input class="pinfoTable" name="versionStart" type="text" value="${user.versionStart}"><span>-</span>
        <input class="pinfoTable" name="versionEnd" type="text" value="${user.versionEnd}">
        </li> 
        <li>
          <label> 身份信息认证：</label>
          <select name="verifyInfoSign" class="select180">
									<option value="">----</option>
									<option value="<%=User.VERIFY_DISABLE%>"
										<c:if test="${user.verifyInfoSign == 0}">selected</c:if>>未认证</option>
									<option value="<%=User.VERIFY_ENABLE%>"
										<c:if test="${user.verifyInfoSign == 1}">selected</c:if>>通过</option>
									<option value="<%=User.VERIFY_WAIT%>"
										<c:if test="${user.verifyInfoSign == 2}">selected</c:if>>认证中</option>
									<option value="3"
										<c:if test="${user.verifyInfoSign == 3}">selected</c:if>>认证失败</option>
											
										</select>
        </li>
        <li>
          <label> 身份照片认证：</label>
          <select name="verifyPhotoSign" class="select180">
									<option value="">----</option>
									<option value="<%=User.VERIFY_DISABLE%>"
										<c:if test="${user.verifyPhotoSign == 0}">selected</c:if>>未认证</option>
									<option value="<%=User.VERIFY_ENABLE%>"
										<c:if test="${user.verifyPhotoSign == 1}">selected</c:if>>通过</option>
									<option value="<%=User.VERIFY_WAIT%>"
										<c:if test="${user.verifyPhotoSign == 2}">selected</c:if>>认证中</option>
									<option value="3"
									<c:if test="${user.verifyPhotoSign == 3}">selected</c:if>>认证失败</option>
									</select>
        </li>
       <li>
        <label>手机号：</label>
        <input class="input180" name="cellPhone" type="text" value="${user.cellPhone}">
        </li>
        <%-- <li>
          <label>用户来源：</label>
          <select name="source" class="select180">
						    <option value="-1">---</option>
						    <c:forEach items="${sourceList}" var="list">
						    <option value="${list.value}" 
						    <c:if test="${user.source==list.value}">selected</c:if> >
						    ${list.name}</option>
						    </c:forEach>
						    </select>
        </li> --%>
        
      </ul>
      
      <!-- <a class="pl115" href="javascript:void(0)">保存搜索条件</a> -->
       <div class="buttonDiv"><input type="submit" class="button" value="查询"><input type="reset" class="button ml15" value="重置"></div>
      </form>
    </div>
   
    <!--页面信息-->
    <div id="div2" class="pageMessage">
      <div class="searchBtn">
        <ul class="btnBox cf">
          <li>
            <input onClick="checkAll('idList');" type="button" class="button" value="全选">
          </li>
          <li>
            <input onClick="reverseAll('idList');"  type="button" class="button" value="反选">
          </li>
          <%-- <li>
            <form id="importForm" action="boss/message/notify/importUser" method="post" enctype="multipart/form-data">
            <input  type="hidden" name="notifyId" value="${notify.id}">
            <input name="file" type="file" class="button" value="导入" onchange="importUser(this);">
            </form>
          </li> --%>
          <li>
            <form id="importForm" action="boss/message/notify/importUser" method="post" enctype="multipart/form-data">
            <input  type="hidden" name="notifyId" value="${notify.id}">
            <div class="form-group">
               <span class="file-input file-input-new">
                <div class="btn button btn-file">导入<input name="file" onchange="importUser(this);" type="file"></div>
                </span>
                </div>
                </form>
          </li>
        </ul>
      </div>
      <!--加载中的提示-->
        <div id="loading" class="loadingImg"></div>
      <div class="messageRight fr"> 查询到<em>${rowNumber}</em>条记录，已选<em id="chooseUserNumber">0</em>条记录</div>
      <div class="clearfix"></div>
    </div>
    
    <!--内容列表-->
    <div class="dataTable">
    <form id="tableForm">
    <table id="userTable" width="100%" border="0" class="tableBox">
      <tr class="tableTh">
        <th>选择</th>
        <th>手机号</th>
        <th>注册身份</th>
        <th>付费状态</th>
        <th>剩余天数</th>
        <th>注册日期</th>
        <th>到期日期</th>
      </tr>
      <c:forEach items="${userList}" var="user">
      <tr>
        <td><input name="idList" value="${user.id}" type="checkbox" onclick="checkSingle(this.checked);"></td>
        <td>${user.cellPhone}</td>
        <td>
        <c:if test="${!empty user.userSign}">
		<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_sign',${user.userSign})" var="sf" />
        <p>${sf.name}</p>
        </c:if>
        </td>
        <td>
        <c:if test="${user.payStatus == 1}">首次</c:if> 
		<c:if test="${user.payStatus == 2}">续费</c:if>
        </td>
        <td>${user.serveDays}</td>
        <td><fmt:formatDate value="${user.ctime}"   type="both"/></td>
        <td><fmt:formatDate value="${user.endTime}" type="date"/></td>
      </tr>
      </c:forEach>
    </table>
    </form>
  </div>
 </div>
</div>
${msg}
</body>
<script type="text/javascript" src="back/jsp/message/js/common.js"></script>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
</html>
