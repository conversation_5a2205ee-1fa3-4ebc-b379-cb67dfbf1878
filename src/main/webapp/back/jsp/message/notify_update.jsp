<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!doctype html>
<html>
<head>
<base href="<%=basePath%>">
<meta charset="utf-8">
<title>消息管理-推送消息设置-修改</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/detailPages.css" type="text/css" rel="stylesheet">

<style>
	.infoTemplateP {
		display: flex;
	}
	.infoTemplateP > label {
		width: 90px;
	}
	.chooseSend {
		flex: 1;
	}
</style>

<script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/bootstrap.js"></script>
<script type="text/javascript">
//检测${error},如果为“保存成功，则关闭当前页面”
function getError(){
	if(${!empty error&&error==1}){
		closeCurrentPage();
	}
}
window.onload=getError();
/*参数校验*/
function checkError(){
	var content=$("#content");//列表摘要
	var linkUrl=$("#linkUrl");//详情链接
	var openType=$("#openType");//列表类型
	var timeLong=$("#timeLong");//详情类型
	var r = /^[0-9]*[1-9][0-9]*$/;//正整数
	if($(".chooseSend>input:checked").length == 0) {
		writeError("信息填写不完整");
		return false;
	}
	if(content.val().length==0){
		content.focus();
		writeError("信息填写不完整");
		return false;
	}
	//打开方式
	if(openType.val()==1){
		// if(linkUrl.val().length==0){
		// 	linkUrl.focus();
		// 	writeError("信息填写不完整");
		// 	return false;
		// }
    if(tytTool.replaceStrAll(linkUrl.val()) == '' &&  $("#jumpPage").val() === '') {
			writeError("跳转页面和详情链接不能同时为空!");
			return false;
    }
	}
	if(timeLong.val().length==0||!r.test(timeLong.val())){
		timeLong.focus();
		writeError("信息填写不完整");
		return false;
	}
	return true;
}

/*错误信息*/
function writeError(msg){
	$("#errorBlock").html(msg);
}
/*显示与隐藏区域*/
$(function(){
	//打开方式change事件
	$("#openType").change(function(){
		var type=this.value;
		if(type==1){
			$("#display1,#jumpPageWarp").show();
		}else{
			$("#display1,#jumpPageWarp").hide();
		}
		
	});

	$(".chooseSend input").change(function() {
		$('.optionItem').remove()
		var goodsOption = $('input[name="carPush"]').is(':checked') ? '<option class="optionItem" value="1">找货页面</option>' : '',
			carsOption = $('input[name="goodsPush"]').is(':checked') ? '<option class="optionItem" value="2">发货页面</option>' : '';
			if($('input[name="carPush"]').is(':checked')  && $('input[name="goodsPush"]').is(':checked')) {
				goodsOption = '';
				carsOption = '';
			}
		$("#jumpPage .firstOption").after(goodsOption + carsOption)
	})
	$("#jumpPage").change(function() {
		$("#display1 > input").val('')
	}) 

	//保存按钮click事件
	$("#saveBtn").click(function(){
		if(checkError()){
			$("#messageForm").attr("action","boss/message/notify/update");
			$("#messageForm").submit();	
		}
		
	});
	//发信按钮click事件
    $("#sendBtn").click(function(){
    	if(checkError()){
    		$("#messageForm").attr("action","boss/message/notify/updateSend");
    		$("#messageForm").submit();
    	}
    	
	});
	//返回按钮click事件
    $("#backBtn").click(function(){
    	closeCurrentPage();
	});
	
});

function closeCurrentPage(){
	window.opener=null; 
	window.close(); 
}
</script>
</head>

<body class="theme-blue">
  <div class="contentBox">
      <h1 class="titUrl">
            <span>当前所在位置：</span><span>APP-消息管理</span><span>&nbsp;&gt;&nbsp;</span><span>通知栏推送消息</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">修改</span>
      </h1>
  
      <div class="main-content">
         <!--系统设置-->
         <form id="messageForm"  method="post" enctype="multipart/form-data">
         <input type="hidden" name="id" value="${notify.id}">
         <div class="coreDataManagement nom_coreDataManagement">
            <ul class="">
              <li>
                <label>消息类型：</label>
                <select name="msgType" class="sec_select">
                  <c:forEach items="${msgTypeList }" var="msgType">
                    <option <c:if test="${notify.msgType==msgType.value}">selected</c:if> value="${msgType.value}">${msgType.name}</option>
                  </c:forEach>
                </select>
              </li>
              <li class="infoTemplateP">
                <label for=""><i class="mustRed">*</i>选择发端：</label>
                <p class="chooseSend">
                  <input type="checkbox" name="carPush" value="1" class="radioBtn" <c:if test="${1 == notify.carPush }">checked="checked"</c:if>>车主端  &nbsp&nbsp&nbsp
                  <input type="checkbox" name="goodsPush" value="1" class="radioBtn" <c:if test="${1 == notify.goodsPush }">checked="checked"</c:if>>货主端  &nbsp&nbsp&nbsp
                </p>
              </li>
              <li>
                <label><i class="mustRed">*</i>打开方式：</label>
                <select id="openType" name="openType" class="sec_select">
                  <option <c:if test="${notify.openType=='0'}">selected</c:if> value="0">启动APP</option>
                  <option <c:if test="${notify.openType=='1'}">selected</c:if> value="1">打开详情</option>
                </select>
              </li>           <!--当打开方式为打开详情时，详情链接为必填-->
              <li>
                <label><i class="mustRed">*</i>有效时长：</label>
                <input id="timeLong" class="sec_input" maxlength="5" value="${notify.timeLong }" name="timeLong" type="text" class="input126">小时,该时间上线的用户均可收到通知
              </li>

              <li id="jumpPageWarp" <c:if test="${'0' == notify.openType }">style="display: none"</c:if>>
                <label for="type">跳转页面：</label>
                <select name="localTable" id="jumpPage" class="sec_select">
                  <option class="firstOption" value="">请选择</option>
                  <option value="3">实名认证</option>
                  <option value="4">企业认证</option>
                  <option value="5">我的会员</option>
                </select>
              </li>
              <li id="display1" <c:if test="${'0' == notify.openType }">style="display: none"</c:if>>
                <label>详情链接：</label>
                <input maxlength="150" value="${notify.linkUrl }" id="linkUrl" name="linkUrl" type="text" class="sec_input" placeholder="支持http https链接">
              </li>

            </ul>

              
              <div class="detail_textarea cf">
                <label class="fl" for="">说明：</label>
                <textarea maxlength="30" id="remarks" name="remarks" type="text" class="input500" placeholder="最多30字" >${notify.remarks}</textarea>
              </div>

               
              <div class="detail_textarea cf">
               <label class="fl"><i class="mustRed">*</i>内容：</label>
               <textarea id="content" name="content" class="textarea w500 h100" maxlength="50" placeholder="最多50字" cols="" rows="">${notify.content}</textarea>
              </div>

              <span class="titBlackred">提示：推送消息，用户不确定百分百接收到，请谨慎使用</span>

              <p id="errorBlock" class="errorBlock" style="margin-left: 90px;text-align: left;">${error}</p>
              <div class="tc nom_text">
                    <input type="button" id="saveBtn" class="button" value="保存">
                    <input id="sendBtn"  type="button" class="button" value="发送">
                    <input id="backBtn" type="button" class="button" value="返回">
              </div>
         </div>
         </form>

      </div>
    </div>
</body>
<script type="text/javascript">
if(${notify.openType=='1'}){
  $("#display1,#jumpPageWarp").show();
}
if(${notify.openType=='0'}){
  $("#display1,#jumpPageWarp").hide();
}
  
</script>
</html>