<%@taglib prefix="s" uri="http://www.springframework.org/tags"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ page import="com.tyt.model.User"%>
<%@ page import="com.tyt.util.Constant"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<base href="<%=basePath%>">
<title>用户管理</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">

<script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="back/model/js/bootstrap.js"></script>
<script src="back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript" src="back/model/js/common_tanchuang.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/common_cookie.js"></script>
<script type="text/javascript" src="back/menu/js/menu_common.js"></script>
<script type="text/javascript" src="back/jsp/js/ajax_source3.js"></script>
<script type="text/javascript" src="back/jsp/js/ajax_source2.js"></script>
<script type="text/javascript" src="shenfenrenzheng/js/shenfenrenzheng_common.js"></script>
<script src="back/model/js/tyt_area/js/tyt_area.js"></script>
<script src="back/model/js/tyt_common.js"></script>
<style>
	#payModal{
		margin-top: -200px;
	}
	.pay-method-radio{
		width: 15px!important;
		height: 15px!important;
	}
	.pay-method-img{
		width: 79px!important;
		height: 30px!important;
		position: relative;
		top: -2px;
	}
	#submitPayModal{
		margin-top:-105px;
	}
	#submitPayModal label{
		width: 280px;
		text-align: left;
		text-indent: 30px;
	}
	#submitPayModal img{
		margin: 0 auto;
		width:126px;
		height: 126px;
		display: block;
	}
	#submitPayModal p{
		text-align: center;
	}
	.pay-success{
		font-size: 20px;
	}
	.goods-sm{
		position: relative;
		top: -33px;
	}
	#payModal ul li input,#payModal ul li label{
		padding: 0;
	}

	.outBgColor, .outMoTaiBgColor {
		width: 100%;
		height: 100%;
		position: fixed;
		_position: absolute;
		left: 0;
		top: 0;
		bottom: 0;
		right: 0;
		background: #000;
		opacity: 0.7;
		filter: alpha(opacity=70);
		z-index: 9999;
	}
	.alert{
		z-index: 10000;
	}
	#cellPhone,
	#goodsName {
		width: 210px;
		height: 30px;
		border: 1px solid #ccc;
		padding-left: 10px;
		border-radius: 5px;
		padding: 0 5px!important;
		box-sizing: border-box;
	}
	.update-button{
		display: block;
		line-height: 1px !important;
	}
	.update-button span{
		display: inline-block;
		color: #fff;
		background: #2957a5;
		width: 80px;
		height: 28px;
		line-height: 28px;
		text-align: center;
		border: none 0;
		border-radius: 4px;
		background: #469ad0;
	}
	.unvalidType{
		height: 300px !important;
	}
	.pay-QR-img {
		width: 100px;
		height: 100px;
		margin: auto;
		margin-bottom: 20px;
	}
	.pay-QR-img canvas{
		width: 100px;
		height: 100px;
	}
</style>

<!--用户手机号处理end  -->
<script type="text/javascript">
$(document).ready(function(){
	 $("#userType").change(function(){
		 var type=this.value;
		 if(type!=1){
		 	 $('#payStatus option:first').attr('selected','selected');}
	 });
	 //$("#source").change(getSourceRemark());
	 $("#source").attr("onchange","getSourceRemark()");
});


</script>
<script type="text/javascript">
function excelUserExport(){ 
	
	var checkResult = validExportCookie("user_list_excelUserExport");
	
	if(checkResult == undefined ||checkResult == "" || checkResult == "-1"){
		alert("请求异常，请稍后重试！");
		return ;
	}
	if(checkResult 	!= "0"){
		alert(checkResult);
		return ;
	}
	var form=document.forms[0];
	form.action="admin/excelExportUser";
	form.submit();
	form.action="admin/userlist";
}
function find(){
	var form=document.forms[0];
	$("#pageNoId").val(1);
	form.action="admin/userlist";
	form.pageSize.value=$("#pageSizeId").val();
	form.submit();
}
function getSourceRemark(){
	var pid=$("#source").find("option:selected").attr("pid");
	if('-1'!=pid){
	//alert(pid);
	 $.ajax({				 
			 type : "get", 			     
			 url:'boss/tytsource/list',
			 async:false,
			 dataType:"json",
			 data:'id='+pid,
			 success:function(data){ 
				 $("#sourceRemark").empty(); 
				 if(data!="0"){  
		                $("#sourceRemark").append("<option pid='-1' value='-1'>----</option>");
		                for(var i=0;i<data.length;i++){  
		                	$("#sourceRemark").append("<option pid='"+data[i].id+"' value='"+data[i].value+"'>"+data[i].name+"</option>");
		                }  
		          } 
			 },
			 error:function(XHR, textStatus, errorThrown){ 				 
				 //$('#sendVoiceVerify').click(sendVoiceCode);	
				 alert("网络有点慢，请重试！");
			 }
		});
	}else{
		$("#sourceRemark").empty(); 
		 $("#sourceRemark").append("<option pid='-1' value='-1'>----</option>");
	}
	
}
function userLoginTongJi(){
	window.location.href="admin/userloginlist?first=0";
}
function resetForm(){
	// $('#userForm')[0].reset();
	tytTool.formReset($('#searchCondition'))
}
</script>
</head>
<style>
	.blue {
		color: #428bca;
		cursor: pointer;
	}
</style>
<body class="">

	<div class="contentBox">

		<div class="titUrl cf">
			<h1 class=" fl">
				<span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">用户信息管理</span>
			</h1>
			<div class="pageNum fr">
				<span style="display: none;">每页显示</span> <input type="hidden" id="pageSizeId" name="pageSize"
					value="${pageSize }">
			</div>

		</div>
		<div class="main-content">
			<form name="userForm" id="userForm"
				action="<%=request.getContextPath()%>/admin/userlist" method="post">
				<input type="hidden" id="pageNoId" name="pageNo" value="${pageNo}"> <input
					type="hidden" name="menuId" value="${curMenu.id }"> <input
					type="hidden" name="pageSize" value="${pageSize}"> <input
					type="hidden" id="searchChannelCode" value="${user.channel }" /> 
					<input
					type="hidden" id="identityTypeId" value="${user.identityType }" />
				<input type="hidden" id="deliverTypeOneId"
					value="${user.deliverTypeOne }" /> <input type="hidden"
					id="deliverTypeTwoId" value="${user.deliverType }" />
				<input type="hidden"
					   id="deliverTypeThreeId" value="${user.deliverTypeThree }" />

				<input type="hidden" id="goodsTypeFirstId" value="${user.goodsTypeFirst == null ? 0 : user.goodsTypeFirst}" />

				<input type="hidden" id="goodsTypeSecondId" value="${user.goodsTypeSecond == null ? 0 : user.goodsTypeSecond}" />

				<input type="hidden" id="auditGoodsTypeFirstId" value="${user.auditGoodsTypeFirst == null ? 0 : user.auditGoodsTypeFirst}" />

				<input type="hidden" id="auditGoodsTypeSecondId" value="${user.auditGoodsTypeSecond == null ? 0 : user.auditGoodsTypeSecond}" />

				<!--搜索条件-->
				<div class="searchBox" id="searchCondition">
					<ul class="searchUl cf">
					<li>
							<label>用户ID：</label>
							<input type="text" name="id" value="<c:out value="${user.id }"/>" class="input180" />
						</li>
					<li>
							<label>用户名：</label>
							<input type="text" name="userName" class="input180" value="${user.userName }" />
						</li>
						<li>
							<label>真实姓名：</label>
							<input type="text" name="trueName" class="input180" value="${user.trueName }" />
						</li>
						<li>
							<label>手机号：</label>
							<input type="text" name="cellPhone" class="input180" value="${user.cellPhone}" />
						</li>
						<li>
							<label>用户状态：</label>
							<select name="userType"
							id="userType" class="select180">
								<option value="">----</option>
								<option value="<%=User.USER_TYPE_TRIAL%>"
									<c:if test="${user.userType == 0}">selected</c:if>>试用</option>
								<option value="<%=User.USER_TYPE_VIP%>"
									<c:if test="${user.userType == 1}">selected</c:if>>付费</option>
								<option value="<%=User.USER_TYPE_TRIAL_NOT_VERIFY%>"
									<c:if test="${user.userType == 2}">selected</c:if>>
									未激活</option>
							</select>
						</li>
						<li style="height: 24px">
							<div style="position: absolute; z-index: 1000">
								<label style="position: absolute;">归属地：</label>
								<div class="fl areaDiv twoDiv"  style="position: absolute; left: 98px;">
							    	<input id="position" type="text" class="input120" placeholder="请选择地区" onclick="tyt_open_area('1','position','boxDiv','sheng','shi','xian','tyt_area_iframe','3');"
							            readonly class="positionInput addressIcon" value="<c:if test='${!fn:startsWith(user.city,user.province)}'>${user.province}&nbsp;</c:if>${user.city}<c:if test="${user.county!=user.city }">&nbsp;${user.county}</c:if>">
							        <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
							            id="boxDiv" class="boxDiv">
							            <iframe name="tyt_area_iframe" id="tyt_area_iframe" src="back/model/js/tyt_area/area.html"
							                frameborder="0" width="240" scrolling="no" height="400" leftmargin="0" topmargin="0" allowtransparency="true"></iframe>
							        </div>
							        <p class="noneInput mtl20"><input id="sheng" name="province" type="text" value="${user.province }" class="positionInput"></p>
							        <p class="noneInput mtl20"><input id="shi" name="city" type="text" value="${user.city }"  class="positionInput"></p>
							        <p class="noneInput mtl20"><input id="xian" name="county" type="text" value="${user.county }"  class="positionInput"></p>
							        <div class="cb"></div>
							    </div>
						    </div>
						</li>
						<li>
							<label>注册开始日期：</label> 
							<input class="input180" type="text" id="ctimeBeginId" name="ctimeBegin" size="7" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.ctimeBegin }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
						</li>
						<li>
							<label>注册结束日期：</label>
							<input class="input180" type="text" id="ctimeEndId" name="ctimeEnd" size="7" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.ctimeEnd }"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
						</li>
						<li>
							<label>用户注册身份：</label>
							<select name="identityType"
							id="identity_type_select_lable_id" class="select180"></select>
						</li>
						<li><label>审核身份一级：</label> <select name="deliverTypeOne"
							id="deliver_type_one_select_lable_id" class="select180"></select>
						</li>
						<li><label>审核身份二级：</label>
						 <select name="deliverType"
							id="deliver_type_two_select_lable_id" class="select180"></select>
						</li>
						<li><label>审核备注：</label>
							<select name="deliverTypeThree"
									id="deliver_type_three_select_lable_id" class="select180">
								<option value="-1">------</option>
							</select>
						</li>
						<li>
							<label>身份核实状态：</label>
							<select name="auditStatus"
									id="auditStatus" class="select180">
								<option value="">请选择</option>
								<option value="0"
										<c:if test="${user.auditStatus == 0}">selected</c:if>>未核实</option>
								<option value="1"
										<c:if test="${user.auditStatus == 1}">selected</c:if>>已核实</option>
							</select>
						</li>
						<li>
							<label>付费日期：</label>
							<input class="pinfoTable" type="text"
							name="payDateBegin"
							value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.payDateBegin }"/>"
							onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" /><span>-</span><input
							class="pinfoTable" type="text" name="payDateEnd"
							value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.payDateEnd }"/>"
							onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
						</li>
						<li>
							<label>续费日期：</label>
							<input type="text"
							name="renewalDateBegin" class="pinfoTable"
							value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.renewalDateBegin }"/>"
							onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" /><span>-</span><input
							type="text" name="renewalDateEnd" class="pinfoTable"
							value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.renewalDateEnd }"/>"
							onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
						</li>
						<li>
							<label>开通年限：</label>
							<select name="renewalYears" class="select180">
								<option value="">----</option>
								<option value="<%=User.RENEWAL_YEARS_ONE %>"
									<c:if test="${user.renewalYears ==1}">selected</c:if>>1</option>
								<option value="<%=User.RENEWAL_YEARS_TWO %>"
									<c:if test="${user.renewalYears == 2}">selected</c:if>>2</option>
								<option value="<%=User.RENEWAL_YEARS_THREE %>"
									<c:if test="${user.renewalYears == 3}">selected</c:if>>3</option>
							</select>
						</li>
						
						<li>
							<label>到期时间：</label>
							<input name="endTime" class="pinfoTable"
							value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.endTime }"/>"
							onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" /><span>-</span><input
							name="endTimeEnd" class="pinfoTable"
							value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.endTimeEnd}"/>"
							onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
						</li>
						<li>
							<label>用户来源：</label>
							<select name="source" id="source"
							class="select180">
								<option pid='-1' value="-1">---</option>
								<c:forEach items="${sourceList}" var="list">
									<option pid="${list.id}" value="${list.value}"
										<c:if test="${user.source==list.value}">selected</c:if>>
										${list.name}</option>
								</c:forEach>
							</select>
						</li>
						<li>
							<label>来源备注：</label>
							<select name="sourceRemark"
							id="sourceRemark" class="select180">
								<option pid='-1' value="-1">---</option>
								<c:if test="${!empty sourceRemarkList }">
									<c:forEach items="${sourceRemarkList }" var="list">
										<option value="${list.value}"
											<c:if test="${user.sourceRemark==list.value}">selected</c:if>>${list.name}</option>
									</c:forEach>
								</c:if>
							</select>
							</li>
						<li>
							<label>剩余天数：</label><input type="text" name="serveDaysBegin" class="pinfoTable" value="<c:out value="${user.serveDaysBegin}"/>"><span>-</span><input
							type="text" name="serveDaysEnd" class="pinfoTable"
							value="<c:out value="${user.serveDaysEnd}"/>">
						</li>
						<li>
							<label>销售：</label>
							<input type="text" class="input180" name="sales" value="<c:out value="${user.sales}"/>" />
						</li>
						<li>
							<label>近？天未登录：</label>
							<select name="noLoginDays" class="select180">
								<option value="">---</option>
								<option value="7"<c:if test="${7==user.noLoginDays}">selected</c:if>>7</option>
								<option value="15"<c:if test="${15==user.noLoginDays}">selected</c:if>>15</option>
								<option value="30"<c:if test="${30==user.noLoginDays}">selected</c:if>>30</option>
								<option value="90"<c:if test="${90==user.noLoginDays}">selected</c:if>>90</option>
								<option value="180"<c:if test="${180==user.noLoginDays}">selected</c:if>>180</option>
								<option value="360"<c:if test="${360==user.noLoginDays}">selected</c:if>>360</option>
							</select>
						</li>
						<li>
							<label>沟通详情：</label>
							<input type="text" name="note" class="input180" value="${user.note }" />
						</li>
<!-- 						渠道搜索隐藏 -->
<!-- 						<li> -->
<%-- 							<label>渠道：</label> <select id="channelSelect" --%>
<%-- 							name="channel" class="select180"> --%>
<%-- 							</select> --%>
<!-- 						</li> -->
						<li>
							<label>未缴费原因：</label>
							<select name="paymentReason" class="select180">
								<option value="-1">---</option>
								<c:forEach items="${paymentList}" var="list">
									<option value="${list.value}"
										<c:if test="${user.paymentReason==list.value}">selected</c:if>>${list.name}</option>
								</c:forEach>
							</select>
						</li>
						
						<li>
							<label>缴费次第：</label>
							<input name="payNumber"value="${user.payNumber}" class="pinfoTable"><span>-</span><input name="payNumberEnd" value="${user.payNumberEnd}" class="pinfoTable">
						</li>
						<li>
							<label>转账银行：</label>
							<input name="bank" class="input180"用户导出
							value="${user.bank}" />
						</li>
						<li>
							<label>缴费金额：</label>
							<input name="money" class="input180"
							value="${user.money}" />
						</li>
						<li>
							<label>缴费优先级：</label>
							<select name="payPriority">
								<option value="-1">请选择</option>
								<c:forEach items="${payPriorities }" var="payPriority">
									<option <c:if test="${user.payPriority==payPriority.value}">selected</c:if> value="${payPriority.value }">${payPriority.name }</option>
								</c:forEach>
							</select>
						</li>


						<li>
							<label>推荐人手机号：</label>
							<input type="text"
							name="recommenderTel" class="input180"
							value="${user.recommenderTel}" />
						</li>
						<li>
							<label>身份认证状态：</label> 
							<select name="verifyPhotoSign" class="select180">
								<option value="">----</option>
								<option value="<%=User.VERIFY_DISABLE%>"
									<c:if test="${user.verifyPhotoSign == 0}">selected</c:if>>未认证</option>
								<option value="<%=User.VERIFY_ENABLE%>"
									<c:if test="${user.verifyPhotoSign == 1}">selected</c:if>>通过</option>
								<option value="<%=User.VERIFY_WAIT%>"
									<c:if test="${user.verifyPhotoSign == 2}">selected</c:if>>认证中</option>
								<option value="3"
									<c:if test="${user.verifyPhotoSign == 3}">selected</c:if>>认证失败</option>
							</select>
						</li>
						<li>
							<label>认证通过车辆：</label>
							 <select name="isCar" class="select180">
								<option value="">----</option>
								<option value="1" <c:if test="${user.isCar=='1'}">selected</c:if>>有</option>
								<option value="0" <c:if test="${user.isCar=='0'}">selected</c:if>>无</option>
							</select>
						</li>
						<li>
							<label>预约时间：</label>
							<input name="appointTime" class="input180" value="<fmt:formatDate pattern="yyyy-MM-dd" value="${user.appointTime }"/>"
							onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
						</li>	
						<!--<li>
							<label>维护人：</label> 
							<input type="text" class="input180" name="maintainMan" value="${user.maintainMan}" />
						</li>	-->
						<li>
							<label>终端来源：</label>
							<select name="platId"
							class="select180">
								<option value="">----</option>
								<option value="<%=Constant.PLAT_PC%>"
									<c:if test="${user.platId == 1}">selected</c:if>>PC</option>
								<option value="<%=Constant.PLAT_ANDROID%>"
									<c:if test="${user.platId == 2}">selected</c:if>>Android</option>
								<option value="<%=Constant.PLAT_IOS%>"
									<c:if test="${user.platId == 3}">selected</c:if>>IOS</option>
								<option value="<%=Constant.PLAT_APAD%>"
									<c:if test="${user.platId == 4}">selected</c:if>>APAD</option>
								<option value="<%=Constant.PLAT_IPAD%>"
									<c:if test="${user.platId == 5}">selected</c:if>>IPAD</option>
								<option value="<%=Constant.PLAT_WEB%>"
									<c:if test="${user.platId == 6}">selected</c:if>>WEB</option>
								<option value="7"
									<c:if test="${user.platId == 7}">selected</c:if>>PHONE
									WEB</option>

							</select>
						</li>
						<!-- <li>
							<label>用户权限身份：</label>
							<select name="userSign" class="select180">
									<option value="">----</option>
									<c:forEach items="${rightSignList}" var="list">
									<option value="${list.value}"
										<c:if test="${user.userSign == list.value}">selected</c:if>>
										${list.name}
										</option>
									</c:forEach>
							</select> 
						</li> -->
						<li>
							<label>信息发布：</label>
							<select name="infoPublishFlag"
							class="select180">
								<option value="">----</option>
								<option value="<%=User.INFO_PUBLISH_DISABLE%>"
									<c:if test="${user.infoPublishFlag == 1}">selected</c:if>>禁止</option>
								<option value="<%=User.INFO_PUBLISH_ENABLE%>"
									<c:if test="${user.infoPublishFlag == 2}">selected</c:if>>可用</option>

							</select>
						</li>
						<li>
							<label>手机开通：</label>
							 <select name="phoneOpenFlag"
							class="select180">
								<option value="">----</option>
								<option value="<%=User.PHONE_OPEN_DISABLE%>"
									<c:if test="${user.phoneOpenFlag== 0}">selected</c:if>>否</option>
								<option value="<%=User.PHONE_OPEN_ENABLE%>"
									<c:if test="${user.phoneOpenFlag == 1}">selected</c:if>>是</option>
							</select>
						</li>
						<li>
							<label>信息上传：</label>
							<select name="infoUploadFlag"
							class="select180">
								<option value="">--</option>
								<option value="<%=User.INFO_UPLOAD_RESERVED%>"
									<c:if test="${user.infoUploadFlag == 0}">selected</c:if>>待定</option>
								<option value="<%=User.INFO_UPLOAD_DISABLE%>"
									<c:if test="${user.infoUploadFlag == 1}">selected</c:if>>禁止</option>
								<option value="<%=User.INFO_UPLOAD_ENABLE%>"
									<c:if test="${user.infoUploadFlag == 2}">selected</c:if>>
									可用</option>
							</select>
						</li>
						<li>
							<label>消息盒子：</label>
							<select name="qqBoxFlag" class="select180">
								<option value="">----</option>
								<option value="<%=User.QQ_BOX_FLAG_CLOSE%>"
									<c:if test="${user.qqBoxFlag== 0}">selected</c:if>>关闭</option>
								<option value="<%=User.QQ_BOX_FLAG_OPEN%>"
									<c:if test="${user.qqBoxFlag == 1}">selected</c:if>>打开</option>
							</select>
						</li>
						
						<li>
							<label>QQ号：</label>
							<input type="text" name="qq"
							class="input180" value="${user.qq }" />
						</li>
						
						
						<li>
							<label>诚信分数：</label>
							<input type="text" name="userPartBegin" class="pinfoTable" value="${user.userPartBegin }" /><span>-</span><input type="text" name="userPartEnd" class="pinfoTable" value="${user.userPartEnd }" />
						</li>

						<li><label>货主注册主体：</label> <select name="goodsTypeFirst" id="goods_type_first" class="select180"></select>
						</li>
						<li><label>货主注册身份：</label> <select name="goodsTypeSecond" id="goods_type_second" class="select180">
							<option value="0">------</option>
						</select>
						</li>
						<li><label>货主审核主体：</label> <select name="auditGoodsTypeFirst" id="audit_goods_type_first" class="select180"></select>
						</li>
						<li><label>货主审核身份：</label> <select name="auditGoodsTypeSecond" id="audit_goods_type_second" class="select180"></select>
						</li>
						
					</ul>
					<i class="showHide showBottom"></i>
					<a class="contBtn" href="javascript:">展开更多筛选项</a>
				</div>
			</form>

			<!--页面搜索条件按钮信息-->
			<div class="pageMessage">
				<c:if test="${! empty subMenus }">
					<div class="searchBtn">
        				<ul class="btnBox cf">
							<c:forEach items="${subMenus }" var="menu">
								<!-- 2链接；3submit;4reset;5button -->
								<c:if test="${menu.type==3 }">
									<li><input class="button sbWidth" type="submit"
										value="${menu.jurisdictionName }"></li>
								</c:if>
								<c:if test="${menu.type==4 }">
									<li><input class="button sbWidth" type="reset"
										value="${menu.jurisdictionName }"></li>
								</c:if>
								<c:if test="${menu.type==5 }">
									<li><input class="button" type="button"
										value="${menu.jurisdictionName }" onclick="${menu.url}"></li>
								</c:if>
							</c:forEach>

							<li><input type="button" class="button" value="缴费" onclick="optFn.showHideAdd('show','')"/></li>
						</ul>
					</div>
      				<div class="clearfix"></div>
				</c:if>
				<div class="messageRight fr">
					<span class="default_data">（备注：无查询条件默认查询两天数据）</span>
					查询到<em>${rowCount }</em>个用户，
					当前是第<span>${pageNo }</span>页，
					共<span>${maxPage }</span>页
				</div>
			</div>

			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
			<!--内容列表-->
			<div class="dataTable">			
				<table width="100%" border="0" class="tableBox usertableBox">
				<tr class="tableTh">
					<th>操作</th>
					<th>ID</th>
					<th>真实姓名(昵称)</th>
					<th>手机号</th>
					<th>注册日期(注册身份)(认证)</th>
					<th>认证通过车辆</th>
					<th>审核身份</th>
					<th>货主注册主体</th>
					<th>货主注册身份</th>
					<th>货主审核主体</th>
					<th>货主审核身份</th>
					<th>信用等级</th>
					<th>身份核实状态</th>
					<th>权益(付费日期)(付费方式)</th>
					<th>到期日期(天数)</th>
					<th>归属地</th>
					<th>用户来源(终端)</th>
					<th>(销售)(维护人)</th>
					<th>一级渠道</th>
					<th>沟通详情</th>
					<!-- <th>身份认证</th>
					<th>用户状态</th>
					<th>剩余天数</th> -->
				</tr>
				<c:if test="${!empty userList }">
					<c:forEach items="${userList }" var="user">
						<tr class="EvenRow">
							<td style="display: none;"><c:out value="${user.id }" /></td>
							<td class="positionTd">
								<a></a>
								<div class="positionDiv">
									<i class="tbtopcur"></i>
									<c:if test="${! empty subMenus}">
									<ul class="editorBox">
									<c:forEach items="${subMenus }" var="menu">
										<c:if test="${menu.type==2}">
											<li onclick="editOperationOnclickFun(this,'${menu.url}');"><a>${menu.jurisdictionName }</a></li>
										</c:if>
									
									</c:forEach>
										<!-- <li><a href="" class="actColor">查看用户详情</a></li>
										<li><a href="">基础资料管理</a></li>
										<li><a href="">车维缴费管理</a></li>
										<li><a href="">核心资料管理</a></li>
										<li><a href="">身份认证录入</a></li>
										<li><a href="">车辆认证录入</a></li>
										<li><a href="">用户日志管理</a></li> -->
									</ul>
									</c:if>
									
								</div>
							</td>
							<td><c:out value="${user.id }" /></td>
							<td><c:out value="${user.trueName }" />(<c:out value="${user.userName }" />)</td>
							<%-- <td>${user.recommenderTel}</td> --%>
							<!-- <td class="phoneHide"><c:out value="${user.cellPhone }" /></td> -->
							<td class="blue" onclick="javascript:phoneSoHfn($(this),'${user.cellPhone}','用户信息列表')">查看电话</td>
							<td><fmt:formatDate pattern="yyyy-MM-dd" value="${user.ctime}" type="both" dateStyle="default" />
								<%--(<c:if test="${!empty user.userClass&&!empty user.identityType}">
									<c:choose>
										<c:when test="${user.userClass==1 }">
											<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_1',${user.identityType})" var="sf" />${sf.name}
										</c:when>
										<c:when test="${user.userClass==2 }">
											<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_2',${user.identityType})" var="sf" />${sf.name}
										</c:when>
									</c:choose>
								</c:if>)--%>
								(<c:if test="${user.identifyName != null}">${user.identifyName}</c:if>)
								(<c:if test="${user.verifyPhotoSign == 0}">未认证</c:if><c:if
									test="${user.verifyPhotoSign == 1}">通过</c:if> <c:if
									test="${user.verifyPhotoSign == 2}">认证中</c:if> <c:if
									test="${user.verifyPhotoSign == 3}">认证失败</c:if>)
							</td>
							<td>
							<c:if test="${user.isCar == 0}">无</c:if>
							<c:if test="${user.isCar == 1}">有</c:if>
							</td>
							<td>
								<c:if test="${! empty  user.deliverTypeOne}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_one',${user.deliverTypeOne})"
										var="sf" />${sf.name}
								</c:if> / <c:if
									test="${! empty  user.deliverTypeOne&&!empty user.deliverType}">
									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_two',${user.deliverType})"
										var="sf" />${sf.name}
								</c:if> / <c:if
									test="${! empty  user.deliverTypeOne&&!empty user.deliverType&&!empty user.deliverTypeThree}">
								<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_three',${user.deliverTypeThree})"
										var="sf" />${sf.name}
							</c:if>
							</td>
							<td><c:out value="${user.goodsTypeFirstName }" /></td>
							<td><c:out value="${user.goodsTypeSecondName }" /></td>
							<td><c:out value="${user.auditGoodsTypeFirstName }" /></td>
							<td><c:out value="${user.auditGoodsTypeSecondName }" /></td>
							<td>${user.goodsrRankLevel}</td>
							<td>
								<c:if test="${user.auditStatus == 0}">未核实</c:if>
								<c:if test="${user.auditStatus == 1}">已核实</c:if>
							</td>
			<!-- 				<td><c:if
									test="${!empty user.userClass&&!empty user.identityType}">
									<c:choose>
										<c:when test="${user.userClass==1 }">
											<s:eval
												expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_1',${user.identityType})"
												var="sf" />
											<p>${sf.name}</p>
										</c:when>
										<c:when test="${user.userClass==2 }">
											<s:eval
												expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_identity_type_2',${user.identityType})"
												var="sf" />
											<p>${sf.name}</p>
										</c:when>
									</c:choose>
								</c:if>
							</td> -->
<!-- 							<td><c:if test="${user.verifyPhotoSign == 0}">未认证</c:if> <c:if
									test="${user.verifyPhotoSign == 1}">通过</c:if> <c:if
									test="${user.verifyPhotoSign == 2}">认证中</c:if> <c:if
									test="${user.verifyPhotoSign == 3}">认证失败</c:if></td> -->
							<td>
                                <!--<c:if test="${user.userType == 0}">试用</c:if>
							<c:if test="${user.userType == 1}">付费</c:if>
							<c:if test="${user.userType == 2}">未激活</c:if> -->
                                <c:if test="${user.userGroup == null}">未注册</c:if>
                                <c:if test="${! empty  user.userGroup}">
                                    <s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_group',${user.userGroup})"
                                            var="sf" />${sf.name}
                                </c:if>
							<c:choose>
							<c:when test="${!empty user.renewalDate } ">
							(<fmt:formatDate  pattern="yyyy-MM-dd"  value="${user.renewalDate}" type="both" dateStyle="default" />)(${user.bank })
							</c:when>
							<c:otherwise>
							<c:if test="${!empty user.payDate}">(<fmt:formatDate  pattern="yyyy-MM-dd"  value="${user.payDate}" type="both" dateStyle="default" />)(${user.bank }<c:if test="${! empty user.money}"><fmt:formatNumber type="number" value="${user.money/100} " maxFractionDigits="2" /></c:if>)</c:if>
							</c:otherwise>
							</c:choose>
							<%-- (<c:if test="${null!=user.payDate}"><fmt:formatDate  pattern="yyyy-MM-dd"  value="${user.payDate}" type="both" dateStyle="default" /></c:if>)
							(<c:out value="${user.bank }" />) --%>
							</td>
							<td><c:if test="${null!=user.endTime}"><fmt:formatDate pattern="yyyy-MM-dd"  value="${user.endTime}" type="date" dateStyle="default" /></c:if>(<c:out value="${user.serveDays }" />)
							</td>

							<td>
								<c:out value="${user.province }" />
								<c:out value="${user.city }" />
								<c:out value="${user.county }" />
							</td>
							<td>
								<c:choose>
									<c:when test="${empty user.source}">待定</c:when>
									<c:otherwise>${user.source}</c:otherwise>
								</c:choose>
								(<c:if test="${user.platId == 1}">PC</c:if><c:if
									test="${user.platId == 2}">Android</c:if><c:if
									test="${user.platId == 3}">IOS</c:if><c:if
									test="${user.platId == 4}">APad</c:if><c:if
									test="${user.platId == 5}">IPAD</c:if><c:if
									test="${user.platId == 6}">WEB</c:if><c:if
									test="${user.platId == 7}">PHONE WEB</c:if>)
							</td>
							<td>(<c:out value="${user.sales }" />)(${user.maintainMan})</td>

<%-- 							<td><c:if test="${!empty user.channel}"> --%>
<%-- 									<s:eval expression="T(com.tyt.util.TytSourceUtil).getSourceName('app_channel',${user.channel})" --%>
<%-- 										var="sf" />${sf.name} --%>
<%-- 		                            </c:if> --%>
<!-- 		                    </td>  -->
		                    <td>
		                    <c:forEach items="${channleList }" var="channel">
		                    <c:if test="${channel.value==user.channel}">
		                    	${channel.oneName}
		                    </c:if>
		                    </c:forEach>
		                    </td> 
							<td><c:out value="${user.note }" /></td>


							<!--操作-->
						<%-- 							<td class="positionTd">
								<a></a>
								<div class="positionDiv">
									<i class="tbtopcur"></i>
									<c:if test="${! empty subMenus}">
									<ul class="editorBox">
									<c:forEach items="${subMenus }" var="menu">
										<c:if test="${menu.type==2}">
											<li onclick="editOperationOnclickFun(this,'${menu.url}');"><a>${menu.jurisdictionName }</a></li>
										</c:if>
									</c:forEach>
										<!-- <li><a href="" class="actColor">查看用户详情</a></li>
										<li><a href="">基础资料管理</a></li>
										<li><a href="">车维缴费管理</a></li>
										<li><a href="">核心资料管理</a></li>
										<li><a href="">身份认证录入</a></li>
										<li><a href="">车辆认证录入</a></li>
										<li><a href="">用户日志管理</a></li> -->
									</ul>
									</c:if>
									
								</div>
							</td> --%>
								<!-- <td><c:if test="${! empty subMenus}">
											<select onclick="onSelectClick(this);">
												<option value="">操作</option>
												<c:forEach items="${subMenus }" var="menu">
													<c:if test="${menu.type==2}">
														<option value="${menu.url}">${menu.jurisdictionName }</option>
													</c:if>
												</c:forEach>
											</select>
										</c:if>
									</td> -->
								
						<!--<td><c:out value="${user.userName }" /></td>
							<td><c:out value="${user.qq }" /></td>
							<td><c:out value="${user.note }" /></td>
							<td><c:out value="${user.province }" />
								<c:out value="${user.city }" />
								<c:out value="${user.county }" /></td>
							<td><c:if test="${user.sex==0}">女</c:if> <c:if
									test="${user.sex==1}">男</c:if></td>
							<td>${user.payNumber}</td>
							<td><c:if test="${user.isCar == 0}">未完善</c:if> <c:if
									test="${user.isCar == 1}">已完善</c:if></td> -->
								<%-- <td><c:if test="${user.phoneOpenFlag == 0}">否</c:if> <c:if
										test="${user.phoneOpenFlag == 1}">是</c:if></td>
								<td><c:if test="${user.infoPublishFlag == 1}">发布禁止</c:if> <c:if
								test="${user.infoPublishFlag == 2}">发布可用</c:if></td>
								<td><c:if test="${user.qqBoxFlag == 0}">关闭</c:if> <c:if
										test="${user.qqBoxFlag == 1}">打开</c:if></td> --%>
								<!--<td><c:if test="${! empty  user.deliverTypeOne}">
									<s:eval
										expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_one',${user.deliverTypeOne})"
										var="sf" />
									<p>${sf.name}</p>
								</c:if>
							</td>
							<td><c:if
									test="${! empty  user.deliverTypeOne&&!empty user.deliverType}">
									<s:eval
										expression="T(com.tyt.util.TytSourceUtil).getSourceName('user_deliver_type_two',${user.deliverType})"
										var="sf" />
									<p>${sf.name}</p>
								</c:if>
							</td>
							<td><c:if test="${user.verifyPhotoSign == 0}">未认证</c:if> <c:if
									test="${user.verifyPhotoSign == 1}">通过</c:if> <c:if
									test="${user.verifyPhotoSign == 2}">认证中</c:if> <c:if
									test="${user.verifyPhotoSign == 3}">认证失败</c:if>
							</td>
							<td><c:if test="${user.platId == 1}">PC</c:if> <c:if
									test="${user.platId == 2}">Android</c:if> <c:if
									test="${user.platId == 3}">IOS</c:if> <c:if
									test="${user.platId == 4}">APad</c:if> <c:if
									test="${user.platId == 5}">IPAD</c:if> <c:if
									test="${user.platId == 6}">WEB</c:if> <c:if
									test="${user.platId == 7}">PHONE WEB</c:if>
							</td>
							<td><c:if test="${null!=user.payDate}">
									<fmt:formatDate value="${user.payDate}" type="both"
										dateStyle="default" />
								</c:if>
							</td>
							<td>${user.renewalYears}</td>
							<td><fmt:formatDate value="${user.endTime}" type="date" /></td>

							<td>${user.userPart}</td>
							<td><c:choose>
									<c:when test="${empty user.source}">待定</c:when>
									<c:otherwise>${user.source}</c:otherwise>
								</c:choose>
							</td>
							<td><c:choose>
									<c:when test="${empty user.sourceRemark}">待定</c:when>
									<c:otherwise>
										<c:if test="${!empty user.sourceRemark}">
											<s:eval
												expression="T(com.tyt.util.TytSourceUtil).getSourceName('source_咨询',${user.sourceRemark})"
												var="sf" />
											<p>${sf.name}</p>
										</c:if>
										<c:if test="${15==user.sourceRemark}">
											<p>电销</p>
										</c:if>
									</c:otherwise>
								</c:choose>
							</td>
							<td><c:choose>
									<c:when test="${empty user.paymentReason}">待定</c:when>
									<c:otherwise>
										<c:if test="${!empty user.paymentReason}">
											<s:eval
												expression="T(com.tyt.util.TytSourceUtil).getSourceName('payment_reason',${user.paymentReason})"
												var="sf" />
		                            ${sf.name}
		                            </c:if>
									</c:otherwise>
								</c:choose>
							</td>
							<td>${user.bank}</td>
							<td><c:if test="${! empty user.money}">
									<fmt:formatNumber type="number" value="${user.money/100} "
										maxFractionDigits="2" />
								</c:if>
							</td> -->
							<%-- <td><c:out value="${user.homePhone }" /></td> --%>
							<!-- <td><c:if test="${!empty user.channel}">
									<s:eval
										expression="T(com.tyt.util.TytSourceUtil).getSourceName('app_channel',${user.channel})"
										var="sf" />
		                            ${sf.name}
		                            </c:if>
		                    </td> -->
						</tr>
					</c:forEach>
				</c:if>

				</table>
			</div>
			<jsp:include page="/back/jsp/footer.jsp" flush="true" />
		</div>


		<!--透明层-->
		<div class="transparentbox" style="display: none;"></div>
		<!--可以变更-->
		<div class="outLook unvalidType" style="z-index:1004;display: none;">
			<div class="innerLook">
				<h2>用户账号变更</h2>
				<ul class="ideaLook">
					<li class="cf">
						<label style="width: 130px" class="fl" for=""><i class="mustRed">*</i>号码变更原因：</label>
						<input style="width: 200px" id="updateReason" class="fl" type="text" placeholder="请输入您变更的原因" maxlength="200"
 						onkeyup="this.value=this.value.replace(/^ +| +$/g,'')" />
					</li>
					<li class="cf">
						<label style="width: 130px" class="fl" for=""><i class="mustRed">*</i>手机号码更换为：</label>
						<input style="width: 200px" class="fl" id="updatePhone" type="text" placeholder="请输入您将要变更的号码"
						oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" maxlength="11">
					</li>
					<li class="cf">
							<label style="width: 130px" class="fl" for="">上传资料：</label>

						　　<label for="file" class=" fl update-button ">　
						　　　　<span>上传</span>
						　　</label> 　　
						<input type="file" id="file" style="display: none"  onchange="updateFile(event)">　
						<div></div>

						<%--<input style="width: 200px" class="fl" id="updatePhone" type="text" placeholder="请输入您将要变更的号码"--%>
							   <%--oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" maxlength="11">--%>
					</li>
				</ul>
				<p class="unvalidBlock changeUnvalid" style="margin-left: 160px;" id="errorMsgLog">该号码已经注册特运通</p>
				<div class="ideabtnlabel cf" style="width: 200px;">
				<button class="fl" onclick="saveMobile();">确定</button>
					<button class="editClose fr">取消</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>

		<!--不可变更-->
		<div class="outLook alert changeCheckBox" style="z-index:1004;display: none;">
			<div class="innerLook">
				<h2>提示</h2>
				<p class="alertContent changeTit"></p>
				<div class="ideabtnlabel changeAlertBox cf" style="width: 200px;display: none;">
					<button class="fl changeTrue">确定</button>
					<!-- <button class="fr changeFalse">取消</button> -->
				</div>
				<div class="ideabtnlabel checkAlertBox cf" style="width: 200px;display: none;">
					<button class="fl chackJumpUrl" style="width: auto;padding: 0 8px;" onclick="tytTool.openWinAuto('back/model/html/user/accountChange.html?cellPhone=${cellPhoneLog}','checkList',1100,600)">查看变更记录</button>
					<button class="fr chackFalseClose">关闭</button>
				</div>
				<div class="ideabtnlabel successAlertBox cf" style="width: 200px;display: none;">
					<button class="fr chackFalseClose" >我知道了</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>


		<!--分页-->
		<!-- <div class="pageBox">
			<ul class="pageOutbox cf">
				<li><input class="firstPage" type="button" value="首页"></li>
				<li><input class="prevPage" type="button" value="上一页"></li>
				<li>
					<ul class="cf">
						<li><a class="activePage" href="">1</a></li>
						<li><a href="">2</a></li>
						<li><a href="">3</a></li>
						<li><a class="dontClick">...</a></li>
						<li><a href="">99</a></li>
					</ul>
				</li>
				<li><input class="nextPage" type="button" value="下一页"></li>
			</ul>
		</div> -->


		<!-- 遮罩层div -->
		<div class="outBgColor" style="display: none;"></div>
		<!-- 缴费弹框 -->
		<div class="alert alert-danger fade in alert-height" style="display: none;" id="payModal">
			<div class="shadow-bg"></div>
			<div class="innerLook">
				<h2>购买商品缴费</h2>
				<div class="addWhiteUser cf">
					<input type="text" hidden id="id" data-type="" tname="id">
					<ul>
						<li>
							<label for="">手机号：</label>
							<input type="text" placeholder="请输入手机号" maxlength="30" id="cellPhone" tname="cellPhone" onBlur="optFn.getUserMsg()"/>
						</li>
						<li id="userIdBox">
							<label for="">用户ID：</label>
							<input type="text" placeholder="" maxlength="30" id="userId" tname="userId" style="border:none;" readonly/>
						</li>
						<li id="userNameBox">
							<label for="">用户名：</label>
							<input type="text" placeholder="" maxlength="30" id="userName" tname="userName" style="border:none;" readonly/>
						</li>
						<li>
							<label for="">商品名称：</label>
							<select tname="goodsName" id="goodsName" onchange="optFn.changeGoodsFun(this)"></select>
						</li>
						<li id="priceBox">
							<label for="">商品价格：</label>
							<input type="text" placeholder="请输入商品价格" maxlength="30" id="price" tname="price" style="border:none;" readonly/>
						</li>
						<li id="remarkBox">
							<label for="" class="goods-sm">商品说明：</label>
							<textarea  class="note" cols="27" rows="3" maxlength="500" tname="remark" id="remark" placeholder="" style="border:none;" readonly></textarea>
						</li>
						<li>
							<label for="" id="">支付方式：</label>
							<input class="pay-method-radio" type="radio" tname="payChannelId" id="payChannelId" checked>
							<img src="" class="pay-method-img" alt="*"/>
						</li>
					</ul>
					<div class="makeSureBtn" style="height:32px;margin: 0 auto;text-align: center;">
						<input class="button" type="button" onclick="optFn.getQRImg()" value="提交">
						<input class="button" type="button" onclick="optFn.resetValue()" value="重置">
					</div>
				</div>
			</div>
			<i class="closelookBtn" onclick="optFn.showHideAdd('hide','payModal')">×</i>
		</div>
		<!-- 提交支付弹框 -->
		<div class="alert alert-danger fade in alert-height" style="display: none;" id="submitPayModal">
			<div class="innerLook">
				<h2>商品缴费</h2>
				<div class="addWhiteUser cf">
					<ul>
						<li>
							<label for="">手机号：<span class="cell-phone"></span></label>
						</li>
						<li>
							<label for="">商品名称：<span class="goods-name"></span></label>
						</li>
						<li>
							<label for="">缴费金额：<span class="goods-price"></span>元</label>
						</li>
					</ul>
                	<div class="pay-QR-img" id="qrcode"></div>
					<!-- <img class="pay-QR-img" src="" alt="*"> -->
					<div class="pay-msg"></div>
				</div>
				<div class="makeSureBtn closePayModalBtn" style="height:32px;margin: 10px auto 0;text-align: center;display: none;">
					<input class="button" type="button" onclick="optFn.closePayModal()" value="确定">
				</div>
			</div>
			<i class="closelookBtn" onclick="optFn.showHideAdd('hide','submitPayModal')">×</i>
		</div>






	</div>
	<%@include file="/back/html/upload_renzheng_list.html"%>
</body>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/jquery.cookie.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/jquery.qrcode.min.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>
<script>
	add_cookie();

	$(".unvalidType .closelookBtn,.editClose").click(function(){
 		$(".unvalidType,.transparentbox").hide();
	})
	$(".changeTrue,.chackFalseClose,.closelookBtn").click(function(){
		$(this).parents('.changeCheckBox').hide();
		$(".transparentbox").hide()
	})
	$("#updateReason,#updatePhone").focus(function(){ 
		$("#errorMsgLog").html("");
	})

	function outChangeTit(str,msg){
 		if(str=='check') { 
			$(".changeCheckBox").css({'height':'170px','margin-top':'-80px'})
			$(".changeTit").html('该账号已经发生更改')
			$(".changeAlertBox").hide();
			$(".checkAlertBox").show();
		} else if(str=='change') {
			$(".changeCheckBox").css({ 'height': '190px', 'margin-top': '-95px' })
 			$(".changeTit").html('该账号不允许变更，理由：该账号为黑名单用户，变更请移除黑名单状态');
			$(".changeAlertBox").css('width','80px').show();
			$(".checkAlertBox").hide();
 		} else if(str == 'success'){
			$(".changeCheckBox").css({ 'height': '170px', 'margin-top': '-80px' })
			$(".changeTit").html('账号变更成功!')
			$(".changeAlertBox,.checkAlertBox,.unvalidType").hide();
			$(".successAlertBox").css('width','80px').show();
		} else if(str == 'noEdit'){
			$(".changeCheckBox").css({ 'height': '170px', 'margin-top': '-80px' })
			$(".changeTit").html(msg)
			$(".changeAlertBox,.checkAlertBox,.unvalidType").hide();
			$(".successAlertBox").css('width','80px').show();
		}
 		$(".changeCheckBox,.transparentbox").show()
	}
	
	
	var phoneUpdateStatus=${phoneUpdateStatus};
	//var cellPhoneLog='${cellPhoneLog}';
	//1弹窗口提示变更手机号
	if(phoneUpdateStatus==1){
		outChangeTit("check");
	}
	
	
	
var oldPhone="";
var updateAccountUserId="";

	function fn_updateMobileLogCheck(userId){
		
		updateAccountUserId=userId;
		$.ajax({
			async:false, 
			type:"post",
			url:"accountUpdateLog/isUpdateAccount",
			data:'userId='+userId,
			dataType:"json",
			success:function(result){
				var code=result.code;
				var msg = result.msg;
				oldPhone=result.data;
				if(code==200){
					$("#updateReason").val('');
					$("#updatePhone").val('');
					$("#errorMsgLog").html("");
					$(".unvalidType,.transparentbox").show();
				}else  if(code==300){
					outChangeTit("change");
				}else  if(code==301){
					outChangeTit("noEdit",msg);
				}else{
					
				}
			}
		});
	}
	// 上传文件后的路径
	var updateFileUrl = ''
	var isUpdateFlag = false

	function saveMobile(){
		console.log('click')
		var updateReason=$("#updateReason").val();
		var updatePhone=$("#updatePhone").val();
	//	alert(updateReason);
	//	alert(updatePhone);
	//	alert(oldPhone);
	//	alert(updateAccountUserId);
		if(updateReason==''){
			$("#errorMsgLog").html("变更原因不能为空");
			return;
		}
		if(updatePhone==''){
			$("#errorMsgLog").html("更换的手机号不能为空");
			return;
		}
		if(updatePhone.length<11){
			$("#errorMsgLog").html("变更号码输入格式有误");
			return;
		}
		
		
		if(updatePhone==oldPhone){
			$("#errorMsgLog").html("更换的手机号不能与原手机号相同");
			return;
		}
		if(isUpdateFlag) {
			$("#errorMsgLog").html("上传中，请稍后");
			return false
		}
		
		$.ajax({
			async:false, 
			type:"post",
			url:"accountUpdateLog/save",
			data:'userId='+updateAccountUserId+'&reason='+updateReason+'&newPhone='+updatePhone+'&cellPhone='+oldPhone+'&certificateUrl='+updateFileUrl,
			dataType:"json",
			success:function(result){
				var code=result.code;
				if(code==200){
					outChangeTit("success");
					$(".unvalidType").hide();
					$("#updateReason").val('');
					$("#updatePhone").val('');
					$("#errorMsgLog").html("");
					updateFileUrl = ''
					
				}else  if(code==300){
					$("#errorMsgLog").html("该号码已经注册特运通");

				}else{

				}
			}
		});
	}
	
	 function checkMobiles(theForm) {
		   //  return /^((13|15|18|14|17)+\d{9})$/.test(theForm);
		     return /^((13|14|15|16|17|18|19)+\d{9})$/.test(theForm);
		 }

//	上传文件

	function updateFile(e) {
            let formData = new FormData()
            let file = e.target.files[0]
			console.log(file)
            let max_size = 1024 * 1024 * 10 // 最大限制10M 
            if (file.size <= max_size) { //文件必须小于100M
                if (/.(PDF|pdf|JPG|jpg|PNG|png|JPEG|jpeg)$/.test(file.name)) {
                    formData.append("file", file); //文件上传处理
                    // formData.append('ssoInfo',tytTool.getCookie('ssoInfo'))
                } else {
					$("#errorMsgLog").html("文件格式错误");
                    // tytTool.toastShow('文件格式错误', 2500);
                    return false
                }

            } else {
				alert('上传文件不能大于10M')
                // tytTool.toastShow('上传文件不能大于10M', 2500);
                return false
            }
			$("#errorMsgLog").html("");
			isUpdateFlag = true
            // 开始上传
            $.ajax({
                type: "POST",
                url: `accountUpdateLog/upload/certificate`,
                data: formData,
                contentType: false,
                /**
                * 必须false才会避开jQuery对 formdata 的默认处理
                * XMLHttpRequest会对 formdata 进行正确的处理
                */
                processData: false,
                async : false, 
                dataType:"json",
                success: function (data) {
					isUpdateFlag = false
                    if(data.code == 200){
						updateFileUrl = data.data
						$("#errorMsgLog").html('上传成功');
                    }else{
						$("#errorMsgLog").html(data.msg);
                        
                    }
                    
                },
                error: function (xhr, status) {
					isUpdateFlag = false
                    console.log(xhr)
                }
            });

        }
</script>
</html>

<script src="back/model/js/jqpaginator.min.js"></script>
<script type="text/javascript" src="back/model/js/userBuyGoods/userBuyGoodsList.js"></script>
<script type="text/javascript" src="back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="back/model/js/hidePhoneCommon.js"></script>



