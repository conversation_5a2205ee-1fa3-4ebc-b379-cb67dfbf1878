<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>新增品宣图</title>
<link href="<%=request.getContextPath()%>/back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/infoTake.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/jquery-1.7.2.min.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span>首页品宣图管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">新增</span>
		</h1>
    <div class="main-content">
      <form action="saveAdvertisePicture" name="saveAdvertisePictureForm" method="post" enctype="multipart/form-data">
        <!--系统设置-->
      <div class="systemParameter">
        <ul class="transportNews cf">
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>标题：</label>
	          <input id="advertiseTit" class="info_input" type="text" name="title" maxlength="20" placeholder="不超过20个字符">
            <span class="lineError"></span>
	        </li>
          <li class="cf">
          	<label for=""><i class="xing">*</i>品宣图文件：</label>
          	<div class="photoWare nomarginL fl">
              <h4><span>预览</span></h4>
              <div class="imgBox"><img class="" src="<%=request.getContextPath()%>/back/model/images/default.jpg" alt="品宣图文件"></div>
              <span>文件名</span>
              <div class="uploadImgbox">
	 						<input id="fileField" class="fileBtn" name="advertisePicturePic" accept="image/bmp,image/jpeg,image/png" type="file" onchange="fileInfo(this,$(this))" data-redtit="3">
                <button class="rule_true" type="button">浏览</button>
              </div>
            </div>
            <span class="lineError"></span>
          </li>
          <li>
          	<label></label>
        		<span>图片尺寸要求： 750*480px，文件大小不超过2M<br/>文件格式：JPG、PNG、BMP</span>
          </li>
	        <li class="cf">
	          <label class="fl">图片链接：</label>
	          <input id="advertiseUrl" class="info_input_no" type="text" name="openUrl" placeholder="" maxlength="500">
            <span class="lineError"></span>
	        </li>
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>显示顺序：</label>
	          <input id="advertiseNum" class="info_input" type="text" maxlength="4" name="sort" placeholder="填写轮播图编号，用于控制轮播图显示顺序">
            <span class="lineError"></span>
	        </li>
        </ul>
        <p class="errorBlock errorBlockNomal" id=""></p>
        <div class="divButton gengButton">
        	<input type="button" class="button updateBtn" onclick="submitForm();" value="保存">
        </div>
    </div>
    </form>
  </div>
	</div>
</body>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/checkType.js"></script>
<script>
	function submitForm() {
		if (validate()) {
			document.forms['saveAdvertisePictureForm'].submit();
		}
	}

</script>
</html>