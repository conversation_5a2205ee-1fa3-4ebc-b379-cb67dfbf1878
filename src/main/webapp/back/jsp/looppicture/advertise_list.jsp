<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>首页品宣图管理</title>
<link href="<%=request.getContextPath()%>/back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableModel.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
<link href="<%=request.getContextPath()%>/back/model/css/validate.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/jurisdiction/jsp/js/common.js"></script>
</head>
<body>
	<input type="hidden" value="${pageNo }" id="pageNoId">
	<div class="contentBox" style="width: auto;">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">首页品宣图管理</span>
		</h1>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="" type="button" value="新增" onclick="openWinAuto('addAdvertisePicture','home_add',1100,600)"></li>
				</ul>
			</div>
			<div class="clearfix"></div>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th width="80">显示顺序</th>
					<th width="120">更新时间</th>
					<th width="150">品宣图缩略图</th>
					<th>标题</th>
					<th>图片链接</th>
					<th>启用状态</th>
					<th width="180">操作</th>
				</tr>
				<c:forEach items="${hpLoopPicturesList }" var="hpLoopPicture">
				<tr>
					<td>${hpLoopPicture.sort }</td>
					<td>
						<fmt:formatDate pattern="yyyy-MM-dd HH:mm" value="${hpLoopPicture.utime }" />
					</td>
					<td>
						<img class="showBigPng" src="${tytServerPictureUrl}${hpLoopPicture.pictureUrl }" alt="">
					</td>
					<td>${hpLoopPicture.title }</td>
					<td>
						<a class="tip" href="${hpLoopPicture.openUrl }" target="_blank"  tip="">${hpLoopPicture.openUrl }</a>
					</td>
					<td>
						<c:if test="${hpLoopPicture.openClose == 1 }">
						<a style="color: #690;">已启用</a>
						</c:if>
						<c:if test="${hpLoopPicture.openClose == 0 }">
						<a style="color: #333">已停用</a>
						</c:if>
					</td>
					<td>
						<c:if test="${hpLoopPicture.openClose == 1 }">
						<a class="info_detail ver_block endTask" idValue="${hpLoopPicture.id }" statusValue="0">停用</a>
						</c:if>
						<c:if test="${hpLoopPicture.openClose == 0 }">
						<a class="info_detail ver_block startTask" idValue="${hpLoopPicture.id }" statusValue="1">启用</a>
						</c:if>
						<span class="ver_block ver_span">|</span>
						<a class="info_detail ver_block" onclick="openWinAuto('findAdvertise?id=${hpLoopPicture.id }','above_block',1100,600)">编辑</a>       
						<span class="ver_block ver_span">|</span>
						<a class="info_detail ver_block removeBlock" idValue="${hpLoopPicture.id }" >删除</a>       
          			</td>
				</tr>
				</c:forEach>
		    </table>
		</div>
		<div class="joinbgOutLook" style="display: none;"></div>
		<div class="outLook infopopType" style="display: none;">
			<div class="innerLook">
				<h2>提示</h2>
		    <p class="alertContent">确认删除此品宣图信息？</p>
				<!-- <p class="errorMsg blockerrorMsg" style=""></p> -->
				<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
					<button class="rule_true" onclick="doDelete();">确定</button>
					<button id="" class="close_block fr">取消</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>
		<div class="outLook startStoppbtn" style="display: none;">
			<div class="innerLook">
				<h2>提示</h2>
		    <p class="alertContent"></p>
				<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
					<button class="rule_true" onclick="changeStatus();">确定</button>
					<button id="" class="close_block fr">取消</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>


	</div>
</body>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/easy_validator.pack.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/back/model/js/colResizable-1.6.min.js"></script>
<script>
	window.onload = function(){
		$(".dataTable .tip").each(function(index, el) {
			$(this).attr('tip',$(this).html())
		});
	}
	var curId, curStatus;
	function doDelete() {
		var pageNo = parseInt($('#pageNoId').val());
		if (pageNo == 1) {
			alert('请至少保留一条数据');
		} else {
			$.post("deleteLoopPicture", {
				id : curId
			}, function(result) {
				// alert(result.msg);
				if (result.code == 200) {
					// 刷新当前页面
					window.location.reload();
				}
			});
		}
	} 

	function changeStatus() {
		$.post("changeStatus", {
			id : curId,
			status : curStatus,
			type: 1
		}, function(result) {
			if (result && typeof result == 'string' && result.indexOf("登录") != -1) {
				parent.location.href = "<%=request.getContextPath()%>/admin/userlogin";
			} else if (result.code == 200) {
				// 刷新当前页面
				window.location.reload();
			} else if (result.code == 300) {
				alert('最多同时显示10张品宣图');
			} 
		});
	}
	$(".dataTable .showBigPng").click(function(event) {
		var _src = $(this).attr('src');
		openWinAuto(_src,'showImg',1100,600)
	});
	
	/*
		=====================
	*/
	$(".tableBox").colResizable({
		liveDrag: true,
		draggingClass: "dragging",
		resizeMode: 'flex',
		minWidth: 80
	});

	$(".removeBlock").click(function(event) {
		curId = $(this).attr('idValue');
		$(".infopopType,.joinbgOutLook").show();
	});
	$(".closelookBtn,.close_block").click(function(event) {
		$(".infopopType,.joinbgOutLook").hide();	
	});
	function setIdAndStatus(curA) {
		curId = curA.attr('idValue');
		curStatus = curA.attr('statusValue');
	}
	$(".startTask").click(function(event) {
		setIdAndStatus($(this));
		$(".startStoppbtn .alertContent").html('确认要启用此品宣图信息吗？<br/>启用后此品宣图可呈现在APP页面上')
		$(".startStoppbtn,.joinbgOutLook").show();
	});
	$(".endTask").click(function(event) {
		setIdAndStatus($(this));
		$(".startStoppbtn .alertContent").html('确认要停用此品宣图信息吗？<br/>停用后此品宣图不再呈现在APP页面上')
		$(".startStoppbtn,.joinbgOutLook").show();
	});
	$(".closelookBtn,.close_block").click(function(event) {
		$(".startStoppbtn .alertContent").html('')
		$(".startStoppbtn,.joinbgOutLook").hide();	
	});
</script>
</html>