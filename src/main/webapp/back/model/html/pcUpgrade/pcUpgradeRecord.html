<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title id="pageName">升级记录列表</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
</head>
<body class="theme-blue">
<!-- 主要内容开始 -->
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>PC升级管理</span><span>&nbsp;&gt;&nbsp;</span><span>升级任务列表</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">升级记录列表</span>
    </h1>
    <div class="main-content">
        <!--搜索条件-->
        <div class="searchBox" id="searchCondition">
            <ul class="searchUl searchUlNone cf"></ul>
        </div>
        <!--页面信息-->
        <div class="pageMessage">
            <div class="searchBtn">
                <ul class="btnBox cf">
                    <li>
                        <input type="button" onclick="recordO.loadList('removeSession')" class="button" value="查询">
                    </li>
                    <li>
                        <input class="" id="resetForm" type="button" value="重置" onclick="tytTool.formReset($('.searchUl'),recordO.loadList)">
                    </li>
                </ul>
            </div>
            <div class="clearfix"></div>
        </div>
        <div id="updataStatusBox" class="cf">
            <div class="messageRight pageFun fr">查询到<em tname="total"></em>条信息，当前是第<span tname="current"></span>页，共<span tname="maxPage"></span>页</div>
        </div>

        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
        <div class="dataTable">
            <table border="0" class="tableBox" id="recordTable"></table>
        </div>
    </div>
    <!--JQ分页-->
    <div id="pageNumberBigBox" class="cf">
        <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>
</div>

</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/pcUpgrade/pcUpgradeCommon.js"></script>
<script src="../../js/pcUpgrade/pcUpgradeRecord.js"></script>
</html>