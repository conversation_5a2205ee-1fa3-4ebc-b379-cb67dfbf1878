<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>货源沟通记录Tab页</title>
	<link rel="stylesheet" href="../../css/tableBase.css">
	<link rel="stylesheet" href="../../css/rourceRecord.css">
</head>
<body>
	<div class="rourceRecordBox">
		<button class="rourceRecord">+添加电话沟通记录</button>
		<div class="rourceListOut">
			<ul class="shrinkBox">
				<li>
					<div class="showShrink">
						<div class="userDetailBox cf">
							<div class="userDetailLeft  fl" data-open="0">
								<div class="firstShow  cf">
									<strong>18287197828</strong><strong>&nbsp;/&nbsp;</strong>
									<a href="#">何老师</a>
									<em style="margin-right: 10px;"><i>个人车主</i>&nbsp;/&nbsp;注册<i>1</i>年</em>
									<span>已付信息费<i>500</i>元</span>
									<em>(预留手机15855888855)</em>
								</div>
								<div class="loadClickShow">
									<a href="#"><span>津QW8969</span> / 自重<span>16</span>吨 / 载重<span>30</span>吨 / 板长<span>17.5</span>米 / <span>现处北京顺义区</span></a>
									<em>/ 距货源38公里 </em>
									<strong>* 车+货已达48吨，有超载风险</strong>	
								</div>
							</div>
							<div class="userDetailRight fr">
								<a class="fl" href="#" onclick="confurseOut()">拒绝</a>
								<button class="fl">达成意向</button>
								<div class="detailRightBox fl">
									<span>详情</span><i></i>
								</div>
							</div>
						</div>
					</div>
					<ul class="linkupListBox" style="display: none;">
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="changeStatus fl">
									<i class="notShowIcon"></i>
									<div class="innerChangeStatus notClick">
										<!-- <span class="already">未完成</span> -->
										<span class="upgrade">升级处理</span>
										<!-- <span class="finish" onclick="upgradeFun()">已完成</span> -->
									</div>
									
								</div>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="changeStatus fl">
									<i></i>
									<div class="innerChangeStatus canClick">
										<span class="already">未完成</span>
										<span class="upgrade" onclick="upgradeFun()">升级处理</span>
										<span class="finish" onclick="upgradeFun()">已完成</span>
									</div>
									
								</div>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="innerChart cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="innerChart remarksBox cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
					</ul>
				</li>
				<li>
					<div class="showShrink">
						<div class="userDetailBox cf">
							<div class="userDetailLeft  fl" data-open="0">
								<div class="firstShow  cf">
									<strong>18287197828</strong><strong>&nbsp;/&nbsp;</strong>
									<a href="#">何老师</a>
									<em style="margin-right: 10px;"><i>个人车主</i>&nbsp;/&nbsp;注册<i>1</i>年</em>
									<em class="noPay">未付信息费</em>
								</div>
								<div class="loadClickShow">
									<a href="#"><span>津QW8969</span> / 自重<span>16</span>吨 / 载重<span>30</span>吨 / 板长<span>17.5</span>米 / <span>现处北京顺义区</span></a>
									<em>/ 距货源38公里 </em>
								</div>
							</div>
							<div class="userDetailRight fr">
								<a class="fl" href="#">代客户创建订单</a>
								<button class="fl">达成意向</button>
								<div class="detailRightBox fl">
									<span>详情</span><i></i>
								</div>
							</div>
						</div>
					</div>
					<ul class="linkupListBox" style="display: none;">
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="changeStatus fl">
									<i class="notShowIcon"></i>
									<div class="innerChangeStatus notClick">
										<!-- <span class="already">未完成</span> -->
										<span class="upgrade">升级处理</span>
										<!-- <span class="finish" onclick="upgradeFun()">已完成</span> -->
									</div>
									
								</div>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="changeStatus fl">
									<i></i>
									<div class="innerChangeStatus canClick">
										<span class="already">未完成</span>
										<span class="upgrade" onclick="upgradeFun()">升级处理</span>
										<span class="finish" onclick="upgradeFun()">已完成</span>
									</div>
									
								</div>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="innerChart cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="innerChart remarksBox cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
					</ul>
				</li>
				<li>
					<div class="showShrink">
						<div class="userDetailBox cf">
							<div class="userDetailLeft  fl" data-open="0">
								<div class="firstShow  cf">
									<strong>18287197828</strong><strong>&nbsp;/&nbsp;</strong>
									<a href="#">何老师</a>
									<em style="margin-right: 10px;"><i>个人车主</i>&nbsp;/&nbsp;注册<i>1</i>年</em>
									<span>已付信息费<i>500</i>元</span>
									<em>(预留手机15855888855)</em>
								</div>
								<div class="loadClickShow">
									<a href="#"><span>津QW8969</span> / 自重<span>16</span>吨 / 载重<span>30</span>吨 / 板长<span>17.5</span>米</a>
									<select name="" id="">
										<option value="">京 W13245</option>
										<option value="">京 Q13245</option>
										<option value="">京 E13245</option>
									</select>
								</div>
							</div>
							<div class="userDetailRight fr">
								<a class="fl" href="#" onclick="confurseOut()">拒绝</a>
								<button class="fl" >达成意向</button>
								<div class="detailRightBox fl">
									<span>详情</span><i></i>
								</div>
							</div>
						</div>
					</div>
					<ul class="linkupListBox" style="display: none;">
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="changeStatus fl">
									<i class="notShowIcon"></i>
									<div class="innerChangeStatus notClick">
										<!-- <span class="already">未完成</span> -->
										<span class="upgrade">升级处理</span>
										<!-- <span class="finish" onclick="upgradeFun()">已完成</span> -->
									</div>
									
								</div>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
<!-- 								<div class="changeStatus fl">
									<i></i>
									<div class="innerChangeStatus canClick">
										<span class="already">未完成</span>
										<span class="upgrade" onclick="upgradeFun()">升级处理</span>
										<span class="finish" onclick="upgradeFun()">已完成</span>
									</div>
									
								</div> -->
								<select name="" id=""  class="changeStatus fl" disabled="disabled">
									<option value="">未完成</option>
									<option value="">升级处理</option>
									<option value="">已完成</option>
								</select>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="innerChart cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="innerChart remarksBox cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
					</ul>
				</li>
				<li>
					<div class="showShrink">
						<div class="userDetailBox cf">
							<div class="userDetailLeft  fl" data-open="0">
								<div class="firstShow  cf">
									<strong>18287197828</strong><strong>&nbsp;/&nbsp;</strong>
									<a href="#">何老师</a>
									<em style="margin-right: 10px;"><i>个人车主</i>&nbsp;/&nbsp;注册<i>1</i>年</em>
									<span>已付信息费<i>500</i>元</span>
									<em>(预留手机15855888855)</em>
								</div>
								<div class="loadClickShow">
									<em class="noCarpass">尚无通过认证车辆</em>
									<a href="javascript:void(0)"class="noCarPassPink youareluan">，请引导客户将车辆认证通过后，才能拉此货源</a>
								</div>
							</div>
							<div class="userDetailRight fr">
								<a class="fl" href="#" onclick="confurseOut()">拒绝</a>
								<button class="fl">达成意向</button>
								<div class="detailRightBox fl">
									<span>详情</span><i></i>
								</div>
							</div>
						</div>
					</div>
					<ul class="linkupListBox" style="display: none;">
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="changeStatus fl">
									<i class="notShowIcon"></i>
									<div class="innerChangeStatus notClick">
										<!-- <span class="already">未完成</span> -->
										<span class="upgrade">升级处理</span>
										<!-- <span class="finish" onclick="upgradeFun()">已完成</span> -->
									</div>
									
								</div>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
						<li>
							<div class="callInOutBox cf">
								<div class="customerService fl">
									<i></i><span>客服王倩</span>
								</div>
								<div class="callIn fl" style="display: none;">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="callOut fl">
									<i></i><span>2017-11-09 09:07</span>
								</div>
								<div class="changeStatus fl">
									<i></i>
									<div class="innerChangeStatus canClick">
										<span class="already">未完成</span>
										<span class="upgrade" onclick="upgradeFun()">升级处理</span>
										<span class="finish" onclick="upgradeFun()">已完成</span>
									</div>
									
								</div>
							</div>
							<p class="chartContent">
								沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通
							</p>
							<div class="chartBox">
								<div class="innerChart cf">
									<label class="fl" for="">沟通结果：</label>
									<span class="fl">沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通沟通</span>
								</div>
								<div class="innerChart remarksBox cf">
									<label class="fl" for="">备注说明：</label>
									<span class="fl">备注备注备注备注备注备注备注备注备注备注备注备注</span>
								</div>
							</div>
						</li> 
					</ul>
				</li>
			</ul>
			<div class="noHaveRecord">当前用户针对此货源暂无沟通记录</div>
		</div>
	</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script>
	$(".innerChangeStatus").click(function(event) {
		$(this).removeClass('canClick');
	});

	$(document).click(function(event) {
	    $(".innerChangeStatus").addClass('canClick')
	});
	$('body', window.parent.document).click(function(event) {
	    $(".innerChangeStatus").addClass('canClick')
	});
	$(".innerChangeStatus").click(function(event) {
	    event.stopPropagation();
	});
	$(".detailRightBox").click(function(event) { 
		var thisParentLeft  = $(this).parents('.userDetailRight').siblings('.userDetailLeft');
	 var thisParent  = $(this).parents('.showShrink');
		var thisParentSib  = $(this).parents('.showShrink').siblings('.linkupListBox');

		if(thisParentLeft.attr('data-open')==0){
			thisParentLeft.attr('data-open',1)
			thisParentSib.show();
			thisParentLeft.addClass('twoLineStyle');
		}else{
			thisParentSib.hide();
			thisParentLeft.attr('data-open',0)
		}

	});

// 拒绝
	function confurseOut(){
		$('.innerRefuse', window.parent.document).show();
		$('.outBgColor', window.parent.document).show();
	}
	function upgradeFun(){
		$('.cursontionBox', window.parent.document).show();
		$('.outBgColor', window.parent.document).show();
	}
</script>
</html>