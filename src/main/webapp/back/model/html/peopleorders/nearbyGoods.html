<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>附近运力</title>
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tyt_common.css">
    <link rel="stylesheet" href="../../css/goodsCommon.css">
</head>
<body>
    <div id="nearbyBox">
        <div class="selectBox cf">
            <div class="carInBack fl" style="display: none;">
                <div class="allCheckedBox fl">
                    <em class="checkedBox checkedDefault"><input id="allChecked" type="checkbox" onclick="no.allCheck($(this))"></em><label for="allChecked">全选</label>
                </div>
                <div class="nearbycarNumber fl">附近共<em>0</em>辆车</div>
            </div>
            <div class="fr">
                <div class="carsSort fl">
                    <label class="font600 fl" for="">车辆排序：</label>
                    <div class="selectPlance fl">
                        <!-- <span>车辆排序：</span> -->
                        <select name="" id="" tname="car_order">
                            <option value="1">智能推荐排序</option>
                            <option value="2">归属地与目的地匹配优先</option>
                            <option value="3">归属地与出发地匹配优先</option>
                            <option value="4">出发地距离优先</option>
                            <option value="5">专线匹配优先</option>
                            <option value="6">常跑路线匹配优先</option>
                        </select>
                    </div>
                </div>
                <div class="searchBox fl">
                    <label class="font600 fl" for="">搜索范围：</label>
                    <div class="selectPlance fl">
                        <span>出发地及周边</span>
                        <select name="" id="" tname="start_round">
                            <option value="10">10公里</option>
                            <option value="30">30公里</option>
                            <option value="50" selected>50公里</option>
                            <option value="100">100公里</option>
                            <option value="150">150公里</option>
                            <option value="200">200公里</option>
                            <option value="300">300公里</option>
                            <option value="500">500公里</option>
                        </select>
                    </div>
                    <div class="selectPlance fl">
                        <span>目的地及周边</span>
                        <select name="" id="" tname="dest_round">
                            <option value="10">10公里</option>
                            <option value="30">30公里</option>
                            <option value="50" selected>50公里</option>
                            <option value="100">100公里</option>
                            <option value="150">150公里</option>
                            <option value="200">200公里</option>
                            <option value="300">300公里</option>
                            <option value="500">500公里</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="goodsListBox">
            <ul class="innerListBox" id="innerListBox"></ul>
        </div>
        <div class="pushcarBox carInBack cf" style="display: none;">
            <div class="pushNum fl">已选<em class="chooseItem">0</em>条，累计已通知<em class="totalPushCount">0</em></div>
            <button class="pushcarBtn fl" onclick="no.pushCar()">将货源发送给指定车</button>
            <h3 class="pushcarTit fl">提示：同一车主每天最多推送3次，同一货源每天最多推送1次</h3>
        </div>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/nearbyGoods.js"></script>
</html>