<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>三周年活动查询管理</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <style type="text/css">
        .searchUl li {
            width: 225px;
        }
        .searchUl li label {
            width: 120px;
        }
        .owernLine {
            background: #6495ed!important;
        }
    </style>
</head>

<body>
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span>
            <span>活动管理</span>
            <span>&nbsp;&gt;&nbsp;</span>
            <span class="currentPage">活动查询</span>
        </h1>
        <div class="main-content">

            <!--搜索条件-->
            <div class="searchBox" id="searchCondition">
                <ul class="searchUl searchUlNone cf" id="threeActive">
                    <li>
                        <label>推荐人手机号：</label>
                        <input id="cellPhone" tname="cellPhone" type="text" class="input90" name="" maxlength="11" value="" oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" />
                    </li>
                    <li>
                        <label>被推荐人手机号：</label>
                        <input id="inviteCellPhone" tname="inviteCellPhone" type="text" class="input90" name="" maxlength="11" value="" oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" />
                    </li>
                </ul>
            </div>
            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                            <input id="busyEntOptfnBtn" data-click="0" type="button" onclick="tFn.checkForm($(this))" class="button" value="查询">
                        </li>
                    </ul>
                </div>
                <div class="clearfix"></div>
                <div class="messageRight fr">
                <span id="totalAwardDays"></span>
                查询到<em id="rowCount"></em>条信息，当前是第
                    <span id="currentPage"></span>页，共
                    <span id="totalPages"></span>页
                </div>
            </div>

            <div id="receiptInfo" class="receiptInfo" style="display: none;">
                <i></i>
                <div class="receiptInner cf">
                    <span></span>
                    <em>复制</em>
                </div>
            </div>
            <div class="dataTable">
                <table border="0" class="tableBox" id="accountChange">
                    <tr class="tableTh">
                        <th>推荐人手机号</th>
                        <th>推荐人奖励天数</th>
                        <th>被推荐人手机号</th>
                        <th>被推荐人奖励天数</th>
                        <th>被推荐人状态</th>
                    </tr>
                </table>
            </div>
        </div>
        <!--JQ分页-->
        <div id="pageNumberBigBox" class="cf">
            <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
        </div>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/hidePhoneCommon.js"></script>
<script src="../../js/threeActive.js"></script>
<script>
    tytTool.bindHidePhone();
    $(".tableBox").colResizable({
        liveDrag: true,
        draggingClass: "dragging",
        resizeMode: 'flex',
        minWidth: 80
    });
</script>

</html>