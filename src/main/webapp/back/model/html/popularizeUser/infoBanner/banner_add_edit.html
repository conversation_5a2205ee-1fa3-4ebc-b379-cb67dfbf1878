<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>新增轮播图/编辑轮播图</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link href="../../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="../../../css/infoTake.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="../../../js/jquery-1.7.2.min.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span>轮播图管理-新</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">新增/编辑</span>
		</h1>
    <div class="main-content">
        <!--系统设置-->
      <div class="systemParameter">
        <ul class="cf">
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>标题：</label>
	          <input class="info_input" type="text" name="title" maxlength="20" placeholder="不超过20个字符">
            <span class="lineError">标题须为1-20个字符</span>
	        </li>
          <li>
          	<label><i class="xing">*</i>轮播图文件：</label>
        		<div class="fileBtnBox fl">
	 						<input class="fileBtn" name="loopPicturePic" id="fileField" accept="image/bmp,image/jpeg,image/jpg,image/png,image/gif" type="file" name="fileField" onchange="fileInfo(this,$(this))">
	 						<button class="rule_true">上传文件</button>
	 					</div>
        		<span class="ImgName"></span>
            <span class="lineError">图片大小不能超过2M</span>
          </li>
          <li>
          	<label></label>
        		<span>图片尺寸要求： 702*200px(2倍高清图），文件大小不超过2M<br/>文件格式：JPEG、PNG、BMP、GIF</span>
            <span class="lineError">请上传轮播图文件，支持JPEG、PNG、BMP、GIF格式</span>
          </li>
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>图片链接：</label>
	          <input class="info_input" name="openUrl" type="text" placeholder="">
            <span class="lineError"></span>
	        </li>
	        <li class="cf">
	          <label class="fl"><i class="xing">*</i>显示顺序：</label>
	          <input class="info_input" type="text" name="sort" maxlength="16" placeholder="填写轮播图编号，用于控制轮播图显示顺序">
            <span class="lineError">须为0-9999内整数</span>
	        </li>

					<li class="cf">
						<label for="">显示设置：</label>
						<div class="setBaoding fl">
							<ul class="chooseBtnBox setBox cf">
								<li class="fl"><input type="checkbox" name="baodingHide"><label for="">保定隐藏</label></li>
								<li class="fl"><input type="checkbox" name="wrongBaodingHide"><label for="">非保定隐藏</label></li>
							</ul>
            <!-- <span class="lineError"></span> -->
						</div>
					</li>
					<li class="titleLi cf">
						<p>提示：显示设置支持单选和复选</p>
					</li>

					<li class="cf">
						<label for="">显示位置：</label>
						<div class="setBaoding fl">
							<ul class="chooseBtnBox placeBox cf">
								<li class="fl"><input type="radio" name="displaySite" value="2"><label for="">车方</label></li>
								<li class="fl"><input type="radio" name="displaySite" value="1"><label for="">货方</label></li>
							</ul>
						</div>
					</li>
        </ul>

        <p class="errorBlock errorBlockNomal" id=""></p>
        <div class="divButton">
        	<input type="button" class="button updateBtn" value="保存">
        </div>
    </div>
  </div>
	</div>
</body>
<script>
	function fileInfo(source, _this) {
	  var f = source.files[0];
	  console.log(f)
	  var name = f.name;
	  var size = f.size;
	  var type = f.type;
	  $(".ImgName").html(name)
	}

	$(".setBox input").click(function(event) {
		if($(this).attr('checked')){
			$(this).removeAttr('checked');	
		}else{
			$(this).attr('checked','checked');	
		}
	});
	$(".placeBox input").click(function(event) {
		$(".placeBox input").removeAttr('checked');
		if($(this).attr('checked')){
			$(this).removeAttr('checked');	
		}else{
			$(this).attr('checked','checked');	
		}
	});
	$(".setBox label").click(function(event) {
		if($(this).siblings('input').attr('checked')){
			$(this).siblings('input').removeAttr('checked');	
		}else{
			$(this).siblings('input').attr('checked','checked');
		}
	});
	$(".placeBox label").click(function(event) {
		$(".placeBox input").removeAttr('checked');
		if($(this).siblings('input').attr('checked')){
			$(this).siblings('input').removeAttr('checked');	
		}else{
			$(this).siblings('input').attr('checked','checked');
		}
	});
</script>
</html>