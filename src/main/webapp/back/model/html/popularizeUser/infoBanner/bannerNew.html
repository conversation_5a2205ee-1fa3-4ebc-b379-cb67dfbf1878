<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>轮播图管理-新</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link href="../../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../../css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="../../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="../../../css/validate.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="../../../js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="../../../../jurisdiction/jsp/js/common.js"></script>
</head>
<body>
	<div class="contentBox" style="width: auto;">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>系统信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">轮播图管理-新</span>
		</h1>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="" type="button" value="新增" onclick="openWinAuto('banner_add_edit.html','banner_add',1100,600)"></li>
				</ul>
			</div>
			<div class="clearfix"></div>
			<div class="messageRight fr">
                查询到<em>16</em>条信息，当前是第<span>1</span>页，共<span>1</span>页
            </div>
		</div>
		<div class="dataTable">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox">
				<tr class="tableTh">
					<th width="80">显示顺序</th>
					<th width="120">添加时间</th>
					<th width="150">轮播缩略图</th>
					<th>标题</th>
					<th>图片链接</th>
					<th width="80">保定隐藏</th>
					<th width="80">非保定隐藏</th>
					<th width="80">显示位置</th>
					<th width="180">操作</th>
				</tr>
				<tr>
					<td>1</td>
					<td>2017-9-3</td>
					<td><img class="showBigPng" src="http://**************:88/app/turnPictures/identityAuth.png" alt=""></td>
					<td>货主福利来啦</td>
					<td><a class="tip" href="http://**************/manage_new/admin/userlogin" target="_blank"  tip="">http://**************/manage_new/admin/userlogin</a></td>
					<td>是</td>
					<td>否</td>
					<td>车方</td>
					<td>
						<a class="info_detail ver_block endTask" style="color: #FF0000">停用</a>
						<span class="ver_block ver_span">|</span>
						<a class="info_detail ver_block" onclick="openWinAuto('banner_add_edit.html','above_block',1100,600)">编辑</a>       
						<span class="ver_block ver_span">|</span>
						<a class="info_detail ver_block removeBlock">删除</a>       
          </td>
				</tr>
				<tr>
					<td>2</td>
					<td>2017-9-3</td>
					<td><img class="showBigPng" src="http://**************:88/app/turnPictures/fahuofang_zhuyi.png" alt=""></td>
					<td>货主福利来啦</td>
					<td><a class="tip" href="http://**************/manage_new/admin/userlogin" target="_blank"  tip="">http://**************/manage_new/admin/userlogin</a></td>
					<td>是</td>
					<td>否</td>
					<td>车方</td>
					<td>
						<a class="info_detail ver_block startTask" style="color: #008000">启用</a>
						<span class="ver_block ver_span">|</span>
						<a class="info_detail ver_block" onclick="openWinAuto('banner_add_edit.html','above_block',1100,600)">编辑</a>       
						<span class="ver_block ver_span">|</span>
						<a class="info_detail ver_block removeBlock">删除</a>       
          </td>
				</tr>
		    </table>
		</div>

		<!--此处引入footer.jsp-->
    <!-- <jsp:include page="/back/jsp/footer.jsp" flush="true"/> -->
	

		<div class="joinbgOutLook" style="display: none;"></div>
		<div class="outLook infopopType" style="display: none;">
			<div class="innerLook">
				<h2>提示</h2>
		    <p class="alertContent">确认删除此轮播图信息？</p>
				<!-- <p class="errorMsg blockerrorMsg" style=""></p> -->
				<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
					<button class="rule_true" onclick="">确定</button>
					<button id="" class="close_block fr">取消</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>
		<div class="outLook startStoppbtn" style="display: none;">
			<div class="innerLook">
				<h2>提示</h2>
		    <p class="alertContent"></p>
				<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
					<button class="rule_true" onclick="">确定</button>
					<button id="" class="close_block fr">取消</button>
				</div>
			</div>
			<i class="closelookBtn">×</i>
		</div>


	</div>
</body>
<script type="text/javascript" src="../../../js/easy_validator.pack.js"></script>
<script type="text/javascript" src="../../../js/colResizable-1.6.min.js"></script>
<script>
	window.onload = function(){
		$(".dataTable .tip").each(function(index, el) {
			$(this).attr('tip',$(this).html())
		});
	}

	$(".dataTable .showBigPng").click(function(event) {
		var _src = $(this).attr('src');
		openWinAuto(_src,'showImg',1100,600)
	});

	/*
		=====================
	*/
	$(".tableBox").colResizable({
		liveDrag: true,
		draggingClass: "dragging",
		resizeMode: 'flex',
		minWidth: 80
	});

	$(".removeBlock").click(function(event) {
		$(".infopopType,.joinbgOutLook").show();
	});
	$(".closelookBtn,.close_block").click(function(event) {
		$(".infopopType,.joinbgOutLook").hide();	
	});


	$(".startTask").click(function(event) {
		$(".startStoppbtn .alertContent").html('确认要启用此轮播图信息吗？<br/>启用后轮播图可呈现在APP页面上')
		$(".startStoppbtn,.joinbgOutLook").show();
	});
	$(".endTask").click(function(event) {
		$(".startStoppbtn .alertContent").html('确认要停用此轮播图信息吗？<br/>停用后此轮播图不再呈现在APP页面上')
		$(".startStoppbtn,.joinbgOutLook").show();
	});
	$(".closelookBtn,.close_block").click(function(event) {
		$(".startStoppbtn .alertContent").html('')
		$(".startStoppbtn,.joinbgOutLook").hide();	
	});
</script>
</html>
