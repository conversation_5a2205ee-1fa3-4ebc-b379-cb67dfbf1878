<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>推广用户管理-添加</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/detailPages.css" type="text/css" rel="stylesheet">
  <link href="../../css/detailPagesAdd.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
	<script src="../../js/tyt_area_no_county/js/tyt_area.js"></script>
  <script src="../../js/awardMessage.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>推广功能管理</span><span>&nbsp;&gt;&nbsp;</span><span>推广设置</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">添加</span>
		</h1>
    <div class="main-content">
        <!--系统设置-->
        <div class="systemParameter">
        <ul class="cf"  id="award_state" data-state="">
          <li class="cf">
            <label class="fl">被推广人适用地区：</label>
              <div class="fl areaDiv">
              <input id="position2" class="input120" type="text" placeholder="请选择地区" onclick="tyt_open_area('1','position2','boxDiv2','sheng2','shi2','tyt_area_iframe2','3');"
                      readonly class="positionInput addressIcon" style="vertical-align: middle;">
                  <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
                      id="boxDiv2">
                      <iframe name="tyt_area_iframe2" id="tyt_area_iframe2" src="../../js/tyt_area_no_county/area.html" frameborder="0"
                          width="240" scrolling="no" height="350" leftmargin="0" topmargin="0" class="boxDiv" allowtransparency="true"></iframe>
                  </div>
                  <p class="noneInput mtl20"><input id="sheng2" type="text" value="" class="positionInput"></p>
                  <p class="noneInput mtl20"><input id="shi2" type="text" value="" class="positionInput"></p>
                  <div class="cb"></div>
              </div>
              <em class="lineError"></em>
          </li>
          <li>
          	<label>被推广人适用身份：</label>
            <select id="recIdentity" data-val=''>
              <option value=''>请选择</option>
              <option value='1'>个人货主</option>
              <option value='2'>企业货主</option>
              <option value='3'>货站</option>
              <option value='6'>个人车主</option>
              <option value='7'>板车司机</option>
              <option value='8'>运输公司/车队</option>
            </select>
              <em class="lineError"></em>
          </li>
          <li>
          	<div class="popularize_check" id="popularize_auth">
          		<label>奖励设置：</label>
          		<input id="auth_abled" type="checkbox"><span>邀请认证成功</span>
          	</div>
          	<ul class="popularize_row row_one" id="auth_rule">
          		<li>
          			第<input class="one_inp must_inp" type="text" value=""><span>-</span>第<input class="two_inp must_inp" type="text" value="">个用户，奖励<input maxlength=5 class="yuan_inp must_inp" type="text" value="">元/人
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text" value=""><span>-</span>第<input class="two_inp" type="text" value="">个用户，奖励<input maxlength=5 class="yuan_inp" type="text" value="">元/人
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text"><span>-</span>第<input class="two_inp" type="text">个用户，奖励<input maxlength=5 class="yuan_inp" type="text">元/人
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text"><span>-</span>第<input class="two_inp" type="text">个用户，奖励<input maxlength=5 class="yuan_inp" type="text">元/人
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text"><span>-</span>第<input class="two_inp" type="text">个用户，奖励<input maxlength=5 class="yuan_inp" type="text">元/人
                <em class="lineError"></em>
          		</li>
          	</ul>
          	<div class="popularize_check" id="popularize_pay">
          		<label></label>
          		<input id="pay_abled" type="checkbox"><span>邀请付费成功</span>
          	</div>
          	<ul class="popularize_row row_two" id="pay_rule">
          		<li>
          			第<input class="one_inp must_inp" type="text" value=""><span>-</span>第<input class="two_inp must_inp" type="text" value="">个用户，奖励付费总额的<input class="per_inp must_inp" type="text" value="">%
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text" value=""><span>-</span>第<input class="two_inp" type="text" value="">个用户，奖励付费总额的<input class="per_inp" type="text" value="">%
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text"><span>-</span>第<input class="two_inp" type="text">个用户，奖励付费总额的<input class="per_inp" type="text">%
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text"><span>-</span>第<input class="two_inp" type="text">个用户，奖励付费总额的<input class="per_inp" type="text">%
                <em class="lineError"></em>
          		</li>
          		<li>
          			第<input class="one_inp award_one_inp" type="text"><span>-</span>第<input class="two_inp" type="text">个用户，奖励付费总额的<input class="per_inp" type="text">%
                <em class="lineError"></em>
          		</li>
          	</ul>
          </li>
        </ul>
        <p class="errorBlock errorBlockNomal" id="errorSpan"></p>
        <div class="divButton">
        	<input id="updateBtn" type="button" class="button" data-ster='0' value="提交"onclick="">
        	<input id="backBtn" type="button" class="button" value="返回"onclick="window.close();">
        </div>
    </div>
    <!--提交成功-->
  <div class="alert alert-danger fade in" id="alert_danger" role="alert" style="display: none;">
        <button type="button" class="close" aria-label="Close" id="close_btn">×</button>
        <h4>提示</h4>
        <p class="alertContent">提交成功，稍后将关闭此窗口</p>
        <div class="tc buttonBox">
          <button type="button" class="button" id="btn_default" datastu="0">我知道了</button>
        </div>
  </div>

  </div>

		
	</div>
</body>
<script src="../../js/popularize.js"></script>
<script>
window.onload = function(){
  var div2 = $("#tyt_area_iframe2").contents().find('#div2');
  div2.click(function() {
    if ($("#boxDiv2").is(':hidden')) {
    var position2_val = $('#position2').val().replace(/\s+/g, "");
      $("#position2").attr('data-city',position2_val)
    }
    $("#errorSpan").html('')
    $("#updateBtn").attr('data-sub', 0)
    IdentityCity()
  });

}

$("#recIdentity").change(function() {
  $("#recIdentity option").each(function(index, el) {
   if ($("#recIdentity").val()==$(this).val()&&$(this).val()!='') {
      $("#recIdentity").attr('data-ident',$(this).html())
    } 
    if($(this).val()==''){
      $("#recIdentity").attr('data-ident','')
    }
  });
  $("#errorSpan").html('')
  $("#updateBtn").attr('data-sub', 0)
  IdentityCity()
});
//提交
$("#updateBtn").click(function(event) {
  if (liEach() == true) {
    addAward()
    $('#alert_danger').show();
  }
});
</script>
</html>
