<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>客服Q&A</title>
    <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
</head>
<style>
    .QandABox{
        width: 100%;
        border: 1px solid #e7e7e7;
        border-top: none;
    }
    .QandABox:nth-child(1){
        border-top: 1px solid #e7e7e7;
    }
    .QandATitle{
        color: #333333;
        line-height: normal;
        font-size: 14px;
        text-align: left;
        font-weight: bold;
        padding: 10px;
    }
    .QandAContent{
        color: #333333;
        line-height: normal;
        font-size: 14px;
        text-align: left;
        padding: 0px 30px 10px 30px;
        word-wrap: break-word;
        word-break: normal;
    }
    .QandAButton{
        color: #189beb;
        font-size: 14px;
        padding: 5px 10px;
        cursor: pointer;
        float: right;
    }
    .QandANoData{
        padding:30px;
        text-align: center;
        color: #333333;
        font-size: 14px;
        border: 1px solid #e7e7e7;

    }
    .addQandAModal{
        height: 295px!important;
        margin: -188px 0 0 -225px;
    }
    #answer_title{
        position: relative;
        top: -68px;
    }
    #answer{
        border: 1px solid #ccc;
    }
    .QandAMessage{
        font-size: 13px;
        color: #999;
        text-align: right;
        padding: 0px 20px 10px 0px;
    }
    .searchTitle{
        margin-left: -38px;
    }
</style>
<body>
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>客服Q&A</span>
    </h1>
    <div class="searchBox">
        <ul class="searchUl searchUlNone cf">
            <li>
                <label class="searchTitle" for="">关键字：</label>
                <input id="keywordSearch" type="text" maxlength="50" tname="keywordSearch">
            </li>
        </ul>
    </div>
    <div class="pageMessage">
        <div class="searchBtn">
            <ul class="btnBox cf">
                <li><input class="" type="button" value="查询" onclick="co.listMsg(1)"></li>
                <li><input class="" type="button" value="+ 增录Q&A" onclick="co.showHideAdd('show')"></li>
            </ul>
        </div>
        <div class="clearfix"></div>
        <div class="messageRight fr">查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span id="totalPages"></span>页
        </div>
    </div>
    <div class="dataTable">
        <ul id="QandAContainer">

        </ul>
    </div>

    <!-- Q&A弹框 -->
    <div class="alert alert-danger fade in alert-height" style="display: none;" id="addQandAModal">
        <div class="innerLook">
            <h2 id='addUpdateTitle'>添加Q&A</h2>
            <div class="addWhiteUser cf">
                <input type="text" hidden id="id" data-type="" tname="id">
                <ul>
                    <li>
                        <label for="">问题：</label>
                        <input type="text" placeholder="最多输入30字" maxlength="30" id="question" tname="question" style="padding-left: 5px;"><br/>
                    </li>
                    <li>
                        <label for="" id="answer_title">答案：</label>
                        <textarea  class="note" cols="27" rows="5" maxlength="500" tname="answer" id="answer" placeholder="最多输入500字"></textarea>
                    </li>
                </ul>
                <div class="makeSureBtn" style="width:68px;height:32px;margin: 0 auto;">
                    <input class="button" type="button" id="saveOneButton" onclick="co.addQandA()" value="保存">
                </div>
            </div>
        </div>
        <i class="closelookBtn" onclick="co.showHideAdd('hide')">×</i>
    </div>

    <!--JQ分页-->
    <div id="pageNumberBigBox" class="cf">
        <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>
</div>
</body>
<script>
    window.JSESSION_ID_TRUE = true;
</script>
<script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="../../js/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../../js/jqpaginator.min.js"></script>
<script type="text/javascript" src="../../js/tyt_common.js"></script>
<!--<script type="text/javascript" src="../../js/companyHandle.js"></script>-->

<script type="text/javascript" src="../../js/customServiceQandA.js"></script>
</html>