<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title id="blockTitName">重点监控用户</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="../../css/blockList.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="../../js/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="../../../jurisdiction/jsp/js/common.js"></script>
</head>
<style>
    .item input, .item label{
        width: auto;
        vertical-align: middle;
    }
    .item select,.item input[type=text] {
        width: 200px;
        margin-right: 10px;
        height: 30px;
        background: #f1f1f1;
    }
    .my_radio {
        width: 20px !important;
        height: auto;
        vertical-align: top;
    }
    .remark label{
        vertical-align: top;
    }
    .remark textarea {
        width: 300px;
        height: auto;
    }
    .extCause{
        padding: 10px;
    }
    #goodOneLimit,#carOneLimit{
        display: none;
    }
    .bootstrap-select .btn {
        padding: 4px 6px !important;
    }
    .bootstrap-select.disabled .btn {
        background: #f1f1f1;
    }
</style>
<body>
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span class="">重点监控用户管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">重点监控用户</span>
        </h1>
        <div class="blockBox">
            <p>增加用户功能说明：</p>
            <p>1、帐号加入重点监控用户后，身份认证过程本人照将置为失败不可编辑，同时限制用户登录</p>
            <p>2、重点监控帐号对应的身份证信息绑定其它帐号再次申请认证时，管理后台审核页面将不允许认证成功</p>
            <p>3、重点监控用户若同时绑定MAC地址，当该MAC地址的帐号申请认证时，管理后台审核页面将不允许认证成功</p>
            <!--<div class="triangle_border">
			    <i class="quest"></i>
			    <div class="popup" style="display: none;">
			        <span><em></em></span>
			        <h2>说明：</h2>
			        <p>加入黑名单后，用户身份认证自动置为审核失败（仅适用于已申请身份认证）；  失败理由：“{身份证号信息/MAC地址终端}使用限制”（所有照片类认证失败理由中均有无效这一项）</p>
			    </div>
			</div> -->
        </div>
        <div class="remove_bind remove_bind_dom">
            <ul class="remove_bind_ul" id="contentForm">
                <li class="regin_phone">
                    <label class="add_label" for=""><i class="mustRed">*</i>手机号：</label>
                    <input class="add_value" id="cellPhone" type="text" />
                    <span class="lineError"></span>
                    <button style="display: none;" id="blockRecord" onclick="goBlockRecord()">处理记录&gt;</button>
                </li>

                <li class="name_idcard" style="display: none;">
                    <!-- 通过检索或是进入页面时候加载 -->
                    <label class="add_label" for="">真实姓名：</label>
                    <span id="userName"></span>
                    <label class="add_label" for="">身份证号：</label>
                    <span id="idcard"></span>
                </li>
                <!-- 已注册拉黑 -->
                <div id="invocable" style="display: none">
                    <!-- 黑名单 -->
                    <li class="item status">
                        <label class="add_label" for="">黑名单：</label>
                        <label for="">是</label>
                        <input disabled class="my_radio" data-forever="" id="forever"  type="radio"  name="status" tname="status" value="1" >
                        <label for="">否</label>
                        <input disabled class="my_radio" type="radio"  name="status" tname="status" value="0"  >
                        <label for="" class="isEdit ml-20">是否处罚：</label>
                        <input class="isEdit" type="checkbox" name="category" tname="restrictChangeStatus" value="1" onchange="isEdit($(this), 'black')" />
                    </li>
                    <li class="item" id="blackSetting" style="display: none;">
                        <label class="add_label"><i class="xing">*</i>拉黑期限：</label>
                        <span>
                            <input class="my_radio black-input"  disabled  tname="perpetual" name="blackTimeGood"  value="1" type="radio"><em>永久拉黑</em></span>
                        <span class="ml-10" />
                        <input disabled class="my_radio black-input"  tname="perpetual" name="blackTimeGood" value="0" type="radio"><em>拉黑</em>
                        <input disabled  class="black-input punish-input" tname="restrictNum"  id="inp"  max="1000" /> 天
                    </li>
                    <!-- 发货限制 -->
                    <li class="item">
                        <label class="add_label" for="">发货：</label>
                        <label for="">限制方式：</label>
                        <select name="good" disabled name="" id="goodLimit" tname="limitMinutes">
                        </select>
                        <select name="good" disabled id="goodOneLimit" tname="subLimitMinutes">
                        </select>
                        <label for="">限制开始时间：</label>
                        <input id="limitStartTime" name="good" disabled placeholder="此刻" onfocus="this.blur()" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" class="" type="text" tname="limitStartTime" />
                    </li>
                    <li class="item blackReason good-rea">
                        <label class="add_label" for="">原因：</label>
                        <select disabled  name="" id="goodCause" class="reason"  tname="cause">
                            <option value="">请选择</option>
                        </select>
                        <select  disabled name="" tname="goodCause1" class="goodCause1"  >
                            <option value="">请选择</option>
                        </select>
                        <select  disabled name=""  tname="goodCause2" class="goodCause2" >
                            <option value="">请选择</option>
                        </select>
                        <select disabled  name="" tname="goodCause3" class="goodCause3">
                            <option value="">请选择</option>
                        </select>
                        <label for="" class="isEdit">是否处罚：</label>
                        <input class="isEdit"  type="checkbox" name="category" tname="goodsChangeStatus" value="1" onchange="isEdit($(this), 'good')" />
                    </li>
                    <!-- 找货 -->
                    <p style="color:red;font-size: 14px;padding-left: 124px;margin-top: 20px">提示：限制货类 和 限制价格 、限制方式 都选择 未限制 才代表解除惩罚。</p>
                    <li class="item bigBox" >
                        <label class="add_label" for="">找货：</label>
                        <div class="item-content">
                            <label for="">限制货类：</label>
                            <select
                                disabled
                                name="car"
                                id="goodsLimitCategory"
                                tname="goodsLimitCategory"
                            >
                            </select>
                            <label for="">限制价格：</label>
                            <select disabled  name="car" id="goodsLimitPrice" tname="goodsLimitPrice">
                            </select>
                            <label for="">限制方式：</label>
                            <select name="car" disabled name="" id="carLimit" tname="carLimitMinutes">
                            </select>
                            <select name="car" disabled id="carOneLimit" tname="subCarLimitMinutes">
                            </select>
                            <label for="">限制开始时间：</label>
                            <input id="carLimitStartTime" name="car" disabled placeholder="此刻" onfocus="this.blur()" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" class="" type="text" tname="carLimitStartTime" />
                        </div>
                    </li>
                    <li class="item blackReason car-rea">
                        <label class="add_label" for="">原因：</label>
                        <select disabled  name="" id="carCause" class="reason" tname="carCause">
                            <option value="">请选择</option>
                        </select>
                        <select disabled  name=""  tname="cause1" class="cause1" >
                            <option value="">请选择</option>
                        </select>
                        <select disabled  name=""  tname="cause2"  class="cause2" >
                            <option value="">请选择</option>
                        </select>
                        <select disabled  name=""  tname="cause3"  class="cause3">
                            <option value="">请选择</option>
                        </select>
                        <label for="" class="isEdit">是否处罚：</label>
                        <input class="isEdit" type="checkbox" name="carChangeStatus" tname="carChangeStatus" value="1"  onchange="isEdit($(this), 'car')"  />
                    </li>
                    <!-- 备注 -->
                    <li class="item remark">
                        <label class="add_label" for="">备注：</label>
                        <textarea class="extCause" name="" id="" cols="30" rows="10" tname="extCause" maxlength="200" placeholder="200字以内详细说明，供内部查阅，以便后续跟进"></textarea>
                    </li>
                    <li>
                        <label class="add_label" for="" style="text-align: left;">绑定MAC地址：</label>
                    </li>
                    <input type="hidden" id="carLimitEndTime" name="carLimitEndTime" tname="carLimitEndTime" />
                    <input type="hidden" id="limitEndTime" name="limitEndTime" tname="limitEndTime" />
                </div>
            </ul>
            <div class="longBlockdata" style="display: none">
                <div class="dataTable" style="">
                    <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i>
                        <div class="receiptInner cf"><span></span><em>复制</em></div>
                    </div>
                    <table border="0" class="tableBox" style="" id="maclist">
                        <tr class="tableTh">
                            <th>该账号曾登录Mac地址</th>
                            <th>登录终端</th>
                            <th>操作</th>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="block_tit">
            <p>注：加入黑名单后，用户将与未激活状态一样无法登陆平台，请谨慎操作。</p>
            <!-- <p>失败理由：<strong>身份认证不合法</strong></p> -->
        </div>

        <div class="outgreen" style="display: none;">
            <p>
                <i></i><span>加入黑名单成功</span>
            </p>
        </div>

    </div>
    <div id="divBox"></div>
</body>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/blockList.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/select.js"></script>
<script src="../../js/addblockMessage.js"></script>
<script>
    $(function () {
        loadDom();
        $(".tableBox").colResizable({
            liveDrag: true,
            draggingClass: "dragging",
            resizeMode: 'flex',
            minWidth: 80
        });


        //说明文的移入展示
        // $(".triangle_border").mouseenter(function(event) {
        // 	$(".popup").show();
        // });
        // $(".triangle_border").mouseleave(function(event) {
        // 	$(".popup").hide();
        // });

        $("#extCause").keyup(function(event) {
            //头部去空
            $(this).val($(this).val().replace(/(^\s*)/g, ""))

        });

        $(".whatMacbox").mouseenter(function(event) {
            $(".whatMacpopup").show();
        });
        $(".whatMacbox").mouseleave(function(event) {
            $(".whatMacpopup").hide();
        });
    })


</script>

</html>