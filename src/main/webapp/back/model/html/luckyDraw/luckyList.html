<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title id="pageName">抽奖活动管理</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/luckyDraw/luckyList.css">
</head>

<body class="theme-blue">
    <!-- 主要内容开始 -->
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>抽奖后台</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">抽奖活动管理</span>
        </h1>
        <div class="main-content">
            <!--搜索条件-->
            <div class="searchBox" id="searchCondition">
                <ul class="searchUl searchUlNone cf">
                    <li>
                        <label>活动名称：</label>
                        <input type="text" tname="activityName" maxlength="200" value="">
                    </li>
                </ul>
            </div>
            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                            <input type="button" onclick="luckO.loadList()" class="button" value="查询">
                        </li>
                        <li>
                            <input type="button" onclick="commonO.operate('open','add')" class="button" value="新增抽奖活动">
                        </li>
                    </ul>
                </div>
                <div class="clearfix"></div>
                <div class="messageRight fr">查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span id="totalPages"></span>页
                </div>
            </div>

            <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i>
                <div class="receiptInner cf"><span></span><em>复制</em></div>
            </div>
            <div class="dataTable">
                <table border="0" class="tableBox" id="luckyList">
                    <tr class="tableTh">
                        <th>ID</th>
                        <th>活动名称</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>保存时间</th>
                        <th>参与规则</th>
                        <th width="180px;">操作</th>
                    </tr>
                </table>
            </div>
        </div>
        <!--JQ分页-->
        <div id="pageNumberBigBox" class="cf">
            <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
        </div>
    </div>
    <div class="outBgColor" style="display: none;"></div>
    <div class="outLook luckOutLook" style="display: none;">
        <div class="innerLook">
            <h2 class="luckName"></h2>
            <ul class="luckBox">
                <li>
                    <input type="hidden" tname="id" value="">
                    <!-- <input type="hidden" tname="limitTimes" value=""> -->
                    <label for="">活动名称：</label>
                    <input class="activeName" tname="activityName" type="text" placeholder="请填写活动名称">
                </li>
                <li>
                    <label for="">活动时间：</label>
                    <input id="timeStart" class="activeTime" tname="startTime" type="text" readonly onclick="commonO.timeStart()" placeholder="开始时间">
                    <span class="activeTimeFlag">-</span>
                    <input id="timeEnd" class="activeTime" tname="endTime" type="text" readonly onclick="commonO.timeEnd()" placeholder="结束时间">
                </li>
                <li class="cf">
                    <label class="fl" for="">参与规则：</label>
                    <!-- <span class="ruleTnamebox fl" style="display: none;"></span> -->
                    <div class="ruleBox fl">
                        <div class="checkRule">
                            <i onclick="luckO.radioCheck($(this),2)"></i><span>每人每天</span>
                        </div>
                        <div class="checkRule">
                            <i onclick="luckO.radioCheck($(this),1)"></i><span>活动期间每人共</span>
                        </div>
                        <input class="ruleHidden" type="hidden" tname="limitTimesType" value="">
                    </div>
                </li>
            </ul>
            <p></p>
            <div class="ideabtnBox luckbtnBox cf">
                <button onclick="commonO.operate('close')" class="close_block fl">取消</button>
                <button onclick="" class="close_block fr submitActie">确认</button>
            </div>
        </div>
        <i class="closelookBtn" onclick="commonO.operate('close')">×</i>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/luckyDraw/luckyCommon.js"></script>
<script src="../../js/luckyDraw/luckyList.js"></script>

</html>