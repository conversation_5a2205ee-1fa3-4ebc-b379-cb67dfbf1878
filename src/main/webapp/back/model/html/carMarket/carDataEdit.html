<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title id="pageName">板车数据管理</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/carMarket/carMarket.css">
</head>
<style type="text/css">
    
  </style>
<body class="theme-blue" style="padding: 0 0 40px;">
    <!-- 主要内容开始 -->
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>板车超市</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">板车数据管理</span>
        </h1>
        <div class="hotContent cf">
            <div class="fl">
                <buttton class="tableTypeBtn" onclick="editO.addType($(this),'车型')">新增车型</buttton>
                <div class="dataTable" style="min-width: 100%;">
                    <table border="0" id="carType" class="tableBox">
                        <tr class="tableTh">
                            <th>车型</th>
                            <th>图片</th>
                            <th>操作</th>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="fl">
                <buttton class="tableTypeBtn" onclick="editO.addType($(this),'长度')">新增长度</buttton>
                <div class="dataTable" style="min-width: 100%;">
                    <table border="0" id="length" class="tableBox">
                        <tr class="tableTh">
                            <th>长度</th>
                            <th>操作</th>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="fl">
                <buttton class="tableTypeBtn" onclick="editO.addType($(this),'自重')">新增自重</buttton>
                <div class="dataTable" style="min-width: 100%;">
                    <table border="0" id="deadWeight" class="tableBox"
                        <tr class="tableTh">
                            <th>自重</th>
                            <th>线索量</th>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="fl">
                <buttton class="tableTypeBtn" onclick="editO.addType($(this),'载重')">新增载重</buttton>
                <div class="dataTable" style="min-width: 100%;">
                    <table border="0" id="loadWeight" class="tableBox" >
                        <tr class="tableTh">
                            <th>载重</th>
                            <th>操作</th>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="outBgColor" style="display: none;"></div>
    <div class="outLook delOutLook" style="display: none;">
        <div class="innerLook">
            <h2 class="luckName">删除提示</h2>
            <p class="delTitle"></p>
            <div class="ideabtnBox luckbtnBox cf">
                <button onclick="commonO.operate('close')" class="close_block fl">否</button>
                <button onclick="editO.deleteLine()" class="close_block fr">是</button>
            </div>
        </div>
        <i class="closelookBtn" onclick="commonO.operate('close')">×</i>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/carMarket/common.js"></script>
<script src="../../js/carMarket/carDataEdit.js"></script>
</html>