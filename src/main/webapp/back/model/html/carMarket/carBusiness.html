<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title id="pageName">商家入驻</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/carMarket/carMarket.css">
</head>
<body class="theme-blue">
<!-- 主要内容开始 -->
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>板车超市</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">商家入驻</span>
    </h1>
    <div class="main-content">
        <!--搜索条件-->
        <div class="searchBox" id="searchCondition">
            <ul class="searchUl searchUlNone cf"></ul>
        </div>
        <!--页面信息-->
        <div class="pageMessage">
            <div class="searchBtn">
                <ul class="btnBox cf"">
                    <li>
                        <input type="button" onclick="busO.loadList()" class="button" value="查询">
                    </li>
                    <li>
                        <input class="" id="resetForm" type="button" value="重置" onclick="tytTool.formReset($('.searchUl'),busO.loadList)">
                    </li>
                </ul>
            </div>
            <div class="clearfix"></div>
            <div class="messageRight fr">查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span id="totalPages"></span>页
            </div>
        </div>

        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
        <div class="dataTable">
            <table border="0" class="tableBox" id="carLib"></table>
        </div>
    </div>
    <!--JQ分页-->
    <div id="pageNumberBigBox" class="cf">
        <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>
</div>
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/carMarket/common.js"></script>
<script src="../../js/carMarket/carBusiness.js"></script>
</html>