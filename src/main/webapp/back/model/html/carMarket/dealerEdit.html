<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>新增经销商</title>
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/detailNav.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/carSaleEdit.css">
    <link rel="stylesheet" href="../../css/carDetails.css">
    <link rel="stylesheet" href="../../css/carMarket/carMarket.css">
    <link rel="stylesheet" href="../../css/carMarket/mapCkeck.css">
    <style>
        .car-type label {
            vertical-align: middle;
            user-select: none;
        }
        .car-checkbox {
            vertical-align: -1px;
        }
        .type-check-box {
            display: inline-block;
        }
        .del-btn {
            margin-left: 400px;
        }
        .ml-40 {
            margin-left: 40px;
        }
        .del-btn,.not-must {
            display: none;
        }
        .switch-item {
            top: 1px;
            right: 10px;
            font-size: 14px;
        }
        .input-checked.switch-anim:checked:after {
            content: attr(after);
            color: #fff;
            position: absolute;
            top: 0;
            left: 11px;
            font-size: 14px;
        }
        .btnBox {
            width: auto;
            display: flex;
            justify-content: center;
        }
        .agentAddress {
            width: 360px;
            height: 28px;
            border: 1px solid #ccc;
            display: inline-block;
            line-height: 28px;
            vertical-align: middle;
            border-radius: 4px;
            padding: 0 10px;
        }
    </style>
</head>
<body>
<div id="relBox" class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>板车超市</span><span>&nbsp;&gt;&nbsp;</span><span>经销商管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">新增经销商</span>
    </h1>
    <!-- 车型信息 -->
    <div class="messageTit head">
        <h2 class="">经销商信息</h2>
    </div>
    <div class="detailBox">
        <ul class="detailListBox cf" style="display: none;">
            <li class="threeLine oneLine fl">
                <label for="" class=""><i class="must">*</i>经销商名称：</label>
                <!--<span class="brandRemove changeRemove" tname="agentName"></span>-->
                <input class="brandRemove" tname="agentName" type="text" maxlength="50">
                <!--<select class="agentIdTool addRemove changeRemove" tname="agentId">
                    <option class="firstOption" value="">请选择</option>
                </select>-->
            </li>
            <li class="threeLine changeRemove addRemove oneLine fl">
                <label for="" class=""><i class="addRemove must">*</i>代理品牌：</label>
                <span class="brandRemove changeRemove" tname="brandName"></span>
                <input class="brandRemove changeRemove" type="hidden" tname="brandId">
                <select class="brandTool addRemove" tname="brandId">
                    <option class="firstOption" value="">请选择</option>
                </select>
            </li>
        </ul>
        <div class="add-info-box brandRemove detailListBox" style="display: none;">
            <button class="button btn-default" onclick="editO.addMoreInfo()">添加更多代理信息</button>
            <div class="add-info addRemove">
                <div class="outline">
                    <label for="" class="car-label"><i class="must">*</i>代理品牌：</label>
                    <select class="brandTool" tname="brandId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                    <button class="button del-btn" onclick="editO.delateInfo($(this))">删除当前代理信息</button>
                </div>
                <div class="outline car-type">
                    <label for="" class="car-label"><i class="must">*</i>代理车型：</label>
                    <div class="type-check-box"></div>
                </div>
            </div>
        </div>
        <ul class="detailListBox cf" style="display: none;">
            <li class="threeLine brandRemove fl">
                <label class="fl"><i class="must">*</i>所属地：</label>
                <input type="text" id="tytCity" readonly="readonly" onclick="editO.tytCity()">
                 <input id="province" type="hidden" tname="province">
                <input id="city" type="hidden" tname="city">
                <input id="area" type="hidden" tname="area">
            </li>
            <li class="twoLine brandRemove fl">
                <label for="" class=""><i class="must">*</i>详细地址：</label>
                <!-- <label for="" class=""><i class="must">*</i>详细地址：</label> -->
                <!-- <input  class="agentAddress" tname="agentAddress" type="text" maxlength="50" onclick="mapO.showMap($(this))"> -->
                <span class="agentAddress" tname="agentAddress" onclick="mapO.showMap($(this))"></span>
            </li>
            <li class="brandRemove radio-box clearBoth">
                <label><i class="must">*</i>经销商是否合作：</label>
                <label for="cooperate1"><input type="radio" id="cooperate1" tname="cooperateStatus" name="cooperateStatus" value="1"> 是</label>
                <label for="cooperate2"><input type="radio" id="cooperate2" tname="cooperateStatus" name="cooperateStatus" value="0" checked> 否</label>
            </li>
            <li class="threeLine brandRemove fl">
                <label for=""><i class="must">*</i>入驻时间：</label>
                <!--<span class="changeRemove" tname="entryTime"></span>-->
                <input tname="entryTime" type="text" readonly onclick="commonO.timePicker()">
<!--                <input tname="entryTime" type="text" readonly onclick="commonO.timePicker()">-->
            </li>
            <li class="threeLine brandRemove fl">
                <label for="" class=""><i class="must">*</i>职位：</label>
                <input  class="" tname="position" type="text"  maxlength="50">
            </li>
            <li class="threeLine brandRemove fl">
                <label for="" class=""><i class="must">*</i>真实身份：</label>
                <select tname="agentRole" id="agentRole">
                    <option class="firstOption" value="">请选择</option>
                    <option value="0" data-val="0">个人</option>
                    <option value="1" data-val="1">厂家</option>
                    <option value="2" data-val="2">经销公司</option>
                </select>
            </li>
            <li class="threeLine brandRemove fl" style="width: 100%;">
                <label for="" class=""><i class="must">*</i>联系人：</label>
                <input  class="" tname="contact" type="text"  maxlength="10">
            </li>
            <li class="brandRemove phone-box fl">
                <label for="" class=""><i class="must">*</i>发布账号：</label>
                <input  class="" tname="cellPhone" type="text"  maxlength="11" onblur="commonO.inpBlurValid($(this),'basePhone')">
                <label for="" class="">联系电话1：</label>
                <input  class="" tname="cellPhone1" type="text"  maxlength="11" onblur="commonO.inpBlurValid($(this),'basePhone')">
                <label for="" class="">联系电话2：</label>
                <input  class="" tname="cellPhone2" type="text"  maxlength="11" onblur="commonO.inpBlurValid($(this),'basePhone')">
            </li>
            <li class="oneLine fl">
                <label for="" class=""><i class="must">*</i>是否启用：</label>
                <label style="position:relative;width:65px;">
                    <input class="switch switch-anim input-checked" checked="checked" type="checkbox" after="是">
                    <i class="switch-item">否</i>
                </label>
            </li>
            <li class="oneLine fl" style="height: 100px;">
                <label for="" class="fl">描述：</label>
                <textarea id="description" name="" tname="description" class="editTextarea fl" cols="30" rows="10" maxlength="50"></textarea>
            </li>
        </ul>
    </div>

    <!-- 驳回原因 -->
    <div class="outBgColor" style="display: none;"></div>
    <div class="alert alert-danger fade in alert-height" style="display: none;" id="confirmRefundModal">
        <div class="innerLook">
            <h2>驳回原因</h2>
            <div class="addWhiteUser cf">
                <ul>
                    <li class="auditFailBox">
                        <label for="">失败原因：</label>
                        <select id="auditFail" class="auditSeclct" tname="auditFail">
                            <option class="firstOption" value="">请选择</option>
                            <option value="经销商信息审核未通过">经销商信息审核未通过</option>
                        </select>
                    </li>
                </ul>
            </div>
            <div class="sureBtnBox">
                <input class="button" type="button" onclick="editO.rejectExist()" value="确定">&nbsp;&nbsp;&nbsp;&nbsp;
                <input class="button" type="button" onclick="editO.rejectLook('hide')" value="取消">
            </div>
        </div>
        <i class="closelookBtn" onclick="editO.rejectLook('hide')">×</i>
    </div>
    
    <!-- 按钮操作区 -->
    <div class="searchBtn">
        <ul class="btnBox cf">
            <li>
                <input type="button" class="button" value="取消" onclick="tytTool.closeWin()">
            </li>
            <li class="rejectBtn" style="display: none;">
                <input type="button" class="button" value="驳回" onclick="editO.rejectLook('show')">
            </li>
            <li id="addSub">
                <input type="button" class="button" value="提交" onclick="editO.subData($(this))">
            </li>
        </ul>
    </div>
</div>
<div id="componentBox"></div>
<div class="mapBox" style="display: none;">
    <i class="closeMapbox" onclick="mapO.closeMap()">×</i>
    <h3 class="mapPlaceBox"><span>请选择</span><span>请选择</span><span>请选择</span></h3>
    <div id="mapContainer"></div>
    <div class="searchInput">
        <input id='tipinput' type="text" placeholder="详情地址(必填)">
    </div>
    <button class="checkPlaceBtn" onclick="mapO.confirmChoose()">确定</button>
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<!-- <script src="../../js/tyt_area/js/tyt_area.js"></script> -->
<script src="../../js/tytCity/index.js"></script>
<script src="../../js/tyt_common.js"></script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.11&key=7b0d7988f8f8c5058dae32d67e1aabf8&plugin=AMap.Autocomplete,AMap.PlaceSearch,AMap.Geocoder,AMap.Size"></script>
<script src="../../js/carMarket/cityMap.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/carMarket/common.js"></script>
<script src="../../js/carMarket/dealerEdit.js"></script>
</html>