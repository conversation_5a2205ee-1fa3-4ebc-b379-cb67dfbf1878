<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>人工派单合同管理</title>
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="../../css/electContract.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="../../../jurisdiction/jsp/js/common.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>货源信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">人工派单合同管理</span>
		</h1>
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li>
					<label for="">合同编号：</label>
					<input type="text" style="">
				</li>
				<li>
					<label for="">托运方：</label>
					<input type="text" style="">
				</li>
				<li>
					<label for="">承运方：</label>
					<input type="text" style="">
				</li>
				<li>
					<label for="">创建人：</label>
					<input type="text" style="">
				</li>
				<li>
					<label for="">创建开始时间：</label>
					<input id="startTime" onclick="WdatePicker({readOnly:true,dateFmt:'yyyy-MM-dd HH:mm', maxDate:'#F{$dp.$D(\'endTime\')}'})" class="" type="text" name="releaseTimeStart" value="">
				</li>
				<li>
					<label for="">创建结束时间：</label>
					<input id="endTime" onclick="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd HH:mm', minDate:'#F{$dp.$D(\'startTime\')}', startDate:'#F{$dp.$D(\'startTime\')}' })" class="" type="text" name="releaseTimeEnd" value="">
				</li>
				<li>
					<label for="">合同状态：</label>
					<select name="" id="">
						<option value="">不限</option>
						<option value="">签约中</option>
						<option value="">已作废</option>
						<option value="">已生效</option>
					</select>
				</li>
			</ul>
		</div>
		<div class="pageMessage cf">
			<div class="searchBtn fl">
				<ul class="btnBox cf">
					<li><input class="" type="button" value="查询"></li>
					<li><input class="" type="button" value="重置"></li>
				</ul>
			</div>
			<div class="messageRight fr">
			查询到<em>256</em>条信息，当前是第<span>1</span>页，共<span>26</span>页
      </div>
		</div>
		<div class="messageList">
			<ul class="innerMessageList">
				<li class="MessageListNav">
					<div class="innerMessageListNav">
						<div class="messageTimeBox">
							<span>昨天 12:08</span>
							<em></em>
						</div>
						<div class="mainContentBox">
							<h2><strong>合同编号17121800011058</strong><a href="javascript:;" onclick="openWinAuto('../peopleorders/goodsDetails.html','goodsDetails',1200,600)">湖南邵阳市至广西南宁市周口店：220挖机马上走…</a></h2>
							<div class="mainDetailBox cf">
								<div class="aboveContract fl">
									<label for="">托运方</label><span>何老师/18688886666</span><em>(已签名)</em>
								</div>
								<div class="aboveContract fl">
									<label for="">承运方</label><span>里老师/15888886666</span>
								</div>
								<div class="fl">
									<label for="">创建人</label><span>田老师</span>
								</div>
							</div>
						</div>
						<div class="operationBox">
							<h3 class="signState Underway">签约中</h3>
							<div class="checkLookCont">
								<i></i><span>查看运输合同</span>
							</div>
						</div>
					</div>
				</li>
				<li class="MessageListNav">
					<div class="innerMessageListNav">
						<div class="messageTimeBox">
							<span>12-01 09:12</span>
							<em>2015年</em>
						</div>
						<div class="mainContentBox">
							<h2><strong>合同编号17121800011058</strong><a href="javascript:;" onclick="openWinAuto('../peopleorders/goodsDetails.html','goodsDetails',1200,600)">湖南邵阳市至广西南宁市周口店：220挖机马上走…</a></h2>
							<div class="mainDetailBox cf">
								<div class="aboveContract fl">
									<label for="">托运方</label><span>XXXXXX工程机械制造公司，代理人何老师/18688886666</span><em>(已签名)</em>
								</div>
								<div class="aboveContract fl">
									<label for="">承运方</label><span>里老师/15888886666</span>
								</div>
								<div class="fl">
									<label for="">创建人</label><span>田老师</span>
								</div>
							</div>
						</div>
						<div class="operationBox">
							<h3 class="signState Underway">签约中</h3>
							<div class="checkLookCont">
								<i></i><span>查看运输合同</span>
							</div>
						</div>
					</div>
				</li>
				<li class="MessageListNav">
					<div class="innerMessageListNav">
						<div class="messageTimeBox">
							<span>12-05 12:12</span>
							<em></em>
						</div>
						<div class="mainContentBox">
							<h2><strong>合同编号17121800011058</strong><a href="javascript:;" onclick="openWinAuto('../peopleorders/goodsDetails.html','goodsDetails',1200,600)">湖南邵阳市至广西南宁市周口店：220挖机马上走…</a></h2>
							<div class="mainDetailBox cf">
								<div class="aboveContract fl">
									<label for="">托运方</label><span>XXXXXX工程机械制造公司，代理人何老师/18688886666</span>
								</div>
								<div class="aboveContract fl">
									<label for="">承运方</label><span>里老师/15888886666</span>
								</div>
								<div class="fl">
									<label for="">创建人</label><span>田老师</span>
								</div>
							</div>
						</div>
						<div class="operationBox">
							<h3 class="signState Underway">签约中</h3>
							<div class="checkLookCont">
								<i></i><span>查看运输合同</span>
							</div>
						</div>
					</div>
				</li>
				<!-- 右侧弹框的结构 -->
				<li class="MessageListNav">
					<div class="innerMessageListNav">
						<div class="messageTimeBox">
							<span>昨天 12:08</span>
							<em></em>
						</div>
						<div class="mainContentBox">
							<h2><strong>合同编号17121800011058</strong><a href="javascript:;" onclick="openWinAuto('../peopleorders/goodsDetails.html','goodsDetails',1200,600)">湖南邵阳市至广西南宁市周口店：220挖机马上走…</a></h2>
							<div class="mainDetailBox cf">
								<div class="aboveContract fl">
									<label for="">托运方</label><span>XXXXXX工程机械制造公司，代理人何老师/18688886666</span>
								</div>
								<div class="aboveContract fl">
									<label for="">承运方</label><span>里老师/15888886666</span>
								</div>
								<div class="fl">
									<label for="">创建人</label><span>田老师</span>
								</div>
							</div>
						</div>
						<div class="operationBox">
							<h3 class="signState Underway">签约中</h3>
							<div class="checkLookCont">
								<i></i><span>查看运输合同</span>
							</div>
						</div>
					</div>
				</li>
				<li class="MessageListNav">
					<div class="innerMessageListNav">
						<div class="messageTimeBox">
							<span>12-01 09:12</span>
							<em>2015年</em>
						</div>
						<div class="mainContentBox">
							<h2><strong>合同编号17121800011058</strong><a href="javascript:;" onclick="openWinAuto('../peopleorders/goodsDetails.html','goodsDetails',1200,600)">湖南邵阳市至广西南宁市周口店：220挖机马上走…</a></h2>
							<div class="mainDetailBox cf">
								<div class="aboveContract fl">
									<label for="">托运方</label><span>XXXXXX工程机械制造公司，代理人何老师/18688886666</span>
								</div>
								<div class="aboveContract fl">
									<label for="">承运方</label><span>里老师/15888886666</span>
								</div>
								<div class="fl">
									<label for="">创建人</label><span>田老师</span>
								</div>
							</div>
						</div>
						<div class="operationBox">
							<h3 class="signState toVoid">已作废</h3>
							<div class="checkLookCont">
								<i></i><span>查看运输合同</span>
							</div>
						</div>
					</div>
				</li>
				<li class="MessageListNav">
					<div class="innerMessageListNav">
						<div class="messageTimeBox">
							<span>12-05 12:12</span>
							<em></em>
						</div>
						<div class="mainContentBox">
							<h2><strong>合同编号17121800011058</strong><a href="javascript:;" onclick="openWinAuto('../peopleorders/goodsDetails.html','goodsDetails',1200,600)">湖南邵阳市至广西南宁市周口店：220挖机马上走…</a></h2>
							<div class="mainDetailBox cf">
								<div class="aboveContract fl">
									<label for="">托运方</label><span>XXXXXX工程机械制造公司，代理人何老师/18688886666</span><em>(已签名)</em>
								</div>
								<div class="aboveContract fl">
									<label for="">承运方</label><span>里老师/15888886666</span><em>(已签名)</em>
								</div>
								<div class="fl">
									<label for="">创建人</label><span>田老师</span>
								</div>
							</div>
						</div>
						<div class="operationBox">
							<h3 class="signState takeEffect">已生效</h3>
							<div class="checkLookCont">
								<i></i><span>查看运输合同</span>
							</div>
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="bgOutLook" style="display: none;"></div>
		<!-- 添加备注消息 -->
		<div class="innerTitBox innerNotedescription" style="display: none;">
			<i class="closeOutBox"></i>
			<div class="titInnerBox">
				<h3 class="makedTit">备注说明</h3>
				<p class="makedDetil">备注信息内容便于后续跟进消息对应的业务</p>
				<div class="textareaBox">
					<textarea name="" id="" cols="30" rows="10" onkeyup="textCounter($(this),50)" placeholder="请输入备注说明"></textarea>
					<div class="textareaLengthNumber">
						<em class="descriptionNumber">0</em><span>/50</span>
					</div>
				</div>
				<div class="makedBtn">
					<button class="trueBtn" onclick="innerNotedescription();">保存</button>
					<button class="falseBtn">取消</button>
				</div>
			</div>
		</div>

		<!--此处引入footer.jsp-->
    <!--<jsp:include page="/back/jsp/footer.jsp" flush="true"/>-->
	</div>
	<!--插入创建合同-->
	<iframe id="openIframe" src="" frameborder="0"  allowtransparency="true" style="display: none;"></iframe>
</body>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script>

	$(".checkLookCont").click(function(event) {
		$("#openIframe").attr('src','../electContract/transportContract.html?loadEdit=168').show()
	});

	$(".operationBox .addRemarks").mouseenter(function(event) {
		$(this).addClass('addRemarksActive');
	});
	$(".operationBox .addRemarks").mouseleave(function(event) {
		$(this).removeClass('addRemarksActive');

	});

	$(".operationBox .removeNav").mouseenter(function(event) {
		$(this).addClass('removeNavActive');
	});
	$(".operationBox .removeNav").mouseleave(function(event) {
		$(this).removeClass('removeNavActive');
	});



	$(".addRemarks").click(function(event) {
		$(".innerNotedescription,.bgOutLook").show();
	});
	$(".removeNav").click(function(event) {
		$(".innerThisDelete,.bgOutLook").show();
	});
	$(".closeOutBox,.makedBtn .falseBtn").click(function(event) {
		$(".innerTitBox,.bgOutLook").hide();
	});

	$(".bgOutLook").click(function(event) {
		$(".innerTitBox,.bgOutLook").hide();
	});

	function innerThisDelete(){
		$(".innerTitBox,.bgOutLook").hide();
		showToast('删除失败', '')
	}
	function innerNotedescription(){
		$(".innerTitBox,.bgOutLook").hide();
		showToast('保存失败', '')
	}

	//字数长度监控
	function textCounter(thisInput, maxlimit) {   
		var showNumber = thisInput.parent('div').find('.descriptionNumber')
		if (thisInput.val().length > maxlimit) { 
			showNumber.html(thisInput.val().length);
			showNumber.css('color','red');
		} else {   
			showNumber.html(thisInput.val().length);
			showNumber.css('color','#333');
		}
	}  

	//动态载入tosta框
	function showToast(title, content) {
		$(".toastBox").remove();
		var _toast = '	<div class="toastBox">\
				<div class="toastBoxbg"></div>\
		<div class="toastBoxInner">\
					<i onclick="closeBlank($(this))"></i>\
					<h3>' + title + '</h3>\
					<p>' + content + '</p>\
				</div>\
			</div>';
		var speend;
		var _size = title.length + content.length
			// alert(_size)
		$('body').append(_toast)
		if (content == '' || title == '') {
			$(".toastBoxInner").css('margin-top', '36px');
			$(".toastBoxInner").css('margin-top', '36px');
		}
		speend = (_size / 4) * 1000
		setTimeout(function() {
			$(".toastBox").remove();
		}, speend);
	}

	// tosta框的"X"关闭
	function closeBlank(_this) {
		_this.parents('.toastBox').remove();
	}
</script>
</html>