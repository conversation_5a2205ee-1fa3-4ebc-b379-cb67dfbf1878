<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>权益赠送规则</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <!-- <link rel="stylesheet" href="../../css/luckyDraw/luckyList.css"> -->
    <link rel="stylesheet" href="../../css/infoFeeActivity/listStyle.css">
</head>

<body class="theme-blue">
    <!-- 主要内容开始 -->
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>订单激励活动</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">权益赠送规则</span>
        </h1>
        <div class="main-content">
            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                            <input type="button" onclick="commonO.operate('open','add')" class="button" value="新增规则">
                        </li>
                    </ul>
                </div>
                <div class="clearfix"></div>
                <div class="messageRight pageFun fr">查询到<em tname="total"></em>条信息，当前是第<span tname="current"></span>页，共<span tname="maxPage"></span>页</div>            </div>
            </div>

            <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i>
                <div class="receiptInner cf"><span></span><em>复制</em></div>
            </div>
            <div class="dataTable">
                <table border="0" class="tableBox" id="ruleList"></table>
            </div>
        </div>
        <!--JQ分页-->
        <div id="pageNumberBigBox" class="cf">
            <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
        </div>
    </div>
    <div class="outBgColor" style="display: none;"></div>
    <div class="outLook ruleOutLook" style="display: none;">
        <div class="innerLook">
            <h2 class="luckName"></h2>
            <ul class="luckBox">
                <li class="activityNameWarp">
                    <input type="hidden" tname="id" value="">
                    <label for="">活动名称：</label>
                    <span id="activitySpanWarp" style="display: none;"></span>
                    <select tname="activityId" id="activityWarp" style="display: none;"></select>
                </li>
                <li>
                    <label for="">权益赠送：</label>
                    <div class="ruleConfigWarp">
                        <button onclick="ruleO.addConfig()">新增</button>
                        <ul class="ruleConfig"></ul>
                    </div>
                </li>
                
            </ul>
            <p></p>
            <div class="ideabtnBox luckbtnBox cf">
                <button onclick="commonO.operate('close')" class="close_block fl">取消</button>
                <button onclick="ruleO.saveRule()" class="close_block fr submitActie">确认</button>
            </div>
        </div>
        <i class="closelookBtn" onclick="commonO.operate('close')">×</i>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/infoFeeActivity/infoFeeCommon.js"></script>
<script src="../../js/infoFeeActivity/ruleList.js"></script>

</html>