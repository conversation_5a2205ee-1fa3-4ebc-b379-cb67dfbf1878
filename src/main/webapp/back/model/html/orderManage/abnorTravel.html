<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>车辆轨迹</title>
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/detailNav.css">
    <link rel="stylesheet" href="../../css/carDetails.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
</head>
<style>
    .searchBtn li input {
        font-size: 14px;
        line-height: 32px;
        height: 32px;
        padding: 0 12px;
        color: #fff;
        border: none 0;
        border-radius: 4px;
        background: #3961b2;
    }
    .addWhiteUser ul li label{
        width: 112px;
        padding-right: 0;
    }
    .imgBox img{
        margin: 10px;
        width: 150px;
        height: 80px;
    }
    #allmap {
        width: 100%;
        height: 100%;
        overflow: hidden;
        margin:0;
        font-family:"微软雅黑";
    }
</style>
<body>
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>车辆详情</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">查看轨迹</span><span>&nbsp;&gt;&nbsp;</span><span>详情页</span>
    </h1>
    <div id="allmap"></div>
    <div class="searchBtn"> 
        <ul class="btnBox cf">
            <li>
                <input type="button" class="button" value="取消" onclick="tytTool.closeWin()">
            </li>
        </ul>
    </div>
</div>
</body>
<script src="http://api.map.baidu.com/api?v=2.0&ak=2NZa1O1V3BHmsDlX9fdomGaO3S5b1AEo"></script>
<script src="../../js/orderManage/abnorTravel.js"></script>
</html>