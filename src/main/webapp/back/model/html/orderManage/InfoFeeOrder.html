<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>信息费订单</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <!-- <link rel="stylesheet" href="../../css/busyEnter.css"> -->
    <style type="text/css">

    </style>
</head>

<body>
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span>
            <span>订单管理</span>
            <span>&nbsp;&gt;&nbsp;</span>
            <span class="currentPage">信息费订单</span>
        </h1>
        <div class="main-content">

            <!--搜索条件-->
            <div class="searchBox" id="searchCondition">
                <ul class="searchUl searchUlNone cf">
                    <li>
                        <label>运单号：</label>
                        <input id="userId" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                    <li>
                        <label>订单状态：</label>
                        <select name="clientSign" id="clientSign" style="width: 100px;">
                            <option class="firstOption" value="">全部</option>
                            <option value="1">运输中</option>
                            <option value="2">已完成</option>
                            <option value="3">已取消</option>
                        </select>
                    </li>
                    <li>
                        <label>发货人：</label>
                        <input id="newPhone" type="text" class="input90" name="" maxlength="11" value="" oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" />
                    </li>
                    <li>
                        <label>发货人手机号：</label>
                        <input id="optName" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                    <li>
                        <label>货物名称：</label>
                        <input id="userId" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                    <li>
                        <label>货物类别：</label>
                        <input id="cellPhone" type="text" class="input90" name="" maxlength="11" value="" oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" />
                    </li>
                    <li>
                        <label>出发地：</label>
                        <input id="newPhone" type="text" class="input90" name="" maxlength="11" value="" oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" />
                    </li>
                    <li>
                        <label>目的地：</label>
                        <input id="optName" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                    <li>
                        <label>发布时间：</label>
                        <input id="optName" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                    <li>
                        <label>签单时间：</label>
                        <input id="userId" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                    <li>
                        <label>承运人：</label>
                        <input id="cellPhone" type="text" class="input90" name="" maxlength="11" value="" oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" />
                    </li>
                    <li>
                        <label>承运人手机号：</label>
                        <input id="newPhone" type="text" class="input90" name="" maxlength="11" value="" oninput="tytTool.onInput($(this),11)" onafterpaste="tytTool.onAfterPaste($(this))" />
                    </li>
                    <li>
                        <label>车头牌号：</label>
                        <input id="optName" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                    <li>
                        <label>挂车牌号：</label>
                        <input id="optName" type="text" class="input90" name="" maxlength="15" value="" />
                    </li>
                </ul>
            </div>
            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                            <input id="busyEntOptfnBtn" data-click="0" type="button" onclick="busyEntOptfn.checkForm($(this))" class="button" value="查询">
                        </li>
                        <li>
                            <input class="" id="resetForm"  type="button" value="重置" onclick="tytTool.resetAllForm($('.searchBox'),$('#busyEntOptfnBtn'))">
                        </li>
                    </ul>
                </div>
                <div class="clearfix"></div>
                <div class="messageRight fr">查询到
                    <em id="rowCount"></em>条信息，当前是第
                    <span id="currentPage"></span>页，共
                    <span id="totalPages"></span>页
                </div>
            </div>

            <div id="receiptInfo" class="receiptInfo" style="display: none;">
                <i></i>
                <div class="receiptInner cf">
                    <span></span>
                    <em>复制</em>
                </div>
            </div>
            <div class="dataTable">
                <table border="0" class="tableBox" id="accountChange">
                </table>
            </div>
        </div>

        <!--JQ分页-->
        <div id="pageNumberBigBox" class="cf">
            <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
        </div>
        <div id="componentBox"></div>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/orderManage/InfoFeeOrder.js"></script>
<script>
    tytTool.bindHidePhone();

    $(".tableBox").colResizable({
        liveDrag: true,
        draggingClass: "dragging",
        resizeMode: 'flex',
        minWidth: 80
    });
</script>

</html>