<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>车辆管理</title>
    <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
    <link href="../../css/dispatchConfigure/serviceChargeSettings.css" type="text/css" rel="stylesheet">
</head>
<body>
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>平台策略配置</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">平台技术服务费配置</span>
    </h1>
    <div class="content">
        <div class="tab">
            请选择技术服务费设置方式：
            <input type="radio" id="freight" name="myradio" value="0" onclick="serviceO.changeTab($(this))"> <label for="freight"> 按运费设置比例</label>
            <input type="radio" id="ladder" name="myradio" value="1" onclick="serviceO.changeTab($(this))"> <label for="ladder"> 按阶梯设置比例</label>
        </div>
        <div class="tab-content">
            <!-- 修改运费框 -->
            <div class="setting-box setting-one form-inline">
                <p class="text-warning mb-20">提示：运费10000元，技术服务费设 2%，技术服务费折扣设 70% ，实收技术服务费：140 元</p>
                按运费金额收取技术服务费：<input type="text" tname="cost" class="form-control serviceRes" disabled>% 可设置0~100 整数
                <span>折扣：<input type="text" tname="discount" class="form-control integer" disabled>%</span>
            </div>
            <!-- 设置比例框 -->
            <div class="setting-box setting-two panel">
                <p class="text-warning">提示：阶梯金额不能有重叠 <button class="button btn-primary addBtn" onclick="serviceO.addLi()" disabled> 新增</button></p>
                <div class="panel-body">
                    <ul class="form-inline checkPrice" id="submitForm"></ul>
                </div>
            </div>
        </div>
        <!--   修改  -->
        <div class="submit-box">
            <button class="button btn-primary change-btn" onclick="serviceO.changeBtn()">修改</button>
            <button class="button btn-success save-btn" onclick="serviceO.saveFn()">保存</button>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../../js/jqpaginator.min.js"></script>
<script type="text/javascript" src="../../js/tyt_common.js"></script>
<script type="text/javascript" src="../../js/dispatchConfigure/serviceChargeSettings.js"></script>
</html>