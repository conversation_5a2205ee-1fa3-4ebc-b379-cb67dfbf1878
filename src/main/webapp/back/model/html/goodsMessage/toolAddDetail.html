<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="description" content="">
	<meta name="author" content="">
	<title></title>
<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
<link href="../../css/identity.css" type="text/css" rel="stylesheet">
<!-- <link href="back/jsp/identityAuth/css/identity.css" type="text/css" rel="stylesheet"> -->
</head>
<body>
 <div class="contentBox">
		<div class="titUrl cf">
			<h1 class=" fl">
				<span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span>看货条数控制(v5600+)</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage"></span>
			</h1>
		</div>
		<div class="main-content">
	       <p class="useCareful">使用说明：本功能支持按不同的<span>销售审核一级身份</span>，对拨打货源电话按照【身份认证】，【车辆认证】，【会员缴费】进行不同条数的控制</p>
		    <!--搜索条件-->
		    <div class="searchCondition">
		      <ul class="o-hidden hiddenList">
			      <li class="useSelect">
			        <label>销售审核一级身份</label>
			        <select id="deliverTypeOne" style="width: 130px;">
			          <option class="firstOption" value="-1">默认身份</option>
			        </select>
			      </li>
		      </ul>
			    <div class="numList cf">
			    	<div class="fl">
						<label>身份认证控制条数</label>
						<input type="text" id="identityAuthNum" value="">
						<i class="mustRed">*</i>
			    	</div>
			    	<div class="fl">
						<label>会员缴费控制条数</label>
						<input type="text" id="vipNum" value="">
						<i class="mustRed">*</i>
			    	</div>
			    	<div class="fl">
						<label>车辆认证控制条数</label>
						<input type="text" id="carAuthNum" value="">
						<i class="mustRed">*</i>
			    	</div>
			    </div>
		    </div>
		    <div class="errBox" id="errBoxId"></div>
		    <div class="subBtn">
		      <button class="button" onclick="goodsToolMyopt.updateGoods();" style="background-color: #3961b2">保存</button>
		    </div>
		</div>
	</div>
</body>

<script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
<!-- <script type="text/javascript" src="back/model/js/bootstrap.js"></script> -->
<!-- <script type="text/javascript" src="back/jsp/identityAuth/js/goodJurisdiction.js"></script> -->
<script src="../../js/tyt_common.js"></script>
<script src="../../js/goodsTool.js"></script>
</html>
