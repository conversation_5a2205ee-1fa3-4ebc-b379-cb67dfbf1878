<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="description" content="">
	<meta name="author" content="">
	<title>看货条数控制(v5600+)</title>
<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
<link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
<!-- <link href="back/jsp/identityAuth/css/identity.css" type="text/css" rel="stylesheet"> -->
<link href="../../css/identity.css" type="text/css" rel="stylesheet">
<link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
<!-- <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet"> -->

</head>
<body>
    <div class="contentBox">
		<div class="titUrl cf">
			<h1 class=" fl">
				<span>当前所在位置：</span><span>货源信息管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">看货条数控制(v5600+)</span>
			</h1>
		</div>
		<div class="main-content"> 
	       <div class="bg-info clearfix">
	          <p class="pull-left" style="margin-top:7px">使用说明：本功能支持按不同的销售审核一级身份，对拨打货源电话按照【身份认证】，【车辆认证】，【会员缴费】进行不同条数的控制</p>
	          <div class="exlain_btns pull-right w100">
	              <!-- <button type="button" class="btn btn-primary pull-left">刷新</button> -->
	              <button type="button" class="btn btn-primary pull-left ml30" onclick="tytTool.openWinAuto('toolAddDetail.html','opwin',1200,600)">新增</button>
	          </div> 
	       </div>
	        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
	       <!--内容列表-->
			<div class="dataTable" id="powerTable">			
				<table width="100%" border="0" class="tableBox" id="goodsTool">
					<tr class="tableTh">
	          <th width="120">身份</th>
	          <th>可拨打货源条数</th>
	          <th>修改时间</th>
	          <th>修改人</th>
	          <th width="120">操作</th>
	        </tr>
	      </table>
	  </div>
	      <p class="remarks">备注：除列表里说明的身份以外，都按照默认身份项控制逻辑走，默认身份项不能删除，只能修改</p>
		</div>
	</div>
	<!-- 警告框 -->
	<div class="alert alert-danger fade in" id="alert_danger" role="alert" style="display: none">
      <button type="button" class="close" aria-label="Close" id="close_btn"  onclick="homeMyfn.btnDefault()">×</button>
      <h4>提示</h4>
      <p class="alertContent">删除后，将会影响用户的找货流程，请慎重</p>
      <p class="tc buttonBox">
        <button type="button" class="button" id="btn_danger" onclick="homeMyfn.deleteGoods($(this))">确定</button>
        <button type="button" class="button ml10" id="btn_default" onclick="homeMyfn.btnDefault()">取消</button>
      </p>
	</div>
</body>
<script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../../js/tyt_common.js"></script>
<script type="text/javascript" src="../../js/toolHome.js"></script>
</html>
