<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>车险-车险询价</title>
    <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
    <style type="text/css">
        .dataTable{
              overflow:hidden;
          }  
  </style>
</head>
<body>
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>保险业务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">车险-车险询价</span>
    </h1>
    <div class="searchBox">
        <ul class="searchUl searchUlNone cf">
            <li>
                <label for="">联系电话：</label>
                <input id="linkPhone" type="text" maxlength="11" name="linkPhone" >
            </li>
            <li>
                <label for="">注册人姓名：</label>
                <input id="userName" type="text" maxlength="25"  name="userName" >
            </li>
            <!-- <li>
                <label for="">险种选择：</label>
                <select name="insuranceType" id="insuranceType" style="width: 100px;">
                    <option class="firstOption" value="">请选择</option>
                </select>
            </li> -->
            <li>
                <label for="">车头车牌号：</label>
                <input id="carHeadNumber" type="text" maxlength="14" name="carHeadNumber" >
            </li>
            <li>
                <label for="">业务人员：</label>
                <select name="serviceUserId" id="serviceUserId">
                    <option class="firstOption" value="">请选择</option>
                </select>
            </li>
            <li>
                <label for="">询价状态：</label>
                <select name="dealStatus" id="dealStatus">
                    <option class="firstOption" value="">请选择</option>
                </select>
            </li>
            <li>
                <label for="">提交时间：</label>
                <input id="commitTimeQuery" onclick="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})"  type="text" name="commitTimeQuery" value="">
            </li>
            <li>
                <label for="">处理时间：</label>
                <input id="dealTimeQuery" onclick="WdatePicker({readOnly:true,dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})" type="text" name="dealTimeQuery" value="">
            </li>
        </ul>
    </div>
    <div class="pageMessage">
        <div class="searchBtn">
            <ul class="btnBox cf">
                <li><input class="" id="queryForm"  type="button" value="查询" onclick="carInsuranceOptfn.queryForm()"></li>
                <li><input class="" id="resetForm"  type="button" value="重置" onclick="tytTool.resetAllForm($('.searchBox'),$('#queryForm'))"></li>
                <li><input class="" id="exportFile" type="button" value="导出" onclick="carInsuranceOptfn.exportFile()"></li>
            </ul>
        </div>
        <div class="clearfixbtnSure"></div>
        <div class="messageRight fr">查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span id="totalPages"></span>页
        </div>
    </div>
    <div class="dataTable">
        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
        <table border="0" cellpadding="0" cellspacing="0" class="tableBox" id="carInsuranceList">
            <tr class="tableTh">
                <th>操作</th>
                <th>车险ID</th>
                <th>注册手机号</th>
                <th>联系电话</th>
                <th>注册人姓名</th>
                <th>车头车牌号</th>
                <!-- <th>选择险种</th>
                <th>选择保项及保额</th> -->
                <th>提交时间</th>
                <th>跟踪人员</th>
                <th>询价状态</th>
            </tr>
        </table>
    </div>
    <!--JQ分页-->
    <div id="pageNumberBigBox" class="cf">
        <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>
</div>
</body>
<script>
    window.JSESSION_ID_TRUE = true;
</script>
<script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="../../js/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../../js/jqpaginator.min.js"></script>
<script type="text/javascript" src="../../js/tyt_common.js"></script>
<script type="text/javascript" src="../../js/carInsurance.js"></script>
<script type="text/javascript" src="../../../js/common_openwin.js"></script>
</html>