<!--
 * @Description: 财务管理>提现余额提醒
 * @Version: 
 * @Autor: houchuanfang
 * @Date: 2021-03-31 14:25:11
 * @LastEditTime: 2021-04-06 17:53:37
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title id="pageName">提现余额提醒</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <style>
        .main-content{
            margin-top: 20px;
        }
        .main-content ul li,.main-content ul li label,.outLook ul.innerLook-con li{
            font-size: 14px;
            color: #333333;
            font-weight: normal;
        }
        .main-content ul li label{
            display: inline-block;
            min-width: 100px;
        }
        .main-content ul li{
            display: -webkit-box;
            display: -moz-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
            margin-bottom: 25px;
        }
        .main-content ul li span:nth-of-type(1){
            margin-right: 20px;
        }
        .main-content ul li span.font-w{
            font-weight: bold;
        }
        .main-content ul li button{
            width: 100px;
            font-size: 14px;
            line-height: 32px;
            height: 32px;
            padding: 0 16px;
            color: #fff;
            border: none 0;
            border-radius: 4px;
            background: #3961b2;
            text-align: center;
            cursor: pointer;
        }
        .outLook{
            min-height: 170px;
            height: auto;
            padding-bottom: 25px;
            transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            -o-transform: translateY(-50%);
            margin-top: inherit;
        }
       .outLook ul.innerLook-con{
            padding: 35px 20px 0;
        }

        .outLook ul.innerLook-con li{
            margin-bottom: 15px;
        }
        .outLook ul.innerLook-con li label{
            display: inline-block;
            min-width: 80px;
            float: left;
            font-weight: normal;
        }
        .outLook ul.innerLook-con li>div{
            width: 100%;
            padding-left: 80px;
        }
        .outLook ul.innerLook-con li input,.outLook ul.innerLook-con li textarea{
            outline: none;
            margin-right: 10px;
            float: left;
            width: 100%;
            padding: 5px 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            color: #333;
        }
        .outLook ul.innerLook-con li input{
            height: 30px;
        }
        .outLook ul.innerLook-con li p.tips{
            font-size: 12px;
            color:#E6A23C;
            display: inline-block;
            margin-top: 5px;

        }
    </style>
</head>
<body>
<!-- 主要内容开始 -->
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>财务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">提现余额提醒</span>
    </h1>
    <div class="main-content">
        <ul>
            <li>
                <label>余额提醒</label>
                <button onclick="remindO.operate('open')">修改</button>
            </li>
            <li>
                <label>限制额度</label>
                <p><span>当提现垫资余额&lt;</span><span class="font-w" id="limit-box"></span> 元时</p>
            </li>
            <li>
                <label>接收手机号</label>
                <p><span>发送提醒信息到</span><span class="font-w" id="tels-box"></span></p>
            </li>
        </ul>
    </div>
    <!--    弹框⬇-->
    <div class="outBgColor" style="display: none;"></div>
    <div class="outLook delOutLook" style="display: none;">
        <div class="innerLook">
            <h2 class="luckName">余额提醒修改</h2>
            <ul class="innerLook-con">
                <li class="clearfix">
                    <label>限制额度</label>
                    <div><input type="text"  id="limit"  oninput="this.value = remindO.clearNoNum(this.value),remindM.originLimitVal = this.value;"  onblur="this.value = remindO.toPrice(this.value),remindM.formatLimitVal = this.value;"/></div>
                </li>
                <li class="clearfix">
                    <label>接收手机号</label>
                    <div>
                        <textarea name="" id="tels" cols="30" rows="3" ></textarea>
                        <p class="tips">多个手机号之间用“,”分隔</p>
                    </div>
                </li>
            </ul>
            <div class="ideabtnBox luckbtnBox cf">
                <button onclick="remindO.operate('close')" class="close_block fl">取消</button>
                <button  class="close_block fr" onclick="remindO.modify()">修改</button>
            </div>
        </div>
        <i class="closelookBtn" onclick="remindO.operate('close')">×</i>
    </div>
    <!--    弹框⬆-->
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="../../js/My97DatePicker/WdatePicker.js" ></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/payManage/payRemind.js"></script>
</html>