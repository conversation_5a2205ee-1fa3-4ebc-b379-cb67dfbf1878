<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>代金券配置管理-编辑</title>
	<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="../../css/upgrade.css" type="text/css" rel="stylesheet">

  <script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="../../../jurisdiction/jsp/js/common.js"></script>
  <script type="text/javascript" src="../../js/My97DatePicker/WdatePicker.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>代金券管理</span><span>&nbsp;&gt;&nbsp;</span><span>代金券配置管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">编辑</span>
		</h1>
		<div class="systemParameter">
	    <div class="pageContext">
	      <ul class="ideaFinal" id="">
              <li>
                  <label for=""><i class="xing">*</i>代金券ID：</label>
                  <span id="couponTypeId"></span>
                  <span class="lineError"></span>
              </li>
	      	<li>
	      		<label for=""><i class="xing">*</i>代金券名称：</label>
	      		<input id="couponName" type="text" maxlength="16" placeholder="请输入代金券名称，最多16个汉字">
                <span class="lineError"></span>
	      	</li>
              <li>
                  <label for=""><i class="xing">*</i>代金券数量：</label>
                  <span id="couponQty"></span>
                  <span class="lineError"></span>
              </li>
              <li>
                  <label for=""><i class="xing">*</i>代金券金额：</label>
                  <span id="couponAmount"></span> 元
                  <span class="lineError"></span>
              </li>
              <li>
                  <label for="">代金券描述：</label>
                  <input id="couponDesc" type="text" maxlength="100" >
              </li>
              <li>
                  <label for=""><i class="xing">*</i>使用范围：</label>
                  <span id="useScopeType"></span> <span id="useScopeDetail"></span>
                  <span class="lineError"></span>
              </li>

                <div class="tc mt60">
                    <input  id="returnBtn" class="button" type="button" onclick="window.close();" value="取消">
                    <input id="btn" type="button" class="button" value="编辑完成" onclick="couOptfn.couponUpdate();">
                </div>
          </ul>
		</div>
	</div>
 </div>
</body>
<script src="../../js/tyt_common.js"></script>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/couponEdit.js"></script>
<script>
    function loadDom() {
        var _html ="";
        var useScopeType = $("#useScopeType").val();
        if (useScopeType==1){
            _html = `<li id="useScopeDetail" data-clientType=''><p><input type="checkbox" name="useScopeDetail" value="一年会员" />一年会员 </p><p><input type="checkbox" name="useScopeDetail" value="两年会员" />两年会员 </p><p><input type="checkbox" name="useScopeDetail" value="三年会员" />三年会员 </p></li>`
        }else if(useScopeType==2){
            _html = `<li id="useScopeDetail" data-clientType=''><p><input type="checkbox" name="useScopeDetail" value="人保货运险" />人保货运险 </p><p><input type="checkbox" name="useScopeDetail" value="华泰货运险" />华泰货运险 </p></li>`
        }
        $("#useScopeDetailDiv").append(_html);
    }
    $("select[name='useScopeType']").change(function(){
        var useScopeType=$("select[name='useScopeType']").val();
        if (useScopeType.length>0) {
            $('#useScopeDetailLi').find($('#useScopeDetail')).remove();
            if (useScopeType==1){
                _html = `<li id="useScopeDetail" data-clientType=''><p><input type="checkbox" name="useScopeDetail" value="一年会员" />一年会员 </p><p><input type="checkbox" name="useScopeDetail" value="两年会员" />两年会员 </p><p><input type="checkbox" name="useScopeDetail" value="三年会员" />三年会员 </p></li>`
            }else if(useScopeType==2){
                _html = `<li id="useScopeDetail" data-clientType=''><p><input type="checkbox" name="useScopeDetail" value="人保货运险" />人保货运险 </p><p><input type="checkbox" name="useScopeDetail" value="华泰货运险" />华泰货运险 </p></li>`
            }else{
                _html = `<li id="useScopeDetail" data-clientType=''></li>`
            }
            $("#useScopeDetailDiv").append(_html);
        }
    });
//执行脚本入口:
loadDom();
</script>
</html>