<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title id="pageName">查看交易规则</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/detailNav.css">
    <link rel="stylesheet" href="../../css/carDetails.css">
    <link rel="stylesheet" href="../../css/tradingRules/rulesDetail.css">
</head>
<body class="theme-blue">
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>推广功能管理</span><span>&nbsp;&gt;&nbsp;</span><span>交易规则配置管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">查看</span>
        </h1>

        <div class="detailWarp">
            <!-- 经销商信息 -->
            <div class="messageTit">
                <h2 class="">规则信息</h2>
            </div>
            <div class="detailBox">
                <ul class="detailListBox cf">
                    <li class="oneLine fl">
                        <label for="" class="">业务端：</label>
                        <span id="ruleFlag"></span>
                    </li>
                    <li class="oneLine fl">
                        <label for="" class="">规则名称：</label>
                        <span id="" tname="ruleTitle"></span>
                    </li>
                    <li class="oneLine fl">
                        <label for="" class="">规则类型：</label>
                        <span id="" tname="ruleTypeName"></span>
                    </li>
                    <li class="oneLine fl">
                        <label for="" class="">排序值：</label>
                        <span id="" tname="sort"></span>
                    </li>
                    <li class="oneLine fl">
                        <label for="" class="">规则状态：</label>
                        <span id="ruleState" tname="status" switch-item="1##启用,2##禁用"></span>
                    </li>

                    <li class="oneLine fl">
                        <label for="" class="">规则内容：</label>
                        <div class="ruleContent"></div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!-- 按钮 -->
    <div class="searchBtn customerBtn">
        <ul class="btnBox cf">
            <li class="backList">
                <input type="button" class="button" onclick="tytTool.closeWin()" value="确定">
            </li>
        </ul>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/tradingRules/common.js"></script>
<script src="../../js/tradingRules/rulesDetail.js"></script>
</html>