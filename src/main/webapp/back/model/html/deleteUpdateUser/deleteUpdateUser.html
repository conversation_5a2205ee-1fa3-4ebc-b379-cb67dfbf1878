<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title id="pageName">经销商小程序列表</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <style>
        .searchUl li {
            width: 300px;
        }
        .upn {
            width: 200px !important;
            -moz-appearance: textfield;
        }
        input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{
            -webkit-appearance: none;
            margin: 0;
        }
        .addWhiteUser{
            text-align: center;
        }
    </style>
</head>

<body class="theme-blue">
    <!-- 主要内容开始 -->
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>经销商小程序</span><span>&nbsp;&gt;&nbsp;</span><span
                class="currentPage">销售订单管理列表</span>
        </h1>
        <div class="main-content">
            <!--搜索条件-->
            <div class="searchBox" id="searchCondition">
                <ul class="searchUl searchUlNone cf">
                    <li>
                        <label>用户手机号：</label>
                        <input type="number" oninput="if(value.length>11)value=value.slice(0,11)" class="upn" id="cellPhone" tname="cellPhone" maxlength="11">
                    </li>
                </ul>
            </div>
            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                            <input type="button" onclick="couRecOptfn.getListMsg()" class="button" value="查询">
                        </li>
                        <li>
                            <input class="" id="resetForm" type="button" value="重置"
                                onclick="tytTool.formReset($('.searchUl'))">
                        </li>
                    </ul>
                </div>
                <div class="clearfix"></div>
            </div>
            <div class="dataTable">
                <table border="0" cellpadding="0" cellspacing="0" class="tableBox" id="couponRecordList">
                    <tr class="tableTh">
                        <th>编号</th>
                        <th>用户id</th>
                        <th>用户名称</th>
                        <th>版本</th>
                        <th>机器码</th>
                        <th>内存(MB)</th>
                        <th>cpu核心数</th>
                        <th>cpu频率(GHZ)</th>
                        <th>cpu型号</th>
                        <th>操作系统</th>
                        <th>是否配置升级</th>
                        <th>操作</th>
                    </tr>
                    <tbody id="duuBarList"></tbody>
                </table>
            </div>
        </div>
        <!--JQ分页-->
        <div id="pageNumberBigBox" class="cf">
            <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
        </div>


        <div class="alert alert-danger fade in alert-height" style="display: none;" id="duuModal">
            <div class="innerLook">
                <h2>确认</h2>
                <div class="addWhiteUser cf tip">
                    确定要删除么？
                </div>
                <div class="makeSureBtn closePayModalBtn" style="height:32px;margin: 10px auto 0;text-align: center;">
                    <input class="button confirm" id="confimBtn" type="button" value="确定">&nbsp;&nbsp;&nbsp;&nbsp;
                    <input class="button" type="button" onclick="couRecOptfn.closeRefundModal()" value="取消">
                </div>
            </div>
            <i class="closelookBtn" onclick="couRecOptfn.closeRefundModal()">×</i>
        </div>
    </div>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/jquery-ui-1.10.4.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script type="text/javascript" src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script>
var pageSize = 30;
var couRec = {
    arr: {
        getListData: {
            url: '',
            otherUrl: '',
            type: 'get',
            contentType: 'application/json; charset=utf-8',
            data: ''
        }
    },
    ajaxfn: {
        getListData: function(data) {
            if (data.code == 200) {
                var _data = data.data;
                var _html = '';
                if (_data && _data.length > 0) {
                    $.each(_data, function(index, el) {
                        _html += `<tr>
                            <td>${index+1}</td>
                            <td>${tytTool.NullObj(_data[index].userId)}</td>
                            <td>${tytTool.NullObj(_data[index].userName)}</td>
                            <td>${tytTool.NullObj(_data[index].clientVersion)}</td>
                            <td>${tytTool.NullObj(_data[index].machineUid)}</td>
                            <td>${tytTool.NullObj(_data[index].memorySize)}</td>
                            <td>${tytTool.NullObj(_data[index].cpuCore)}</td>
                            <td>${tytTool.NullObj(_data[index].cpuFrequency)}</td>
                            <td>${tytTool.NullObj(_data[index].cpuModel)}</td>
                            <td>${tytTool.NullObj(_data[index].osName)}</td>
                            <td>${_data[index].upgradeId == null?'无':_data[index].upgradeId}</td>
                            <td>${_data[index].upgradeId == null?'':'<a id="'+_data[index].upgradeId+ '" class="info_detail ver_block duu_btn">删除</a>'}</td>
                        </tr>`
                    });
                    $("#duuBarList").html(_html);
                    tytTool.bindHidePhone()
                    couRecOptfn.duuFun();
                }else{
                    _html = '<tr><td colspan="'+ $(".tableTh th").length +'">暂无查询数据</td></tr>'
                    $("#duuBarList").html(_html);
                }
            }else{
                if(tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        }
    },
    optFn: {
        duuFun:function(){
            $('.duu_btn').click(function(){
                var id = $(this).attr('id');
                $('#duuModal').show()
                $('#confimBtn').attr('id',id)
            })

            $('#confimBtn').click(function(){
                var id = $(this).attr('id');
                couRecArr.getListData.url = '/upgrade/deleteTytUserUpgradeById?currentPage='+'&upgradeId=' + id
                tytTool.ajaxFn(couRecArr.getListData, function(data){
                    if (data.code == 200) {
                        tytTool.toastShow(data.msg, 4000)
                        couRecOptfn.getListMsg()
                        $('#duuModal').hide()
                    }else{
                        if(tytTool.NullObj(data.msg) != '') {
                            tytTool.toastShow(data.msg, 4000)
                        }
                    }
                })
            })
        },
        getListMsg: function(currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            couRecArr.getListData.url = '/upgrade/queryUserHardwareList?currentPage=' + currentPage + '&pageSize=' + pageSize + '&cellPhone=' + $("#cellPhone").val()
            tytTool.ajaxFn(couRecArr.getListData, couRecAjaxfn.getListData)
        },
        checkForm: function() {
            var arr = {
                "totalPages": 1,
                "pageSize": pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": couRecOptfn.getListMsg
            }
            tytTool.addResetIdent($('.searchUl'))
            tytTool.jqPaginator('#pageNumberBox', arr)
        },
        closeRefundModal:function(){
            $('#duuModal').hide()
        }
    }
}

var couRecArr = couRec.arr;
var couRecAjaxfn = couRec.ajaxfn;
var couRecOptfn = couRec.optFn;

$(function() {
    var arrAll = {
        "totalPages": 1,
        "pageSize": pageSize,
        "visiblePages": 7,
        "currentPage": 1,
        "funName": couRecOptfn.getListMsg
    }
    tytTool.jqPaginator('#pageNumberBox', arrAll);
})
</script>
</html>