<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>定制板车参考信息</title>
    <link rel="stylesheet" href="../../css/tableBase.css">
    <!-- <link rel="stylesheet" href="../../css/main-css.css"> -->
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/diyCar.css">
</head>
<body>
<div id="relBox" class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>经销商小程序</span><span>&nbsp;&gt;&nbsp;</span><span>板车菜单数据</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">定制板车参考信息</span>
    </h1>
    <div class="modelBtnBox">
        <div class="searchBtn" style="margin: 0;">
            <ul class="btnBox cf">
                <li>
                    <input type="button" class="button" value="批量导入" onclick="diyO.showHide('excelShow')">
                </li>
                <li id="addSub">
                    <input type="button" class="button" value="批量导出" onclick="diyO.exportOut()">
                </li>
            </ul>
        </div>
    </div>

    <div class="dataTable">
        <table border="0" class="tableBox" id="diyCar"></table>
    </div>
    

	<!-- 文件导入 -->
	<div class="bgOutLook" style="display: none;"></div>
	<div class="outLook excelBox" style="display: none;">
			<div class="innerLook">
				<h2>请选择文件</h2>
		    <p class="alertContent">请点击“浏览”选择需要导入的模板文件</p>
				<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
					<div class="fileBtnBox fl"> 
							<!-- <input type="file" id="validToken" name="validToken" value="" style="display: none;"> -->
							<input class="fileBtn" id="configFile" type="file" name="configFile" accept=".xls,.xlsx" onchange="diyO.fileInfo(this,$(this))">
						<button class="rule_true">浏览</button>
					</div>
					<button id="uploadFile" onclick="diyO.uploadFile()" class="close_block fr">上传</button>
				</div>
			</div>
			<i class="closelookBtn" onclick="diyO.showHide('excelHide')">×</i>
	  </div>
      
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<!-- <script src="../../js/xlsx.js"></script> -->
<script src="../../js/dealerBusiness/common.js"></script>
<script src="../../js/dealerBusiness/diyCar.js"></script>

</html>