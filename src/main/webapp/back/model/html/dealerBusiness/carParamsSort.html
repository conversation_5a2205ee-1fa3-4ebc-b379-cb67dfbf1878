<!-- 业务流水管理 -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title id="pageName">板车菜单数据管理</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">

    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache">
    <meta http-equiv="Expires" content="-1">

    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/carMarket/carData.css">
    <style>
        .checkBox > li {
            position: relative;
        }
        .checkBox > li label {
            width: 90px;
            text-align: right;
            position: absolute;
            left: 0;
        }
        .table-span{
            background: #eee;
            border-radius: 5px;
            display: inline-block;
            padding: 5px 10px;
            margin-right: 10px;
        }
        .info_opt{
            margin-right: 10px;
        }
        .tip-txt-e{
            background: rgb(254, 249, 237);
            border: 1px solid rgb(254, 233, 197);
            color: #B8741A;
            padding: 5px 10px;
        }
        .drag-box{
            background: #f7f7f7;
            padding: 20px;
            margin: 10px 0;
        }
        .listBox li{
            background: #fff;
            cursor: pointer;
            user-select: none;
        }
        .info_opt{
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 0 14px;
            line-height: 26px;
            display: inline-block;
            height: 26px;
            margin-right: 4px;
        }
        .info_add,.info_add:hover{
            color: rgb(22, 155, 213);
            border-color: rgb(22, 155, 213);
        }
        .info_remove,.info_remove:hover{
            color: rgb(236, 128, 141);
            border-color: rgb(236, 128, 141);
        }
        .caoz-btn{
            width: 160px;
            display: inline-block;
            text-align: right;
            user-select: none;
            position: absolute;
            right: 0;
        }
        .listBox {
            max-width: calc(100% - 230px);
            margin: 0 160px 0 90px;
            width: 100%;
        }
        .tb_btn{
            border: none;
        }
        .argInput{
            height: 30px;
            border:1px solid #999;
        }
        .inputBox {
            text-align: center;
            line-height: 20px;
            font-size: 14px;
            color: #333;
            margin: 60px 0 0;
        }
        .inputBox label {
            display: inline-block;
            width: 120px;
            height: 28px;
            line-height: 28px;
            text-align: right;
            vertical-align: middle;
        }
        .inputBox select {
            display: inline-block;
            width: 200px;
            height: 28px;
            line-height: 28px;
            vertical-align: middle;
            border-radius: 4px;
            outline: none;
            padding: 0 10px;
        }
        .liClose{
            color: #333;
            position: absolute;
            top: 0px;
            right:0px;
            font-size:15px;
            display: none;
        }
        .listBox li{
            position: relative;
        }
        .btn-save,.btn-save:hover,
        .btn-cancel{
            width: 40px;
            width: 100px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            background: rgba(22, 155, 213, 1);
            color: #fff;
            border:none;
            user-select: none;
        }
        .btn-cancel,.btn-cancel:hover{
            background: rgba(215, 215, 215, 1);
            color: #fff;
            border:none;
        }
        .big-title{
            font-size: 18px;
            border-bottom: 1px solid #333;
            padding:10px 0;
            margin:0px 0 20px 0;
        }
        .moren-icon{
            color: #ff561b;
            border:1px solid #ff561b;
            padding: 5px;
            margin-left: 8px;
        }
        .main-content{
            margin-bottom: 40px;
        }
        .diyCar {
            margin: 0 0 20px;
        }
    </style>
</head>

<body class="theme-blue">
    <!-- 主要内容开始 -->
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>经销商小程序</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">板车菜单数据管理</span>
        </h1>
        <input type="button" onclick="cpsO.diyCarHandle()" class="button diyCar" value="定制板车参考信息">
        <div class="tip-txt-e">拖拽可改变菜单的先后顺序哦！</div>
        <div class="drag-box">
            <ul class="checkBox"></ul>
            <a class="info_opt info_add btn-cancel" onclick="cpsO.cancelAllX()">取消</a>
            <a class="info_opt info_remove btn-save" onclick="cpsO.allSortFun()">保存</a>
        </div>
        <div class="main-content">
            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                            <input type="button" onclick="cpsO.removeArgFun(event,'','','addTableParameters')" class="button addItem" value="新增">
                        </li>
                    </ul>
                </div>
            </div>
            <div class="dataTable">
                <table border="0" class="tableBox JCLRFlex JColResizer" id="platList1">
                    <tbody class="tbodyContent">
                        <!-- <tr class="tableTh">
                            <th>参数详情</th>
                            <th>操作</th>
                        </tr>
                        <tr index="0">
                            <td>12</td>
                            <td>
                                <a class="info_opt tb_btn" onclick="dealO.updateItem('6')">删除</a>
                            </td>
                        </tr>
                        <tr index="2">
                            <td>11</td>
                            <td>
                                <a class="info_opt tb_btn" onclick="dealO.updateItem('6')">删除</a>
                            </td>                        
                        </tr> -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="outBgColor" style="display: none;"></div>
    <div class="outLook delOutLook addbox" style="display: none;">
        <div class="innerLook">
            <p class="inputBox">
                <!-- 确定删除xxx? -->
            </p>
            <div class="ideabtnBox luckbtnBox cf">
                <button onclick="commonO.operate('close')" class="close_block fl">取消</button>
                <button onclick="cpsO.removeArg()" class="close_block fr">确定</button>
            </div>
        </div>
        <i class="closelookBtn" onclick="commonO.operate('close')">×</i>
    </div>

</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/sortable.js"></script>
<script src="../../js/dealerBusiness/common.js"></script>
<script src="../../js/dealerBusiness/carParamsSort.js"></script>
</html>