<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title id="pageName">车型库</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/dealerBusiness/carMarket.css">
</head>
<body class="theme-blue">
<!-- 主要内容开始 -->
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>经销商小程序</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">车型库</span>
    </h1>
    <div class="main-content">
        <!--搜索条件-->
        <div class="searchBox" id="searchCondition">
            <ul class="searchUl searchUlNone cf"></ul>
        </div>
        <!--页面信息-->
        <div class="pageMessage">
            <div class="searchBtn">
                <ul class="btnBox cf">
                    <li>
                        <input type="button" onclick="libO.loadList('removeSession')" class="button" value="查询">
                    </li>
                    <li>
                        <input class="" id="resetForm" type="button" value="重置" onclick="libO.formReset()">
                    </li>
                    <li>
                        <input class="" id="addForm" type="button" value="新增" onclick="libO.openDetail()">
                    </li>
                    <li>
                        <input class="" id="updataStatusUp" type="button" value="上架" onclick="expO.shelves('2')">
                    </li>
                    <li>
                        <input class="" id="updataStatusDown" type="button" value="下架" onclick="expO.shelves('1')">
                    </li>
                    <li>
                        <input class="" id="downModel" type="button" value="模板下载" onclick="expO.downModel()">
                    </li>
                    <li>
                        <input class="" id="importStatus" type="button" value="批量导入" onclick="expO.importOption('show')">
                    </li>
                    <li>
                        <input class="" id="exportStatus" type="button" value="批量导出" onclick="expO.exportStatus()">
                    </li>
                </ul>
            </div>
            <div class="clearfix"></div>
        </div>
        <div id="updataStatusBox" class="cf">
            <div id="vehicleStatusBox" class="fl">
                <label for="shanJia">只看上架车型：</label><input type="checkbox" tname="vehicleStatus" id="shanJia" value="2" onchange="libO.statusChange($(this))">
                <label for="xiaJia">只看下架车型：</label><input type="checkbox" tname="vehicleStatus" id="xiaJia" value="1" onchange="libO.statusChange($(this))">
            </div>
            <div class="messageRight pageFun fr">查询到<em tname="total"></em>条信息，当前是第<span tname="current"></span>页，共<span tname="maxPage"></span>页</div>
        </div>

        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
        <div class="dataTable">
            <table border="0" class="tableBox" id="carTable"></table>
        </div>
    </div>
    <!--JQ分页-->
    <div id="pageNumberBigBox" class="cf">
        <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>

    <div class="outBgColor" style="display: none;"></div>
    <div class="outLook delOutLook" style="display: none;">
        <div class="innerLook">
            <h2 class="luckName">删除提示</h2>
            <p class="delTitle"></p>
            <div class="ideabtnBox luckbtnBox cf">
                <button onclick="commonO.operate('close')" class="close_block fl">否</button>
                <button onclick="libO.deleteLine()" class="close_block fr">是</button>
            </div>
        </div>
        <i class="closelookBtn" onclick="commonO.operate('close')">×</i>
    </div>
</div>

<!-- 导出层 -->
<div class="outBgColor" style="display: none;" id="import_file"></div>
<div class="outLook excelBox" style="display: none;">
    <div class="innerLook" id="importCarList">
        <h2>导入车型</h2>
        <p class="alertContent">请点击"浏览"选择需要导入的模板文件</p>
        <div class="importTit">最多上传200条车型</div>
        <div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
            <div class="fileBtnBox fl">
                <input class="fileBtn" id="vehicleFile" type="file" name="vehicleFile" accept=".xlsx,.xls" onchange="expO.fileInfo(this,$(this))" value="">
                <button class="rule_true">浏览</button>
            </div>
            <button id="uploadFile" onclick="expO.importStatus()" class="close_block fr">上传</button>
        </div>
    </div>
    <i class="closelookBtn" aria-label="Close" onclick="expO.importOption('hide')">×</i>
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/dealerBusiness/common.js"></script>
<script src="../../js/dealerBusiness/exportFun.js"></script>
<script src="../../js/dealerBusiness/carLibrary.js"></script>
</html>