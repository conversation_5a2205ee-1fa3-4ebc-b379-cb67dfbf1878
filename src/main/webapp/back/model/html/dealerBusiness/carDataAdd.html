<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>板车数据管理-新增车型详情页图片</title>
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/detailNav.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/carSaleEdit.css">
    <link rel="stylesheet" href="../../css/dealerBusiness/carMarket.css">
    <style>
        .selectBox{
            display: inline-block;
            height: 28px;
            line-height: 28px;
            vertical-align: middle;
        }
        .hiddenBox{
            width: 80px;
            overflow: hidden;
        }
        .addRecord{
            background-color: #fff!important;
            border: none!important;
        }
        .selectBox {
            width: 220px;
            height: auto!important;
            line-height: inherit!important;
        }
        .selectBox span {
            display: inline-block;
            width: 100%;
        }
       .flexUlDetail {
            display: flex;
            flex-flow: wrap;
            justify-content: space-between;
        }
        .flexLiDetail {
            justify-content: space-around;
        }
        .hiddenBox {
            overflow: inherit!important;
        }
    </style>
</head>
<body>
<div id="relBox" class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>经销商小程序</span><span>&nbsp;&gt;&nbsp;</span><span>新增车型详情页图片</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">新增车型详情页图片</span>
    </h1>
    <div class="messageTit head">
        <h2 class="">车型信息</h2>
    </div>
    <!--搜索条件-->

    <div class="detailBox">
        <ul class="detailListBox flexUlDetail">
            <li class="threeLine fl">
                <label for="" class="">品牌：</label>
                <div class="selectBox">
                    <select  class="brand_id addRemove" data-name="品牌" tname="brandId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                    <span class="ckDetail"></span>
                </div>
            </li>
            <li class="threeLine flexLiDetail">
                <label for="" class="">车型：</label>
                <!--<select tname="vehicleModelId" class="vehicle" data-name="车型">-->
                    <!--<option value=""></option>-->
                <!--</select>-->
                <div class="selectBox">
                    <select  class="vehicle_model addRemove" data-name="车型" tname="vehicleModelId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                    <span class="ckDetail"></span>
                </div>
            </li>
            <li class="threeLine flexLiDetail">
                <label for="" class="">长度：</label>
                <div class="selectBox">
                    <select  class="vehicle_length addRemove" data-name="长度" tname="vehicleLengthId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                    <span class="ckDetail"></span>
                </div>
            </li>
            <li class="threeLine flexLiDetail">
                <label for="" class="">承载：</label>
                <div class="selectBox">
                    <select  class="vehicle_load addRemove" data-name="承载" tname="vehicleLoadId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                    <span class="ckDetail"></span>
                </div>
            </li>
            <li class="threeLine flexLiDetail">
                <label for="" class="">平台样式：</label>
                <div class="selectBox">
                    <select  class="platform_style addRemove" data-name="平台样式" tname="platformStyleId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                    <span class="ckDetail"></span>
                </div>
            </li>
            <!-- <li class="threeLine fl">
                <label for="" class="">轮胎外露：</label>
                <div class="selectBox">
                    <select  class="vehicleId addRemove" data-name="轮胎外露" tname="tireStyleId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                </div>
            </li> -->
            <li class="threeLine flexLiDetail"></li>

        </ul>
    </div>
    <div class="messageTit">
        <h2 class=""><i class="must">*</i>图片信息</h2>


    </div>
    <div class="detailBox imgBox">
        <ul class="detailListBox uploaderFiles cf">
            <li class="updataBtnBox fl">
                <i class=""></i>
                <p>上传图片</p>
                <input id="pictures" class="uploaderInput" onchange="commonO.fileChange($('.uploaderFiles'),this,10)" onclick="commonO.clearFile(event)" type="file" title=" " accept="image/jpeg,image/jpg,image/png">
            </li>
        </ul>
    </div>
    <!-- 按钮操作区 -->
    <div class="searchBtn" style="width: 110px;">
        <ul class="btnBox cf">
            <li>
                <input type="button" class="button" value="提交" onclick="editO.submit($(this))">
            </li>
        </ul>
    </div>
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/jquery-ui-1.10.4.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/dealerBusiness/common.js"></script>
<script src="../../js/dealerBusiness/carDataAdd.js"></script>
</html>