<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title id="pageName">经销商小程序</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/dealerBusiness/carData.css">
</head>
<style type="text/css">
    
  </style>
<body class="theme-blue" style="padding: 0 0 40px;">
    <!-- 主要内容开始 -->
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>经销商小程序</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">板车数据管理</span>
        </h1>
        <ul class="checkBox">
            <li class="cf">
                <label class="fl" for="">基本参数：</label>
                <ul class="listBox fl">
                    <li onclick="editO.checkTab($(this))" class="active">车型</li>
                    <li onclick="editO.checkTab($(this))">长度</li>
                    <li onclick="editO.checkTab($(this))">承载</li>
                    <li onclick="editO.checkTab($(this))">平台样式</li>
                    <li onclick="editO.checkTab($(this))">轮胎外露</li>
                    <li onclick="editO.checkTab($(this))">车轴品牌</li>
                    <li onclick="editO.checkTab($(this))">爬梯样式</li>
                    <li onclick="editO.checkTab($(this))">车型详情页图片</li>
                </ul>
            </li>
            <li class="cf">
                <label class="fl" for="">外观样式：</label>
                <ul class="listBox fl">
                    <li onclick="editO.checkTab($(this))">鹅颈样式</li>
                    <li onclick="editO.checkTab($(this))">鹅颈小翅膀</li>
                    <li onclick="editO.checkTab($(this))">鹅颈小爬梯</li>
                    <li onclick="editO.checkTab($(this))">鹅颈升降</li>
                    <li onclick="editO.checkTab($(this))">工具箱</li>
                    <li onclick="editO.checkTab($(this))">备胎架</li>
                    <li onclick="editO.checkTab($(this))">凹心种类</li>
                    <li onclick="editO.checkTab($(this))">凹心位置</li>
                    <li onclick="editO.checkTab($(this))">宽度</li>
                    <li onclick="editO.checkTab($(this))">自重</li>
                    <li onclick="editO.checkTab($(this))">支腿形式</li>
                </ul>
            </li>
            <li class="cf">
                <label class="fl" for="">技术参数：</label>
                <ul class="listBox fl">
                    <li onclick="editO.checkTab($(this))">鞍座高度</li>
                    <li onclick="editO.checkTab($(this))">中间货台面高度</li>
                    <li onclick="editO.checkTab($(this))">后货台面高度</li>
                    <li onclick="editO.checkTab($(this))">有效货台面长度</li>
                    <li onclick="editO.checkTab($(this))">车轴型号</li>
                    <li onclick="editO.checkTab($(this))">轮胎型号</li>
                    <li onclick="editO.checkTab($(this))">ABS</li>
                    <li onclick="editO.checkTab($(this))">大梁高度</li>
                    <li onclick="editO.checkTab($(this))">边梁高度</li>
                    <li onclick="editO.checkTab($(this))">底板</li>
                    <li onclick="editO.checkTab($(this))">板簧</li>
                </ul>
            </li>
            <!-- <li class="cf">
                <label class="fl" for="">配件品牌：</label>
                <ul class="listBox fl">
                    <li onclick="editO.checkTab($(this))">板簧</li>
                    <li onclick="editO.checkTab($(this))">悬架</li>
                </ul>
            </li> -->
            <!-- <li class="cf">
                <label class="fl" for="">材质：</label>
                <ul class="listBox fl">
                    <li onclick="editO.checkTab($(this))">钢材</li>
                </ul>
            </li> -->
        </ul>
        <div class="hotContent cf">
            <div class="fl" style="width: 100%;">
                <buttton class="tableTypeBtn" onclick="editO.addType($(this),'车型')">新增车型</buttton>
                <div class="dataTable" style="min-width: 100%;">
                    <table border="0" id="tableBox" class="tableBox"></table>
                </div>
            </div>
        </div>
    </div>
    <div class="outBgColor" style="display: none;"></div>
    <div class="outLook delOutLook" style="display: none;">
        <div class="innerLook">
            <h2 class="luckName">删除提示</h2>
            <p class="delTitle"></p>
            <div class="ideabtnBox luckbtnBox cf">
                <button onclick="commonO.operate('close')" class="close_block fl">否</button>
                <button onclick="editO.deleteLine()" class="close_block fr">是</button>
            </div>
        </div>
        <i class="closelookBtn" onclick="commonO.operate('close')">×</i>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/dealerBusiness/common.js"></script>
<script src="../../js/dealerBusiness/carData.js"></script>
</html>