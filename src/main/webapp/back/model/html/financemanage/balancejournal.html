<!-- 业务流水管理 -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title id="pageName">个人账户流水</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/carMarket/carMarket.css">
</head>
<style>
    .searchUl li{
        width:auto;
    }
    .searchUl li label{
        width:auto;
    }
    .dataTable {
        overflow: inherit;
    }
</style>
<body class="theme-blue">
    <!-- 主要内容开始 -->
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>财务管理</span>
            <span>&nbsp;&gt;&nbsp;</span>
            <span class="currentPage"><!--用户余额流水-->个人账户流水</span>

        </h1>
        <div class="main-content">
            <!--搜索条件-->
            <div class="searchBox" id="searchCondition">
                <ul class="searchUl searchUlNone cf">
                </ul>
            </div>
            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                            <input type="button" onclick="flowO.loadList()" class="button" value="查询">
                        </li>
                        <li>
                            <input type="button" onclick="tytTool.formReset($('.searchUl'))" class="button" value="重置">
                        </li>
                        <li>
                            <input type="button" onclick="flowO.excelExport()" class="button" value="导出">
                        </li>
                       
                    </ul>
                </div>
                <div class="clearfix"></div>
                <div class="messageRight fr">
                    用户钱包总余额:<span id="yhqbzje"></span>元，
                    提现总金额:<span id="txzje"></span>元，
                    充值总金额:<span id="czzje"></span>元，
                    查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span
                        id="totalPages"></span>页
                </div>
            </div>
            <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i>
                <div class="receiptInner cf"><span></span><em>复制</em></div>
            </div>
            <div class="dataTable">
                <table border="0" class="tableBox" id="platList"></table>
            </div>
        </div>
        <!--JQ分页-->
        <div id="pageNumberBigBox" class="cf">
            <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
        </div>
        <div id="componentBox"></div>
    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/tytCity/index.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<!-- <script src="../../js/carManage/carCommon.js"></script> -->
<script src="../../js/financemanage/balancejournal.js"></script>

</html>