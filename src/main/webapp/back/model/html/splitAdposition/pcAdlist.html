<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>广告位管理</title>
	<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
	<link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
	<link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
	<link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
	<link href="../../css/splitAdposition/adList.css" type="text/css" rel="stylesheet">
</head>
<body>
<div class="contentBox">
	<h1 class="titUrl">
		<span>当前所在位置：</span><span>广告位管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">PC广告位列表</span>
	</h1>
	<div class="searchBox">
		<form id="upFormDownLoad" action="" method="post" enctype="multipart/form-data" name="upload_form" target="upIfrDown">
			<ul class="searchUl searchUlNone cf">
				<li>
					<label for="">显示位置：</label>
					<select id="showPosition" tname="showPosition" name="showPosition">
						<option class="firstOption" value="">不限</option>
					</select>
				</li>
				<li>
					<label for="">启用状态：</label>
					<select id="status" tname="status" name="status">
						<option class="firstOption" value="">不限</option>
						<option value="1">已启用</option>
						<option value="0">已停用</option>
					</select>
				</li>
			</ul>
		</form>
	</div>
	<div class="pageMessage">
		<div class="searchBtn">
			<ul class="btnBox cf">
				<li><input class="" id="checkForm" type="button" value="查询" onclick="goodsOptfn.checkForm()"></li>
				<li><input class="" id="resetForm" type="button" value="重置" onclick="tytTool.resetAllForm($('.searchUl'),$('#checkForm'))"></li>
				<li><input class="" id="" type="button" value="新增" onclick="goodsOptfn.addDetail()"></li>
				<li><input type="button" id="button" value="模板下载" onclick="goodsOptfn.excelExportModel()"></li>
			</ul>
		</div>
		<div class="clearfix"></div>
		<div class="messageRight fr">查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span id="totalPages"></span>页
		</div>
	</div>
	<div id="dataTable" class="dataTable">
		<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		<table border="0" cellpadding="0" cellspacing="0" class="tableBox" id="companyManage">
			<tr class="tableTh">
				<th width="80">活动ID</th>
				<th width="90">显示优先级</th>
				<th width="150">更新时间</th>
				<th width="150">缩略图</th>
				<th>标题</th>
				<th>图片链接</th>
				<th>显示位置</th>
				<th width="300">分众条件</th>
				<th>展示时间</th>
				<th>定制时间</th>
				<th width="90">启用状态</th>
				<th width="90">最近操作人</th>
				<th width="200">操作</th>
			</tr>
		</table>
	</div>
	<!--JQ分页-->
	<div id="pageNumberBigBox" class="cf">
		<ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
	</div>
	<div class="ProvinceBox">
		<div class="tit">
			<span>展示省市</span><span class="close" onclick="goodsOptfn.closeFn()">×</span>
		</div>
		<div class="list">

		</div>
	</div>
	<!-- 文件导入 -->
    <div class="outLook_ excelBox" style="display: none;">
		<div class="innerLook">
			<h2>请选择文件</h2>
		<p class="alertContent">请点击“浏览”选择需要导入的模板文件</p>
			<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
				<div class="fileBtnBox fl"> 
					<input class="fileBtn" id="fileField" type="file" name="fileField" accept=".xlsx,.xls" onchange="goodsOptfn.fileInfo(this,$(this))">
					<button class="rule_true">浏览</button>
				</div>
				<button id="uploadFile" onclick="goodsOptfn.uploadFile()" class="close_block fr">上传</button>
			</div>
		</div>
		<i class="closelookBtn" onclick="goodsOptfn.showHide('excelHide')">×</i>
	</div>
	<!-- 遮罩 -->
	<div class="backg"></div>
</div>
</div>
<iframe id="upIfrDown" name="upIfrDown" src="" frameborder="0"></iframe>


<div class="outBgColor"  style="display: none;"></div>

</body>

<script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../../js/jqpaginator.min.js"></script>
<script type="text/javascript" src="../../js/tyt_common.js"></script>
<script type="text/javascript" src="../../js/splitAdposition/adCommon.js"></script>
<script type="text/javascript" src="../../js/jquery.cookie.js"></script>
<script type="text/javascript" src="../../js/splitAdposition/adList.js"></script>

<script>

</script>
</html>
