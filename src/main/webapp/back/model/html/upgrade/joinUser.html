<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>参与用户管理</title>
	<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="../../css/upgrade.css" type="text/css" rel="stylesheet">

  <script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="../../../jurisdiction/jsp/js/common.js"></script>
  <!-- <script src="../../js/"></script> -->
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>客户端升级管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">参与用户管理</span>
		</h1>
		<div class="joinBox">
			<ul class="joinInnerBox">
				<li>
					<label for="">升级任务标题：</label>
					<span>和由远及近和由远及近和由远及近和由远及近和由远及近和由远及近</span>
				</li>
				<li style="width: 400px;">
					<label for="">任务时间：</label>
					<span>2017-09-23 14:03:05~2017-09-24 14:03:05</span>
				</li>
				<li>
					<label for="">客户端类型：</label>
					<span>Android</span>
				</li>
				<li style="width: 400px;">
					<label for="">当前状态：</label>
					<span>进行中</span>
				</li>
			</ul>
		</div>
		<div class="searchBox">
			<ul class="searchUl searchUlNone cf">
				<li><label for="">手机号码：</label><input type="text" style=""></li>

				<li>
					<label for="">身份一级分类：</label>
					<select name="" id="">
						<option value="">全部</option>
						<option value="">车辆方</option>
						<option value="">发货方</option>
					</select>
				</li>
				<li>
					<label for="">身份二级分类：</label>
					<select name="" id="">
						<option value="">请选择</option>
						<option value="">企业货主</option>
						<option value="">货站</option>
					</select>
				</li>

        <li class="">
          <label class="fl">归属地：</label>
            <div class="fl areaDiv">
            <input id="position2" class="input120" type="text"  placeholder="请选择地区" onclick="tyt_open_area('1','position2','boxDiv2','sheng2','shi2','tyt_area_iframe2','3');"
                    readonly class="positionInput addressIcon" value="">
                <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
                    id="boxDiv2">
                    <iframe name="tyt_area_iframe2" id="tyt_area_iframe2" src="../../js/tyt_area_no_county/areaTwo.html" frameborder="0"
                        width="240" scrolling="no" height="550" leftmargin="0" topmargin="0" class="boxDiv" allowtransparency="true"></iframe>
                </div>
                <p class="noneInput mtl20"><input id="sheng2" name="recProvince" type="text" value="" class="positionInput"></p>
                <p class="noneInput mtl20"><input id="shi2" type="text" name="recCity" value="" class="positionInput"></p>
                <div class="cb"></div>
            </div>
        </li>
				<li>
					<label for="">升级状态：</label>
					<select name="" id="">
						<option value="">请选择</option>
						<option value="">未完成</option>
						<option value="">已完成</option>
					</select>
				</li>
			</ul>
		</div>
		<div class="pageMessage">
			<div class="searchBtn">
				<ul class="btnBox cf">
					<li><input class="sbWidth" type="button" value="查询"></li>
					<li><input class="loadDataBtn" type="button" value="导入"></li>
					<li><input class="addlookBtn" type="button" value="模板下载"></li>
					<li><input class="clearDataBtn" type="button" value="清空导入用户"></li>
				</ul>
			</div>
		</div>
		<div class="bgOutLook" style="display: none;"></div>
		<div class="progressBar" style="display: none;">导入中...<span>80</span>%</div>
		<div class="loadbox" style="display: none;">请先导入目标用户</div>
		<div class="dataTable" style="display: block; min-width: 1060px;width: 100%;">
			<div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
		    <table border="0" cellpadding="0" cellspacing="0" class="tableBox" id="joinUserBox" style="min-width: 1060px;width: 100%;">
				<tr class="tableTh">
					<th width="80">用户ID</th>
					<th width="260">client_id</th>
					<th>电话</th>
					<th>真实姓名</th>
					<th>身份二级分类</th>
					<th>身份一级分类</th>
					<th>归属地</th>
					<th>最后登录版本</th>
					<th>升级状态</th>
					<th width="120">操作</th>
				</tr>
				<tr>
					<td>358</td>
					<td>ffffffff-a10a-17f6-f076-32940033c587</td>
					<td>13403263999</td>
					<td>徐建</td>
					<td>个人车主</td>
					<td>车辆方</td>
					<td>河北保定市</td>
					<td>Android3310</td>
					<td>未完成</td>
					<td>
						<a class="bindBtn">删除</a>
					</td>
				</tr>
				<tr>
					<td>8832</td>
					<td>00000000-4edb-e165-ffff-ffffca2b575a</td>
					<td>13722257925</td>
					<td>安立敏</td>
					<td>个人车主</td>
					<td>车辆方</td>
					<td>河北保定市</td>
					<td>Android3310</td>
					<td>未完成</td>
					<td>
						<a class="bindBtn">删除</a>
					</td>
				</tr>
				<tr>
					<td>10186</td>
					<td>ffffffff-cc7a-7ea3-ffff-fffffc7179ca</td>
					<td>13931391182</td>
					<td>杨华</td>
					<td>个人车主</td>
					<td>车辆方</td>
					<td>河北保定市</td>
					<td>Android3310</td>
					<td>未完成</td>
					<td>
						<a class="bindBtn">删除</a>
					</td>
				</tr>

						
				
		    </table>
		</div>

		<!--此处引入footer.jsp-->
    <!-- <jsp:include page="/back/jsp/footer.jsp" flush="true"/> -->


		<div class="outgreen" style="display: none;">
			<p>
				<i></i><span class="load_ss">导入完成</span><span>共导入数据</span><span>1800</span><span>条</span>
			</p>
		</div>

	<div class="joinbgOutLook" style="display: none;"></div>
	<div class="outLook excelBox" style="display: none;">
			<div class="innerLook"> 
				<h2>请选择文件</h2> 
		    <p class="alertContent">文件名</p>
 				<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">	
 					<div class="fileBtnBox fl">
 						<input class="fileBtn" type="file" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"   onchange="fileInfo(this,$(this))">
 						<button class="rule_true">浏览</button>
 					</div>
 					<button class="close_block fr">上传</button> 
 				</div> 
 			</div> 
 			<i class="closelookBtn">×</i> 
 		</div>


	</div>
</body>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../../js/infoMessage.js"></script>
<script src="../../js/tyt_area_no_county/js/tyt_area_two.js"></script>
<script>

	$(".loadDataBtn,.loadbox").click(function(event) {
		// loadData();
	});
	$(".clearDataBtn").click(function(event) {
		clearData();
	});
	//====================================
	$(".loadDataBtn").click(function(event) {
		$(".excelBox,.joinbgOutLook").show()
	});
	$(".closelookBtn").click(function(event) {
		$(".excelBox,.joinbgOutLook").hide();
	});
	function fileInfo(source, _this) {
	  var f = source.files[0];
	  console.log(f)
	  var name = f.name;
	  var size = f.size;
	  var type = f.type;
	  $(".excelBox .alertContent").html(name)
	}
	//====================================
	//注: 前端暂时用定时器模拟效果, 待导入结束后调用该函数 ,  
	function loadData(){
		$(".progressBar,.bgOutLook").show();
		setTimeout(function(){
			$(".loadDataBtn").val('继续导入');
			$(".loadbox").hide();
			$(".progressBar,.bgOutLook").hide();
			$(".dataTable").show();
			$(".outgreen").show();
		},2000)
		setTimeout(function(){
			$(".outgreen").fadeOut();
		},4000)
	}
	//清空已导入数据
	function clearData(){
		$("#joinUserBox tr:not('.tableTh')").remove();
		$(".dataTable").hide();
		$(".loadbox").show();
	}
</script>
</html>