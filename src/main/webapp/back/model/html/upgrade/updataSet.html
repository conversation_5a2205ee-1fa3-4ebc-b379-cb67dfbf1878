<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>创建任务</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <!-- <link href="../../css/popularize.css" type="text/css" rel="stylesheet"> -->
  <link href="../../css/upgrade/updata.css" type="text/css" rel="stylesheet">
</head>
<body>
	<div class="setBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>客户端升级管理</span><span>&nbsp;&gt;&nbsp;</span><span>升级任务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">创建任务</span>
		</h1>
        <div class="main-content">
            <!--系统设置-->
            <div class="systemParameter">
                <ul class="systemUl cf">
                    <li class="cf">
                        <label class="fl"><i class="xing">*</i>任务标题：</label>
                        <input class="widthLong validClass" type="text" tname="taskTitle" maxlength="48" placeholder="最多输入48个字">
                    </li>
                    <li class="cf">
                        <label class="fl"><i class="xing">*</i>选择任务模板：</label>
                        <select tname="templateId" id="templateSelect" class="widthLong validClass">
                            <option class="firstOption" value="">请选择</option>
                            <!-- <option value="4">哈哈哈</option> -->
                        </select>
                    </li>
                    <li class="cf">
                        <label for=""><i class="xing">*</i>任务时间：</label>
                        <input type="text" id="taskStartTime" class="pinfoActive time validClass" tname="taskStartTime" readonly onclick="comO.startPicker('taskEndTime')">
                        <span>-</span>
                        <input type="text" id="taskEndTime" class="pinfoActive time validClass" tname="taskEndTime" readonly onclick="comO.endPicker('taskStartTime')">
                    </li>
                    <li class="cf">
                        <input type="hidden" class="allUserFlag" value="">
                        <label for=""><i class="xing">*</i>选择用户：</label>
                        <div class="fl">
                            <div class="radioNav">
                                <input type="radio" name="allUserFlag" tname="allUserFlag" value="1" id="sys" onchange="setO.radioChange($(this),'allUserFlag')">
                                <label for="sys">系统全部用户</label>
                            </div>
                            <div class="radioNav">
                                <input type="radio" name="allUserFlag" tname="allUserFlag" value="0" id="excel" onchange="setO.radioChange($(this),'allUserFlag')">
                                <label for="excel">导入Excel</label>
                                <span class="excelFile"  onclick="setO.commonClick('excelShow')">选择文件</span>
                                <a href="javascript:;" onclick="setO.downloadModel()">Excel下载</a>
                            </div>
                        </div>
                    </li>
                    <li class="cf">
                        <input type="hidden" class="closeFlag" value="">
                        <label for=""><i class="xing">*</i>弹层样式：</label>
                        <div class="fl">
                            <div class="radioNav">
                                <input type="radio" name="closeFlag" tname="closeFlag" value="0" id="noOut" onchange="setO.radioChange($(this),'closeFlag')">
                                <label for="noOut">不带关闭弹层</label>
                            </div>
                            <div class="radioNav">
                                <input type="radio" name="closeFlag" tname="closeFlag" value="1" id="haveOut" onchange="setO.radioChange($(this),'closeFlag')">
                                <label for="haveOut">带关闭弹层</label>
                            </div>
                        </div>
                    </li>
                </ul>
                <div class="setBtn cf">
                    <input class="button fl" type="button" value="取消" onclick="window.close();">
                    <input class="button fr" type="button" id="submitTrue" value="保存" onclick="setO.submitSet()">
                </div>
            </div>
            <!-- 导入引导框 -->
            <div class="outBgColor" style="display: none;"></div>
            <div class="outLook excelBox" style="display: none;">
            <div class="innerLook">
                <h2>请选择文件</h2>
                <p class="alertContent">请点击“浏览”选择需要导入的模板文件</p>
                <div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
                <div class="fileBtnBox fl">
                    <input class="fileBtn" id="fileField" type="file" name="fileField" accept=".xlsx,.xls" onchange="setO.fileInfo(this,$(this))">
                    <button class="rule_true">浏览</button>
                </div>
                <button id="uploadFile" onclick="setO.commonClick('excelHide')" class="close_block fr">确定</button>
                </div>
            </div>
            <i class="closelookBtn" onclick="setO.commonClick('excelClear')">×</i>
            </div>
            <!--Excel导入错误提示-->
            <div class="outLook importTit" style="display: none;" >
                <div class="innerLook">
                    <h2>提  示</h2>
                    <p class="alertContent"></p>
                    <div class="ideabtnBox cf">
                        <button class="fr" onclick="setO.commonClick('importHide')">确定</button>
                    </div>
                </div>
            </div>
        </div>
	</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>  
<script src="../../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/tyt_common.js"></script>  
<script src="../../js/upgrade/updataCommon.js"></script>
<script src="../../js/upgrade/updataSet.js"></script>  
<script>
</script>
</html>