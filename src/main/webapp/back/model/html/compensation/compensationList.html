
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title id="pageName">补偿券发放</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <link rel="stylesheet" href="../../css/dealerBusiness/carMarket.css">
    <style>
        .searchUl li{
            width: auto;
        }
        .searchUl li label{
            width: 140px;
        }
        .searchUl .setLabelWidth input{
            width: 40px;
        }
        tr td:nth-child(5){
            max-width: 300px !important;
            width: 300px !important;
        }
        .tipModal .alertContent {
            margin: 15px auto;
            width: 90%;
            min-height: 40px;
            text-align: left;
            font-size: 14px;
            color: #333;
    }
    .alertContent label {
        width: auto;
        min-width: 100px
    }
    .alertContent input, textarea {
        display: inline-block;
        width: 140px;
        height: 24px;
        border: 1px solid #ccc;
        background: #fff;
        vertical-align: top;
    }
    .alertContent li {
        padding: 3px;
        vertical-align: top;
    }
    .outLook{
        width: 430px;
        height: auto;
    }
    .alertContent .checkbox{
        width: 20px;
        vertical-align: top;
    }
    .alertContent .checkout-val{
        display: inline-block;
        margin-top: 7px;
    }
    .sendLabel{
        width: auto;
        min-width: 0px !important;
        font-weight: 400;
        margin-left: 30px;
        margin-left: 15px;
        vertical-align: -webkit-baseline-middle;
    }
    .sendInput{
        margin-top: 4px;
    }
    .innerLook{
        padding-bottom: 20px;
    }
    .outLook{
        margin-top: -192px;
    }
    .rejectText{
        width: 262px;
        min-height: 134px;
    }
    </style>
</head>
<!-- TODO 线上活动 补偿券发放 -->
<body class="theme-blue">
<!-- 主要内容开始 -->
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>线上活动管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">补偿券发放</span>
    </h1>
    <div class="main-content">
        <!--搜索条件-->
        <div class="searchBox" id="searchCondition">
            <ul class="searchUl searchUlNone cf"></ul>
        </div>
        <!--页面信息-->
        <div class="pageMessage">
            <div class="searchBtn">
                <ul class="btnBox cf">
                    <li>
                        <input type="button" onclick="rulesO.loadList('removeSession')" class="button" value="查询">
                    </li>
                    <li>
                        <input class="" id="resetForm" type="button" value="新增补偿券" onclick="commonO.tipModal('addVouchers')">
                    </li>
                    <li>
                        <input class="" id="resetForm" type="button" value="导出" onclick="rulesO.addRules()">
                    </li>
                </ul>
            </div>           
            <div class="listOpt pageFun cf">
                <div class="optItem messageRight fr">查询到<em tname="total"></em>条信息，当前 是第<span tname="current"></span>页，共<span tname="maxPage"></span>页</div>
            </div>
        </div>
        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
        <div class="dataTable">
            <table border="0" class="tableBox" id="goodsPlaint"></table>
        </div>
    </div>
    <!--JQ分页--> 
    <div id="pageNumberBigBox" class="cf">
        <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>
</div>
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/compensation/common.js"></script>
<script src="../../js/compensation/compensationList.js"></script>
</html>