<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title id="pageName" data-tit="二手车管理">二手车一主一挂列表</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
</head>
<style>
    th,td{
        max-width: 150px;
    }
    .brand_box{
        position: relative;
    }
    .brand_ul{
        position: absolute;
        right: -100px;
        top: 30px;
        text-align: center;
        border:1px solid #eee;
        width: 200px;
        background: #999;
        color: #fff;
        max-height: 280px;
        overflow: scroll;
        overflow-x: hidden;
    }
    .brand_li{
        border-bottom: 1px solid #eee;
        width: 200px!important;
        margin: 0!important;
        padding: 0!important;
        height: 30px;
        line-height: 30px;
        cursor: pointer;
    }
    .a_button{
        font-size: 14px;
        line-height: 32px;
        height: 32px;
        padding: 0 12px;
        color: #fff;
        border: none 0;
        border-radius: 4px;
        background: #3961b2;
        display: block;
    }
    .a_button:hover,.a_button:active,.a_button:visited,.a_button:link,.a_button:focus{
        color: #fff;
    }
    .ver_block{
        cursor: pointer;
    }
    .tip{
        text-align: center;
        padding: 10px 0;
    }
    .addWhiteUser {
        margin: 23px auto 7px!important;
        width: 270px;
    }
    .auditSeclct {
        width: 170px;
        height: 30px;
        border: 1px solid #ccc;
        padding-left: 10px;
        border-radius: 5px;
    }
</style>
<body class="theme-blue">
<!-- 主要内容开始 -->
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>二手车管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">二手车一主一挂列表</span>
    </h1>
    <div class="main-content">
        <!--搜索条件-->
        <div class="searchBox" id="searchCondition">
            <ul class="searchUl searchUlNone cf">
                <li>
                    <label>注册人：</label>
                    <input id="userName" type="text" tname="userName" maxlength="20"  onblur="tytTool.onInput($(this),20,5)" value=""/>
                </li>
                <li>
                    <label>手机号：</label>
                    <input id="saleCellPhone" type="text" tname="saleCellPhone" onblur="tytTool.onInput($(this),11,7)" onafterpaste="tytTool.onAfterPaste($(this))" maxlength="11" value=""/>
                </li>
                <li>
                    <label>车辆信息ID：</label>
                    <input id="id" type="text" tname="id" value=""/>
                </li>
                <li class="brand_box">
                    <label>车头品牌：</label>
                    <input id="headBrand" type="text" tname="headBrand" value="" autocomplete="off"/>
                    <ul class="brand_ul brand_ul_head" style="display: none">
                    </ul>
                </li>
                <li>
                    <label>马力：</label>
                    <select id="horsepower" tname="horsepower" >
                        <option class="firstOption" value="">请选择</option>
                        <option class="firstOption" value="400以下">400以下</option>
                        <option class="firstOption" value="400-450">400-450</option>
                        <option class="firstOption" value="451-550">451-550</option>
                    </select>
                </li>
                <li>
                    <label>发布状态：</label>
                    <select id="infoStatus" tname="infoStatus" >
                        <option class="firstOption" value="">请选择</option>
                        <option class="firstOption" value="1">上架</option>
                        <option class="firstOption" value="2">下架</option>
                        <option class="firstOption" value="4">审核未通过</option>
                        <option class="firstOption" value="3">审核中</option>
                    </select>
                </li>


                <li class="brand_box">
                    <label>板车品牌：</label>
                    <input id="tailBrand" type="text" tname="tailBrand" value="" autocomplete="off"/>
                    <ul class="brand_ul brand_ul_bar" style="display: none">
                    </ul>
                </li>
                <li>
                    <label>板车长度：</label>
                    <select id="tailLength" tname="tailLength" >
                    </select>
                </li>
                <li>
                    <label>板车类型：</label>
                    <select id="tailType" tname="tailType" >
                    </select>
                </li>
                <!-- <li>
                    <label>板车种类：</label>
                    <select id="tailKind" tname="tailKind" >
                    </select>
                </li> -->
            </ul>
        </div>
        <!--页面信息-->
        <div class="pageMessage">
            <div class="searchBtn">
                <ul class="btnBox cf">
                    <li>
                        <input type="button" onclick="lo.loadList()" class="button" value="查询">
                    </li>
                    <li><input class="" id="resetForm" type="button" value="重置" onclick="tytTool.formReset($('.searchUl'))"></li>
                    <li>
                    <input type="button" class="button" value="导出excel" onclick="lo.exportData();">
                    </li>
                    <li>
                        <!-- <a class="a_button" href="carSaleEdit.html?pageType=all">新增</a> -->
                        <!-- <a class="a_button" href="javascript:;" onclick="lo.openNewPage('all')">新增</a> -->
                    </li>
                </ul>
            </div>
            <div class="clearfix"></div>
            <div class="messageRight fr">查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span id="totalPages"></span>页
            </div>
        </div>

        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
        <div class="dataTable">
            <table border="0" class="tableBox">
                <tr class="tableTh">
                    <th>车辆信息ID</th>
                    <th>注册人</th>
                    <th>发布人</th>
                    <!-- <th>联系电话</th> -->
                    <th>发布时间</th>

                    <!-- <th>出厂时间</th> -->
                    <th>车头上牌日期</th>
                    <th>车头品牌</th>
                    <!-- <th>车头型号</th> -->
                    <th>马力</th>
                    <th>驱动形式</th>

                    <th>品牌</th>
                    <th>板车上牌日期</th>
                    <th>板车长度</th>
                    <th>板车类型</th>
                    <!-- <th>板车种类</th> -->
                    <th>所在地</th>
                    <!-- <th>是否可拆卖</th> -->
                    <th>发布状态</th>
                    <th width=80px;>操作</th>
                </tr>
                <tbody id="headAndBarList"></tbody>
            </table>
        </div>
    </div>

    <div class="alert alert-danger fade in alert-height" style="display: none;" id="confirmRefundModal">
        <div class="innerLook">
            <h2>确认</h2>
            <div class="addWhiteUser cf tip">
                <ul>
                    <li>
                        <label for="">下架原因：</label>
                        <select class="auditSeclct" tname="">
                            <option class="firstOption" value="">请选择</option>
                            <option value="1">车辆已售</option>
                            <option value="2">不想卖了</option>
                            <option value="3">车辆有问题</option>
                            <option value="4">其他</option>
                        </select>
                    </li>
                </ul>
            </div>
            <div class="makeSureBtn closePayModalBtn" style="height:32px;margin: 10px auto 0;text-align: center;">
                <input class="button confirm" type="button" value="确定">&nbsp;&nbsp;&nbsp;&nbsp;
                <input class="button" type="button" onclick="lo.closeRefundModal()" value="取消">
            </div>
        </div>
        <i class="closelookBtn" onclick="lo.closeRefundModal()">×</i>
    </div>

    <!--JQ分页-->
    <div id="pageNumberBigBox" class="cf">
        <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>
</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js" ></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/carSale/headAndBarList.js"></script>
</html>