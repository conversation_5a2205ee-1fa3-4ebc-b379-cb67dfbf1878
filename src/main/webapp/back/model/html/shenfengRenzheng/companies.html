<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>企业审核</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- <link href="../../css/viewer.min.css" rel="stylesheet" type="text/css" /> -->
    <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="../../css/identity.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
    <link  href="../../css/validate.css" rel="stylesheet" type="text/css">
    <style>
        .messagePhoto .margin-auto {
            margin: 0 auto;
        }
    </style>

</head>
<body>

<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span>身份认证列表</span><span>&nbsp;&gt;&nbsp;</span><span>审核</span><span>&nbsp;&gt;&nbsp;</span><span style="color: #428bca;">企业审核</span>
    </h1>
    <div class="main-content" id="mainContent">
        <form name="validateForm" method="post" id="validateForm" action=""  autocomplete="off">
            <!--搜索条件-->
            <div class="searchCondition">
                <ul class="o-hidden">
                    <li>
                        <label>注册手机号：</label>
                        <span id="reg_phone_num"></span>
                    </li>
                    <li>
                        <label>用户注册身份：</label>
                        <span id="reg_status"></span>
                    </li>
                    <li>
                        <label>企业名称：</label>
                        <span id="company_name" tip=""></span>
                    </li>
                    <li>
                        <label>企业座机：</label>
                        <span id="company_phone"></span>
                    </li>
                    <li style="width: auto;">
                        <label>企业类型：</label>
                        <span id="company_type"></span>
                    </li>
                    <li style="width: auto;">
                        <label>销售审核身份：</label>
                        <span>一级</span>
                        <select name="" id="audit_status1" class="select180">
                            <option class="intril" value="">--请选择--</option>
                        </select>
                        <span>二级</span>
                        <select name="" id="audit_status2" class="select180">
                            <option class="intril" value="">--请选择--</option>
                        </select>
                    </li>

                    <li>
                        <label>提交认证时间：</label>
                        <span id="authen_time"></span>
                    </li>
                    <li>
                        <label>最新审核时间：</label>
                        <span id="audit_time"></span>
                    </li>
                    <li style="width: auto;">
                        <label>认证状态：</label>
                        <span>对接人身份证正面照-<strong id="idcard_hand_rz"></strong>，</span>
                        <span>对接人本人照-<strong id="iPhoto_rz"></strong>，</span>
                        <span>企业营业执照-<strong id="business_license_rz"></strong></span>
                    </li>

                </ul>
            </div>
            <!--对接人身份证正面照认证-->
            <div class="userMessage">
                <!-- 身份信息认证 -->
                <div class="messageTxt">
                    <table width="390" border="0" class="table-bordered">
                        <tr><th colspan="2"><h4>身份信息认证</h4></th></tr>
                        <tr>
                            <td>真实姓名</td>
                            <td><input id="true_name" disabled readonly class="input180" type="text" maxlength="15" reg="^[\u4e00-\u9fa5]+·?[\u4e00-\u9fa5]+$" tip="请输入你的真实姓名"  /></td>
                        </tr>
                        <tr>
                            <td>身份证号</td>
                            <td><input id="id_card" disabled readonly class="input180" onblur="tyt_Obj.identityBlur($(this));" type="text" tip="请输入18位身份证"></td>
                        </tr>
                    </table>
                    <table width="390" border="0" class="table-bordered">
                        <tr><th colspan="2"><h4>企业信息</h4></th></tr>
                        <tr>
                            <td>企业名称</td>
                            <td><input id="enterpriseAuthCompanyName" maxlength="50" class="input180" type="text" reg="^[0-9a-zA-Z-_\u4e00-\u9fa5]{1,50}$" placeholder="请输入企业名称" /></td>
                        </tr>
                        <tr>
                            <td>统一社会信用代码</td>
                            <td><input id="enterpriseAuthCreditCode" maxlength="50" class="input180" type="text" reg="^[0-9a-zA-Z-_\u4e00-\u9fa5]{1,50}$" placeholder="请输入统一社会信用代码"></td>
                        </tr>
                    </table>
                </div>

                <div class="messagePhoto margin-auto licenseUrl_big cf">
                    <!-- 企业营业执照认证 -->
                    <div class="photoWare margin-auto ">
                        <h4><span>企业营业执照</span></h4>
                        <div class="imgBox"><img id="authLicenseUrl" src="../../images/default.jpg" rotate="0" alt="资质证照"></div>
<!--                        <div class="previewImgbox">-->
<!--                            <a id="licenseUrl_big">查看原图</a>-->
<!--                        </div>-->
                        <div class="previewImgbox">
                            <a id="licenseUrl_big">查看原图</a>
                            <span>|</span>
                            <a href="javascript:;" style="cursor: pointer;" onclick="doLeft('authLicenseUrl')">向左旋转</a>
                            <span>|</span>
                            <a href="javascript:" style="cursor: pointer;" onclick="doRight('authLicenseUrl')">向右旋转</a>
                        </div>

                        <table width="100%" border="0" class="table-bordered">
                            <tr>
                                <td>企业营业执照认证状态</td>
                                <td><select name="" id="enterpriseAuthStatus" data-ind="0" class="select180 selectOne">
                                    <option value="0">请选择认证结论</option>
                                    <option value="1">认证成功</option>
                                    <option value="3">认证失败</option>
                                </select></td>
                            </tr>
                            <tr style="display: none;">
                                <td>失败原因</td>
                                <td><select name="" id="enterpriseAuthFailureReason" data-ind="0" class="select180 selectTwo">
                                    <option value="0">请选择失败原因</option>
                                    <option value="1">营业执照模糊</option>
                                    <option value="2">营业执照不完整</option>
                                    <option value="3">营业执照为无效证件</option>
                                </select></td>
                            </tr>
                        </table>

                    </div>
                </div>
            </div>
        </form>
        <div class="errBox" style="display: none;"></div>
        <!-- 黑名单管理 -->
        <div class="blackListAuto">
            <div class="blackList"></div>
        </div>
    </div>

    <!-- 弹出框  成功 -->

    <div class="alert alert-danger fade in" id="alert_danger" role="alert" style="display: none">
        <button type="button" class="close" aria-label="Close" id="close_btn">×</button>
        <h4>提示</h4>
        <p class="alertContent"></p>
        <div class="tc buttonBox">
            <button type="button" class="button" id="btn_default">我知道了</button>
        </div>
    </div>

</div>

<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/bootstrap.js"></script>
<script src="../../js/easy_validator.pack.js"></script>
<script src="../../js/jquery.bgiframe.min.js"></script>
<!-- <script src="../../js/viewer.min.js"></script> -->
<script src="../../js/formValidatorRegex.js"></script>
<script src="../../../jurisdiction/jsp/js/common.js"></script>
<script src="../../js/identity.js"></script>
<script src="../../js/ident_common.js"></script>
<!-- <script src="../../js/indent_blackList.js"></script> -->

<script type="text/javascript">
    var parsesearchId = tyt_Obj.parseSearch().userId;

    // 查看原图的打开方式

    $('#licenseUrl_big').click(function(event) {
        // lookBigImg('licenseUrl_big')
        var this_src = $(this).parents('.previewImgbox').siblings('.imgBox').find('img').attr('src');
        tyt_Obj.openWinAuto('../imgLook/imgLook.html?imgUrl=' + this_src, '', 400, 450, 0, 0);
    });
    function doLeft(name) {
        var img = $("#" + name);
        var rDeg = Number(img.attr("rotate")) - 90;
        img.attr("rotate",rDeg)
        console.log('123')
        img.css('transform','rotate('+ rDeg +'deg)');
    }

    function doRight(name) {
        var img = $("#" + name);
        var rDeg = Number(img.attr("rotate")) + 90;
        img.attr("rotate",rDeg)
        img.css('transform','rotate('+ rDeg +'deg)');
    }
    // 身份+图片的失败下拉框样式
    tyt_Obj.selectshowHide();
    //submit提交
    //jquery1.9之前用live，1.9之后用on
    $(".submitBtn").live('click', function(event) {
        event.preventDefault();
        /* Act on the event */
        if(tyt_Obj.selectEach()==1){
            alert('认证状态不能为空');
            return false;
        }else if(tyt_Obj.selectEach()==2){
            alert('请选择失败原因');
            return false;
        }

        // 提交数据

        var enterpriseAuthCompanyName = $('#enterpriseAuthCompanyName').val();
        var enterpriseAuthCreditCode = $('#enterpriseAuthCreditCode').val();
        var deliverTypeOne = $("#audit_status1 option:selected").val();
        var deliverTypeTwo = $("#audit_status2 option:selected").val();
        var enterpriseAuthStatus = $("#enterpriseAuthStatus option:selected").val();
        var enterpriseAuthFailureReason = $("#enterpriseAuthFailureReason option:selected").text();
        if(enterpriseAuthStatus!=3) {
            if(enterpriseAuthCompanyName == '') {
                alert('请输入企业名称')
                return false
            }
            if(enterpriseAuthCreditCode == '') {
                alert('请输入统一社会信用代码')
                return false
            }
        }
        if(deliverTypeOne==''||deliverTypeTwo==''){
            alert('请选择销售身份')
            return false;
        }

        // 如果选择成功状态，失败原因置为空。

        if(enterpriseAuthStatus==1){
            enterpriseAuthFailureReason = ''
        }
        var arr_fx = {
            userId:parsesearchId,
            deliverTypeOne,
            deliverTypeTwo,
            enterpriseAuthStatus,
            enterpriseAuthFailureReason,
            enterpriseAuthCompanyName,
            enterpriseAuthCreditCode
        }
        var reset  = '';
        var param;
        for (var key in arr_fx)
        {
            if (arr_fx[key]!='') {
                reset += '&'+[key]+'='+arr_fx[key]
                param =  reset.substr(1)
            }
        }
        submit_data(param, '/boss/user/identity/auth/enterpriseExamine');

    });
    $(".selectOne,.selectTwo").change(function(event) {
        //将所选的option的value值添加给select做添加自定义属性   便于查找
        $(this).attr('data-ind', $(this).val());
        //成功时候不显示失败的理由
        $(".selectOne").each(function(index, el) {
            if ($(this).val() == 3) {
                $(this).parents('tr').siblings('tr').show()
            } else {
                $(this).parents('tr').siblings('tr').hide()
            }
        });
    });
</script>
</body>
</html>
