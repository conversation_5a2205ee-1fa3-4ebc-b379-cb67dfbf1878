<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title></title>
</head>
<body>
	
</body>
<script>
var funArr = {

		parseSearch:function () {
			var search = {}
			var s = location.search.replace('?', '');
			if (!s) return search;
			s = s.split("&");

			for (var i = 0; i < s.length; i++) {
				search[s[i].split("=")[0]] = s[i].split("=")[1];
			}
			return search;
		},
	  	openWin:function (appurl,winName,iWidth,iHeight) { 
	  		// openWin('url','_newpage','',600);
	  		//width,height为空,默认使用下面已设的width,height
	        //参数:
	        //appurl : 转向网页的地址; 
	        //winName : 网页名称，可为空; 
	        //iWidth : 弹出窗口的宽度; 
	        //iHeight : 弹出窗口的高度; 
	       var iWidth,iHeight;
	       if(!iWidth)iWidth = '800';
	       if(!iHeight)iHeight = '600';
	       // 获得窗口的垂直位置 
	       var iTop = (window.screen.availHeight - 30 - iHeight) / 2; 
	       //获得窗口的水平位置 
	       var iLeft = (window.screen.availWidth - 10 - iWidth) / 2; 

	       window.open(appurl, winName, 'height=' + iHeight + ',,innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',status=no,toolbar=no,menubar=no,location=no,resizable=no,scrollbars=yes,titlebar=no'); 
	   }
	},
	userId = funArr.parseSearch().userId,
	mobile = funArr.parseSearch().mobile,
	userClass = funArr.parseSearch().userClass,
	userClassName = decodeURI(funArr.parseSearch().userClassName),
	identityType = funArr.parseSearch().identityType,
	identityTypeName = decodeURI(funArr.parseSearch().identityTypeName),
	// var userId = 123,
	// 	userClass = 1,
	// 	userClassName = 'duile',
	// 	identityType = 456,
	// identityTypeName = '个人车主',
	param = '?userId='+userId+'&userClass='+userClass+'&identityType='+identityType+'&userClassName='+userClassName+'&identityTypeName='+identityTypeName+'&mobile='+mobile;
	//1： 个人货主 2：企业货主 3：货站 4：工程机车 5：设备服务 6：个人车主 7： 板车司机 8：运输公司或车队 9：板车服务
	var goURL="";
	if(identityType == 1) {
		goURL='upload_gerenhuozhu.html';
		//funArr.openWin(+'','upload_gerenhuozhu',1000,600);
	}else if(identityType == 2) {
		goURL='upload_qiyehuozhu.html';
	}else if(identityType == 3) {
		goURL='upload_huozhan.html';
	}else if(identityType == 4) {
		goURL='upload_gongchengchesiji.html';
	}else if(identityType == 5) {
		goURL='upload_shebeifuwu.html';
	}else if(identityType == 6) {
		goURL='upload_gerenchezhu.html';
	}else if(identityType == 7) {
		goURL='upload_banchesiji.html';
	}else if(identityType == 8) {
		goURL='upload_yunshugongsi.html';
	}else if(identityType == 9) {
		goURL='upload_banchefuwu.html';
	}else{
		alert('error');
	};
	window.location.href=goURL+param;
</script>
</html>