<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>修改身份</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="../../css/theme.css" type="text/css" rel="stylesheet">
<link href="../../css/font-awesome.css" type="text/css" rel="stylesheet">
	<link href="../../css/base.css" type="text/css" rel="stylesheet">
<link href="../../css/identity.css" type="text/css" rel="stylesheet">


</head>
<body class="theme-blue">


<div class="content">
  <div class="header"> 
    <ul class="breadcrumb">
      <li>系统信息管理</li>
      <li class="active">找货权限管理</li>
      <li class="">修改</li>
    </ul>
  </div>
  <div class="main-content"> 
  <p class="useCareful">使用说明：本功能支持按不同的<span>注册一级身份</span>，对拨打货源电话按照【身份认证】，【车辆认证】，【会员缴费】进行不同顺序，不同条数的控制</p>
    <!--搜索条件-->
    <div class="searchCondition">
      <ul class="o-hidden hiddenList">

	      <li class="useSelect">
	        <label>用户注册身份</label>
	        <select class="">
	          <option>请选择</option>
	        </select>
	        <select class="">
	          <option>请选择</option>
	        </select>
	      </li>
      </ul>

	    <div class="orderList cf">
	    	<div class="fl">
				<label>身份认证控制顺序</label><input type="text"><i class="mustRed">*</i>
	    	</div>
	    	<div class="fl">
				<label>会员缴费控制顺序</label><input type="text"><i class="mustRed">*</i>
	    	</div>
	    	<div class="fl">
				<label>车辆认证控制顺序</label><input type="text"><i class="mustRed">*</i>
	    	</div>
	    </div>
	    <div class="numList cf">
	    	<div class="fl">
				<label>身份认证控制条数</label><input type="text"><i class="mustRed">*</i>
	    	</div>
	    	<div class="fl">
				<label>会员缴费控制条数</label><input type="text"><i class="mustRed">*</i>
	    	</div>
	    	<div class="fl">
				<label>车辆认证控制条数</label><input type="text"><i class="mustRed">*</i>
	    	</div>
	    </div>
	    <p class="useHint">注：控制顺序只能输入0，1，2，3，输入0时为无效</p>
    </div>
   
    <div class="errBox">错误提示错误提示</div>
    <div class="subBtn">
      <button class="button">保存</button>
    </div>
    
  </div>
 </div>
 
 <script type="text/javascript" src="../../js/jquery-1.11.1.min.js"></script>
 <script type="text/javascript" src="../../js/bootstrap.js"></script>
<script type="text/javascript">
</script>
</body>
</html>
