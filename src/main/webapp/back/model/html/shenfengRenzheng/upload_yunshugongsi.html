<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>认证页面-运输公司-上传</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/identity.css" type="text/css" rel="stylesheet">
  <link  href="../../css/validate.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div class="contentBox">
    <div class="titUrl cf">
      <h1 class=" fl">
        <span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span>用户信息管理</span><span>&nbsp;&gt;&nbsp;</span><span>身份认证录入</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage"></span>
      </h1>
    </div>
    <div class="main-content"> 
      <form name="validateForm" method="post" id="validateForm" action="../../../../boss/user/identity/auth/save" enctype="multipart/form-data" autocomplete="off">
        <input type="hidden" name="userId" id="userIdId">
        <input type="hidden" name="userClass" id="userClassId">
        <input type="hidden" name="identityType" id="identityTypeId">
        <!--搜索条件-->
        <div class="searchCondition">
          <ul class="o-hidden">
             <li>
              <label>注册手机号：</label>
              <span id="reg_phone_num"></span>
            </li>
            <li>
              <label>用户注册身份：</label>
              <span id="reg_status"></span>
            </li>
            <li>
              <label>公司名称：</label>
              <input class="input180" reg="^[\u4E00-\u9FA5]{2,30}$" tip="公司名称字数最多30字" name="enterpriseName" type="text"><i class="mustRed">*</i>
            </li>
            <li>
              <label>公司座机：</label>
              <input class="input180" name="enterprisePhone" reg="^\d{10}$|^\d{11}$|^\d{12}$" tip="企业座机必须输入10到12位数字" type="text"><i class="mustRed">*</i>
            </li>
          </ul>
        </div>
        <!-- 身份信息认证 -->
        <div class="userMessage">
          <h3 class="messageTit">身份信息认证</h3>
          <div class="messageNav">
            <label for="">真实姓名</label><input id="" name="trueName" type="text" maxlength="15" reg="^[\u4e00-\u9fa5]+·?[\u4e00-\u9fa5]+$" tip="请输入你的真实姓名"  /><i class="mustRed">*</i>
          </div>
          <div class="messageNav">
            <label for="">身份证号</label><input id="" name="idCard" onblur="tyt_Obj.identityBlur($(this));" type="text" tip="请输入18位身份证"><i class="mustRed">*</i>
          </div>
          <div class="messagePhoto cf">
            <div class="photoWare nomarginL fl">
              <h4><span>身份证正面照</span><i class="mustRed">*</i></h4>
              <div class="imgBox"><img class="mustImg" src="../../images/default.jpg" alt="身份证件照"></div>
              <span>文件名</span>
              <div class="uploadImgbox">
                <input id="" class="fileBtn" type="file" name="mainUrlPic" accept="image/gif,image/jpeg,image/jpg,image/png" onchange="fileInfo(this,$(this),0)">
                <button class="uploadBtn" type="button">浏览</button>
              </div>
            </div>
            <div class="photoWare fl">
              <h4><span>本人照</span><i class="mustRed">*</i></h4>
              <div class="imgBox"><img class="mustImg" src="../../images/default.jpg" alt="对接人本人照"></div>
              <span>文件名</span>
              <div class="uploadImgbox">
                <input id="file1" class="fileBtn" type="file" name="iPhotoUrlPic" accept="image/gif,image/jpeg,image/jpg,image/png" onchange="fileInfo(this,$(this),1)">
                <button class="uploadBtn" type="button">浏览</button>
              </div>
            </div>
            <div class="photoWare nomarginL fl">
              <h4><span>企业营业执照</span><i class="mustRed">*</i></h4>
              <div class="imgBox"><img class="mustImg" src="../../images/default.jpg" alt="资质证照"></div>
              <span>文件名</span>
              <div class="uploadImgbox">
                <input id="" class="fileBtn" type="file" name="licenseUrlPic" accept="image/gif,image/jpeg,image/jpg,image/png" onchange="fileInfo(this,$(this),1)">
                <button class="uploadBtn" type="button">浏览</button>
              </div>
            </div>
          </div>
        </div>
      </form>

      <div class="errBox" style="display: none;"></div>
      <div class="subBtn">
        <button class="submitBtn button">提交</button>
      </div>
      
    </div>
  </div>
  
  <script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="../../js/bootstrap.js"></script>
  <script type="text/javascript" src="../../js/uploadImg.js"></script>
  <script type="text/javascript" src="../../js/easy_validator.pack.js"></script>
  <script type="text/javascript" src="../../js/jquery.bgiframe.min.js"></script>
  <script type="text/javascript" src="../../js/formValidatorRegex.js"></script>
  <script type="text/javascript" src="../../js/identity.js"></script>
  <script type="text/javascript">
      var urlStr = window.location.href;
      var tempStr = urlStr.indexOf("/",urlStr.indexOf("/",7)+1);
      var website = urlStr.substring(0, tempStr);
      //    var website = 'http://api.teyuntong.net/manage_new';
var userId = tyt_Obj.parseSearch().userId,
userClass = tyt_Obj.parseSearch().userClass,
userClassName = decodeURI(tyt_Obj.parseSearch().userClassName),
identityType = tyt_Obj.parseSearch().identityType,
identityTypeName = decodeURI(tyt_Obj.parseSearch().identityTypeName),
mobile = tyt_Obj.parseSearch().mobile;
//用户手机和注册身份
$('#reg_status').html(identityTypeName);
$(".titUrl .currentPage").html(identityTypeName);
$('#reg_phone_num').html(mobile);
  /*
  设置用户一级身份的隐藏域
  */
  $('#userIdId').val(userId);
  $('#userClassId').val(userClass);
  $('#identityTypeId').val(identityType);  


  var mustInp = $('.mustRed').siblings('input');
  var fileBtn = $('.fileBtn');
  mustInp.focus(function(event) {
    $(".errBox").hide().html('');
  });
  fileBtn.click(function(event) {
    $(".errBox").hide().html('');
  });
  //提交操作
  $('.submitBtn').click(function() {
    $(".errBox").hide().html('');
    // tyt_Obj.mustRed()方法是验证必填项没有填
    if(tyt_Obj.mustRed()==1){
        $(".errBox").show().html('带*号信息必传，请上传')
     }else if(tyt_Obj.mustRed()==2){
       $(".errBox").show().html('填写的信息有误')
     }else{
        //提交链接 , 调用图片上传
        //upload_img('http://localhost/img_upload/upload.php');
        //提交后,清空
       // tyt_Obj.qingkong();
    	document.forms['validateForm'].submit();
      }
    });
  </script>
</body>
</html>
