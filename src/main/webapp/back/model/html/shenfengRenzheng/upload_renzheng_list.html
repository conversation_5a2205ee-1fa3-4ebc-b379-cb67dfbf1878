<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>员工上传认证列表</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="../../css/base.css" type="text/css" rel="stylesheet">
<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="../../css/theme.css" type="text/css" rel="stylesheet">
<link href="../../css/font-awesome.css" type="text/css" rel="stylesheet">
<link href="../../css/identity.css" type="text/css" rel="stylesheet">

</head>
<body class="theme-blue">
<div class="content">
  <div class="header"> 
    <ul class="breadcrumb">
      <li>系统信息管理</li>
      <li class="active">员工上传认证列表</li>
    </ul>
  </div>
  <div class="main-content"> 

   <a class="identity-authen1">点击弹出提示框</a>
   <a class="identity-authen2">点击弹出选择身份框</a>
  </div>
 </div>
 
 <!-- 提示框 -->
<div class="alert alert-danger alert-dismissible fade in" id="alert_danger" role="alert" style="display: none">
      <button type="button" class="close" aria-label="Close" id="close_btn">×</button>
      <h4>提  示</h4>
      <p class="tc f14">此用户已认证通过，无需上传</p>
      <p class="tc mt30">
      
        <button type="button" class="btn btn-default ml10" id="btn_default">我知道了</button>
      </p>
</div>

<!-- 选择用户注册身份 -->
<div class="alert alert-default alert-dismissible fade in" id="alert_default" role="alert" style="display: none1">
      <button type="button" class="close" aria-label="Close" id="close_identity_btn">×</button>
      <h4>请先选中用户注册身份</h4>
      <div class="identityDiv">
      <p class="identity-label">发货方：</p>
        <ul class="fl">
          <li><span class="btn btn-default selected">货站</span></li>
          <li><span class="btn btn-default">个人货主</span></li>
          <li><span class="btn btn-default">企业货主</span></li>
          <li><span class="btn btn-default">设备服务</span></li>
          <li><span class="btn btn-default">工程车司机</span></li>          
        </ul>
        <div class="clearfix"></div>
      </div>

      <div class="identityDiv">
      <p class="identity-label">车辆方：</p>
        <ul class="fl">
          <li><span class="btn btn-default">个人车主</span></li>
          <li><span class="btn btn-default">运输公司或车队</span></li>
          <li><span class="btn btn-default">板车司机</span></li>
          <li><span class="btn btn-default">板车服务</span></li>           
        </ul>
        <div class="clearfix"></div>
      </div>          
      <p class="tc mt20">    
        <button type="button" class="btn btn-default" id="close_upload_btn">关闭</button>  
        <button type="button" class="btn btn-danger ml20" id="upload_auth_btn">上传认证</button>        
      </p>
      <p class="error-block">注：此用户已上传过用户信息，如果进行此操作，将会将用户信息覆盖</p>
</div>

 <script type="text/javascript" src="../../js/jquery-1.11.1.min.js"></script>
 <script type="text/javascript" src="../../js/bootstrap.js"></script>

<!-- 弹出框js -->

<script>

$(function(){

$('.identity-authen1').on('click', function(event) {
  event.preventDefault();
   /*Act on the event */
  $('#alert_danger').show();
});


// 点击取消和关闭按钮

$('#close_btn, #btn_default').on('click', function(event) {
  event.preventDefault();
  /* Act on the event */
  $('#alert_danger').fadeOut('fast');
});

//选择用户身份 
$('.identity-authen2').on('click', function(event) {
  event.preventDefault();
  /* Act on the event */
  $('#alert_default').show();

});

$('#close_identity_btn, #close_upload_btn').on('click', function(event) {
  event.preventDefault();
  /* Act on the event */
  $('#alert_default').fadeOut('fast');
});

});


</script>
</body>
</html>
