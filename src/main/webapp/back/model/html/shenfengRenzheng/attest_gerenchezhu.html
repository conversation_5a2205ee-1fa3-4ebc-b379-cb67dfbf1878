<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title id="identityStatus"></title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <!-- <link href="../../css/viewer.min.css" rel="stylesheet" type="text/css" /> -->
  <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/identity.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <link  href="../../css/validate.css" rel="stylesheet" type="text/css">
  <script src="../../js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>

  <div class="contentBox">
    <h1 class="titUrl">
      <span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span>身份认证列表</span><span>&nbsp;&gt;&nbsp;</span><span>审核</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage"></span>
    </h1>
    <div class="main-content" id="mainContent">
      <form name="validateForm" method="post" id="validateForm" action=""  autocomplete="off">
        <!--搜索条件-->
        <div class="searchCondition">
          <ul class="o-hidden">
            <li>
              <label>注册手机号：</label>
              <span id="reg_phone_num"></span>
            </li>
            <li>
              <label>用户注册身份：</label>
              <span id="reg_status"></span>
            </li>
            <li style="width: auto">
              <label>销售审核身份：</label>
              <span>一级</span>
              <select name="" id="audit_status1" class="select180">
                <option class="intril" value="">--请选择--</option>
              </select>
              <span>二级</span>
              <select name="" id="audit_status2" class="select180">
                <option class="intril" value="">--请选择--</option>
              </select>
              <span>审核备注</span>
              <select name="" id="audit_status3" class="select180">
                <option class="intril" value="">--请选择--</option>
              </select>
            </li>

            <li>
              <label>提交认证时间：</label>
              <span id="authen_time"></span>
            </li>
            <li>
              <label>最新审核时间：</label>
              <span id="audit_time"></span>
            </li>
            <li style="width: auto">
              <label>认证状态：</label>
              <span>身份信息-<strong id="info_status_rz"></strong>，</span>
              <span>身份证正面照-<strong id="idcard_hand_rz"></strong>，</span>
              <span>本人照-<strong id="iPhoto_rz"></strong></span>
            </li>
            <li>
              <label>账号注销次数：</label>
              <span class="attest-text" id="cancellation_num"></span>
            </li>
            <li>
              <label>手机号归属地：</label>
              <span class="attest-text" id="phone_ownership"></span>
            </li>
            <li class="li-ip">
              <label>登录IP：</label>
              <span class="attest-text" id="login_ip"></span>
              <span class="attest-check">查看</span>
            </li>
            <li>
              <label>开户状态：</label>
              <span id="manBangAcctStatus"></span>
            </li>
            <li>
              <label>初始货量</label>
              <select name="" id="Initial_status" class="select180">
                <option class='intril' value='0'>--请选择--</option>
                <option class='intril' value='1'>1-20条</option>
                <option class='intril' value='2'>21-100条</option>
                <option class='intril' value='3'>101-500条</option>
                <option class='intril' value='4'>501条以上</option>
              </select>
            </li>
            <li>
              <label>车辆数量</label>
              <select name="" id="car_status" class="select180">
                <option class='intril' value='0'>--请选择--</option>
                <option class='intril' value='1'>1辆</option>
                <option class='intril' value='2'>2-5辆</option>
                <option class='intril' value='3'>6-10辆</option>
                <option class='intril' value='4'>11辆以上</option>
              </select>
            </li>
            <li>
              <label>身份核实状态：</label>
              <select name="" id="Identity_status" class="select180">
                <option value="0">未核实</option>
                <option value="1">已核实</option>
              </select>
            </li>
            <li class="li-ip">
              <label>活体检测：</label>
              <span class="attest-text" id="face_verify"></span>
            </li>
            <li class="li-ip">
              <label>二要素检测：</label>
              <span class="attest-text" id="real_verify"></span>
            </li>
          </ul>
        </div>
        <!--认证-->
        <div class="userMessage">
          <!-- 身份信息认证 -->
          <div class="messageTxt">
            <table width="390" border="0" class="table-bordered">
              <tr><th colspan="2"><h4>身份信息认证</h4></th></tr>
              <tr>
                <td>真实姓名</td>
                <td><input id="true_name" class="input180" type="text" maxlength="15" reg="^[\u4e00-\u9fa5]+·?[\u4e00-\u9fa5]+$" tip="请输入你的真实姓名"  /></td>
              </tr>
              <tr>
                <td>身份证号</td>
                <td><input id="id_card" class="input180" onblur="tyt_Obj.identityBlur($(this));" type="text" tip="请输入18位身份证"></td>
              </tr>
              <tr>
                <td>性别</td>
                <td><input id="sex_type" class="input180" type="text" tip="请输入性别" reg="^['男'|'女']$" /></td>
              </tr>
              <tr>
                <td>证件到期时间</td>
                <td>
                  <label style="margin-right: 20px"><input onchange="idCardLongTermCheng(this)" type="radio" name="idCardLongTerm" value="1">长期</label>
                  <input onchange="idCardLongTermCheng(this)" type="radio" name="idCardLongTerm" value="0" >
                  <input class="input60" style="width: 75px" name="idCardValidDate" type="text" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"  pattern="yyyy-MM-dd" >
                </td>
              </tr>

              <tr>
                <td>身份信息认证状态</td>
                <td><select name="" id="info_status"  data-ind="0" class="select180 selectBtn">
                  <option value="0">请选择认证结论</option>
                  <option value="1">认证成功</option>
                  <option value="2">认证失败</option>
                </select></td>
              </tr>
              <tr style="display: none;">
                <td>失败原因</td>
                <td><select name="" id="info_failure_reason" data-ind="0"  class="select180 selectBtnFail">
                  <option value="0">请选择失败原因</option>
                  <option value="1">身份证号或姓名输入有误</option>
                </select></td>
              </tr>
            </table>
          </div>
          <!-- 照片认证 -->
          <div class="messagePhoto cf">
            <div class="photoWare nomarginL mainUrl_big fl">
              <h4><span>身份证正面照</span></h4>
              <div class="imgBox"><img id="mainUrl" rotate="0" src="../../images/default.jpg" alt="身份证件照"></div>
              <div class="previewImgbox">
                <a id="mainUrl_big">查看原图</a>
                <span>|</span>
                <a href="javascript:;" style="cursor: pointer;" onclick="doLeft('mainUrl')">向左旋转</a>
                <span>|</span>
                <a href="javascript:" style="cursor: pointer;" onclick="doRight('mainUrl')">向右旋转</a>
              </div>

              <table width="100%" border="0" class="table-bordered">
                <tr>
                  <td>身份证正面照认证状态</td>
                  <td><select name="" id="main_status" data-ind="0" class="select180 selectOne">
                    <option value="0">请选择认证结论</option>
                    <option value="1">认证成功</option>
                    <option value="2">认证失败</option>
                  </select></td>
                </tr>
                <tr style="display: none;">
                  <td>失败原因</td>
                  <td><select name="" id="main_failure_reason" data-ind="0" class="select180 selectTwo">
                    <option value="0">请选择失败原因</option>
                    <option value="1">身份证模糊</option>
                    <option value="2">身份证与本人不符</option>
                    <option value="3">身份证信息不完整</option>
                    <option value="4">身份证为无效证件</option>
                  </select></td>
                </tr>
              </table>
            </div>
            <!-- 本人照 -->
            <div class="photoWare iPhotoUrl_big fl">
              <h4><span>身份证反面照</span></h4>
              <div class="imgBox"><img id="iPhotoUrl" rotate="0" src="../../images/default.jpg" alt="身份证件照"></div>
              <div class="previewImgbox">
                <a id="iPhotoUrl_big" target="_blank">查看原图</a>
                <span>|</span>
                <a href="javascript:;" style="cursor: pointer;" onclick="doLeft('iPhotoUrl')">向左旋转</a>
                <span>|</span>
                <a href="javascript:" style="cursor: pointer;" onclick="doRight('iPhotoUrl')">向右旋转</a>
              </div>

              <table width="100%" border="0" class="table-bordered">
                <tr>
                  <td>反面照认证状态</td>
                  <td><select name="" id="iPhoto_status" data-ind="0" class="select180 selectOne">
                    <option value="0">请选择认证结论</option>
                    <option value="1">认证成功</option>
                    <option value="2">认证失败</option>
                  </select></td>
                </tr>
                <tr style="display: none;">
                  <td>失败原因</td>
                  <td><select name="" id="iPhoto_failure_reason" data-ind="0" class="select180 selectTwo">
                    <option value="0">请选择失败原因</option>
                    <option value="1">本人照模糊</option>
                    <option value="2">本人照不完整</option>
                    <option value="3">本人照与证件不符</option>
                    <option value="4">本人照无效</option>
                    <option value="5">身份认证不合法</option>
                    <option value="6">身份证反面模糊</option>
                    <option value="7">身份证反面不完整</option>
                    <option value="8">身份证反面无效</option>
                    <option value="9">身份证不合法</option>
                    </select></td>
                  </tr>
                </table>
              </div>

              <!-- 道路运输经营许可证 -->
              <div class="photoWare nomarginL qualificationsUrl_big fl">
                <h4><span>道路运输经营许可证（非必传）</span></h4>
                <div class="imgBox"><img id="qualificationsUrl" rotate="0" src="../../images/default.jpg" alt="道路运输经营许可证"></div>
                <div class="previewImgbox">
                  <a id="qualificationsUrl_big">查看原图</a>
                  <span>|</span>
                  <a href="javascript:;" style="cursor: pointer;" onclick="doLeft('qualificationsUrl')">向左旋转</a>
                  <span>|</span>
                  <a href="javascript:" style="cursor: pointer;" onclick="doRight('qualificationsUrl')">向右旋转</a>
                </div>
              </div>

            </div>

          </div>
        </form>
        <div class="errBox" style="display: none;"></div>
        <!-- 黑名单管理 -->
    <div class="blackListAuto">
      <div class="blackList"></div>
    </div>
      </div>

      <!-- 弹出框提示框 -->

      <div class="alert alert-danger fade in" id="alert_danger" role="alert" style="display: none">
        <button type="button" class="close" aria-label="Close" id="close_btn">×</button>
        <h4>提示</h4>
        <p class="alertContent"></p>
        <div class="tc buttonBox">
          <button type="button" class="button" id="btn_default">我知道了</button>
        </div>
      </div>

    </div>

    <script src="../../js/jquery-1.7.2.min.js"></script>
    <script src="../../js/bootstrap.js"></script>
    <script src="../../js/easy_validator.pack.js"></script>
    <script src="../../js/jquery.bgiframe.min.js"></script>
    <!-- <script src="../../js/viewer.min.js"></script> -->
    <script src="../../js/formValidatorRegex.js"></script>
    <script src="../../../jurisdiction/jsp/js/common.js"></script>
    <script src="../../js/identity.js"></script>
     <!-- <script src="../../js/indent_blackList.js"></script> -->
    <script src="../../js/ident_common.js"></script>


<script type="text/javascript">
      var parsesearchId = tyt_Obj.parseSearch().userId;

// 查看原图的打开方式

$('#mainUrl_big').click(function(event) {
  var this_src = $(this).parents('.previewImgbox').siblings('.imgBox').find('img').attr('src');
  tyt_Obj.openWinAuto('../imgLook/imgLook.html?imgUrl=' + this_src, '', 400, 450, 0, 0);
  // lookBigImg('mainUrl_big')
});

$('#iPhotoUrl_big').click(function(event) {
  // lookBigImg('iPhotoUrl_big')
    var this_src = $(this).parents('.previewImgbox').siblings('.imgBox').find('img').attr('src');
    var this_src2 = $('#mainUrl_big').parents('.previewImgbox').siblings('.imgBox').find('img').attr('src');
    tyt_Obj.openWinAuto('../imgLook/imgLook.html?imgUrl=' + this_src, '', 400, 450, 0, 450);
    tyt_Obj.openWinAuto('../imgLook/imgLook.html?imgUrl=' + this_src2, '', 400, 450, 0, 0);
});

$('#qualificationsUrl_big').click(function(event) {
  // lookBigImg('qualificationsUrl_big')
  var this_src = $(this).parents('.previewImgbox').siblings('.imgBox').find('img').attr('src');
  tyt_Obj.openWinAuto('../imgLook/imgLook.html?imgUrl=' + this_src, '', 400, 450, 0, 0);
});

// 身份+图片的失败下拉框样式
tyt_Obj.selectshowHide();

function doLeft(name) {
    var img = $("#" + name);
    var rDeg = Number(img.attr("rotate")) - 90;
    img.attr("rotate",rDeg)
    img.css('transform','rotate('+ rDeg +'deg)');
}

function doRight(name) {
    var img = $("#" + name);
    var rDeg = Number(img.attr("rotate")) + 90;
    img.attr("rotate",rDeg)
    img.css('transform','rotate('+ rDeg +'deg)');
}

//submit提交
//jquery1.9之前用live，1.9之后用on
$(".submitBtn").live('click', function(event) {
  event.preventDefault();
  /* Act on the event */

  if(tyt_Obj.selectEach()==1){
    alert('认证状态不能为空');
    return false;
  }else if(tyt_Obj.selectEach()==2){
    alert('请选择失败原因');
    return false;
  }else if(tyt_Obj.selectEach()==3){
    alert('认证状态不能为空');
    return false;
  }else if(tyt_Obj.selectEach()==4){
    alert('请选择失败原因');
    return false;
  }

  //判断input
  if(tyt_Obj.idCardValidPageindent()==1) {
    alert('身份信息认证不能为空');
    return false;
  }else if(tyt_Obj.idCardValidPageindent()==2){
    alert('身份信息认证有误');
    return false;
  }

// 提交数据

var trueName = $('#true_name').val();
var idCard = $('#id_card').val();
var deliverTypeOne = $("#audit_status1").find("option:selected").val();
var deliverTypeTwo = $("#audit_status2").find("option:selected").val();
var deliverTypeThree = $("#audit_status3").find("option:selected").val();

var mainStatus = $("#main_status option:selected").val();
var mainFailureReason = $("#main_failure_reason option:selected").text();
var infoStatus = $("#info_status option:selected").val();
var infoFailureReason = $("#info_failure_reason option:selected").text();
var iPhotoStatus = $("#iPhoto_status option:selected").val();
var iPhotoFailureReason = $("#iPhoto_failure_reason option:selected").text();
var Initial_status = $("#Initial_status").find("option:selected").val();
var car_status = $("#car_status").find("option:selected").val();
var Identity_status = $("#Identity_status").find("option:selected").val();
// 6410 新增证件到期时间
var idCardValidDate = $("input[name='idCardValidDate']").val();
var idCardLongTerm = $("input:radio[name='idCardLongTerm']:checked").val()

if(deliverTypeOne==''||deliverTypeTwo==''){
  alert('请选择销售身份')
  return false;
}

// 如果选择成功状态，失败原因置为空。

if(mainStatus==1){
  mainFailureReason = ''
}
if(infoStatus==1){
  infoFailureReason = ''
}

if(iPhotoStatus==1){
  iPhotoFailureReason = ''
}
if (Initial_status == 0) {
  Initial_status = ''
}
if (car_status == 0) {
  car_status = ''
}

var arr_fx = {
  userId:parsesearchId,
  trueName:trueName,
  idCard:idCard,
  deliverTypeOne:deliverTypeOne,
  deliverTypeTwo:deliverTypeTwo,
  deliverTypeThree:deliverTypeThree,
  mainStatus:mainStatus,
  mainFailureReason:mainFailureReason,
  infoStatus:infoStatus,
  infoFailureReason:infoFailureReason,
  iPhotoStatus:iPhotoStatus,
  //6270将手持照片改为身份证反面照，为兼容老版本同时将backStatus也保存一份
  backStatus:iPhotoStatus,
  iPhotoFailureReason:iPhotoFailureReason,
  //6270将手持照片改为身份证反面照，为兼容老版本同时将backFailueReason也保存一份
  backFailueReason:iPhotoFailureReason,
  initialNum:Initial_status,
  initialCarNum:car_status,
  auditStatus:Identity_status,
  idCardValidDate:idCardValidDate,
  idCardLongTerm:idCardLongTerm
}

var reset  = '';
var param;
for (var key in arr_fx)
{
  if (arr_fx[key]!='') {
    reset += '&'+[key]+'='+arr_fx[key]
    param =  reset.substr(1)
  }
}

submit_data(param);

});

</script>

</body>

</html>
