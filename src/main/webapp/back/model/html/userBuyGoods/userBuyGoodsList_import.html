<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>商品订单管理</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="../../js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="../../js/bootstrap.js"></script>

    <script type="text/javascript" src="../../../js/common_openwin.js"></script>
    <script type="text/javascript" src="../../js/common_cookie.js"></script>
</head>
<style>
    #payModal{
        margin-top: -200px;
    }
    .pay-method-radio{
        width: 15px!important;
        height: 15px!important;
    }
    .pay-method-img{
        width: 79px!important;
        height: 30px!important;
        position: relative;
        top: -2px;
    }
    #submitPayModal{
        margin-top:-105px;
    }
    #submitPayModal label{
        width: 280px;
        text-align: left;
        text-indent: 30px;
    }
    #submitPayModal img{
        margin: 0 auto;
        width:126px;
        height: 126px;
        display: block;
    }
    #submitPayModal p{
        text-align: center;
    }
    .pay-success{
        font-size: 20px;
    }
    .goods-sm{
        position: relative;
        top: -33px;
    }
    #payModal ul li input,#payModal ul li label{
        padding: 0;
    }
    .excelBox .alertContent {
        margin: 15px auto;
        width: 280px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: 14px;
        color: #333;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    .fileBtnBox {
        width: 80px;
        height: 32px;
        position: relative;
    }
    .fileBtn, .rule_true {
        position: absolute;
        left: 0;
        top: 0;
        display: inline-block;
        width: 80px;
        height: 32px;
    }
    .fileBtn {
        opacity: 0;
        filter: alpha(opacity=0);
        z-index: 530;
    }
    .rule_true {
        z-index: 525;
    }
    .outBgColor{
        width: 100%;
        height: 100%;
        position: fixed;
        _position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        background: #000;
        opacity: 0.7;
        filter: alpha(opacity=70);
        z-index: 10;
    }
    .dataTable{
            overflow:hidden;
        }
</style>
<body>
<div class="contentBox">
    <div class="titUrl cf">
        <h1 class=" fl">
            <span>当前所在位置：</span><span>财务管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">商品订单管理</span>
        </h1>
    </div>

    <div class="main-content">
        <!--搜索条件-->
        <div class="searchBox" id="searchCondition">
            <ul class="searchUl searchUlNone cf">
                <li>
                    <label>用户ID：</label><input class="input180" tname="userId" type="text" />
                </li>
                <li>
                    <label>用户姓名：</label><input class="input180" tname="userName" type="text" />
                </li>
                <li><label>手机号：</label><input class="input180" tname="cellPhone" type="text" /></li>
                <li>
                    <label for="">商品类型：</label>
                    <select tname="goodsType" id="goodsType">
                        <option class="firstOption" value="">请选择</option>
                        <option value="1">货会员</option>
                        <option value="2">车会员</option>
                        <option value="3">组合会员</option>
                        <option value="4">发货次卡</option>
                        <option value="10">加购商品</option>
                      <!--  <option value="5">车体验</option>
                        <option value="6">车试用</option>
                        <option value="7">n次x年找货卡</option>
                        <option value="8">货体验</option>
                        <option value="9">n次x年发货卡</option>-->
                    </select>
                </li>
                <li>
                    <label for="">订单状态：</label>
                    <select tname="ordersStatus" id="ordersStatus" onchange="">
                        <option class="firstOption" value="">请选择</option>
                        <option value="0">待支付</option>
                        <option value="1">支付失败</option>
                        <option value="2">支付成功</option>
                        <option value="3">已退款</option>
                    </select>
                </li>
                <li>
                    <label>订单号：</label><input class="input180" tname="orderId" type="text" />
                </li>
                <li style="width: 280px;">
                    <label for="">创建时间：</label>
                    <input tname="startCtime" backTname="startCtime" id="startCtime" onclick="WdatePicker({readOnly:true,dateFmt:'yyyy-MM-dd', maxDate:'#F{$dp.$D(\'endCtime\')}'})" class="pinfoTable" style="width: 80px;" type="text" name="" value="" readonly="">
                    <span>-</span>
                    <input tname="endCtime" backTname="endCtime" id="endCtime" onclick="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd', minDate:'#F{$dp.$D(\'startCtime\')}', startDate:'#F{$dp.$D(\'startCtime\')}' })" class="pinfoTable" style="width: 80px;" type="text" name="" value="" readonly="">
                </li>
            </ul>
        </div>

        <!--页面信息-->
        <div class="pageMessage">
            <div class="searchBtn">
                <ul class="btnBox cf">
                    <li><input type="button" class="button" value="查询" onclick="optFn.loadList()"/></li>
                    <li><input type="reset" class="button" value="重置" onclick="tytTool.formReset($('.searchUl'),optFn.loadList)"/></li>
                    <li><input type="button" class="button" value="下载模板" onclick="optFn.downloadTemplate()"/></li>
                    <li><input type="button" class="button" value="导入" onclick="optFn.importFile()"/></li>
                </ul>
            </div>
            <div class="clearfix"></div>
            <div class="messageRight fr"> </div>
        </div>
        <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
        <div class="dataTable">
            <!--内容列表-->
            <table width="100%" border="0" class="tableBox">
                <tr class="tableTh">
                    <th>ID</th>
                    <th>订单号</th>
                    <th>用户ID</th>
                    <th>用户名</th>
                    <th>商品名称</th>
                    <th>商品类型</th>
                    <th>商品价格(元)</th>
                    <th>支付价格(元)</th>
                    <th>支付渠道</th>
                    <th>订单状态</th>
                    <th>创建时间</th>
                    <th>支付时间</th>
                    <th>退款时间</th>
                    <th>操作人</th>
                    <th>退款人</th>
                    <th>操作</th>
                </tr>
                <tbody id="list">
                </tbody>
            </table>
        </div>

        <!-- 遮罩层div -->
        <div class="outBgColor" style="display: none;"></div>
        <!-- 缴费弹框 -->
        <div class="alert alert-danger fade in alert-height" style="display: none;" id="payModal">
            <div class="shadow-bg"></div>
            <div class="innerLook">
                <h2>购买商品缴费</h2>
                <div class="addWhiteUser cf">
                    <input type="text" hidden id="id" data-type="" tname="id">
                    <ul>
                        <li>
                            <label for="">手机号：</label>
                            <input type="text" placeholder="请输入手机号" maxlength="30" id="cellPhone" tname="cellPhone" onBlur="optFn.getUserMsg()"/>
                        </li>
                        <li id="userIdBox">
                            <label for="">用户ID：</label>
                            <input type="text" placeholder="" maxlength="30" id="userId" tname="userId" style="border:none;" readonly/>
                        </li>
                        <li id="userNameBox">
                            <label for="">用户名：</label>
                            <input type="text" placeholder="" maxlength="30" id="userName" tname="userName" style="border:none;" readonly/>
                        </li>
                        <li>
                            <label for="">商品名称：</label>
                            <select tname="goodsName" id="goodsName" onchange="optFn.changeGoodsFun(this)"></select>
                        </li>
                        <li id="priceBox">
                            <label for="">商品价格：</label>
                            <input type="text" placeholder="请输入商品价格" maxlength="30" id="price" tname="price" style="border:none;" readonly/>
                        </li>
                        <li id="remarkBox">
                            <label for="" class="goods-sm">商品说明：</label>
                            <textarea  class="note" cols="27" rows="3" maxlength="500" tname="remark" id="remark" placeholder="" style="border:none;" readonly></textarea>
                        </li>
                        <li>
                            <label for="" id="">支付方式：</label>
                            <input class="pay-method-radio" type="radio" tname="payChannelId" id="payChannelId" checked>
                            <img src="" class="pay-method-img" alt="*"/>
                        </li>
                    </ul>
                    <div class="makeSureBtn" style="height:32px;margin: 0 auto;text-align: center;">
                        <input class="button" type="button" onclick="optFn.getQRImg()" value="提交">
                        <input class="button" type="button" onclick="optFn.resetValue()" value="重置">
                    </div>
                </div>
            </div>
            <i class="closelookBtn" onclick="optFn.showHideAdd('hide','payModal')">×</i>
        </div>

        <!-- 提交支付弹框 -->
        <div class="alert alert-danger fade in alert-height" style="display: none;" id="submitPayModal">
            <div class="innerLook">
                <h2>商品缴费</h2>
                <div class="addWhiteUser cf">
                    <ul>
                        <li>
                            <label for="">手机号：<span class="cell-phone"></span></label>
                        </li>
                        <li>
                            <label for="">商品名称：<span class="goods-name"></span></label>
                        </li>
                        <li>
                            <label for="">缴费金额：<span class="goods-price"></span>元</label>
                        </li>
                    </ul>
                    <img class="pay-QR-img" src="" alt="*">
                    <div class="pay-msg"></div>
                </div>
                <div class="makeSureBtn closePayModalBtn" style="height:32px;margin: 10px auto 0;text-align: center;display: none;">
                    <input class="button" type="button" onclick="optFn.closePayModal()" value="确定">
                </div>
            </div>
            <i class="closelookBtn" onclick="optFn.showHideAdd('hide','submitPayModal')">×</i>
        </div>

        <!-- 确认退款弹框 -->
        <div class="alert alert-danger fade in alert-height" style="display: none;" id="confirmRefundModal">
            <div class="innerLook">
                <h2>确认退款</h2>
                <div class="addWhiteUser cf">
                    <div class="notice-tip"><p style="color:#a94442;">请仔细确认以下退款信息，确认无误后，点击"确定"按钮即可进行退款操作</p></div>
                    <ul>
                        <li></li>
                        <li>
                            <label for="">ID：</label>
                            <span id="user_buy_goods_id"></span>
                        </li>
                        <li>
                            <label for="">支付订单号：</label>
                            <span id="confirm_order_id"></span>
                        </li>
                        <li>
                            <label for="">用户名：</label>
                            <span id="confirm_user_name"></span>
                        </li>
                        <li>
                            <label for="">手机号：</label>
                            <span id="confirm_cell_phone"></span>
                        </li>
                        <li>
                            <label for="">商品名称：</label>
                            <span id="confirm_goods_name"></span>
                        </li>
                        <li>
                            <label for="">退款金额：</label>
                            <span id="confirm_goods_price"></span>元
                        </li>
                    </ul>
                </div>
                <div class="makeSureBtn closePayModalBtn" style="height:32px;margin: 10px auto 0;text-align: center;">
                    <input class="button" type="button" onclick="optFn.orderConfirmRefund()" value="确定">&nbsp;&nbsp;&nbsp;&nbsp;
                    <input class="button" type="button" onclick="optFn.closeRefundModal()" value="取消">
                </div>
            </div>
            <i class="closelookBtn" onclick="optFn.closeRefundModal()">×</i>
        </div>
        <!--JQ分页-->
        <div id="pageNumberBigBox" class="cf">
            <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
        </div>
    </div>

    <!-- 导入引导框 -->
    <div class="outBgColor" style="display: none;" id="import_file"></div>
    <div class="outLook excelBox" style="display: none;">
        <div class="innerLook">
            <h2>导入商品订单数据</h2>
            <p class="alertContent">请点击"浏览"选择需要导入的模板文件</p>
            <div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
                <div class="fileBtnBox fl">
                    <input class="fileBtn" id="fileField" type="file" name="carteamExcel" accept=".csv" onchange="optFn.fileInfo(this,$(this))">
                    <button class="rule_true">浏览</button>
                </div>
                <button id="uploadFile" onclick="optFn.uploadFile()" class="close_block fr">上传</button>
            </div>
        </div>
        <i class="closelookBtn" aria-label="Close" onclick="optFn.importFileClose()">×</i>
    </div>
</div>
</body>
<script type="text/javascript" src="../../js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="../../js/infoMessage.js"></script>
<script src="../../js/My97DatePicker/WdatePicker.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script type="text/javascript" src="../../js/userBuyGoods/userBuyGoodsList_import.js"></script>

<script>
    window.JSESSION_ID_TRUE = true;
</script>
</html>

