<template>
  <div class="com-wap" v-show="iscaseder">
    <div class="address-wrap" :class="{ 'map-wrap-big': !addressFlag }">
      <span class="address-close no-select" @click="onClose">×</span>
      <span
        class="no-select please-select"
        v-show="oneList.length"
        :class="{ 'tab-p-s-active': listFlag == 1 }"
        @click="selectProFun()"
        >{{ province }}</span
      >
      <span
        class="no-select please-select"
        v-show="twoList.length"
        :class="{ 'tab-p-s-active': listFlag == 2 }"
        @click="selectCityFun()"
        >{{ city }}</span
      >
      <span
        class="no-select please-select"
        v-show="threeList.length"
        :class="{ 'tab-p-s-active': listFlag == 3 }"
        @click="selectAreaFun()"
        >{{ area }}</span
      >
      <p class="blue-line"></p>
      <ul v-show="addressFlag" class="address-list clear-float">
        <li
          class="address-item no-select"
          @click="clickOneFun(item)"
          v-show="listFlag == 1"
          :class="item.flag ? 'active-item' : ''"
          v-for="(item, index) in this.oneList"
        >
          <span class="address-item-sub">
            {{ item.city}}
          </span>
        </li>
        <li
          class="address-item no-select"
          @click="clickTwoFun(item)"
          :display="listFlag == 2"
          v-show="listFlag == 2"
          :class="item.flag ? 'active-item' : ''"
          v-for="(item, index) in twoList"
        >
          <span class="address-item-sub">{{
            item.city ? item.city : item.label
          }}</span>
        </li>
        <li
          class="address-item no-select"
          @click="clickThreeFun(item)"
          v-show="listFlag == 3"
          :class="item.flag ? 'active-item' : ''"
          v-for="(item, index) in threeList"
        >
          <span class="address-item-sub">{{
            item.city ? item.city : item.label
          }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
module.exports = {
  props: {
    iscaseder: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    selectval: {
      type: String,
      default: () => {
        return "";
      },
    },
    addressList: {
      type: Array,
      default: () => {
        return [];
      },
    }
  },
  //components: {},

  data: function () {
    return {
      listFlag: 1,
      twoList: [],
      threeList: [],
      province: "请选择",
      city: "请选择",
      area: "请选择",
      modalFlag: false,
      addressFlag: true,
      historyAddress: [
        111,
        222,
        45678,
        4455677777,
        22222222,
        99999999,
        55665655556,
        343434343,
        "aaaaa",
        1999999,
      ],
      // 级联选择值
      casederSelectVal: "",
      spinShow: false,
    };
  },
  created() {
    //  this.getAddressList();
    // this.onClose();
  },
  watch: {
    watchMulti(val) {
      if (val.iscaseder && val.oneList.length > 0 && val.selectval != "") {
        this.initData(); //清除上一次赋值信息，避免信息填充错乱情况
        // this.setData();
      }
      // 当选中值为空的时候恢复初始化状态，解决在当选择在发一条的时候上次的选中数据还存在
      if (val.selectval == "") {
        this.initData(); //清除上一次赋值信息，避免信息填充错乱情况
      }
    },
  },
  computed: {
    // 同时监听等多个属性，为解决在初始化渲染时必须先获取数据的问题
    watchMulti() {
      return {
        iscaseder: this.iscaseder,
        oneList: this.oneList,
        selectval: this.selectval,
      };
    },
    oneList(){
      console.log(this.addressList)
      return this.addressList;
    }
  },
  //  // activated() {},
  methods: {
    initData: function () {
      this.listFlag = 1;
      this.province = "请选择";
      this.city = "请选择";
      this.area = "请选择";
      this.oneList.forEach((item) => {
        item.flag = false;
        return item;
      });
      this.twoList = [];
      this.threeList = [];
    },
    onClose() {
      this.$emit("update:iscaseder", false);
    },
    selectProFun() {
      this.listFlag = 1;
      this.addressFlag = true;
    },
    selectCityFun() {
      this.listFlag = 2;
      this.addressFlag = true;
    },
    selectAreaFun() {
      this.listFlag = 3;
      this.addressFlag = true;
    },
    clickOneFun(item) {
      this.oneList.forEach((ele, index) => {
        ele.flag = false;
      });
      item.flag = true;
      this.province = item.city ? item.city : item.label;
      this.listFlag = 2;
      this.twoList = item.children;
      //点击第一个tab  重置
      this.city = "请选择";
      this.area = "请选择";
    },
    clickTwoFun(item) {
      if (this.twoList.length) {
        this.twoList.forEach((ele, index) => {
          ele.flag = false;
        });
        item.flag = true;
        this.city = item.city ? item.city : item.label;
        if (item.children) {
          this.listFlag = 3;
          this.threeList = item.children;
        } else {
          this.setCasederVal();
        }
        //点击第2个tab  重置
        this.area = "请选择";
      }
    },
    clickThreeFun: function (item) {
      if (this.threeList.length) {
        this.threeList.forEach((ele, index) => {
          ele.flag = false;
        });
        item.flag = true;
        this.area = item.city ? item.city : item.label;
        this.setCasederVal();
      }
    },
    /**
     * @description: 设置级联选中值，当选到最后一层时赋值
     */
    setCasederVal: function () {
      var valArr = [this.province, this.city, this.area];
      var casederSelectVal = valArr.slice(0, this.listFlag).join("/");
      var selectObj = {
        selectData: []
          .concat(
            this.oneList.filter((item) => {
              var name = item.city ? item.city : item.label;
              return name === this.province;
            })
          )
          .concat(
            this.twoList.filter((item) => {
              var name = item.city ? item.city : item.label;
              return name === this.city;
            })
          )
          .concat(
            this.threeList.filter((item) => {
              var name = item.city ? item.city : item.label;
              return name === this.area;
            })
          ),
        selectVal: casederSelectVal,
      };
      console.log(selectObj);
      this.$emit("caseder-select-val", selectObj);
      this.onClose();
    }
  },
};
</script>
<style>
.com-wap {
  position: absolute;
  z-index: 1000;
  bottom: 0;
  top: 60px;
}
.address-close {
  margin: 10px;
  position: absolute;
  right: 0px;
  top: -5px;
  color: #308de3;
  font-size: 40px;
}

.address-wrap {
  width: 530px;
  min-height: 280px;
  position: absolute;
  top: 0;
  left: 0;
  background: #fff;
  box-shadow: 0 4px 12px 0 rgba(0, 55, 147, 0.1);
  text-align: left;
}

.address-list {
  font-family: MicrosoftYaHei;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  padding: 10px 0px;
}
.address-item {
  width: 75px;
  height: 36px;
  line-height: 15px;
  float: left;
  position: relative;
}
.address-item-sub {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: block;
  width: 100%;
}
.active-item {
  color: #308de3;
  background: #daedff;
  border-radius: 2px;
  font-weight: bold;
}
.please-select {
  background: #f2f2f2;
  box-shadow: 0 1px 8px 0 rgba(67, 129, 230, 0.15);
  border-radius: 3px 3px 0px 0px;
  min-width: 100px;
  padding: 0px 10px;
  height: 40px;
  display: inline-block;
  text-align: center;
  line-height: 40px;
  font-family: MicrosoftYaHei-Bold;
  font-size: 14px;
  color: #888;
  text-align: center;
  font-weight: bold;
  margin: 10px 5px 0 5px;
}

.tab-p-s-active {
  background: #ecf4ff;
  color: #308de3;
}

.please-select:nth-of-type(1) {
  margin-left: 10px;
}

.blue-line {
  border: 1px solid #308de3;
}

.address-b-btn-box {
  text-align: center;
  margin: 0px 0 0 20px;
  position: absolute;
  left: 0;
  bottom: 10px;
  width: 100%;
}
.address-b-btn-box button {
  width: 100px !important;
  height: 36px;
  margin-top: 10px;
}
.address-b-btn-box button span {
  position: relative;
  top: -3px;
}

#container {
  width: 100%;
  height: 90%;
}

.map-btn {
  background: #4381e6;
  border-radius: 3px;
  text-align: center;
  font-family: MicrosoftYaHei-Bold;
  font-size: 14px;
  color: #ffffff;
  width: 280px;
  height: 32px;
  line-height: 32px;
  font-weight: bold;
  position: relative;
  top: -60px;
  margin: 0 auto;
}

.history-add-list {
  width: 298px;
  top: 39px;
  font-family: MicrosoftYaHei;
  font-size: 14px;
  color: #333333;
}

.com-wap .map-wrap-big {
  width: 700px;
  height: 450px;
}
</style>
