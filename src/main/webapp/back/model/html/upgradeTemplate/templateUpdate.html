<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title></title>
	<link href="../../css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableModel.css" type="text/css" rel="stylesheet">
  <link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
  <link href="../../css/upgrade.css" type="text/css" rel="stylesheet">
	<link rel="stylesheet" href="../../css/carSaleEdit.css">
	<link rel="stylesheet" href="../../css/upgradeTemplate/template.css">

  <script type="text/javascript" src="../../js/jquery-1.7.2.min.js"></script>
  <script type="text/javascript" src="../../../jurisdiction/jsp/js/common.js"></script>
  <script type="text/javascript" src="../../js/My97DatePicker/WdatePicker.js"></script>
</head>
<body>
	<div class="contentBox">
		<h1 class="titUrl">
			<span>当前所在位置：</span><span>客户端升级管理</span><span>&nbsp;&gt;&nbsp;</span><span>特运通老APP端升级 </span><span>&nbsp;&gt;&nbsp;</span><span>定向用户升级 </span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">创建模板</span>
		</h1>
		<div class="systemParameter">
	    <div class="pageContext">
	      <ul class="ideaFinal" id="">
	      	<li >
	      		<label for=""><i class="xing">*</i>模板名称：</label>
	      		<input id="templateName" type="text" tname="templateName" class="templateClass" maxlength="30" placeholder="请输入模板名称">
	      		<span class="lineError"></span>
	      	</li>
			<li>
				  <label for="">IOS下载地址：</label>
				<input type="text" style="display:none">
				<span class="lineError"></span>
			</li>
	      	<li>
	      		<label for=""><i class="xing">*</i>车方版下载地址：</label>
	      		<input id="iosCarAddress" maxlength="200" tname="carOwnerIosUrl" class="templateClass" type="text" placeholder="请输入IOS车方版下载地址">
	      		<span class="lineError"></span>
	      	</li>
	      	<li>
	      		<label for=""><i class="xing">*</i>货站版下载地址：</label>
	      		<input id="iosGoodAddress" maxlength="200" tname="cargoStationIosUrl" class="templateClass" type="text" placeholder="请输入IOS货站版下载地址">
	      		<span class="lineError"></span>
	      	</li>
			<li>
				<label for="">android下载地址：</label>
				<input type="text" style="display:none">
				<span class="lineError"></span>
			</li>
		    <li>
			  <label for=""><i class="xing">*</i>车方版下载地址：</label>
			  <input id="androidCarAddress" maxlength="200" tname="carOwnerAndroidUrl" class="templateClass"  type="text" placeholder="请输入android车方版下载地址">
			  <span class="lineError"></span>
			</li>
			<li>
			  <label for=""><i class="xing">*</i>货站版下载地址：</label>
			  <input id="androidGoodAddress" maxlength="200" tname="cargoStationAndroidUrl" class="templateClass" type="text" placeholder="请输入android货站版下载地址">
			  <span class="lineError"></span>
			</li>
			<li>
				<label for=""><i class="xing">*</i>图片内容：</label>
				<div class="detailBox imgBox">
					<ul class="detailListBox uploaderFiles cf">
						<li class="updataBtnBox fl">
							<i class=""></i>
							<p>上传图片</p>
							<input id="uploaderInput" class="uploaderInput" onchange="comO.fileChange(this,10)" onclick="comO.clearFile(event)" type="file" title=" " accept="image/jpeg,image/jpg,image/png">
						</li>
					</ul>
				</div>
			</li>
	      </ul>
	      <input id="createEmp" type="hidden">
	      <input id="updateTime" type="hidden">
	      <input id="upgradestatus" type="hidden">
		  <input id="upgradectime" type="hidden">
		  <div class="divButton" id="divButton"></div>
          <!-- <div>
			<ul class="btnBox cf">
				<li>
					<button onclick="tytTool.closeWin()">取消</button>
				</li>
				<li id="addSub" class="subutton">
					<button onclick="submitUpgrade($(this))">保存</button>
				</li>
			</ul>
		  </div> -->
	    </div>
		</div>

	</div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/jquery-ui-1.10.4.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/upgradeTemplate/recordCommon.js"></script>
<script src="../../js/upgradeTemplate/updateMessage.js"></script>
</html>