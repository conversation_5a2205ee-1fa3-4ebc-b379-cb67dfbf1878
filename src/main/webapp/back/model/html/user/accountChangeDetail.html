<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-15 16:43:40
 * @LastEditTime: 2023-09-12 11:59:50
 * @LastEditors: heyuyan
 * @FilePath: /work/Users/<USER>/d/tyt_manage_new/workspace/tyt_manage_new/src/main/webapp/back/model/html/user/accountChangeDetail.html
 * @Description: 
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>用户账号变更详情</title>
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/detailNav.css">
</head>
<style>
    .certificate{
        width: 300px;
        height: auto;
        vertical-align: top;
        padding-top: 10px;
        box-sizing: border-box;
        cursor: pointer;
    }
    #imageDataWarp > img {
        margin: 0 0 0 20px;
    }
    #imageDataWarp > img:first-child {
        margin: 0;
    }
</style>
<body>
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>用户资料管理</span><span>&nbsp;&gt;&nbsp;</span><span>用户账号变更管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">用户账号变更详情</span>
    </h1>
    <div class="warpBox">
        <div class="detailBox"> 
            <ul class="detailListBox cf">
                <li class="twoLine flexWidth fl">
                    <label for="" class="">用户ID ：</label>
                    <span id="" class="" tname="userId"></span>
                </li>
                <li class="twoLine flexWidth fl">
                    <label for="" class="">
                        原手机号：</label>
                    <span id="" class="" tname="cellPhone" ></span>
                </li>
                <li class="twoLine flexWidth fl">
                    <label for="" class="">
                        新手机号：</label>
                    <span id="" class="" tname="newPhone"></span>
                </li>
                <li class="twoLine flexWidth fl">
                    <label for="" class="">
                        变更原因：</label>
                    <span id="" class="" tname="reason"></span>
                </li>
                <li class="twoLine flexWidth fl">
                    <label for="" class="">变更时间：</label>
                    <span id="" class="" tname="ctime" time-format="yyyy-MM-dd hh:mm"></span>
                </li>
                <li class="twoLine flexWidth fl">
                    <label for="" class="">
                        操作人：</label>
                    <span id="" class="" tname="optName"></span>
                </li>
                <li class="oneLine flexWidth fl" id="imageDataWarp">
                    <label for="" class="">
                        上传资料：</label>
                    <span id="noImg" style="display: none;"> 未上传 </span>
                </li>
            </ul>
        </div>
    </div>

</div>


</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/accountChangeDetail.js"></script>
</html>