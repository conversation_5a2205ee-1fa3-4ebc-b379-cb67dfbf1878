<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>用户踢出管理</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="stylesheet" href="../../css/bootstrap.css">
    <link rel="stylesheet" href="../../css/tableBase.css">
    <link rel="stylesheet" href="../../css/tableModel.css">
    <link rel="stylesheet" href="../../css/tableAlert.css">
    <!-- <link rel="stylesheet" href="../../css/busyEnter.css"> -->
    <style type="text/css">

    </style>
</head>

<body>
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span>
            <span>用户资料管理</span>
            <span>&nbsp;&gt;&nbsp;</span>
            <span class="currentPage">用户踢出</span>
        </h1>
        <div>
            <div class="addWhiteUser cf">
                <div style="text-align: left;">
                    <ul>
                        <li>
                            <span style="width: 500px;">用户IDs（以英文逗号分隔）示例: 111,222,333</span>
                        </li>
                        <li>
                            <textarea id="userIds" style="width: 1000px;height: 600px;"></textarea>
                        </li>
                        <li>
                            <span style="width: 500px;">客户端类型：</span>
                        </li>
                        <li >
                            <input type="radio" name="type" checked="checked" value="ALL"> 全部
                            <input type="radio" name="type" value="OBJECT"> 老版本
                            <input type="radio" name="type" value="CAR"> 车版
                            <input type="radio" name="type" value="GOODS"> 货版
                        </li>
                    </ul>
                </div>

                <div class="makeSureBtn" style="height:62px;margin: 0 auto;text-align: center;">
                    <input class="button" type="button" onclick="kickOutByUserIds()" style="margin: 0 20px 0 0;" value="根据userId踢出用户">
                </div>
            </div>

        </div>


    </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="../../js/tyt_common.js"></script>
<script>
    function kickOutByUserIds() {
        var ids = $("#userIds").val();
        let type = $('input:radio:checked').val();
        if(ids == null || ids.trim() ==''){
            alert("请输入userIds");
            return false;
        }
        if(type == null || type.trim() ==''){
            alert("请选择客户端类型");
            return false;
        }

        var userIds = ids.toString().replaceAll('\n','').split(',');
        var postData = {
            userIds: userIds,
            type: type
        }
        $.ajax({
            type: "POST",
            data: JSON.stringify(postData),
            contentType : 'application/json',
            dataType : 'json',
            url: tytTool.getRootPath_web() + "/manage/user/kickOutByUserIds",
            success: function (data) {
                alert(data.msg);
            }
        });
    }


</script>

</html>