<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>完善信息</title>
	<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="description" content="">
	<meta name="author" content="">
	<link href="../../css/tableBase.css" type="text/css" rel="stylesheet">
	<link href="../../css/tableAlert.css" type="text/css" rel="stylesheet">
	<link href="../../css/detailPages.css" type="text/css" rel="stylesheet">
	<link href="../../css/carpeopolepage.css" type="text/css" rel="stylesheet">
	<link rel="stylesheet" href="../../js/navControl/css/navControl.css">
</head>

<body>
<input type="hidden" id="oldRenewalYears">
<input type="hidden" id="oldHomePhone">
<input type="hidden" name="id" value="147764">
<input type="hidden" id="deliverTypeOneId" value="13" />
<input type="hidden" id="deliverTypeTwoId" value="29" />
<div class="contentBox" id="carTramBox">
	<div class="Mask" >
	</div>
	<!----新建路线--->
	<div style="position: fixed;;z-index:99;position: fixed;z-index: 99;top: 50%;left: 50%;display: none;margin: -128px 0 0 -228px;" id="showboxDiv" class="showboxDiv" >
		<div class="btnbigbox" >
			<div style="position: relative;;">
					<div id="runline" style="display:none">
						<ul>
							<li ><span></span></li>
						</ul>
					</div>
				<div id="runlines" style="display:none">
						<ul>
							<li ><span></span></li>
						</ul>
				</div>
				<h1 id="runrun">常跑路线</h1>
				<div id="hidetab" onclick="co.commonClick('9')" style=" font-size: 30px;position: absolute;top:-15px;left:390px;">&times;</div>
			</div>
			<div class="smallbox">
				<ul>
					<li>
						<label for="">&nbsp;&nbsp;&nbsp;&nbsp;出发地:</label>
						<div class="fl areaDiv twoDiv" onclick="co.commonClick(3)">
							<input id="position1" type="text"   placeholder=" 选择、省、市" onclick="tyt_open_area('1','position1','boxDiv1','sheng','shi','xian','tyt_area_iframe1','3');"
								   readonly class="positionInput addressIcon" value="" tname="startFull">
							<div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
								 id="boxDiv1" >
								<iframe name="tyt_area_iframe1" id="tyt_area_iframe1" src="../../js/tyt_area/area.html"
										frameborder="0" width="240" scrolling="no" height="550" leftmargin="0" topmargin="0" allowtransparency="true"></iframe>
							</div>
							<p class="noneInput mtl20"><input id="sheng" type="text"  class="positionInput" tname="startProv" value=""></p>
							<p class="noneInput mtl20"><input id="shi" type="text" class="positionInput" tname="startCity" value=""></p>
							<p class="noneInput mtl20"><input id="xian" type="text" class="positionInput" tname="startArea" value=""></p>
							<div class="cb"></div>
						</div>
					</li>
					<li>
						<label for="">&nbsp;&nbsp;&nbsp;&nbsp;目的地:</label>
						<div class="fl areaDiv" onclick="co.commonClick(3)">
							<input id="position3"   type="text" placeholder=" 选择全国、省、市" onclick="tyt_open_area('1','position3','boxDiv3','sheng3','shi3','xian3','tyt_area_iframe3','3');"
								   readonly class="positionInput addressIcon" value="" tname="destFull"><input type="button"  id="totalbtn"  value="全国">
							<div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
								 id="boxDiv3">
								<iframe name="tyt_area_iframe3" id="tyt_area_iframe3" src="../../js/tyt_area/area.html" frameborder="0"
										width="240" scrolling="no" height="550" leftmargin="0" topmargin="0" class="boxDiv" allowtransparency="true"></iframe>
							</div>
							<p class="noneInput mtl20"><input id="sheng3" type="text" class="positionInput" tname="destProv" value=""></p>
							<p class="noneInput mtl20"><input id="shi3" type="text" class="positionInput"  tname="destCity" value=""></p>
							<p class="noneInput mtl20"><input id="xian3" type="text" class="positionInput" tname="destArea" value=""></p>
							<div class="cb"></div>
						</div>

					</li>
					<li>
						<label for="">车辆数量:</label>
						<input type="text" value="" id="numcar" class="positionInput" tname="carNum">
					</li>
				</ul>
			</div>
			<div class="bottombox">
				<button class="cansole" name="isCar" onclick="co.commonClick(2)">取消</button>

				<button  class="sumit" name="isCar" onclick="co.addRegularrun()">确定</button>

			</div>
		</div>

	</div>
	<!----新建联系人--->
	<div>
		<div style="position: fixed;;z-index:99;top:30%;left:29%;" id="showboxDiv2" class="showboxDiv2" >
			<div class="btnbigboxpeople" >
				<div style="position: relative;;">
					<h1>联系人</h1>
					<div id="hidetabtwo" onclick="co.commonClick(5)"  style=" font-size: 30px;position: absolute;top:-15px;left:525px;">&times;</div>
				</div>
				<div class="smallbox2" id="smallboxtname">
					<!-- <ul>
                        <li>
                            <label for="">电话:</label>
                            <input type="text" class="phoneinput">
                        </li>
                        <li>
                            <label for="">姓名:</label>
                            <input type="text" class="phoneinput">
                        </li>
                        <li>
                            <label for="">职务:</label>
                            <input type="text" class="phoneinput">
                        </li>
                        <li id="buttonli">
                                <button class="buttontab">最新</button>
                        </li>
                        <li id="buttonli">
                                <button class="buttontab">停用</button>
                        </li>
                        <li id="buttonli">
                                <button class="buttontab">删除</button>
                        </li>
                    </ul> -->

				</div>
				<div class="bottomboxs">
					<button  class="cansoles" name="isCar" onclick="co.addphonepeopel()">新增联系人</button>
					<button  class="cansoles" name="isCar" onclick="co.commonClick(1)">取消</button>

					<button   class="sumits" name="isCar"  onclick="co.commonClick(15)">确定</button>

				</div>
			</div>
		</div>
	</div>
	<!----保存--->
	<div>
		<div style="position: fixed;;z-index:99;top:30%;left:37%;" id="showboxDiv3" class="showboxDiv3" >
			<div class="btnbigbox">
				<div style="position: relative;;">
					<h1>提示</h1>
					<!-- <div id="hidetab"  style=" font-size: 30px;position: absolute;top:-15px;left:390px;">&times;</div> -->
				</div>
				<div class="smallbox3 " id="jiaoyanone">
					<span id="baocunsuncess">信息填写不规范，不可保存</span>
				</div>
				<button class="summitbtn" onclick="co.commonClick(4)">知道了</button>
			</div>
		</div>
	</div>
	<!----新建/设备种类-->
	<div>
		<div style="position: fixed;;z-index:99;top:30%;left:37%;" id="showboxDiv4" class="showboxDiv4" >
			<div class="btnbigbox">
				<div style="position: relative;;">
					<h1>设备种类</h1>
					<div id="hidetab"  onclick="co.commonClick(23)" style=" font-size: 30px;position: absolute;top:-15px;left:390px;">&times;</div>
				</div>
				<div class="smallbox4">
					<textarea cols="54.5" rows="8" id="textarea" placeholder="0-1000" maxlength="1000" placeholder="在这里输入内容..."></textarea>
					<span class="xianzhizishu">0-1000字符</span>
					<!-- <input type="text" class="inputcontentcontent"> -->
				</div>
				<div class="bottomboxss">
					<button  class="cansoles" name="isCar" onclick="co.commonClick(1)">取消</button>
					<button   class="sumits" name="isCar"  onclick="co.commonClick(22,$())">确定</button>

				</div>
			</div>
		</div>
	</div>
   <!---备备注框--->
   <div>
	<div style="position: fixed;;z-index:99;top:30%;left:37%;" id="showboxDiv5" class="showboxDiv5">
		<div class="btnbigbox">
			<div style="position: relative;;">
				<h1>板车长度备注</h1>
				<div id="hidetab"  onclick="co.commonClick(23)" style=" font-size: 30px;position: absolute;top:-15px;left:390px;">&times;</div>
			</div>
			<div class="smallbox4">
				<textarea cols="54.5" rows="8" id="textareas" tname="bancheLengthRemark" placeholder="0-80" maxlength="80" placeholder="在这里输入内容..."></textarea>
				<span class="xianzhizishu">0-80字符</span>
				<!-- <input type="text" class="inputcontentcontent"> -->
			</div>
			<div class="bottomboxss">
				<button  class="cansoles" name="isCar" onclick="co.commonClick(1)">取消</button>
				<button   class="sumits" name="isCar"  onclick="co.commonClick(30,$(this))">确定</button>
			</div>
		</div>
	 </div>
  </div>


	<h1 class="titUrl">
		<span>当前所在位置：</span><span>车辆方服务</span><span>&nbsp;&gt;&nbsp;</span><span class="titletext">车队线索管理</span><span>&nbsp;&gt;&nbsp;</span><span
			class="currentPage">完善信息</span>
	</h1>
	<div class="detailBox" id="onesubmit">
		<input type="hidden" tname="id"  ttname="id" id="id" value="">
		<input type="hidden" tname="clewId" ttname="clewId" id="clewId" value="">
		<h2 class="detailTit">联系信息</h2>
		<ul class="detailUl cf">
			<li id="firsttitle">
				<!-- <i style="color:red;margin-right:5px">*</i>
				<label for="">是否车队：</label>
				<select ttname="isCarteam"  tname="isCarteam" id="isCarteamteam" >
					<option class="isCarteam" value="2">否</option>
					<option class="isCarteam" value="1">是</option>
				</select> -->
			</li>
			<li id="carname">
				<label for="">车队名称：</label>
				<span> 
						<input type="text" name="" maxlength="40"  onblur="tytTool.onInput($(this),40,8)" ttname="carteamName" tname="carteamName" value="">
					</span>
			</li>
			<li id="cartype">
				<label for=""><i style="color:red;margin-left: -11px;">*</i>
					车队类型：</label>
				<select ttname="carteamType"  tname="carteamType" id="carteamTypes">
					<option class="carteamType" value="">请选择</option>
					<option class="carteamType" value="1">个人车队</option>
					<option class="carteamType" value="2">企业车队</option>
				</select>
			</li>
			<li class="">
				<label class="fl">归属地：</label>
				<div class="fl areaDiv twoDiv" onclick="co.commonClick(3)">
					<input id="position4"    tname="location" type="text" title="" class="input120" placeholder="请选择地区" onclick="tyt_open_area('1','position4','boxDiv4','sheng4','shi4','xian4','tyt_area_iframe4','3');"
						   readonly class="positionInput addressIcon" value="选择所在省、市">
					<div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px" id="boxDiv4" class="boxDiv4">
						<iframe name="tyt_area_iframe4" id="tyt_area_iframe4" src="../../js/tyt_area/area.html" frameborder="0" width="240"
								scrolling="no" height="550" leftmargin="0" topmargin="0" allowtransparency="true"></iframe>
					</div>
					<p class="noneInput mtl20"><input id="sheng4" name="province" type="text"  ttname="locateProv" tname="locateProv" value="" class="positionInput"></p>
					<p class="noneInput mtl20"><input id="shi4" name="city" type="text" ttname="locateCity" tname="locateCity"  value="" class="positionInput"></p>
					<p class="noneInput mtl20"><input id="xian4" name="county" type="text" ttname="locateArea" tname="locateArea" value="" class="positionInput"></p>
					<div class="cb"></div>
				</div>
				<span  style=" white-space: nowrap;overflow:visible;text-overflow:string;color: red" tname="locationImport" style="margin-left:100px ;color:red"></span>
			</li>
		</ul>
		<ul class="detailUl cf"  id="onepeopelsumit" style="padding: 0 16px 0;">

			<li id="detailulone">
				<i class="numberIcon">①</i>
				<label for="">
					联系电话：</label>
				<span>
						<input type="text" name="" id="clewPhone" readonly value="" ttname="clewPhone" tname="clewPhone">

					</span>
			</li>
			<li>
				<label for=""><i style="color:red;margin-left: -11px;">*</i>
					姓名：</label>
				<span>
						<input type="text" name=""  id="peopelphone" readonly  ttname="clewName"  tname="clewName" value="">
					</span>
			</li>
			<li>
				<label for="">审核身份：</label>
				 <span id="identity_types"  >
						<input type="text" ttname="indentity"  tname="indentity"  id="indentity" readonly style="border:none;" name=""  value="">
				</span>
				
			</li>
			<li>
				<label for="">职务：</label>
				<span>
						<input type="text" name=""   ttname="position" readonly tname="mainPosition" id="positionidback" maxlength="15"  onblur="tytTool.onInput($(this),15,5)"  value="">
						
					</span>
			</li>
			<li>
				
				<label for=""><i style="color:red;margin-right:5px">*</i>合作意向：</label>
				<select name="source" id="" ttname="coorIntention" tname="coorIntention">
					<option class="coorIntention" value="无意愿">无意愿</option>
					<option class="coorIntention" value="意愿强烈">意愿强烈</option>
					<option class="coorIntention" value="一般意愿">一般意愿</option>
				</select>
			</li>
			<li>
				<label for="">备注：</label>
				 <span>
						<input type="text" name="" style="border:1px solid #ccc; "   ttname="remark" tname="remark" placeholder="可输入80字" maxlength="80">
				</span>
			</li>
			<li>
				<button class="createBtn" onclick="co.commonClick(8)">修改/新建联系人</button>
			</li>
		</ul>
		<ul class="detailUl cf" id="phonepeopel" style="padding: 0 16px 0;">
			<!-- <input type="hidden" tname="carteamId"   value="" id="carteamId">
			<li>
				<i class="numberIcon">②</i>
				<label for="">联系电话：</label>
				<span>
						<input type="text" name=""  tname="phone"  value="">
					</span>
			</li>
			<li>
				<label for="">姓名：</label>
				<span>
						<input type="text" name=""    tname="name" value="">
					</span>
			</li>
			<li>
				<label for="">职务：</label>
				<span>
						<input type="text" name=""   tname="position" value="">
					</span>
			</li>
			<li>
				<button class="createBtn" onclick="co.commonClick(8)">新建联系人</button>
			</li> -->
		</ul>
	</div>

	<div class="detailBox">
		<h2 class="detailTit">车辆运力</h2>
		<ul class="detailUl cf" id="twosubmit" >
			<li>
				<label for="">车辆数量：</label>
				<span>
						<input type="text" name="" ttname="carAmount"  onblur="tytTool.onInput($(this))" tname="carAmount" value="">

					</span>
			</li>
			<li id="detailulone">
				<label for="">认证车辆数量：</label>
				<span>
						<input type="text" tname="authCarAmount"  onblur="tytTool.onInput($(this))" readonly name=""    value="">
				</span>
			</li>
			<li>
				<label for="">板车类型：</label>
				<span >
						<input type="text" id="bancheType" data-arr="" title="" placeholder="新建/板车类型" value="" onclick="co.commonClick(11,$(this))"  readonly>
					</span>
			</li>
			<li>
				<label for="">板车长度：</label>
				<span>
						<input type="text" name=""  id="bancheLength" data-arr="" title="" placeholder="新建/板车长度"   onclick="co.commonClick(21,$(this))" value="" readonly>
					</span>
			</li>
			<li>
				<label for="">板车备注：</label>
				 <span>
						<input type="text" name="" style="border:1px solid #ccc; " title="" data-math="${Math.floor(Math.random()*100000000)}" data-arr=""  onclick="co.commonClick(30,$(this))" tname="bancheLengthRemark"  ttname="bancheLengthRemark"  placeholder="可输入80字"  maxlength="80">
				</span>
			</li>
			<li>
				<label for="">是否有爬梯：</label>
				<select name="source"  tname="isLadder" ttname="isLadder" id="">
					<option class="isLadder" value="">请选择</option>
					<option class="isLadder" value="1">是</option>
					<option class="isLadder" value="2">否</option>
				</select>
			</li>
		</ul>
		<div class="taboutBox" id="taboutbigBox">
			<h2>常跑路线：</h2>
			<!-- <ul class="detailUl detailUlLine cf">  -->
				<!-- <li>
					<label for="">出发地：</label>
					<span>
							<input type="text" name="" value="">
						</span>
				</li>
				<li>
					<label for="">目的地：</label>
					<span>
							<input type="text" name="" value="">
						</span>
				</li>
				<li>
					<label for="">车量数量：</label>
					<span>
							<input type="text" name="" value="">
						</span>
				</li>
				<li style="width:70px">
					<button type="button" class="createBtn" name="" value="">编辑</button>
				</li>
				<li style="width:70px">
					<button type="button" class="createBtn" style="background:#fff" name="" value=""></button>
				</li> -->
			<!-- </ul>  -->
			<div class="createBtnnewaddul">
				<li style="width:90px">
					<button class="createBtnnewadd" onclick="co.commonClick('10')">新建路线</button>
				</li>
			</div>
		</div>
	</div>
	<div class="detailBox" id="detailBoxs">
			<div class="addbtn">
					<button  class="cansoles" name="isCar"   onclick="co.addshowcengyun()">新增企业列表</button>
			</div>
		<h2 class="detailTit">车队业务</h2>
		<ul class="detailUl dataJiao cf " id="threesumit">
			<li>
				<label for="">承揽企业货源：</label>
				<select  id="loadEnters" tname="loadEnter" ttname="loadEnter">
					<option class="loadEnter" value="">请选择</option>
					<option class="loadEnter" value="1">承揽</option>
					<option class="loadEnter" value="2">不承揽</option>

				</select>
			</li>
			<li id="enterNums">
				<label for=""> 承揽企业数量：</label>
				<input type="text" name="" style="border:none" readonly tname="enterNum"  id="addshowcengyun" ttname="enterNum" value="">
			</li>
		</ul>
		<div class="companyBox">
			<!-- <div id="boxbox">
				<h2 class="detailTit">企业1</h2>
				<ul class="detailUl cf">
					<li>
						<label for="">承运企业名称：</label>
						<input type="text" name="" value="">
					</li>
					<li>
						<label for=""> 营业额：</label>
						<input type="text" name="" value="">
					</li>
					<li>
						<label for=""> 设备数：</label>
						<input type="text" name="" value="">
					</li>
					<li>
						<label for=""> 运输量：</label>
						<input type="text" name="" value="">
					</li>
					<li>
						<label for=""> 设备种类：</label>
						<input type="button" name="" data-arr="" title="" onclick="clue.tytNav($(this))" value="新建/设备种类" readonly>
					</li>
				</ul>
				<div class="taboutBoxtwo">
					<h2>承包路线：</h2> -->
					<!-- <ul class="detailUl detailUlLine cf">
						<li>
							<label for="">出发地：</label>
							<span>
					   <input type="text" name="" value="">
				   </span>
						</li>
						<li>
							<label for="">目的地：</label>
							<span>
					   <input type="text" name="" value="">
				   </span>
						</li>
						<li>
							<label for="">车量数量：</label>
							<span>
					   <input type="text" name="" value="">
				   </span>
						</li>
						<li style="width:70px">
							<button type="button" class="createBtn" name="" value="">编辑</button>
						</li>
						<li style="width:70px">
							<button type="button" class="createBtn" name="" value="">删除</button>
						</li>
					</ul> -->
					<div class="createBtnnewaddultwo">
						<li style="width:90px">
							<button class="createBtnnewadd" onclick="co.commonClick('10')">新建路线</button>
						</li>
					</div>
				</div>
	</div>

	
	<div class="btnBox" style="width:auto;">
			<span class="" id="errorSpanId" style="display:none;">*号必填项不能为空</span>
			<div class="innerBtn">
				<button type="button" class="btnTrun" onclick="co.commonClick(7)">保存</button><button class="btnClose" onclick="javascript:window.opener=null;window.close();">关闭</button>
			</div>
		</div>
		</div>

</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<!-- <script src="../../js/jquery-1.11.1.min.js"></script> -->
<script src="../../js/tyt_area/js/tyt_area.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/navControl/script/jquery.checkbox.js"></script>
<script src="../../js/carpeopolperfect.js"></script>
<script>
    var arrBox = [{
        "id": "1",
        "name": "东风柳汽"
    }, {
        "id": "3",
        "name": "广汽奔驰"
    }, {
        "id": "2",
        "name": "陕汽德龙"
    }, {
        "id": "4",
        "name": "三一重工"
    }, {
        "id": "5",
        "name": "陕汽榆林"
    }, {
        "id": "6",
        "name": "陕汽通力"
    }, {
        "id": "7",
        "name": "上海格拉曼"
    }, {
        "id": "8",
        "name": "上汽红岩"
    }]
    // var per = {
    // 	tytNav: function (_this) {
    // 		var common = {
    // 			"arrBox": arrBox,
    // 			"choiceArr": _this.attr('data-arr'),
    // 			"resetBtn": false,
    // 			"maxLen": [100000, '您选择的类型已经超出范围', false],
    // 			"checkboxTit": "你好,旭东",
    // 			"resetBtn": true,
    // 			"cssJoin": false
    // 		};
    // 		_this.tytNav(common);
    // 	}
	// }
	
    
  

</script>

</html>