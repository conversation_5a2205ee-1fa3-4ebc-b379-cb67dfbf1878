<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>车队维护管理</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <link rel="stylesheet" href="../../css/bootstrap.css">
  <link rel="stylesheet" href="../../css/tableBase.css">
  <link rel="stylesheet" href="../../css/tableModel.css">
  <link rel="stylesheet" href="../../css/tableAlert.css">
  <link rel="stylesheet" href="../../css/carCommon.css">
</head>
<body>
  <div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>车辆方服务</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">车队维护管理</span>
    </h1>
    <div class="main-content">

      <!--搜索条件-->
      <div class="searchBox" id="searchCondition">
        <ul class="searchUl searchUlNone cf">
	        <li>
	          <label>出发地：</label>
				    <div class="fl areaDiv twoDiv">
				    	<input id="position" type="text" class="input120" placeholder="" onclick="tyt_open_area('1','position','boxDiv','sheng','shi','xian','tyt_area_iframe','3');"
				            readonly class="positionInput addressIcon" value="" tname="startFull">
				        <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
				            id="boxDiv" class="boxDiv">
				            <iframe name="tyt_area_iframe" id="tyt_area_iframe" src="../../js/tyt_area/area.html"
				                frameborder="0" width="240" scrolling="no" height="550" leftmargin="0" topmargin="0" allowtransparency="true"></iframe>
				        </div>
				        <p class="noneInput mtl20"><input id="sheng" type="text"  class="positionInput" tname="startProv" value=""></p>
				        <p class="noneInput mtl20"><input id="shi" type="text" class="positionInput" tname="startCity" value=""></p>
				        <p class="noneInput mtl20"><input id="xian" type="text" class="positionInput" tname="startArea" value=""></p>
				        <div class="cb"></div>
				    </div>
	        </li>
	        <li>
	          <label>目的地：</label>
	          <div class="fl areaDiv">
	            <input id="position2" class="input120" type="text" placeholder="" onclick="tyt_open_area('1','position2','boxDiv2','sheng2','shi2','xian2','tyt_area_iframe2','3')" readonly class="positionInput addressIcon" value="" style="width:56px;vertical-align: middle;"><span class="allCountry" onclick="mo.commonClick('allCountry',$(this))">全国</span>
                      <input id="destFullBox" type="hidden" tname="destFull" value="" />
	            <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
	                id="boxDiv2">
	                <iframe name="tyt_area_iframe2" id="tyt_area_iframe2" src="../../js/tyt_area/area.html" frameborder="0"
	                    width="240" scrolling="no" height="550" leftmargin="0" topmargin="0" class="boxDiv" allowtransparency="true"></iframe>
	            </div>
	            <p class="noneInput mtl20"><input id="sheng2" type="text" class="positionInput" tname="destProv" value=""></p>
	            <p class="noneInput mtl20"><input id="shi2" type="text" class="positionInput"  tname="destCity" value=""></p>
	            <p class="noneInput mtl20"><input id="xian2" type="text" class="positionInput" tname="destArea" value=""></p>
	            <div class="cb"></div>
	          </div>
	        </li>
          <li>
            <label>地区匹配类型：</label>
            <select id="matchTypeBox" tname="matchType">
              <option value="0" class="firstOption">请选择</option>
              <option value="1">精准匹配</option>
              <option value="2">仅出发地精准匹配</option>
              <option value="3">仅目的地精准匹配</option>
              <option value="4">模糊匹配</option>
  		      </select>
          </li>
          <!-- <li class="radioPlantBox">
          	<span onclick="mo.addRemoveChecked($(this),event)"><input type="radio" name="ra" tname="matchType" id="ra1" value="1"><label for="ra1">精准匹配</label></span>
          	<span onclick="mo.addRemoveChecked($(this),event)"><input type="radio" name="ra" tname="matchType" id="ra2" value="2"><label for="ra2">仅出发地精准匹配</label></span>
          	<span onclick="mo.addRemoveChecked($(this),event)"><input type="radio" name="ra" tname="matchType" id="ra3" value="3"><label for="ra3">仅目的地精准匹配</label></span>
          	<span onclick="mo.addRemoveChecked($(this),event)"><input type="radio" name="ra" tname="matchType" id="ra4" value="4"><label for="ra4">模糊匹配</label></span>
          </li> -->
          <li>
            <label>车队联系电话：</label>
            <input id="" type="text" class="input90" tname="phone" maxlength="11" value="" onblur="tytTool.onInput($(this),11,7)" />
          </li>
	        <li>
	          <label>归属地：</label>
	          <div class="fl areaDiv">
	            <input id="position3" class="input120" type="text" placeholder="" onclick="tyt_open_area('1','position3','boxDiv3','sheng3','shi3','xian3','tyt_area_iframe3','3');"
	                    readonly class="positionInput addressIcon" value="">
	            <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px"
	                id="boxDiv3">
	                <iframe name="tyt_area_iframe3" id="tyt_area_iframe3" src="../../js/tyt_area/area.html" frameborder="0"
	                    width="240" scrolling="no" height="550" leftmargin="0" topmargin="0" class="boxDiv" allowtransparency="true"></iframe>
	            </div>
	            <p class="noneInput mtl20"><input id="sheng3" type="text" class="positionInput" tname="locateProv" value=""></p>
	            <p class="noneInput mtl20"><input id="shi3" type="text" class="positionInput"  tname="locateCity" value=""></p>
	            <p class="noneInput mtl20"><input id="xian3" type="text" class="positionInput" tname="locateArea" value=""></p>
	            <div class="cb"></div>
	          </div>
	        </li>
          <li>
            <label>板车类型：</label>
            <select id="bancheType" tname="bancheType">
              <option class="firstOption" value="">请选择</option>
  		      </select>
          </li>
          <li class="carLenMain">
            <label>板车长度：</label>
            <input id='bancheLength' type="text" tname="bancheLength" onblur="tytTool.onInput($(this),6,2)" onafterpaste="tytTool.onAfterPaste($(this))"/><select id="bancheLengthType" tname="bancheLengthType">
              <option value="1">米以上</option>
              <option value="2">米</option>
  		      </select>
          </li>
          <li>
            <label>是否车队：</label>
            <select id="" tname="isCarteam">
              <option value="">请选择</option>
              <option value="1">是</option>
              <option value="2">不是</option>
  		      </select>
          </li>
          <li>
            <label>车队类型：</label>
            <select id="" tname="carteamType">
              <option value="">请选择</option>
              <option value="1">个人车队</option>
              <option value="2">企业车队</option>
  		      </select>
          </li>
          <li>
            <label>状态：</label>
            <select id="" tname="status">
              <option value="">请选择</option>
              <option value="1">启用</option>
              <option value="2">停用</option>
  		      </select>
          </li>
        </ul>
      </div>
      <!--页面信息-->
      <div class="pageMessage">
        <div class="searchBtn">
          <ul class="btnBox cf" id="btnBox">
            <li id="checkListBox">
              <input type="button" onclick="mo.listMsg(1)" value="查询">
            </li>
          </ul>
        </div>
        <div class="clearfix"></div>
        <div class="messageRight fr">查询到<em id="rowCount"></em>条信息，当前是第<span id="currentPage"></span>页，共<span id="totalPages"></span>页
      </div>
      </div>

      <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
      <div class="dataTable">
        <table border="0" class="tableBox" id="mainEnter">
          <tr class="tableTh">
            <th>ID</th>
            <th>车队名称</th>
            <th>归属地</th>
            <th width="160px">联系方式</th>
            <th>联系次数</th>
            <th>成交次数</th>
            <th>车辆数量</th>
            <th>认证车辆数量</th>
            <th>车队类型</th>
            <th>板车长度</th>
            <th width="180px">常跑路线</th>
            <th>状态</th>
            <th width="140px">操作</th>
          </tr>
        </table>
      </div>
    </div>

    <!--JQ分页-->
    <div id="pageNumberBigBox" class="cf">
      <ul class="pageNumberBox pagination fl" id="pageNumberBox"></ul>
    </div>
  </div>
  <!-- 导入引导框 -->
  <div class="outBgColor" style="display: none;"></div>
  <div class="outLook excelBox" style="display: none;">
		<div class="innerLook">
			<h2>请选择文件</h2>
	    <p class="alertContent">请点击“浏览”选择需要导入的模板文件</p>
			<div class="ideabtnBox cf" style="width: 200px;margin-top:0; ">
				<div class="fileBtnBox fl">
						<input class="fileBtn" id="fileField" type="file" name="carteamExcel" accept=".xls" onchange="mo.fileInfo(this,$(this))">
					<button class="rule_true">浏览</button>
				</div>
				<button id="uploadFile" onclick="mo.uploadFile()" class="close_block fr">上传</button>
			</div>
		</div>
		<i class="closelookBtn" onclick="mo.excelHide()">×</i>
  </div>
</body>
<script src="../../js/jquery-1.7.2.min.js"></script>
<!-- <script src="../../js/My97DatePicker/WdatePicker.js" ></script> -->
<script src="../../js/tyt_area/js/tyt_area.js"></script>
<script src="../../js/jqpaginator.min.js"></script>
<script src="../../js/colResizable-1.6.min.js"></script>
<script src="../../js/tyt_common.js"></script>
<script src="../../js/carMain.js"></script>
<script src="../../js/hidePhoneCommon.js"></script>
<script>
  function keyCode(event,_this){
    var reg = /^(0|[1-9][0-9])(\.[0-9]{1,3})/;
    console.log(Number(_this.val()))
    console.log(reg.test(Number(_this.val())))
    return( reg.test(String.fromCharCode(event.keyCode)))
  }
</script>
</html>
