@charset 'utf-8';

/* CSS Reset Document */
body, html {
    background: #fff;
}
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, button, textarea, p, blockquote, th, td, img, select, option, a, span, em, strong, i {
    margin: 0;
    padding: 0;
}
body, form, fieldset, input, textarea, select, option {
    font: normal 14px Arial, 'Microsoft Yahei', Verdana, Geneva, sans-serif;
    color: #333;
    background: #fff;
}
table {
    border-spacing: 0;
    border-collapse: collapse;
}
fieldset, img {
    border: 0;
}
address, caption, cite, code, dfn, em, strong, th, var {
    font-weight: normal;
    font-style: normal;
}
ol, ul, li {
    list-style: none;
}
a {
    text-decoration: none;
}
button, input {
    /*outline: none;*/
    border: none 0;
}
capation, th {
    text-align: left;
}
h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
    font-weight: normal;
}
textarea {
    resize: none;
}
q:before, q:after {
    content: ' ';
}
abbr, acronym {
    border: 0;
}
input {
    border: none;
}
a, a:hover {
    text-decoration: none;
}
ol, ul, li {
    list-style: none;
}
.tc {
    text-align: center;
}
i {
    font-style: normal;
}
.cf:after {
    display: block;
    clear: both;
    content: '';
}
.cf {
    zoom: 1;
}
.fl {
    float: left;
}
.fr {
    float: right;
}
body, html {
    min-width: 1100px;
}

/*模态框共用*/
.outMoTaiBgColor {
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.7;
    filter: alpha(opacity=70);
    z-index: 10000000000;
}
.innerTitBox {
    width: 520px;
    position: fixed;
    left: 50%;
    top: 50%;
    border: 4px solid #dae7ff;
    background: #fff;
    z-index: 12;
    z-index: 10000000000;
}
.innerTransport {
    margin: -88px 0 0 -264px;
}
.closeOutBox {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 12px;
    height: 12px;
    background: url(../images/closeQuery.png) no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
}
.titInnerBox {

    /* width: 440px; */
    margin: 40px 0 0 40px;
}
.makedTit {
    font-size: 20px;
    color: #333;
    margin-bottom: 12px;
    width: 432px;
    word-wrap: break-word;
}
.makedDetil {
    font-size: 12px;
    color: #999;
    width: 434px;
}
.makedBtn {
    text-align: right;
    margin:  40px 0px 20px 170px;;
}
.makedBtn button {
    width: auto;
    height: 32px;
    text-align: center;
    line-height: 32px;
    border: none 0;
    border-radius: 4px;
    margin-left: 12px;
    cursor: pointer;
    padding: 0 14px;
}
.makedBtn .trueBtn {
    border: 1px solid #3961b2;
    background: #3961b2;
    color: #fff;
}
.makedBtn .falseBtn {
    background: #fff;
    color: #333;
    border: 1px solid #e7e7e7;
}

/*复制功能优化*/
#copyBg {
    position: fixed;
    _position: absolute;
    left: 50%;
    top: 50%;
    width: auto;
    height: 40px;
    margin: -20px 0 0 -50px;
    border-radius: 6px;
    z-index: 800;
}
#copyBg .bgDiv {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.6;
    filter: alpha(opacity=60);
    border-radius: 6px;
}
#copyBg .textDiv {
    height: 40px;
    line-height: 40px;
    position: relative;
    left: 0;
    top: 0;
    text-align: center;
    color: #fff;
    font-size: 14px;
    display: inline-block;
    padding: 0 20px;
}

.contentBox{
    min-width: 1400px;
}
#navLeft{
    width: 10000px;
    position: fixed;
    left: 0px;
    top: 53px;
    height: 100%;
    z-index: 100;
    margin-bottom: 30px;
    overflow-y: auto;
    background: #fff;
}
.nav_ul{
    overflow-y: visible;
    width:194px;
}
#navLeft li{
    height: 40px!important;
    line-height: 40px;
    margin-top: 0px;
    position: relative;
}
#navLeft li:last-child{
    margin-bottom: 200px;
}
#navLeft i{
    height: 41px!important;
    line-height: 41px;
    position: absolute;
    left: 0;
    top: 0;
    padding: 0px 15px;
    text-align: center;
    width: 80%;
    display: inline-block;
    color: #000;
    border-radius: 0px;
    font-style: normal;
    cursor: pointer;
    border:1px solid #eee;
}
#navLeft i:hover{
    background:#eee;
}
.right-content{
    height:100%;
    z-index: 105;
    padding:20px 0 240px 0;
    position: absolute;
    top: 64px;
    left: 142px;
    overflow-y: scroll;
    width:100%;
}
.right-box{
    width: 100%;
    position: relative;
    top: 0px;
    padding-bottom:200px
}
.select-item-a{
    background:#0797ee!important;
    background-position: 124px 10px!important;
    color:#fff!important;
}
.right_active {
    right: 14px;
    position: absolute;
    top: 8px;
    left: 131px;
    width: 70px;
    height: 40px;
    background: none;
}
.select-item-a .right_active{
    background: url('../../images/nav-jt.png') no-repeat!important;
    background-size:100% 100%;
}
body{
    overflow: auto!important;
    padding: 0 10px;
}
.title-nav a {
    width: 150px;
    text-align: right;
    display: inline-block;
}
.border-black,.border-black-user{
    border:1px solid #999;
    padding:20px 10px;
    position: relative;
    top: -10px;
    width: 710px;
    min-height: 35px;
}
.padding10{
    padding:10px;
}
.ml10{
    margin:4px 5px 4px 20px!important;
}
.viewPhoneinput{
    width: 100px;
    position: relative;
    top: -5px;
}
.divBox{
    float: left;
    margin-left: 20px;
}
.itemBox{
    margin-bottom: 10px;
}
.clear_float {
    display: block;
    clear: both;
    margin-left: 82px;
    font-size: 12px;
    color: #f33;
    margin-bottom: -4px;
    height: 5px;
}
.noselect {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Chrome/Safari/Opera */
    -khtml-user-select: none; /* Konqueror */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently*/
}
.length-item{
    padding: 2px;
}
.length-item:last-child{
    border-bottom: none;
}
.length-item h3{
    font-weight: bold;
    padding:0 3px;
    margin-top: 5px;
}
.length-right{
    padding-left:50px;
}
.common-st .left{
    font-weight: bold;
}
.common-st .length-right{
    padding-left: 3px;
}
.weight-w{
    font-weight: bold;
}
.gray-font{
    font-weight: normal;
}
.dashed{
    /*border-bottom: 1px dashed #000;*/
}
.dashed:last-child{
    border: 0;
}
.dashed-padding{
    padding: 8px;
}
.defaultTit{
    z-index: 1000;
}
.defaultTit i{
    margin: 2px 2px 2px 8px;
    cursor: pointer;
}
.vehicleClassBox .defaultTit{
    position:absolute;
}
.vehicleClassBox .defaultTit i{
    display:inline-block!important;
}
.vehicleClassBox .defaultTit span{
    position: absolute;
    left: 29px;
    top: -7px;
    width: 300px;
}
.mr-3{
    margin-top: -3px;
}
.btnBox{
    text-align: center;
}
.button{
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding: 0 15px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
    margin: 10px 20px;
}
.customizedInputbox{
    height: 20px;
    line-height: 20px;
    width: 74px;
    line-height: 28px;
    border: 1px solid #ccc;
    padding: 0 6px;
    font-size: 12px;
    margin: 0px 5px 0 15px;
    display: inline-block;
    position: relative;
    top: 6px;
}
.customizedInputbox input{
    border: none;
    outline: none;
    position: absolute;
    top: 0px;
    left: 0px;
    background: none;
    width: 50px;
}
.customizedInputbox span{
    position: absolute;
    top: 0;
    right: 0;
    height: 20px;
    line-height: 20px;
}
.loadingBox{
    text-align: center;
    margin:100px 0;
    width: 1000px;
}
.dataInitBeforeImg{
    width: 60px;
    height: 60px;
    z-index: 10000;
    box-shadow: 1px 1px 20px #bbb;
}
.loadingBox span{
    display: block;
    font-size: 15px;
    color: #333;
    margin-top: 10px;
}
.w-150{
    width: 150px;
    margin-right: 10px;
}
.blue-text{
    color:#186FD5;
}
.defaultTit .wh-icon{
    background: #6490eb;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 14px;
    background-image:none;
    font-size:12px;
}
.toastBox-t{
    width: 700px;
    height: 300px;
    filter: alpha(opacity=80);
    margin: -150px 0 0 -350px;
    padding: 0 27px;
}
.specialPrice{
    border:1px solid #999!important;
    width:100px;
}
.appendDomThis{
    margin:10px 0px;
}
.uploadFileBox,.imgItem{
    border:1px solid #999;
    width:50px;
    height:60px;
    line-height:52px;
    text-align:center;
    font-size:48px;
    color:#999;
    position:relative;
    margin: 0 5px;
}
.uploaded-pic{
    width:100%;
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.imgItem .delete-pic{
    position:absolute;
    top:-17px;
    right:-17px;
    height:20px;
    width:20px;
    margin:8px;
    cursor:pointer;
    display:none;
}
.imgItem:hover .delete-pic{
    display:inline-block;
}
.filePicker,
.filePicker span,
.filePicker input{
    opacity:0!important;
    width:50px!important;
    height:60px!important;
    position:absolute!important;
    top:0px!important;
    left:0px!important;
    margin:0px!important;
}

.fixed-btn{
    position:fixed;
    right:0px;
    bottom:0px;
    background:#fff;
    text-align:center;
    height:50px;
    line-height:50px;
    width:100%;
    border-top:1px solid #999;
    z-index:101;
}
.disabled-gray{
    background:#666;
}
