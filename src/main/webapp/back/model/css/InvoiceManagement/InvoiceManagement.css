.searchBtn {
    margin-bottom: 0;
}

.optItem {
    padding: 0 0 10px 10px;
}

.optItem em {
    color: red;
}

.leftOptItem {
    vertical-align: middle;
}

.leftOptItem span,
.leftOptItem em {
    vertical-align: middle;
}

.leftOptItem input {
    margin: 0 4px 0 0;
    vertical-align: middle;
}

.checkItemOpt {
    background: #12d011!important
}


.distRight {
    margin-right: 30px;
}

.checkedNum {
    margin: 0 3px;
}

.statusColorYellow {
    color: #F59A23;
}

.redSatus {
    color: red;
}

.greenSatus {
    color: green;
}
/* 弹框 */
.outBgColor {
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.7;
    filter: alpha(opacity=70);
    z-index: 10;
}

.payTitBox li {
    width: 100%;
    display: inline-block;
    margin: 16px 0 0;
}
.payTitBox label {
    font-size: 14px;
    display: inline-block;
    text-align: right;
    color: #333;
    line-height: 24px;
    font-weight: normal;
}
.payTitBox input {
    width: 190px;
    display: inline-block;
    height: 24px;
    line-height: 24px;
    border: 1px solid #ccc;
    background: #fff;
    padding: 0 4px;
    box-sizing: border-box;
}
.payCenter {
    padding: 0 16px;
    box-sizing: border-box;
}
.centerTitBox label {
    display: block;
    text-align: center;
}
.abnormalBox,.abnormalBox .abnormalOk {
    width: 90px;
}
.applyType,.abnormalType {
    width: 82px;
}
.applyType .abnormalBtn,
.abnormalType .applyBtn {
    display: none;
}
