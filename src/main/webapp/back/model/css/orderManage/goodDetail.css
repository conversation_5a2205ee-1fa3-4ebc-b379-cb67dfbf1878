.searchBtn li .btnNoClick {
    background: #ccc;
    cursor: not-allowed;
}
.searchBtn {
    width: 232px;
    margin: 0 auto 40px;
}
.searchBtn li {
    margin-right: 12px;
    margin-bottom: 0;
}
.searchBtn li:last-child {
    margin-right: 0;
}
.searchBtn li input {
    width: 110px;
}
.chooseType{
    width: 110px;
}
.repair{
    border: .5px solid #333;
    width: 110px;
}
.reportBox{
    padding-left: 10px;
    margin: 15px 0;
    height: 150px;
}
.repor {
    width: 25%;
    border: .5px solid #333;
    float: left;
    height: 110px;
    padding: 15px;
}
.report {
    width: 33.4%;
    border: .5px solid #333;
    border-left: 0px;
    float: left;
    height: 110px;
    padding: 15px;
}
.suc{
    color: red !important;
}
.carMoney{
    width: 9%;
    border: .5px solid #333;
}
.sugges{
    border: .5px solid #333;
    width: 290px;
}
.uploadBox{
    width: 100%;
    height: 50px;
}
.upload{
    width: 50px;
    height: 50px;
    display:flex; 
    justify-content:center;
    align-items:center;
    float: left;
}
.boxPX{
    float: left;
    line-height: 50px;
}
.deleteBtn{
    color: red;
    margin-left: 30px;
}
.infom{
    display: inline-block;
}
.returnBtn{
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding: 0 20px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
    border: none;
    outline: none;
    cursor: pointer;
    text-align: center;
    float: right;
    margin-top: 6px;
    width: 100px;
}
.goodsDe span{
    width: 100%;
    border-left: 5px solid #86BAEB;
    padding: 0 0 0 5px;
}