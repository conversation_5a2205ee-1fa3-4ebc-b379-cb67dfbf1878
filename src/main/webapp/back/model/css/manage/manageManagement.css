.searchBtn {
    margin-bottom: 0;
}

.optItem {
    padding: 0 0 10px 10px;
}

.optItem em {
    color: red;
}

.leftOptItem {
    vertical-align: middle;
}

.leftOptItem span,
.leftOptItem em {
    vertical-align: middle;
}

.leftOptItem input {
    margin: 0 4px 0 0;
    vertical-align: middle;
}

.checkItemOpt {
    background: #12d011!important
}


.distRight {
    margin-right: 30px;
}

.checkedNum {
    margin: 0 3px;
}

.statusColorYellow {
    color: #F59A23;
}

.redSatus {
    color: red;
}

.greenSatus {
    color: green;
}
/* 弹框 */
.outBgColor,.carOutColor {
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.7;
    filter: alpha(opacity=70);
    z-index: 10;
}
.carOutColor {
    z-index: 101;
    opacity: 0;
}

.innerLook {
    height: 100%;
}

.delOutLook ,.carOutlook{
    margin-top: 0;
    margin-left: 0;
    height: auto;
    min-height: 170px;
    transform: translate(-50%, -50%);
}
.carOutlook{
    z-index: 102;
}
.delOutLook .ideabtnBox,.carOutlook .ideabtnBox{
    margin: 30px auto 20px;
}
.delTitle {
    text-align: left;
    line-height: 20px;
    font-size: 14px;
    color: #333;
    margin: 20px 0 0;
    padding: 0 20px;
}

.delOutLook li , .carOutlook li{
    width: 100%;
    display: inline-block;
    margin: 16px 0 0;
}
.delOutLook label ,.carOutlook label{
    font-size: 14px;
    display: inline-block;
    text-align: right;
    color: #333;
    line-height: 24px;
    font-weight: normal;
}
.delOutLook input, .carOutlook input {
    width: 190px;
    display: inline-block;
    height: 24px;
    line-height: 24px;
    border: 1px solid #ccc;
    background: #fff;
    padding: 0 4px;
    box-sizing: border-box;
}
.payCenter {
    padding: 0 16px;
    box-sizing: border-box;
}
.centerTitBox label {
    display: block;
    text-align: center;
}
.abnormalBox,.abnormalBox .abnormalOk {
    width: 90px;
}
.applyType,.abnormalType {
    width: 82px;
}
.applyType .abnormalBtn,
.abnormalType .applyBtn {
    display: none;
}
.searchUl .doubleBox{
    width: auto;
}
.searchUl .doubleBox input {
    width: 60px;
}
.uploaderFiles{
    padding: 20px 0 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-flow: wrap;

}
.uploaderFiles li {
    width: 30%;
    height: 80px;
    position: relative;
    overflow: hidden;
    border: 1px dotted #308DE3;
    margin: 0 10px 10px 0;
}
.updataBtnBox i {
    display: block;
    margin: 12px auto 6px;
    width: 35px;
    height: 28px;
    background: url(../../images/camera_updata.png) no-repeat;
    background-size: cover;
}
.updataBtnBox p {
    width: 100%;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #308DE3;
    line-height: 30px;
}
.updataBtnBox .uploaderInput {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    opacity: 0;
}
.file i {
    width: 30px;
    height: 30px;
    background: url(../../images/img_close.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 10;
}
.file img {
    width: 100%;
    height: 60px;
    display: block;
}
.imgTitle {
    width: 100%;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #333;
    font-size: 12px;
    background: #f7f7f7;
}
.uploadbox{
    display: flex;
    flex-flow: column;
    justify-items: center;
    box-sizing: border-box;
    padding: 10px 20px 20px;
}
.uploadbox textarea {
    width: 330px;
    height: 80px;
    outline: none;
}
.red{
    color: red;
}
.labeltip{
    text-align: right;
    line-height: 30px;
    box-sizing: border-box;
    padding: 0 20px;
}
.labeltip em {
    color: red;
}
.traveitem {
    margin-bottom: 20px;
}
.traveitem label {
    width: 70px;
    text-align: left;
}
.traveitem .traveitem_btn {
    display: inline-block;
    background: url(../../../image/u911.png) right center no-repeat;
    padding-right: 16px;
    height: 24px;
    line-height: 24px;
    color: #428bca;
    margin-left: 10px;
    cursor: pointer;
}
.license_plate_box li{
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 16px;
}
.license_plate_box li input{
    width: auto;
    height: auto;
    line-height: normal;
    margin-right: 10px;
}
.license_plate_box li label {
    cursor: pointer;
}
.questCilcle em{
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #02A7F0;
    text-align: center;
    line-height: 16px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    z-index: 11;
}
.receiptInfo, .receiptBlack, .receiptEdit{
    width: 500px;
}
.receiptInner{
    width: auto;
}
.receiptInner em{
    background: #4871c2;
}
.receiptInner span {
    max-width: none;
}
.fileOutLook {
    height: auto;
    padding-bottom: 20px;
}
.fileOutLook .luckbtnBox {
    width: 100%;
    display: flex;
    justify-content: center;
}

.fileOutLook .luckbtnBox  .close_block {
    margin-left: 10px;
    display: inline-block;
}

.orderMargin {
    margin: 0 0 0 10px;
}

.evidenceOutlook {
    display: block;
    width: 600px;
    height: auto;
    margin-left: 0;
    margin-top: 0;
    transform: translate(-50%, -50%);
}

.evidenceOutlook .ideabtnBox {
    width: 82px;
    margin: 30px auto;
}


.reasonTitle {
    max-height: 200px;
    overflow: auto;
}

.innerEvidence {
    padding: 10px 20px 0;
    box-sizing: border-box;
    max-height: 500px;
    overflow-y: scroll;
}

.innerEvidence strong {
    display: block;
    font-size: 16px;
    font-weight: 700;
    padding: 16px 0;
    box-sizing: border-box;
}

#innerEvidence {
    padding: 20px 30px 0;
}
#innerEvidence > li {
    padding: 14px 0;
    box-sizing: border-box;
    border-bottom: 1px dashed #ccc;
}
#innerEvidence > li:last-child {
    border-bottom: none;
}

#innerEvidence .innerItem > li {
    display: flex;
    width: 100%;
    align-items: center;
    margin: 0 0 10px;
}
#innerEvidence .innerItem li > label {
    width: 120px;
    text-align: right;
    padding: 0 10px 0 0;
    box-sizing: border-box;
}
#innerEvidence .innerItem li > p,
#innerEvidence .innerItem li > ul
 {
    flex: 1;
}


.evidenceImgList {
    max-height: 300px;
    overflow: auto;
}

.evidenceImgList li {
    width: 100px;
    height: 100px;
    float: left;
    margin: 0 10px 10px 0;
    overflow: hidden;
}

.evidenceImgList li img {
    display: block;
    width: 100%;
    height: auto;
}

.radioWarp {
    padding: 0 10px;
    box-sizing: border-box;
}

.radioWarp li {

}

.radioWarp > li > input {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin: 0 6px 0 0;
    cursor: pointer;
}
.radioWarp > li > label {
    vertical-align: middle;
    cursor: pointer;
}

.userTable {
    padding: 20px 10px 0;
    box-sizing: border-box;
}

.trajectorybox{
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-flow: row;
    align-items: center;
}
.trajectorybox label {
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
}
.trajectorybox input[type="radio"] {
    width: auto;
    margin: 0 5px 0 0;
}
.tooltipTd{
    overflow: unset !important;
}
.tooltipBox { 
    position: relative;
    display: inline-block;
    width: 80px;
}
.tooltipBox .wordtext{
    display: inline-block;
    width: 80px;
    overflow: hidden;
    text-wrap: nowrap;
    text-overflow: ellipsis;
}
.tooltipBox .tooltiptext{
    visibility: hidden;
    width: 120px;
    text-wrap: pretty;
    background-color: black;
    color: #fff;
    text-align: center;
    padding: 5px 0;
    border-radius: 6px;
    position: absolute;
    z-index: 1;
    left: 50%;
    bottom: 20px;
    transform: translate(-50%, 0);
}
.tooltipBox:hover .tooltiptext {
    visibility: visible;
}