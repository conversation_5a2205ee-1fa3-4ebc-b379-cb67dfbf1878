html,body {
    height: 100%;
}
.contentBox {
    /* height: 100%; */
    padding: 0 20px 60px;
    box-sizing: border-box;
}
.imgBox img {
    margin: 10px;
    width: 150px;
    height: 80px;
}

.searchBtn li .btnNoClick {
    background: #ccc;
    cursor: not-allowed;
}

.searchBtn {
    width: 232px;
    margin: 0 auto 40px;
}

.searchBtn li {
    margin-right: 12px;
    margin-bottom: 0;
}

.searchBtn li {
    float: left;
    margin-right: 12px;
    margin-bottom: 12px;
}

.searchBtn li .sbWidth {
    width: 80px;
}

.searchBtn li input {
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding: 0 12px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
}

.searchBtn li:last-child {
    margin-right: 0;
}

.searchBtn li input {
    width: 110px;
}

.saleCheckBox i {
    display: inline-block;
    cursor: pointer;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    background: url(../images/sale_normal.png) no-repeat;
    background-size: 100% 100%;
}

.saleCheckBox .saleCheck {
    background: url(../images/sale_checked.png) no-repeat;
    background-size: 100% 100%;
}

.modelBtnBox {
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 20;
    width: 100%;
    height: 60px;
    background: #fff;
    border-top: 1px solid #ccc;
}

.modelBtnBox .searchBtn {
    margin: 14px auto 0;
}

#optHistory {
    margin: 0 0 100px;
    overflow: hidden;
}

.ideaFinal {
    padding: 20px 10px;
}

.finalOpinion {
    width: 100%;
    display: flex;
    line-height: 26px;
}

.finalOpinion>label {
    width: 120px;
    text-align: right;
    vertical-align: middle;
    font-weight: 700;
}

.flexBox {
    flex: 1;
    vertical-align: middle;
}

.flexBox>strong {
    margin: 0 0 0 -24px;
    vertical-align: middle;
}

.finalOpinion textarea {
    outline: none;
    width: 300px;
    height: 140px;
    padding: 6px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.finalOpinion .xing {
    vertical-align: middle;
    color: red;
}

.finalOpinion em {
    vertical-align: middle;
    margin: 0 16px 0 0;
    cursor: pointer;
}

.finalOpinion em input {
    vertical-align: middle;
    cursor: pointer;
}

.finalOpinion em label {
    vertical-align: middle;
    padding-left: 6px;
    cursor: pointer;
}

.flexWidth {
    display: flex;
}
.flexWidth label {
    min-width: 80px;
}

.pictureUrlBox img {
    display: inline-block;
    width: 100px;
    height: auto;
    max-height: 70px;
    margin: 0 6px 0 0;
}

.clickColor {
    color: #02A7F0;
    vertical-align: middle;
}

.comOutlookBg {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: #000;
    opacity: 0.3;
    z-index: 100;
    display: none;
}

.comOutlook {
    width: 90%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 110;
    display: none;
}
.comOutlook .detailBox {
    background: #fff;
}

.closeBtn {
    position: absolute;
    right: 5px;
    top: 5px;
    z-index: 112;
    padding: 0px 6px;
    font-size: 27px;
    cursor: pointer;
}
.complaintBox,.histroyBox {
    display: none;
}

/* 历史记录 */
.historyBox {
    padding: 10px;
    box-sizing: border-box;
    width: 100%;
    min-height: 200px;
    max-height: 350px;
    overflow-y: scroll;
}

.innerList li {
    line-height: 30px;
    display: flex;
}
.innerList li:nth-of-type(2n) {
    background: #e8efff;
}
.innerList li span {
    color: #333;
    font-size: 14px;
    margin-right: 10px;
    line-height: 30px;
    display: inline-block;
    min-width: 150px;
}

.innerList li span:first-child,
.innerList li span:last-child {
    width: 150px;
}

.innerList li span:first-child {
    padding: 0 0 0 8px;
    box-sizing: border-box;
}
.innerList .longContent {
    height: 30px;
    flex: 1;
    overflow: hidden;
    padding: 0 60px 0 0;
    position: relative;
    word-break: break-word;
}
.longContent a {
    vertical-align: middle;
    display: inline-block;
    color: #02A7F0;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
}
.innerList .listNone {
    width: 100%;
    padding: 10px 0;
    box-sizing: border-box;
    text-align: center;
}
.showStyle {
    height: auto!important;
    overflow: initial!important;
}

/* 图片查看器 */
 
.viewer-container .customer_btn {
    color: #F00;
    width: 24px;
    height: 24px;
    display: inline-block;
    background: url(../../images/see.png) no-repeat;
    background-size: 24px 24px;
    vertical-align: middle;
}

.viewer-container {
    position: fixed;
    left: 50%;
    top: 52px;
    margin-left: -30%;
    width: 60%;
    height: 50vh;
    z-index: 10;
    /* border-top: 20px solid #d9edf7; */
    cursor: pointer;
}

.viewer-container h2 {
    font-size: 16px;
    color: #FFF;
    text-align: center;
    background: rgba(0, 0, 0, 0.6);
    line-height: 50px;
    height: 50px;
    padding: 0;
    position: absolute;
    left: 0;
    right: 0;
    border: none;
    /* display: none; */
    z-index: 10;
}

.viewer-button {
    z-index: 100;
}

.viewer-toolbar {
    width: 350px;
}

.viewer-title,
.viewer-navbar,
.viewer-play,
/* .viewer-prev, */
/* .viewer-next, */
.viewer-flip-horizontal,
.viewer-flip-vertical,
.viewer-rotate-right,
.viewer-reset {
    display: none;
}

.viewer-toolbar>li {
    margin-right: 10px;
}

.viewer-toolbar {
    padding: 3px 0 10px 0;
    width: 220px;
    justify-content: center;
    display: flex;
}

.viewer-open {
    overflow-y: auto;
}   

.viewerIndex {
    height: 30px;
    width: 100%;
    display: block;
    text-align: center;
    line-height: 30px;
    color: #fff;
    font-size: 16px;
    padding: 0 0 10px;
}
.detail{
    display: none;
}
.editor{
    display: none;
}