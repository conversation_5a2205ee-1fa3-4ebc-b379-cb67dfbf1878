
/* 导出模块 */

.excelBox .alertContent {
    margin: 15px auto;
    width: 280px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.fileBtnBox {
    width: 80px;
    height: 32px;
    position: relative;
}
.fileBtn, .rule_true {
    position: absolute;
    left: 0;
    top: 0;
    display: inline-block;
    width: 80px;
    height: 32px;
}
.fileBtn {
    opacity: 0;
    filter: alpha(opacity=0);
    z-index: 530;
}
.rule_true {
    z-index: 525;
}
.outBgColor{
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.5;
    filter: alpha(opacity=50);
    z-index: 10;
}
.colorOrange {
    color: #F59A23;
}
.colorGreen {
    color: green;
}
.colorRed {
    color: red;
}
.searchUl .tytCarUser {
    width: 220px;
}
.searchUl .tytCarUser label {
    width: 120px;
}