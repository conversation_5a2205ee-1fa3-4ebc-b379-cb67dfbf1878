/*
    carMarket.css for carLibEdit/*.html
    author: hey<PERSON>an
*/
tr {
    background: #fff;
}
.dataTable {
    overflow: hidden;
}
.innerDataTable {
    width: 100%;
    padding: 0 20px 20px;
    box-sizing: border-box;
    overflow: hidden;
}
.hotContent {
  width: 100%;
}
.hotContent>div {
  width: 22%;
  padding-right: 1%;
  box-sizing: border-box;
}
.hotContent>div:first-child {
    width: 34%;
}
.hotContent>div:last-child {
  padding-right: 0;
}
.tableTypeBtn {
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding: 0 12px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
    display: inline-block;
    margin: 0 0 12px;
    cursor: pointer;
}
.editInput {
    width: 90%;
    margin: 5% 0;
    height: 30px;
    border: 1px solid #e7e7e7;
    text-align: center;
}
.uploadBox {
    width: 32px;
    height: 32px;
    background: url(../../images/img_add.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    z-index: 10;
    margin: 0 auto;
}
.upload {
    width: 32px;
    height: 32px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 12;
    opacity: 0;
    cursor: pointer;
}
.tableBox img {
    max-height: 40px;
}
.changeUpload {
    width: 0;
    height: 0;
    opacity: 0;
}

.outBgColor {
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.7;
    filter: alpha(opacity=70);
    z-index: 10;
}
.innerLook {
    height: 100%;
}
.delOutLook {
    margin-top: -85px;
    margin-left: -185px;
}
.delTitle {
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    color: #333;
    margin: 30px 0 0;
}
.rankReset .alertContent,
.deleteAsk .alertContent {
    margin: 15px auto;
    width: 280px;
    min-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #333;
}
.editTextarea {
    height: 100px;
    width: 500px;
    border-radius: 4px;
    border: 1px solid #e7e7e7;
    outline: none;
    padding: 6px;
    box-sizing: border-box;
}
.must {
    color: red;
    font-size: 14px;
    line-height: 28px;
    vertical-align: middle;
    margin: 0 3px 0 0;
}
.navBox,.innerNavBox {
    z-index: 600!important;
}
.contentNavUl li {
    float: left;
    cursor: pointer;
}
.contentNavUl .disableActive {
    border: 1px solid #e7e7e7;
    color: #ccc;
    cursor: not-allowed;
}
.typeOne,.typeTwo,.typeThree,.typeFour,.typeFive {
    display: none;
}
.addTypeError {
    padding: 20px;
    text-align: center;
}
#updataStatusBox label {
    vertical-align: middle;
    line-height: 24px;
}
#updataStatusBox input {
    margin: 0 12px 0 0;
    vertical-align: middle;
    line-height: 24px;
}
#updataStatusBox .messageRight {
    clear: inherit;
    margin: 0;
}
.checkNoColor {
    color: red;
}

/* 导出模块 */

.excelBox .alertContent {
    margin: 15px auto;
    width: 280px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.fileBtnBox {
    width: 80px;
    height: 32px;
    position: relative;
}
.fileBtn, .rule_true {
    position: absolute;
    left: 0;
    top: 0;
    display: inline-block;
    width: 80px;
    height: 32px;
}
.fileBtn {
    opacity: 0;
    filter: alpha(opacity=0);
    z-index: 530;
}
.rule_true {
    z-index: 525;
}
.outBgColor{
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.5;
    filter: alpha(opacity=50);
    z-index: 10;
}
.checkPhoneBtn {
    font-size: 14px;
    line-height: 30px;
    height: 30px;
    padding: 0 20px;
    margin: 0 0 0 10px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #87d068;
}
.tableBox .trNoUser {
    height: 100px;
}
.trNoUser .innerNoUser span {
    color: #333;
    font-size: 14px;
    margin: 0 5px 0 0;
}
.trNoUser .innerNoUser a {
    color: #333;
    font-size: 14px;
    color: #428bca;
    cursor: pointer;
}
.editDealerHead {
    position: relative;
}
.editDealerHead .editDealerClose {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    padding: 14px;
}
.editDealerHead .editDealerClose i {
    display: block;
    width: 18px;
    height: 18px;
    background: url(../../images/closeMessage.png) no-repeat;
    background-size: 100% 100%;
}
.detailBox .detailListBox li input[type="password"] {
    display: inline-block;
    width: 150px;
    height: 28px;
    line-height: 28px;
    vertical-align: middle;
    border-radius: 4px;
    outline: none;
    padding: 0 10px;
}

#importCarList .alertContent {
    margin: 10px auto 0;
    width: 280px;
    height: 30px;
}
#importCarList .importTit {
    text-align: center;
    margin: 0 auto 10px;
    color: #de8a4c;
}