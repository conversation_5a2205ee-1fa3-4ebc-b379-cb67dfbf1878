/*
    carData.css for carData.html
    author: hey<PERSON>an
*/
/* 弹框默认 */
.outBgColor {
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.7;
    filter: alpha(opacity=70);
    z-index: 10;
}
.innerLook {
    height: 100%;
}
.delOutLook {
    margin-top: -85px;
    margin-left: -185px;
}
.delTitle {
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    color: #333;
    margin: 30px 0 0;
}
.rankReset .alertContent,
.deleteAsk .alertContent {
    margin: 15px auto;
    width: 280px;
    min-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #333;
}

/* 查询tab */
.checkBox {
    width: 100%;
    padding: 0 0 30px;
    border-bottom: 1px solid #ccc;
    margin: 0 0 20px;
}
.checkBox > li {
    
}
.checkBox > li label {
    width: 70px;
    line-height: 26px;
}
.listBox {
    max-width: calc(100% - 70px);
}
.listBox > li {
    float: left;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 0 14px;
    line-height: 26px;
    margin: 0 6px 8px 0;
    cursor: pointer;
}
.listBox .active {
    background: #15bb5f;
    border: 1px solid #15bb5f;
    color: #fff;
}
#tableBox {}
#tableBox img {
    max-height: 40px;
}
.tableTypeBtn {
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding: 0 12px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
    display: inline-block;
    margin: 0 0 12px;
    cursor: pointer;
}
.editInput {
    width: 90%;
    /* margin: 0 5%; */
    height: 30px;
    border: 1px solid #e7e7e7;
    text-align: center;
}
.unitClass {
    margin: 0 0 0 6px;
    display: inline-block;
}
.uploadBox {
    width: 32px;
    height: 32px;
    background: url(../../images/img_add.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    z-index: 10;
    margin: 0 auto;
}
.upload {
    width: 32px;
    height: 32px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 12;
    opacity: 0;
    cursor: pointer;
}
.changeUpload {
    width: 0;
    height: 0;
    opacity: 0;
}