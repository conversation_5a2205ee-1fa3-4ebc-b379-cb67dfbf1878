/*
    adStartList.css for adStartList.html
    author: heyuyuan
*/

#startList img {
    height: auto;
    max-height: 120px;
}

.cursorClass {
    display: inline-block;
    cursor: zoom-in;
    cursor: -webkit-zoom-in;
}

.outBgColor {
    width: 100%;
    height: 100%;
    position: fixed;
    _position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    opacity: 0.7;
    filter: alpha(opacity=70);
    z-index: 10;
    display: none;
}

.adStartOutlook .innerLook {
    height: 100%;
}

.adStartOutlook .alertContent {
    height: 30px;
    line-height: 30px;
    margin: 15px auto;
    text-align: center;
}

.status {
    color: #333;
}

.statusIng {
    color: #9ae02e;
}

.statused {
    color: #f90303;
}

.statusNull {
    color: #999999;
}
.searchUl .doubleBox{
    width: auto;
}
.searchUl .doubleBox label {
    width: auto;
}
.searchUl .doubleBox input {
    width: 60px;
}
.tipModal .alertContent {
    margin: 15px auto;
    width: 280px;
    min-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #333;
}
