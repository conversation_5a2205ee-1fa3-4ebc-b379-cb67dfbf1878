/*
    adStartEdit.css for adStartEdit.html
    author: heyuyuan
*/

html,
body {
    overflow-x: hidden;
}

.systemParameter {
    margin-bottom: 20px;
}

.transportNews li {
    display: none;
    margin-top: 8px;
    padding-bottom: 8px;
    /*  border-bottom:1px solid #ddd;	*/
}

.transportNews li>label {
    font-weight: 700;
    line-height: 32px;
    float: left;
    width: 160px;
    height: 32px;
    text-align: right;
    vertical-align: top;
}

.transportNews li select {
    width: 400px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #ccc;
    padding-left: 5px;
}

.transportNews li label i {
    font-style: normal;
    padding-right: 5px;
    color: #f00;
}

.transportNews li>input {
    width: 400px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #ccc;
    padding-left: 5px;
}

.chooseBox em {
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    margin: 0 20px 0 0;
    display: inline-block;
    cursor: pointer;
}
.chooseBox em input {
    display: inline-block;
    margin: 0 6px 0 0;
}
.chooseBox em label {
    display: inline-block;
    vertical-align: middle;
    font-weight: normal;
    cursor: pointer;
}
.transportNews .selectBox select {
    width: 120px;
}
.transportNews .selectBox input {
    width: 276px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #ccc;
    padding-left: 5px;
}

.transportNews .startTime,
.transportNews .endTime {
    width: 192px;
    vertical-align: middle;
}

.flogTime {
    vertical-align: middle;
    padding: 0px 5px;
}

.button,
.transportNews li input.button {
    width: auto;
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding: 0 20px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
    border: none;
    outline: none;
    cursor: pointer;
    text-decoration: none;
}

.contentBox {
    padding: 0 20px;
    width: 1000px;
    margin: 0 auto;
}


/*
Model 1:页面定位
*/

.titUrl {
    padding: 20px 0 !important;
    margin-bottom: 20px;
    border-bottom: 1px solid #e7e7e7;
}

.titUrl span {
    font-size: 14px;
    color: #333;
}

.titUrl .currentPage {
    color: #26a2d7;
}

.errorBlock {
    color: #ff3333;
    text-align: center;
    margin-top: 30px;
    height: 40px;
    line-height: 40px;
}

.fileBtnBox {
    width: 80px;
    height: 32px;
    position: relative;
}

.fileBtn {
    position: absolute;
    left: 0;
    top: 0;
    display: inline-block;
    width: 80px;
    height: 32px;
}

.fileBtn {
    opacity: 0;
    filter: alpha(opacity=0);
    z-index: 530;
}

.fileBtnBox .rule_true {
    border: none 0;
    width: 80px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-size: 14px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
}

.ImgName {
    display: inline-block;
    margin-left: 10px;
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
}

.innerPhone {
    width: 400px;
    height: 270px;
    border: 1px solid #999;
}

.innerPhoneTit {
    margin-top: 8px;
}

.photoWare h4 {
    line-height: 40px;
    text-align: center;
    border-bottom: 1px solid #999;
    background: #f5f5f5;
}

.imgBox {
    overflow: hidden;
    width: 170px;
    height: 115px;
    margin: 20px auto 0;
    border: 1px solid #ccc;
}

.imgBox img {
    display: block;
    width: 170px;
    height: 115px;
}

.photoName {
    display: block;
    font-size: 14px;
    color: #333;
    height: 40px;
    line-height: 40px;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 90%;
    margin: 0 auto;
    ;
}

.uploadImgbox {
    position: relative;
    height: 60px;
}


/*上传图片 优化===========================*/

.uploadImgbox input {
    width: 0;
    height: 0;
    display: inline-block;
}

.uploadImgbox button {
    line-height: 30px;
    position: absolute;
    top: 0;
    left: 50%;
    width: 80px;
    height: 30px;
    margin-left: -40px;
    text-align: center;
    border: 1px solid #ccc;
    background: #f5f5f5;
}

.uploadImgbox button {
    z-index: 1;
    border-radius: 5px;
}

.uploadImgbox input {
    position: absolute;
    top: 0;
    left: 50%;
    width: 80px !important;
    height: 30px !important;
    line-height: 30px !important;
    margin-left: -40px;
    z-index: 10;
    opacity: 0;
    filter: alpha(opacity=0);
    padding: 0;
}

.photoWare>span {
    display: block;
    height: 40px;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 40px;
}

.gengButton {
    margin: 20px 0 90px 130px !important;
}

#adFormSubmit {
    display: none;
}