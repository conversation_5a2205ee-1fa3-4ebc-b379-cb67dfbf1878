/* carCommon.css author hey<PERSON><PERSON>  */
.excelBox {
	z-index: 520;
}
.outBgColor {
	width: 100%;
	height: 100%;
	position: fixed;
	_position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	background: #000;
	opacity: 0.7;
	filter: alpha(opacity=70);
	z-index: 10;
}
.excelBox .alertContent {
	margin: 15px auto;
	width: 280px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-size: 14px;
	color: #333;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}
.fileBtnBox {
	width: 80px;
	height: 32px;
	position: relative;
}
.fileBtn, .rule_true {
	position: absolute;
	left: 0;
	top: 0;
	display: inline-block;
	width: 80px;
	height: 32px;
}
.fileBtn {
	opacity: 0;
	filter: alpha(opacity=0);
	z-index: 530;
}
.rule_true {
	z-index: 525;
}
.joinbgOutLook {
	width: 100%;
	height: 100%;
	position: fixed;
	background: #ccc;
	display: block;
	left: 0;
	top: 0;
	opacity: 0.2;
	filter: alpha(opacity=20);
	z-index: 500;
}
.fileBtn, .rule_true, .close_block {
	cursor: pointer;
}
.ideabtnBox input {
	border: none 0;
	width: 80px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	font-size: 14px;
	color: #fff;
	border: none 0;
	border-radius: 4px;
	background: #3961b2;
}
#fileField, .fileBtnBox {
	cursor: pointer;
}

/* 车队线索管理-tab样式 */
.tableHead {
	width: 400px;
	height: 34px;
	border: 1px solid #e7e7e7;
	border-radius: 10px 10px 10px 10px;
	margin-bottom: 20px;
}
.tableHead li {
	width: 50%;
	line-height: 32px;
	text-align: center;
	cursor: pointer;
}
.now_act {
	border-radius: 10px 0 0 10px;
}
.hositry_body {
	padding-top: 20px;
}
.hositry_act {
	border-radius: 0 10px 10px 0;
}
.tableHead li:active, .tableHead li.active {
	background: #3961b2;
	color: #fff;
}

/* 车队维护管理-头部信息 */
.listMainMsg {
	padding: 0 0 14px;
	box-sizing: border-box;
}
.listMainMsg span {
	margin-right: 14px;
	font-size: 14px;
	color: #333;
	line-height: 1;
	font-weight: 600;
}
.searchUl .radioPlantBox {
	width: 500px;
}
.searchUl .radioPlantBox input {
	display: inline-block;
	width: 14px;
	height: 14px;
	vertical-align: middle;
	margin: 0;
	margin-right: 4px;
	cursor: pointer;
}
.searchUl .radioPlantBox label {
	float: initial;
	display: inline-block;
	vertical-align: middle;
	width: auto;
	margin-right: 16px;
	cursor: pointer;
}
.searchUl .carLenMain input {
	width: 40px;
	vertical-align: middle;
}
.searchUl .carLenMain select {
	width: 54px;
	vertical-align: middle;
}

/**/
.taboutBox {
	padding: 0 16px;
	margin-bottom: 20px;
}
.taboutBox .tableBox {
	width: 100%;
	border: 1px solid #e7e7e7;
}
.taboutBox .tableBox th {
	font-size: 14px;
	padding: 0 !important;
	text-align: center;
	color: #333;
	background: #efefef;
}
.taboutBox .tableBox tr {
	height: 36px;
	text-align: center;
	border-top: 1px solid transparent;
	border-bottom: 1px solid #e7e7e7;
}
.usertableBox tr {
	text-align: left;
}
.taboutBox .tableBox .tableTh {
	line-height: 40px;
	height: 40px;
	border-top: none 0;
	background: #efefef;
}
.taboutBox .tableBox tr:not(.inerChaert):nth-child(2n+1) {
	background-color: #f7f7f7;
}
.taboutBox .tableBox tr.tableTh:hover {
	border-top: 1px solid #e7e7e7;
	border-bottom: 1px solid #e7e7e7;
	background: #e8efff;
}
.taboutBox .tableBox tr:hover {
	border-top: 1px solid #dde8ff;
	border-bottom: 1px solid #dde8ff;
	background: #e8efff;
}
th, td {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	border-right: 1px solid #e7e7e7;
}
.firstTd,.firstStopTd {
	position: relative;
}
.firstTd i {
	position: absolute;
	left: 0;
	top: 0;
	width: 34px;
	height: 34px;
	background: url(../images/newLeftTop.png) no-repeat;
	background-size: 100% 100%;
}
.firstStopTd i {
	position: absolute;
	left: 0;
	top: 0;
	width: 34px;
	height: 34px;
	background: url(../images/stopLeftTop.png) no-repeat;
	background-size: 100% 100%;
}
.taboutBox h2 {
	height: 16px;
	line-height: 16px;
	font-size: 14px;
	color: rgb(51, 51, 51);
	padding-left: 6px;
	margin: 0 0px 16px;
	border-left: 3px solid #15bb5f;
}
.companyBox {
	margin: 0 20px 16px;
	border: 1px solid #e7e7e7;
	box-sizing: border-box;
}
.contentBoxDetail {
	width: 1100px;
}
.detailBox {
	width: 1100px;
}
.detailUl {
	padding-top: 12px;
}
.detailUl li {
	width: 250px;
	margin: 0 0 12px 16px;
}
.detailUl label {
	width: 100px;
}
.noCompanyCar {
	width: 100%;
	height: 34px;
	text-align: center;
	line-height: 34px;
	color: #333;
}
.innerAllot {
	margin: -256px 0 0 -260px;
}
.AllotBox {
	padding: 20px 20px 0 20px;
	box-sizing: border-box;
}
.AllotBox h2 {
	font-size: 20px;
	color: #a9a5a5;
	font-weight: 600;
	height: 30px;
	line-height: 30px;
}
.isAllot li {
	width: 167px;
	height: 0;
	border-bottom: 40px solid #00a0e9;
	border-right: 30px solid transparent;
	border-radius: 8px 0 0 0;
	line-height: 40px;
	color: #fff;
	text-align: center;
}
.lineNav {
	width: 100%;
	border: 1px solid #00a0e9;
}
.linkAllot {
	width: 100%;
	box-sizing: border-box;
	padding: 0 10px;
	max-height: 240px;
	overflow-y: auto;
	height: 240px;
}
.linkAllot li {
	vertical-align: middle;
	border-bottom: 1px solid #e7e7e7;
	padding: 8px 4px;
	box-sizing: border-box;
}
.linkAllot li span {
	vertical-align: middle;
	color: #333;
	font-size: 14px;
	width: 140px;
	margin-right: 10px;
	display: inline-block;
}
.linkAllot li em {
	vertical-align: middle;
	color: #333;
	font-size: 14px;
}
.clearAllBtn {
	width: 80px;
	height: 32px;
	line-height: 32px;
	font-size: 12px;
	text-align: center;
	border: none 0;
	color: #fff;
	border-radius: 5px;
	cursor: pointer;
	margin-bottom: 10px;
	background: #15bb5f;
	margin-left: 10px;
}
.linkAllot .checkAllot {
	display: inline-block;
	vertical-align: middle;
	width: 14px;
	height: 14px;
	margin: 0 24px 0 0;
}
.linkAllot .changeAllot {
	vertical-align: middle;
	width: 80px;
	height: 20px;
	border: 1px solid #e6e7e9;
	margin: 0 6px;
}
.lineActilcel {
	padding: 6px 10px;
	background: #efefef;
	margin-bottom: 8px;
}
.lineActilcel span {
	color: #333;
	font-size: 14px;
}
.lineActilcel em {
	color: red;
	font-size: 14px;
}
.lineActilcel input {
	height: 22px;
	padding: 0 4px;
	width: 50px;
	box-sizing: border-box;
	border: 1px solid #e6e7e9;
}
#resultSource {
}
#resultSource ul {
	padding: 0 0 6px 30px;
}
#resultSource li {
	margin-bottom: 8px;
}
#resultSource ul input {
	width: 14px;
	height: 14px;
	vertical-align: middle;
	margin: 0;
}
#resultSource ul label {
	vertical-align: middle;
	color: #333;
	font-size: 14px;
	text-align: initial;
	padding: 0 0 0 6px;
	width: auto;
}
.innerAllot .makedBtn {
  margin: 40px 20px 20px 170px;
}
.allCountry {
	display: inline-block;
  cursor: pointer;
  padding: 0 7px;
  border-radius: 0 6px 6px 0;
  border: 1px solid #ccc;
  height: 24px;
  vertical-align: middle;
  line-height: 24px;
  background-color: #fff;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.allCountryChecked {
	background-image: url(../images/selectedIcon.png);
	background-repeat: no-repeat;
	background-position: right bottom;
}