.batchManage {
    position: relative;
    height: 30px;
}

.batchManage .title {
    padding-bottom: 8px;
    font-weight: 600;
}

.batchManageBox {
    position: absolute;
    top: 40px;
    left: 0px;
    width: 300px;
    padding-top: 12px;
    border: 1px solid #ddd;
    background-color: #fff;
    z-index: 999;
}

.batchManageBox input + input {
    margin-left: 12px;
}

.bgOutLook {
    opacity: 0.6;
}
.excelBox, .resultBox {
    z-index: 600!important;
}

.excelBox .alertContent {
    margin: 15px auto;
    width: 280px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.outLook.resultBox {
    width: 500px;
    height: auto;
    padding-bottom: 30px;
}

.resultBox .alertContent {
    width: auto;
    min-height: 40px;
    margin: 15px;
    text-align: center;
    font-size: 14px;
    color: #333;
}

.resultBox .alertContent .resultTable {
    margin-top: 20px;
    max-height: 200px;
    overflow: auto;
}

.fileBtnBox {
    width: 80px;
    height: 32px;
    position: relative;
}

.fileBtn,
.rule_true {
    position: absolute;
    left: 0;
    top: 0;
    display: inline-block;
    width: 80px;
    height: 32px;
}

.fileBtn {
    opacity: 0;
    filter: alpha(opacity=0);
    z-index: 530;
}

.rule_true {
    z-index: 525;
}


.ideabtnBox input {
    border: none 0;
    width: 80px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-size: 14px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #189beb;
}

#configFile,
.fileBtnBox {
    cursor: pointer;
}


td.order_limit {
    position: relative;
    overflow: visible!important;
}
.order_limit a {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.order_limit .limit_detail {
    position: absolute;
    bottom: 100%;
    left: -100px;
    z-index: 999;
    width: 600px;
    background: #fff;
    border: 1px solid #ddd;
    display: none;
}

.order_limit:hover .limit_detail {
    display: block;
}

.limit_detail_header {
    padding: 8px 0;
    font-weight: 600;
    
}

.limit_detail_body {
    max-height: 160px;
    padding: 0 20px;
    overflow: auto;
}

.limit_detail_body li {
    display: flex;
    justify-content: space-between;
    line-height: 30px;
}
/*进度条样式*/
#progressDialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0,0,0,0.6);
    z-index: 999;
}
.progressDialogMain {
    box-sizing: border-box;
    width: 400px;
    height: 170px;
    padding: 24px;
    margin: 200px auto 0;
    background-color: #fff;
}
.forceResult {
    position: relative;
}
.forceResult:hover .forceResultBox {
    display: block;
}
.forceResultBox {
    display: none;
    position: absolute;
    top: 32px;
    padding: 12px;
    width: 220px;
    text-align: center;
    background-color: #fff;
    border: 1px solid #eee;
    z-index: 100;
}
.forceResultBox a:first-child {
    margin-right: 16px;
}
.batchManageBox.searchAction{
    width: 360px;
    box-sizing: border-box;
    padding: 10px;
}
.sdcard-action {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
}
.sdcard-action input[type='text'] {
    background: #FFFFFF;
    border: 1px solid #cccccc;
    height: 32px;
    line-height: 232px;
}
.sdcard-action-tip{
    display: block;
    font-size: 12px;
    color: red;
}