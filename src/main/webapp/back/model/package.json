{"name": "manage", "version": "0.1.0", "private": true, "scripts": {"dev": "node ./deploy", "test": "node ./deploy", "rel": "node ./deploy", "help": "node ./deploy/help.js"}, "devDependencies": {"chalk": "^4.1.2", "copy-paste": "^1.3.0", "ftp-deploy": "^2.4.3", "gix-opn": "^5.4.4", "inquirer": "^8.0.0", "ora": "^5.1.0", "scp2": "^0.5.0", "tyt_deploy": "^1.0.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}