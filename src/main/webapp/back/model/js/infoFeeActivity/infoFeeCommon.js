/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2022-02-15 16:05:34 
 * @Last Modified by: yuyan.He
 * @Last Modified time: 2022-02-24 16:00:02
 */
let manage = tytTool.getRootPath_web();
let common = {
    myData: {
        suspectFlag: [
            '0##接单人与发货人为同一认证人；',
            '1##接单人与发货人认证同一企业；',
            '2##接单人与发货人认证同一辆车；',
            '3##1人同一天，接3单及以上，接单的车辆为同一车牌号；',
            '4##发货时间与接单时间间隔小于1分钟且确认付款时间间隔小于5分钟'
        ]
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        activityData: (data,other) => { //加载活动类型
            if (data.code == 200) {
                if(data.data && data.data.length > 0) {
                    tytTool.dataList(data.data, {
                        ele: '#activeList,#activityWarp==>option##请选择',
                        val: ['id,activityName','_','id:'],
                        attr: [other.type === 'listCheck' ? "value=={{activityName}}" : "value=={{id}}"]
                    })
                }
            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            }
            other.def.resolve(data);
        }
    },
    opt: {
        activityList: type => { //活动类型请求入口
            let def = $.Deferred();
            commonA.common.otherData = {
                def,
                type
            };
            commonA.common.otherUrl = `${manage}/order/encourage/rule/getActivity`;
            commonA.common.data = {
                flag: type === 'listCheck' ? 2 : 1 // 1.添加下拉框（符合添加规则）2.全部（列表条件）
            }
            tytTool.ajaxFn(commonA.common, commonJ.activityData)
            return def.promise();
        },
        colResizable: () => {
            // $(".tableTh th").width(80)
            $('.JCLRgrips').remove();
            let tabWidth = $('.tableBox').parents('.dataTable').width();
            $('.tableBox').width(tabWidth);
            tytTool.colResizable($('.tableBox'));
            $('.JCLRgrips').width(tabWidth);
        },
        inpFocusSave: _this => { //暂存值
            _this.attr('data-oldval',_this.val())
        },
        inpBlurValid: (_this, _reg, _toast) => {
            let flag = true;
            _this.val(_this.val());
            if (_reg != '' && NO(_this.val()) != '' && !tytTool.validateStr(_reg, _this.val())) {
                _toast && tytTool.toastShow(_toast, 2500)
                _this.val(_this.attr('data-oldval') || '');
                flag = false;
            }
            return flag;
        },
        loadReason: params => { //加载刷单嫌疑原因
            let arr  = params.split('##')[0].split(','),
                count = NO(params.split('##')[1]) || 0;
            let str = '';
            $.each(arr, (index, ele) => {
                str += `<li>${'（' + (index + 1) +'）' + tytTool.switchItem(ele, commonM.suspectFlag)}</li>`
            })
            $(".refreshReason").empty().append(str)
            $("#brushesCount").html(count)
        },
        operate: (type, opt, params) => { //操作功能
            if(location.href.indexOf('ruleList.html') != -1) { //抽奖活动管理页面
                if(type == 'close') {
                    $(".outBgColor,.outLook").fadeOut(100);
                    ruleM.saveData = {flag: 1}
                } else if(type == 'open') {
                    $("#activitySpanWarp,#activityWarp").hide()
                    if(opt == 'add') {
                        $("#activityWarp").show();
                        $("#activityWarp option:first").attr('selected',true)
                        $(".luckName").html('新增活动规则');
                        //加载如默认指定的行数
                        $(".ruleConfig").empty().append(ruleO.loadDefault('load'))
                        $(".outBgColor,.outLook").fadeIn(200);
                    } else if(opt == 'edit') {
                        $("#activitySpanWarp").show();
                        $(".luckName").html('修改活动规则')
                        //查看活动是否启动,并加载列表配置
                        ruleO.ruleDetail(params).then(res => {
                            if(res.code == 200) {
                                $(".outBgColor,.outLook").fadeIn(200);
                            }
                        })
                    }
                }
            } else if(location.href.indexOf('alreadyJoin.html') != -1) { //预设中奖名单页面
                if(type === 'close') {
                    $("#submitActie").attr('onclick','')
                    $(".outBgColor,.outLook").fadeOut(100);
                } else if(type === 'open') {
                    $(".refreshOrder,.changeOrder,.orderDetail,.refreshReasonWarp").hide()
                    $(".luckbtnBox").show()
                    if(opt === 'refresh') { // 确认刷单
                        $(".refreshOrder").show()
                        $(".luckName").html('确认刷单')
                        $("#submitActie").attr('onclick',`joinO.brushSubmit(${params},'${opt}')`)
                    } else if(opt === 'change') { // 修改订单
                        $(".changeOrder").show()
                        $(".luckName").html('修改订单')
                        $("#submitActie").attr('onclick',`joinO.brushSubmit(${params},'${opt}')`)
                    } else if(opt === 'detail') { // 详情
                        $(".luckbtnBox").hide()
                        $(".orderDetail").show()
                        $(".luckName").html('详情')
                        joinO.openDetail(params)
                    } else if(opt === 'reason') { // 刷单嫌疑原因
                        $(".refreshReasonWarp").show()
                        $(".luckName").html('刷单嫌疑原因')
                        commonO.loadReason(params)
                        $(".luckbtnBox").hide()
                    }
                    $(".outLook").width(opt === 'detail' ? 600: 370)
                    $(".outBgColor,.outLook").fadeIn(200);
                }
            }
        },
        formReset: fn => {
            tytTool.formReset($('.searchUl'),fn)
        }
    }
}

let commonM = common.myData;
let commonA = common.arr;
let commonJ = common.ajx;
let commonO = common.opt;