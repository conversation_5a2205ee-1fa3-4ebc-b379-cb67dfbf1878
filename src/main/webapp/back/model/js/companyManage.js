// companyManage.js  auther: hyy
var pageSize = 30;
var goods = {
	arr: {
		getListData: {
			otherUrl: '',
			type: 'post',
			contentType: 'application/json',
			headers: {
				token: JSESSIONID
			},
			data: ''
		},
		getTokenLoad: {
			otherUrl: '',
			type: 'get',
			contentType: 'application/json',
			headers: {
				token: JSESSIONID
			}
		},
		recordExportData: {
			otherUrl: '',
			type: 'post',
			contentType: 'application/json',
			headers: {
				token: JSESSIONID
			},
			data: ''
		},
		radioHandle: {
			otherUrl: '',
			type: 'post',
			contentType: 'application/json',
			headers: {
				token: JSESSIONID
			},
			data: ''
		},
		sendHandle: {
			otherUrl: '',
			type: 'post',
			contentType: 'application/json',
			headers: {
				token: JSESSIONID
			},
			data: ''
		}
	},
	ajaxfn: {
		getListData: function(data) {
			var _data = data.data;
			var data_list = _data.orderList;
			$("#companyManage tr:not('.tableTh')").remove();
			var arrParams = {
				currentPage: _data.pageNo || 1,
				totalPages: _data.maxPage || 1
			}
			if (data_list && _data.maxPage > 0) {
				$("#totalPages").html(_data.maxPage);
				$("#currentPage").html(_data.pageNo);
				$("#rowCount").html(data.totalCount);
				tytTool.jqPaginatorExtend(arrParams, 'goodsOptfn.getListMsg')
			} else {
				$("#totalPages").html(0);
				$("#currentPage").html("");
				$("#rowCount").html(0);
				tytTool.jqPaginatorExtend(arrParams, 'goodsOptfn.getListMsg')
			}
			//正式载入数据

			var _html = '',
				_robStatus = '',
				_startPoint = '',
				_destPoint = '',
				_startdest = '',
				_corpOrderType = '',
				_jump = '';
			if (data_list && data_list.length > 0) {
				$.each(data_list, function(index, el) {
					//0待接单/同意,4同意装货/装货中 5车主装货完成13车方卸货完成-待货方确认 14货方确认卸货完成
					switch (Number(data_list[index].robStatus)) {
						case 0:
							_robStatus = '待同意';
							break;
						case 4:
							_robStatus = '装货中';
							break;
						case 5:
							_robStatus = '装货完成';
							break;
						case 13:
							_robStatus = '车方卸货完成';
							break;
						case 14:
							_robStatus = '货方卸货完成';
							break;
					}

					switch (Number(data_list[index].corpOrderType)) {
						case 1:
							_corpOrderType = '特运通代理';
							if (data_list[index].robStatus == 4 || data_list[index].robStatus == 5) {
								_jump = `<span class="ver_block ver_span">|</span><a class="info_detail ver_block" href="javascript:;" 
								onclick="goodsOptfn.radioHandle(2,${tytTool.NullObj(data_list[index].id)})">异常上报</a>`
							} else {
								_jump = '';
							}
							break;
						case 2:
							_corpOrderType = '企业自发';
							_jump = ''
							break;
					}

					var startPoint = tytTool.NullObj(data_list[index].startPoint)
					var destPoint = tytTool.NullObj(data_list[index].destPoint)
					if (startPoint != '' && destPoint != '') {
						_startdest = startPoint + '-->' + destPoint;
					} else if (startPoint != '' && destPoint == '') {
						_startdest = startPoint;
					} else if (startPoint == '' && destPoint != '') {
						_startdest = destPoint;
					}
					_html += `<tr>
					<td>${tytTool.NullObj(data_list[index].tsOrderNo)}</td>
					<td>${tytTool.NullObj(data_list[index].tsId)}</td>
					<td>${tytTool.NullObj(data_list[index].platTsId)}</td>
					<td>${_startdest}</td>
					<td>${tytTool.NullObj(data_list[index].taskContent)}</td>
					<td>${_corpOrderType}</td>
					<td>${tytTool.NullObj(data_list[index].corpUploadCellphone)}</td>
					<td>${tytTool.NullObj(data_list[index].payCellPhone)}</td>
					<td>${tytTool.NullObj(data_list[index].headPlateNumbers)}</td>
					<td>${_robStatus}</td>
					<td><a class="info_detail ver_block" href="javascript:;" onclick="tytTool.openWinAuto('companyManageDetails.html?id=${tytTool.NullObj(data_list[index].id)}&robStatus=${tytTool.NullObj(data_list[index].robStatus)}','details',1200,600)">查看</a>${_jump}</td>
				</tr>`

				});
				$("#companyManage").append(_html);
				tytTool.bindHidePhone()
			}
		},
		getTokenLoad: function(data) {
			if (data.code == 200) {
				goodsOptfn.recordExportData(data.data)
			}
		},
		radioHandle: function(data) {
			if (data.code == 200) {
				console.log(data)
				$("#exCarLook li").remove();
				var _li = '';
				$.each(data.data, function(index, el) {
					_li += `<li><input type="radio" tname="exType" name="radioEx" value=${data.data[index].value} onclick="goodsOptfn.radioClick($(this),'${data.data[index].name}')"><span  onclick="goodsOptfn.radioClick($(this),'${data.data[index].name}')">${data.data[index].name}</span></li>`
				});
				$("#exCarLook").append(_li);
			}
		},
		sendHandle: function(data) {
			if (data.code == 200) {
				location.reload()
			}
		}
	},
	optFn: {
		getListMsg: function(currentPage) {
			currentPage == '' ? currentPage = 1 : currentPage = currentPage;
			var arr = tytTool.htmlGetTname($(".searchUl"));
			goodsArr.getListData.otherUrl = orgService + '/order/tyt/list?currentPage=' + currentPage + '&pageSize=' + pageSize + '&menuId=' + tytTool.parseSearch().menuId;
			goodsArr.getListData.data = JSON.stringify(arr)
			tytTool.ajaxFn(goodsArr.getListData, goodsAjaxfn.getListData)
		},
		getTokenLoad: function() {
			goodsArr.getTokenLoad.otherUrl = orgService + '/order/export/getToken';
			tytTool.ajaxFn(goodsArr.getTokenLoad, goodsAjaxfn.getTokenLoad)
		},
		recordExportData: function(_data) {
			var arr = tytTool.htmlGetTname($(".searchUl"));
			window.open(orgService + '/order/tyt/exportOrder?validToken=' + _data + '&' + tytTool.parseParam(arr))
		},
		radioHandle: function(_val, _id) {
			if (_val == 2) {
				$("#exPartyVal").val(1)
			} else {
				$("#exPartyVal").val(2)
			}
			$("#orderIdVal").val(_id)
			goodsArr.radioHandle.otherUrl = orgService + '/order/ex/getExType?exParty=' + _val;
			tytTool.ajaxFn(goodsArr.radioHandle, goodsAjaxfn.radioHandle)
			goodsOptfn.toggleOutlook();
		},
		sendHandle: function(_val) {
			goodsArr.sendHandle.otherUrl = orgService + '/order/ex/save/report';
			var arr = tytTool.htmlGetTname($("#exLookBox"));
			goodsArr.sendHandle.data = JSON.stringify(arr)
			console.log(arr)
			tytTool.ajaxFn(goodsArr.sendHandle, goodsAjaxfn.sendHandle)
		},
		checkForm: function() {
			var arr = {
				"totalPages": 1,
				"pageSize": pageSize,
				"visiblePages": 7,
				"currentPage": 1,
				"funName": goodsOptfn.getListMsg
			}
			tytTool.addResetIdent($('.searchUl'))
			tytTool.jqPaginator('#pageNumberBox', arr)
		},
		toggleOutlook: function() {
			$(".exLookBox").css({
				'width': '480px',
				'height': '374px',
				'margin-top': '-187px',
				'margin-left': '-240px'
			});
			$("#otherExremark").remove();
			$(".outBgColor,.exLookBox").toggle();
		},
		radioClick: function(_this, _type) {
			$("#otherExremark").remove();
			$("#exCarLook input").removeAttr('checked')
			_this.parents('li').find('input').attr('checked', true)
			if (_type === '其他') {
				$(".exLookBox").css({
					'width': '480px',
					'height': '496px',
					'margin-top': '-248px',
					'margin-left': '-240px'
				});
				$("#exCarLook").after(`<div id="otherExremark">
					<textarea name="" tname="exOther" id="" cols="30" rows="10" maxlength="100" placeholder="最多100个字"></textarea>
				</div>`)
			} else {
				$(".exLookBox").css({
					'width': '480px',
					'height': '374px',
					'margin-top': '-187px',
					'margin-left': '-240px'
				});
			}
		}
	}
}

var goodsArr = goods.arr;
var goodsAjaxfn = goods.ajaxfn;
var goodsOptfn = goods.optFn;

var arrAll = {
	"totalPages": 1,
	"pageSize": pageSize,
	"visiblePages": 7,
	"currentPage": 1,
	"funName": goodsOptfn.getListMsg
}

$.when(sessionAjx).done(function() {
	if (tytTool.parseSearch().tsId) {
		$("#tsIdInp").val(tytTool.parseSearch().tsId)
	}
	tytTool.jqPaginator('#pageNumberBox', arrAll);
});



$(".tableBox").colResizable({
	liveDrag: true,
	draggingClass: "dragging",
	resizeMode: 'flex',
	minWidth: 80
});

$(document).click(function(event) {
	$("#boxDiv,#boxDiv2").hide();
});
$(".areaDiv").click(function(event) {
	event.stopPropagation();
});

var commonJson = {
	max: 30,
	minChars: 0,
	width: 180,
	scrollHeight: 200,
	matchContains: true,
	autoFill: false,
	minChars: 1,
	nohaveData: '无匹配区域，请修改关键词',
	formatItem: function(row, i, max) {
		return row.name;
	},
	formatMatch: function(row, i, max) {
		return row.match;
	},
	formatResult: function(row) {
		return row.name;
	}
}
var startJson = {
	resultsClass: 'start_place'
}
var destJson = {
	resultsClass: 'end_place'
}
$('#startPointBox').autocomplete(cities,
	$.extend(startJson, commonJson)).result(function(event, row, formatted) {
	$("#startPointBox,#startPoint").val(row.name);
	$("#startProvinc").val(row.provinc);
	if (row.city == row.area) {
		$("#startCity").val(row.city);
		$("#startArea").val('');
	} else {
		$("#startCity").val(row.city);
		$("#startArea").val(row.area);
	}
});

$('#destPointBox').autocomplete(cities,
	$.extend(destJson, commonJson)).result(function(event, row, formatted) {
	$("#destPointBox,#destPoint").val(row.name);
	$("#destProvinc").val(row.provinc);
	if (row.city == row.area) {
		$("#destCity").val(row.city);
		$("#destArea").val('');
	} else {
		$("#destCity").val(row.city);
		$("#destArea").val(row.area);
	}
});
$("#startPointBox").keyup(function(event) {
	if ($(this).val() == '') {
		$("#startPoint,#startPointBox,#startProvinc,#startCity,#startArea").val('');
	}
});
$("#destPointBox").keyup(function(event) {
	if ($(this).val() == '') {
		$("#destPoint,#destPointBox,#destProvinc,#destCity,#destArea").val('');
	}
});
$("#startPointBox").blur(function(event) {
	if ($(".start_place ul li").length == 0) {
		$("#startPoint,#startPointBox,#startProvinc,#startCity,#startArea").val('');
		$(".nohaveList").remove()
	}
});
$("#destPointBox").blur(function(event) {
	if ($(".end_place ul li").length == 0) {
		$("#destPoint,#destPointBox,#destProvinc,#destCity,#destArea").val('');
		$(".nohaveList").remove()
	}
});