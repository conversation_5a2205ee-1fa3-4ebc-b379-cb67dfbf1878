/*
    directActivity.js for directActivity.html
    author: <PERSON><PERSON><PERSON>
*/
var directActivity = {
    pageSize:20,
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data,_currentPage) {
            if(data.code == 200) {
                var data_list = data.data.list;
                var count = data.data.pageBean.rowCount;
                var currentPage = Number(_currentPage),
                    maxPage = Math.ceil(Number(count) / directActivity.pageSize),
                    rowCount = Number(count);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams,'dao.listOpt')
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams,'dao.listOpt')
                }
                //正式载入数据
                var _html = '',_edit = '';
                if (data_list && data_list.length > 0) {
                    var awardBoxIdText = '';
                    $.each(data_list, function(index, el) {
                        if(el.status == 1){
                            awardBoxIdText = '未发奖';
                        }else if(el.status == 2){
                            awardBoxIdText = '已发奖';
                        }else if(el.status == 4){
                            awardBoxIdText = '发奖中';
                        }else if(el.status == 6){
                            awardBoxIdText = '已撤销';
                        }else{
                            awardBoxIdText = '不是注册用户';
                        }
                        _html +=`<tr>
							<td>${tytTool.NullObj(el.id)}</td>
							<td>${tytTool.NullObj(el.userId)}</td>
                            <td class="blue" onclick="phoneSoHfn($(this),'${el.cellPhone}','定向礼包发放')">查看电话</td>
							<td>${tytTool.NullObj(el.activityName)}</td>
							<td>${tytTool.NullObj(el.boxContent)}</td>
						    <td>${tytTool.timeTrans(el.mtime, { timeOpt: ['Y', 'M', 'D', 'h', 'm', 's'],splitStr: "/"}).replace(/\\s+/g, " ")}</td>
							<<td>${awardBoxIdText}</td>
						</tr>`
                    });
                    $("#directActivity").html(_html);
                } else {
                    _html = '<tr><td colspan="'+ $(".tableTh th").length +'">暂无查询数据</td></tr>'
                    $("#directActivity").html(_html);
                }
            } else {
                if(tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
        sendWinBackCall:function (data) {
            if(data.code == 200) {
                if(tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
                setTimeout(function(){
                    $('#loadingBox').hide();
                    tytTool.reloadWin()
                },1000)
            } else {
                if(tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
                $('#loadingBox').hide();
            }
        }
    },
    opt: {
        loadList:function () {
            var pageArr = {
                "totalPages": 1,
                "pageSize": directActivity.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": dao.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) {
            currentPage==''?currentPage=1:currentPage=currentPage;
            daa.common.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            var page = {
                "currentPage": currentPage,
                "pageSize": directActivity.pageSize
            }
            daa.common.data = $.extend({}, arr, page)
            daa.common.otherUrl = tytTool.getRootPath_web() + '/promo/direct/list?currentPage='+ currentPage + '&pageSize=' + directActivity.pageSize;
            tytTool.ajaxFn(daa.common, daj.listData)
        },
        sendWinFun:function(){
            $('#loadingBox').show();
            daa.common.otherUrl = tytTool.getRootPath_web() + '/promo/direct/award';
            tytTool.ajaxFn(daa.common, daj.sendWinBackCall)
        },
        // 撤回未发奖
        sendCancelFun:function(){
            daa.common.otherUrl = tytTool.getRootPath_web() + '/promo/direct/cancel/award';
            tytTool.ajaxFn(daa.common, function(data){
                if(data.code == 200) {
                    if(tytTool.NullObj(data.msg) != '') {
                        tytTool.toastShow(data.msg, 4000)
                    }
                    setTimeout(function(){
                        tytTool.reloadWin()
                    },1000)
                } else {
                    if(tytTool.NullObj(data.msg) != '') {
                        tytTool.toastShow(data.msg, 4000)
                    }
                }
            })
        },
        excelExportModel: function() { //下载
            tytTool.openPostWindow(tytTool.getRootPath_web() + `/promo/direct/excelExportModel`, '');
        },
        fileInfo:function(source, _this) { //file选择
            var f = source.files[0];
            var name = f.name;
            $(".alertContent").html(name).attr('data-doc',name);
        },
        excelImportFile:function(){ //上传file请求
            if (tytTool.NullObj($(".alertContent").attr('data-doc')) == "") {
                tytTool.toastShow("请选择需上传的文件",1500);
                return false;
            }
            var formData = new FormData();
            formData.append("fileField",$("#fileField")[0].files[0]);
            $.ajax({
                type: "POST",
                url: tytTool.getRootPath_web() +  "/promo/direct/importList",
                data: formData,
                contentType: false,
                /**
                 * 必须false才会避开jQuery对 formdata 的默认处理
                 * XMLHttpRequest会对 formdata 进行正确的处理
                 */
                processData: false,
                async : false,
                dataType:"json",
                success: function (data) {
                    if(data.code == 200){
                        tytTool.toastShow(data.msg,2500);
                        setTimeout(function(){
                            tytTool.reloadWin()
                        },5000)
                    } else {
                        var mag = data.msg || '导入失败，请仔细核对！'
                        tytTool.toastShow(mag,3000);
                        preO.resetFile()
                    }
                },
                error: function (xhr, status) {
                    tytTool.toastShow('仅支持csv文件',1500);
                    preO.resetFile()
                }
            });
        },
        closeRefundModal:function () {
            $('#directActivityModal').hide();
            $('#directCancelModal').hide();
        },
        showModal:function () {
            $('#directActivityModal').show();
        },
        showCancelModal:function () {
            $('#directCancelModal').show();
        }
    }
}

var dad = directActivity.myData;
var daa = directActivity.arr;
var daj = directActivity.ajx;
var dao = directActivity.opt;
dao.loadList()

