/*
    carClue.js for carClue.hmtl
    author: heyuyan
*/
var list = {
    pageSize:20,
    myData: {
        defineData: {}
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data,_currentPage) {
            if(data.code == 200) {
                var data_list = data.data.list;
                var count = data.data.pageBean.rowCount;
                var currentPage = Number(_currentPage),
                    maxPage = Math.ceil(Number(count) / list.pageSize),
                    rowCount = Number(count);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams,'lo.listOpt')
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams,'lo.listOpt')
                }
                //正式载入数据
                var _html = '',_edit = '';
                if (data_list && data_list.length > 0) {
                    $.each(data_list, function(index, el) {
                        lm.defineData[el.id] = {
                            userId: el.userId,
                            headTitle: NO(el.headTitle),
                            tailTitle: ''
                        }
                        var infoStatusText = '';
                        var address = tytTool.placeRuleTwo({
                            pro:el.headSourceProvince,
                            city:el.headSourceCity,
                            county:el.headSourceArea
                        });
                        var button = '';
                        if(el.postStatus == 1 && el.auditStatus == 2){
                            infoStatusText = '上架';
                            button = '<a id="'+ el.id +'" class="info_detail ver_block">详情</a><span class="line_span">|</span><a id="'+ el.id +'" postStatus="'+ el.postStatus +'" class="xiajia ver_block">下架</a>';
                        }
                        if(el.postStatus == 2){
                            infoStatusText = '下架';
                            button = '<a id="'+ el.id +'" class="shangjia ver_block">申请上架</a>';
                        }
                        if(el.auditStatus == 3){
                            infoStatusText = '审核未通过';
                            button = '<a id="'+ el.id +'" class="shangjia ver_block">申请上架</a>';
                        }
                        if(el.postStatus == 1 && el.auditStatus == 1){
                            infoStatusText = '审核中';
                            button = '<a id="'+ el.id +'" class="info_detail ver_block">详情</a>';
                        }
                        _html +=`<tr>
							<td>${tytTool.NullObj(el.id)}</td>
							<td  width="100px">${tytTool.NullObj(el.userName)}</td>
							<td>${tytTool.NullObj(el.saleName)}</td>
							<td>${tytTool.timeTrans(el.publishTime, {timeOpt: ['Y', 'M', 'D','h','m','s'],splitStr: "/"}).replace(/\\s+/g, " ")}</td>
							<td>${tytTool.timeTrans(el.headCheckInTime, {timeOpt: ['Y', 'M', 'D'],splitStr: "/"}).replace(/\\\\s+/g, " ")}</td>
							<td>${tytTool.NullObj(el.headBrand)}</td>
							<td>${tytTool.NullObj(el.headHorsepower)}</td>
							<td>${tytTool.NullObj(el.headDriveStyle)}</td>
							<td>${address}</td>
							<td>${infoStatusText}</td>
							<<td>
								${button}
							</td>
						</tr>`
                    });
                    $("#headList").html(_html);
                    lo.goDetailPage();
                } else {
                    _html = '<tr><td colspan="'+ $(".tableTh th").length +'">暂无查询数据</td></tr>'
                    $("#headList").html(_html);
                }
                tytTool.bindHidePhone();
                tytTool.colResizable($(".tableBox"));
            } else {
                if(tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
        renderResultFun:function (data) {
            if(data.code == 200) {
                var brand_str = '';
                for(var i=0;i<data.data.length;i++){
                    brand_str += '<li onclick="lo.selectBrancd($(this))" class="brand_li">'+ data.data[i].carBrandName +'</li>';
                }
                $('.brand_ul').html(brand_str).show();
            } else {
                if(tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        }
    },
    opt: {
        loadList:function () {
            var pageArr = {
                "totalPages": 1,
                "pageSize": list.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": lo.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);

            $('#headBrand').bind('input propertychange', function () {
                lo.searchBrandByKeyValue($(this).val());
            })
            $('body').bind('click', function () {
                $('.brand_ul').hide();
            })
        },
        listOpt: function(currentPage) {
            currentPage==''?currentPage=1:currentPage=currentPage;
            la.common.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            //马力
            var horsepower = $('#horsepower').val();
            var headHorsepowerMin = '',headHorsepowerMax = '';
            if(horsepower != ''){
                if(horsepower.indexOf('-') != -1 ){
                    headHorsepowerMin = horsepower.split('-')[0];
                    headHorsepowerMax = horsepower.split('-')[1];
                }else{
                    headHorsepowerMin = '';
                    headHorsepowerMax = lo.RemoveChinese(horsepower);
                }
            }
            var page = {
                "currentPage": currentPage,
                "pageSize": list.pageSize,
                headHorsepowerMax:headHorsepowerMax,
                headHorsepowerMin:headHorsepowerMin,
                infoType:1,
            }
            la.common.data = $.extend({}, arr, page)
            la.common.otherUrl = tytTool.getRootPath_web() + '/used/car/sale/getList?currentPage='+ currentPage + '&pageSize=' + list.pageSize;
            tytTool.ajaxFn(la.common, lj.listData)
        },
        searchBrandByKeyValue:function(val){
            //请求基本信息
            la.common.data.brandName = val;
            la.common.data.brandType = 1;
            la.common.otherUrl = tytTool.getRootPath_web() + '/used/car/sale/getUsedCarBrandList?';
            tytTool.ajaxFn(la.common, lj.renderResultFun);
        },
        xiajiaFun:function(id){
            var reason = $(".auditSeclct").val();
            if(reason == '') {
                tytTool.toastShow('请选择下架原因',3000)
                return false;
            }
            //目前id为单个适合此执行方法:
            var liatArr = lm.defineData[id];
            //请求基本信息
            la.common.data = $.extend({},{
                ids: id,
                postStatus: 2,
                dismountReason: reason
            }, liatArr);
            la.common.otherUrl = tytTool.getRootPath_web() + '/used/car/sale/updateCarStatus?';
            tytTool.ajaxFn(la.common, function(data){
                if(data.code == 200) {
                    tytTool.toastShow('车辆下架成功', 4000)
                    $('#confirmRefundModal').hide();
                    lo.loadList();
                } else {
                    if(tytTool.NullObj(data.msg) != '') {
                        tytTool.toastShow(data.msg, 4000)
                        $('#confirmRefundModal').hide();
                    }
                }
            });
        },
        showModal:function(id){
            $('#confirmRefundModal').show();
            // $('.tip').html('是否将该车辆信息下架？').attr('id',id);
            $('.tip').attr('id',id);
        },
        goDetailPage:function () {
            $('.info_detail').click(function () {
                var id = $(this).attr('id');
                // window.location.href = './carSaleView.html?id=' + id;
                var _url = './carSaleView.html?id=' + id;
                tytTool.openWinAuto(_url,'viewpage_' + id,1200,600)
            })
            $('.xiajia').click(function () {
                lo.showModal($(this).attr('id'),$(this).attr('postStatus'));
            })
            $('.shangjia').click(function () {
                var id = $(this).attr('id');
                // window.location.href = './carSaleEdit.html?id=' + id + '&pageType=bar';
                var _url = './carSaleEdit.html?id=' + id + '&pageType=head'
                tytTool.openWinAuto(_url,'editpage_' + id,1200,600)
            })
        },
        openNewPage:function (_type) {
            var _url = './carSaleEdit.html?pageType=' + _type
            tytTool.openWinAuto(_url,'editpage_' + _type,1200,600)
        },
        closeRefundModal:function () {
            $(".auditSeclct").val('')
            $('#confirmRefundModal').hide();
        },
        selectBrancd:function(_this){
            $('#headBrand').val(_this.html())
            $('.brand_ul').hide();
        },
        //去掉汉字
        RemoveChinese:function (strValue) {
            if(strValue!= null && strValue != ""){
                var reg = /[\u4e00-\u9fa5]/g;
                return strValue.replace(reg, "");
            }
            else
                return "";
        },
        exportData:function () {
            var params = tytTool.htmlGetTname($(".searchUl"));
            params.contentType = 'application/x-www-form-urlencoded',
            window.open(tytTool.getRootPath_web() + '/used/car/sale/headListExcel?'+ tytTool.parseParam(params));
        }
    }
}

var lm = list.myData;
var la = list.arr;
var lj = list.ajx;
var lo = list.opt;
lo.loadList()
$('.confirm').click(function(){
    var id = $('.tip').attr('id');
    lo.xiajiaFun(id);
})
