/*
    userCommon.js for model/html/user/*.hmtl
    author: hey<PERSON><PERSON>
*/
var driverUrl = tytTool.getRootPath_web();
var common = {
    myData: {
        plat: {//查询项配置-总用户信息管理
            box: '.searchUl=>li',
            list: [

                {
                    label: '司机姓名：',
                    attr: ['tname##driverShowName', 'maxlength##50'],
                },
                {
                    label: '司机电话：',
                    attr: ['tname##driverPhone', 'maxlength##50'],
                },
                {
                    label: '司机ID：',
                    attr: ['tname##driverId', 'maxlength##50'],
                },
                {
                    label: '车主账号：',
                    attr: ['tname##userPhone', 'maxlength##50'],
                },
                {
                    label: '车主姓名：',
                    attr: ['tname##userShowName', 'maxlength##50'],
                },
                {
                    label: '审核结果：##select',
                    attr: ['tname##examineStatus', 'maxlength##50'],
                    option: [
                        '全部##""',
                        '待审核##1',
                        '审核通过##2',
                        '审核失败##3'
                    ]
                },
                {
                    label: '审核人：##select',
                    attr: ['tname##examineUserId','id##examineUserId', 'maxlength##50'],
                    option: [
                        '全部##""',
                    ]
                    // attr: ['tname##', 'maxlength##50'],
                    // option: [
                    //     '全部##',
                    //     '##',

                    // ]
                },
                {
                    label: '审核起始日期：',
                    attr: ['tname##examineStartTime', 'id##timeStart0', 'readonly##readonly', 'onclick##comO.timeStart(\'timeEnd0\')', 'data-width##add'],
                },
                {
                    label: '审核结束日期：',
                    attr: ['tname##examineEndTime', 'id##timeEnd0', 'readonly##readonly', 'onclick##comO.timeEnd(\'timeStart0\')', 'data-width##add'],
                },
                {
                    label: '提交审核起始日期：',
                    attr: ['tname##commitStartTime', 'id##timeStart1', 'readonly##readonly', 'onclick##comO.timeStart(\'timeEnd1\')', 'data-width##add'],
                },
                {
                    label: '提交审核结束日期：',
                    attr: ['tname##commitEndTime', 'id##timeEnd1', 'readonly##readonly', 'onclick##comO.timeEnd(\'timeStart1\')', 'data-width##add'],
                },
            ]
        },
    },
    arr: {},
    ajx: {},
    opt: {

        timeStart: function (timeEnd) { //选择日期控件
            return WdatePicker({
                readOnly: true,
                dateFmt: 'yyyy-MM-dd',
                // maxDate: '#F{$dp.$D(\'timeEnd0\')}'
                maxDate: `#F{$dp.$D('${timeEnd}')}`
            })
        },
        timeEnd: function (timeStart) {
            return WdatePicker({
                readOnly: true,
                dateFmt: 'yyyy-MM-dd',
                // minDate: '#F{$dp.$D(\'timeStart0\')}',
                // startDate: '#F{$dp.$D(\'timeStart0\')}'
                minDate: `#F{$dp.$D('${timeStart}')}`,
                startDate: `#F{$dp.$D('${timeStart}')}`
            })
        },

    }
}

var comM = common.myData;
var comA = common.arr;
var comJ = common.ajx;
var comO = common.opt;

/*
    userInfoAll.js for userInfoAll.hmtl
    author: syl
*/
var driver = {
    myData: {
        pageSize: 20,
    },
    arr: {
        getList: {
            url: '',
            otherUrl: driverUrl + "/car/driver/list",
            type: 'get',
            otherData: '',
            data: '',
            contentType: 'application/json'
        },
        getExamineListAjax: {
            url: '',
            otherUrl: driverUrl + "/car/driver/examineEmployee",
            type: 'get',
            otherData: '',
            data: '',
            contentType: 'application/json'
        },
    },
    ajx: {
        listData: function (data, _currentPage) {
            if (data.code == "200") {
                var data_list = data.data.list;

                var count = Math.abs(data.data.rowCount);
                for (var i = 0; i < data_list.length; i++) {
                    data_list[i].commitTime = tytTool.timeTrans(data_list[i].commitTime, { timeOpt: ['Y','M','D','h','m','s'], splitStr: "-" })
                    data_list[i].examineTime = tytTool.timeTrans(data_list[i].examineTime, { timeOpt: ['Y','M','D','h','m','s'], splitStr: "-" })
                }
                var currentPage = Number(_currentPage),
                    maxPage = Math.ceil(Number(count) / driverM.pageSize),
                    rowCount = Number(count);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams, 'driverO.listOpt')//
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams, 'driverO.lilass="info_opt"stOpt')
                }
                //正式载入数据
                var rule = {
                    list: [
                        'ID##id',
                        '司机姓名##driverShowName',
                        '司机电话##driverPhone##Fn::driverO.phoneShowFn({{id}},"driverPhone")',
                        '车主账号##userPhone##Fn::driverO.phoneShowFn({{id}},"userPhone")',
                        '车主姓名##userShowName',
                        '提交时间##commitTime',
                        '审核时间##examineTime',
                        '审核人##examineUserName',
                        '审核结果##examineStatus##Fn::driverO.examineStatusFn({{examineStatus}})',
                        '操作width100##examineStatus##Fn::driverO.opeartionFn({{examineStatus}},{{id}})',
                    ],
                    item: {
                        // driverPhone: {
                        //     attr: ['class==phoneHide']
                        // },
                        // userPhone: {
                        //     attr: ['class==phoneHide']
                        // },
                    },
                    opt: {
                        dom: `
                        <a class="info_opt" onclick="driverO.verify({{id}})">审核</a>
                        `
                    }
                }
                var tmp = tytTool.eachList(data_list, rule)
                $("#platList").empty().append(tmp.str)
                tytTool.colResizable($("#platList"))
                // tytTool.bindHidePhone();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
        getExamineListBack: function (data) {
            if (data.code == "200") {
                data.data.forEach(function (e) {
                    var str = e.realName + "##" + e.id;
                    comM.plat.list[6].option.push(str)
                })
                tytTool.loadCheckTab(comM.plat, {})  //搜索条
                $(".searchBox").css("display", "none")
                $(".searchBox input[data-width]").each(function () {
                    $(this).parent().find("label").css("width", "127px")
                    $(this).parent().css("width", "225px")
                })
                $(".searchBox").css("display", "block")
                driverO.loadList();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        }

    },
    opt: {
        opeartionFn: function (status, id) {
            var html = ""
            if (status == "1") {
                html = '<a class="info_opt" onclick="driverO.verify(\'' + id + '\')">审核</a>'
            } else {
                html = '<a class="info_opt" onclick="driverO.verify(\'' + id + '\')">详情</a>'
            }
            return html;
        },
        phoneShowFn: function(id,name){
            var html = `<span class="info_opt blue" onclick="phoneSoHfn($(this),${id},'司机审核管理','special',\'${name}\')">查看电话</span>`
            
            return html;
        },
        examineStatusFn: function (status) {
            var html = ""
            if (status == "1") {
                html = "<span>待审核</span>"
            } else if (status == "2") {
                html = "<span>审核通过</span>"
            } else if (status == "3") {
                html = "<span>审核失败</span>"
            }
            return html;
        },
        loadList: function () {
            var pageArr = {
                "totalPages": 1,
                "pageSize": driverM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": driverO.listOpt    //促发函数
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
            
        },
        getExamineList: function () {
            if (comM.plat.list[6].option.length <= 1) {
                tytTool.ajaxFn(driverA.getExamineListAjax, driverJ.getExamineListBack)   //ajax请求函数
            }
        },
        listOpt: function (currentPage) {   //
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            driverA.getList.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            driverA.getList.data = $.extend({}, driverM.common, arr, {
                "currentPage": currentPage,
                "pageSize": driverM.pageSize,
            })

            tytTool.ajaxFn(driverA.getList, driverJ.listData)   //ajax请求函数

        },
        verify: function (id) {
            tytTool.openWinAuto("driverDetail.html?id=" + id, "pageName_" + id, 1200, 600)
        }
    }
}

var driverM = driver.myData;
var driverA = driver.arr;
var driverJ = driver.ajx;
var driverO = driver.opt;

driverO.getExamineList();
$(window).resize(function () {
    tytTool.colResizable($("#platList"))
    tytTool.colResizableExtend()
})