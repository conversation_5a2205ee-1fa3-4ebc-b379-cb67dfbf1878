var market = tytTool.getRootPath_web('owers_boss');

var list = {
    myData: {
        pageSize: 20,
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
            contentType: 'application/json; charset=utf-8',
        },
        commonEx: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
            contentType: 'application/x-www-form-urlencoded',
        }
    },
    ajx: {
        listData: function(data, _currentPage) {
            if (data.code == 200) {
                var str = '';
                tytTool.pageFun({
                    total: data.data ? data.data.pageBean.rowCount : 0,
                    size:  data.data.pageBean.pageSize,
                    current: _currentPage,
                    pageCallback: 'listO.listOpt'
                })
                data = data.data.subList
                if(data.length){
                    for(var i=0;i<data.length;i++){
                        //支付宝23，微信22，银联25
                        var payTypeTxt = '';
                        if(data[i].payType === 22){
                            payTypeTxt = '微信';
                        }else if(data[i].payType === 23){
                            payTypeTxt = '支付宝';
                        }else if(data[i].payType === 25){
                            payTypeTxt = '银联';
                        }
                        str += `<tr>\
                            <td>${tytTool.NullObj(data[i].id)}</td>\
                            <td>${tytTool.NullObj(data[i].number)}</td>\
                            <td class='blue' onclick="phoneSoHfn($(this),'${data[i].cellPhone}','保险-订单流水')">查看电话</td>\
                            <td>${tytTool.timeNull(data[i].ctime,"yyyy-MM-dd hh:mm:ss")}</td>\
                            <td>${tytTool.NullObj(data[i].currency)/100}</td>\
                            <td>${tytTool.NullObj(data[i].retreatType === 1?'保险购买':'保险退回')}</td>\
                            <td>${tytTool.NullObj(payTypeTxt)}</td>\
                            <td>${tytTool.NullObj(data[i].company)}</td>\
                        </tr>`
                    }
                }else{
                    str = '<tr><td colspan="1000"><div>暂无查询数据</div></td></tr>';
                }
                $('.tbody').html(str)
                // tytTool.bindHidePhone();
            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        }
    },
    opt: {
        loadList: function () {
            var pageArr = {
                "totalPages": 1,
                "pageSize": listM.pageSize,
                "currentPage": 1,
                "funName": listO.listOpt,
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function (currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            listA.common.otherUrl = `${market}/pingan/insurance/getSubList?currentPage=${currentPage}&pageSize=${listM.pageSize}&ssoInfo=${tytTool.getCookie('ssoInfo')}`
            listA.common.otherData = currentPage; 
            var arr = tytTool.htmlGetTname($(".searchUl"));
            listA.common.data = JSON.stringify(arr);
            tytTool.ajaxFn(listA.common, listJ.listData)
        },
        //导出
        exportList: function () { //经销商订单导出入口
            var arr = tytTool.htmlGetTname($(".searchUl"));
            if(arr.startTime == ''){
                delete arr.startTime;
            }
            if(arr.endCtime == ''){
                delete arr.endCtime;
            }
            console.log(arr)
            tytTool.openPostWindow(`${market}/pingan/insurance/subExcelExport?ssoInfo=${tytTool.getCookie('ssoInfo')}`,$.extend({}, listA.commonEx ,arr))
        }
    }
}

var listM = list.myData;
var listA = list.arr;
var listJ = list.ajx;
var listO = list.opt;

//加载数据列表
listO.loadList();