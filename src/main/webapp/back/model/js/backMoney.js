/*
	backMoney.js
*/
var pageSize = 20;
var back = {
	myData: {
		sendUpdate: [],
		pageNo: 1
	},
	arr: {
		common: {
			url: '',
			otherUrl: '',
			otherData: '',
			type: 'post',
			contentType: 'application/json; charset=utf-8',
			data: ''
		}
	},
	ajx: {
		listData: function(data) {
			if (data.code == 200) {
				$("#backMoney tr:not('.tableTh')").remove();
				var _data = data.data,
					data_list = _data.list,
					jurisdiction_list = _data.menus;
				var arrParams = {
					currentPage: _data.pageNo || 1,
					totalPages: _data.maxPage || 1
				}
				if (tytTool.NullObj(data_list) != '' && data_list.length > 0) {
					$("#totalPages").html(_data.maxPage);
					$("#currentPage").html(_data.pageNo);
					//默认记录下当前页面:
					bo.pageNo = _data.pageNo;
					$("#rowCount").html(_data.rowCount);
					tytTool.jqPaginatorExtend(arrParams, 'bo.listMsg')
				} else {
					$("#totalPages").html(0);
					$("#currentPage").html("");
					$("#rowCount").html(0);
					tytTool.jqPaginatorExtend(arrParams, 'bo.listMsg')
				}
				//"用户导出"权限
				var _all = '',
					_colspan = 11;
				if (jurisdiction_list && jurisdiction_list.length > 0) {
					$(".backCheck,.backDelAll,.mdDown,.backLoad,.exDown").remove();
					$.each(jurisdiction_list, function(index, el) {
						var _inp = '';
						var _name = jurisdiction_list[index].jurisdictionName;
						if (_name == '查询') {
							_inp = `<li class="backCheck">
	              <input type="button" onclick="bo.loadList()" value="${_name}">
	            </li>`;
						} else if (_name == '批量返现') {
							_inp = `<li class="backDelAll">
							<input type="button" onclick="bo.updateReturnMoeny($(this),'approveAll')" value="${_name}">
	            </li>`;
							_colspan = 12;
							_all = 'approveAll';
						} else if (_name == '模板下载') {
							_inp = `<li class="mdDown">
	              <input type="button" onclick="bo.excelExportModel()" value="${_name}">
	            </li>`;
						} else if (_name == '导入') {
							_inp = `<li class="backLoad">
	              <input type="button" onclick="bo.commonClick('excelShow')" value="${_name}">
	            </li>`;
						}else if (_name == '导出') {
                            _inp = `<li class="exDown">
	              <input type="button" onclick="bo.excelExport()" value="${_name}">
	            </li>`;
                        }
						$("#btnBox").append(_inp);
					});
				}

				if (tytTool.NullObj(data_list) && tytTool.NullObj(data_list).length != 0) {
					//正式载入数据
					var _html = '',
						_vipStatus = '',
						_backOpt = '',
						_td = '',
					    _walletType = '';
					$.each(data_list, function(index, el) {
						var ind = data_list[index];
						switch (ind.status) {
							case 1:
								_vipStatus = '未返现';
								_backOpt = `<a class="info_detail ver_block" onclick="bo.updateReturnMoeny($(this),'approve')">批准返现</a>
														<span class="ver_block ver_span">|</span>
														<a class="info_detail ver_block" onclick="bo.updateReturnMoeny($(this),'refuse')">拒绝返现</a>`
								break;
							case 2:
								_vipStatus = '已返现';
								_backOpt = '';
								break;
							case 3:
								_vipStatus = '已拒绝';
								_backOpt = '';
								break;
							default:
								_vipStatus = '';
								_backOpt = '';
								break;
						}

						switch (ind.walletType) {
							case 1:
								_walletType = '营销钱包';
								break;
							case 2:
								_walletType = '保障金专用钱包';
								break;
							default:
								_walletType = '';
								break;
						}

						if (_all == 'approveAll') {
							$("#chooseAll").show()
							if (ind.status == 1) {
								_td = `<td><input class="checkBack" type="checkbox"></td>`;
							} else {
								_td = '<td></td>';
							}
						} else {
							$("#chooseAll").hide()
						}
						//<td><input type="checkbox"></td>


						_html += `<tr>	
						<td tname="id" style="display:none;">${tytTool.NullObj(ind.id)}</td>
						${_td}
						<td tname="userId">${tytTool.NullObj(ind.userId)}</td>
						<td>${tytTool.NullObj(ind.userName)}</td>			
						<td>${tytTool.NullObj(ind.buyGoods)}</td>		
						<td>${tytTool.NullObj(ind.activityName)}</td>		
						<td tname="returnMoney">${tytTool.NullObj(ind.returnMoney)}</td>
						<td>${_vipStatus}</td>		
						<td>${tytTool.NullObj(ind.importName)}</td>		
						<td>${tytTool.NullObj(ind.operatorName)}</td>		
						<td>${tytTool.timeNull(ind.buyTime,'yyyy-MM-dd')}</td>
						<td>${tytTool.timeNull(ind.operatorTime,'yyyy-MM-dd hh:mm:ss')}</td>
						<td>${_walletType}</td>
						<td id="opt_${ind.id}">${_backOpt}</td>
					</tr>`;
					});
					$("#backMoney").append(_html);
					// console.log(tytTool.sortNoRepeat(yearBox)) //对年份去重排序处理
					if($("#backMoney .checkBack").length == 0) {
						$(".checkAllInp").attr('disabled',true)
					} else {
						$(".checkAllInp").removeAttr('disabled')
					}

					tytTool.bindHidePhone()
				} else {
					$("#backMoney").append(`<tr class="noCompanyCar"><td colspan="${_colspan}">无匹配用户</td></tr>`);
				}
				tytTool.colResizable($(".tableBox"))
			}
			if (data.code == 500) {
				tytTool.toastShow(data.msg, 3000)
			}
		},
		//修改用户返现记录返回结果
		updateData: function(data) {
			var userIds = data.data;

			if (data.code == 200) {
				tytTool.toastShow(data.msg, 5000)
				setTimeout(function() {
					tytTool.reloadWin()
				}, 1000)
			} else {
                var _userIds = '';
                $.each(userIds, function(index, el) {
                	var userId = userIds[index];
                    _userIds += userId + ",";
				});
                tytTool.toastShow("用户ID为："+_userIds+data.msg, 5000)
                setTimeout(function() {
                    tytTool.reloadWin()
                }, 3000)
			}
		}
	},
	opt: {
		loadList: function() {
			var arr = {
				"totalPages": 1,
				"pageSize": pageSize,
				"visiblePages": 7,
				"currentPage": 1,
				"funName": bo.listMsg
			}
			tytTool.jqPaginator('#pageNumberBox', arr);
		},
		listMsg: function(pageNo) {
			pageNo == "" ? pageNo = 1 : pageNo = pageNo;
			br.common.otherUrl = `${tytTool.getRootPath_web()}/userRerurnMoney/getList?currentPage=${pageNo}&pageSize=${pageSize}&menuId=${tytTool.parseSearch().menuId}`
			var arr = tytTool.htmlGetTname($("#searchMoney"));
			br.common.data = JSON.stringify(arr)
			tytTool.ajaxFn(br.common, ba.listData)
		},
		checkAllInp:function(e,_this) {
			e.stopPropagation();
			if(_this.attr('checked')) {
				_this.attr('checked')
				$("#backMoney .checkBack").attr('checked','checked')
				return false;
			} else {
				_this.removeAttr('checked')
				$("#backMoney .checkBack").removeAttr('checked');
				return false;
			}
		},
		updateReturnMoeny: function(_this, _type) {
			bm.status = 0;
			bm.sendUpdate = [];
			var arr = '';
			if (_type == 'approveAll') { //批量批准
				bm.status = 2;
				$("#backMoney .checkBack").each(function(index, el) {
					if ($(this).is(':checked')) {
						arr = tytTool.htmlGetTname($(this).parents('tr'));
						arr.userId = Number(arr.userId);
						bm.sendUpdate.push(arr)
					}
				});
				if (bm.sendUpdate.length == 0) {
					tytTool.toastShow('请选择要批量处理的用户', 3000);
					return false;
				}
				var _num = 0,
					_noNumber = 0;
				$.each(bm.sendUpdate, function(index, el) {
					var ind = Number(bm.sendUpdate[index].returnMoney);
					if (isNaN(ind)) {
						_noNumber = 1;
					}
					_num += ind;
				});
				if (_noNumber == 1) {
					tytTool.toastShow('所选用户中返现金额有不规则字段', 3000);
					return false;
				}
				$("#backMoneyPeople").html(`返现批准共计：${bm.sendUpdate.length}人`);
				$("#backMoneyNumber").html(`返现金额共计：${_num}元`);
				bo.commonClick('backShow');
			} else { //单个
				arr = tytTool.htmlGetTname(_this.parents('tr'));
				arr.userId = Number(arr.userId);
				bm.sendUpdate.push(arr)
				if (_type == 'approve') { //批准
					bm.status = 2;
				} else if (_type == 'refuse') { //拒绝
					bm.status = 3;
				}
				bo.updateDataFn();
			}
		},
		updateDataFn: function() {
			br.common.otherUrl = `${tytTool.getRootPath_web()}/userRerurnMoney/updateReturnMoeny?status=${bm.status}`;
			br.common.data = JSON.stringify(bm.sendUpdate)
			tytTool.ajaxFn(br.common, ba.updateData)
		},
		excelExportModel: function() {
			bo.openPostWindow(`${tytTool.getRootPath_web()}/userRerurnMoney/excelExportModel`, '');
		},
        excelExport: function() {
			var params = tytTool.htmlGetTname($(".searchUl"));
			if(params.status == '') {
				delete params.status
			}
			bo.openPostWindow(`${tytTool.getRootPath_web()}/userRerurnMoney/excelExport`, params);
        },
		openPostWindow: function(url, params) {
			var newWin = window.open(),
				formStr = '';
			//设置样式为隐藏，打开新标签再跳转页面前，如果有可现实的表单选项，用户会看到表单内容数据
			var allInp = '';
			for (var val in params) {
				allInp +=`<input type="hidden" name="${val}" value="${decodeURI(params[val])}" />`
			}
			formStr = `<form style="visibility:hidden;" method="POST" action="${url}">${allInp}</form>`;
			newWin.document.body.innerHTML = formStr;
			newWin.document.head.innerHTML = '<meta charset="UTF-8">'
			newWin.document.forms[0].submit();
			setTimeout(function() {
				newWin.close()
			}, 3000);
			return newWin;
		},
		uploadFile: function() {
			if ($(".excelBox h2").html() == "请选择文件") {
				alert("请选择导入文件");
				return;
			}
			$(".outBgColor,.excelBox").hide();
			var formData = new FormData();
			formData.append("fileField", $("#fileField")[0].files[0]);
			$.ajax({
				type: "POST",
				url: tytTool.getRootPath_web() + "/userRerurnMoney/importList",
				data: formData,
				contentType: false,
				/**
				 * 必须false才会避开jQuery对 formdata 的默认处理
				 * XMLHttpRequest会对 formdata 进行正确的处理
				 */
				processData: false,
				async: false,
				dataType: "json",
				success: function(data) {
					if (data.code == 200) {
						tytTool.showToast(data.msg, '');
						setTimeout(function() {
							tytTool.reloadWin();
						}, 1000)
					} else {
						tytTool.showToast(data.msg, '');
						$(".excelBox").hide();
						$(".excelBox .alertContent").html('请点击“浏览”选择需要导入的模板文件');
						$(".excelBox h2").html("请选择文件");
						$("#fileField").attr("value", "");
					}
				},
				error: function(xhr, status) {
					tytTool.showToast('导入文件格式不正确', '');
				}
			});
		},
		fileInfo: function(source, _this) {
			var f = source.files[0];
			var name = f.name;
			$(".excelBox .alertContent").html(name);
			$(".excelBox h2").html("请上传");
		},
		loadMaintain: function() { //加载修改对象
			br.commonLoad.otherUrl = `${tytTool.getRootPath_web()}/maintainedCustom/getCwEmployee`;
			tytTool.ajaxFn(br.commonLoad, ba.maintainData)
		},
		buyTimeStart: function() { //选择日期控件
			return WdatePicker({
				maxDate: '#F{$dp.$D(\'buyTimeEnd\')||\'%y-%M-%d\'}',
				dateFmt: 'yyyy-MM-dd'
			});
		},
		buyTimeEnd: function() {
			return WdatePicker({
				minDate: '#F{$dp.$D(\'buyTimeStart\')}',
				maxDate: '#F{\'%y-%M-%d\'}',
				dateFmt: 'yyyy-MM-dd'
			});
		},
        operatorTimeStart: function() { //选择日期控件
            return WdatePicker({
                maxDate: '#F{$dp.$D(\'operatorTimeEnd\')||\'%y-%M-%d\'}',
                dateFmt: 'yyyy-MM-dd'
            });
        },
        operatorTimeEnd: function() {
            return WdatePicker({
                minDate: '#F{$dp.$D(\'operatorTimeStart\')}',
                maxDate: '#F{\'%y-%M-%d\'}',
                dateFmt: 'yyyy-MM-dd'
            });
        },
		commonClick: function(optName) {
			if (optName == 'excelShow') { //显示Excel上传框
				$(".outBgColor").show();
				$(".excelBox").fadeIn(400);
			} else if (optName == 'excelHide') { //隐藏Excel上传框
				$(".outBgColor").hide();
				$(".excelBox").fadeOut(400);
			} else if (optName == 'backShow') {
				$(".outBgColor").show();
				$(".backMoneyLook").fadeIn(400);
			} else if (optName == 'backHide') {
				$(".outBgColor").hide();
				$(".backMoneyLook").fadeOut(400);
			}
			if (optName == 'excelShow' || optName == 'excelHide') {
				$(".excelBox .alertContent").html('请点击“浏览”选择需要导入的模板文件');
				$(".excelBox h2").html("请选择文件");
				$("#fileField").attr("value", "");
			}
		}
	}
}

var bm = back.myData;
var br = back.arr;
var ba = back.ajx;
var bo = back.opt;

bo.loadList()
	// $(".outBgColor,.excelBox").show();

$(document).on("click","#backMoney .checkBack",function(){
	var Len = $("#backMoney .checkBack").length,
	checkLen = 0;
	$("#backMoney .checkBack").each(function(index,ele) {
		if($(this).attr('checked')) {
			checkLen += 1;
		}
	})
	// console.log(checkLen)
	if(Len == checkLen) {
		$(".checkAllInp").attr('checked','checked')
	} else {
		$(".checkAllInp").removeAttr('checked')
	}
})