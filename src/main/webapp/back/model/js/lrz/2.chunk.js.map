{"version": 3, "sources": ["webpack:///2.chunk.js", "webpack:///src/lib/jpeg_encoder_basic.js"], "names": ["webpackJsonp", 7, "module", "exports", "JPEGEncoder", "l", "M", "ag", "af", "ae", "aj", "k", "O", "i", "ah", "ad", "ai", "K", "ac", "ab", "ak", "aa", "d", "Z", "q", "Array", "W", "u", "g", "c", "h", "v", "Y", "G", "w", "E", "T", "J", "B", "z", "m", "n", "V", "C", "X", "t", "a", "F", "j", "push", "e", "p", "N", "aZ", "ap", "aL", "aK", "aJ", "aI", "aH", "aD", "aC", "aB", "aR", "aN", "aq", "aY", "aO", "aX", "aP", "aU", "aQ", "aT", "aS", "an", "am", "al", "ax", "at", "aw", "au", "av", "ar", "az", "aG", "aW", "ao", "aV", "ay", "aF", "aM", "a1", "a0", "aA", "aE", "P", "b", "r", "D", "H", "I", "L", "S", "y", "String", "fromCharCode", "R", "x", "Math", "floor", "Q", "Date", "getTime", "round", "A", "f", "U", "this", "encode", "width", "height", "displayName", "data", "length", "Uint8Array", "charCodeAt", "btoa", "join"], "mappings": "AAAAA,cAAc,IAERC,EACA,SAASC,EAAQC,GCHvB,QAAAC,GAAAC,GAmCA,QAAAC,GAAAC,GAEA,OADAC,IAAA,4MACAC,EAAA,EAAwB,GAAAA,EAASA,IAAA,CACjC,GAAAC,GAAAC,GAAAH,EAAAC,GAAAF,EAAA,QACA,GAAAG,EACAA,EAAA,EAEAA,EAAA,MACAA,EAAA,KAGAE,EAAAC,EAAAJ,IAAAC,EAGA,OADAI,IAAA,iMACAC,EAAA,EAAwB,GAAAA,EAASA,IAAA,CACjC,GAAAC,GAAAL,GAAAG,EAAAC,GAAAR,EAAA,QACA,GAAAS,EACAA,EAAA,EAEAA,EAAA,MACAA,EAAA,KAGAC,EAAAJ,EAAAE,IAAAC,EAIA,OAFAE,IAAA,wEACAC,EAAA,EACAC,EAAA,EAAwB,EAAAA,EAAQA,IAChC,OAAAC,GAAA,EAA4B,EAAAA,EAAQA,IACpCC,EAAAH,GAAA,GAAAP,EAAAC,EAAAM,IAAAD,EAAAE,GAAAF,EAAAG,GAAA,GACAE,EAAAJ,GAAA,GAAAF,EAAAJ,EAAAM,IAAAD,EAAAE,GAAAF,EAAAG,GAAA,GACAF,IAKA,QAAAK,GAAAf,EAAAY,GAIA,OAHAN,GAAA,EACAR,EAAA,EACAC,EAAA,GAAAiB,OACAN,EAAA,EAAwB,IAAAA,EAAUA,IAAA,CAClC,OAAAD,GAAA,EAA4BA,GAAAT,EAAAU,GAAcD,IAC1CV,EAAAa,EAAAd,OACAC,EAAAa,EAAAd,IAAA,GAAAQ,EACAP,EAAAa,EAAAd,IAAA,GAAAY,EACAZ,IACAQ,GAEAA,IAAA,EAEA,MAAAP,GAGA,QAAAkB,KACAC,EAAAH,EAAAI,EAAAC,GACAC,EAAAN,EAAAO,EAAAC,GACAC,EAAAT,EAAAU,EAAAC,GACAC,EAAAZ,EAAAa,EAAAC,GAGA,QAAAC,KAGA,OAFArB,GAAA,EACAC,EAAA,EACAE,EAAA,EAAwB,IAAAA,EAAUA,IAAA,CAClC,OAAAN,GAAAG,EAA6BC,EAAAJ,EAASA,IACtCyB,EAAA,MAAAzB,GAAAM,EACAoB,EAAA,MAAA1B,MACA0B,EAAA,MAAA1B,GAAA,GAAAM,EACAoB,EAAA,MAAA1B,GAAA,GAAAA,CAEA,QAAAN,KAAAU,EAAA,IAAoCD,GAAAT,EAAWA,IAC/C+B,EAAA,MAAA/B,GAAAY,EACAoB,EAAA,MAAAhC,MACAgC,EAAA,MAAAhC,GAAA,GAAAY,EACAoB,EAAA,MAAAhC,GAAA,GAAAU,EAAA,EAAAV,CAEAS,KAAA,EACAC,IAAA,GAIA,QAAAuB,KACA,OAAArB,GAAA,EAAwB,IAAAA,EAAUA,IAClCsB,EAAAtB,GAAA,MAAAA,EACAsB,EAAAtB,EAAA,cAAAA,EACAsB,EAAAtB,EAAA,aAAAA,EAAA,MACAsB,EAAAtB,EAAA,eAAAA,EACAsB,EAAAtB,EAAA,gBAAAA,EACAsB,EAAAtB,EAAA,eAAAA,EAAA,QACAsB,EAAAtB,EAAA,gBAAAA,EACAsB,EAAAtB,EAAA,eAAAA,EAIA,QAAAuB,GAAAvB,GAGA,IAFA,GAAAH,GAAAG,EAAA,GACAF,EAAAE,EAAA,KACAF,GAAA,GACAD,EAAA,GAAAC,IACA0B,GAAA,GAAAC,GAEA3B,IACA2B,IACA,EAAAA,IACA,KAAAD,GACAE,EAAA,KACAA,EAAA,IAEAA,EAAAF,GAEAC,EAAA,EACAD,EAAA,GAKA,QAAAE,GAAA1B,GACA2B,EAAAC,KAAAC,EAAA7B,IAGA,QAAA8B,GAAA9B,GACA0B,EAAA1B,GAAA,OACA0B,EAAA,OAGA,QAAAK,GAAAC,EAAAC,GACA,GAAAC,GAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAEAC,EADAC,EAAA,CAEA,MAAAC,GAAA,EACAjD,EAAA,EACA,KAAA+C,EAAA,EAAoBE,EAAAF,IAASA,EAAA,CAC7BR,EAAAF,EAAAW,GACAR,EAAAH,EAAAW,EAAA,GACAP,EAAAJ,EAAAW,EAAA,GACAN,EAAAL,EAAAW,EAAA,GACAL,EAAAN,EAAAW,EAAA,GACAJ,EAAAP,EAAAW,EAAA,GACAH,EAAAR,EAAAW,EAAA,GACAF,EAAAT,EAAAW,EAAA,EACA,IAAAE,GAAAX,EAAAO,EACAK,EAAAZ,EAAAO,EACAM,EAAAZ,EAAAK,EACAQ,EAAAb,EAAAK,EACAS,EAAAb,EAAAG,EACAW,EAAAd,EAAAG,EACAY,EAAAd,EAAAC,EACAc,EAAAf,EAAAC,EACAe,EAAAR,EAAAM,EACApD,EAAA8C,EAAAM,EACAG,EAAAP,EAAAE,EACAM,EAAAR,EAAAE,CACAjB,GAAAW,GAAAU,EAAAC,EACAtB,EAAAW,EAAA,GAAAU,EAAAC,CACA,IAAAE,GAAA,YAAAD,EAAAxD,EACAiC,GAAAW,EAAA,GAAA5C,EAAAyD,EACAxB,EAAAW,EAAA,GAAA5C,EAAAyD,EACAH,EAAAD,EAAAF,EACAI,EAAAJ,EAAAF,EACAO,EAAAP,EAAAF,CACA,IAAAW,GAAA,YAAAJ,EAAAE,GACAG,EAAA,SAAAL,EAAAI,EACAE,EAAA,YAAAJ,EAAAE,EACAG,EAAA,WAAAN,EACA7D,EAAAqD,EAAAc,EACA1E,EAAA4D,EAAAc,CACA5B,GAAAW,EAAA,GAAAzD,EAAAwE,EACA1B,EAAAW,EAAA,GAAAzD,EAAAwE,EACA1B,EAAAW,EAAA,GAAAlD,EAAAkE,EACA3B,EAAAW,EAAA,GAAAlD,EAAAkE,EACAhB,GAAA,EAGA,IADAA,EAAA,EACAD,EAAA,EAAoBE,EAAAF,IAASA,EAAA,CAC7BR,EAAAF,EAAAW,GACAR,EAAAH,EAAAW,EAAA,GACAP,EAAAJ,EAAAW,EAAA,IACAN,EAAAL,EAAAW,EAAA,IACAL,EAAAN,EAAAW,EAAA,IACAJ,EAAAP,EAAAW,EAAA,IACAH,EAAAR,EAAAW,EAAA,IACAF,EAAAT,EAAAW,EAAA,GACA,IAAAkB,GAAA3B,EAAAO,EACApD,EAAA6C,EAAAO,EACAqB,EAAA3B,EAAAK,EACApD,EAAA+C,EAAAK,EACAuB,EAAA3B,EAAAG,EACA1C,EAAAuC,EAAAG,EACAyB,EAAA3B,EAAAC,EACAtC,EAAAqC,EAAAC,EACA2B,EAAAJ,EAAAG,EACAE,EAAAL,EAAAG,EACAG,EAAAL,EAAAC,EACAK,EAAAN,EAAAC,CACA/B,GAAAW,GAAAsB,EAAAE,EACAnC,EAAAW,EAAA,IAAAsB,EAAAE,CACA,IAAAhF,GAAA,YAAAiF,EAAAF,EACAlC,GAAAW,EAAA,IAAAuB,EAAA/E,EACA6C,EAAAW,EAAA,IAAAuB,EAAA/E,EACA8E,EAAAjE,EAAAH,EACAsE,EAAAtE,EAAAT,EACAgF,EAAAhF,EAAAC,CACA,IAAAgF,GAAA,YAAAJ,EAAAG,GACA1E,EAAA,SAAAuE,EAAAI,EACAC,EAAA,YAAAF,EAAAC,EACAvE,EAAA,WAAAqE,EACAI,EAAAlF,EAAAS,EACA0E,EAAAnF,EAAAS,CACAkC,GAAAW,EAAA,IAAA6B,EAAA9E,EACAsC,EAAAW,EAAA,IAAA6B,EAAA9E,EACAsC,EAAAW,EAAA,GAAA4B,EAAAD,EACAtC,EAAAW,EAAA,IAAA4B,EAAAD,EACA3B,IAEA,GAAA8B,EACA,KAAA/B,EAAA,EAAoB/C,EAAA+C,IAASA,EAC7B+B,EAAAzC,EAAAU,GAAAT,EAAAS,GACAgC,EAAAhC,GAAA+B,EAAA,EAAAA,EAAA,KAAAA,EAAA,IAEA,OAAAC,GAGA,QAAAC,KACA7C,EAAA,OACAA,EAAA,IACAJ,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,GACAA,EAAA,GACAA,EAAA,GACAA,EAAA,GACAI,EAAA,GACAA,EAAA,GACAJ,EAAA,GACAA,EAAA,GAGA,QAAAkD,GAAA5E,EAAAF,GACAgC,EAAA,OACAA,EAAA,IACAJ,EAAA,GACAI,EAAAhC,GACAgC,EAAA9B,GACA0B,EAAA,GACAA,EAAA,GACAA,EAAA,IACAA,EAAA,GACAA,EAAA,GACAA,EAAA,IACAA,EAAA,GACAA,EAAA,GACAA,EAAA,IACAA,EAAA,GAGA,QAAAmD,KACA/C,EAAA,OACAA,EAAA,KACAJ,EAAA,EACA,QAAA5B,GAAA,EAAwB,GAAAA,EAASA,IACjC4B,EAAAnC,EAAAO,GAEA4B,GAAA,EACA,QAAA1B,GAAA,EAAwB,GAAAA,EAASA,IACjC0B,EAAA9B,EAAAI,IAIA,QAAA8E,KACAhD,EAAA,OACAA,EAAA,KACAJ,EAAA,EACA,QAAAtC,GAAA,EAAwB,GAAAA,EAASA,IACjCsC,EAAAnB,EAAAnB,EAAA,GAEA,QAAAM,GAAA,EAAwB,IAAAA,EAAUA,IAClCgC,EAAAlB,EAAAd,GAEAgC,GAAA,GACA,QAAA7B,GAAA,EAAwB,GAAAA,EAASA,IACjC6B,EAAAb,EAAAhB,EAAA,GAEA,QAAAC,GAAA,EAAwB,KAAAA,EAAWA,IACnC4B,EAAAZ,EAAAhB,GAEA4B,GAAA,EACA,QAAA1B,GAAA,EAAwB,GAAAA,EAASA,IACjC0B,EAAAhB,EAAAV,EAAA,GAEA,QAAAP,GAAA,EAAwB,IAAAA,EAAUA,IAClCiC,EAAAf,EAAAlB,GAEAiC,GAAA,GACA,QAAAxC,GAAA,EAAwB,GAAAA,EAASA,IACjCwC,EAAAV,EAAA9B,EAAA,GAEA,QAAAC,GAAA,EAAwB,KAAAA,EAAWA,IACnCuC,EAAAT,EAAA9B,IAIA,QAAA4F,KACAjD,EAAA,OACAA,EAAA,IACAJ,EAAA,GACAA,EAAA,GACAA,EAAA,GACAA,EAAA,GACAA,EAAA,IACAA,EAAA,GACAA,EAAA,IACAA,EAAA,GACAA,EAAA,IACAA,EAAA,GAGA,QAAAsD,GAAAtF,EAAAM,EAAAuD,EAAAE,EAAAxB,GACA,GAEApC,GAFAX,EAAA+C,EAAA,GACAnC,EAAAmC,EAAA,IAEA,MAAA4B,GAAA,GACAlE,EAAA,GACAF,EAAA,EAEA,QADAmD,GAAAb,EAAArC,EAAAM,GACAsD,EAAA,EAAwB7D,EAAA6D,IAASA,EACjC2B,EAAAzF,EAAA8D,IAAAV,EAAAU,EAEA,IAAAD,GAAA4B,EAAA,GAAA1B,CACAA,GAAA0B,EAAA,GACA,GAAA5B,EACA9B,EAAAkC,EAAA,KAEA5D,EAAA,MAAAwD,EACA9B,EAAAkC,EAAAtC,EAAAtB,KACA0B,EAAAH,EAAAvB,IAGA,KADA,GAAAT,GAAA,GACcA,EAAA,MAAA6F,EAAA7F,GAA0BA,KAExC,MAAAA,EAEA,MADAmC,GAAArC,GACAqE,CAIA,KAFA,GACAI,GADAM,EAAA,EAEA7E,GAAA6E,GAAA,CAEA,IADA,GAAAlE,GAAAkE,EACkB,GAAAgB,EAAAhB,IAAA7E,GAAA6E,IAA4BA,GAE9C,GAAA5E,GAAA4E,EAAAlE,CACA,IAAAV,GAAAwE,EAAA,CACAF,EAAAtE,GAAA,CACA,QAAAF,GAAA,EAAgCwE,GAAAxE,IAAUA,EAC1CoC,EAAAzB,EAEAT,GAAA,GAAAA,EAEAQ,EAAA,MAAAoF,EAAAhB,GACA1C,EAAAU,GAAA5C,GAAA,GAAA8B,EAAAtB,KACA0B,EAAAH,EAAAvB,IACAoE,IAKA,MAHA7E,IAAAO,GACA4B,EAAArC,GAEAqE,EAGA,QAAA2B,KAEA,OADApF,GAAAqF,OAAAC,aACApF,EAAA,EAAwB,IAAAA,EAAUA,IAClC6B,EAAA7B,GAAAF,EAAAE,GAqFA,QAAAqF,GAAAvF,GAOA,GANA,GAAAA,IACAA,EAAA,GAEAA,EAAA,MACAA,EAAA,KAEAwF,GAAAxF,EAAA,CAGA,GAAAE,GAAA,CAEAA,GADA,GAAAF,EACAyF,KAAAC,MAAA,IAAA1F,GAEAyF,KAAAC,MAAA,MAAA1F,GAEAb,EAAAe,GACAsF,EAAAxF,GAGA,QAAA2F,KACA,GAAAzF,IAAA,GAAA0F,OAAAC,SACA3G,KACAA,EAAA,IAEAkG,IACA7E,IACAa,IACAG,IACAgE,EAAArG,IACA,GAAA0G,OAAAC,UAAA3F,EA1gBA,GAOAM,GACAG,EACAG,EACAG,EAaAuE,EArBAhG,GADAiG,KAAAK,MACAL,KAAAC,OACAjG,EAAA,GAAAa,OAAA,IACAR,EAAA,GAAAQ,OAAA,IACAH,EAAA,GAAAG,OAAA,IACAF,EAAA,GAAAE,OAAA,IAKAgB,EAAA,GAAAhB,OAAA,OACAe,EAAA,GAAAf,OAAA,OACAsE,EAAA,GAAAtE,OAAA,IACA6E,EAAA,GAAA7E,OAAA,IACAuB,KACAH,EAAA,EACAC,EAAA,EACAoE,EAAA,GAAAzF,OAAA,IACA0F,EAAA,GAAA1F,OAAA,IACA2F,EAAA,GAAA3F,OAAA,IACAyB,EAAA,GAAAzB,OAAA,KACAkB,EAAA,GAAAlB,OAAA,MAEAZ,GAAA,uLACAe,GAAA,mCACAC,GAAA,2BACAK,GAAA,qCACAC,GAAA,+jBACAJ,GAAA,mCACAC,GAAA,2BACAK,GAAA,qCACAC,GAAA,8jBA2XA+E,MAAAC,OAAA,SAAA5C,EAAAhE,EAAAoD,GACA,GAAAzC,IAAA,GAAA0F,OAAAC,SACAtG,IACAgG,EAAAhG,GAEAsC,EAAA,GAAAvB,OACAoB,EAAA,EACAC,EAAA,EACAK,EAAA,OACA6C,IACAE,IACAD,EAAAvB,EAAA6C,MAAA7C,EAAA8C,QACArB,IACAC,GACA,IAAAxB,GAAA,EACAX,EAAA,EACAqB,EAAA,CACAzC,GAAA,EACAC,EAAA,EACAuE,KAAAC,OAAAG,YAAA,UASA,KARA,GAKA3G,GACA6D,EAAAE,EAAAM,EACAhE,EAAAmC,EAAApC,EAAAV,EAAAC,EAPAqE,EAAAJ,EAAAgD,KACAxC,EAAAR,EAAA6C,MACA1B,EAAAnB,EAAA8C,OACAhC,EAAA,EAAAN,EAEA3E,EAAA,EAGAsF,EAAAtF,GAAA,CAEA,IADAO,EAAA,EACA0E,EAAA1E,GAAA,CAKA,IAJAK,EAAAqE,EAAAjF,EAAAO,EACAwC,EAAAnC,EACAD,EAAA,GACAV,EAAA,EACAC,EAAA,EAA4B,GAAAA,EAASA,IACrCD,EAAAC,GAAA,EACAS,EAAA,KAAAT,GACA6C,EAAAnC,EAAAX,EAAAgF,EAAAtE,EACAX,EAAAC,GAAAqF,IACAvC,GAAAkC,GAAAjF,EAAA,EAAAC,EAAAqF,IAEA/E,EAAAI,GAAAsE,IACAlC,GAAAxC,EAAAI,EAAAsE,EAAA,GAEAb,EAAAG,EAAAxB,KACAuB,EAAAC,EAAAxB,KACA6B,EAAAL,EAAAxB,KACA4D,EAAAzG,IAAAkC,EAAAgC,GAAAhC,EAAAkC,EAAA,QAAAlC,EAAAwC,EAAA,iBACAgC,EAAA1G,IAAAkC,EAAAgC,EAAA,QAAAhC,EAAAkC,EAAA,SAAAlC,EAAAwC,EAAA,kBACAiC,EAAA3G,IAAAkC,EAAAgC,EAAA,SAAAhC,EAAAkC,EAAA,SAAAlC,EAAAwC,EAAA,iBAEAP,GAAAyB,EAAAa,EAAA5F,EAAAsD,EAAAjD,EAAAM,GACAgC,EAAAoC,EAAAc,EAAA5F,EAAA0C,EAAAnC,EAAAM,GACAkD,EAAAe,EAAAe,EAAA7F,EAAA+D,EAAAxD,EAAAM,GACAtB,GAAA,GAEAP,GAAA,EAEA,GAAAuC,GAAA,GACA,GAAAiC,KACAA,GAAA,GAAAjC,EAAA,EACAiC,EAAA,OAAAjC,EAAA,KACAF,EAAAmC,GAGA,GADA5B,EAAA,OACAW,EAAA,CAGA,OAFAmB,GAAAjC,EAAA2E,OACA9D,EAAA,GAAA+D,YAAA3C,GACAD,EAAA,EAA4BC,EAAAD,EAASA,IACrCnB,EAAAmB,GAAAhC,EAAAgC,GAAA6C,YAEA7E,OACA,GAAA+D,OAAAC,UAAA3F,CACA,OAAAwC,GAEA,GAAA9C,GAAA,0BAAkC+G,KAAA9E,EAAA+E,KAAA,IAClC/E,OACA,GAAA+D,OAAAC,UAAA3F,CACA,OAAAN,IAmCA+F,IAGA5G,EAAAC,QAAAC", "file": "2.chunk.js", "sourcesContent": ["webpackJsonp([2],{\n\n/***/ 7:\n/***/ function(module, exports) {\n\n\tfunction JPEGEncoder (l) {\n\t    var o = this;\n\t    var s = Math.round;\n\t    var k = Math.floor;\n\t    var O = new Array(64);\n\t    var K = new Array(64);\n\t    var d = new Array(64);\n\t    var Z = new Array(64);\n\t    var u;\n\t    var h;\n\t    var G;\n\t    var T;\n\t    var n = new Array(65535);\n\t    var m = new Array(65535);\n\t    var P = new Array(64);\n\t    var S = new Array(64);\n\t    var j = [];\n\t    var t = 0;\n\t    var a = 7;\n\t    var A = new Array(64);\n\t    var f = new Array(64);\n\t    var U = new Array(64);\n\t    var e = new Array(256);\n\t    var C = new Array(2048);\n\t    var x;\n\t    var i = [0, 1, 5, 6, 14, 15, 27, 28, 2, 4, 7, 13, 16, 26, 29, 42, 3, 8, 12, 17, 25, 30, 41, 43, 9, 11, 18, 24, 31, 40, 44, 53, 10, 19, 23, 32, 39, 45, 52, 54, 20, 22, 33, 38, 46, 51, 55, 60, 21, 34, 37, 47, 50, 56, 59, 61, 35, 36, 48, 49, 57, 58, 62, 63];\n\t    var g = [0, 0, 1, 5, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0];\n\t    var c = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];\n\t    var w = [0, 0, 2, 1, 3, 3, 2, 4, 3, 5, 5, 4, 4, 0, 0, 1, 125];\n\t    var E = [1, 2, 3, 0, 4, 17, 5, 18, 33, 49, 65, 6, 19, 81, 97, 7, 34, 113, 20, 50, 129, 145, 161, 8, 35, 66, 177, 193, 21, 82, 209, 240, 36, 51, 98, 114, 130, 9, 10, 22, 23, 24, 25, 26, 37, 38, 39, 40, 41, 42, 52, 53, 54, 55, 56, 57, 58, 67, 68, 69, 70, 71, 72, 73, 74, 83, 84, 85, 86, 87, 88, 89, 90, 99, 100, 101, 102, 103, 104, 105, 106, 115, 116, 117, 118, 119, 120, 121, 122, 131, 132, 133, 134, 135, 136, 137, 138, 146, 147, 148, 149, 150, 151, 152, 153, 154, 162, 163, 164, 165, 166, 167, 168, 169, 170, 178, 179, 180, 181, 182, 183, 184, 185, 186, 194, 195, 196, 197, 198, 199, 200, 201, 202, 210, 211, 212, 213, 214, 215, 216, 217, 218, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250];\n\t    var v = [0, 0, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0];\n\t    var Y = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];\n\t    var J = [0, 0, 2, 1, 2, 4, 4, 3, 4, 7, 5, 4, 4, 0, 1, 2, 119];\n\t    var B = [0, 1, 2, 3, 17, 4, 5, 33, 49, 6, 18, 65, 81, 7, 97, 113, 19, 34, 50, 129, 8, 20, 66, 145, 161, 177, 193, 9, 35, 51, 82, 240, 21, 98, 114, 209, 10, 22, 36, 52, 225, 37, 241, 23, 24, 25, 26, 38, 39, 40, 41, 42, 53, 54, 55, 56, 57, 58, 67, 68, 69, 70, 71, 72, 73, 74, 83, 84, 85, 86, 87, 88, 89, 90, 99, 100, 101, 102, 103, 104, 105, 106, 115, 116, 117, 118, 119, 120, 121, 122, 130, 131, 132, 133, 134, 135, 136, 137, 138, 146, 147, 148, 149, 150, 151, 152, 153, 154, 162, 163, 164, 165, 166, 167, 168, 169, 170, 178, 179, 180, 181, 182, 183, 184, 185, 186, 194, 195, 196, 197, 198, 199, 200, 201, 202, 210, 211, 212, 213, 214, 215, 216, 217, 218, 226, 227, 228, 229, 230, 231, 232, 233, 234, 242, 243, 244, 245, 246, 247, 248, 249, 250];\n\t\n\t    function M (ag) {\n\t        var af = [16, 11, 10, 16, 24, 40, 51, 61, 12, 12, 14, 19, 26, 58, 60, 55, 14, 13, 16, 24, 40, 57, 69, 56, 14, 17, 22, 29, 51, 87, 80, 62, 18, 22, 37, 56, 68, 109, 103, 77, 24, 35, 55, 64, 81, 104, 113, 92, 49, 64, 78, 87, 103, 121, 120, 101, 72, 92, 95, 98, 112, 100, 103, 99];\n\t        for (var ae = 0; ae < 64; ae++) {\n\t            var aj = k((af[ae] * ag + 50) / 100);\n\t            if (aj < 1) {\n\t                aj = 1\n\t            } else {\n\t                if (aj > 255) {\n\t                    aj = 255\n\t                }\n\t            }\n\t            O[i[ae]] = aj\n\t        }\n\t        var ah = [17, 18, 24, 47, 99, 99, 99, 99, 18, 21, 26, 66, 99, 99, 99, 99, 24, 26, 56, 99, 99, 99, 99, 99, 47, 66, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99];\n\t        for (var ad = 0; ad < 64; ad++) {\n\t            var ai = k((ah[ad] * ag + 50) / 100);\n\t            if (ai < 1) {\n\t                ai = 1\n\t            } else {\n\t                if (ai > 255) {\n\t                    ai = 255\n\t                }\n\t            }\n\t            K[i[ad]] = ai\n\t        }\n\t        var ac = [1, 1.387039845, 1.306562965, 1.175875602, 1, 0.785694958, 0.5411961, 0.275899379];\n\t        var ab = 0;\n\t        for (var ak = 0; ak < 8; ak++) {\n\t            for (var aa = 0; aa < 8; aa++) {\n\t                d[ab] = (1 / (O[i[ab]] * ac[ak] * ac[aa] * 8));\n\t                Z[ab] = (1 / (K[i[ab]] * ac[ak] * ac[aa] * 8));\n\t                ab++\n\t            }\n\t        }\n\t    }\n\t\n\t    function q (ae, aa) {\n\t        var ad = 0;\n\t        var ag = 0;\n\t        var af = new Array();\n\t        for (var ab = 1; ab <= 16; ab++) {\n\t            for (var ac = 1; ac <= ae[ab]; ac++) {\n\t                af[aa[ag]]    = [];\n\t                af[aa[ag]][0] = ad;\n\t                af[aa[ag]][1] = ab;\n\t                ag++;\n\t                ad++\n\t            }\n\t            ad *= 2\n\t        }\n\t        return af\n\t    }\n\t\n\t    function W () {\n\t        u = q(g, c);\n\t        h = q(v, Y);\n\t        G = q(w, E);\n\t        T = q(J, B)\n\t    }\n\t\n\t    function z () {\n\t        var ac = 1;\n\t        var ab = 2;\n\t        for (var aa = 1; aa <= 15; aa++) {\n\t            for (var ad = ac; ad < ab; ad++) {\n\t                m[32767 + ad]    = aa;\n\t                n[32767 + ad]    = [];\n\t                n[32767 + ad][1] = aa;\n\t                n[32767 + ad][0] = ad\n\t            }\n\t            for (var ae = -(ab - 1); ae <= -ac; ae++) {\n\t                m[32767 + ae]    = aa;\n\t                n[32767 + ae]    = [];\n\t                n[32767 + ae][1] = aa;\n\t                n[32767 + ae][0] = ab - 1 + ae\n\t            }\n\t            ac <<= 1;\n\t            ab <<= 1\n\t        }\n\t    }\n\t\n\t    function V () {\n\t        for (var aa = 0; aa < 256; aa++) {\n\t            C[aa]               = 19595 * aa;\n\t            C[(aa + 256) >> 0]  = 38470 * aa;\n\t            C[(aa + 512) >> 0]  = 7471 * aa + 32768;\n\t            C[(aa + 768) >> 0]  = -11059 * aa;\n\t            C[(aa + 1024) >> 0] = -21709 * aa;\n\t            C[(aa + 1280) >> 0] = 32768 * aa + 8421375;\n\t            C[(aa + 1536) >> 0] = -27439 * aa;\n\t            C[(aa + 1792) >> 0] = -5329 * aa\n\t        }\n\t    }\n\t\n\t    function X (aa) {\n\t        var ac = aa[0];\n\t        var ab = aa[1] - 1;\n\t        while (ab >= 0) {\n\t            if (ac & (1 << ab)) {\n\t                t |= (1 << a)\n\t            }\n\t            ab--;\n\t            a--;\n\t            if (a < 0) {\n\t                if (t == 255) {\n\t                    F(255);\n\t                    F(0)\n\t                } else {\n\t                    F(t)\n\t                }\n\t                a = 7;\n\t                t = 0\n\t            }\n\t        }\n\t    }\n\t\n\t    function F (aa) {\n\t        j.push(e[aa])\n\t    }\n\t\n\t    function p (aa) {\n\t        F((aa >> 8) & 255);\n\t        F((aa) & 255)\n\t    }\n\t\n\t    function N (aZ, ap) {\n\t        var aL, aK, aJ, aI, aH, aD, aC, aB;\n\t        var aN   = 0;\n\t        var aR;\n\t        const aq = 8;\n\t        const ai = 64;\n\t        for (aR = 0; aR < aq; ++aR) {\n\t            aL         = aZ[aN];\n\t            aK         = aZ[aN + 1];\n\t            aJ         = aZ[aN + 2];\n\t            aI         = aZ[aN + 3];\n\t            aH         = aZ[aN + 4];\n\t            aD         = aZ[aN + 5];\n\t            aC         = aZ[aN + 6];\n\t            aB         = aZ[aN + 7];\n\t            var aY     = aL + aB;\n\t            var aO     = aL - aB;\n\t            var aX     = aK + aC;\n\t            var aP     = aK - aC;\n\t            var aU     = aJ + aD;\n\t            var aQ     = aJ - aD;\n\t            var aT     = aI + aH;\n\t            var aS     = aI - aH;\n\t            var an     = aY + aT;\n\t            var ak     = aY - aT;\n\t            var am     = aX + aU;\n\t            var al     = aX - aU;\n\t            aZ[aN]     = an + am;\n\t            aZ[aN + 4] = an - am;\n\t            var ax     = (al + ak) * 0.707106781;\n\t            aZ[aN + 2] = ak + ax;\n\t            aZ[aN + 6] = ak - ax;\n\t            an         = aS + aQ;\n\t            am         = aQ + aP;\n\t            al         = aP + aO;\n\t            var at     = (an - al) * 0.382683433;\n\t            var aw     = 0.5411961 * an + at;\n\t            var au     = 1.306562965 * al + at;\n\t            var av     = am * 0.707106781;\n\t            var ah     = aO + av;\n\t            var ag     = aO - av;\n\t            aZ[aN + 5] = ag + aw;\n\t            aZ[aN + 3] = ag - aw;\n\t            aZ[aN + 1] = ah + au;\n\t            aZ[aN + 7] = ah - au;\n\t            aN += 8\n\t        }\n\t        aN = 0;\n\t        for (aR = 0; aR < aq; ++aR) {\n\t            aL          = aZ[aN];\n\t            aK          = aZ[aN + 8];\n\t            aJ          = aZ[aN + 16];\n\t            aI          = aZ[aN + 24];\n\t            aH          = aZ[aN + 32];\n\t            aD          = aZ[aN + 40];\n\t            aC          = aZ[aN + 48];\n\t            aB          = aZ[aN + 56];\n\t            var ar      = aL + aB;\n\t            var aj      = aL - aB;\n\t            var az      = aK + aC;\n\t            var ae      = aK - aC;\n\t            var aG      = aJ + aD;\n\t            var ac      = aJ - aD;\n\t            var aW      = aI + aH;\n\t            var aa      = aI - aH;\n\t            var ao      = ar + aW;\n\t            var aV      = ar - aW;\n\t            var ay      = az + aG;\n\t            var aF      = az - aG;\n\t            aZ[aN]      = ao + ay;\n\t            aZ[aN + 32] = ao - ay;\n\t            var af      = (aF + aV) * 0.707106781;\n\t            aZ[aN + 16] = aV + af;\n\t            aZ[aN + 48] = aV - af;\n\t            ao          = aa + ac;\n\t            ay          = ac + ae;\n\t            aF          = ae + aj;\n\t            var aM      = (ao - aF) * 0.382683433;\n\t            var ad      = 0.5411961 * ao + aM;\n\t            var a1      = 1.306562965 * aF + aM;\n\t            var ab      = ay * 0.707106781;\n\t            var a0      = aj + ab;\n\t            var aA      = aj - ab;\n\t            aZ[aN + 40] = aA + ad;\n\t            aZ[aN + 24] = aA - ad;\n\t            aZ[aN + 8]  = a0 + a1;\n\t            aZ[aN + 56] = a0 - a1;\n\t            aN++\n\t        }\n\t        var aE;\n\t        for (aR = 0; aR < ai; ++aR) {\n\t            aE    = aZ[aR] * ap[aR];\n\t            P[aR] = (aE > 0) ? ((aE + 0.5) | 0) : ((aE - 0.5) | 0)\n\t        }\n\t        return P\n\t    }\n\t\n\t    function b () {\n\t        p(65504);\n\t        p(16);\n\t        F(74);\n\t        F(70);\n\t        F(73);\n\t        F(70);\n\t        F(0);\n\t        F(1);\n\t        F(1);\n\t        F(0);\n\t        p(1);\n\t        p(1);\n\t        F(0);\n\t        F(0)\n\t    }\n\t\n\t    function r (aa, ab) {\n\t        p(65472);\n\t        p(17);\n\t        F(8);\n\t        p(ab);\n\t        p(aa);\n\t        F(3);\n\t        F(1);\n\t        F(17);\n\t        F(0);\n\t        F(2);\n\t        F(17);\n\t        F(1);\n\t        F(3);\n\t        F(17);\n\t        F(1)\n\t    }\n\t\n\t    function D () {\n\t        p(65499);\n\t        p(132);\n\t        F(0);\n\t        for (var ab = 0; ab < 64; ab++) {\n\t            F(O[ab])\n\t        }\n\t        F(1);\n\t        for (var aa = 0; aa < 64; aa++) {\n\t            F(K[aa])\n\t        }\n\t    }\n\t\n\t    function H () {\n\t        p(65476);\n\t        p(418);\n\t        F(0);\n\t        for (var ae = 0; ae < 16; ae++) {\n\t            F(g[ae + 1])\n\t        }\n\t        for (var ad = 0; ad <= 11; ad++) {\n\t            F(c[ad])\n\t        }\n\t        F(16);\n\t        for (var ac = 0; ac < 16; ac++) {\n\t            F(w[ac + 1])\n\t        }\n\t        for (var ab = 0; ab <= 161; ab++) {\n\t            F(E[ab])\n\t        }\n\t        F(1);\n\t        for (var aa = 0; aa < 16; aa++) {\n\t            F(v[aa + 1])\n\t        }\n\t        for (var ah = 0; ah <= 11; ah++) {\n\t            F(Y[ah])\n\t        }\n\t        F(17);\n\t        for (var ag = 0; ag < 16; ag++) {\n\t            F(J[ag + 1])\n\t        }\n\t        for (var af = 0; af <= 161; af++) {\n\t            F(B[af])\n\t        }\n\t    }\n\t\n\t    function I () {\n\t        p(65498);\n\t        p(12);\n\t        F(3);\n\t        F(1);\n\t        F(0);\n\t        F(2);\n\t        F(17);\n\t        F(3);\n\t        F(17);\n\t        F(0);\n\t        F(63);\n\t        F(0)\n\t    }\n\t\n\t    function L (ad, aa, al, at, ap) {\n\t        var ag   = ap[0];\n\t        var ab   = ap[240];\n\t        var ac;\n\t        const ar = 16;\n\t        const ai = 63;\n\t        const ah = 64;\n\t        var aq   = N(ad, aa);\n\t        for (var am = 0; am < ah; ++am) {\n\t            S[i[am]] = aq[am]\n\t        }\n\t        var an = S[0] - al;\n\t        al     = S[0];\n\t        if (an == 0) {\n\t            X(at[0])\n\t        } else {\n\t            ac = 32767 + an;\n\t            X(at[m[ac]]);\n\t            X(n[ac])\n\t        }\n\t        var ae = 63;\n\t        for (; (ae > 0) && (S[ae] == 0); ae--) {\n\t        }\n\t        if (ae == 0) {\n\t            X(ag);\n\t            return al\n\t        }\n\t        var ao = 1;\n\t        var au;\n\t        while (ao <= ae) {\n\t            var ak = ao;\n\t            for (; (S[ao] == 0) && (ao <= ae); ++ao) {\n\t            }\n\t            var aj = ao - ak;\n\t            if (aj >= ar) {\n\t                au = aj >> 4;\n\t                for (var af = 1; af <= au; ++af) {\n\t                    X(ab)\n\t                }\n\t                aj = aj & 15\n\t            }\n\t            ac = 32767 + S[ao];\n\t            X(ap[(aj << 4) + m[ac]]);\n\t            X(n[ac]);\n\t            ao++\n\t        }\n\t        if (ae != ai) {\n\t            X(ag)\n\t        }\n\t        return al\n\t    }\n\t\n\t    function y () {\n\t        var ab = String.fromCharCode;\n\t        for (var aa = 0; aa < 256; aa++) {\n\t            e[aa] = ab(aa)\n\t        }\n\t    }\n\t\n\t    this.encode = function (an, aj, aB) {\n\t        var aa = new Date().getTime();\n\t        if (aj) {\n\t            R(aj)\n\t        }\n\t        j                       = new Array();\n\t        t                       = 0;\n\t        a                       = 7;\n\t        p(65496);\n\t        b();\n\t        D();\n\t        r(an.width, an.height);\n\t        H();\n\t        I();\n\t        var al                  = 0;\n\t        var aq                  = 0;\n\t        var ao                  = 0;\n\t        t                       = 0;\n\t        a                       = 7;\n\t        this.encode.displayName = \"_encode_\";\n\t        var at                  = an.data;\n\t        var ar                  = an.width;\n\t        var aA                  = an.height;\n\t        var ay                  = ar * 4;\n\t        var ai                  = ar * 3;\n\t        var ah, ag              = 0;\n\t        var am, ax, az;\n\t        var ab, ap, ac, af, ae;\n\t        while (ag < aA) {\n\t            ah = 0;\n\t            while (ah < ay) {\n\t                ab = ay * ag + ah;\n\t                ap = ab;\n\t                ac = -1;\n\t                af = 0;\n\t                for (ae = 0; ae < 64; ae++) {\n\t                    af = ae >> 3;\n\t                    ac = (ae & 7) * 4;\n\t                    ap = ab + (af * ay) + ac;\n\t                    if (ag + af >= aA) {\n\t                        ap -= (ay * (ag + 1 + af - aA))\n\t                    }\n\t                    if (ah + ac >= ay) {\n\t                        ap -= ((ah + ac) - ay + 4)\n\t                    }\n\t                    am    = at[ap++];\n\t                    ax    = at[ap++];\n\t                    az    = at[ap++];\n\t                    A[ae] = ((C[am] + C[(ax + 256) >> 0] + C[(az + 512) >> 0]) >> 16) - 128;\n\t                    f[ae] = ((C[(am + 768) >> 0] + C[(ax + 1024) >> 0] + C[(az + 1280) >> 0]) >> 16) - 128;\n\t                    U[ae] = ((C[(am + 1280) >> 0] + C[(ax + 1536) >> 0] + C[(az + 1792) >> 0]) >> 16) - 128\n\t                }\n\t                al = L(A, d, al, u, G);\n\t                aq = L(f, Z, aq, h, T);\n\t                ao = L(U, Z, ao, h, T);\n\t                ah += 32\n\t            }\n\t            ag += 8\n\t        }\n\t        if (a >= 0) {\n\t            var aw = [];\n\t            aw[1]  = a + 1;\n\t            aw[0]  = (1 << (a + 1)) - 1;\n\t            X(aw)\n\t        }\n\t        p(65497);\n\t        if (aB) {\n\t            var av = j.length;\n\t            var aC = new Uint8Array(av);\n\t            for (var au = 0; au < av; au++) {\n\t                aC[au] = j[au].charCodeAt()\n\t            }\n\t            j      = [];\n\t            var ak = new Date().getTime() - aa;\n\t            return aC\n\t        }\n\t        var ad = \"data:image/jpeg;base64,\" + btoa(j.join(\"\"));\n\t        j      = [];\n\t        var ak = new Date().getTime() - aa;\n\t        return ad\n\t    };\n\t    function R (ab) {\n\t        if (ab <= 0) {\n\t            ab = 1\n\t        }\n\t        if (ab > 100) {\n\t            ab = 100\n\t        }\n\t        if (x == ab) {\n\t            return\n\t        }\n\t        var aa = 0;\n\t        if (ab < 50) {\n\t            aa = Math.floor(5000 / ab)\n\t        } else {\n\t            aa = Math.floor(200 - ab * 2)\n\t        }\n\t        M(aa);\n\t        x      = ab;\n\t    }\n\t\n\t    function Q () {\n\t        var aa = new Date().getTime();\n\t        if (!l) {\n\t            l = 50\n\t        }\n\t        y();\n\t        W();\n\t        z();\n\t        V();\n\t        R(l);\n\t        var ab = new Date().getTime() - aa;\n\t    }\n\t\n\t    Q()\n\t}\n\t\n\tmodule.exports = JPEGEncoder;\n\n/***/ }\n\n});\n\n\n/** WEBPACK FOOTER **\n ** 2.chunk.js\n **/", "function JPEGEncoder (l) {\n    var o = this;\n    var s = Math.round;\n    var k = Math.floor;\n    var O = new Array(64);\n    var K = new Array(64);\n    var d = new Array(64);\n    var Z = new Array(64);\n    var u;\n    var h;\n    var G;\n    var T;\n    var n = new Array(65535);\n    var m = new Array(65535);\n    var P = new Array(64);\n    var S = new Array(64);\n    var j = [];\n    var t = 0;\n    var a = 7;\n    var A = new Array(64);\n    var f = new Array(64);\n    var U = new Array(64);\n    var e = new Array(256);\n    var C = new Array(2048);\n    var x;\n    var i = [0, 1, 5, 6, 14, 15, 27, 28, 2, 4, 7, 13, 16, 26, 29, 42, 3, 8, 12, 17, 25, 30, 41, 43, 9, 11, 18, 24, 31, 40, 44, 53, 10, 19, 23, 32, 39, 45, 52, 54, 20, 22, 33, 38, 46, 51, 55, 60, 21, 34, 37, 47, 50, 56, 59, 61, 35, 36, 48, 49, 57, 58, 62, 63];\n    var g = [0, 0, 1, 5, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0];\n    var c = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];\n    var w = [0, 0, 2, 1, 3, 3, 2, 4, 3, 5, 5, 4, 4, 0, 0, 1, 125];\n    var E = [1, 2, 3, 0, 4, 17, 5, 18, 33, 49, 65, 6, 19, 81, 97, 7, 34, 113, 20, 50, 129, 145, 161, 8, 35, 66, 177, 193, 21, 82, 209, 240, 36, 51, 98, 114, 130, 9, 10, 22, 23, 24, 25, 26, 37, 38, 39, 40, 41, 42, 52, 53, 54, 55, 56, 57, 58, 67, 68, 69, 70, 71, 72, 73, 74, 83, 84, 85, 86, 87, 88, 89, 90, 99, 100, 101, 102, 103, 104, 105, 106, 115, 116, 117, 118, 119, 120, 121, 122, 131, 132, 133, 134, 135, 136, 137, 138, 146, 147, 148, 149, 150, 151, 152, 153, 154, 162, 163, 164, 165, 166, 167, 168, 169, 170, 178, 179, 180, 181, 182, 183, 184, 185, 186, 194, 195, 196, 197, 198, 199, 200, 201, 202, 210, 211, 212, 213, 214, 215, 216, 217, 218, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250];\n    var v = [0, 0, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0];\n    var Y = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];\n    var J = [0, 0, 2, 1, 2, 4, 4, 3, 4, 7, 5, 4, 4, 0, 1, 2, 119];\n    var B = [0, 1, 2, 3, 17, 4, 5, 33, 49, 6, 18, 65, 81, 7, 97, 113, 19, 34, 50, 129, 8, 20, 66, 145, 161, 177, 193, 9, 35, 51, 82, 240, 21, 98, 114, 209, 10, 22, 36, 52, 225, 37, 241, 23, 24, 25, 26, 38, 39, 40, 41, 42, 53, 54, 55, 56, 57, 58, 67, 68, 69, 70, 71, 72, 73, 74, 83, 84, 85, 86, 87, 88, 89, 90, 99, 100, 101, 102, 103, 104, 105, 106, 115, 116, 117, 118, 119, 120, 121, 122, 130, 131, 132, 133, 134, 135, 136, 137, 138, 146, 147, 148, 149, 150, 151, 152, 153, 154, 162, 163, 164, 165, 166, 167, 168, 169, 170, 178, 179, 180, 181, 182, 183, 184, 185, 186, 194, 195, 196, 197, 198, 199, 200, 201, 202, 210, 211, 212, 213, 214, 215, 216, 217, 218, 226, 227, 228, 229, 230, 231, 232, 233, 234, 242, 243, 244, 245, 246, 247, 248, 249, 250];\n\n    function M (ag) {\n        var af = [16, 11, 10, 16, 24, 40, 51, 61, 12, 12, 14, 19, 26, 58, 60, 55, 14, 13, 16, 24, 40, 57, 69, 56, 14, 17, 22, 29, 51, 87, 80, 62, 18, 22, 37, 56, 68, 109, 103, 77, 24, 35, 55, 64, 81, 104, 113, 92, 49, 64, 78, 87, 103, 121, 120, 101, 72, 92, 95, 98, 112, 100, 103, 99];\n        for (var ae = 0; ae < 64; ae++) {\n            var aj = k((af[ae] * ag + 50) / 100);\n            if (aj < 1) {\n                aj = 1\n            } else {\n                if (aj > 255) {\n                    aj = 255\n                }\n            }\n            O[i[ae]] = aj\n        }\n        var ah = [17, 18, 24, 47, 99, 99, 99, 99, 18, 21, 26, 66, 99, 99, 99, 99, 24, 26, 56, 99, 99, 99, 99, 99, 47, 66, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99];\n        for (var ad = 0; ad < 64; ad++) {\n            var ai = k((ah[ad] * ag + 50) / 100);\n            if (ai < 1) {\n                ai = 1\n            } else {\n                if (ai > 255) {\n                    ai = 255\n                }\n            }\n            K[i[ad]] = ai\n        }\n        var ac = [1, 1.387039845, 1.306562965, 1.175875602, 1, 0.785694958, 0.5411961, 0.275899379];\n        var ab = 0;\n        for (var ak = 0; ak < 8; ak++) {\n            for (var aa = 0; aa < 8; aa++) {\n                d[ab] = (1 / (O[i[ab]] * ac[ak] * ac[aa] * 8));\n                Z[ab] = (1 / (K[i[ab]] * ac[ak] * ac[aa] * 8));\n                ab++\n            }\n        }\n    }\n\n    function q (ae, aa) {\n        var ad = 0;\n        var ag = 0;\n        var af = new Array();\n        for (var ab = 1; ab <= 16; ab++) {\n            for (var ac = 1; ac <= ae[ab]; ac++) {\n                af[aa[ag]]    = [];\n                af[aa[ag]][0] = ad;\n                af[aa[ag]][1] = ab;\n                ag++;\n                ad++\n            }\n            ad *= 2\n        }\n        return af\n    }\n\n    function W () {\n        u = q(g, c);\n        h = q(v, Y);\n        G = q(w, E);\n        T = q(J, B)\n    }\n\n    function z () {\n        var ac = 1;\n        var ab = 2;\n        for (var aa = 1; aa <= 15; aa++) {\n            for (var ad = ac; ad < ab; ad++) {\n                m[32767 + ad]    = aa;\n                n[32767 + ad]    = [];\n                n[32767 + ad][1] = aa;\n                n[32767 + ad][0] = ad\n            }\n            for (var ae = -(ab - 1); ae <= -ac; ae++) {\n                m[32767 + ae]    = aa;\n                n[32767 + ae]    = [];\n                n[32767 + ae][1] = aa;\n                n[32767 + ae][0] = ab - 1 + ae\n            }\n            ac <<= 1;\n            ab <<= 1\n        }\n    }\n\n    function V () {\n        for (var aa = 0; aa < 256; aa++) {\n            C[aa]               = 19595 * aa;\n            C[(aa + 256) >> 0]  = 38470 * aa;\n            C[(aa + 512) >> 0]  = 7471 * aa + 32768;\n            C[(aa + 768) >> 0]  = -11059 * aa;\n            C[(aa + 1024) >> 0] = -21709 * aa;\n            C[(aa + 1280) >> 0] = 32768 * aa + 8421375;\n            C[(aa + 1536) >> 0] = -27439 * aa;\n            C[(aa + 1792) >> 0] = -5329 * aa\n        }\n    }\n\n    function X (aa) {\n        var ac = aa[0];\n        var ab = aa[1] - 1;\n        while (ab >= 0) {\n            if (ac & (1 << ab)) {\n                t |= (1 << a)\n            }\n            ab--;\n            a--;\n            if (a < 0) {\n                if (t == 255) {\n                    F(255);\n                    F(0)\n                } else {\n                    F(t)\n                }\n                a = 7;\n                t = 0\n            }\n        }\n    }\n\n    function F (aa) {\n        j.push(e[aa])\n    }\n\n    function p (aa) {\n        F((aa >> 8) & 255);\n        F((aa) & 255)\n    }\n\n    function N (aZ, ap) {\n        var aL, aK, aJ, aI, aH, aD, aC, aB;\n        var aN   = 0;\n        var aR;\n        const aq = 8;\n        const ai = 64;\n        for (aR = 0; aR < aq; ++aR) {\n            aL         = aZ[aN];\n            aK         = aZ[aN + 1];\n            aJ         = aZ[aN + 2];\n            aI         = aZ[aN + 3];\n            aH         = aZ[aN + 4];\n            aD         = aZ[aN + 5];\n            aC         = aZ[aN + 6];\n            aB         = aZ[aN + 7];\n            var aY     = aL + aB;\n            var aO     = aL - aB;\n            var aX     = aK + aC;\n            var aP     = aK - aC;\n            var aU     = aJ + aD;\n            var aQ     = aJ - aD;\n            var aT     = aI + aH;\n            var aS     = aI - aH;\n            var an     = aY + aT;\n            var ak     = aY - aT;\n            var am     = aX + aU;\n            var al     = aX - aU;\n            aZ[aN]     = an + am;\n            aZ[aN + 4] = an - am;\n            var ax     = (al + ak) * 0.707106781;\n            aZ[aN + 2] = ak + ax;\n            aZ[aN + 6] = ak - ax;\n            an         = aS + aQ;\n            am         = aQ + aP;\n            al         = aP + aO;\n            var at     = (an - al) * 0.382683433;\n            var aw     = 0.5411961 * an + at;\n            var au     = 1.306562965 * al + at;\n            var av     = am * 0.707106781;\n            var ah     = aO + av;\n            var ag     = aO - av;\n            aZ[aN + 5] = ag + aw;\n            aZ[aN + 3] = ag - aw;\n            aZ[aN + 1] = ah + au;\n            aZ[aN + 7] = ah - au;\n            aN += 8\n        }\n        aN = 0;\n        for (aR = 0; aR < aq; ++aR) {\n            aL          = aZ[aN];\n            aK          = aZ[aN + 8];\n            aJ          = aZ[aN + 16];\n            aI          = aZ[aN + 24];\n            aH          = aZ[aN + 32];\n            aD          = aZ[aN + 40];\n            aC          = aZ[aN + 48];\n            aB          = aZ[aN + 56];\n            var ar      = aL + aB;\n            var aj      = aL - aB;\n            var az      = aK + aC;\n            var ae      = aK - aC;\n            var aG      = aJ + aD;\n            var ac      = aJ - aD;\n            var aW      = aI + aH;\n            var aa      = aI - aH;\n            var ao      = ar + aW;\n            var aV      = ar - aW;\n            var ay      = az + aG;\n            var aF      = az - aG;\n            aZ[aN]      = ao + ay;\n            aZ[aN + 32] = ao - ay;\n            var af      = (aF + aV) * 0.707106781;\n            aZ[aN + 16] = aV + af;\n            aZ[aN + 48] = aV - af;\n            ao          = aa + ac;\n            ay          = ac + ae;\n            aF          = ae + aj;\n            var aM      = (ao - aF) * 0.382683433;\n            var ad      = 0.5411961 * ao + aM;\n            var a1      = 1.306562965 * aF + aM;\n            var ab      = ay * 0.707106781;\n            var a0      = aj + ab;\n            var aA      = aj - ab;\n            aZ[aN + 40] = aA + ad;\n            aZ[aN + 24] = aA - ad;\n            aZ[aN + 8]  = a0 + a1;\n            aZ[aN + 56] = a0 - a1;\n            aN++\n        }\n        var aE;\n        for (aR = 0; aR < ai; ++aR) {\n            aE    = aZ[aR] * ap[aR];\n            P[aR] = (aE > 0) ? ((aE + 0.5) | 0) : ((aE - 0.5) | 0)\n        }\n        return P\n    }\n\n    function b () {\n        p(65504);\n        p(16);\n        F(74);\n        F(70);\n        F(73);\n        F(70);\n        F(0);\n        F(1);\n        F(1);\n        F(0);\n        p(1);\n        p(1);\n        F(0);\n        F(0)\n    }\n\n    function r (aa, ab) {\n        p(65472);\n        p(17);\n        F(8);\n        p(ab);\n        p(aa);\n        F(3);\n        F(1);\n        F(17);\n        F(0);\n        F(2);\n        F(17);\n        F(1);\n        F(3);\n        F(17);\n        F(1)\n    }\n\n    function D () {\n        p(65499);\n        p(132);\n        F(0);\n        for (var ab = 0; ab < 64; ab++) {\n            F(O[ab])\n        }\n        F(1);\n        for (var aa = 0; aa < 64; aa++) {\n            F(K[aa])\n        }\n    }\n\n    function H () {\n        p(65476);\n        p(418);\n        F(0);\n        for (var ae = 0; ae < 16; ae++) {\n            F(g[ae + 1])\n        }\n        for (var ad = 0; ad <= 11; ad++) {\n            F(c[ad])\n        }\n        F(16);\n        for (var ac = 0; ac < 16; ac++) {\n            F(w[ac + 1])\n        }\n        for (var ab = 0; ab <= 161; ab++) {\n            F(E[ab])\n        }\n        F(1);\n        for (var aa = 0; aa < 16; aa++) {\n            F(v[aa + 1])\n        }\n        for (var ah = 0; ah <= 11; ah++) {\n            F(Y[ah])\n        }\n        F(17);\n        for (var ag = 0; ag < 16; ag++) {\n            F(J[ag + 1])\n        }\n        for (var af = 0; af <= 161; af++) {\n            F(B[af])\n        }\n    }\n\n    function I () {\n        p(65498);\n        p(12);\n        F(3);\n        F(1);\n        F(0);\n        F(2);\n        F(17);\n        F(3);\n        F(17);\n        F(0);\n        F(63);\n        F(0)\n    }\n\n    function L (ad, aa, al, at, ap) {\n        var ag   = ap[0];\n        var ab   = ap[240];\n        var ac;\n        const ar = 16;\n        const ai = 63;\n        const ah = 64;\n        var aq   = N(ad, aa);\n        for (var am = 0; am < ah; ++am) {\n            S[i[am]] = aq[am]\n        }\n        var an = S[0] - al;\n        al     = S[0];\n        if (an == 0) {\n            X(at[0])\n        } else {\n            ac = 32767 + an;\n            X(at[m[ac]]);\n            X(n[ac])\n        }\n        var ae = 63;\n        for (; (ae > 0) && (S[ae] == 0); ae--) {\n        }\n        if (ae == 0) {\n            X(ag);\n            return al\n        }\n        var ao = 1;\n        var au;\n        while (ao <= ae) {\n            var ak = ao;\n            for (; (S[ao] == 0) && (ao <= ae); ++ao) {\n            }\n            var aj = ao - ak;\n            if (aj >= ar) {\n                au = aj >> 4;\n                for (var af = 1; af <= au; ++af) {\n                    X(ab)\n                }\n                aj = aj & 15\n            }\n            ac = 32767 + S[ao];\n            X(ap[(aj << 4) + m[ac]]);\n            X(n[ac]);\n            ao++\n        }\n        if (ae != ai) {\n            X(ag)\n        }\n        return al\n    }\n\n    function y () {\n        var ab = String.fromCharCode;\n        for (var aa = 0; aa < 256; aa++) {\n            e[aa] = ab(aa)\n        }\n    }\n\n    this.encode = function (an, aj, aB) {\n        var aa = new Date().getTime();\n        if (aj) {\n            R(aj)\n        }\n        j                       = new Array();\n        t                       = 0;\n        a                       = 7;\n        p(65496);\n        b();\n        D();\n        r(an.width, an.height);\n        H();\n        I();\n        var al                  = 0;\n        var aq                  = 0;\n        var ao                  = 0;\n        t                       = 0;\n        a                       = 7;\n        this.encode.displayName = \"_encode_\";\n        var at                  = an.data;\n        var ar                  = an.width;\n        var aA                  = an.height;\n        var ay                  = ar * 4;\n        var ai                  = ar * 3;\n        var ah, ag              = 0;\n        var am, ax, az;\n        var ab, ap, ac, af, ae;\n        while (ag < aA) {\n            ah = 0;\n            while (ah < ay) {\n                ab = ay * ag + ah;\n                ap = ab;\n                ac = -1;\n                af = 0;\n                for (ae = 0; ae < 64; ae++) {\n                    af = ae >> 3;\n                    ac = (ae & 7) * 4;\n                    ap = ab + (af * ay) + ac;\n                    if (ag + af >= aA) {\n                        ap -= (ay * (ag + 1 + af - aA))\n                    }\n                    if (ah + ac >= ay) {\n                        ap -= ((ah + ac) - ay + 4)\n                    }\n                    am    = at[ap++];\n                    ax    = at[ap++];\n                    az    = at[ap++];\n                    A[ae] = ((C[am] + C[(ax + 256) >> 0] + C[(az + 512) >> 0]) >> 16) - 128;\n                    f[ae] = ((C[(am + 768) >> 0] + C[(ax + 1024) >> 0] + C[(az + 1280) >> 0]) >> 16) - 128;\n                    U[ae] = ((C[(am + 1280) >> 0] + C[(ax + 1536) >> 0] + C[(az + 1792) >> 0]) >> 16) - 128\n                }\n                al = L(A, d, al, u, G);\n                aq = L(f, Z, aq, h, T);\n                ao = L(U, Z, ao, h, T);\n                ah += 32\n            }\n            ag += 8\n        }\n        if (a >= 0) {\n            var aw = [];\n            aw[1]  = a + 1;\n            aw[0]  = (1 << (a + 1)) - 1;\n            X(aw)\n        }\n        p(65497);\n        if (aB) {\n            var av = j.length;\n            var aC = new Uint8Array(av);\n            for (var au = 0; au < av; au++) {\n                aC[au] = j[au].charCodeAt()\n            }\n            j      = [];\n            var ak = new Date().getTime() - aa;\n            return aC\n        }\n        var ad = \"data:image/jpeg;base64,\" + btoa(j.join(\"\"));\n        j      = [];\n        var ak = new Date().getTime() - aa;\n        return ad\n    };\n    function R (ab) {\n        if (ab <= 0) {\n            ab = 1\n        }\n        if (ab > 100) {\n            ab = 100\n        }\n        if (x == ab) {\n            return\n        }\n        var aa = 0;\n        if (ab < 50) {\n            aa = Math.floor(5000 / ab)\n        } else {\n            aa = Math.floor(200 - ab * 2)\n        }\n        M(aa);\n        x      = ab;\n    }\n\n    function Q () {\n        var aa = new Date().getTime();\n        if (!l) {\n            l = 50\n        }\n        y();\n        W();\n        z();\n        V();\n        R(l);\n        var ab = new Date().getTime() - aa;\n    }\n\n    Q()\n}\n\nmodule.exports = JPEGEncoder;\n\n\n/** WEBPACK FOOTER **\n ** src/lib/jpeg_encoder_basic.js\n **/"], "sourceRoot": ""}