/*
    companyAccountOpening.js for companyAccountOpening.hmtl
    author: zy
*/
var common = {
    opt: {
        deleteImg: function (_this, fileNum) { //删除图片--deleteImageIds字段的整理
            if (NO(_this.attr('data-id')) != '') {
                editM.ids.push(Number(_this.attr('data-id')));
            }
            _this.parents('.file').remove();
            //整理顺序:
            commonO.resetIndex();
            //上传控制
            var imgLen = $(".uploaderFiles .file").length;
            if (imgLen < fileNum) {
                if ($(".updataBtnBox").is(':hidden')) {
                    $(".updataBtnBox").show()
                }
            }
        },
        idFileChange: function (_this, filename, imgbox) {
            var base = "";
            var fileArr = tytTool.fileInfo(_this, 3);
            setTimeout(function () {
                if (fileArr.result.length == 0) {
                    return false;
                }
                base = fileArr.result[0];
                var fileType = fileArr.img_index[0].name
                fileType = fileType.substring(fileType.lastIndexOf(".") + 1).toLowerCase();
                var imgLen = $(".uploaderFiles .file").length;
                var _ind = $(".uploaderFiles .file").length + 1;
                var _li = '<img onclick="commonO.lookBigImg($(this))" src="' + base + '" alt="" ondblclick="return false;" data-lock="true" data-type="' + fileType + '">';
                // $(".updataBtnBox").before(_li)
                $(("." + imgbox)).html(_li)
                imgLen = $(".uploaderFiles .file").length;
                if (imgLen >= fileNum) {
                    $(".updataBtnBox").hide()
                }
            }, 60);
        },
        fileChange: function (_this, fileNum, imgbox) { //处理Base64,插入DOM
            

            var base = '';
            var fileArr = tytTool.fileInfo(_this, 3);
            setTimeout(function () {
                if (fileArr.result.length == 0) {
                    return false;
                }
                base = fileArr.result[0];
                var fileType = fileArr.img_index[0].name
                fileType = fileType.substring(fileType.lastIndexOf(".") + 1).toLowerCase();

                var imgLen = $(".uploaderFiles .file").length;
                var _ind = $(".uploaderFiles .file").length + 1;
                // var _li = '<li class="file fl">\
                //     <i onclick="commonO.deleteImg($(this),' + fileNum + ')"></i>\
                //     <img onclick="commonO.lookBigImg($(this))" src="' + base + '" alt="" ondblclick="return false;">\
                //     <p class="imgTitle">' + _ind + '</p>\
                // </li>';
                var _li = '<img onclick="commonO.lookBigImg($(this))" src="' + base + '" alt="" ondblclick="return false;" data-lock="true" data-type="' + fileType + '">';
                // $(".updataBtnBox").before(_li)

                $(("." + imgbox)).html(_li)
                $(("." + imgbox)).css("display", "block")
                imgLen = $(".uploaderFiles .file").length;
                if (imgLen >= fileNum) {
                    $(".updataBtnBox").hide()
                }
            }, 60);
        },
        clearFile: function (e) {
            e.target.value = ''
        },
        lookBigImg: function (_this, Small) { //base图片窗口浏览
            var size = NO(Small) != '' ? [600, 300] : [1200, 600]
            var _img = _this.attr('data-url') || _this.attr('src');
            var img = new Image(),
                isTop = (window.screen.availHeight - 30 - size[1]) / 2,
                isLeft = (window.screen.availWidth - 10 - size[0]) / 2;
            img.style = '-webkit-user-select:none;margin:auto;display:block;text-align:center;vertical-align: middle;'
            img.src = _img;
            var newWin = window.open("", Small, 'width=' + size[0] + ',height=' + size[1] + ',top=' + isTop + ',left=' + isLeft + ',status=,toolbar=no,menubar=no,location=no,resizable=no,scrollbars=yes,titlebar=no');
            newWin.document.write(img.outerHTML);
            newWin.document.body.style = 'margin:0px;background:#0e0e0e;'
            newWin.document.title = "查看图片"
            newWin.document.close();
        },
    }
}
var commonM = common.mayData;
var commonA = common.arr;
var commonJ = common.ajx;
var commonO = common.opt;
var interfaceUrl = tytTool.getRootPath_web();
// var interfaceUrl = '192.168.2.103:8088/manage_new'
var cao = {
    myData: {
        codeLock: false,
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
            contentType: 'application/json; charset=utf-8',
        }
    },
    ajx: {
        submitCbFun: function (data) {
            if (data.code == 200) {
                $(".loading").css("display","none")
                tytTool.toastShow("提交成功", 4000)
                setTimeout(function(){
                    window.location.href="manage.html"
                },3000)
            } else {
                $(".loading").css("display","none")
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        }
    },
    opt: {
        timeOpt: function () { //时间选择器
            return WdatePicker({
                isShowClear: false,
                dateFmt: 'yyyy-MM-dd',
                onpicking: function (dp) {
                    var val = tytTool.NullObj(dp.cal.getNewDateStr());
                    var str = val.replace(/年|月/g, "-").replace(/日/g, "")
                    var date = new Date(str), timeString = Date.parse(date);
                    $(dp.el).attr('data-str', timeString)
                }
            });
        },
        codefun: function () {   //获取验证码
            var phone = $("#phone").val();
            var reg = /^(0|1)[0-9]{10}$/;
            if (!reg.test(phone)) {
                tytTool.toastShow('请输入正确的绑定手机号', '')
                return false;
            }

            if (cm.codeLock) {
                return false;
            }
            cm.codeLock = true;
            var ajaxUrl = interfaceUrl + '/enterprise/acct/verifyCode/send';
            tytTool.ajaxFn({
                url: '',
                otherUrl: ajaxUrl,
                type: 'get',
                otherData: '',
                data: {
                    "regPhone": phone
                },
                contentType: 'application/json; charset=utf-8',
            }, function (res) {
                // res.code=200   //用于调试
                if (res.code == 200) {
                    $("#code_submit").css({ "background": "rgba(48, 141, 227, 1)", "color": "#FFF" });
                    $("#code_submit").attr("data-id", true);
                    $(".regPhone").attr("data-userid", res.data)
                    co.countDown();
                    $(".code_error").html()
                } else {
                    cm.codeLock = false;
                    if (res.msg == "用户不存在") {
                        res.msg = "用户不存在，请先注册再开户。"
                    }
                    $(".code_error").html(res.msg)
                }
            })
        },
        countDown: function () {   //  获取验证码后开始 数秒
            var code_btn = $(".code_btn");
            var time = 60;
            var timer = setInterval(function () {
                time--
                if (time == 0) {
                    clearInterval(timer)
                    code_btn.html("重新获取")
                    cm.codeLock = false;
                } else {
                    code_btn.html(time + 's后重新获取')

                }
            }, 1000)
        },
        checkcodefun: function () { // 验证手机验证码
            var reg = /^(0|1)[0-9]{10}$/;
            if (!reg.test($('#phone').val())) {
                tytTool.toastShow('请输入正确的绑定手机号', '')
                return false;
            }
            if (!$("#code_submit").attr("data-id")) {
                tytTool.toastShow('请先获取手机验证码', '')
                return false;
            }
            if ($("#code").val() == "") {
                tytTool.toastShow('请输入手机验证码', '')
                return false;
            }
            var checkcodeurl = interfaceUrl + "/enterprise/acct/regPhone/bind";
            tytTool.ajaxFn({
                url: '',
                otherUrl: checkcodeurl,
                type: 'post',
                otherData: '',
                data: {
                    "regPhone": $("#phone").val(),
                    "verifyCode": $("#code").val()
                },
                contentType: 'application/x-www-form-urlencoded',
            }, function (res) {
                if (res.code == 200) {
                    $(".regPhone").val($('#phone').val());
                    $(".code").css("display", "none")
                    $(".main-content").css("display", "block");
                    co.initFun(); // 企业初始化 用于密码输入框的出现的判断
                } else {
                    $(".code_error").html("验证码输入错误!")
                }
            })
        },
        submitData: function () {
            //开户基本信息验证
            var reg = /^(0|1)[0-9]{10}$/;
            // if (!reg.test($('.regPhone').val())) {
            //     tytTool.toastShow('请输入正确的绑定手机号', '')
            //     return false;
            // }
            if ($.trim($('.companyName').val()) == '') {
                tytTool.toastShow('请输入企业名称', '')
                return false;
            }
            if ($.trim($('.address').val()) == '') {
                tytTool.toastShow('请输入企业地址', '')
                return false;
            }
            if ($('.reqEmail').val() == "") {
                tytTool.toastShow('请输入注册邮箱', '')
                return false;
            }
            var regEmail = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (!regEmail.test($('.reqEmail').val())) {
                tytTool.toastShow('请输入正确的注册邮箱', '')
                return false;
            }
            // var reg_code = /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g;
            // if (!reg_code.test($('.creditCode').val())) {
            //     tytTool.toastShow('请输入正确的社会信用代码证', '')
            //     return false;
            // }
            if (!$(".imgbox3").find("img").attr("data-lock")) {
                tytTool.toastShow('请上传社会信用代码证照片', '')
                return false;
            }
            if ($.trim($('.idExp').val()) == '') {
                tytTool.toastShow('请选择证件有效期', '')
                return false;
            }
            if ($.trim($('#_ocx_password_str').find("input").val()) == '') {
                tytTool.toastShow('请输入密码', '')
                return false;
            }
            //开户绑卡信息验证
            if ($.trim($('.bankName').val()) == '') {
                tytTool.toastShow('请输入银行名称', '')
                return false;
            }
            if ($.trim($('.bankAccount').val()) == '') {
                tytTool.toastShow('请输入银行账号', '')
                return false;
            }
            if ($.trim($('.bankCode').val()) == '') {
                tytTool.toastShow('请输入银行编号', '')
                return false;
            }
            if ($.trim($('.linkedAcctName').val()) == '') {
                tytTool.toastShow('请输入对公账户名', '')
                return false;
            }
            if ($.trim($('.linkedAcctBankNo').val()) == '') {
                tytTool.toastShow('请输入对公账户开户行行号', '')
                return false;
            }
            // 企业法人信息 验证
            if ($.trim($('.legalReptName').val()) == '') {
                tytTool.toastShow('请输入法定代表人姓名', '')
                return false;
            }
            if (!reg.test($('.legalReptPhone').val())) {
                tytTool.toastShow('请输入正确的法定代表人手机号', '')
                return false;
            }
            if (!IdCardValidate($('.legalReptIdNo').val())) {
                tytTool.toastShow('请输入正确的法定代表人身份证号', '')
                return false;
            }
            if ($.trim($('.legalReptIdExp').val()) == '') {
                tytTool.toastShow('请选择法定代表人身份证有效期', '')
                return false;
            }
            if (!$(".imgbox1").find("img").attr("data-lock")) {
                tytTool.toastShow('请上传法定代表人身份证照片', '')
                return false;
            }
            if (!$(".imgbox2").find("img").attr("data-lock")) {
                tytTool.toastShow('请上传法定代表人身份证照片', '')
                return false;
            }
            //企业联系人信息验证
            if ($.trim($('.contactsName').val()) == '') {
                tytTool.toastShow('请输入联系人姓名', '')
                return false;
            }
            if (!reg.test($('.contactsPhone').val())) {
                tytTool.toastShow('请输入联系人手机号', '')
                return false;
            }
            //企业基本户信息验证
            if ($.trim($('.companyBasicBankCode').val()) == '') {
                tytTool.toastShow('请输入企业基本户银行编码', '')
                return false;
            }
            if ($.trim($('.companyBasicAcctNo').val()) == '') {
                tytTool.toastShow('请输入企业基本户账号', '')
                return false;
            }
           
            $(".loading").css("display","block")
            tytTool.ajaxFn(
                {    //随机因子获取
                    url: '',
                    otherUrl: interfaceUrl + '/enterprise/acct/random/get',
                    type: 'POST',
                    otherData: '',
                    data: {
                        "userId":$(".regPhone").attr("data-userid"),
                        // "userId": "150059",

                        'channel': "PC"
                    },
                    contentType: 'application/x-www-form-urlencoded',
                },
                function (data) {
                    if (data.code != '200') {
                        tytTool.toastShow('保存失败!', '');
                        $(".loading").css("display","none")
                        return false;
                    }
                    var wid1 = pgeditor1.settings.pgeWindowID;
                    // pgeditor1.pwdSetSk($('#random_value').val(),function(){//设置随机因子value

                    var obj = {
                        "randomKey": data.data.randomKey,
                        "randomValue": data.data.randomValue
                    }
                    pgeditor1.pwdSetSk(obj.randomValue, function () {//设置随机因子value
                        pgeditor1.pwdHash(function () {
                            pgeditor1.pwdResultRsa(function () {
                                //获取密文
                                $("#password_ciphertext").val(outs[wid1].aes);  //讲密码加密
                                co.submitUserDataFun(obj)
                            });
                        });
                    });
                }
            )
        },
        submitUserDataFun: function (obj) {
            ca.common.data = JSON.stringify({
                basicInfo: {
                    regPhone: $('.regPhone').val(),  //绑定手机号：
                    companyName: $('.companyName').val(),   //企业名称：
                    address: $('.address').val(),        //企业地址
                    regEmail: $('.reqEmail').val(),     //注册邮箱
                    creditCode: $('.unifiedcodefiletype').val(),   //社会信用代码证：
                    // unifiedCodeImg: $(".imgbox3").find("img").attr("src"),    // 上传社会信用代码证照片
                    unifiedCodeImg: $(".imgbox3").find("img").attr("src").split("base64,")[1],    // 上传社会信用代码证照片
                    unifiedCodeFiletype: $(".imgbox3").find("img").attr("data-type"),// 统一社会信用代码证照片类型
                    idExp: $('.idExp').val().replace(/-/g, ''),  //证件有效期：
                    password: $('#password_ciphertext').val(),   //设置密码：
                    randomKey: obj.randomKey,   //随机因子KEY
                },
                bindCardInfo: {
                    bankName: $('.bankName').val(),
                    bankAccount: $('.bankAccount').val(),   //银行账号：
                    bankCode: $('.bankCode').val(),   //银行编码：
                    linkedAcctName: $('.linkedAcctName').val(),   //对公账户名：
                    linkedAcctBankNo: $('.linkedAcctBankNo').val(),   //对公账户开户行行号：
                },
                legalreptInfo: {
                    legalReptName: $('.legalReptName').val(),  //法定代表人姓名：
                    legalReptPhone: $('.legalReptPhone').val(),  //法定代表人手机号：
                    legalReptIdNo: $('.legalReptIdNo').val(),   //法定代表人身份证号：
                    legalReptIdExp: $('.legalReptIdExp').val().replace(/-/g, ''),  //法定代表人身份证有效期：
                    // idPortraitImg: $(".imgbox1").find("img").attr("src"),   // 法定代表人身份证照片上传：   身份证正面（人像）
                    // idEmblemImg: $(".imgbox2").find("img").attr("src"),//法定代表人身份证照片上传：  身份证反面（国徽）
                    idPortraitImg: $(".imgbox1").find("img").attr("src").split("base64,")[1],   // 法定代表人身份证照片上传：   身份证正面（人像）
                    idEmblemImg: $(".imgbox2").find("img").attr("src").split("base64,")[1],//法定代表人身份证照片上传：  身份证反面（国徽）
                    idFiletype: $(".imgbox2").find("img").attr("data-type")
                },
                contactInfo: {
                    contactsName: $('.contactsName').val(),  //联系人姓名：
                    contactsPhone: $('.contactsPhone').val(),  //联系人手机号：
                },
                companyBasicAcctInfo: {
                    companyBasicBankCode: $('.companyBasicBankCode').val(),   //企业基本户银行编码：
                    companyBasicAcctNo: $('.companyBasicAcctNo').val(),   //企业基本户账号：
                }
            })
            ca.common.type = 'post';
            ca.common.contentType = 'application/json; charset=utf-8',
                ca.common.otherUrl = interfaceUrl + '/enterprise/acct/apply';
            tytTool.ajaxFn(ca.common, cj.submitCbFun)
        },
        initFun: function () {
            ca.common.otherUrl = interfaceUrl + '/enterprise/acct/init';
            ca.common.type = 'get';
            ca.common.contentType = "application/x-www-form-urlencoded"
            tytTool.ajaxFn(ca.common, function (data) {
                initPgeditor(data.data);
            })
        },
        longtimefun:function(box){
            $("."+box).val('9999-12-31')
        }
    }
}
var cm = cao.myData;
var ca = cao.arr;
var cj = cao.ajx;
var co = cao.opt;
// co.initFun(); // 企业初始化 用于密码输入框的出现的判断