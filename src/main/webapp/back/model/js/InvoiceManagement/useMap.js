/**
 * @time 2021/1/16 16:33
 * <AUTHOR>
 * @description 使用高德地图js
 */
let mapUse = {};
function AmapUse(arr) {
    // return new Promise(((resolve, reject) => {
    AMapLoader.load({
        key: "7b0d7988f8f8c5058dae32d67e1aabf8", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "1.4.15", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ["AMap.Scale", "AMap.ToolBar", "AMap.Geocoder"], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        AMapUI: {
            // 是否加载 AMapUI，缺省不加载
            version: "1.1", // AMapUI 缺省 1.1
            plugins: ["overlay/SimpleMarker"], // 需要加载的 AMapUI ui插件
        },
        Loca: {
            // 是否加载 Loca， 缺省不加载
            version: "1.3.2", // Loca 版本，缺省 1.3.2
        },
    }).then((AMap) => {
        mapUse = new useMap(AMap, arr);
        arr.usePolyline ? mapUse.usePolyline() : '';
        /*
            经纬度转地址的方法调用
            typeof arr.regeoCodeFn === 'function' ? mapUse.regeoCode() : '';
        */
       arr.callback ? arr.callback(AMap,mapUse) : '';
    }).catch((e) => {
        console.error(e); //加载错误提示
    });
    // }))
}

class useMap {
    constructor(params, arr) {
        this.AMap = params;
        this.arr = arr;
        this.position = this.arr.center || this.arr.lineArr[Math.ceil(this.arr.lineArr.length / 2)]; //指定点或路线的经纬中心点
        this.map = this.init(this.AMap)
    }
    init(AMap) {
        return new AMap.Map("mapContainer", {
            resizeEnable: true,
            center: this.position,
            zoom: 15,
            showIndoorMap: false, // 关闭室内地图
        });
    }
    /**
     * @param lineArr 经纬度数组 [[lng,lat]]
     */
    usePolyline() {
        const polyline = new this.AMap.Polyline({
            path: this.arr.lineArr,
            strokeOpacity: 1,
            strokeWeight: 6,
            lineJoin: "round",
            lineCap: "round",
            zIndex: 50,
            showDir: true,
            // strokeColor: "#28F", //线颜色
            strokeColor: "#03BE6D", //线颜色
        });
        polyline.setMap(this.map);
    }
    useInfoWindow(data = {}) {
        //先关闭open出来的infoWindow
        this.map.clearInfoWindow();
        let content = `<div class="windowBg">
          <h3 class="windowTitle">${data.title}</h3>
          <time class="windowTime">${data.time}</time>
          <p class="windowAddress">${data.text}</p>
        </div>`;
        const infoWindow = new this.AMap.InfoWindow({
            content: content,
            // offset: new this.AMap.Pixel(16, -45),
        });
        infoWindow.open(this.map, [data.lng, data.lat]);
    }
    regeoCode() {
        if (this.arr.lineArr.length > 0) {
            var geocoder = new this.AMap.Geocoder({
                // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
                city: '010'
            })
            // result为对应的地理位置详细信息
            geocoder.getAddress(this.arr.lineArr, (status, result) => {
                if (status === 'complete' && result.info === 'OK') {
                    let array = [];
                    result.regeocodes.forEach((ele, index) => {
                        // lat和lon使用转译前的使用即可,防止被定点地址类化
                        array.push({
                            lat: this.arr.lineArr[index].lat,
                            lon: this.arr.lineArr[index].lng,
                            curAddr: ele.formattedAddress
                        })
                    })
                    this.arr.regeoCodeFn(array)
                }
            });
        }
    }
}

