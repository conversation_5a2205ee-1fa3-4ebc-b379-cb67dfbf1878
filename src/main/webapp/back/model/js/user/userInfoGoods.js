/*
    userInfoAll.js for userInfoAll.hmtl
    author: hey<PERSON>an
*/
var goods = {
    myData: {
        pageSize: 20,
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data, _currentPage) {
            if (data.code == 200) {
                var data_list = data.data.list;
                var total = data.data.total;
                var currentPage = Number(_currentPage),
                    maxPage = Math.ceil(Number(total) / goodsM.pageSize),
                    rowCount = Number(total);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                }
                tytTool.jqPaginatorExtend(arrParams, 'goodsO.listOpt')
                //正式载入数据
                var rule = {
                    list: [
                        '操作width200##opt',
                        'ID##userId',
                        '姓名##trueName',
                        '用户名##goodsUserName',
                        '手机号码##cellPhone##Fn::goodsO.optPhone({id:{{cellPhone}}})',
                        '归属地##attribution',
                        '注册日期##ctimeBegin##Fn::tytTool.timeNull({{registDate}},"yyyy/MM/dd hh:mm")',
                        '会员到期日##goodsEndTime##Fn::tytTool.timeNull({{goodsEndTime}},"yyyy/MM/dd")',
                        '维护人##maintainMan',
                        '来源渠道##source',
                    ],
                    item: {
                        cellPhone: {
                            attr: ['class==phoneHide']
                        }
                    },
                    opt: {
                        dom: `<a class="info_opt" onclick="comO.changeItem({{userId}},{{carVipLabel}},{{goodsVipLabel}})">详情</a>
                        <a class="info_opt check_btn" onclick="comO.exposureCardDetails({{userId}},{{carVipLabel}},{{goodsVipLabel}})">查看曝光卡</a>`
                    }
                }
                var tmp = tytTool.eachList(data_list, rule)
                $("#infoGoods").empty().append(tmp.str)
                // tytTool.bindHidePhone();
                comO.colResizable();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
        
    },
    opt: {
        loadList: function() {
            var pageArr = {
                "totalPages": 1,
                "pageSize": goodsM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": goodsO.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            goodsA.common.otherUrl = `${path}/admin/v6000/user/goods/list`
            goodsA.common.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            goodsA.common.data = $.extend({}, arr, {
                "page": currentPage,
                "size": goodsM.pageSize,
                "province": NO($("#tytCity").attr('data-val')).split(',')[0] || '',
                "city": NO($("#tytCity").attr('data-val')).split(',')[1] || '',
                "county": NO($("#tytCity").attr('data-val')).split(',')[2] || ''
            })
            tytTool.ajaxFn(goodsA.common, goodsJ.listData)
        },
        optPhone: function(data){ 
            return  `<a class="blue" onclick="phoneSoHfn($(this),\'${data.id}\','用户信息管理（货方）')"'>查看电话</a>`
        }
    }
}

var goodsM = goods.myData;
var goodsA = goods.arr;
var goodsJ = goods.ajx;
var goodsO = goods.opt;
tytTool.loadCheckTab(comM.goods, {})
comO.groupSource('sourceSelect')
goodsO.loadList();