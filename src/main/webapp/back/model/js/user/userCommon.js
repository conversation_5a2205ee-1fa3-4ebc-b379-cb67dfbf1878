/*
    userCommon.js for model/html/user/*.hmtl
    author: hey<PERSON>an
*/
var path = tytTool.getRootPath_web();
var common = {
    myData: {
        all: { //查询项配置-总用户信息管理
            box: '.searchUl=>li',
            list: [{
                    label: '用户ID：',
                    attr: ['tname##userId', 'maxlength##40'],
                },
                {
                    label: '真实姓名：',
                    attr: ['tname##trueName', 'maxlength##40'],
                },
                {
                    label: '车方用户名：',
                    attr: ['tname##carUserName', 'maxlength##40'],
                },
                {
                    label: '货方用户名：',
                    attr: ['tname##goodsUserName', 'maxlength##40'],
                }, {
                    label: '手机号：',
                    attr: ['tname##cellPhone', 'maxlength##11', 'onblur##comO.inpBlurValid($(this),\'basePhone\')'],
                }, {
                    label: '用户身份：##select',
                    attr: ['tname##userSign'],
                    option: ['请选择##', '全部##2', '车方##0', '货方##1']
                }, {
                    label: '归属地：',
                    attr: ['id##tytCity', 'readonly##readonly', 'onclick##comO.tytCity()', 'placeholder##请选择'],
                }, {
                    label: '用户来源：##select',
                    attr: ['tname##source', 'id##sourceSelect'],
                    option: ['全部##']
                },
                {
                    label: '销售：',
                    attr: ['tname##sell', 'maxlength##40'],
                },
                {
                    label: '维护人：',
                    attr: ['tname##maintainMan', 'maxlength##40'],
                }, {
                    label: '注册日期：',
                    boxAttr: ['class##twoColumn'],
                    attr: ['tname##registDateStart', 'id##registDateStart', 'class##pickTime', 'readonly##readonly', 'placeholder##开始时间', 'onclick##comO.startPicker("registDateEnd","reg")'],
                    splitDom: '<span> - </span><input type="text" tname="registDateEnd" id="registDateEnd" class="pickTime" readonly="readonly" placeholder="结束时间" onclick="comO.endPicker(\'registDateStart\',\'reg\')">'
                },
            ]
        },
        car: { //查询项配置-车用户信息管理
            box: '.searchUl=>li',
            list: [{
                    label: '用户ID：',
                    attr: ['tname##userId', 'maxlength##40'],
                }, {
                    label: '真实姓名：',
                    attr: ['tname##trueName', 'maxlength##40'],
                }, { 
                    label: '车方用户名：',
                    attr: ['tname##carUserName', 'maxlength##40'],
                }, {
                    label: '手机号：',
                    attr: ['tname##cellPhone', 'maxlength##11', 'onblur##comO.inpBlurValid($(this),\'basePhone\')'],
                }, {
                    label: '归属地：',
                    attr: ['id##tytCity', 'readonly##readonly', 'onclick##comO.tytCity()', 'placeholder##请选择'],
                }, {
                    label: '用户来源：##select',
                    attr: ['tname##source', 'id##sourceSelect'],
                    option: ['全部##']
                }, 
                {
                    label: '维护人：',
                    attr: ['tname##maintainMan', 'maxlength##40'],
                },{
                    label: '注册日期：',
                    boxAttr: ['class##twoColumn'],
                    attr: ['tname##registDateStart', 'id##registDateStart', 'class##pickTime', 'readonly##readonly', 'placeholder##开始时间', 'onclick##comO.startPicker("registDateEnd","reg")'],
                    splitDom: '<span> - </span><input type="text" tname="registDateEnd" id="registDateEnd" class="pickTime" readonly="readonly" placeholder="结束时间" onclick="comO.endPicker(\'registDateStart\',\'reg\')">'
                }, {
                    label: '车方权益到期日：',
                    boxAttr: ['class##twoColumn'],
                    attr: ['tname##carStartTime', 'id##carStartTime', 'class##pickTime', 'readonly##readonly', 'placeholder##开始时间', 'onclick##comO.startPicker("carEndTime")'],
                    splitDom: '<span> - </span><input type="text" tname="carEndTime" id="carEndTime" class="pickTime" readonly="readonly" placeholder="结束时间" onclick="comO.endPicker(\'carStartTime\')">'
                },{
                    label: '货方权益到期日：',
                    boxAttr: ['class##twoColumn'],
                    attr: ['tname##goodsDateStart', 'id##goodsDateStart', 'readonly##readonly', 'placeholder##开始时间', 'onclick##comO.startPicker("goodsDateEnd")'],
                    splitDom: '<span> - </span><input type="text" tname="goodsDateEnd" id="goodsDateEnd" readonly="readonly" placeholder="结束时间" onclick="comO.endPicker(\'goodsDateStart\')">'
                },
            ]
        },
        goods: { //查询项配置-货用户信息管理
            box: '.searchUl=>li',
            list: [{
                    label: '用户ID：',
                    attr: ['tname##userId', 'maxlength##40'],
                }, {
                    label: '真实姓名：',
                    attr: ['tname##trueName', 'maxlength##40'],
                }, {
                    label: '货方用户名：',
                    attr: ['tname##userName', 'maxlength##40'],
                }, {
                    label: '手机号：',
                    attr: ['tname##cellPhone', 'maxlength##11', 'onblur##comO.inpBlurValid($(this),\'basePhone\')'],
                }, {
                    label: '归属地：',
                    attr: ['id##tytCity', 'readonly##readonly', 'onclick##comO.tytCity()', 'placeholder##请选择'],
                }, {
                    label: '用户来源：##select',
                    attr: ['tname##source', 'id##sourceSelect'],
                    option: ['全部##']
                },{
                    label: '维护人：',
                    attr: ['tname##maintainMan', 'maxlength##40'],
                },{
                    label: '注册日期：',
                    boxAttr: ['class##twoColumn'],
                    attr: ['tname##registDateStart', 'id##registDateStart', 'class##pickTime', 'readonly##readonly', 'placeholder##开始时间', 'onclick##comO.startPicker("registDateEnd","reg")'],
                    splitDom: '<span> - </span><input type="text" tname="registDateEnd" id="registDateEnd" class="pickTime" readonly="readonly" placeholder="结束时间" onclick="comO.endPicker(\'registDateStart\',\'reg\')">'
                }, {
                    label: '货方权益到期日：',
                    boxAttr: ['class##twoColumn'],
                    attr: ['tname##goodsDateStart', 'id##goodsDateStart', 'class##pickTime', 'readonly##readonly', 'placeholder##开始时间', 'onclick##comO.startPicker("goodsDateEnd")'],
                    splitDom: '<span> - </span><input type="text" tname="goodsDateEnd" id="goodsDateEnd" class="pickTime" readonly="readonly" placeholder="结束时间" onclick="comO.endPicker(\'goodsDateStart\')">'
                }
            ]
        },
        visitor: { //查询项配置-游客用户信息管理
            box: '.searchUl=>li',
            list: [{
                    label: '用户ID：',
                    attr: ['tname##userId', 'maxlength##40'],
                },
                {
                    label: '真实姓名：',
                    attr: ['tname##trueName', 'maxlength##40'],
                },
                {
                    label: '车方用户名：',
                    attr: ['tname##carUserName', 'maxlength##40'],
                },
                {
                    label: '货方用户名：',
                    attr: ['tname##goodsUserName', 'maxlength##40'],
                }, {
                    label: '手机号：',
                    attr: ['tname##cellPhone', 'maxlength##11', 'onblur##comO.inpBlurValid($(this),\'basePhone\')'],
                }, {
                    label: '用户身份：##select',
                    attr: ['tname##userSign'],
                    option: ['请选择##', '全部##2', '车方##0', '货方##1']
                }, {
                    label: '归属地：',
                    attr: ['id##tytCity', 'readonly##readonly', 'onclick##comO.tytCity()', 'placeholder##请选择'],
                }, {
                    label: '用户来源：##select',
                    attr: ['tname##source', 'id##sourceSelect'],
                    option: ['全部##']
                }, {
                    label: '销售：',
                    attr: ['tname##sell', 'maxlength##40'],
                }, {
                    label: '注册日期：',
                    boxAttr: ['class##twoColumn'],
                    attr: ['tname##registDateStart', 'id##registDateStart', 'class##pickTime', 'readonly##readonly', 'placeholder##开始时间', 'onclick##comO.startPicker("registDateEnd","reg")'],
                    splitDom: '<span> - </span><input type="text" tname="registDateEnd" id="registDateEnd" class="pickTime" readonly="readonly" placeholder="结束时间" onclick="comO.endPicker(\'registDateStart\',\'reg\')">'
                }
            ]
        },
    },
    arr: {
        source: {
            url: '',
            otherUrl: '',
            type: 'post',
            data: ''
        }
    },
    ajx: {
        sourceData: function(data, source) {
            if (data && data.length > 0) {
                var select = '';
                $("#" + source).find("option:not('.firstOption')").remove()
                $.each(data, function(index, ele) {
                    select += `<option value="${ele.value}">${ele.name}</option>`
                })
                $("#" + source).append(select)
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000)
                }
            }
        }
    },
    opt: {
        groupSource: function(source) { //用户来源数据请求入口
            comA.source.otherUrl = `${path}/boss/tytsource/getByGroupCode`;
            comA.source.otherData = source;
            comA.source.data = {
                "groupCode": 'source'
            }
            tytTool.ajaxFn(comA.source, comJ.sourceData)
        },
        colResizable: function() {
            $(".JCLRgrips").remove()
            var tabWidth = $(".tableBox").parents('.dataTable').width()
            $(".tableBox").width(tabWidth);
            tytTool.colResizable($(".tableBox"));
            $(".JCLRgrips").width(tabWidth)
        },
        changeItem: function(_id, car,goods) { //跳转详情页面携带参数
            var type = comO.identCheck(car,goods)
            var _url = 'userInfoDetail.html?userId=' + _id + '&pageType=' + type;
            tytTool.openWinAuto(_url, 'changeItem', 1200, 600)
        },
        tytCity: function(params) {
            $("#tytCity").tytCity({
                "componentBox": "#componentBox",
                "cityTit": ['省份', '城市', '县区'],
                callback: function(arr, str, val) {
                    // $("#province").val(NO(val.split(',')[0]))
                    // $("#city").val(NO(val.split(',')[1]))
                    // $("#area").val(NO(val.split(',')[2]))
                }
            });
        },
        formReset: function(fn) {
            $("#tytCity").val('').removeAttr('data-val');
            $(".pickTime").removeAttr('pick-time')
            tytTool.formReset($('.searchUl'), fn)
        },
        identCheck: function (car,goods) {
            var ident = '',
                c = NO(car) != '' ? JSON.parse(car) : null,
                g = NO(goods) != '' ? JSON.parse(goods) : null;
            if(c && g) {
                ident = 'all';
            } else if(c && !g) {
                ident = 'car';
            } else if(!c && g) {
                ident = 'goods';
            } else if(!c && !g) {
                ident = 'visitor';
            }
            return ident;
        },
        startPicker: function(end,reg) { //时间控件--开始
            var arr = {};
            if(reg == 'reg') {
                arr = {
                    maxDate: `#F{$dp.$D('${end}')||\'%y-%M-%d\'}`
                }
            } else {
                arr = {
                    maxDate: `#F{$dp.$D('${end}')}`,
                }
            }
            return WdatePicker($.extend({},{
                dateFmt: 'yyyy-MM-dd',
                onpicking: comO.onpicking,
                oncleared: comO.oncleared
            },arr));
        },
        endPicker: function(start,reg) { //时间控件--结束
            var arr = {};
            if(reg == 'reg') {
                arr = {
                    minDate: `#F{$dp.$D('${start}')}`,
                    maxDate: '#F{\'%y-%M-%d\'}'
                }
            } else {
                arr = {
                    minDate: `#F{$dp.$D('${start}')}`,
                }
            }
            return WdatePicker($.extend({},{
                dateFmt: 'yyyy-MM-dd',
                onpicking: comO.onpicking,
                oncleared: comO.oncleared
            },arr));
        },
        onpicking: function(dp) { //时间控件--选择后触发
            var val = tytTool.NullObj(dp.cal.getNewDateStr());
            var date = new Date(val),
                timeString = Date.parse(date);
            $(dp.el).attr('pick-time', timeString)
        },
        oncleared: function(dp) { //时间控件--清除操作触发
            $(dp.el).removeAttr('pick-time')
        },
        inpBlurValid: function(_this, _reg) {
            _this.val(_this.val())
            if (_reg != '' && !tytTool.validateStr(_reg, _this.val())) {
                _this.val('')
            }
        },
        exposureCardDetails: function(type,car,goods){
            console.log(type)
            console.log($('.userId').html())
            var _id = type=='detail'? tytTool.parseSearch().userId : type
                _pageType = type=='detail'? tytTool.parseSearch().pageType : comO.identCheck(car,goods)
                _openType = type=='detail'? 'details' : 'tabList'
                _url = tytTool.pathUrl() + 'manageModel/exposureDetails?userId='+_id+'&pageType='+ _pageType+'&openType='+_openType
            tytTool.openWinAuto(_url,'openWin', 1200, 600)
        }
    }
}

var comM = common.myData;
var comA = common.arr;
var comJ = common.ajx;
var comO = common.opt;