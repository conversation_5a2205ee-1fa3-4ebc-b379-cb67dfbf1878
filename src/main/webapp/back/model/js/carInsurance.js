/**
 * carInsurance.js 车险询价列表页面js
 * <AUTHOR>
 * @date 2019-03-26
 * @type {number}
 */
//页面大小
var pageSize = 30;
//登录人角色是否是车险业务人员 0否 1:是
var isCarInsuranceRole = 0;
var carInsurance = {
    //ajax请求头参数
    arr: {
        getCarInsuranceDict: {
            otherUrl: '',
            type: 'post',
            contentType: 'application/json',
            headers: {
                token: JSESSIONID
            },
            data: ''
        },
        getListData: {
            otherUrl: '',
            type: 'post',
            contentType: 'application/json',
            headers: {
                token: JSESSIONID
            },
            data: ''
        }
    },
    //ajax请求返回结果
    ajaxfn: {
        getCarInsuranceDict: function(data) {
            if (data.code == 200) {
                //字典列表数据
                var _data = data.data;
                var _insuranceTypeList = _data.insuranceTypeList;
                var _servicerList = _data.servicerList;
                var _dealStatusList = _data.dealStatusList;

                //加载车险询价字典列表
                //车险险种
                $("#insuranceType option:not('.firstOption')").remove();
                var _option = '';
                $.each(_insuranceTypeList,function(index, el) {
                    _option += `<option value="${_insuranceTypeList[index].value}">${_insuranceTypeList[index].name}</option>`
                });
                $("#insuranceType").append(_option);

                //业务人员
                $("#serviceUserId option:not('.firstOption')").remove();
                _option = '';
                $.each(_servicerList,function(index, el) {
                    _option += `<option value="${_servicerList[index].id}">${_servicerList[index].realName}</option>`
                });
                $("#serviceUserId").append(_option);

                //询价状态
                $("#dealStatus option:not('.firstOption')").remove();
                _option = '';
                $.each(_dealStatusList,function(index, el) {
                    _option += `<option value="${_dealStatusList[index].value}">${_dealStatusList[index].name}</option>`
                });
                $("#dealStatus").append(_option);
            }
        },
        getListData: function(data) {
            if (data.code == 200) {
                //分页数据
                var _data = data.data;
                //判断登录人角色是否是车险业务人员
                isCarInsuranceRole = _data.isCarInsuranceRole;
                var data_list = _data.carInsuranceInquiryList;
                var _pageBean = _data.pageBean;
                $("#carInsuranceList tr:not('.tableTh')").remove();
                var arrParams = {
                    currentPage: _pageBean.currentPage || 1,
                    totalPages: _pageBean.maxPage || 1
                }
                if (data_list && _pageBean.maxPage > 0) {
                    $("#totalPages").html(_pageBean.maxPage);
                    $("#currentPage").html(_pageBean.currentPage);
                    $("#rowCount").html(_pageBean.rowCount);
                    tytTool.jqPaginatorExtend(arrParams, 'carInsuranceOptfn.getListMsg')
                } else {
                    $("#totalPages").html(0);
                    $("#currentPage").html("");
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams, 'carInsuranceOptfn.getListMsg')
                }
                //加载车险询价的列表数据
                var _html = '';
                var _commitTime = '';
                var _insuranceType = '';
                var _itemAndAmt = '';
                var _dealStatus = '';
                var  _serviceUserName = '';
                if (data_list && data_list.length > 0) {
                    $.each(data_list, function(index, el) {
                        //车险险种 1.商业险 2.交强险 3.商业险、交强险
                        switch (Number(data_list[index].insuranceType)) {
                            case 1:
                                _insuranceType = '商业险';
                                break;
                            case 2:
                                _insuranceType = '交强险';
                                break;
                            case 3:
                                _insuranceType = '商业险、交强险';
                                break;
                            default:
                                _insuranceType = '';
                        }
                        //处理结果(跟踪状态) 1.待处理 2.处理中 3.已处理 4.已投保
                        switch (Number(data_list[index].dealStatus)) {
                            case 1:
                                _dealStatus = '待处理';
                                break;
                            case 2:
                                _dealStatus = '处理中';
                                break;
                            case 3:
                                _dealStatus = '已处理';
                                break;
                            case 4:
                                _dealStatus = '已投保';
                                break;
                            default:
                                _dealStatus = '';
                        }
                        //保项保额信息
                        var insuranceItemList = data_list[index].insuranceItemList;
                        var _insuranceItem = '',
                            _amtCurrency = '',
                            _itemAndAmt = '';
                        //险种保项(商业险) 1.车辆损失险 2.盗抢险 3.自然损失险 4.第三者责任保险 5.车上司机险  6.车上乘客险
                        $.each(insuranceItemList, function(i, e) {
                            switch (Number(insuranceItemList[i].insuranceItem)) {
                                case 1:
                                    _insuranceItem = '车辆损失险';
                                    break;
                                case 2:
                                    _insuranceItem = '盗抢险';
                                    break;
                                case 3:
                                    _insuranceItem = '自然损失险';
                                    break;
                                case 4:
                                    _insuranceItem = '第三者责任保险';
                                    break;
                                case 5:
                                    _insuranceItem = '车上司机险';
                                    break;
                                case 6:
                                    _insuranceItem = '车上乘客险';
                                    break;
                                default:
                                    _insuranceItem = '';
                            }
                            tytTool.NullObj(insuranceItemList[i].amtCurrency) == 0 ? _amtCurrency = '' : _amtCurrency = "("+insuranceItemList[i].amtCurrency+"万)";
                            if(i != insuranceItemList.size-1)
                            {
                                _itemAndAmt += _insuranceItem + _amtCurrency +'<br/>';
                            }else{
                                _itemAndAmt += _insuranceItem + _amtCurrency;
                            }
                        });
                        //跟踪人员
                        tytTool.NullObj(data_list[index].serviceUserName) == '' ? _serviceUserName = '待定' : _serviceUserName = tytTool.NullObj(data_list[index].serviceUserName);
                        //提交时间
                        tytTool.NullObj(data_list[index].commitTime) == '' ? _commitTime = '' : _commitTime = getFormatDateByLong(data_list[index].commitTime, "yyyy-MM-dd hh:mm:ss");

                        var _opera = '';
                        //根据登录角色判断有没有权限进行编辑操作！
                        if(isCarInsuranceRole == 1){
                            _html += `<tr>
                            <td class="positionTd">
                               <a onclick="openWinAuto('carInsuranceDetail.html?inquiryId='+${tytTool.NullObj(data_list[index].id)},'carInsuranceDetail',1200,600)"></a>     
                            </td>`
                        }else{
                            _html += `<tr><td></td>`
                        }
                        _html += `
							<td>${tytTool.NullObj(data_list[index].id)}</td>
							<td>${tytTool.NullObj(data_list[index].cellPhone)}</td>
							<td>${tytTool.NullObj(data_list[index].linkPhone)}</td>
							<td>${tytTool.NullObj(data_list[index].userName)}</td>
							<td>${tytTool.NullObj(data_list[index].carHeadCity)}${tytTool.NullObj(data_list[index].carHeadNo)}</td>
							<td>${_commitTime}</td>
							<td>${_serviceUserName}</td>
							<td>${_dealStatus}</td>
                        </tr>`
                        /*
                            <td>${_insuranceType}</td>
							<td>${_itemAndAmt}</td>
                        */
                    });
                    $("#carInsuranceList").append(_html);
                    tytTool.bindHidePhone();
                }
            }
        }
    },
    //拼接ajax请求的方法
    optFn: {
        getCarInsuranceDict: function(){
            carInsuranceArr.getCarInsuranceDict.otherUrl = tytTool.getRootPath_web() + '/carInsurance/inquiry/carInsuranceDictList';
            tytTool.ajaxFn(carInsuranceArr.getCarInsuranceDict, carInsuranceAjaxfn.getCarInsuranceDict);
        },
        getListMsg: function(currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            carInsuranceArr.getListData.otherUrl = tytTool.getRootPath_web() + '/carInsurance/inquiry/carInsuranceInquiryList?currentPage=' + currentPage + '&pageSize=' + pageSize + '&menuId=' + tytTool.parseSearch().menuId;
            //车头车牌号(城市+车牌号)
            var carHeadNumber = $("#carHeadNumber").val().trim();
            //车头车牌城市
            var _carHeadCity = '';
            //车头车牌号码
            var _carHeadNo = '';
            if(carHeadNumber != '' && carHeadNumber.length == 7)
            {
                _carHeadCity = carHeadNumber.substring(0,1);
                _carHeadNo = carHeadNumber.substring(1);
            }
            //请求参数
            var params = {
                "linkPhone": $("#linkPhone").val(),
                "userName": $("#userName").val(),
                "insuranceType": $("#insuranceType").val(),
                "carHeadCity": _carHeadCity,
                "carHeadNo": _carHeadNo,
                "serviceUserId": $("#serviceUserId").val(),
                "dealStatus": $("#dealStatus").val(),
                "commitTimeQuery": $("#commitTimeQuery").val(),
                "dealTimeQuery": $("#dealTimeQuery").val(),
            }
            carInsuranceArr.getListData.data = JSON.stringify(params);
            tytTool.ajaxFn(carInsuranceArr.getListData, carInsuranceAjaxfn.getListData);
        },
        queryForm: function() {
            var arr = {
                "totalPages": 1,
                "pageSize": pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": carInsuranceOptfn.getListMsg
            }
            tytTool.addResetIdent($('.searchUl'))
            tytTool.jqPaginator('#pageNumberBox', arr)
        },
        exportFile: function() {
            //如果不是车险业务人员角色
            if(isCarInsuranceRole != 1)
            {
                alert("非常抱歉,您不是车险业务人员,没有导出权限！");
                return;
            }
            //车头车牌号(城市+车牌号)
            var carHeadNumber = $("#carHeadNumber").val();
            //车头车牌城市
            var _carHeadCity = '';
            //车头车牌号码
            var _carHeadNo = '';
            if(carHeadNumber != '' && carHeadNumber.length == 7)
            {
                _carHeadCity = carHeadNumber.substring(0,2);
                _carHeadNo = carHeadNumber.substring(2);
            }
            //请求参数
            var params = {
                "linkPhone": $("#linkPhone").val(),
                "userName": $("#userName").val(),
                "insuranceType": $("#insuranceType").val(),
                "carHeadCity": _carHeadCity,
                "carHeadNo": _carHeadNo,
                "serviceUserId": $("#serviceUserId").val(),
                "dealStatus": $("#dealStatus").val(),
                "commitTimeQuery": $("#commitTimeQuery").val(),
                "dealTimeQuery": $("#dealTimeQuery").val(),
            }
            var exportUrl = tytTool.getRootPath_web() + '/carInsurance/inquiry/exportCarInsuranceInquiryFile';
            window.open(exportUrl + '?' + tytTool.parseParam(params));
        }
    }
}

var carInsuranceArr = carInsurance.arr;
var carInsuranceAjaxfn = carInsurance.ajaxfn;
var carInsuranceOptfn = carInsurance.optFn;

$(function() {

    var arrAll = {
        "totalPages": 1,
        "pageSize": pageSize,
        "visiblePages": 7,
        "currentPage": 1,
        "funName": carInsuranceOptfn.getListMsg
    }

    $(".tableBox").colResizable({
        liveDrag: true,
        draggingClass: "dragging",
        resizeMode: 'flex',
        minWidth: 80
    });

    $(document).click(function(event) {
        $("#boxDiv,#boxDiv2").hide();
    });
    $(".areaDiv").click(function(event) {
        event.stopPropagation();
    });

    //车险询价列表页面入口,加载查询条件字典列表
    $.when(sessionAjx).done(function() {
        //加载字典数据
        carInsuranceOptfn.getCarInsuranceDict();
        //加载列表数据
        tytTool.jqPaginator('#pageNumberBox', arrAll);
    });
})