<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>navControl</title>
</head>
<style>
	#Inp{
		width: 140px;
		height: 24px;
		border: 1px solid #ccc;	
		cursor: pointer;
	}
</style>
<body>
	<input id="Inp" type="text" value="" readonly>
	<ul id="nav"></ul>
</body>
<script src="../jquery-1.7.2.min.js"></script>
<script src="script/jquery.checkbox.js"></script>

<script>
var arrBox = [{
		"id": "1",
		"name": "东风柳汽"
	}, {
		"id": "3",
		"name": "广汽奔驰"
	}, {
		"id": "2",
		"name": "陕汽德龙"
	}, {
		"id": "4",
		"name": "三一重工"
	}, {
		"id": "5",
		"name": "陕汽榆林"
	}, {
		"id": "6",
		"name": "陕汽通力"
	}, {
		"id": "7",
		"name": "上海格拉曼"
	}, {
		"id": "8",
		"name": "上汽红岩"
	}]

	$("#Inp").click(function(event) {
		$("#Inp").tytNav({
			"arrBox":arrBox,
			"choiceArr": $('#Inp').attr('data-arr'),
			"maxLen":[6,'您选择的类型已经超出范围'],
			"checkboxTit":"请选择车辆类型"
		});

	});

</script>
</html>