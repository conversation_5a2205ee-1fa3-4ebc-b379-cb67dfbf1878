// auther: zy
var pageSize = 20;
//登录人角色是否是退款业务人员 0否 1:是
var isRefundRole = 0;
var goodsType = [
    {
        id:1,
        goodsTypeName:'货会员'
    },{
        id:2,
        goodsTypeName:'车会员'
    },{
        id:3,
        goodsTypeName:'组合会员'
    },{
        id:4,
        goodsTypeName:'发货次卡'
    },{
        id:5,
        goodsTypeName:'车体验'
    },{
        id:6,
        goodsTypeName:'车试用'
    },{
        id:7,
        goodsTypeName:'n次x年找货卡'
    },{
        id:8,
        goodsTypeName:'货体验'
    },{
        id:9,
        goodsTypeName:'n次x年发货卡'
    },{
        id:10,
        goodsTypeName:'加购商品'
    }
]

var joinData = {
    myData: {
        goodsList:'',
        url:'',
        orderId:'',
        timer:''
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            contentType: 'application/x-www-form-urlencoded',
            data: ''
        }
    },
    ajaxfn: {
        getListData: function(data) {
            var _data = data.data,
                userBuyGoodsList = _data.userBuyGoodsList,
                pageBean = _data.pageBean;
                isRefundRole = _data.isRefundRole;
                paySuccessCount = _data.paySuccessCount;
                paySuccessAmount = _data.paySuccessAmount;
            var arrParams = {
                currentPage: pageBean.currentPage || 1,
                totalPages: pageBean.maxPage || 1
            }
            tytTool.jqPaginatorExtend(arrParams, 'optFn.getListMsg');

            //正式载入数据
            var _html = '';
            if(data.code == 200){
                if(userBuyGoodsList.length){
                    $.each(userBuyGoodsList, function(index, el) {
                        let ctime = Number(`${tytTool.NullObj(el.ctime)}`);
                        let utime = Number(`${tytTool.NullObj(el.utime)}`);
                        let refundTime = Number(`${tytTool.NullObj(el.refundTime)}`);
                        let ordersStatus = el.ordersStatus;
                        switch (ordersStatus){
                            case 0:
                               ordersStatus = '待支付';
                               break;
                            case 1:
                               ordersStatus = '支付失败';
                               break;
                            case 2:
                               ordersStatus = '支付成功';
					            break;
                            case 3:
                                ordersStatus = '已退款';
                                break;
                        }
                        let goods_type_msg = '';
                        $.each(goodsType, function(index, item) {
                            if(el.goodsType == item.id){
                                goods_type_msg = item.goodsTypeName
                            }
                        })

                        _html += `<tr>
                            <td>${tytTool.NullObj(el.id)}</td>
                            <td>${tytTool.NullObj(el.orderId)}</td>
                            <td>${tytTool.NullObj(el.userId)}</td>
                            <td>${tytTool.NullObj(el.userName)}</td>
                            <td>${tytTool.NullObj(el.goodsName)}</td>
                            <td>${goods_type_msg}</td>
                            <td>${tytTool.NullObj(el.price)}</td>
                            <td>${tytTool.NullObj(el.totalFee)}</td>
                            <td>${tytTool.NullObj(el.payChannel)}</td>
                            <td>${tytTool.NullObj(ordersStatus)}</td>
                            <td>${tytTool.timeTrans(ctime, {timeOpt:['Y','M','D','h','m','s'],splitStr: "/"}).replace(/\s+/g, " ")}</td>
                            <td>${tytTool.timeTrans(utime, {timeOpt:['Y','M','D','h','m','s'],splitStr: "/"}).replace(/\s+/g, " ")}</td>
                            <td>${tytTool.timeTrans(refundTime,{timeOpt:['Y','M','D','h','m','s'],splitStr: "/"}).replace(/\\s+/g, " ")}</td>
                            <td>${tytTool.NullObj(el.remark)}</td>
                            <td>${tytTool.NullObj(el.refundUserName)}</td>
                            <td></td></tr>`;

                        //根据订单支付状态和当前登录人角色判断有没有权限进行退款操作
                        // if(el.ordersStatus == 2 && isRefundRole == 1){
                        //     _html += `<td>
                        //                 <a style="cursor: pointer" onclick="optFn.confirmRefund('${el.id}','${el.orderId}','${el.cellPhone}','${el.goodsName}','${el.totalFee}','${el.userName}')">确认退款</a>
                        //               </td></tr>`
                        // }else{
                        //     _html += `<td></td></tr>`
                        // }
                    });
                }else{
                    _html = '<tr><td colspan="1000"><div>暂无查询数据</div></td></tr>';
                }
                $("#list").html(_html);
                $('.messageRight').html(`商品订单笔数:<em>${paySuccessCount}</em>笔,商品订单金额:<em>${paySuccessAmount}</em>元&nbsp;&nbsp;查询到<em>${pageBean.rowCount}</em>条数据，当前是第<span>${pageBean.currentPage }</span>页，共<span>${pageBean.maxPage }</span>页`)
                tytTool.bindHidePhone();
            }else {
                tytTool.toastShow(data.msg, 1000)
            }
        },
        renderGoodsList: function(data) {
            if(data.code == 200){
                let goodsList = data.data.goodsList;
                let channelList = data.data.channelList[0];
                MyData.goodsList = goodsList;//记录数据
                MyData.url = channelList.requestUrl;

                $("#payModal").show(300)//拿到数据 再显示modal
                $('.pay-method-img').attr('src',tytTool.pathUrl('manage_new') + channelList.icoImageUrl);//支付图标
                $("#payChannelId").val(channelList.id);
                var _html = '<option class="firstOption" value="">请选择</option>';
                $.each(goodsList, function(index, el) {
                    _html += '<option value="'+ el.id +'">'+ el.name +'</option>'
                })
                $('#goodsName').html(_html);
            }else {
                tytTool.toastShow(data.msg, 1000);
            }
        },
        getQRImgCallBack:function(data){
            if(data.code == 200){
                $("#payModal").hide(300)//关闭第一个modal
                $('#submitPayModal').show(300).find('.goods-name').html($('#goodsName').find('option:selected').html());
                $('#submitPayModal').find('.goods-price').html($('#price').val());
                $('#submitPayModal').find('.cell-phone').html($('#cellPhone').val());
                $('.pay-QR-img').show().attr('src','/manage_new/payment/weixin/getPayQRCode?codeUrl='+ data.data.codeUrl + '&userId=' + $('#userId').val());
                $('.pay-msg').html('<p>二维码有效期为10分钟，请在10分钟之内支付</p>');

                MyData.orderId = data.data.orderId;
                //开启轮训
                optFn.startTimer();
            }else{
                tytTool.toastShow(data.msg, 1000);
            }
        },
        getPayResultCallBack(data){
            if(data.code == 200){
                if(data.data.order.status == 2){
                    optFn.stopTimer();//关闭轮询
                    $('.pay-QR-img').hide();//完成的话 隐藏二维码
                    $('.pay-msg').html('<p class="pay-success">缴费完成</p>');
                }else if(data.data.order.status == 1){
                    optFn.stopTimer();//关闭轮询
                    $('.pay-QR-img').hide();
                    $('.pay-msg').html('<p class="pay-success">缴费失败</p>');
                }
                $('.closePayModalBtn').show();
            }
        },
        getUserMsgCallBack(data){
            if(data.code == 200){
                $('#payModal').find('#userId').val(data.data.user.id);
                $('#payModal').find('#userName').val(data.data.user.userName);
                $('#userIdBox,#userNameBox').show();
            }else{
                tytTool.toastShow(data.msg, 1000);
            }
        },
        getOrderRefundResult(data){ //得到商品订单退款的返回信息
            if(data.code == 200){
                //先关闭确认退款弹窗，2s后再进行消息提示
                optFn.closeRefundModal();
                //倒计时时间,单位:s
                var t = 1;
                var inter = setInterval(function() {
                    t--;
                    if(t <= 0) {
                        tytTool.toastShow(data.msg, 2000);
                        clearInterval(inter);
                    }
                }, 1000);
            }else{
                tytTool.toastShow(data.msg, 2000);
            }
        },

    },
    optFn: {
        startTimer:function(){//开启定时轮询
            MyData.timer = setInterval(function () {
                optFn.getPayResult();
            },5000)
        },
        stopTimer:function(){//关闭定时轮询
            clearInterval(MyData.timer);
        },
        getPayResult: function() { //轮询查结果请求
            Arr.common.url = `/goods/userBuyGoodsImport/getPayStatusById`;
            Arr.common.data = {
                orderId:MyData.orderId
            }
            tytTool.ajaxFn(Arr.common, myAjax.getPayResultCallBack)
        },
        getQRImg: function() { //生成二维码请求
            if($('#goodsName').find('option:selected').val() == ''){
                tytTool.toastShow('请选择商品名称', 1000);
                return false;
            }
            if($('#cellPhone').val() == ''){
                tytTool.toastShow('请输入手机号', 1000);
                return false;
            }
            Arr.common.url = MyData.url.split('manage_new')[1];
            Arr.common.data = {
                goodsId:$('#goodsName').find('option:selected').val(),
                buyPath:'manage',
                userId:$('#userId').val(),
                payChannelId:$("#payChannelId").val(),
            }
            tytTool.ajaxFn(Arr.common, myAjax.getQRImgCallBack)
        },
        getUserMsg: function() {
            if($.trim($('#cellPhone').val()) == ''){
                $('#payModal').find('#userId').val('');
                $('#payModal').find('#userName').val('');
                $('#userIdBox,#userNameBox').hide();
                return false;
            }
            Arr.common.url = `/goods/userBuyGoodsImport/getUserByCellphone`;
            Arr.common.data = {
                cellphone:$('#cellPhone').val(),
            }
            tytTool.ajaxFn(Arr.common, myAjax.getUserMsgCallBack)
        },
        getGoodsList: function() {
            Arr.common.url = `/goods/userBuyGoodsImport/getGoodsAndPayChannelList`;
            tytTool.ajaxFn(Arr.common, myAjax.renderGoodsList)
        },
        getListMsg: function(currentPage) {
            currentPage == "" ? currentPage = 1 : currentPage = currentPage;
            Arr.common.url = `/goods/userBuyGoodsImport/userBuyGoodsList?currentPage=${currentPage}&pageSize=${pageSize}`;
            Arr.common.data = tytTool.htmlGetTname($(".searchUl"));
            tytTool.ajaxFn(Arr.common, myAjax.getListData)
        },
        loadList: function() {
            var arr = {
                "totalPages": 1,
                "pageSize": pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": optFn.getListMsg
            }
            tytTool.jqPaginator('#pageNumberBox', arr);
        },
        changeGoodsFun:function(){
            if($('#goodsName').find('option:selected')[0].index == 0){
                $('#price').val('');
                $('#remark').val('');
                $('#priceBox,#remarkBox').hide();
                return false;
            }
            $('#price').val(MyData.goodsList[$('#goodsName').find('option:selected')[0].index-1].price);
            $('#remark').val(MyData.goodsList[$('#goodsName').find('option:selected')[0].index-1].remark);
            $('#priceBox,#remarkBox').show();
        },
        resetValue:function(){//重置
            $('#price,#remark').val('');
            $('#priceBox,#remarkBox').hide();
            $('#payModal').find('#userId').val('');
            $('#payModal').find('#userName').val('');
            $('#userIdBox,#userNameBox').hide();
            $('#cellPhone,#goodsName').val('');
        },
        closePayModal:function(){ //关闭支付弹窗 并刷新
            //隐藏遮罩层
            $(".outBgColor").hide();
            optFn.showHideAdd('hide','submitPayModal');
            tytTool.formReset($('.searchUl'),optFn.loadList)
        },
        showHideAdd: function() {
            if (arguments[0] == 'hide') {
                //隐藏遮罩层
                $(".outBgColor").hide();
                $("#" + arguments[1] ).hide(300);
                optFn.stopTimer();//关闭轮询
                //重新加载商品订单列表
                tytTool.formReset($('.searchUl'),optFn.loadList);
            } else if (arguments[0] == 'show') {
                //显示遮罩层
                $(".outBgColor").show();
                $('#cellPhone,#userId,#userName,#price,#remark').val('');
                $('#userIdBox,#userNameBox,#priceBox,#remarkBox').hide();
                optFn.getGoodsList();
            }
        },
        confirmRefund: function(id,orderId,cellPhone,goodsName,totalFee,userName) {
            //显示遮罩层
            $(".outBgColor").show();
            //给商品订单ID的隐藏域赋值
            $('#user_buy_goods_id').html(id);
            $('#confirm_order_id').html(orderId);
            $('#confirm_user_name').html(userName);
            $('#confirm_cell_phone').html(cellPhone);
            $('#confirm_goods_name').html(goodsName);
            $('#confirm_goods_price').html(totalFee);

            //显示"确认退款"弹框
            $('#confirmRefundModal').show();
        },
        closeRefundModal:function(){ //关闭确认退款弹窗 
            //隐藏遮罩层
            $(".outBgColor").hide();
            optFn.showHideAdd('hide','confirmRefundModal');
        },
        orderConfirmRefund:function(){ //商品订单确认退款
            //商品订单记录ID
            var userBuyGoodsId = $('#user_buy_goods_id').html();
            Arr.common.url = `/goods/userBuyGoodsImport/orderConfirmRefund`;
            Arr.common.data = {
                id:userBuyGoodsId
            }
            tytTool.ajaxFn(Arr.common, myAjax.getOrderRefundResult)
        },
        importFile: function(){
            $(".outBgColor").show();
            $('#errorMsg').html("");
            $(".excelBox").show();
        },
        fileInfo: function(source, _this){
            var f = source.files[0];
            var name = f.name;
            $(".excelBox .alertContent").html(name);
            $(".excelBox h2").html("导入商品订单数据");
        },
        uploadFile: function(){
            //选择文件
            var fileField = $("#fileField")[0].files[0];
            if (fileField == null) {
                tytTool.toastShow("请选择导入文件","");
                return;
            }
            var formData = new FormData();
            formData.append("fileField",fileField);
            $.ajax({
                type: "POST",
                url: tytTool.getRootPath_web() + "/goods/userBuyGoodsImport/importList",
                data: formData,
                contentType: false,
                /**
                 * 必须false才会避开jQuery对 formdata 的默认处理
                 * XMLHttpRequest会对 formdata 进行正确的处理
                 */
                processData: false,
                async : false,
                dataType:"json",
                success: function (data) {
                    if(data.code == 200){
                        tytTool.toastShow(data.msg,"");
                        setTimeout(function(){
                            tytTool.reloadWin();
                        },1000)
                    }else{
                        tytTool.toastShow(data.msg,"");
                        $(".outBgColor").hide();
                        $(".excelBox .alertContent").html('请点击"浏览"选择需要导入的模板文件');
                        $(".excelBox h2").html("导入商品订单数据");
                        $("#fileField").attr("value","");
                        $(".excelBox").hide();
                    }
                },
                error: function (xhr, status) {
                    tytTool.toastShow("导入商品订单数据失败,请检查文件及数据","");
                }
            });
        },
        importFileClose: function(){
            $(".outBgColor").hide();
            $(".excelBox .alertContent").html('请点击"浏览"选择需要导入的模板文件');
            $(".excelBox h2").html("导入商品订单数据");
            $("#fileField").attr("value","");
            $(".excelBox").hide();
        },
        downloadTemplate: function(){
            window.open(tytTool.getRootPath_web() + "/goods/userBuyGoodsImport/downloadTemplate", '_blank')
        }
    }
}

var MyData = joinData.myData;
var Arr = joinData.arr;
var myAjax = joinData.ajaxfn;
var optFn = joinData.optFn;

var _url = '';
var wdlHref = window.document.location.href;

optFn.loadList();
