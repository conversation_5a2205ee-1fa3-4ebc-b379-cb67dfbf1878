/*
    userInfoAll.js for userInfoAll.hmtl
    author: hey<PERSON>an
*/
var task = {
    myData: {
        pageSize: 20,
        currentPage: 1,
        allUserFlag:['1##系统全部用户','0##自定义用户'],
        taskStatus:['0##未开始','1##进行中','2##已暂停','3##已结束']
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'get',
            otherData: '',
            data: ''
        },
        status: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data, _currentPage) {
            if (data.code == 200) {
                var data_list = data.data.list,
                    bean  = data.data.pageBean,
                    currentPage = bean.currentPage,
                    maxPage = bean.maxPage,
                    rowCount = bean.rowCount;
                taskM.currentPage = currentPage;
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams, 'taskO.listOpt')
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams, 'taskO.listOpt')
                }
                //正式载入数据
                var rule = {
                    list: [
                        '任务ID##id',
                        '任务标题##taskTitle',
                        '模板名称##templateName',
                        '开始时间##taskStartTime##Fn::tytTool.timeNull({{taskStartTime}},"yyyy-MM-dd hh:mm")',
                        '结束时间##taskEndTime##Fn::tytTool.timeNull({{taskEndTime}},"yyyy-MM-dd hh:mm")',
                        '创建时间##ctime##Fn::tytTool.timeNull({{ctime}},"yyyy-MM-dd hh:mm")',
                        '发送用户##allUserFlag##Fn::tytTool.switchItem({{allUserFlag}},taskM.allUserFlag)',
                        '任务状态##taskStatus##Fn::tytTool.switchItem({{taskStatus}},taskM.taskStatus)',
                        '操作人##operator',
                        '操作##opt'
                    ],
                    item: {
                        taskStatus: {
                            dom: [
                                '0==<a class="info_opt" onclick="taskO.checkJoin({{id}})">查看</a>',
                                '1==<a class="info_opt" onclick="taskO.changeStatus({{id}},{{taskStatus}})">暂停</a><span class="line_span">|</span><a class="info_opt" onclick="taskO.checkJoin({{id}})">查看</a>',
                                '2==<a class="info_opt" onclick="taskO.changeStatus({{id}},{{taskStatus}})">启用</a><span class="line_span">|</span><a class="info_opt" onclick="taskO.checkJoin({{id}})">查看</a>',
                                '3==<a class="info_opt" onclick="taskO.checkJoin({{id}})">查看</a>'
                            ]
                        }
                    }
                }
                var tmp = tytTool.eachList(data_list, rule)
                $("#infoAll").empty().append(tmp.str)
                tytTool.bindHidePhone();
                comO.colResizable();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 3000)
                }
            }
        },
        changeStatusData: function (data) {
            if (data.code == 200) {
                taskO.listOpt(taskM.currentPage);
                tytTool.toastShow(data.msg,3000)
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 3000)
                }
            }
        }
        
    },
    opt: {
        loadList: function() {
            tytTool.loadCheckTab(comM.task, {})
            var pageArr = {
                "totalPages": 1,
                "pageSize": taskM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": taskO.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) {
            var pageNo =  currentPage == '' ? 1 : currentPage;
            taskA.common.otherUrl = `${path}/upgrade/task/list`
            taskA.common.otherData = pageNo;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            taskA.common.data = $.extend({}, arr, {
                "pageNo": pageNo,
                "pageSize": taskM.pageSize
            })
            tytTool.ajaxFn(taskA.common, taskJ.listData)
        },
        changeStatus: function(id,status) { //改变任务状态请求入口
            
            taskA.status.otherUrl = `${path}/upgrade/task/changeStatus`
            taskA.status.data = {
                "id": id,
                //status: 0 未开始 1 进行中 2 已暂停 3 已结束
                //status为0(未开始)/2(已暂停)时候入参需为1-启用
                "taskStatus": (NO(status) == 0 || NO(status) == 2) ? 1 : 0,
            }
            tytTool.ajaxFn(taskA.status, taskJ.changeStatusData)
        },
        checkJoin: function (id) { //查看参与用户的跳转
            var _url = '../../html/upgrade/updataJoin.html?id=' + id
            tytTool.openWinAuto(_url, 'checkJoin' + id, 1200, 600)
        },
        createTask: function () { //创建任务页面的跳转
            tytTool.openWinAuto('../../html/upgrade/updataSet.html', 'createTask', 1200, 600)
        }
    }
}

var taskM = task.myData;
var taskA = task.arr;
var taskJ = task.ajx;
var taskO = task.opt;
taskO.loadList();