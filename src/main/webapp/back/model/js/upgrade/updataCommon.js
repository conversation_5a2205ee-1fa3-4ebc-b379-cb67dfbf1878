/*
    userCommon.js for model/html/upgrade/*.hmtl
    author: hey<PERSON>an
*/
var path = tytTool.getRootPath_web(),
    params = tytTool.parseSearch();
var common = {
    myData: {
        task: {//查询项配置-升级任务管理管理
            box: '.searchUl=>li',
            list: [{
                    label: '任务标题：',
                    attr: ['tname##taskTitle', 'maxlength##50'],
                },{
                    label: '状态：##select',
                    attr: ['tname##taskStatus'],
                    option: ['请选择##', '未开始##0', '进行中##1', '已暂停##2', '已结束##3']
                },
                {
                    label: '操作人：',
                    attr: ['tname##operator', 'maxlength##50'],
                }
            ]
        },
        join: {//查询项配置-参加用户升级明细
            box: '.searchUl=>li',
            list: [{
                    label: '手机号：',
                    attr: ['tname##userPhone', 'maxlength##11', 'onblur##comO.inpBlurValid($(this),\'basePhone\')'],
                }, {
                    boxAttr: ['class##longWidth'],
                    label: '车主版升级状态：##select',
                    attr: ['tname##carOwnerUpgradeStatus'],
                    option: ['请选择##', '已完成##1', '未完成##0']
                }, {
                    boxAttr: ['class##longWidth'],
                    label: '货站版升级状态：##select',
                    attr: ['tname##cargoStationUpgradeStatus'],
                    option: ['请选择##', '已完成##1', '未完成##0']
                }
            ]
        }
    },
    arr: {},
    ajx: {},
    opt: {
        colResizable: function() {
            $(".JCLRgrips").remove()
            var tabWidth = $(".tableBox").parents('.dataTable').width()
            $(".tableBox").width(tabWidth);
            tytTool.colResizable($(".tableBox"));
            $(".JCLRgrips").width(tabWidth)
        },
        formReset: function (fn) {
            $("#tytCity").val('').removeAttr('data-val')
            tytTool.formReset($('.searchUl'),fn)
        },
        startPicker: function(end,arr) { //时间控件--开始
            var array = NO(arr) == '' ? {} : arr
            return WdatePicker($.extend({},{
                dateFmt: 'yyyy/MM/dd HH:mm',
                maxDate: `#F{$dp.$D('${end}')}`,
                onpicking: comO.onpicking,
                oncleared: comO.oncleared
            },array));
        },
        endPicker: function(start,arr) { //时间控件--结束
            var array = NO(arr) == '' ? {} : arr
            return WdatePicker($.extend({},{
                dateFmt: 'yyyy/MM/dd HH:mm',
                minDate: `#F{$dp.$D('${start}')}`,
                onpicking: comO.onpicking,
                oncleared: comO.oncleared
            },array));
        },
        onpicking: function(dp) { //时间控件--选择后触发
            var val = tytTool.NullObj(dp.cal.getNewDateStr());
            var date = new Date(val),
                timeString = Date.parse(date);
            $(dp.el).attr('pick-time', timeString)
        },
        oncleared: function(dp) { //时间控件--清除操作触发
            $(dp.el).removeAttr('pick-time')
        },
        inpBlurValid: function(_this, _reg) {
            _this.val(_this.val())
            if (_reg != '' && !tytTool.validateStr(_reg, _this.val())) {
                _this.val('')
            }
        }
    }
}

var comM = common.myData;
var comA = common.arr;
var comJ = common.ajx;
var comO = common.opt;