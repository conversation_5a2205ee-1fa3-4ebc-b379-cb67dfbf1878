/*
 * @Author: fly
 * @Date: 2023-01-12 15:02:58
 * @LastEditTime: 2024-04-19 14:10:28
 * @LastEditors: Please set LastEditors
 * @FilePath: /tyt_manage_new/src/main/webapp/back/model/js/compensate/common.js
 * @Description: 
 */
var market = tytTool.getRootPath_web();
var common = {
    myData: {
        data: {
            plaintOrder: { //下游-订单管理-待申报订单
                box: '.searchUl=>li',
                list: [{
                        boxAttr: ['class##timeBox'],
                        label: '上报日期：',
                        attr: ['tname##beginExTime', 'id##beginExTime', 'readonly##readonly', 'onclick##commonO.startPicker("endExTime")'],
                        splitDom: ' — <input type="text" tname="endExTime" id="endExTime" readonly="readonly" onclick="commonO.endPicker(\'beginExTime\')">',
                    },
                    {
                        label: '订金编号：',
                        attr: ['tname##tsOrderId'],
                    },
                    {
                        label: '发货人账号：',
                        attr: ['tname##goodsPhone'],
                    },
                    {
                        label: '车主账号：',
                        attr: ['tname##carPhone'],
                    },
                    {
                        label: '补偿对象：##select',
                        attr: ['tname##compensateTarget'],
                        option: ['全部##', '车方##1', '货方##2']
                    },
                    {
                        label: '补偿类型：##select',
                        attr: ['tname##compensateType'],
                        option: ['全部##', '订金补偿##1','放空补偿##2','压车补偿##3','距离加价补偿##4','吨位加价补偿##5','拉跑货补偿##6','倒卖货补偿##7','倒送货补偿##8','防溢出补偿##9','其他##10']
                    },
                    {
                        label: '补偿方式：##select',
                        attr: ['tname##compensateMethod'],
                        option: ['全部##', '订金减免券##1', '现金红包##2']
                    },
                    {
                        label: '补偿原因：',
                        attr: ['tname##compensateReason'],
                    },
                    {
                        boxAttr: ['class##timeBox'],
                        label: '最新审核时间：',
                        attr: ['tname##beginMTime', 'id##beginMTime', 'readonly##readonly', 'onclick##commonO.startPicker("endMTime")'],
                        splitDom: ' — <input type="text" tname="endMTime" id="endMTime" readonly="readonly" onclick="commonO.endPicker(\'beginMTime\')">',
                    },
                    {
                        label: '审核人：',
                        attr: ['tname##userName'],
                    },
                    {
                        label: '审核状态：##select',
                        attr: ['tname##type'],
                        option: ['全部##', '一级审核中##0,1', '二级审核中##0,2','三级审核中##0,3','审核通过##1']
                    },
                    
                ]
            },
        },
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            contentType: 'application/json; charset=utf-8',
            data: ''
        },
    },
    ajx: {},
    opt: {
        inpBlurValid: function (_this, _reg) {
            _this.val(_this.val())
            if (_reg != '' && !tytTool.validateStr(_reg, Number(_this.val()))) {
                _this.val('')
            }
        },
        lookBigImg: function (_this, Small) { //base图片窗口浏览
            var size = NO(Small) != '' ? [600, 300] : [1200, 600]
            var _img = _this.attr('data-url') || _this.attr('src');
            var img = new Image(),
                isTop = (window.screen.availHeight - 30 - size[1]) / 2,
                isLeft = (window.screen.availWidth - 10 - size[0]) / 2;
            img.style = '-webkit-user-select:none;margin:auto;display:block;text-align:center;vertical-align: middle;'
            img.src = _img;
            var newWin = window.open("", Small, 'width=' + size[0] + ',height=' + size[1] + ',top=' + isTop + ',left=' + isLeft + ',status=,toolbar=no,menubar=no,location=no,resizable=no,scrollbars=yes,titlebar=no');
            newWin.document.write(img.outerHTML);
            newWin.document.body.style = 'margin:0px;background:#0e0e0e;'
            newWin.document.title = "查看图片"
            newWin.document.close();
        },
        orderDetail: function (_id) {
            tytTool.openWinAuto('../../html/goodsMessage/complaintDetail.html?id=' + _id, 'detail_' + _id, 1200, 600)
        },
        startPicker: function (endTime) {
            //时间控件--开始
            return WdatePicker({
                maxDate: `#F{$dp.$D('${endTime}')}`,
                dateFmt: 'yyyy-MM-dd',
                onpicking: function (dp) {
                    var val = tytTool.NullObj(dp.cal.getNewDateStr());
                    var date = new Date(val),
                        timeString = Date.parse(date);
                    $(dp.el).attr('data-time', timeString);
                },
                oncleared: function (dp) {
                    $(dp.el).removeAttr('data-time');
                },
            });
        },
        endPicker: function (startTime) {
            //时间控件--结束
            return WdatePicker({
                minDate: `#F{$dp.$D('${startTime}')}`,
                dateFmt: 'yyyy-MM-dd',
                onpicking: function (dp) {
                    var val = tytTool.NullObj(dp.cal.getNewDateStr());
                    var date = new Date(val),
                        timeString = Date.parse(date);
                    $(dp.el).attr('data-time', timeString);
                },
                oncleared: function (dp) {
                    $(dp.el).removeAttr('data-time');
                },
            });
        },
        
    }
}


var commonM = common.myData;
var commonA = common.arr;
var commonJ = common.ajx;
var commonO = common.opt;
tytTool.loadCheckTab(commonM.data.plaintOrder, {})