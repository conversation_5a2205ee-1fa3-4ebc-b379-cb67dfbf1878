/*
    getCode.js
    author: hey<PERSON><PERSON>
*/
var time;
var code = {
    myData: {
        second: 60,
        placeholder: '获取验证码'
    },
    arr: {
        getCode: {
            url: '',
            otherUrl: '',
            type: 'post',
            data: ''
        }
    },
    ajx: {
        codeData: function(req, obj) { //获取短信验证码成功
            if (req.code == 200) {
                //倒计时展示
                //codeO.countDown(obj)
            } else {
                if(NO(req.msg) != '') {
                    tytTool.toastShow(req.msg, 3000)
                }
            }
        }
    },
    opt: {
        check: function (params) {
            var flag = true;
            var phone = NO($("#phoneNumber").val()),
                code = NO($("#veryCodeId").val()),
                pwd = NO($("#password").val());

            if (phone == '') {
                tytTool.toastShow('用户账号不能为空', 3000)
                flag = false;
            } else if (code == '') {
                tytTool.toastShow('veryCode获取失败', 3000)
                flag = false;
            } else if (pwd == '') {
                tytTool.toastShow('用户密码不能为空', 3000)
                flag = false;
            }
            return flag;
        },
        getSecond: function(_this) { //发起获取短信验证码
            if(!codeO.check()) {
                return false;
            }
            if (_this.hasClass('noClick')) {
                return false;
            }
            //停止预设的定时器
            clearInterval(time)

            var phone = NO($("#phoneNumber").val()),
                code = NO($("#veryCodeId").val()),
                pwd = NO($("#password").val());
            codeA.getCode.otherUrl = tytTool.getRootPath_web() + '/admin/verifyCode/send';
            codeA.getCode.otherData = _this
            codeA.getCode.data = {
                "cellPhone": phone,
                "veryCode": code,
                "password": pwd,
                "platId": 0,
            }
            tytTool.ajaxFn(codeA.getCode, codeJ.codeData);
            //后续修改,接口等待会出现倒计时延长的情况;
            codeO.countDown(_this)
        },
        countDown: function(obj) { //倒计时的处理以及点击的恢复
            obj.addClass('noClick');
            var count = codeM.second;
            time = setInterval(function() {
                count--;
                obj.val(count + 's后重新获取').addClass('secondClass')
                if (count == 0) {
                    clearInterval(time)
                    obj.val('获取验证码').removeClass('secondClass').removeClass('noClick')
                }
            }, 1000);

        },
        init: function() {
            $('.verificationCode').bind('input propertychange', function() {
                var v = $(this).val();
                if (v.length >= 6) {
                    $(this).val(v.slice(0, 6))
                }
            })
        }
    },
}

var codeM = code.myData;
var codeA = code.arr;
var codeJ = code.ajx;
var codeO = code.opt;
codeO.init()