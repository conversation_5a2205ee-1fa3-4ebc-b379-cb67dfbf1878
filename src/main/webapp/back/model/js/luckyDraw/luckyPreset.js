/*
    luckyPreset.js for luckyPreset.html;
    author hey<PERSON><PERSON>
*/
var pre = {
    myData: {
        pageSize: 30,
        prizeType:['1##会员券','2##广告券']
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        clear: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data) { //列表请求成功
            if (data.code == 200) {
                var _data = data.data,
                    data_list = _data.list,
                    data_bean = _data.pageBean;
                $("#luckyPreset tr:not('.tableTh')").remove();
                var currentPage = data_bean.currentPage,
                    maxPage = data_bean.maxPage,
                    rowCount = data_bean.rowCount;
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams, 'preO.listOpt')
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams, 'preO.listOpt')
                }
                //正式载入数据
                var _html = '';
                if (data_list && data_list.length > 0) {
                    $.each(data_list, function(index, el) {
                        _html += `<tr>
                            <td>${tytTool.NullObj(el.id)}</td>
                            <td>${tytTool.NullObj(el.userId)}</td>
                            <td class="phoneHide">${tytTool.NullObj(el.cellphone)}</td>
                            <td>${tytTool.NullObj(el.activityName)}</td>
                            <td>${tytTool.NullObj(el.activityId)}</td>
                            <td>${tytTool.switchItem(el.prizeType,preM.prizeType)}</td>
                            <td>${tytTool.NullObj(el.prizeName)}</td>
                            <td>${tytTool.timeNull(el.ctime,'yyyy-MM-dd hh:mm:ss')}</td>
                        </tr>`
                    });
                    $("#luckyPreset").append(_html);
                } else {
                    _html = '<tr><td colspan="' + $(".tableTh th").length + '">暂无查询数据</td></tr>'
                    $("#luckyPreset").append(_html);
                }
                tytTool.colResizable($(".tableBox"))
                tytTool.bindHidePhone()

            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            }
        },
        clearData: function(data) { //清空成功
            if (data.code == 200) {
                commonO.operate('close')
                var mag = data.msg || '清空成功！'
                tytTool.toastShow(mag, 1500);
                setTimeout(() => {
                    tytTool.reloadWin()
                }, 2000);
            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            }
        }
    },
    opt: {
        loadList: function() { //列表分页发起
            var pageArr = {
                "totalPages": 1,
                "pageSize": preM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": preO.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) { //列表入口请求
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            var page = {
                "currentPage": currentPage,
                "pageSize": preM.pageSize
            }
            preA.common.data = $.extend({}, arr, page);
            preA.common.otherUrl = manage + '/luckdraw/preset/winner/list';
            tytTool.ajaxFn(preA.common, preJ.listData)
        },
        clearList: function (params) { //清空指定预设中奖名单
            if(commonO.checkSelect('#activeName')) {
                return false;
            }
           preA.clear.otherUrl = manage + '/luckdraw/preset/winner/clear';
           preA.clear.data = {
               "activityId": tytTool.NullObj($("#activeName").val())
           }
           tytTool.ajaxFn(preA.clear, preJ.clearData)

        },
		excelExportModel: function() { //下载预设中奖名单模板
			tytTool.openPostWindow(`${manage}/luckdraw/preset/winner/excelExportModel`, '');
		},
		fileInfo:function(source, _this) { //file选择
		  var f = source.files[0];
		  var name = f.name;
		  $(".luckOutLook .alertContent").html(name).attr('data-doc',name);
		},
		uploadFile:function(){ //上传file请求
            if(commonO.checkSelect('#activeName')) {
                return false;
            }
			if (tytTool.NullObj($(".alertContent").attr('data-doc')) == "") {
				tytTool.toastShow("请选择需上传的文件",1500);
                return false;
			}
			$(".outBgColor,.luckOutLook").hide();
			var formData = new FormData();
			formData.append("fileField",$("#fileField")[0].files[0]);
			formData.append("activityId",tytTool.NullObj($("#activeName").val()));
			formData.append("activityName",tytTool.NullObj($("#activeName option:selected").html()));
			$.ajax({
		        type: "POST",
		        url: manage + "/luckdraw/preset/winner/importList",
		        data: formData,
		        contentType: false,
		        /**
		        * 必须false才会避开jQuery对 formdata 的默认处理
		        * XMLHttpRequest会对 formdata 进行正确的处理
		        */
		        processData: false,
		        async : false, 
		        dataType:"json",
		        success: function (data) {
		        	if(data.code == 200){
                        tytTool.toastShow(data.msg,2500);
                        setTimeout(function(){
                            tytTool.reloadWin();
                        },1000)
		        	} else {
                        var mag = data.msg || '导入失败，请仔细核对！'
						tytTool.toastShow(mag,3000);
                        preO.resetFile()
		        	}
		        },
		        error: function (xhr, status) {
                    tytTool.toastShow('仅支持csv文件',1500);
                    preO.resetFile()
		        }
		    });
        },
        resetFile: function (params) { //重置file记录
            $(".outBgColor,.luckOutLook").hide();
            $(".luckOutLook .alertContent").html('请点击“浏览”选择需要导入的模板文件，只支持csv文件');
            $("#fileField").attr("value","");
            $(".luckOutLook .alertContent").removeAttr('data-doc')
            $("#activeName option:first").attr('selected',true)
        }
    }
}

var preM = pre.myData;
var preA = pre.arr;
var preJ = pre.ajx;
var preO = pre.opt;

//控制权限和列表加载的顺序
$.when(commonO.activityList()).done(function() {
    preO.loadList()
}).fail(function() {
    preO.loadList()
})