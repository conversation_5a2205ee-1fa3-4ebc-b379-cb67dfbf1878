/*
    luckyList.js for luckyList.html;
    author hey<PERSON>an
*/
var luck = {
    myData: {
        pageSize: 30
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
    },
    ajx: {
        listData: function(data, _currentPage) { //列表请求成功
            if (data.code == 200) {
                var _data = data.data,
                    data_list = _data.list,
                    data_bean = _data.pageBean;
                $("#luckyList tr:not('.tableTh')").remove();
                var currentPage = data_bean.currentPage,
                    maxPage = data_bean.maxPage,
                    rowCount = data_bean.rowCount;
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams, 'luckO.listOpt')
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams, 'luckO.listOpt')
                }
                //正式载入数据
                var _html = '';
                if (data_list && data_list.length > 0) {
                    $.each(data_list, function(index, el) {
                        _html += `<tr>
                            <input type="hidden" tname="limitTimesType" value="${tytTool.NullObj(el.limitTimesType)}">
                            <input type="hidden" tname="limitTimes" value="${tytTool.NullObj(el.limitTimes)}">
                            <td tname="id">${tytTool.NullObj(el.id)}</td>
                            <td tname="activityName">${tytTool.NullObj(el.activityName)}</td>
                            <td tname="startTime">${tytTool.timeNull(el.startTime,'yyyy-MM-dd')}</td>
                            <td tname="endTime">${tytTool.timeNull(el.endTime,'yyyy-MM-dd')}</td>
                            <td>${tytTool.timeNull(el.utime,'yyyy-MM-dd hh:mm')}</td>
                            <td tname="rule">${tytTool.NullObj(el.rule)}</td>
                            <td>
                                <a class="info_detail ver_block" onclick="luckO.changeActie($(this))">修改</a>
                            </td>
                        </tr>`
                    });
                    $("#luckyList").append(_html);
                } else {
                    _html = '<tr><td colspan="' + $(".tableTh th").length + '">暂无查询数据</td></tr>'
                    $("#luckyList").append(_html);
                }
                tytTool.colResizable($(".tableBox"))

            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            }
        },
        subActiveData: function(data) { //新增、修改成功
            if (data.code == 200) {
                commonO.operate('close')
                tytTool.toastShow(data.msg, 1500);
                luckO.listOpt(1)
            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            }
        }
    },
    opt: {
        loadList: function() { //列表分页入口
            var pageArr = {
                "totalPages": 1,
                "pageSize": luckM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": luckO.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) { //列表请求入口
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            luckA.common.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            var page = {
                "currentPage": currentPage,
                "pageSize": luckM.pageSize
            }
            luckA.common.data = $.extend({}, arr, page)
            luckA.common.otherUrl = manage + '/luckdraw/activity/info/list';
            tytTool.ajaxFn(luckA.common, luckJ.listData)
        },
        changeActie: function (_this) { //打开修改框,传输当前行的数据
            commonO.operate('open','change')
            var arr = tytTool.htmlGetTname(_this.parents('tr'))
            //limitTimesType字段--总共: 1 ; 每天: 0
            var _eq = Number(arr.limitTimesType) == 1 ? 1 : 0
            $(".ruleBox i").removeClass('checkRadio');
            $(".ruleBox .checkRule").eq(_eq).find('i').addClass('checkRadio');
            $(".ruleBox .checkRule").eq(_eq).append('<input class="inpRule" type="text" tname="limitTimes" maxlength="3" onblur="commonO.inpBlurValid($(this),\'integer\')"><em>次</em>')
            tytTool.tnameGetHtml($(".luckBox"),arr);
        },
        submitActie: function (subType) { //新增、修改提交入口
            var arr = tytTool.htmlGetTname($(".luckBox"))
            if(!commonO.checkValue()) {
                return false
            }
            if(subType == 'add') { //新增提交入口
                delete arr.id;
            } else if(subType == 'change') { //修改提交入口
                arr.id = Number(arr.id)
            }
            luckA.common.data = $.extend({},arr,{
                "startTime": Date.parse(arr.startTime),
                "endTime": Date.parse(arr.endTime),
                "limitTimes": Number(arr.limitTimes),
                "limitTimesType": Number(arr.limitTimesType)
            })
            luckA.common.otherUrl = manage + '/luckdraw/activity/info/save';
            tytTool.ajaxFn(luckA.common, luckJ.subActiveData)
        },
        radioCheck:function (_this,_type) { //参与规则按钮切换
            var fa = _this.parents('.ruleBox');
            fa.find('.inpRule,em').remove();
            _this.parents('.checkRule').append('<input class="inpRule" type="text" tname="limitTimes" maxlength="3" onblur="commonO.inpBlurValid($(this),\'integerAll\')"><em>次</em>')
            if(_this.hasClass('checkRadio')) {
                fa.find('i').removeClass('checkRadio')
                fa.find('.ruleHidden').val('');
                fa.find('.inpRule,em').remove();
            } else {
                fa.find('i').removeClass('checkRadio')
                _this.addClass('checkRadio')
                fa.find('.ruleHidden').val(_type)
            }
        }
    }
}

var luckM = luck.myData;
var luckA = luck.arr;
var luckJ = luck.ajx;
var luckO = luck.opt;

luckO.loadList()

//控制权限和列表加载的顺序
// $.when(userRole).done(function() {
//     luckO.loadList()
// }).fail(function() {
//     luckO.loadList()
// })