var website = tytTool.getRootPath_web();
var domIndex = tytTool.parseSearch().domIndex;

var all = {
    myData: {},
    arr: {},
    ajx: {
		//提交
		submitUpgrade:function() {
			if ($("#savaBtn").attr('data-clickon') == 1) {
				return false;
			}
			// 获取图片
			// console.log($(".uploaderFiles .file img")[0])
			let img=''
			let bloon = true;
			if($(".uploaderFiles .file img").length===0){
				tytTool.toastShow('请上传图片',2500);
				bloon = false;
				return false;
			} else {
				img = $(".uploaderFiles .file img")[0].src
			}
			var GetJsonData = {
				"id": Number(tytTool.parseSearch().id),
				"templateName": $("#templateName").val(), // 模板名称
				"cargoStationAndroidUrl": $("#androidGoodAddress").val(), //货站版android链接
				"cargoStationIosUrl": $("#iosGoodAddress").val() || '', // 货站版iOS链接
				"carOwnerAndroidUrl": $("#androidCarAddress").val(), // 车主版android链接
				"carOwnerIosUrl": $("#iosCarAddress").val(), //  车主版iOS链接
				// "templatePic": img //  图片
			}
			console.log(img)
			if(img.indexOf("http") == 0){
				GetJsonData.templatePic = ''
			} else {
				GetJsonData.templatePic = img
			}
			$(".templateClass").each(function (ind,ele) {
                if($(this).val() == '') {
					tytTool.toastShow('请填写完整信息',2500);
					bloon = false;
                    console.log($(this));
                    return false;
				}
			})
			if(!bloon) {
                return false;
            }
			$.ajax({
				url: '' + website + '/upgrade/template/update',
				type: 'post',
				contentType: 'application/json; charset=utf-8',
				data: JSON.stringify(GetJsonData),
				dataType: 'json',
				success: function(data) {
					if (data.code == 200) {
						$("#savaBtn").attr('data-clickon', 1)
						tytTool.toastShow(data.msg,2500);
						setTimeout(function() {
							tytTool.reloadUpWin()
					   }, 2000);
					}
				},
				error: function(message) {
					console.log(message)
				}
			});
		},	
		//回显
		checkUpgrade:function() {
			//清空回显状态
			$("#taskInp input,#taskStyle input").removeAttr('checked');
			$("#taskInp").attr('data-clienttype', '');
			$("#taskStyle").attr('data-upgrademode', '');
			$.ajax({
				url: '' + website + '/upgrade/template/details',
				type: 'get',
				dataType: 'json',
				data: {
					'id': tytTool.parseSearch().id
				},
				success: function(data) {
					console.log(data)
					var dataTab = data.data;
					console.log(dataTab)
					tytTool.tnameGetHtml($(".ideaFinal"),dataTab)
					// 图片回显
					if(dataTab.templatePic) {
						var _ind = 1
						if (domIndex == 303) { //编辑
							var _li = '<li class="file fl">\
								<i onclick="comO.deleteImg($(this))"></i>\
								<img onclick="comO.lookBigImg($(this))" src="'+ dataTab.templatePic +'" alt="">\
								<p class="imgTitle">'+ _ind +'</p>\
							</li>';
						} else if (domIndex == 527) { //查看
							var _li = '<li class="file fl">\
								<img onclick="comO.lookBigImg($(this))" src="'+ dataTab.templatePic +'" alt="">\
								<p class="imgTitle">'+ _ind +'</p>\
							</li>';
						}
						$(".uploaderFiles").prepend(_li)
                   		$(".updataBtnBox").hide()
                    }
				},
				error: function(message) {
					console.log(message)
				}
			});
		},
        
    },
    opt: {
		loadDom:function() {
			let edit_page = `<input id="savaBtn" class="button" type="button" onclick="recordJ.submitUpgrade()" value="保存">
			<input  id="returnBtn" class="button" type="button" onclick="window.close();" value="取消">`;
			let look_page = `<input  id="returnBtn" class="button" type="button" onclick="window.close();" value="关闭">`;
			if (domIndex == 303) { //编辑
				$(".currentPage,title").html('编辑升级模板');
				$("#divButton").append(edit_page);
				recordJ.checkUpgrade()
			} else if (domIndex == 527) { //查看
				console.log('1111')		
				$(".currentPage,title").html('查看升级模板');
				$(".ideaFinal input,.ideaFinal textarea").attr('disabled', true)
				$("#divButton").append(look_page);
				recordJ.checkUpgrade()
			}
		}
    }
}

var recordM = all.myData;
var recordA = all.arr;
var recordJ = all.ajx;
var recordO = all.opt;
recordO.loadDom();
