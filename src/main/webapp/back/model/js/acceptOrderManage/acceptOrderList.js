var market = tytTool.getRootPath_web('owers_boss');
var accept = {
    myData: {
        pageSize: 30,
        isFinalList: ['0##否', '1##是', ],
        isEffectiveList: ['0##否', '1##是', '2##待定', '3##待处理'],
        acceptOrder: { //下游-订单管理-待申报订单
            box: '.searchUl=>li',
            list: [{
                    label: '客户手机号：',
                    attr: ['tname##consumerPhone', 'maxlength##11', 'onblur##acceptO.inpBlurValid($(this),\'basePhone\')', 'placeholder##请输入手机号'],
                }, {
                    label: 'ID：',
                    attr: ['tname##id', 'maxlength##50', 'onblur##acceptO.inpBlurValid($(this),\'number\')', 'placeholder##请输入ID'],
                }, {
                    label: '保险类型：##select',
                    attr: ['tname##type'],
                    option: ['请选择##', '单程货运险##4', '全年货运险##5','车险##7','雇主责任险##8']
                }, {
                    label: '保单状态：##select',
                    attr: ['tname##status', 'class##status'],
                    option: ['请选择##', '待支付##0', '生效中##1', '已失效##2', '已退保##3']
                },
                {
                    label: '保险公司：##select',
                    attr: ['tname##company', 'class##status'],
                    option: ['请选择##', '阳光财产保险股份有限公司##阳光财产保险股份有限公司', '中国太平洋财产保险股份有限公司##中国太平洋财产保险股份有限公司', '中国平安财产保险股份有限公司##中国平安财产保险股份有限公司', '中国人民财产保险股份有限公司##中国人民财产保险股份有限公司', '国任财产保险股份有限公司##国任财产保险股份有限公司']
                }, {
                    boxAttr: ['class##timeBox'],
                    label: '订单提交开始时间：',
                    attr: ['tname##startCtime', 'id##complaintBeginTime', 'readonly##readonly', 'onclick##acceptO.startPicker("complaintEndTime")', 'placeholder##请选择时间'],
                }, {
                    boxAttr: ['class##timeBox'],
                    label: '订单提交结束时间：',
                    attr: ['tname##endCtime', 'id##complaintEndTime', 'readonly##readonly', 'onclick##acceptO.endPicker("complaintBeginTime")', 'placeholder##请选择时间'],
                },
                {
                    label: '新旧货物：##select',
                    attr: ['tname##contentNewOld', 'class##contentNewOld'],
                    option: ['全部##', '全新##全新', '非全新##非全新', '暂无##暂无' ]
                },
                {
                    label: '跟踪状态##select',
                    attr: ['tname##backStatus', 'class##backStatus'],
                    option: ['全部##', '未跟踪##0', '已跟踪##1']
                },
            ]
        },
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'get',
            otherData: '',
            data: ''
        },
        type: {
            '1': '华泰货运险',
            '2': '人保货运险',
            '3': '平安货运险',
            '4': '单程货运险',
            '5': '全年货运险',
            '7': '车险',
            '8': '雇主责任险'
        }
    },
    ajx: {
        listData: function (data, _currentPage) {
            if (data.code == 200) {
                var data_list = data.data ? data.data.list : [];
                tytTool.pageFun({
                    total: data.data ? data.data.total : 0,
                    size: acceptM.pageSize,
                    current: _currentPage,
                    pageCallback: 'acceptO.listOpt'
                })
                $.each(data_list, function (i, item) {
                    if (item.status != 1) {
                        item.effectiveDay = ''
                    }
                });
                var rule = {
                    list: [
                        'ID##id',
                        '注册手机号##cellPhone##Fn::acceptO.optPhone({id:{{id}}, type:"cellPhone"} )',
                        '投保人姓名##applicantName',
                        '投保人电话号##applicantPhone##Fn::acceptO.optPhone({id:{{id}}, type:"applicantPhone"} )',
                        '订单提交时间##ctime##Fn::tytTool.timeNull({{ctime}},"yyyy-MM-dd hh:mm")',
                        '车头牌照号##headNo',
                        '挂车牌照号##tailNo',
                        '车头车架号##vin',
                        '被保人姓名##insuredName',
                        '被保人电话号##insuredPhone##Fn::acceptO.optPhone({id:{{id}}, type:"insuredPhone"} )',
                        '新旧货物##contentNewOld',
                        '保险类型##typeStr',
                        '保险公司##company',
                        '剩余有效期##effectiveDay',
                        '保单状态##statusStr',
                        '跟踪状态##backStatusVO',
                        '操作width160##opt'
                    ],
                    item: {
                        // cellPhone: {
                        //     attr: ['class==phoneHide']
                        // },
                        // applicantPhone: {
                        //     attr: ['class==phoneHide']
                        // },
                        // insuredPhone: {
                        //     attr: ['class==phoneHide']
                        // }
                    },
                    opt: {
                        fn: `acceptO.optType({id:{{id}}, type:{{type}},statusStr:{{statusStr}},userId:{{userId}} } )`,
                    },
                }
                var tmp = tytTool.eachList(data_list, rule)
                $("#goodsPlaint").empty().append(tmp.str)
                tytTool.bindHidePhone();
                tytTool.colResizableExtend('#goodsPlaint')
            } else {
                if (NO(data.message) != '') {
                    tytTool.toastShow(data.message, 4000)
                }
            }
        }
    },
    opt: {
        inpBlurValid: function (_this, _reg) {
            _this.val(_this.val())
            if (_reg != '' && !tytTool.validateStr(_reg, Number(_this.val()))) {
                _this.val('')
            }
        },
        loadList: function () {
            var pageArr = {
                "pageNum": 1,
                "pageSize": acceptM.pageSize,
                "funName": acceptO.listOpt,
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function (currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            acceptA.common.otherUrl = `${market}/transport/insurance/get/list`
            acceptA.common.otherData = currentPage;
            acceptA.common.data = $.extend({}, arr, {
                "pageNum": currentPage,
                "pageSize": acceptM.pageSize,
                'ssoInfo': tytTool.getCookie('ssoInfo')
            })
            tytTool.ajaxFn(acceptA.common, acceptJ.listData)
        },
        optType: function (data) {
            let item = JSON.stringify(data)
            let str = ''
            // 已失效不能编辑
            if (data.statusStr == '已失效') {
                str = `<a class="info_opt" onclick='acceptO.orderDetail(${item},0)'>查看</a>`
            } else {
                str = `<a class="info_opt" onclick='acceptO.orderDetail(${item},1)'>编辑</a>   <a class="info_opt" onclick='acceptO.orderDetail(${item},0)'>查看</a>`
            }
            console.log(data.userId == '', data.userId)
            if(data.userId == '') { // 后端新增订单
                str += `  <a class="info_opt" onclick='acceptO.addOrder(${item},0)'>再来一单</a>`
            }
            return str
        },
        optPhone: function(data){
            return  `<a class="blue" onclick="phoneSoHfn($(this),${data.id},'收单管理','special','${data.type}')"'>查看电话</a>`
        },
        orderDetail: function (data, pageType) {
            if (pageType == 1) {
                tytTool.openWinAuto(`../../html/acceptOrderManage/acceptOrderEditor.html?id=${data.id}&type=${data.type}`,  `name_${data.id}`, 1200, 600)
            } else {
                tytTool.openWinAuto(`../../html/acceptOrderManage/acceptOrderDetail.html?id=${data.id}&type=${data.type}`, `name_${data.id}`, 1200, 600)
            }

        },
        startPicker: function (endTime) {
            //时间控件--开始
            return WdatePicker({
                maxDate: `#F{$dp.$D('${endTime}')}`,
                dateFmt: 'yyyy-MM-dd',
                onpicking: function (dp) {
                    var val = tytTool.NullObj(dp.cal.getNewDateStr());
                    var date = new Date(val),
                        timeString = Date.parse(date);
                    $(dp.el).attr('data-time', timeString);
                },
                oncleared: function (dp) {
                    $(dp.el).removeAttr('data-time');
                },
            });
        },
        endPicker: function (startTime) {
            //时间控件--结束        
            return WdatePicker({
                minDate: `#F{$dp.$D('${startTime}')}`,
                dateFmt: 'yyyy-MM-dd',
                onpicking: function (dp) {
                    var val = tytTool.NullObj(dp.cal.getNewDateStr());
                    var date = new Date(val),
                        timeString = Date.parse(date);
                    $(dp.el).attr('data-time', timeString);
                },
                oncleared: function (dp) {
                    $(dp.el).removeAttr('data-time');
                },
            });
        },
        taskExport: function () { //导出入口
            var params = tytTool.htmlGetTname($(".searchUl"));
            params.ssoInfo = tytTool.getCookie('ssoInfo')
            tytTool.openPostWindow(`${market}/transport/insurance/export`, params, {
                type: 'get'
            });
        },
        addOrder: function (params){ // 新增订单
            let url = ''
            if(params && params.id) {
                url = `../../html/acceptOrderManage/acceptOrderEditor.html?type=4&isAdd=1&id=${params.id}`
            }else{
                url = `../../html/acceptOrderManage/acceptOrderEditor.html?type=4&isAdd=1`
            }
            tytTool.openWinAuto(url,  `name_1`, 1200, 600)
        }
    }
}

var acceptM = accept.myData;
var acceptA = accept.arr;
var acceptJ = accept.ajx;
var acceptO = accept.opt;

tytTool.loadCheckTab(acceptM.acceptOrder, {})
acceptO.loadList()