var domain = tytTool.getRootPath_web('owers_boss');;
var detail = {
    myData: {
        msgId: '',
        isFinal: ['0##否','1##是']
    },
    arr: {
        common: {
            url: '',
            otherUrl: `${domain}/transport/insurance/get/details`,
            type: 'get',
            otherData: '',
            data: ''
        },
        submit: {
            url: '',
            otherUrl: `${domain}/certificate/feedback/edit`,
            type: 'post',
            otherData: '',
            data: ''
        },
        typeOpt:{
            4:'单程货运险',
            5:'全年货运险',
            7:'车险',
            8:'雇主责任险'
        },
        statusOpt:{
            0:'待支付',
            1:'已生效',
            2:'已退保',
            3:'生效中'
        }
    },
    ajx: {
        detailData: function (data) { //加载详情页面成功
            if(data.code == 200) {
                var req = data.data || {};
                //渲染数据
                req.premiumCurrencyStr = req.premiumCurrencyVO
                req.amtCurrencyStr = Number(req.amtCurrencyVO).toFixed(0)
                req.typeStr = detailA.typeOpt[req.type]
                req.weightStr = req.weightVO
                req.statusStr = detailA.statusOpt[req.status]
                let rdined  = NO(req.premiumVO) ?  floatObj.multiply(req.premiumVO, 100) : ''
                req.rdinedStr = rdined == ''? '' : rdined + '%'
                tytTool.tnameGetHtml($(".warpBox"),req);
                //暂存msgId
                detailM.msgId = NO(req.msgId);
            } else {
                if(NO(data.msg) != '') {
                    tytTool.toastShow(data.msg,2500)
                }
            }
        },
        submitData: function (data) { //提交结果成功
            if(data.code == 200) {
                tytTool.toastShow(data.msg,2500);
                setTimeout(function() {
                    tytTool.reloadUpWin()
                }, 1000);
            } else {
                if(NO(data.msg) != '') {
                    tytTool.toastShow(data.msg,2500)
                }
            }
        }
    },
    opt: {
        showHide: function (_this) { //展开收缩控制
            var fa = _this.parents('.longContent');
            if(fa.hasClass('showStyle')) {
                fa.removeClass('showStyle');
                _this.html('展开')
            } else {
                fa.addClass('showStyle');
                _this.html('收起')
            }
        },
        loadDetail: function () { //加载详情页面数据入口
            detailA.common.data = {
                id:param.id ,
                'ssoInfo':tytTool.getCookie('ssoInfo')
            }
            // param.type 4单程货运险5:全年货运险
            if(param.type == '4') {
                $('.type-5').hide()
            }else{
                $('.type-4').hide()
            } 
            tytTool.ajaxFn(detailA.common, detailJ.detailData)
        },
        submitComplaint: function () { //提交处理内筒请求入口
            if(tytTool.vaildForm($(".ideaFinal"))) return false;
            var arr = tytTool.htmlGetTname($(".ideaFinal"))
            detailA.submit.data = $.extend({}, arr, {
                id: NO(param.id),
                handleName:decodeURI(param.user),
                'ssoInfo':tytTool.getCookie('ssoInfo')
            })
            tytTool.ajaxFn(detailA.submit, detailJ.submitData)
        },
    }
};
var detailM = detail.myData;
var detailA = detail.arr;
var detailJ = detail.ajx;
var detailO = detail.opt;
//加载详情页面数据
detailO.loadDetail();

console.log(param)