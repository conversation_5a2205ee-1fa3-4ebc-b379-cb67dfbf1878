var newAdd = {
    pageSize:20,
    popupIdOne:'',
    popupIdTwo:'',
    arr: {
        commonGet: {
            url: '',
            otherUrl: '',
            type: 'get',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            otherData: '',
            data: ''
        },
        commonPost: {
            url: '',
            otherUrl: '',
            type: 'post',
            contentType: 'application/json',
            dataType: 'json',
            otherData: '',
            data: ''
        }
    },
    ajx: {
    },
    opt: {
        initFun:function(){
            nwa.commonPost.otherUrl = tytTool.getRootPath_web() + '/marketing/activity/getActivityTypes';
            tytTool.ajaxFn(nwa.commonPost,function(res){
                if(res.code == 200){
                    var str = '<option value="">请选择</option>';
                    for(var i=0;i<res.data.length;i++){
                        str += '<option value="'+ res.data[i].value +'">'+ res.data[i].name +'</option>'
                    }
                    $('.activityTypeName').html(str)
                    //请求后 执行~
                    if(nwo.GetParam("id")){
                        $('.currentPage').html('编辑')
                        //回填
                        nwo.fillDatafun(nwo.GetParam("id"))
                    }
                }else{
                    tytTool.toastShow(res.msg,3000);
                }
            })

            
            //是否展示我的活动
            $('input[name="isNeedShow"]').change(function() {
                if($(this).val() == 1){
                    $('.showOrHide,.activityContentWarp').show();
                }else{
                    $('.showOrHide,.activityContentWarp').hide();
                }
            });
            //是否弹窗
            $('input[name="isNeedPopup"]').change(function() {
                if($(this).val() == 1){
                    $('.guanggao').show();
                }else{
                    $('.guanggao').hide();
                }
            });

            //活动方/选择
            $('input[name="activityPart"]').change(function() {
                nwo.getGGFun($(this).val())
            });
        },
        getGGFun:function(activityPart){
            nwa.commonPost.contentType = 'application/x-www-form-urlencoded';
            nwa.commonPost.data = $.extend({},{
                'activityPart':activityPart
            })
            nwa.commonPost.otherUrl = tytTool.getRootPath_web() + '/marketing/activity/getAdList';
            tytTool.ajaxFn(nwa.commonPost,function(res){
                if(res.code == 200){
                    var str = '<option value="">请选择</option>';
                    for(var i=0;i<res.data.length;i++){
                        str += '<option value="'+ res.data[i].id +'"';
                        if(res.data[i].id == newAdd.popupIdOne){
                            str += 'selected'
                        }
                        str += '>'+ res.data[i].title +'</option>'
                    }
                    $('.popupIdOne').html(str)

                    var str = '<option value="">请选择</option>';
                    for(var i=0;i<res.data.length;i++){
                        str += '<option value="'+ res.data[i].id +'"';
                        if(res.data[i].id == newAdd.popupIdTwo){
                            str += 'selected'
                        }
                        str += '>'+ res.data[i].title +'</option>'
                    }
                    $('.popupIdTwo').html(str)
                }else{
                    tytTool.toastShow(res.msg,3000);
                }
            })
        },
        //回填
        fillDatafun:function(id){
            nwa.commonPost.data = $.extend({},{
                'id':id
            })
            nwa.commonPost.contentType = 'application/x-www-form-urlencoded';
            nwa.commonPost.otherUrl = tytTool.getRootPath_web() + '/marketing/activity/getById';
            tytTool.ajaxFn(nwa.commonPost,function(res){
                if(res.code == 200){
                    var data = res.data;
                    $('.activityName').val(data.activityName)
                    $("input[name='isNeedPopup'][value='" + data.isNeedPopup + "']").prop("checked", "checked");
                    if(data.isNeedPopup == 1){
                        $('.guanggao').show(data.isNeedPopup);
                    }
                    nwo.getGGFun(data.activityPart)
                    newAdd.popupIdOne = data.popupIdOne;
                    newAdd.popupIdTwo = data.popupIdTwo;
                    $("input[name='activityPart'][value='" + data.activityPart + "']").prop("checked", "checked");
                    $("input[name='isNeedShow'][value='" + data.isNeedShow + "']").prop("checked", "checked");
                    if(data.isNeedShow == 1){
                        $('.showOrHide,.activityContentWarp').show();
                        $('.activityUrl').val(data.activityUrl)
                        $(".activityContentWarp .activityContent").val(data.activityContent)
                    }
                    $('.startTime').val(nwo.timestampToTime(data.startTime/1000).replace(/\s+/g,""))
                    $('.endTime').val(nwo.timestampToTime(data.endTime/1000).replace(/\s+/g,""))

                    $(".activityTypeName").find("option[value='"+ data.activityType +"']").attr("selected",true);
                }else{
                    tytTool.toastShow(res.msg,3000);
                }
            })
        },
        submitFun:function(){
            // if($('.activityName').val() == ''){
            //     tytTool.toastShow('请填写活动名称',3000);
            //     return false;
            // }
            // if($('input[name="isNeedPopup"]:checked').val() == undefined){
            //     tytTool.toastShow('请选择是否弹窗',3000);
            //     return false;
            // }
            // if($('.popupIdOne').val() == ''){
            //     tytTool.toastShow('请选择广告位1',3000);
            //     return false;
            // }
            // if($('input[name="activityPart"]:checked').val() == undefined){
            //     tytTool.toastShow('请选择活动方',3000);
            //     return false;
            // }
            // if($('input[name="isNeedShow"]:checked').val() == undefined){
            //     tytTool.toastShow('请选择是否展示我的活动',3000);
            //     return false;
            // }

            // if($('.startTime').val() == ''){
            //     tytTool.toastShow('请选择活动开始时间',3000);
            //     return false;
            // }

            // if($('.endTime').val() == ''){
            //     tytTool.toastShow('请选择活动结束时间',3000);
            //     return false;
            // }
            // if(new Date($('.startTime').val()).getTime() > new Date($('.endTime').val()).getTime() ){
            //     tytTool.toastShow('活动开始时间不能大于活动结束时间',3000);
            //     return false;
            // }
            // if($('.activityTypeName').val() == ''){
            //     tytTool.toastShow('请选择活动类型',3000);
            //     return false;
            // }
            $(".updateBtn").attr("disabled",true);
            nwa.commonPost.contentType = 'application/json';
            nwa.commonPost.data = JSON.stringify({
                'id':nwo.GetParam("id")?nwo.GetParam("id"):'',
                'activityName':$('.activityName').val(),
                'activityContent':$('.activityContent').val(),
                'popupIdOne':$('.popupIdOne').val(),
                'popupIdTwo':$('.popupIdTwo').val(),
                'isNeedPopup':$('input[name="isNeedPopup"]:checked').val(),
                'activityPart':$('input[name="activityPart"]:checked').val(),
                'isNeedShow':$('input[name="isNeedShow"]:checked').val(),
                'activityUrl':$('.activityUrl').val(),
                'startTime':$('.startTime').val(),
                'endTime':$('.endTime').val(),
                'activityTypeName':$('.activityTypeName option:selected').html(),
                'activityType':$('.activityTypeName').val()
            })
            nwa.commonPost.otherUrl = tytTool.getRootPath_web() + '/marketing/activity/save';
            tytTool.ajaxFn(nwa.commonPost,function(res){
                if(res.code == 200){
                    tytTool.toastShow('保存成功，稍后将关闭此窗口',3000);
                    setTimeout(function(){
                        window.close();
                        tytTool.reloadUpWin()
                        $(".updateBtn").attr("disabled",false);
                    },1500)
                }else{
                    tytTool.toastShow(res.msg,3000);
                    setTimeout(function(){
                        $(".updateBtn").attr("disabled",false);
                    },1500)
                }
            })
        },
        GetParam:function(paraName) {
            var url = document.location.toString();
            var arrObj = url.split("?");
            if (arrObj.length > 1) {
                var arrPara = arrObj[1].split("&");
                var arr;
                for (var i = 0; i < arrPara.length; i++) {
                    arr = arrPara[i].split("=");
                    if (arr != null && arr[0] == paraName) {
                        return arr[1];
                    }
                }
                return "";
            }
            else {
                return "";
            }
        },
        timestampToTime:function (timestamp) {
            var date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
            var Y = date.getFullYear() + '-';
            var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            var D = (date.getDate() < 10 ? '0'+ (date.getDate()) : date.getDate()) + ' ';
            var h = date.getHours() + ':';
            var m = date.getMinutes() + ':';
            var s = date.getSeconds();
            return Y+M+D;
        }
    }
}

var nwa = newAdd.arr;
var nwj = newAdd.ajx;
var nwo = newAdd.opt;

nwo.initFun();