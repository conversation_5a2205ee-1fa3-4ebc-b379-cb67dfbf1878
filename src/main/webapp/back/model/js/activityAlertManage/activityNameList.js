/*
    activityNameList.js for activityNameList.html
    author: <PERSON><PERSON><PERSON>
*/
var activityNameList = {
    pageSize:20,
    arr: {
        commonGet: {
            url: '',
            otherUrl: '',
            type: 'get',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            otherData: '',
            data: ''
        },
        commonPost: {
            url: '',
            otherUrl: '',
            type: 'post',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data,_currentPage) {
            if(data.code == 200) {
                var data_list = data.data.list;
                var count = data.data.rowCount;
                var currentPage = Number(_currentPage),
                    maxPage = Math.ceil(Number(count) / activityNameList.pageSize),
                    rowCount = Number(count);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams,'aop.listOpt')
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams,'aop.listOpt')
                }
                //正式载入数据
                var _html = '',_edit = '';
                if (data_list && data_list.length > 0) {
                    $.each(data_list, function(index, el) {
                        _html +=`<tr>
							<td>${tytTool.NullObj(el.id)}</td>
							<td>${tytTool.NullObj(el.userId)}</td>
                            <td>${tytTool.timeTrans(el.ctime, { timeOpt: ['Y', 'M', 'D'],splitStr: "-"}).replace(/\\s+/g, " ")}</td>
							<td>${tytTool.NullObj(el.userGrade) == '' ? '无' : el.userGrade}</td>
							<td>${tytTool.NullObj(el.isJoin	== 1?'否':'是')}</td>
                            <td>${tytTool.NullObj(el.operater)}</td>
							<td>${tytTool.NullObj(el.isJoin	== 1?'<a class="info_opt mgr-10" onclick="aop.removeFun('+ tytTool.NullObj(el.id) +')">删除</a>':'')}</td>
						</tr>`
                    });
                    $("#activityNameList").html(_html);
                } else {
                    _html = '<tr><td colspan="'+ $(".tableTh th").length +'">暂无查询数据</td></tr>'
                    $("#activityNameList").html(_html);
                }
            } else {
                if(tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        }
    },
    opt: {
        loadList:function () {
            var pageArr = {
                "totalPages": 1,
                "pageSize": activityNameList.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": aop.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) {
            currentPage==''?currentPage=1:currentPage = currentPage;
            aar.commonPost.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            console.log(arr)
            aar.commonPost.data = $.extend({}, arr,{
                activityId:aop.GetParam("id")
            })
            aar.commonPost.otherUrl = tytTool.getRootPath_web() + '/marketing/activity/user/list?currentPage='+ currentPage + '&pageSize=' + activityNameList.pageSize;
            tytTool.ajaxFn(aar.commonPost, aaj.listData)
        },
        //删除人员
        removeFun:function(id){
            aar.commonPost.data = $.extend({}, {
                id:id
            })
            aar.commonPost.otherUrl = tytTool.getRootPath_web() + '/marketing/activity/user/delete';
            tytTool.ajaxFn(aar.commonPost,function(res){ 
                if(res.code == 200){
                    tytTool.toastShow(res.msg,3000);
                    aop.loadList()
                }else{
                    tytTool.toastShow(res.msg,3000);
                }
            })
        },
        GetParam:function(paraName) {
            var url = document.location.toString();
            var arrObj = url.split("?");
            if (arrObj.length > 1) {
                var arrPara = arrObj[1].split("&");
                var arr;
                for (var i = 0; i < arrPara.length; i++) {
                    arr = arrPara[i].split("=");
                    if (arr != null && arr[0] == paraName) {
                        return arr[1];
                    }
                }
                return "";
            }
            else {
                return "";
            }
        },
        showHide: function(_type) {
			if (_type == 'excelHide') { //隐藏Excel上传框
				$(".excelBox").hide(300);
				$(".excelBox .alertContent").html('请点击“浏览”选择需要导入的模板文件');
				$(".excelBox h2").html("请选择文件");
				$("#fileField").attr("value", "");
			} else if (_type == 'excelShow') {
				$(".excelBox").show(300);
			}
		},
        fileInfo:function(source, _this) { //file选择
            var f = source.files[0];
			var name = f.name;
			$(".excelBox .alertContent").html(name);
			$(".excelBox h2").html("请上传");
        },
        uploadFile: function() {
			if ($(".excelBox h2").html() == "请选择文件") {
				alert("请选择导入文件");
				return;
			}
            if($("#fileField")[0].files[0].size > 5 * 1024 * 1024){
                tytTool.toastShow('上传的附件文件不能超过5M！！！', 2000);
                $(".excelBox .alertContent").html('请点击“浏览”选择需要导入的模板文件');
                $(".excelBox h2").html("请选择文件");
                $("#fileField").attr("value", "");
                return false;
            }
            $("#loadingBox").show();
            if(confirm("请确认删除名单")){
                // $(".bgOutLook,.excelBox").hide();
                var formData = new FormData();
                formData.append("activityId", aop.GetParam("id"));
                formData.append("fileField", $("#fileField")[0].files[0]);
                var _uploadUrl =  tytTool.getRootPath_web() + '/marketing/activity/user/batchDel'
                $("#loadingBox").show();
                $.ajax({
                    type: "POST",
                    url:  _uploadUrl,
                    data: formData,
                    contentType: false,
                    /**
                     * 必须false才会避开jQuery对 formdata 的默认处理
                     * XMLHttpRequest会对 formdata 进行正确的处理
                     */
                    processData: false,
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        $("#loadingBox").hide();
                        if(data.code == 200){
                            alert("已成功删除"+ data.data +"条数据");
                            tytTool.reloadWin();
                        }else{
                            tytTool.toastShow(data.msg,"");
                            $(".excelBox .alertContent").html('请点击"浏览"选择需要导入的模板文件');
                            $(".excelBox h2").html("请选择文件");
                            $("#fileField").attr("value", "");
                            $(".excelBox").hide();
                        }
                    },
                    error: function(xhr, status) {
                        $("#loadingBox").hide();
                        tytTool.toastShow('导入文件格式不正确', 2000);
                        $(".excelBox").hide();
                        $(".excelBox .alertContent").html('请点击“浏览”选择需要导入的模板文件');
                        $(".excelBox h2").html("请选择文件");
                        $("#fileField").attr("value", "");
                    }
                });
            }
		},
        openUploadBox:function(){
            aop.showHide('excelShow'); 
        },
    }
}

var aar = activityNameList.arr;
var aaj = activityNameList.ajx;
var aop = activityNameList.opt;
aop.loadList()

