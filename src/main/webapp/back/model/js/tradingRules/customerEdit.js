// var market = tytTool.getRootPath_web('tyt_manage_new');
var customerEdit = {
    myData: {

    },
    arr: {
        submit: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        add: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        delete: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        get: {
            url: '',
            otherUrl: '',
            type: 'get',
            otherData: '',
            data: ''
        },
        
        
    },
    ajx: {
        submitRuleData: data => { //提交成功
            if(data.code == 200) {
                console.log(data);
                tytTool.toastShow('操作成功', 2500)
                tytTool.toastShow('操作成功', 2500, () => {
                    tytTool.reloadUpWin()
                })
            } else {
                if(NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            } 
        },
        deleteSelectData: data => { // 删除规则成功
            if(data.code == 200) {
                commonO.ruleList('edit')
                tytTool.toastShow('删除成功')
                $("#addTypeInput").val('')
            } else {
                if(NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            } 
        },
        addSelectData: res =>{
            if(res.code == 200 ) {
                commonO.ruleList('edit')
                tytTool.toastShow('添加成功')
            }else{
                if(NO(res.msg) != '') {
                    tytTool.toastShow(res.msg, 2500)
                }
            }
        }
    },
    opt: {
        vaildRule: () => { //保存前的校验
            let arr = tytTool.htmlGetTname($(".detailListBox"))
            var flag = true;
            if(NO(arr.port) === '' ) {
                tytTool.toastShow('请选择业务端',2500);
                flag = false;
            }
            if(flag && NO($("#ruleName").val()) === '') {
                tytTool.toastShow('请输入规则名称',2500);
                flag = false;
            }
            if(flag && NO($("#ruleType").attr('data-val')) === '') {
                tytTool.toastShow('请选择规则类型',2500);
                flag = false;
            }
            if(flag && NO($("#ruleSort").val()) === '') {
                tytTool.toastShow('请输入排序值',2500);
                flag = false;
            }
            if(flag && (Number($("#ruleSort").val()) >100 ||Number($("#ruleSort").val()) ==0) ) {
                tytTool.toastShow('请输入合理排序值（1-100）',2500);
                flag = false;
            }
            if(flag && NO(editor.txt.text().replace(/&nbsp;/ig,'').replace(/ /ig,'')) === '') {
                tytTool.toastShow('请输入规则内容',2500);
                flag = false;
            }
            return flag;
        },
        submitRule: () => { // 保存规则类型入口
            //校验
            if(!editO.vaildRule()) {
                return false
            }
            var arr = tytTool.htmlGetTname($(".detailListBox"));
            //富文本数据整合
            $.when(commonO.ruleContent()).done(res => {
                console.log(res)
                if(typeof res === 'boolean') {
                    console.log(res)
                    return false;
                }
                let base = {}
                if(NO(param.id) != '') { // 编辑
                    editA.submit.url = `/custom/service/rule/edit`;
                    base = {
                        id:param.id
                    }
                }else{
                    editA.submit.url = `/custom/service/rule/add`;
                }
                
                editA.submit.data = $.extend(base, arr,{
                    ruleTypeName: $("#ruleType").html(),
                    typeId: $("#ruleType").attr('data-val'),
                    ruleContent: res,
                    operater:decodeURI(param.name)
                });
                
                console.log(editA.submit.data);
                tytTool.ajaxFn(editA.submit, editJ.submitRuleData);
            })
        },
        addSelect:() =>{
            editA.add.url = `/custom/service/type/add`;
            editA.add.data = $.extend({}, {
                typeName: $('#addTypeInput').val(),
            });
            tytTool.ajaxFn(editA.add, editJ.addSelectData);
        },
        deleteSelect: (id) => { // 删除规则类型入口
            editA.delete.url = `/custom/service/type/del`;
            editA.delete.data = {
                id,
            };
            tytTool.ajaxFn(editA.delete, editJ.deleteSelectData);
        },
        showSelect:  event => { // 展示自定义下拉
            event.stopPropagation();
            $(".innerSelectOpt").fadeIn(100)
        },
        changeSelect: (event,_this) => { // 自定义下拉的改变
            _this = $(_this)
            var html = '',val = '';

            if(_this.html() === '请选择') {
                html = '请输入规则名称';
                val = '';
                $(".selectOpt em").addClass('placeholderClass')
            } else {
                html = _this.html();
                val = _this.attr('data-val')
                $(".selectOpt em").removeClass('placeholderClass')
                console.log(_this.parent())
                _this.parent().addClass('active').siblings().removeClass('active');
            }
            $(".selectOpt em").html(html).attr('data-val',val)
            $('.innerSelectOpt').fadeOut(100)
        }

    },
}

var editM = customerEdit.myData;
var editA = customerEdit.arr;
var editJ = customerEdit.ajx;
var editO = customerEdit.opt;

$(document).on('click', event => {
    $('.innerSelectOpt').fadeOut()
})

console.log(param.id)
// 编辑页面回显
if(NO(param.id) != '') {
    $.when(
        commonO.ruleList('edit')
    ).done(() => {
        commonO.detailShow()
    })
    $('.currentPage').html('编辑')
}else{
    commonO.ruleList('edit')
    tytTool.tnameGetHtml($(".detailListBox"),{
        status:2
    });
}