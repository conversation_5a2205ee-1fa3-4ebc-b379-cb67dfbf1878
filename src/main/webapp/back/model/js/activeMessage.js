// 动态URL
var website = getRootPath_web();
var parseRecaId = parseSearch().recaId;

/*========================*/
//添加奖励规则   (默认加载全部)
function defaultActive() {
	var begin_time = $("#beginTime").val();
	var end_time = $("#endTime").val();
	$.ajax({
		url: '' + website + '/admin/ruleActivity/queryActiveRule',
		type: 'post',
		dataType: 'json',
		async: false,
		data: {
			beginTime: begin_time,
			endTime: end_time
		}
	}).done(function(data) {
		if (data.data == undefined || data.data == null) {
			return false;
		}
		$("#awardTab").html('');
		var head_html = '<tr class="tableTh"><th width="80">选择</th><th width="120">适用身份</th><th width="150">适用地区</th><th>奖励方式</th></tr>';
		var data_list = data.data.list;
		var identity_shi = '';
		// 遍历数据
		$.each(data_list, function(index, el) {
			var dataTojson = '';
			if (data_list[index].ruleContent != '') {

				dataTojson = strToJson(data_list[index].ruleContent);
			}
			var rule_auth = dataTojson.auth;
			var rule_pay = dataTojson.pay;
			var auth_html = '';
			var pay_html = '';
			var recIdentityName = '';

			//规则排布
			if (dataTojson != '') {
				if (rule_auth.isEnabled) {
					$.each(rule_auth.rule, function(ind_num, el) {
						if (ind_num % 2 != 0) {
							auth_html += '认证：' + rule_auth.rule[ind_num].begin_num + '-' + rule_auth.rule[ind_num].end_num + '人，' + rule_auth.rule[ind_num].re_money_value + '元；<br/>'
						} else {
							auth_html += '认证：' + rule_auth.rule[ind_num].begin_num + '-' + rule_auth.rule[ind_num].end_num + '人，' + rule_auth.rule[ind_num].re_money_value + '元；'
						}
					});
				}
				if (rule_pay.isEnabled) {
					$.each(rule_pay.rule, function(ind_num, el) {
						if (ind_num % 2 != 0) {
							pay_html += '付费： ' + rule_pay.rule[ind_num].begin_num + ' - ' + rule_pay.rule[ind_num].end_num + ' 人， ' + rule_pay.rule[ind_num].re_money_value + ' % ；<br/>'
						} else {
							pay_html += '付费： ' + rule_pay.rule[ind_num].begin_num + ' - ' + rule_pay.rule[ind_num].end_num + ' 人， ' + rule_pay.rule[ind_num].re_money_value + ' % ；'
						}
					});
				}
			}

			//取出使用身份的名称
			$("#recIdentity option").each(function(ind, elem) {
				if (Number($(this).val()) == Number(data_list[index].recIdentity)) {
					recIdentityName = $(this).html();
				}
			});

			identity_shi += '<tr>\
						<td><input class="award_check" type="checkbox" data-subid=' + data_list[index].id + ' data-clash=' + data_list[index].type + '></td>\
						<td>' + recIdentityName + '</td>\
						<td>' + data_list[index].recProvince + data_list[index].recCity + '</td>\
						<td>' + auth_html + pay_html + '</td>\
					</tr>';

		});

		$("#awardTab").append(head_html + identity_shi);


		//排除已经选择的规则
		$("#awardTab .award_check").each(function(index, el) {
			if (Number($(this).attr('data-clash')) == 1) {
				$(this).parents('tr').css({
					'background': '#ccc',
					'cursor': 'not-allowed'
				});
				$(this).attr('disabled', 'disabled');
				$("#awardTab").append($(this).parents('tr'))

			}
		});

	}).fail(function() {
		console.log("error");
	});
}

//查询奖励规则
function checkActive() {
	var begin_time = $("#beginTime").val();
	var end_time = $("#endTime").val();
	var rec_identity = $("#rec_identity").attr('data-val');
	var recIdentity_val = $("#recIdentity").val();
	var sheng_val = $("#sheng2").val();
	var shi_val = $("#shi2").val();
	$.ajax({
		url: '' + website + '/admin/ruleActivity/queryActiveRule',
		type: 'get',
		dataType: 'json',
		data: {
			beginTime: begin_time,
			endTime: end_time
		}
	}).done(function(data) {
		if (data.data == undefined) {
			return false;
		}
		var identity_shi = '';
		var show_list = '';
		var data_list = data.data.list;
		var head_html = '<tr class="tableTh"><th width="80">选择</th><th width="120">适用身份</th><th width="150">适用地区</th><th>奖励方式</th></tr>';
		$("#awardTab").html('');

		// 遍历数据
		$.each(data_list, function(index, el) {

			var dataTojson = '';
			if (data_list[index].ruleContent != '') {

				dataTojson = strToJson(data_list[index].ruleContent);
			}

			var rule_auth = dataTojson.auth;
			var rule_pay = dataTojson.pay;
			var auth_html = '';
			var pay_html = '';
			var recIdentityName = '';


			//取出使用身份的名称
			$("#recIdentity option").each(function(ind, elem) {
				if (Number($(this).val()) == Number(data_list[index].recIdentity)) {
					recIdentityName = $(this).html();
				}
			});

			//规则排布
			if (dataTojson != '') {
				if (rule_auth.isEnabled) {
					$.each(rule_auth.rule, function(ind_num, el) {
						if (ind_num % 2 != 0) {
							auth_html += '认证：' + rule_auth.rule[ind_num].begin_num + '-' + rule_auth.rule[ind_num].end_num + '人，' + rule_auth.rule[ind_num].re_money_value + '元；<br/>'
						} else {
							auth_html += '认证：' + rule_auth.rule[ind_num].begin_num + '-' + rule_auth.rule[ind_num].end_num + '人，' + rule_auth.rule[ind_num].re_money_value + '元；'
						}
					});
				}
				if (rule_pay.isEnabled) {
					$.each(rule_pay.rule, function(ind_num, el) {
						if (ind_num % 2 != 0) {
							pay_html += '付费： ' + rule_pay.rule[ind_num].begin_num + ' - ' + rule_pay.rule[ind_num].end_num + ' 人， ' + rule_pay.rule[ind_num].re_money_value + ' % ；<br/>'
						} else {
							pay_html += '付费： ' + rule_pay.rule[ind_num].begin_num + ' - ' + rule_pay.rule[ind_num].end_num + ' 人， ' + rule_pay.rule[ind_num].re_money_value + ' % ；'
						}
					});
				}
			}

			if (recIdentity_val == '' && shi_val == '') { //(1)无条件 -全部显示 --
				identity_shi += '<tr>\
						<td><input class="award_check" type="checkbox" data-subid=' + data_list[index].id + ' data-clash=' + data_list[index].type + '></td>\
						<td>' + recIdentityName + '</td>\
						<td>' + data_list[index].recProvince + data_list[index].recCity + '</td>\
						<td>' + auth_html + pay_html + '</td>\
					</tr>';
			} else if (recIdentity_val == '' && shi_val != '') { //(2)地区 - 按指定地区显示 --
				if ($("#shi2").val() == data_list[index].recCity) {
					identity_shi += '<tr>\
							<td><input class="award_check" type="checkbox" data-subid=' + data_list[index].id + ' data-clash=' + data_list[index].type + '></td>\
							<td>' + recIdentityName + '</td>\
							<td>' + data_list[index].recProvince + data_list[index].recCity + '</td>\
							<td>' + auth_html + pay_html + '</td>\
						</tr>';
				}
			} else if (recIdentity_val != '' && shi_val != '') { //(3)身份+地区  - 按指定地区和身份显示 --
				if ($("#shi2").val() == data_list[index].recCity && Number($("#recIdentity").attr('data-val')) == Number(data_list[index].recIdentity)) {
					identity_shi += '<tr>\
							<td><input class="award_check" type="checkbox" data-subid=' + data_list[index].id + ' data-clash=' + data_list[index].type + '></td>\
							<td>' + recIdentityName + '</td>\
							<td>' + data_list[index].recProvince + data_list[index].recCity + '</td>\
							<td>' + auth_html + pay_html + '</td>\
						</tr>';
				}
			} else if (recIdentity_val != '' && shi_val == '') { //(4)身份 - 按身份显示 --
				if (Number($("#recIdentity").attr('data-val')) == Number(data_list[index].recIdentity)) {
					identity_shi += '<tr>\
							<td><input class="award_check" type="checkbox" data-subid=' + data_list[index].id + ' data-clash=' + data_list[index].type + '></td>\
							<td>' + recIdentityName + '</td>\
							<td>' + data_list[index].recProvince + data_list[index].recCity + '</td>\
							<td>' + auth_html + pay_html + '</td>\
						</tr>';
				}
			}

		});
		$("#awardTab").append(head_html + identity_shi);


		//排除已经选择的规则
		$("#awardTab .award_check").each(function(index, el) {
			if (Number($(this).attr('data-clash')) == 1) {
				$(this).parents('tr').css({
					'background': '#ccc',
					'cursor': 'not-allowed'
				});
				$(this).attr('disabled', 'disabled')
				$("#awardTab").append($(this).parents('tr'))

			}
		});

	}).fail(function() {
		console.log("error");
	});
}

//提交新建的活动
function submit_active(param) {
	$.ajax({
		url: '' + website + '/admin/ruleActivity/saveActive',
		type: 'post',
		dataType: 'json',
		data: param
	}).done(function(data) {
		if (data.code == 200) {
			$("#alert_danger").show();
			closeWin()
		} else {
			$("#alert_danger .alertContent").html('提交失败，稍后将关闭此窗口');
			$("#alert_danger").show();
		}
	}).fail(function() {
		console.log("error");
	});
};

function closeWin() {
	clearTimeout(times);
	var times = setTimeout(function() {
		window.close();
		window.opener.location.reload();
	}, 3000);
}

function getRootPath_web() {
	//获取当前网址，如： http://localhost:8083/uimcardprj/share/meun.jsp
	var curWwwPath = window.document.location.href;
	//获取主机地址之后的目录，如： uimcardprj/share/meun.jsp
	var pathName = window.document.location.pathname;
	var pos = curWwwPath.indexOf(pathName);
	//获取主机地址，如： http://localhost:8083
	var localhostPaht = curWwwPath.substring(0, pos);
	//获取带"/"的项目名，如：/uimcardprj
	var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
	return (localhostPaht + projectName);
}

//取头部的参数值:方法
function parseSearch() {
	var search = {}
	var s = location.search.replace('?', '');
	if (!s) return search;
	s = s.split("&");

	for (var i = 0; i < s.length; i++) {
		search[s[i].split("=")[0]] = s[i].split("=")[1];
	}
	return search;
}

//字符串转json
function strToJson(str) {
	var json = eval('(' + str + ')');
	return json;
}