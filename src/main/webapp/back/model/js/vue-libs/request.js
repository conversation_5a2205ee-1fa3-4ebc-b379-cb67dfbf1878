

var   HttpRequest = {

    baseUrl:()=> {
        var curWwwPath = window.document.location.href;
        //获取主机地址之后的目录，如： uimcardprj/share/meun.jsp
        var pathName = window.document.location.pathname;
        var pos = curWwwPath.indexOf(pathName);
        //获取主机地址，如： http://localhost:8083
        var localhostPaht = curWwwPath.substring(0, pos);
        //获取带"/"的项目名，如：/uimcardprj
        var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
        return localhostPaht + projectName;
    },

    get(url, params){
        return new Promise((resolve,reject)=>{
            params = params || {};
            axios.get(this.baseUrl()+url,{
                params,
            }).then(res=>{
                resolve(res)
            }).catch(error=>{
                reject('error');
            })
        })
    },
    post(url, params){
        return new Promise((resolve,reject)=>{
            params = params || {};
            axios.post(this.baseUrl()+url,Qs.stringify(params),{
                headers:{
                    'Content-Type':'application/x-www-form-urlencoded'
                }
            }).then(res=>{
                resolve(res)
            }).catch(error=>{
                reject('error');
            })
        })
    },
    xmlFile(url, params){

    },
    //获取公共资源基础路径
    checkUrl(){
        let baseUrl = '';
        var protocol = window.location.protocol + '//';

        if(this.baseUrl().includes('localhost')) {// 本地环境
            baseUrl = protocol + 'dev.teyuntong.net'
        }else if(this.baseUrl().includes('dev')) { // dev环境
            baseUrl = protocol + 'dev.teyuntong.net'
        }else if(this.baseUrl().includes('test')) {// newtest环境
            baseUrl = protocol + 'test.teyuntong.net'
        }else if(this.baseUrl().includes('release')) {// release环境
            baseUrl = protocol + 'release.teyuntong.net'
        }else{// 线上
            baseUrl = protocol + 'api.teyuntong.net'
        }
        return baseUrl
    },
    //获取公共资源方法
    getGlobal (url,params,methods) {
       
        return new Promise((resolve,reject)=>{
            
            axios({
                url:url,
                data:Qs.stringify(params),
                method:methods? methods :'post',
                headers:{
                    'Content-Type':'application/x-www-form-urlencoded'
                }
            }).then(res=>{
                    resolve(res.data)
                })
            

        })
    }





}
