/*
    existPayConfig.js for existPayConfig.hmtl
    author: hey<PERSON>an
*/
var config = {
    myData: {

    },
    arr: {
        get: {
            url: '',
            otherUrl: '',
            type: 'post',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            otherData: '',
            data: ''
        },
        set: {
            url: '',
            otherUrl: '',
            type: 'post',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        saveData: function(data) { // 提交成功
            if (data.code == 200) {
                tytTool.toastShow('收费标准修改成功', 3000)
                configO.checkEdit('save')
                configO.backMsg()
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 3000)
                }
            }
        },
        backData: function(data) { // 回显成功
            if (data.code == 200) {
                tytTool.tnameGetHtml($('.detailWarp'), data);
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 3000)
                }
            }
        }
    },
    opt: {
        backMsg: function() { //回显值请求入口
            configA.get.otherUrl = `${market}manage/current/currentConfig`
            configA.get.data = $.extend({}, commonM.data.common)
            tytTool.ajaxFn(configA.get, configJ.backData)
        },
        subData: function(_this) { // 保存值请求入口
            var val = $("#configValue").val(),
                html = $(".saveSpan").html();
            if(val == '') { //空
                tytTool.toastShow('请填写收费标准金额', 2500);
                return false;
            }
            if(html == val) { //未做修改,不请求数据直接返回
                configO.checkEdit('save')
                return false;
            }
            configA.set.otherUrl = `${market}manage/current/setCurrentConfig`
            configA.set.data = $.extend({}, commonM.data.common, {
                configValue: val
            })
            tytTool.ajaxFn(configA.set, configJ.saveData)
        },
        checkEdit: function (type) {
            if(type == 'change') {
                $("#changeInput,.changeSpan").show()
                $("#saveInput,.saveSpan").hide()
            } else if(type == 'save') {
                $("#changeInput,.changeSpan").hide()
                $("#saveInput,.saveSpan").show()
            }
        },
        configBlur: function (_this) {
            if(_this.val() > 5000 || _this.val() < 0) {
                tytTool.toastShow('请填写正确的收费标准金额', 2500);
                _this.val(_this.attr('data-oldval'))
                return false;
            }
            if(!commonO.inpBlurValid(_this,'decimal_2','请填写正确的收费标准金额')) {
                _this.val(_this.attr('data-oldval'))
                return false
            }
        }
    },
}
var configM = config.myData;
var configA = config.arr;
var configJ = config.ajx;
var configO = config.opt;

configO.backMsg()
