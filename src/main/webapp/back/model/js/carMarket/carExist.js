/*
    carExist.js for carExist.hmtl
    author: lhy
*/
var exist = {
    myData: {
        pageSize: 20,
        currentPage: 1
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        delete: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data, _currentPage) {
            if (data.code == 200) {
                var data_list = data.data;
                var count = data.count;
                var currentPage = Number(_currentPage),
                    maxPage = Math.ceil(Number(count) / existM.pageSize),
                    rowCount = Number(count);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                tytTool.jqPaginatorExtend(arrParams, 'existO.listOpt')
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                }
                //正式载入数据
                var rule = {
                    list: [
                        'ID##vehicleId',
                        '车型##vehicleModel',
                        '长度##vehicleLength',
                        '承载##vehicleLoad',
                        '平台样式##platformStyle',
                        '轮胎外露##tireStyle',
                        // '牵引销售量##tractionPinCount',
                        '车轴型号##axleModel',
                        '鹅颈升降##vehicleEjingLift',
                        // '厂商指导价##guideSale',
                        '操作width100##opt'],
                    item: {
                        cellPhone: {
                            attr: ['class==phoneHide']
                        }
                    },
                    opt: {
                        dom: `<a class="info_opt" data-type="现车" onclick="commonO.operate('open',$(this),{{vehicleId}})">删除</a><span class="line_span">|</span><a class="info_opt" onclick="existO.addExist({{vehicleId}})">详情</a>`
                    }
                }
                var tmp = tytTool.eachList(data_list, rule)
                $("#carExist").empty().append(tmp.str)
                tytTool.bindHidePhone()
                commonO.colResizable();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
        deteleData: function (data) {
            if (data.code == 200) {
                commonM.del.obj.remove();
                commonO.operate('close');
                tytTool.toastShow('删除成功！', 2000);
                if($("#carExist tr").length == 1 && Number(session.get('pageNo')) > 1) {
                    session.set('pageNo',Number(session.get('pageNo')) - 1)
                }
                existO.loadList()
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000)
                }
            }
        }
    },
    opt: {
        loadList: function(type) {
            var pageArr = {
                "totalPages": 1,
                "pageSize": existM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": existO.listOpt,
                "saveCurrent": true,
                "session": type
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            existA.common.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            existA.common.otherUrl = `${market}Manage/Vehicle/read`
            existA.common.data = $.extend({}, commonM.common, arr, {
                "currentPage": currentPage,
                "pageSize": existM.pageSize,
                "isDetail": 0,
                "type": 2,
                "vehicleId": $('#vehicleModel').val(),
            })
            tytTool.ajaxFn(existA.common, existJ.listData)
        },
        addExist: function (_id) {
            var id = NO(_id) == '' ? '' : '&vId=' + _id
            var _url = '../../html/carMarket/modelEdit.html?type=exist&&userId=' + commonM.common.userId + id;
            tytTool.openWinAuto(_url,'existAdd_' + _id, 1200, 600)
        },
        deleteLine: function(_this) { //删除本行入口
            existA.delete.otherUrl = `${market}manage/Vehicle/delete`
            existA.delete.data = $.extend({}, commonM.common, {
                vehicleId: commonM.del.delId
            })
            tytTool.ajaxFn(existA.delete, existJ.deteleData)
        }
    }
}

var existM = exist.myData;
var existA = exist.arr;
var existJ = exist.ajx;
var existO = exist.opt;
//控制权限和列表加载的顺序
$.when(
    commonO.baseMsg()
).done(function() {
    commonO.toolSearch('brand')
    existO.loadList();
}).fail(function() {
    existO.loadList();
})