
var cityMap = {
    myData: {
        data: {
            initMarker: [], //初始化默认点
            localLngLat: [], //存储当前点的经纬度
            realLngLat: '', //实际点的经纬度
            place: {}, //地点数据
            chooseFlag: false  //已选择当前位置地点
        },
        map: Object(),
        marker: Object(),
    },
    opt: {
        init: function (arr) {
            let array = arr ? { center: arr } : {}
            let markerArr = arr ? { position: arr } : {}
            mapM.map = new AMap.Map('mapContainer', $.extend({}, {
                zoom: 15,
                resizeEnable: true
            }, array));
            mapM.map.setDefaultCursor('pointer');
            mapM.marker = new AMap.Marker($.extend({
                icon: new AMap.Icon({
                    image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                    imageSize:new AMap.Size(26,34)
                }),
            }, markerArr))
            mapM.map.add(mapM.marker);

            //输入框
            var autocomplete = new AMap.Autocomplete({
                input: "tipinput"
            });
            //注册监听，当选中某条记录时会触发
            AMap.event.addListener(autocomplete, 'select', (e) => {
                if(e.poi.location == '') {
                    tytTool.toastShow('当前无经纬度', 2000)
                    return false
                }
                mapO.changeMarker([e.poi.location.lng, e.poi.location.lat])
                mapO.Geocoder([e.poi.location.lng, e.poi.location.lat])
            })

            //注册监听，当选中某条记录时会触发
            AMap.event.addListener(autocomplete, "error", function (data) {
                console.log("定位失败")
                alert(data.info)
            });
        },
        changeMarker: function (arr) {
            if(mapM.data.realLngLat === '') {
                mapM.data.realLngLat = arr
            }
            mapM.data.localLngLat = arr;
            mapM.map.remove([mapM.marker])
            mapM.marker = new AMap.Marker({
                icon: new AMap.Icon({
                    image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                    imageSize:new AMap.Size(26,34)
                }),
                position: arr,
                center: arr,
            });
            mapM.map.add([mapM.marker]);
            mapM.map.setFitView();
            // $("#tipinput").val('')
        },
        placeHandle: function (type) { // 高德地址数据的处理
            var place = mapM.data.place;
            var span = '', dataVal = '', val = '';
            var pp = place.pro, ppr = place.pro.replace('省', ''), pprr = place.pro.replace('省', '').replace('市', ''),
                pc = place.city, pcr = place.city.replace('省', ''), pcrr = place.city.replace('省', '').replace('市', ''),
                pd = place.district, pdr = place.district.replace('省', ''), pdrr = place.district.replace('省', '').replace('市', '');
            if (pp == pd && pp == pc && pc == pd) { //台湾,澳门
                span = `<span>${ppr}</span>`
                dataVal = ppr + ',' + ppr + ',' + ppr;
                val = ppr;
                place.detailPlace = place.address.split('特别行政区')[1]
            } else if (pc == '' && pd != '') { //北京,天津,上海,重庆
                span = `<span>${pprr}</span><span>${ppr}</span><span>${pd}</span>`
                dataVal = pprr + ',' + ppr + ',' + pd;
                if(pprr.indexOf('海南') != -1) {
                    dataVal = pprr + ',' + pd + ',' + pd;
                    span = `<span>${pprr}</span><span>${pd}</span><span>${pd}</span>`
                }
                val = ppr + ' ' + pd
            } else {
                span = `<span>${pprr}</span><span>${pcr}</span><span>${pd}</span>`
                dataVal = pprr + ',' + pcr + ',' + pd;
                val = pprr + ' ' + pcr + ' ' + pd;
            }
            mapM.data.place.realPlace = val.replace(/ /ig, '')
            if(type === 'confirm') { //确认选择并回显到页面时候执行
                mapM.data.chooseFlag = false;
                $("#tytCity").attr('data-val', dataVal).val(val);
            } else { //仅仅选择选择地址的时候
                $(".mapPlaceBox").empty().append(span)
                $(".mapPlaceBox span:last-child").addClass('active')
                $("#tipinput").val(place.detailPlace)
                $(".setLabelBox").hide()
            }
            if(type === 'choose') {
                mapM.data.chooseFlag = true;
                //更新实际经纬度点
                mapM.data.realLngLat = mapM.data.localLngLat;
            }
        },
        Geocoder: function (arr) { //获取地理位置
            var lnglatXY = new AMap.LngLat(arr[0], arr[1]);
            let MGeocoder = new AMap.Geocoder({
                radius: 1000,
                extensions: "all"
            });
            //返回地理编码结果
            AMap.event.addListener(MGeocoder, "complete", function (event) {
                if (event.info === 'OK') {
                    var regeocode = event.regeocode;
                    mapM.data.place = {
                        address: NO(regeocode.formattedAddress),
                        pro: NO(regeocode.addressComponent.province).replace('省','').replace('自治区','').replace('壮族','').replace('藏族','').replace('维吾尔','').replace('特别行政区',''),
                        city: NO(regeocode.addressComponent.city).replace('特别行政区',''),
                        district: NO(regeocode.addressComponent.district),
                        detailPlace: NO(regeocode.formattedAddress).split(NO(regeocode.addressComponent.district))[1],
                        lng: lnglatXY.lng,
                        lat: lnglatXY.lat
                    }
                    mapM.data.place.realPlace = mapM.data.place.pro + mapM.data.place.city + mapM.data.place.district;
                    if(mapM.data.place.pro.indexOf('澳门') != -1 || mapM.data.place.pro.indexOf('香港') != -1) {
                        mapM.data.place.detailPlace = NO(regeocode.formattedAddress).split('特别行政区')[1]
                        mapM.data.place.city = mapM.data.place.pro;
                        mapM.data.place.district = mapM.data.place.pro;
                        mapM.data.place.realPlace = mapM.data.place.pro;

                    }
                    if(mapM.data.place.pro.indexOf('台湾') != -1) {
                        mapM.data.place.detailPlace = NO(regeocode.formattedAddress).split('省')[1]
                        mapM.data.place.city = mapM.data.place.pro;
                        mapM.data.place.district = mapM.data.place.pro;
                        mapM.data.place.realPlace = mapM.data.place.pro;
                    }
                    mapO.markerSetLabel()
                }
            });
            MGeocoder.getAddress(lnglatXY);
        },
        markerSetLabel: function () { //设label
            var place = mapM.data.place;
            var pc = place.city,pd = place.district;
            if (pc == '' && pd == '') { //台湾,澳门
                place.detailPlace = place.address.split('省')[1]
            }
            mapM.marker.setLabel({
                // offset: new AMap.Pixel(20, 20),  //设置文本标注偏移量
                offset: new AMap.Pixel(-150, -80),
                content: `<div class='setLabelBox'>
                            <i onclick="mapO.closeLabel(event)">×</i>
                            <div class="placeBox">
                                <h3>${mapM.data.place.realPlace}</h3>
                                <p>地址：${place.detailPlace}</p>
                            </div>
                            <div class="setCheckbtn" onclick="mapO.placeHandle('choose')">选择</div>
                        </div>`, //设置文本标注内容
                direction: 'right' //设置文本标注方位
            });
        },
        confirmChoose: function () {
            if(!mapM.data.chooseFlag) {
                tytTool.toastShow('请先点击地图选择定位点位置', 2500);
                return false
            }
            mapO.placeHandle('confirm');
            $(".mapBox,.setLabelBox").hide();
            $(".agentAddress").html(mapM.data.place.detailPlace).attr('data-lng',mapM.data.place.lng).attr('data-lat',mapM.data.place.lat)
            $("#tipinput").val(mapM.data.place.detailPlace)
            mapM.data.chooseFlag = false;
        },
        closeLabel: function (event) {
            event.stopPropagation();
            $(".setLabelBox").hide()
            mapO.changeMarker(mapM.data.realLngLat)
            if($("#tipinput").val() == '') {
                mapM.data.chooseFlag = false;
            }
        },
        showMap: function (_this) {
            if($("#tytCity").val() == '' || $("#tytCity").attr('data-val') =='') {
                tytTool.toastShow('请先选择所属地', 2500)
                return false;
            }
            var X = _this.offset().left,
                Y = _this.offset().top + _this.height();
            //加载插件结构
            $('.mapBox').removeAttr('style').css({
                'left': X,
                'top': Y,
            }).show();
            if($(".agentAddress").html() == '') {
                if(mapM.data.initMarker[0] != '' && mapM.data.initMarker[1] != '') {
                    mapO.changeMarker(mapM.data.initMarker)
                }
            } else {
                var arr = [Number($(".agentAddress").attr('data-lng')),Number($(".agentAddress").attr('data-lat'))]
                if(arr[0] != '' && arr[1] != '') {
                    mapO.changeMarker(arr)
                    mapO.Geocoder(arr)

                }
            }
            mapM.data.localLngLat = mapM.data.initMarker
        },
        closeMap: function () {
            mapM.data.localLngLat = []
            mapM.data.realLngLat = ''
            $(".mapBox,.setLabelBox").hide();
            // $(".agentAddress").html('')
            $("#tipinput").val('')
        }
        
    }
}

var mapM = cityMap.myData;
var mapO = cityMap.opt;

mapO.init()

mapM.map.on('click', function (e) {
    if(e.originEvent.target.nodeName !== 'CANVAS') return;
    mapO.changeMarker([e.lnglat.lng, e.lnglat.lat])
    mapO.Geocoder([e.lnglat.lng, e.lnglat.lat])
})