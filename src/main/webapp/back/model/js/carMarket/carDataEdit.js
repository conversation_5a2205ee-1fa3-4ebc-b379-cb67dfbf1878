/*
    carDataEdit.js for carDataEdit.hmtl
    author: hey<PERSON>an
*/
var edit = {
    myData: {
        config: {
            id: '',
            value: '',
            pictures: ''
        }
    },
    arr: {
        add: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        change: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        delete: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        deteleData: function(data) {
            if (data.code == 200) {
                commonM.del.obj.remove();
                commonO.operate('close');
                tytTool.toastShow('删除成功！', 2000);
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000)
                }
            }
        },
        changeData: function(data, other) {
            if (data.code == 200) {
                $(other.that).parents('tr').find('img').attr('src', other.base)
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000)
                }
            }
        },
        addData: function(data) {
            if (data.data && data.code == 200) {
                tytTool.reloadWin()
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000)
                }
            }
        }
    },
    opt: {
        loadList: function() {
            // tytTool.loadCheckTab(commonM.carDataEdit)
            tytTool.colResizable($(".tableBox"))
        },
        listOpt: function() {
            editA.common.data = $.extend({}, arr, page)
            editA.common.otherUrl = tytTool.getRootPath_web() + '/used/car/sale/getList?currentPage=' + currentPage + '&pageSize=' + editM.pageSize;
            tytTool.ajaxFn(editA.common, editJ.listData)
        },
        addType: function(_this, _type) { //添加"增加单项"编辑框
            $(".addOptline").remove()
            var td = '',
                fa = _this.siblings('.dataTable').find('.tableBox');
            if (_type == '长度' || _type == '自重' || _type == '载重') {
                td = '';
            } else if (_type == '车型') {
                td = `<td><div class="uploadBox">
                    <input class="upload" onchange="editO.fileChange(this)" onclick="commonO.clearFile(event)" type="file" title=" " accept="image/jpeg,image/jpg,image/png">
                </div></td>`
            }
            var str = `<tr class="addOptline"><td><input tname="dataname" class="editInput" type="text" maxlength="20"></td>${td}
                <td>
                    <a class="info_opt" onclick="$(this).parents('.addOptline').remove()">取消</a>
                    <span class="line_span">|</span>
                    <a class="info_opt" onclick="editO.addLine($(this),'${_type}')">确定</a>
                </td>
            </tr>`;
            fa.append(str);
            fa.find('.editInput').focus()
        },
        addDom: function(_type, arr) { //编辑成功后插入列表DOM----暂时废弃
            var td = '';
            if (_type == '长度' || _type == '自重' || _type == '载重') {
                td = '', opt = '';
            } else if (_type == '车型') {
                td = `<td><img src="${arr.pictures[0].picture}" alt="" onclick="commonO.lookBigImg($(this),'Small')"  ondblclick="return false;"></td>`
                opt = `<span class="line_span">|</span><a class="info_opt" onclick="editO.changeLineImg($(this),'','')">修改图片</a>`
            }
            var str = `<tr><td>${arr.value}</td>${td}
                <td><a class="info_opt" data-type="${_type}" onclick="commonO.operate('open',$(this))">删除</a>${opt}</td>
            </tr>`;
            return str;
        },
        fileChange: function(_this, hiddenFile) { //处理Base64,插入DOM
            var base = '',
                fileArr = tytTool.fileInfo(_this, 3);
            setTimeout(function() {
                base = fileArr.result[0];
                if (hiddenFile == 'hiddenFile') {
                    editA.change.otherUrl = `${market}manage/config/update`;
                    editA.change.otherData = {
                        // 'hiddenFile':hiddenFile,
                        'that': _this,
                        'base': base
                    };
                    editA.change.data = $.extend({}, commonM.common, {
                        'id': editM.config.id,
                        'value': editM.config.value,
                        'pictures': [{
                            'type': 1,
                            'index': 1,
                            'picture': base
                        }]
                    })
                    tytTool.ajaxFn(editA.change, editJ.changeData);
                } else {
                    $(_this).parents('td').empty().append(`<img src="${base}" alt="" onclick="commonO.lookBigImg($(this),'Small')">`)
                }
            }, 50);
        },
        addLine: function(_this, _type) {
            var _id = '';
            switch (_type) {
                case '车型':
                    _id = 1;
                    break;
                case '长度':
                    _id = 2;
                    break;
                case '自重':
                    _id = 3;
                    break;
                case '载重':
                    _id = 4;
                    break;
            }
            var inp = _this.parents('.addOptline').find('.editInput')
            if (NO(inp.val()) == '') {
                tytTool.toastShow('请填写' + _type, 2000);
                return false;
            }
            if (_type == '车型' && $(".addOptline img").length != 1) {
                tytTool.toastShow('请上传图片', 2000);
                return false;
            }
            var arr = {
                "id": _id,
                "value": NO(inp.val())
            }
            if (_type == '长度' || _type == '自重' || _type == '载重') {

            } else if (_type == '车型') {
                arr = $.extend({}, arr, {
                    "pictures": [{
                        "type": 1,
                        "index": 1,
                        "picture": $(".addOptline img").attr('src')
                    }]
                })
            }
            editA.add.otherUrl = `${market}manage/config/add`
            editA.add.data = $.extend({}, commonM.common, arr)
            tytTool.ajaxFn(editA.add, editJ.addData);
        },
        changeLineImg: function(_this, _val, _id) {
            $(".changeUpload").remove()
            var file = `<input class="changeUpload" onchange="editO.fileChange(this,'hiddenFile')" onclick="commonO.clearFile(event)" type="file" title=" " accept="image/jpeg,image/jpg,image/png">`
            _this.after(file);
            $(".changeUpload").click();
            editM.config = {
                id: _id,
                value: _val,
                picture: ''
            }
        },
        deleteLine: function(_this) { //删除本行入口
            editA.delete.otherUrl = `${market}manage/config/delete`
            editA.delete.data = $.extend({}, commonM.common, {
                id: commonM.del.delId
            })
            tytTool.ajaxFn(editA.delete, editJ.deteleData)
        },
    }
}

var editM = edit.myData;
var editA = edit.arr;
var editJ = edit.ajx;
var editO = edit.opt;

//控制权限和列表加载的顺序
$.when(commonO.baseMsg()).done(function() {
    editO.loadList();
}).fail(function() {
    editO.loadList();
})