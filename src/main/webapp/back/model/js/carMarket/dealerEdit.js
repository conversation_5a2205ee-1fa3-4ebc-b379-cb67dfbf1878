/*
    dealerEdit.js for dealerEdit.hmtl
    author: hey<PERSON>an
*/
var TYPE = NO(tytTool.parseSearch().type);
var edit = {
    myData: {
        fileLen: 1,
        eachValid: [],
        valid: {
            one: [
                'input##agentName##请填写经销商名称',
                'select##brandId##请选择品牌代理',
            ],
            two: [
                // 'input##province##请填写所属地',
                // 'input##city##请填写所属地',
                // 'input##area##请填写所属地',
                // 'input##agentAddress##请填写详细地址', //暂时去除
                'input##cellPhone##请填写电话',
            ],
            vChange: [
                'span##entryTime##请填写入驻时间',
            ],
            vAdd: [
                'input##entryTime##请填写入驻时间',
            ],
            three: [
                'input##contact##请填写联系人',
                'input##position##请填写职位',
                'select##agentRole##请选择真实身份',
                'input##cellPhone##请输入联系电话'
            ],
            four: [
                'select##agentId##请选择经销商名称',
                'select##brandId##请选择品牌代理',
                // 'select##top##请选择是否置顶'
            ]
        },
        carType: [], // 车型
        brandList: [], // 品牌
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        reject: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        saveData: function(data) { // 提交成功
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 3000)
                setTimeout(function() {
                    tytTool.reloadUpWin()
                }, 2000);
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 3000)
                }
            }
        },
        backData: function(data) { // 回显成功
            if (data.code == 200) {
                var req = data.data;
                let cellPhone = String(req.cellPhone).split(',')
                req.cellPhone = cellPhone.length > 0 ? cellPhone[0] : '';
                req.cellPhone1 = cellPhone.length > 1 ? cellPhone[1] : '';
                req.cellPhone2 = cellPhone.length > 2 ? cellPhone[2] : '';
                if(req.agentInfo.length > 0) {
                    $.each(req.agentInfo,function (index,item) {
                        if(item.handleStatus == 0) {
                            let str = `<div class="add-info">
                                            <div class="outline">
                                                <label for="" class="car-label"><i class="must">*</i>代理品牌：</label>
                                                <span class="must">${item.brandName}</span>
                                                <a class="info_opt ml-40" onclick="editO.changeItem(${item.brandId})">修改品牌信息</a>
                                                <p class="must ml-40">红色为：品牌库未匹配到该品牌，需要去“品牌管理”品牌库添加后才可显示。</p>
                                            </div>
                                        </div>`
                            $('.add-info-box').append(str);
                            $('.type-check-box').eq(index).append(editO.getCarType());
                        } else {
                            editO.addMoreInfo();
                            $.each($('.type-check-box').eq(index).find('input'), function (ind,el) {
                                if(String(item.vehicleModelId).split(',').includes($(el).val())) {
                                    $(el).attr('checked', true)
                                }
                            });
                            $.each($('.brandTool').eq(index).find('option'), function (ind,el) {
                                if($(el).val() == item.brandId) {
                                    $(el).attr('selected', true)
                                }
                            });
                        }
                    })
                }
                tytTool.tnameGetHtml($('.detailListBox'), req);
                //是否启用回显
                if(req.agentStatus == 1) {
                    $(".switch-anim").attr('checked','checked')
                } else if(req.agentStatus == 0) {
                    $(".switch-anim").removeAttr('checked')
                }
                //地区回显
                var strArr = [NO(req.province), NO(req.city), NO(req.area)],
                placeStr = {
                    pro: NO(req.province),
                    city: NO(req.city),
                    county: NO(req.area)
                }
                $("#tytCity").val(tytTool.placeRuleTwo(placeStr,' ')).attr('data-val',tytTool.removeRuleStr(strArr,','))
                $(".agentAddress").attr('data-lng',req.longitude).attr('data-lat',req.latitude)
                editM.placeContent = tytTool.removeRuleStr(strArr,',')
                mapM.data.initMarker = [NO(req.longitude),NO(req.latitude)]
                // placeRule('sheng', 'shi', 'xian', 'position')
                //                 // loadCall('tyt_area_iframe', req.province)
                //                 // setAreaValues2(req.province, req.city, req.area, 'tyt_area_iframe');
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 3000)
                }
            }
        },
        confirmfun: function (data) { // 发起驳回的请求发起成功
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 2000);
                setTimeout(function () {
                    tytTool.reloadUpWin()
                }, 2000);
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 3000)
                }
            }
            editO.rejectLook('hide');
        },
    },
    opt: {
        loadDom: function() {
            var e = editM.valid;
            if (TYPE == 'change') { //修改经销商
                //根据字段treatStatus查看是否是"未处理"
                if(param.treatStatus == 0) {
                    $(".rejectBtn").show()
                }
                editO.backMsg()
                $(".addRemove").remove();
                editM.eachValid = [].concat(e.two,e.three,e.vAdd);
                $(".oneLine").removeClass('threeLine')
            } else if (TYPE == 'agent') { //新增经销商
                $(".changeRemove").remove()
                editM.eachValid = [].concat(e.one,e.two,e.three,e.vAdd)
                $(".threeLine").removeClass('oneLine')
            } else if (TYPE == 'brand') { //新增经销商代理品牌
                $(".currentPage,title").html('新增经销商代理品牌');
                $(".brandRemove").remove()
                editM.eachValid = e.four;
                $(".threeLine").removeClass('oneLine')
            }
            $(".detailListBox").show()
        },
        backMsg: function(params) {
            editA.common.otherUrl = `${market}Manage/Agent/read`
            editA.common.data = $.extend({}, commonM.common, {
                agentId: ID,
                isDetail: 1,
            })
            tytTool.ajaxFn(editA.common, editJ.backData)
        },
        subData: function(_this) { //保存入口(添加经销商、修改经销商、添加经销商代理品牌)
            let cooperateStatus = tytTool.htmlGetTname($('.radio-box')).cooperateStatus; // 经销商是否合作
            let getBrandList = editO.getBrandList(); // 代理信息列表
            if(!getBrandList) return false
            if (TYPE != 'brand') {
                if(NO($("#tytCity").val()) == '' || NO($("#tytCity").attr('data-val')) == '') {
                    tytTool.toastShow('请填写所属地',2000);
                    return false;
                };
            }
            if (commonO.eachValid(editM.eachValid)) {
                return false;
            }
            if (TYPE == 'change') { //修改经销商
                editA.common.otherUrl = `${market}Manage/Agent/update`;
                var update = {
                    agentId: ID,
                    agentRole: NO($("#agentRole option:selected").attr('data-val')),
                    agentStatus: $(".switch-anim").is(':checked') ? 1 : 0, //0:禁用 1:启用
                    description: $("#description").val()
                }
            } else if (TYPE == 'agent') { //新增经销商
                editA.common.otherUrl = `${market}Manage/Agent/add`
                var add = {
                    agentRole: NO($("#agentRole option:selected").attr('data-val')),
                    agentStatus: $(".switch-anim").is(':checked') ? 1 : 0, //0:禁用 1:启用
                    description: $("#description").val()
                }
            } else if (TYPE == 'brand') { //新增经销商代理品牌
                editA.common.otherUrl = `${market}Manage/Agent/addRelation`
            }
            var arr = tytTool.htmlGetTname($('.detailListBox'))
            arr.cellPhone = arr.cellPhone ? `${arr.cellPhone},${arr.cellPhone1},${arr.cellPhone2}` : ''
            editA.common.data = $.extend({}, commonM.common,arr, add, update, {
                "province": NO($("#tytCity").attr('data-val')).split(',')[0] || '',
                "city": NO($("#tytCity").attr('data-val')).split(',')[1] || '',
                "area": NO($("#tytCity").attr('data-val')).split(',')[2] || '',
                "longitude": $(".agentAddress").attr('data-lng'),
                "latitude": $(".agentAddress").attr('data-lat'),
                "agentInfo": getBrandList
            })
            console.log(editA.common.data);
            tytTool.ajaxFn(editA.common, editJ.saveData)
        },
        rejectExist: function () { // 提交驳回
            var reason = $('#auditFail').val();
            if (reason == '') { // 失败原因
                tytTool.toastShow('失败原因不能为空', 3000)
                return false;
            }
            editA.reject.otherUrl = `${market}manage/agent/reject`
            editA.reject.data = $.extend({},commonM.common, {
                'agentId': param.id,
                'rejectReason': reason
            })
            tytTool.ajaxFn(editA.reject, editJ.confirmfun)
        },
        getBrandList: function () {
            var arrList = [];
            let flag = false; // 未勾选完
            $('.add-info').each((ind,ele) => {
                var arr = [];
                let checkItem = $(ele).find('input[name="carType"]:checked')
                $(ele).find('select').each((index,element) => {
                    if(!$(element).val()) {
                        tytTool.toastShow('请选择代理品牌')
                        flag = true
                        return false
                    }
                    arrList[ind] = {brandId: $(element).val()}
                })
                if(checkItem.length == 0 ) {
                    tytTool.toastShow('请勾选代理车型')
                    flag = true
                    return false
                }
                checkItem.each(function(index, element) {
                    //追加到数组中
                    arr.push($(element).val());
                });
                arrList[ind] = {...arrList[ind],vehicleModels: arr.join(',')}
            })
            if(flag) return false
            return arrList;
        },
        getCarType: function (v) {
            editM.carType = editM.carType.length > 0  ? editM.carType : v[0].children;
            let labelStr = ''
            editM.carType.forEach(item => {
                labelStr += `<label><input type="checkbox" tname="vehicleModelId" class="car-checkbox" name="carType" value="${item.id}" > ${item.value}</label>`
            });
            return labelStr
        },
        getBrand: function() {
            let brand = ''
            editM.brandList.forEach(item => {
                brand += `<option class="firstOption" tname="item.brandId" value="${item.brandId}">${item.brandName}</option>`
            })
            return brand
        },
        addMoreInfo: function () {
            if($('.type-check-box').length >= 10) {
                tytTool.toastShow('最多添加10条信息');
                return false
            }
            let str =  `<div class="add-info">
                <div class="outline">
                    <label for="" class="car-label"><i class="must">*</i>代理品牌：</label>
                    <select class="brandTool" tname="brandId">
                        <option class="firstOption" value="">请选择</option>
                    </select>
                    <button class="button del-btn" onclick="editO.delateInfo($(this))">删除当前代理信息</button>
                </div>
                <div class="outline car-type">
                    <label for="" class="car-label"><i class="must">*</i>代理车型：</label>
                    <div class="type-check-box"></div>
                </div>
            </div>`;
            $('.add-info-box').append(str);
            let currentLength = $('.type-check-box').length -1;
            $('.type-check-box').eq(currentLength).append(editO.getCarType());
            $('.brandTool').eq(currentLength).append(editO.getBrand())
            if($('.type-check-box').length > 1) {
                $('.del-btn').show()
            }
        },
        delateInfo: function (_this) {
            $(_this).parents('.add-info').remove()
            if($('.type-check-box').length <= 1) {
                $('.del-btn').hide()
            }
        },
        changeItem: function(_id) { //跳转新增和修改页面携带参数
            var id = NO(_id) == '' ? '' : '&id=' + _id
            var _url = 'brandEdit.html?userId=' + commonM.common.userId + id;
            tytTool.openWinAuto(_url, 'changeItem'+_id, 1000, 600)
        },
        rejectLook: function (type) { // 显示隐藏驳回框
            if(type == 'hide') {
                $('#confirmRefundModal,.outBgColor').fadeOut(100);
                $('#auditStatus,#auditFail,#failureDescription').val('');
            } else if(type == 'show') {
                $('#confirmRefundModal,.outBgColor').fadeIn(100);
            }
        },
        tytCity: function (params) {
            $("#tytCity").tytCity({
                "componentBox": "#componentBox",
                "cityTit": ['省份', '城市', '县区'],
                "standardname": true,
                callback: function (arr, str, val) {
                    var len = arr.length;
                    var mk = [Number(arr[len - 1].longitude), Number(arr[len - 1].latitude)];
                    //添加初始经纬度
                    mapM.data.initMarker = mk
                    mapO.changeMarker(mk)
                    mapM.data.place = {
                        address: '',
                        pro: NO(val.split(',')[0]),
                        city: NO(val.split(',')[1]),
                        district: NO(val.split(',')[2]),
                        detailPlace: ''
                    }
                    mapO.placeHandle()
                    if(editM.placeContent !== val) {
                        editM.placeContent = val
                        $(".agentAddress").html('')
                        $("#tipinput").val('')
                    }
                },
                cleanCallback: function () {
                    $(".agentAddress").html('')
                }
            });
        },
    },
}
var editM = edit.myData;
var editA = edit.arr;
var editJ = edit.ajx;
var editO = edit.opt;
window.onload = function() {
    //控制权限和列表加载的顺序
    $.when(
        commonO.toolSearch('agent##agentId,brand').then(res => {
            editM.brandList = res.data.brand
        }),
        commonO.baseMsg('车型', {
            callback: editO.getCarType
        })
    ).done(function() {
        editO.loadDom();
        $('.type-check-box').append(editO.getCarType())
    }).fail(function() {
        editO.loadDom();
    })
}



// AmapUse({
//     // usePolyline: true,
//     mk: [114.376602, 36.878120],
//     callback: (AMap, mapUse) => {
//         //渲染右侧列表
//         // detailsO.creatList(arrayLngLat)
//     }
// })