var invoice = tytTool.getRootPath_web();
const robStatusArray =  [
    '0##待接单','1##接单成功','2##货主拒绝','3##系统拒绝',
    '4##同意装货','5##车主装货完成','6##系统装货完成',
    '7##异常上报','8##货主撤销货源退款',
    '9##系统撤销货源退款','10##车主取销装货', '11##接单失败',
    '12##车方取消订单', '13##异常处理完成','15##车方已发起延迟付款'
];
var costStatusArray = [
    '15##已支付',
    '21##拒绝退款',
    '25##异常上报',
    '30##退款中',
    '35##已退款',
    '40##已打款',
    '45##自动收款',
    '50##异常完成',
    '55##延迟付款',
    '211##拒绝退款延迟付款'
]


var orderDetails = {
    myData: {
        travelingTrack: '', //存储轨迹信息数据
        orderType:[
            "applyOrder##待申报订单",
            "abnormalOrder##异常订单",
            "payOrder##待支付订单",
            "invoiceOrder##待开票订单"
        ],
        driverData:'', //切换司机暂存信息
        carMessageData:'', //切换车辆暂存信息
        carOwnerTel:'', // 车主手机号
        orderStatus:'', //订单状态   1基础信息更改2订单信息修改
        driverContent: '' , // 司机名称
        carContent: '', //车头车牌号
        waybillNo:''// 运单编号
        //暂用mock
        //localMock: `http://localhost/tyt_manage_new/src/main/webapp/back/model/js/InvoiceManagement/chentest__json.json`,
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
            // contentType:'application/json',
        },
        search: {
            url: '',
            otherUrl: '',
            contentType:'application/x-www-form-urlencoded',
            type: 'get',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        backOrderData: function (data,parms) { // 回显订单成功
            if (data.code == 200) {
                var req = data.data;
                const btnTmp = `<div class="innerBtn btnTrun">
                    <button class="btnClose" onclick="commonO.operate('open','confirm',{idList:${parms.stimulateActivityId},type:2,detail:true})">确认刷单</button>
                    <button class="toReview" onclick="commonO.operate('open','relieve',{idList:${parms.stimulateActivityId},type:3,detail:true})">解除嫌疑</button>
                </div>`
                // req.transportOrders.publishType = req.transportMain.publishType
                // 刷单进入 url 不存在 exceptionOrder
                var type = tytTool.parseSearch(true);
                if(!type.exceptionOrder){
                    //客服刷单信息
                    if(req.transportOrdersRisk.activityStatus==2||req.transportOrdersRisk.activityStatus==3) {
                        $('#customServer').show();
                        $('#btnBox').hide().html();
                        tytTool.tnameGetHtml($('#bilInfoTitle'), req.transportOrdersRisk);
                    }else if(req.transportOrdersRisk.activityStatus==1) {
                        $('#btnBox').html(btnTmp).show();
                    }

                    req.fileInfoList && req.fileInfoList.length>0 && $.each(req.fileInfoList,function (index,item) {
                        const imgTmp =$( `<li class="carImgBox">
                        <div class="imgItem">
                            <img class="imgPositive"  src="" alt="" onclick="commonO.lookBigImg('papersImgBox')">
                        </div>
                    </li>`);
                        imgTmp.find('img').attr('src',item.smallFilePath);
                        $('#papersImgBox').append(imgTmp);
                    })

                    tytTool.tnameGetHtml($('#infoBox'), req.transportWaybill);

                    var dom = '',html=''
                    if(req.transportWaybill && req.transportWaybill.orderList && req.transportWaybill.orderList.length > 0){
                        $.each(req.transportWaybill.orderList, function(index, ele) {
                            var cTime = ele.agreeTime?getFormatDateByLong(ele.agreeTime, 'yyyy-MM-dd hh:mm:ss'):''
                            var eTime = ele.loadTime?getFormatDateByLong(ele.loadTime, 'yyyy-MM-dd hh:mm:ss'):''
                            dom+='<li class="threeLine fl"><label for="">车主ID：</label><span>'+ele.payUserId+'</span></li>\
						<li class="threeLine fl"><label for="">车主注册账号：</label><span>'+ele.payCellPhone+'</span></li>\
						<li class="threeLine fl"><label for="">车主联系电话：</label><span>'+ele.payLinkPhone+'</span></li>\
						<li class="threeLine fl"><label for="">车主支付金额：</label><span>'+ele.payAmount/100+'元</span></li>\
						<li class="threeLine fl"><label for="">运费金额：</label><span>'+ele.carriageFee+'元</span></li>\
						<li class="threeLine fl"><label for="">车主支付时间：</label><span>'+getFormatDateByLong(ele.payEndTime, 'yyyy-MM-dd hh:mm:ss')+'</span></li>\
						<li class="threeLine fl"><label for="">同意装货时间：</label><span>'+cTime+'</span></li>\
						<li class="threeLine fl"><label for="">装货完成时间：</label><span>'+eTime+'</span></li>\
						<li class="threeLine fl"><label for="">创建时间：</label><span>'+getFormatDateByLong(ele.ctime, 'yyyy-MM-dd hh:mm:ss')+'</span></li>\
						<li class="threeLine fl"><label for="">修改时间：</label><span>'+getFormatDateByLong(ele.mtime, 'yyyy-MM-dd hh:mm:ss')+'</span></li>\
						<li class="threeLine fl"><label for="">订金状态：</label><span>'+ tytTool.switchItem(ele.costStatus,costStatusArray)+'</span></li>'
                            if(ele.costStatus===35){
                                dom+='<li class="threeLine fl"><label for="">退款原因：</label><span>'+ele.refundReason+'</span></li>'
                            }
                            html+='<ul class="detailListBox cf ">'+dom+'</ul>'
                            dom = ''
                        });
                    }
                    $('.order_receiving').empty().append(html)
                }
                req.carLocation.avgSpd?req.carLocation.avgSpd = Math.floor(req.carLocation.avgSpd * 100) / 100:'';
                req.carLocation.totalMlg?req.carLocation.totalMlg = Math.floor(req.carLocation.totalMlg * 100) / 100:'';
                tytTool.tnameGetHtml($('#carInfo'), req.carLocation);
                const goodsPoint= {
                    startPoint:{
                        lng: req.transportMain.startLongitudeValue/1000000,
                        lat: req.transportMain.startLatitudeValue/1000000,
                    },
                    endPoint:{
                        lng: req.transportMain.destLongitudeValue/1000000,
                        lat: req.transportMain.destLatitudeValue/1000000,
                        content:''
                    }
                }
                // tytTool.tnameGetHtml($('#waybilInfo'), req.transportOrders);
                if(req.carLocationList && req.carLocationList!== null){
                    const lineArr = [];
                    req.carLocationList.forEach((element) => {
                        lineArr.push([element.lon, element.lat])
                    })
                    // 实例化地图调用
                    AmapUse({
                        usePolyline: true,
                        lineArr: lineArr,
                        carLocationList:req.carLocationList,
                        goodsPoint: goodsPoint,
                        callback: (AMap, mapUse) => {
                            //渲染右侧列表
                            // detailsO.creatList(req.carLocationList)
                        }
                    })
                } else{
                    $('.trajectoryMapText').html('暂无轨迹')
                    $('.trajectoryMap').css('display','none')
                }
            } else {
                if (NO(data.message) != '') {
                    tytTool.toastShow(data.message, 3000)
                }
            }
        }
    },
    opt: {
        creatList: function (array) {
            if (array.length > 0) {
                detailsM.travelingTrack = array;
                detailsO.openInfoWindow($(this),1,'最新位置');
                detailsO.openInfoWindow($(this),array.length-1,'终点');
            }
        },
        backOrdermessage: function () { //回显订单信息入口
            var tsOrderNo = tytTool.parseSearch(true);
            detailsA.common.otherUrl = `${invoice}/ordersRisk/getOrdersRiskDetail`;
            if(tsOrderNo.exceptionOrder && tsOrderNo.exceptionOrder === "1"){
                detailsA.common.otherUrl = `${invoice}/stimulate/getCarLocationDetail`;
            }
            detailsA.common.otherData = $.extend({},tsOrderNo);
            detailsA.common.data = $.extend({},tsOrderNo);
            tytTool.ajaxFn(detailsA.common, detailsJ.backOrderData)

        },
        openInfoWindow(_this, index,text) {
            $(_this).addClass('active').siblings().removeClass('active')
            if(detailsM.travelingTrack[index]) {
                let list = {
                    lng: detailsM.travelingTrack[index].lon,
                    lat: detailsM.travelingTrack[index].lat,
                    title: text,
                    time: detailsM.travelingTrack[index].gtm,
                    //text: detailsM.travelingTrack[index].curAddr
                }
                mapUse.useInfoWindow(list);
            }

        }
    },
}
var detailsM = orderDetails.myData;
var detailsA = orderDetails.arr;
var detailsJ = orderDetails.ajx;
var detailsO = orderDetails.opt;
var type = tytTool.parseSearch(true);
if(type.exceptionOrder && type.exceptionOrder === "1"){
    $("#infoBox").remove();
    $("#btnBox").remove();
    $(".titUrl").eq(0).hide();
    $(".titUrl").eq(1).show();
    $(".newTrack").show();
}
//控制权限和列表加载的顺序
$.when(
   //  detailsO.backDrivermessage(),  // 司机车辆
    detailsO.backOrdermessage(),   // 订单详情
).done(function () {
    // detailsO.loadDom()
}).fail(function () {
    // detailsO.loadDom()
})
