// JavaScript Document

window.onload = function()
  {
    var aP = document.getElementsByClassName("navbarH3");
	var aH3 = document.getElementsByClassName("navbarh3Color");	
	var aBtn = document.getElementsByClassName("fold");
	var aUl = document.getElementsByClassName("navbarUl");
	var i = 0;
	for ( i = 0; i < aP.length; i++){ // 调用三次onclick,对应三次不同的内容
		
	    var f = function(){ //一个对象
	    var j = i;  //对象的属性
		aP[j].onclick = function()
		{ 
		   if(aUl[j].style.display == "none")
			 {  
				aUl[j].style.display = "block";
				//aBtn[z].className = "down"
				//aH3[j].className = "active";
			 }
			 else
			 {
				aUl[j].style.display = "none";  
				//aBtn[z].className = "up";
				//aH3[j].className = "navbarh3Color";
			 }
		 }
	};
	f();
	
 }
}
