// companyComplete.js  auther: hyy
var pageSize = 30;
var goods = {
	arr: {
		getListData: {
			url: '',
			type: 'post',
			contentType: 'application/json; charset=utf-8',
			data: ''
		}
	},
	ajaxfn: {
		getListData: function(data) {
			var _data = data.data;
			var data_list = _data.list;
			var data_menus = _data.menus;
			$("#companyComplete tr:not('.tableTh')").remove();
			var arrParams = {
				currentPage: _data.pageNo || 1,
				totalPages: _data.maxPage || 1
			}
			if (data_list && _data.maxPage > 0) {
				$("#totalPages").html(_data.maxPage);
				$("#currentPage").html(_data.pageNo);
				$("#rowCount").html(_data.rowCount);
				tytTool.jqPaginatorExtend(arrParams, 'goodsOptfn.getListMsg')
			} else {
				$("#totalPages").html(0);
				$("#currentPage").html("");
				$("#rowCount").html(0);
				tytTool.jqPaginatorExtend(arrParams, 'goodsOptfn.getListMsg')
			}
			//正式载入数据
			var _html = '';
			if (data_list && data_list.length > 0) {
				$.each(data_list, function(index, el) {


				});
				$("#companyComplete").append(_html);
				tytTool.bindHidePhone()
			}
		}
	},
	optFn: {
		getListMsg: function(currentPage) {
			currentPage == '' ? currentPage = 1 : currentPage = currentPage;
			goodsArr.getListData.url = '/boss/car/businessApply/getBusinessApply?currentPage=' + currentPage + '&pageSize=' + pageSize + '&menuId=' + tytTool.parseSearch().menuId
			var getListData = {
				"channelType": $("#channelType").val(),
				"beginCtime": $("#beginCtime").val(),
				"endCtime": $("#endCtime").val(),
				"beginNextTime": $("#beginNextTime").val(),
				"endNextTime": $("#endNextTime").val(),
				"status": $("#status").val() == '' ? '' : Number($("#status").val()),
				"cellPhone": $("#cellPhone").val(),
				"phone1": $("#phone1").val(),
				"salesman": $("#salesman").val()
			}
			goodsArr.getListData.data = JSON.stringify(getListData)
			tytTool.ajaxFn(goodsArr.getListData, goodsAjaxfn.getListData)
		},
		checkForm: function() {
			var arr = {
				"totalPages": 1,
				"pageSize": pageSize,
				"visiblePages": 7,
				"currentPage": 1,
				"funName": goodsOptfn.getListMsg
			}
			tytTool.addResetIdent($('.searchUl'))
			tytTool.jqPaginator('#pageNumberBox', arr)
		},
		toggleOutlook: function() {
			$(".outBgColor,.unvalidType").toggle();
		}
	}
}

var goodsArr = goods.arr;
var goodsAjaxfn = goods.ajaxfn;
var goodsOptfn = goods.optFn;

var arrAll = {
	"totalPages": 1,
	"pageSize": pageSize,
	"visiblePages": 7,
	"currentPage": 1,
	"funName": goodsOptfn.getListMsg
}
tytTool.jqPaginator('#pageNumberBox', arrAll);

$(".tableBox").colResizable({
	liveDrag: true,
	draggingClass: "dragging",
	resizeMode: 'flex',
	minWidth: 80
});

$(document).click(function(event) {
	$("#boxDiv,#boxDiv2").hide();
});
$(".areaDiv").click(function(event) {
	event.stopPropagation();
});