@charset "utf-8";
/* CSS Document */

* {
	margin: 0;
	padding: 0;
}

/*body {
	 font:normal 14px '微软雅黑', Verdana, Geneva, sans-serif;
	 color:#333;
	}*/

	
.mtl20 {
    height: 0;
    overflow: hidden;
    margin:20px 0 0 20px;	
	}
	
ul {
	list-style: none;
	white-space: nowrap;
}
.clear {
	clear: both;
}
.clearfix:after {
  display: block;
  content: '';
  clear: both;
}

.positionInput {
   width:232px;
   height:25px;
   line-height:25px;
   border:1px solid #ccc;
   padding-left:5px;
   font:normal 14px '微软雅黑', Verdana, Geneva, sans-serif;	
	}
	
.wrap {
   position:relative;	
	}
	
#boxDiv {
   width:240px;
   position:absolute;
   margin-top:5px;
   z-index:999;	
	}
	
.closeBtn {
  position:absolute;
  right:0px;
  top:0px;
  cursor:pointer;	
	}

#tabs {
	width:242px !important;
	position:relative !important;
	z-index:999 !important;
}
	
#tabs ul li {
	width:78px;
	height: 34px;
	background:#fff;
	float: left !important;
	/*margin-right:8px;*/
	text-align: center;
	cursor: pointer;
	border-right:1px solid #ccc;
}
	
#tabs ul li input {
   width:80px;
   height:34px;
   // background:#f5f5f5 url(../images/selectDown.png) no-repeat 112px center;
   background: #f8f8f8;
   *padding-top:2px;
   padding-top:2px\9;
   text-align:center;
   font:normal 16px '微软雅黑', Verdana, Geneva, sans-serif;
   cursor:pointer;
   border:1px solid #eaeaea;
   outline:none;
   float:left;
   color:#333;
   font-size: 14px;
   	}

#tabs ul li input.active {
   background:#fff;
   color:#eb3030;
   border:1px solid #eaeaea;
   border-bottom:none;
   font-size: 14px;
   outline:none;
	}

#divs {
  overflow: hidden;
  background: #fff;
  border:1px solid #eaeaea;    
  padding-bottom: 14px;
}

#divs div {
	width: 240px !important;
	display:none;    
  overflow: hidden;
	padding: 10px 0 6px;
}

#u1 {
  float: left;
  width: 194px;
  overflow: hidden;
}
#divs #u1 li,
#divs #div2 li,
#divs #div3 li{
	float: left!important;
  padding: 0 5px;
  height: 18px;
  line-height: 18px!important;
  text-align: center;
  margin-left: 8px;
	cursor:pointer;
   margin-bottom: 6px;
	font:normal 12px '微软雅黑', Verdana, Geneva, sans-serif;
}
#div2 ul li,#div3 ul li {
 
}
#divs #u1 li.active,
#divs #div2 li.active,
#divs #div3 li.active {
	background: #eb3030;
  color: #fff;
  border-radius: 4px;
}
#divs #u1 li:hover,
#divs #div2 li:hover,
#divs #div3 li:hover {
  background: #fbe6e6;
  color: #eb3030;
  border-radius: 4px;
}

#divs .btnP {
  width:206px;
  margin: 0 auto;
	}
	
#divs .btnP input {
  width:94px;
  height:28px;
  float:left;
  border:none;
  font:normal 14px '微软雅黑', Verdana, Geneva, sans-serif;
  cursor:pointer;
  outline:0;
 	}
	
#divs .btnP .confirmBtn {
  background-color:	#eb3030;
  color:#fff;
  border-radius:3px;
	}
	
#divs .btnP .clearBtn {
  background-color:#f8f8f8;
  color:#333;
  border-radius:3px;
  margin-right: 17px;
  border:1px solid #ccc;	
	}
  #divs #div1 .abcList {
    // height: 100%;
    float: left;
    width: 23px;
    padding: 0 10px;
  }
  #divs #div1 .abcList li {
    margin-left: 0;
    font-size: 12px;
    color:#333;
    font-weight: bold;
    line-height:22px;  
    font-family: '微软雅黑', Verdana, Geneva, sans-serif;
  }

