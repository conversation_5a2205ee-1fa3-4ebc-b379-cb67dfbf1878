//动态更改HOST
var website = getRootPath_web();
//本地测试用
//测试环境,生产中需注释
// var website = window.location.host;
// if (website == 'localhost') {
// 	var website = 'http://**************/manage_new';
// }
//测试环境,生产中需注释
var parsesearchId = parseSearch().userId;
var parsesearchGoodsId = parseSearch().goodsId;

String.prototype.startWith = function(s) {
	if (s == null || s == "" || this.length == 0 || s.length > this.length)
		return false;
	if (this.substr(0, s.length) == s)
		return true;
	else
		return false;
	return true;
};

// 基础信息管理 > 查询用户详情
function info_yhxq() {
	$.ajax({
		url: '' + website + '/manage/user/getDetail',
		type: 'get',
		dataType: 'jsonp',
		jsonp: 'jsoncallback',
		data: {
			userId: parsesearchId
		},
		success: function(data) {
			var innerData = data.data.user;
			var innerUserSub = data.data.userSub;
			var tytUserIdentityLabel = data.data.tytUserIdentityLabel;
			$('#infoNum').text(innerUserSub.initialNum);
			$('#carNum').text(innerUserSub.initialCarNum);
			$('#vehicleLevel').text(data.data.goodsrRankLevel); // 新增信用等级
			if(data.data.userIdentityAuth && typeof data.data.userIdentityAuth != "undefined") {
                var workType = data.data.userIdentityAuth.workType;
				$('#shefenStatus').text(data.data.userIdentityAuth.auditStatus);
            }
			if (!innerData) {
				return;
			}
			var lianlianMessage = data.data.lianlianAcctInfo;
			var acctBindCards = data.data.acctBindCards;
			var userClass = innerData.userClass;
			var verifyPhotoSign = innerData.verifyPhotoSign;
			var userSign = innerData.userSign;
			var source = innerData.source;
			var sourceRemark = innerData.sourceRemark;
			var deliver_type_one = innerData.deliver_type_one;
			var deliverType = innerData.deliverType;
			var identityType = innerData.identityType;
			var paymentReason = innerData.paymentReason;

			$('#lianlianAcctStatus').html(tytTool.switchItem(lianlianMessage.lianlianAcctStatus, [
				'ACTIVATE_PENDING##待激活(连连)',
				'NORMAL##已激活(连连)',
				'UNOPEN##未开户(连连)',
				'FAILED##开户失败(连连)',
				'REAL_NAME_NONE##未实名(满帮)',
				'PERSON_REAL_NAME_FOUR##个人四要素实名(满帮)',
				'PERSON_REAL_NAME_SIX##个人六要素实名(满帮)'
			]))

			if(lianlianMessage.lianlianAcctStatus==='ACTIVATE_PENDING'){
				// $('#lianlianAcctStatus').html('待激活')
			} else if(lianlianMessage.lianlianAcctStatus==='NORMAL'){
				// $('#lianlianAcctStatus').html('已激活')
				$(".acctBindCards").show()
				if(acctBindCards.length > 0) {
					var cards = '';
					$.each(acctBindCards,function (index,ele) {
						if(ele.linkedAcctno != '') {
							cards += ele.linkedAcctno + ','
						}
					})
					cards = cards.substring(0,cards.length-1)
					$("#acctBindCards").append(cards).attr('tip',cards)
				}
			} else if(lianlianMessage.lianlianAcctStatus==='UNOPEN'){
				// $('#lianlianAcctStatus').html('未开户')
			} else if(lianlianMessage.lianlianAcctStatus==='FAILED'){
				// $('#lianlianAcctStatus').html('开户失败')
				$('.lianlianAcctFailReason').css('display','inline-block')
				$('.lianlianAcctFailReason').css('width','40%')
				$('#lianlianAcctFailReason').html(lianlianMessage.lianlianAcctFailReason)
				$('#lianlianAcctFailReason').css('overflow','inherit')
			}

			$("#detai_userid").html(innerData.id);
			$("#detai_trueName").html(innerData.trueName);
			$("#detai_userName").html(innerData.userName);
			$("#detai_idCard").html(innerData.idCard);
			$("#detai_idCard").attr("tip", innerData.idCard);
			$("#detai_cellPhone").html(innerData.cellPhone);
			$("#detai_qq").html(innerData.qq);
			if (innerData.sex) {
				if (innerData.sex == 1) {
					$("#detai_sex").html('男');
				} else {
					$("#detai_sex").html('女');
				}
			}
			$("#detai_ctime").html(getFormatDateByLong(innerData.ctime, "yyyy-MM-dd hh:mm"));
			// if (innerData.userType) {
			if (innerData.userType == 0) {
				$("#detai_userType").html('试用');
			} else if (innerData.userType == 1) {
				$("#detai_userType").html('付费');
			} else if (innerData.userType == 2) {
				$("#detai_userType").html('未激活');
			}
			// }
			if (innerData.serveDays || innerData.serveDays == 0) {
				$("#detai_serveDays").html(innerData.serveDays + '天');
			}
			// else {
			// 	$("#detai_serveDays").html('天');
			// }
			$("#detai_sales").html(innerData.sales);
			if (innerData.platId == 1) {
				$("#detai_platId").html('PC');
			} else if (innerData.platId == 2) {
				$("#detai_platId").html('Android');
			} else if (innerData.platId == 3) {
				$("#detai_platId").html('IOS');
			} else if (innerData.platId == 4) {
				$("#detai_platId").html('apad');
			} else if (innerData.platId == 5) {
				$("#detai_platId").html('ipad');
			} else if (innerData.platId == 6) {
				$("#detai_platId").html('WEB');
			} else if (innerData.platId == 7) {
				$("#detai_platId").html('PHONE WEB');
			}
			if (innerData.province && innerData.city) {
				//省市县规则
				var province = innerData.province,
					city = innerData.city,
					county = innerData.county,
					region = "";

				if (city.startWith(province)) {
					region = city;
				} else {
					region += province + "/" + city;
				}
				if (!county && county != undefined && county != city) {
					region += "/" + county;
				}
				$("#detai_prov_city").html(region);
				$("#detai_prov_city").attr("tip", region);
			} else {
				$("#detai_prov_city").html(innerData.province);
				$("#detai_prov_city").attr("tip", innerData.province);
			}
			$("#detai_c1").html(innerData.c1);
			$("#detai_c1").attr("tip", innerData.c1);
			$("#detai_note").html(innerData.note);
			$("#detai_note").attr("tip", innerData.note);
			$("#detai_recommenderTel").html(innerData.recommenderTel);
			// getFormatDateByLong(time, "yyyy-MM-dd hh:mm")
			if (innerData.appointTime) {
				$("#detai_appointTime").html(getFormatDateByLong(innerData.appointTime, "yyyy-MM-dd hh:mm"));
			}
			if (innerData.payDate) {
				$("#detai_payDate").html(getFormatDateByLong(innerData.payDate, "yyyy-MM-dd hh:mm"));
			}
			if (innerData.endTime) {
				$("#detai_endTime").html(getFormatDateByLong(innerData.endTime, "yyyy-MM-dd"));
			}

			if (!innerData.renewalDate) {
				if (!innerData.payDate) {
					// return false;
				} else {
					$("#detai_jiaofei").html(getFormatDateByLong(innerData.payDate, "yyyy-MM-dd hh:mm"));

				}
			} else {
				$("#detai_jiaofei").html(getFormatDateByLong(innerData.renewalDate, "yyyy-MM-dd hh:mm"));
			}
			if (innerData.payNumber) {
				$("#detai_payNumber").html(innerData.payNumber + '次');
			}
			// else {
			// 	$("#detai_payNumber").html('次');
			// }
			if (innerData.renewalYears) {
				$("#detai_renewalYears").html(innerData.renewalYears + '年');
			}
			// else {
			// 	$("#detai_renewalYears").html('年');

			// }
			if (innerData.money) {
				$("#detai_money").html(parseFloat(innerData.money / 100).toLocaleString() + '元');

			}
			// else {
			// 	$("#detai_money").html('元');
			// }
			$("#detai_bank").html(innerData.bank);
			if (innerData.verifyPhotoSign == 0) {
				$("#detai_verifyFlag").html('未认证');
			} else if (innerData.verifyPhotoSign == 1) {
				$("#detai_verifyFlag").html('通过');
			} else if (innerData.verifyPhotoSign == 2) {
				$("#detai_verifyFlag").html('认证中');
			} else if (innerData.verifyPhotoSign == 3) {
				$("#detai_verifyFlag").html('认证失败');
				$("#verifyFlagDetail").show();
//				$("#verifyFlagDetail").css('display','block');

			}
			if (innerData.isCar == 0) {
				$("#detai_isCar").html('未完善');
			} else if (innerData.isCar == 1) {
				$("#detai_isCar").html('完善');
			}

			$("#detai_source").html(source)
			if (innerData.infoUploadFlag == 0) {
				$("#detai_infoUploadFlag").html('信息保留');
			} else if (innerData.infoUploadFlag == 1) {
				$("#detai_infoUploadFlag").html('信息禁止');
			} else if (innerData.infoUploadFlag == 2) {
				$("#detai_infoUploadFlag").html('信息允许');
			}
			if (innerData.infoPublishFlag == 1) {
				$("#detai_infoPublishFlag").html('发布禁止');
			} else if (innerData.infoPublishFlag == 2) {
				$("#detai_infoPublishFlag").html('发布允许');
			}
			if (innerData.phoneOpenFlag == 0) {
				$("#detai_phoneOpenFlag").html('未开通');
			} else if (innerData.phoneOpenFlag == 1) {
				$("#detai_phoneOpenFlag").html('开通');
			}
			if (innerData.qqBoxFlag == 0) {
				$("#detai_qqBoxFlag").html('关闭');
			} else if (innerData.qqBoxFlag == 1) {
				$("#detai_qqBoxFlag").html('打开');
			}

			// // 用户来源+来源备注
			// $.ajax({
			// 	url: '' + website + '/boss/tytsource/getListForGroupCode',
			// 	type: 'get',
			// 	dataType: 'jsonp',
			// 	jsonp: 'jsoncallback',
			// 	data: {
			// 		groupCode: 'source_' + source
			// 	},
			// 	success: function(data) {
			// 		$.each(data, function(index, el) {
			// 			if (data[index].value == sourceRemark) {
			// 				$("#detai_GroupCode").html(data[index].name)
			// 			}
			// 		});
            //
			// 	},
			// 	error: function(XHR, textStatus, errorThrown) {
            //
			// 	}
			// });
			$('#detai_GroupCode').html(sourceRemark);
			// 销售一级二级身份
			$.ajax({
				url: '' + website + '/boss/tytsource/getSource',
				type: 'get',
				dataType: 'jsonp',
				jsonp: 'jsoncallback',
				data: {
					groupCode: 'user_deliver_type_one',
					value: deliver_type_one
				},
				success: function(data) {
					if (!data.data) {
						return;
					}
					$.each(data.data.subset, function(index, el) {
						if (data.data.subset[index].value == deliverType) {
							$("#detai_type_one_two").html(data.data.name + '/' + data.data.subset[index].name)
							$("#detai_type_one_two").attr("tip", data.data.name + '/' + data.data.subset[index].name)
						}
					});

				},
				error: function(XHR, textStatus, errorThrown) {

				}
			});
			// 注册身份：
			$.ajax({
				url: '' + website + '/boss/tytsource/getSource',
				type: 'get',
				dataType: 'jsonp',
				jsonp: 'jsoncallback',
				data: {
					groupCode: 'user_identity_type_' + userClass,
					value: identityType
				},
				success: function(data) {
					if (!data.data) {
						return
					}
					$("#detai_nameclass").html(data.data.name)

				},
				error: function(XHR, textStatus, errorThrown) {

				}
			});

			//权限身份
			$.ajax({
				url: '' + website + '/boss/tytsource/getSource',
				type: 'get',
				dataType: 'jsonp',
				jsonp: 'jsoncallback',
				data: {
					groupCode: 'user_sign',
					value: userSign
				},
				success: function(data) {
					if (!data.data) {
						return
					}
					$("#detai_user_sign").html(data.data.name)

				},
				error: function(XHR, textStatus, errorThrown) {

				}
			});


			//从事工作

			$.ajax({
				url: '' + website + '/boss/tytsource/getListForGroupCode',
				type: 'post',
				dataType: 'jsonp',
				jsonp: "jsoncallback",
				data: {
					groupCode: 'user_work_type'
				}
			}).done(function(data) {
				var workType1 = workType + "";
				var workTypeArray = workType1.split(",");
				var workTypeNames = "";
				$.each(data, function(index, el) {
					$.each(workTypeArray, function(index1, el) {

						if (data[index]['value'] == workTypeArray[index1]) {
							workTypeNames += "，" + data[index]['name'];
						}
					});

					var subsworkTypeNames = workTypeNames.substring(1);
					$("#work_type").html(subsworkTypeNames);

				});

			}).fail(function() {
				console.log("error");
			});
			//未付费原因
			$.ajax({
				url: '' + website + '/boss/tytsource/getSource',
				type: 'get',
				dataType: 'jsonp',
				jsonp: 'jsoncallback',
				data: {
					groupCode: 'payment_reason',
					value: paymentReason
				},
				success: function(data) {
					if (!data.data) {
						return
					}
					$("#detai_paymentReason").html(data.data.name)
					$("#detai_paymentReason").attr("tip", data.data.name)
				},
				error: function(XHR, textStatus, errorThrown) {

				}
			});

			// 用户注册、审核一二级身份展示
			$("#goodsTypeFirst").html(tytUserIdentityLabel.goodsTypeFirstName)
			$("#goodsTypeSecond").html(tytUserIdentityLabel.goodsTypeSecondName)
			$("#auditGoodsTypeFirst").html(tytUserIdentityLabel.auditGoodsTypeFirstName)
			$("#auditGoodsTypeSecond").html(tytUserIdentityLabel.auditGoodsTypeSecondName)
			// 非有效身份展示货主审核身份备注
			if(tytUserIdentityLabel.auditGoodsTypeSecond == 4){
				$("#auditGoodsTypeRemarkBox").show();
				$("#auditGoodsTypeRemark").html(tytUserIdentityLabel.auditGoodsTypeRemark);
			}else{
				$("#auditGoodsTypeRemarkBox").hide();
			}

			tytUserIdentityLabel
			if (!innerUserSub) {
				return;
			}

			//标签数据信息
			bcarIdentityLables = split_str(innerUserSub.bcarIdentityLables)
			auditBcarIdentityLables = split_str(innerUserSub.auditBcarIdentityLables)
			scarIdentityLables = split_str(innerUserSub.scarIdentityLables)
			auditScarIdentityLables = split_str(innerUserSub.auditScarIdentityLables)


			$("#detai_maintainMan").html(innerUserSub.maintainMan);

			//板车业务 [用户选择+管理员选择]
			$.ajax({
				url: '' + website + '/boss/tytsource/getListForGroupCode',
				type: 'post',
				dataType: 'jsonp',
				jsonp: 'jsoncallback',
				data: {
					groupCode: 'bcar_identity_labels'
				},
				success: function(data) {
					var _html = '';
					var _text = '';
					$.each(bcarIdentityLables, function(ind, elem) {
						$.each(data, function(index, el) {
							if (data[index].value == bcarIdentityLables[ind]) {
								_html += data[index].name + ' / ';
							}
						});

					});
					_html = _html.substr(0, _html.length - 2)
					$("#detai_bcarIdentityLables").html(_html);

					$.each(auditBcarIdentityLables, function(ind, elem) {
						$.each(data, function(index, el) {
							if (data[index].value == auditBcarIdentityLables[ind]) {
								_text += data[index].name + ' / ';
							}
						});
					});
					_text = _text.substr(0, _text.length - 2)
					$("#detai_auditBcarIdentityLables").html(_text)
				},
				error: function(XHR, textStatus, errorThrown) {

				}
			});

			//设备业务 [用户选择+管理员选择]
			$.ajax({
				url: '' + website + '/boss/tytsource/getListForGroupCode',
				type: 'post',
				dataType: 'jsonp',
				jsonp: 'jsoncallback',
				data: {
					groupCode: 'scar_identity_labels'
				},
				success: function(data) {
					var _html = '';
					var _text = '';
					$.each(scarIdentityLables, function(ind, elem) {
						$.each(data, function(index, el) {
							if (data[index].value == scarIdentityLables[ind]) {
								_html += data[index].name + ' / ';
							}
						});
					});
					_html = _html.substr(0, _html.length - 2)
					$("#detai_scarIdentityLables").html(_html);

					$.each(auditScarIdentityLables, function(ind, elem) {
						$.each(data, function(index, el) {
							if (data[index].value == auditScarIdentityLables[ind]) {
								_text += data[index].name + ' / ';
							}
						});

					});
					_text = _text.substr(0, _text.length - 2)
					$("#detai_auditScarIdentityLables").html(_text)
				},
				error: function(XHR, textStatus, errorThrown) {

				}
			});



		},
		error: function(XHR, textStatus, errorThrown) {

		}
	});
}
function jumpEnterprise(id) {
    if(NO(id) === '') return false;
    var _url = window.location.origin + '/manageInvoice/enterpriceDetail?id='+ id +'&selectRow=VIEW';
    tytTool.openWinAuto(_url,'企业列表详情', 1200, 600);
}
// 货源信息管理 > 货物信息详情
function info_goods() {
	$.ajax({
		url: '' + website + '/boss/transport/getDetail',
		type: 'get',
		dataType: 'jsonp',
		jsonp: 'jsoncallback',
		data: {
			goodsId: parsesearchGoodsId
		},
		success: function(data) {
			console.log(data)
			var innerData = data.data;
			$("#userId").html(NO(innerData.userId));
			$("#userName").html(NO(innerData.userName));
			$("#nickName").html(NO(innerData.nickName));
			$("#tel").html(NO(innerData.tel));
			$("#id").html(NO(innerData.id));
            $("#pubDate").html(tytTool.timeNull(NO(innerData.pubDate), "yyyy-MM-dd hh:mm"));
            $("#loadingTime").html(tytTool.timeNull(NO(innerData.loadingTime), "yyyy-MM-dd hh:mm"));
            $("#unloadTime").html(tytTool.timeNull(NO(innerData.unloadTime), "yyyy-MM-dd hh:mm"));
			//出发地
            $("#startDetailAdd").html(NO(innerData.startDetailAdd)).attr('tip',NO(innerData.startDetailAdd));
            $("#destDetailAdd").html(NO(innerData.destDetailAdd)).attr('tip',NO(innerData.destDetailAdd));
            $("#taskContent").html(NO(innerData.taskContent));
            $("#taskContent").attr('tip',NO(innerData.taskContent));
            $("#goodTypeName").html(NO(innerData.goodTypeName)).attr('tip',NO(innerData.goodTypeName));
            $("#useCarType").html(NO(innerData.useCarType));
            $("#weight").html(NO(innerData.weight));
            // if (NO(innerData.length) && NO(innerData.wide) && NO(innerData.high)) {
			var l_w_h = (NO(innerData.length) || '-') + ' / ' + (NO(innerData.wide) || '-') + ' / ' + (NO(innerData.high) || '-') + '&nbsp;&nbsp;(米)'
            $("#goods_l_w_h").html(l_w_h).attr('tip',l_w_h);
            // }
            //装货时间

            $("#price").html(NO(innerData.price));


			if(innerData.publishType && innerData.publishType == 1){
				$("#publishType").html('电议');
			}else if(innerData.publishType && innerData.publishType == 2){
				$("#publishType").html('一口价');
			}

			$("#infoFee").html(NO(innerData.infoFee));

			// if(innerData.infoFee !== ''){ //金额为空,不展示 退还/不退还 标签
				innerData.refundFlag == 1 ? $('.re_flag').html('(退还)') : $('.re_flag').html('(不退还)') //添加金额的标签
			// }
			$("#tecServiceFee").html(NO(innerData.tecServiceFee));

			$("#shuntingQuantity").html(NO(innerData.shuntingQuantity));

            // if (NO(innerData.workPlaneMinHigh) && NO(innerData.workPlaneMaxHigh) && NO(innerData.carStyle) && NO(innerData.carType) ) {

				/*
				显示样式：
					11~13米/纯平/三线六轴
					最小长XX米/纯平/三线六轴
					最大长XX米/纯平/三线六轴
				*/
				// var str = (NO(innerData.workPlaneMinHigh) || '-') + ' / ' + (NO(innerData.workPlaneMaxHigh) || '-') + ' / ' + (NO(innerData.carStyle) || '-') + ' / ' + (NO(innerData.carType) || '-')
				var len = '-',
					minL = NO(innerData.carMinLength),
					maxL = NO(innerData.carMaxLength);
				if(maxL != '' && minL != '') {
					len = minL + '~' + maxL + '米'
				} else if(maxL != '' && minL == '')  {
					len = '长度最大' + maxL + '米'
				} else if(maxL == '' && minL != '')  {
					len = '长度最小' + minL + '米'
				} else {
					len = ' '
				}
				$('#len').html(len).attr('tip',len);

				$('#carType').html(NO(innerData.carType)).attr('tip',NO(innerData.carType));
            	$('#carStyle').html(NO(innerData.carStyle)).attr('tip',NO(innerData.carStyle));
            	var high = '-',
                    minHigh = NO(innerData.workPlaneMinHigh);
            		maxHigh = NO(innerData.workPlaneMaxHigh);
				if(maxHigh != '' && minHigh != '') {
                    high = minHigh + '~' + maxHigh + '米'
				} else if(maxHigh != '' && minHigh == '')  {
                    high = '工作面高最大' + maxHigh + '米'
				} else if(maxHigh == '' && minHigh != '')  {
                    high = '工作面高最小' + minHigh + '米 '
				} else {
                    high = ' '
				}
            	$('#workHigh').html(high).attr('tip',high)
				var workLeng = '-',
					minLeng = NO(innerData.workPlaneMinLength);
					maxLeng = NO(innerData.workPlaneMaxLength);
				if(maxLeng != '' && minLeng != '') {
                    workLeng = minLeng + '~' + maxLeng + '米'
				} else if(maxLeng != '' && minLeng == '')  {
                    workLeng = '工作面长最大' +  maxLeng + '米'
				} else if(maxLeng == '' && minLeng != '')  {
                    workLeng = '工作面长最大' + minLeng + '米'
				} else {
                    workLeng = ''
				}
				$('#workLength').html(workLeng).attr('tip',workLeng)
				if( NO(innerData.climb) == "1" ){
                    $('#climb').html('是');
				}else{
                    $('#climb').html('否');
				}
				//var str = len  + (NO(innerData.carType) || '-') + ' / ' + (NO(innerData.carStyle) || '-')
                //$("#needCar").attr('tip',str).html(str);
            // }
            $("#callsNumber").html(NO(innerData.callsNumber));
			if (NO(innerData.excellentGoods) == 0) {
				$("#goodsType").html('普通货源');
			} else if (NO(innerData.excellentGoods) == 1) {
				$("#goodsType").html('优车货源');
			} else if (NO(innerData.excellentGoods) == 2) {
				$("#goodsType").html('专车货源');
			}
            $("#remark").html(NO(innerData.remark));
			if (NO(innerData.infoStatus) == 0) {
				$("#infoStatus").html('待接单');
			} else if (NO(innerData.infoStatus) == 1) {
				$("#infoStatus").html('有人支付成功');
			} else if (NO(innerData.infoStatus) == 2) {
				$("#infoStatus").html('装货中');
			} else if (NO(innerData.infoStatus) == 3) {
				$("#infoStatus").html('车主装货完成 ');
			} else if (NO(innerData.infoStatus) == 4) {
				$("#infoStatus").html('系统装货完成');
			} else {
				$("#infoStatus").html('异常上报');
			}
			//状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
			if (NO(innerData.status) == 0) {
				$("#status").html('无效（已过期）');
			} else if (NO(innerData.status) == 1) {
				$("#status").html('有效（发布中）');
			} else if (NO(innerData.status) == 2) {
				$("#status").html('待定（QQ专用）');
			} else if (NO(innerData.status) == 3) {
				$("#status").html('阻止（QQ专用） ');
			} else if (NO(innerData.status) == 4) {
				$("#status").html('成交');
			} else {
				$("#status").html('取消状态');
			}

			$('#startPoint').html(NO(innerData.startPoint)).attr('tip',NO(innerData.startPoint))
            $('#destPoint').html(NO(innerData.destPoint)).attr('tip',NO(innerData.destPoint))
            $('#goodTypeName').html(NO(innerData.goodTypeName)).attr('tip',NO(innerData.goodTypeName))
            // var _url='' + website + '/back/model/html/transport/info_waybill.html?tsOrderNo='+NO(innerData.tsOrderNo);
            // // openWinAuto(_url,'运单详情',1200,600);
			//运单详情
            var ta='<a href="javascript:void(0);"  onclick="jumpOrder('+NO(innerData.tsOrderNo)+')">运单详情</a>';
            //信息费详情
			ta +='&nbsp;&nbsp;<a href="javascript:void(0);"  onclick="jumpInfoFee('+NO(innerData.tsOrderNo)+')">信息费详情</a>';
            $("#goods_info").html(ta);

			if (NO(innerData.invoiceTransport) == 1) {
				$("#invoiceTransport").html('是');
			} else {
				$("#invoiceTransport").html('否');
			}
			$("#tsOrderNo").html(NO(innerData.tsOrderNo));
			$("#enterpriseTaxRate").html(NO(innerData.enterpriseTaxRate));
			$("#additionalPrice").html(NO(innerData.additionalPrice));
			$("#priceCount").html(NO(innerData.priceCount));
			var enterpriseDom = `<a class="clickColor" href="javascript:;"  onclick="jumpEnterprise('${NO(innerData.enterpriseId)}')">${NO(innerData.enterpriseName)}</a>`
			$("#enterpriseName").html(NO(innerData.enterpriseName) === '' ? '' : enterpriseDom);
		},
		error: function(XHR, textStatus, errorThrown) {

		}
	});
}
$(".toReview").click(function (event) {
    var id = $("#id").html();
    $.ajax({
        dataType:"json",
        type:"post",
        data: {
            idList: id,
			status: 0
        },
        async:false,
        url:`${tytTool.getRootPath_web()}/admin/inforeview`,
        success:function(data){
            var code=data.code;
            var msg=data.msg;
            if(code==200){
                var successMsg="设置无效操作成功";
                //openWin("back/jsp/transport_update_success.jsp","statusUpdate");
                infoListShowAlert(successMsg);

            }else{
                //alert(msg);
                infoListShowAlert(msg)

            }

        },
        error:function(data){
            var errorMsg="失败"
            if(status==0)errorMsg="置无效已失败，请重试";
            if(status==1)errorMsg="置有效已失败，请重试";
            if(status==2)errorMsg="置待定已失败，请重试";
            if(status==4)errorMsg="置成交已失败，请重试";
            infoListShowAlert(errorMsg);

        }
    });
})


function infoListShowAlert(msg) {
	$("#infoListAlertMsgID").html(msg);
	$("#alert_danger").show();
};

function closeBtn(event) {
    $("#alert_danger").hide();
    window.location.reload();
}
function jumpOrder(tsOrderNo){
    var _url='' + website + '/back/model/html/transport/info_waybill.html?tsOrderNo='+tsOrderNo;
    tytTool.openWinAuto(_url,'运单详情',1200,600);
}
//跳转到信息费详情页面
function jumpInfoFee(tsOrderNo){
    var _url='' + website + '/back/model/html/transport/infoFee_detail.html?tsOrderNo='+tsOrderNo;
    tytTool.openWinAuto(_url,'信息费详情',1200,600);
}
function split_str(str) {
	if (str == undefined) {
		return ['']; //数据中未有项 , 返回空数组给后面$.each遍历
	}
	var strs = [];
	strs = str.split(",");
	return strs;
}

function getRootPath_web() {
	//获取当前网址，如： http://localhost:8083/uimcardprj/share/meun.jsp
	var curWwwPath = window.document.location.href;
	//获取主机地址之后的目录，如： uimcardprj/share/meun.jsp
	var pathName = window.document.location.pathname;
	var pos = curWwwPath.indexOf(pathName);
	//获取主机地址，如： http://localhost:8083
	var localhostPaht = curWwwPath.substring(0, pos);
	//获取带"/"的项目名，如：/uimcardprj
	var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
	return (localhostPaht + projectName);
}
//身份认证失败查看详情
function failureDetail() {
	var userId=$("#detai_userid").html();
	$.ajax({
		url: '' + website + '/manage/user/authFailureDetail',
		type: 'post',
		dataType: 'json',
		data: {
			userId: userId
		},
		success: function(data) {
			var detail = data.data;
			//审核人
			$("#examineUser").html(detail.examineUserName);
			//身份信息
			if(detail.infoStatus==0){
				$("#infoStatus").html('认证中');
			}else if(detail.infoStatus==1){
				$("#infoStatus").html('认证成功');
			}else if(detail.infoStatus==2){
				$("#infoStatus").html('认证失败');
				$("#infoStatusLi").show();
				$("#infoFailureReason").html(detail.infoFailureReason);
			}
			//身份证正面照
			if($("#detai_nameclass").html()=='板车司机'){
				$("#mainPhoto").html('驾驶证正面照：');
				if(detail.mainStatus==0){
					$("#mainStatus").html('认证中');
				}else if(detail.mainStatus==1){
					$("#mainStatus").html('认证成功');
				}else if(detail.mainStatus==2){
					$("#mainStatus").html('认证失败');
					$("#mainStatusLi").show();
					$("#mainFailureReason").html(detail.mainFailureReason);
				}
			}else{
				if(detail.mainStatus==0){
					$("#mainStatus").html('认证中');
				}else if(detail.mainStatus==1){
					$("#mainStatus").html('认证成功');
				}else if(detail.mainStatus==2){
					$("#mainStatus").html('认证失败');
					$("#mainStatusLi").show();
					$("#mainFailureReason").html(detail.mainFailureReason);
				}
			}
			//本人照
			if(detail.iPhotoStatus==0){
				$("#iPhotoStatus").html('认证中');
			}else if(detail.iPhotoStatus==1){
				$("#iPhotoStatus").html('认证成功');
			}else if(detail.iPhotoStatus==2){
				$("#iPhotoStatus").html('认证失败');
				$("#iPhotoStatusLi").show();
				$("#iPhotoFailureReason").html(detail.iPhotoFailureReason);
			}
			//企业营业执照
			if($("#detai_nameclass").html()=='运输公司或车队'){
				$("#licensePhoto").show();
				if(detail.licenseStatus==0){
					$("#licenseStatus").html('认证中');
				}else if(detail.licenseStatus==1){
					$("#licenseStatus").html('认证成功');
				}else if(detail.licenseStatus==2){
					$("#licenseStatus").html('认证失败');
					$("#licenseStatusLi").show();
					$("#licenseFailureReason").html(detail.licenseFailureReason);
				}
			}
		},
		error: function(XHR, textStatus, errorThrown) {

		}
	});
}
//车辆认证详情查看
function carAuthDetail() {
	var userId=$("#detai_userid").html();
	$.ajax({
		url:'' + website + '/manage/user/carAuthDetail',
		type: 'get',
		dataType: 'json',
		data: {userId:userId},
	}).done(function(data) {
		var data_list = data.data;
		var _html = '';
		if(data_list.length>0){
			$.each(data_list, function(index, el) {
				if (!data_list) {
					return false;
				}
				//车头认证结果
				var headAuthStatus = '';
				if (data_list[index].headAuthStatus == 0) {
					headAuthStatus = '认证中';
				} else if (data_list[index].headAuthStatus == 1) {
					headAuthStatus = '认证成功';
				} else if (data_list[index].headAuthStatus == 2){
					headAuthStatus = '失败';
				}
				//挂车认证结果
				var tailAuthStatus = '';
				if (data_list[index].tailAuthStatus == 0) {
					tailAuthStatus = '认证中';
				} else if (data_list[index].tailAuthStatus == 1) {
					tailAuthStatus = '认证成功';
				} else if (data_list[index].tailAuthStatus == 2){
					tailAuthStatus = '失败';
				}
				_html += '<ul class="isCarinfoDetailUl">\
					<li class="cf">\
					<div class="fl">\<label for="">车头车牌号：</label><span class="lessWidth">'+data_list[index].headCity+data_list[index].headNo+'</span></div>\
					<div class="fl"><label for="" style="width:115px;">车头认证结果：</label><span class="lessWidth">'+headAuthStatus+'</span></div>';
				if (data_list[index].headAuthStatus == 2){
					_html +='<div id="headAuth" class="fl"><label for="">失败原因：</label><span class="moreWidth">'+data_list[index].headFailReason+'</span></div>';
				}
				_html +='</li><li class="cf">\
					<div class="fl"><label for="">挂车车牌号：</label><span class="lessWidth">'+data_list[index].tailCity+data_list[index].tailNo+'</span></div>\
					<div class="fl"><label for="" style="width:115px;">挂车认证结果：</label><span class="lessWidth">'+tailAuthStatus+'</span></div>';
				if (data_list[index].tailAuthStatus == 2){
					_html += '<div id="tailAuth" class="fl"><label for="">失败原因：</label><span class="moreWidth">'+data_list[index].tailFailReason+'</span></div>';
				}
				_html +='</li></ul>';

			});
		}else{
			_html +='<ul class="noneCarPass" ><li>当前用户暂未发起车辆认证</li></ul>';
		}
		$(".isCarinfoDetailBox").html('');
		$(".isCarinfoDetailBox").append(_html);
	}).fail(function() {
		console.log("error");
	});
}


function getTag(){
    var header= {}
    if($.cookie('SESSION')) {
        header = Object.assign({},header,{
            'TYT-SESSION-ID':$.cookie('SESSION')
        })
    }
    if($.cookie('ssoInfo')) {
        header = Object.assign({},header,{
            'ssoInfo':$.cookie('ssoInfo')
        })
    }
    tytTool.ajaxFn({
        otherUrl: tytTool.getlocalhostPath_web()+ '/cargo/dispatch/tecServiceFee/getTransportTecServiceFeeLog?srcMsgId='+ parsesearchGoodsId ,
		type: 'get',
        headers: header,
        }, function(res) {
            if(res.code == 200) {
                let str = ''
                $.each(res.data,function (index,ele) {
                    str +=`<em>${ele.optionType== 1? '抽佣' : '不抽佣'} （ ${getFormatDateByLong(ele.createTime, "yyyy-MM-dd hh:mm:ss")} ）;  </em>`
                })
                console.log(str)
                $("#getTransportTecServiceFeeLog").html(str)
            }else {
                alert(res.msg)
            }
            console.log(res)
        })

   
	
}
getTag()