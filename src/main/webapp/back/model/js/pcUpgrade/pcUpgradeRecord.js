/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-11-04 10:55:30 
 * @Last Modified by: yuyan.He
 * @Last Modified time: 2021-11-08 18:24:49
 */
var record = {
    myData: {
        pageSize: 30,
        upgradeStatus: [
            '0##<span style="color:#15bb5f;">升级成功</span>',
            '1##<span style="color:#f33;">升级失败</span>',
            '2##<span style="color:#00a0e9;">升级中</span>',
            '3##<span style="color:#666;">跳过该版本</span>',
        ]
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'get',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        listData: function(data, _currentPage) {
            if (data.code == 200) {
                var data_list = data.data ? data.data.list : [];
                tytTool.pageFun({
                    total: data.data ? data.data.total : 0,
                    size: recordM.pageSize,
                    current: _currentPage,
                    pageCallback: 'recordO.recordOpt'
                })
                //正式载入数据
                var rule = {
                    list: [
                        '用户昵称##userName',
                        '用户手机号##cellPhone',
                        '升级前版本号##beforeVersion',
                        '升级后版本号##laterVersion',
                        '升级状态##upgradeStatus##Item::recordM.upgradeStatus'
                    ]
                }
                var tmp = tytTool.eachList(data_list, rule)
                $("#recordTable").empty().append(tmp.str)
                tytTool.colResizable($('.tableBox'));
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
    },
    opt: {
        loadList: function(type) {
            var pageArr = {
                "totalPages": 1,
                "pageSize": recordM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": recordO.recordOpt,
                "saveCurrent": true,
                "session": type
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        recordOpt: function(currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            recordA.common.otherData = currentPage;
            recordA.common.otherUrl = `${domain}/upgrade/task/get/upgraderecord`
            recordA.common.data = $.extend({}, arr, {
                "pageNum": currentPage,
                "pageSize": recordM.pageSize,
                "taskId": param.tId
            })
            tytTool.ajaxFn(recordA.common, recordJ.listData)
        }
    }
}

var recordM = record.myData;
var recordA = record.arr;
var recordJ = record.ajx;
var recordO = record.opt;

tytTool.loadCheckTab(commonM.checkItem.pcUpgradeRecord, {});
recordO.loadList();