var domain = tytTool.getRootPath_web();
var common = {
    myData: {
        data: {
            goodsOrder: {
                box: '.searchUl=>li',
                list: [{
                    label: '状态：##select',
                    attr: ['tname##status', "id##status"],
                    option: ['请选择##', '发布中##1', '已接单##3', '运输中##5', '已完成##4', '已取消##2', '已过期##6', '无效##7']
                }, {
                    label: '发货人电话：',
                    attr: ['tname##phone', 'maxlength##11', 'placeholder##请输入', 'onblur##commonO.inpBlurValid($(this),\'mobPhone\')'],
                },{
                    label: '订单编号：',
                    attr: ['tname##id', 'maxlength##20', 'placeholder##请输入', 'onblur##commonO.inpBlurValid($(this),\'number\')'],
                }, {
                    label: '货物名称：',
                    attr: ['tname##taskContent',  'placeholder##请输入', 'maxlength##20'],
                },{
                    label: '起始时间：',
                    attr: ['tname##startTime',  'placeholder##开始', 'id##startTime', 'readonly##readonly', 'onclick##commonO.startPicker("endTime")'],
                }, {
                    label: '结束时间：',
                    attr: ['tname##endTime',  'placeholder##结束', 'id##endTime', 'readonly##readonly', 'onclick##commonO.endPicker("startTime")'],
                },{
                    label: '出发地：',
                    attr: ['id##startPoint', 'placeholder##起始地', 'readonly##readonly', 'data-val##\'\'', 'onclick##commonO.tytCity(\'startPoint\')'],
                }, {
                    label: '目的地：',
                    attr: ['id##destPoint',  'placeholder##到达地', 'readonly##readonly', 'data-val##\'\'', 'onclick##commonO.tytCity(\'destPoint\')'],
                }]
            },
        },
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
    },
    ajx: {
        carLocationData: function (data,other) {
            if(data.code == 200) {
                if(data.data && NO(data.data.locationLongitude) != '' && NO(data.data.locationLatitude) != '') {
                    var lon = data.data.locationLongitude;
                    var lat = data.data.locationLatitude;
                    commonO.loadMap(other.mapLook,Number(lon),Number(lat))
                } else {
                    commonO.loadMap(other.mapLook)
                }
            } else {
                if(NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            }
        }
    },
    opt: {
        inpBlurValid: function (_this, _reg) {
            _this.val(_this.val())
            if (_reg != '' && !tytTool.validateStr(_reg, Number(_this.val()))) {
                _this.val('')
            }
        },
        tytCity: function (objStr) {
            $("#" + objStr).tytCity({
                "componentBox": "#" + objStr +'Cp',
                "cityTit": ['省份', '城市', '县区'],
                "cityCheck": true,
                callback: function (arr, str, val) {},
                clearCallback: function (el) {}
            });
        },
        startPicker: function (endTime) {
            //时间控件--开始
            return WdatePicker({
                maxDate: `#F{$dp.$D('${endTime}')}`,
                dateFmt: 'yyyy-MM-dd',
                onpicking: function (dp) {
                    var val = tytTool.NullObj(dp.cal.getNewDateStr());
                    var date = new Date(val),
                        timeString = Date.parse(date);
                    $(dp.el).attr('data-time', timeString);
                },
                oncleared: function (dp) {
                    $(dp.el).removeAttr('data-time');
                },
            });
        },
        endPicker: function (startTime) {
            //时间控件--结束
            console.log(startTime);
            return WdatePicker({
                minDate: `#F{$dp.$D('${startTime}')}`,
                dateFmt: 'yyyy-MM-dd',
                onpicking: function (dp) {
                    var val = tytTool.NullObj(dp.cal.getNewDateStr());
                    var date = new Date(val),
                        timeString = Date.parse(date);
                    $(dp.el).attr('data-time', timeString);
                },
                oncleared: function (dp) {
                    $(dp.el).removeAttr('data-time');
                },
            });
        },
        timeSet: function () {
            $("#startTime").val(tytTool.isToday(-2).dayStr)
            $("#endTime").val(tytTool.isToday().dayStr)
        },
        jumpTip: function (type,_this) { //跳转合集
            if(type == 'history') { //查看历史处理记录
                $(".complaintBox").hide()
                $(".historyItemBox").show()
                $(".comOutlook,.comOutlookBg").fadeIn(100)
            } else if(type == 'hide') { //隐藏合集框
                $(".comOutlook,.comOutlookBg").fadeOut(100)
            } else if(type == 'hideMap') {
                $(".car-container-warp").fadeOut(100)
            } else if(type == 'hideMapLook') {
                $(".car-container-warp,.comOutlookBg").fadeOut(100)
            }
        },
        loadMap: function(mapLook,lon,lat) { //成功后执行加载地图
            var marker, map;
            var config = {
                zoom: 3
            }
            if(lon && lon != '' && lat && lat != '') {
                config = {
                    zoom: 12,
                    center: [lon,lat] 
                }      
                var carIcon = new AMap.Icon({
                    // 图标尺寸
                    size: new AMap.Size(88, 29),
                    // 图标的取图地址
                    image: '../../images/detail_map_icon.png',
                    // 图标所用图片大小
                    imageSize: new AMap.Size(88, 29),
                });
                marker = new AMap.Marker({
                    // image: '../../images/detail_map_icon.png',
                    icon: carIcon,
                    size: new AMap.Size(25, 34),
                    offset: new AMap.Pixel(-44, -16)
                });
            }
            map = new AMap.Map("container", $.extend({}, {
                resizeEnable: true
            },config));

            marker && marker.setMap(map);

            if(mapLook == 'mapLook') {
                $(".comOutlookBg").fadeIn(100)
            }
            $(".car-container-warp").fadeIn(100)
        },
        loadCarLocation: function (event,mapLook,id) { //加载地图
            event.stopPropagation();
            commonA.common.otherData = {
                mapLook
            }
            commonA.common.otherUrl = `${domain}/publishGoods/getCarLocation`
            commonA.common.data = {
                id
            }
            tytTool.ajaxFn(commonA.common, commonJ.carLocationData)
        },
        viewTel: function (event,_this) { //查看手机号
	        event.stopPropagation();
            var li = '';
            var telArr = _this.attr('data-tel').split(',') || []
            $.each(telArr,function (index,ele) {
                li += `<li>${ele}</li>`
            })
            $(".user-tel-warp").empty().append(li);

            var X = $(_this).offset().left,
                Y = $(_this).offset().top + $(_this).height();
            //加载插件结构
            $('.view-user-tel').removeAttr('style').css({
                'left': (X - 185 < 0) ? 10 : X - 185, //防止超出可视区
                'top': Y
            }).show()
        },
        hideView: function () {
            $('.view-user-tel').removeAttr('style').hide()
        },
        sizeChange: function(len,width,height){
            let sizeInfo = '';
            let goodsLength = (len && len != '' && len) != 0 ? len : null
            let goodsWidth = (width && width != '' && width != 0) ? width : null
            let goodsHeight = (height && height != '' && height != 0) ? height : null
            if (goodsHeight && goodsLength && goodsWidth) {
                sizeInfo = `${goodsLength}*${goodsWidth}*${goodsHeight}米`
            } else if(!goodsWidth && goodsHeight && goodsLength) {
                sizeInfo = `长${goodsLength}*高${goodsHeight}米`
            } else if(!goodsHeight && goodsWidth && goodsLength) {
                sizeInfo = `长${goodsLength}*宽${goodsWidth}米`
            } else if(!goodsLength && goodsWidth && goodsHeight) {
                sizeInfo = `宽${goodsWidth}*高${goodsHeight}米`
            } else if(!goodsLength && !goodsWidth && goodsHeight) {
                sizeInfo = `高${goodsHeight}米`
            } else if(!goodsLength && !goodsHeight && goodsWidth) {
                sizeInfo = `宽${goodsWidth}米`
            } else if(!goodsWidth && !goodsHeight && goodsLength) {
                sizeInfo = `长${goodsLength}米`
            } 
            return sizeInfo
        },
        tranStr: function (arr) {
            return arr.filter(function (s) {
                return s && s.trim();
            }).join(',')
        }
    }
}

var commonM = common.myData;
var commonA = common.arr;
var commonJ = common.ajx;
var commonO = common.opt;


$(document).click(function(event) {
	$(".view-user-tel").hide();
});
$(".view-user-tel,.showTell").click(function(event){
	event.stopPropagation();
});