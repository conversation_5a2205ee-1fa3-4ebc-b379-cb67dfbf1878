// carChoose.js  auther: zy
/*
***
*** 此页面为请求数据加载所有数据 至页面中
*   同时包含一些页面中的操作方法  如 联动车头品牌 型号 马力 等
*   初始化定制输入input  左侧导航相关等
***
 */
var pageSize = 30;
var carDataInitEnt = {
    pageAllInitData:'',
    arr: {
        headBrandData:'',
        cdCommon: {
            url: '',
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded; charset=utf-8',
            data: '',
            otherUrl:''
        }
    },
    ajaxfn: {
        rendePage: function(data) {
            if(data.code == 200){
                var data = data.data;
                carDataInitEnt.pageAllInitData = data;
                var nameStr = '';
                for(var i in data){
                    nameStr += '<li role="presentation"><a href="#">'+ data[i].name +'</a></li>';
                }
                $('#navLeft').html(nameStr);
                
                //板车品牌
                var trailBrandStr = '';
                $('#trailBrandName').html('<i class="mustRed isShow">*</i>'+ data.trailBrand.name + '：');
                for(var i=0;i<data.trailBrand.detail.length;i++){
                    trailBrandStr += '<label for="trailBrand'+ i +'"><input type="radio" name="trailBrand" id="trailBrand'+ i +'" class="f12 ml10" value="'+ data.trailBrand.detail[i] +'">'+ data.trailBrand.detail[i] + '</label>';
                    trailBrandStr += '<div class="defaultTit" style="display: inline-block;"><i class="defaultIcon mr-3 wh-icon">?</i></div>';
                }
                $('.trailBrandBox').html(trailBrandStr);

                //板车型号
                var trailModelStr = '';
                $('#trailModelName').html('<i class="mustRed isShow">*</i>'+ data.trailModel.name + '：');
                for(var i=0;i<data.trailModel.detail.children.length;i++){
                    for(var j=0;j<data.trailModel.detail.children[i].children.length;j++) {
                        trailModelStr += '<label class="trailModelLabel" for="trailModel' + j + i +'"><input type="radio" name="trailModel" class="f12 ml10" id="trailModel' + j + i +'" value="' + data.trailModel.detail.children[i].value + data.trailModel.detail.children[i].children[j].value +'">' + data.trailModel.detail.children[i].value + data.trailModel.detail.children[i].children[j].value +'</label>';
                        i.toString() + j.toString() == '03'?trailModelStr+='<br/>':'';
                    }
                }
                $('.trailModelBox').html(trailModelStr);

                //板车长度
                var trailLengthStr = '';
                $('#trailLengthName').html('<i class="mustRed isShow">*</i>'+ data.trailLength.name + '：');
                for(var i=0;i<data.trailLength.detail.children.length;i++){
                    trailLengthStr += '<div class="length-item">';
                    for(var j=0;j<data.trailLength.detail.children[i].children.length;j++) {
                        trailLengthStr += '<div class="s-item"><h3>'+ data.trailLength.detail.children[i].value + '/' + data.trailLength.detail.children[i].children[j].value + '</h3>';
                        for(var e=0;e<data.trailLength.detail.children[i].children[j].children.length;e++) {
                            trailLengthStr += '<div class="length-right"><span class="left">'+ data.trailLength.detail.children[i].children[j].children[e].value +'：</span>'
                            for(var a=0;a<data.trailLength.detail.children[i].children[j].children[e].children.length;a++) {
                                trailLengthStr += '<label class="trailLengthLabel" attr="trailLengthLabel" for="trailLength' + i + j + e + a +'"><input trailBrand="'+ data.trailLength.detail.children[i].value +'" trailCategory="'+ data.trailLength.detail.children[i].children[j].value +'" trailBrandAndtrailCategory="'+ data.trailLength.detail.children[i].value + '/' + data.trailLength.detail.children[i].children[j].value + '" trailModel="'+ data.trailLength.detail.children[i].children[j].children[e].value +'" type="radio" name="trailLength" id="trailLength' + i + j + e + a +'" class="f12 ml10" value="'+ data.trailLength.detail.children[i].children[j].children[e].children[a].value +'">' + data.trailLength.detail.children[i].children[j].children[e].children[a].value + '</label>';
                            }
                            trailLengthStr += '</div>';
                        }
                        trailLengthStr += '</div>';
                    }
                    trailLengthStr += '</div>';
                }
                $('.trailLengthBox').html(trailLengthStr);

                //马力与鞍座高度
                var headBrandStr = '';
                cdArr.headBrandData = data.saddleHeight.detail.children;
                $('#headPowerName').html('<i class="mustRed isShow">*</i>马力与鞍座高度：');
                headBrandStr += '<select name="headBrand" id="headBrand" class="viewPhoneinput fl w-150"><option class="firstOption" value="">请选择车头品牌</option>';
                for(var i=0;i<data.saddleHeight.detail.children.length;i++) {
                    headBrandStr += '<option class="firstOption" value="'+ data.saddleHeight.detail.children[i].value +'">'+ data.saddleHeight.detail.children[i].value +'</option>'
                }
                headBrandStr += '</select>';
                headBrandStr += '<select name="headModel" id="headModel" class="viewPhoneinput fl w-150"></select>';
                headBrandStr += '<select name="headPower" id="headPower" class="viewPhoneinput fl w-150"></select>';
                headBrandStr += '鞍座高度：<span id="saddleHeight"></span>';
                $('.headPowerBox').html(headBrandStr);

                //车轴规格
                var axleNormsStr = ''
                var axleNormsArr = [];
                $('#axleNormsName').html('<i class="mustRed isShow">*</i>'+ data.axleNorms.name + '：');
                for(var i=0;i<data.axleNorms.detail.children.length;i++){
                    for(var j=0;j<data.axleNorms.detail.children[i].children.length;j++){
                        for(var e=0;e<data.axleNorms.detail.children[i].children[j].children.length;e++){
                            axleNormsArr.push(data.axleNorms.detail.children[i].children[j].children[e].value);
                        }
                    }
                }
                var newAxleNormsArr = axleNormsArr.filter(function (element, index, array) {
                    return array.indexOf(element) === index;
                });
                for(var i= 0;i<newAxleNormsArr.length;i++){
                    axleNormsStr += '<label for="axleNorms'+ i +'" class="axleNormsLabel"><input type="radio" name="axleNorms" id="axleNorms'+ i +'" class="f12 ml10" value="'+ newAxleNormsArr[i] +'">'+ newAxleNormsArr[i] + '</label>';
                }
                axleNormsStr += '<div class="defaultTit" style="display: inline-block;"><i class="defaultIcon mr-3"></i><span title="轮胎型号与车轴关系表"></span></div>';
                $('.axleNormsBox').html(axleNormsStr);

                //车轴品牌
                var axleBrandStr = '';
                $('#axleBrandName').html('<i class="mustRed isShow">*</i>'+ data.axleBrand.name + '：');
                for(var i=0;i<data.axleBrand.detail.children.length;i++){
                    axleBrandStr += '<div class="length-item">';
                    for(var j=0;j<data.axleBrand.detail.children[i].children.length;j++) {
                        axleBrandStr += '<div class="s-item"><h3>'+ data.axleBrand.detail.children[i].value + '/' + data.axleBrand.detail.children[i].children[j].value + '</h3>';
                        for(var e=0;e<data.axleBrand.detail.children[i].children[j].children.length;e++) {
                            axleBrandStr += '<div class="length-right"><span class="left">'+ data.axleBrand.detail.children[i].children[j].children[e].value +'：</span>'
                            for(var a=0;a<data.axleBrand.detail.children[i].children[j].children[e].children.length;a++) {
                                axleBrandStr += '<label for="axleBrand' + i + j + e + a +'"><input type="radio"  trailBrand="'+ data.axleBrand.detail.children[i].value +'" trailCategory="'+ data.axleBrand.detail.children[i].children[j].value +'" trailBrandAndtrailCategory="'+ data.axleBrand.detail.children[i].value + '/' + data.axleBrand.detail.children[i].children[j].value + '" trailModel="'+ data.axleBrand.detail.children[i].children[j].children[e].value +'" name="axleBrand" id="axleBrand' + i + j + e + a + '" class="f12 ml10" value="' + data.axleBrand.detail.children[i].children[j].children[e].children[a].value + '">' + data.axleBrand.detail.children[i].children[j].children[e].children[a].value + '</label>';
                            }
                            axleBrandStr += '</div>';
                        }
                        axleBrandStr += '</div>';
                    }
                    axleBrandStr += '</div>';
                }
                $('.axleBrandBox').html(axleBrandStr);

                //载重
                var loadStr = ''
                var loadArr = [];
                $('#loadName').html('<i class="mustRed isShow">*</i>'+ data.load.name + '：');
                for(var i=0;i<data.load.detail.children.length;i++){
                    for(var j=0;j<data.load.detail.children[i].children.length;j++){
                        for(var e=0;e<data.load.detail.children[i].children[j].children.length;e++){
                            for(var g=0;g<data.load.detail.children[i].children[j].children[e].children.length;g++) {
                                loadArr.push(data.load.detail.children[i].children[j].children[e].children[g].value);
                            }
                        }
                    }
                }
                var newloadArr = loadArr.filter(function (element, index, array) {
                    return array.indexOf(element) === index;
                });
                for(var i= 0;i<newloadArr.length;i++){
                    loadStr += '<label for="load'+ i +'" attr="loadLabel"><input type="radio" name="load" id="load'+ i +'" class="f12 ml10" value="'+ newloadArr[i] +'">'+ newloadArr[i] + '</label>';
                }
                loadStr += '<div class="defaultTit" style="display: inline-block;"><i class="defaultIcon mr-3"></i><span title="不能做小数"></span></div>';
                $('.loadBox').html(loadStr);

                //颜色
                var colorStr = '';
                $('#colorName').html('<i class="mustRed isShow">*</i>'+ data.color.name + '：');
                for(var i= 0;i<data.color.detail.length;i++){
                    colorStr += '<label for="color'+ i +'"><input type="radio" name="color" id="color'+ i +'" class="f12 ml10" value="'+ data.color.detail[i] +'">'+ data.color.detail[i] + '</label>';
                }
                $('.colorBox').html(colorStr);

                //牵引销样式
                var tractionPinStyleStr = '<label for="tractionPinStyle"><input type="radio" trailBrand="" name="tractionPinStyle" class="f12 ml10" id="tractionPinStyle" value="无升降(厂配)">无升降(厂配)</label>';
                $('#tractionPinStyleName').html('<i class="mustRed isShow">*</i>'+ data.tractionPinStyle.name + '：');
                for(var i=1;i<data.tractionPinStyle.detail.children.length;i++){
                    tractionPinStyleStr += '<div class="length-right"><span class="left">'+ data.tractionPinStyle.detail.children[i].value +'：</span>';
                    for(var j=0;j<data.tractionPinStyle.detail.children[i].children.length;j++) {
                        tractionPinStyleStr += '<label for="tractionPinStyle' + j + i +'"><input trailBrand="' + data.tractionPinStyle.detail.children[i].value + '" type="radio" name="tractionPinStyle" class="f12 ml10" id="tractionPinStyle' + j + i +'" value="' + data.tractionPinStyle.detail.children[i].children[j].value +'">' + data.tractionPinStyle.detail.children[i].children[j].value +'</label>';
                    }
                    tractionPinStyleStr += '</div>';
                }
                $('.tractionPinStyleBox').html(tractionPinStyleStr);

                //轮胎样式
                var tireStyleStr = '';
                var tireStyleArr = [];
                $('#tireStyleName').html('<i class="mustRed isShow">*</i>'+ data.tireStyle.name + '：');
                for(var i= 0;i<data.tireStyle.detail.children.length;i++){
                    for(var j=0;j<data.tireStyle.detail.children[i].children.length;j++){
                        tireStyleArr.push(data.tireStyle.detail.children[i].children[j].value)
                    }
                }
                var newtireStyleArr = tireStyleArr.filter(function (element, index, array) {
                    return array.indexOf(element) === index;
                });
                for(var i= 0;i<newtireStyleArr.length;i++){
                    tireStyleStr += '<label for="tireStyle'+ i +'"><input type="radio" name="tireStyle" id="tireStyle'+ i +'" class="f12 ml10" value="'+ newtireStyleArr[i] +'">'+ newtireStyleArr[i] + '</label>';
                }
                tireStyleStr += '<div class="defaultTit" style="display: inline-block;"><i class="defaultIcon mr-3"></i><span title="提示：普通板的车轮胎可露可不露，轴线板的车轮胎必须露"></span></div>';
                $('.tireStyleBox').html(tireStyleStr);

                //板车样式
                var trailStyleStr = '';
                $('#trailStyleName').html('<i class="mustRed isShow">*</i>'+ data.trailStyle.name + '：');
                for(var i= 0;i<data.trailStyle.detail.length;i++){
                    trailStyleStr += '<label for="trailStyle'+ i +'"><input type="radio" name="trailStyle" id="trailStyle'+ i +'" class="f12 ml10" value="'+ data.trailStyle.detail[i] +'">'+ data.trailStyle.detail[i] + '</label>';
                }
                $('.trailStyleBox').html(trailStyleStr);

                //平台样式分类
                var platformStyleStr = '';
                $('#platformStyleName').html('<i class="mustRed isShow">*</i>'+ data.platformStyle.name + '：');
                for(var i=0;i<data.platformStyle.detail.children.length;i++){
                    platformStyleStr += '<div class="length-right"><span class="left">'+ data.platformStyle.detail.children[i].value +'：</span><div>';
                    for(var j=0;j<data.platformStyle.detail.children[i].children.length;j++) {
                        platformStyleStr += '<label for="platformStyle' + j + i +'"><input trailCategory="'+ data.platformStyle.detail.children[i].value +'" type="radio" name="platformStyle" class="f12 ml10" id="platformStyle' + j + i +'" value="' + data.platformStyle.detail.children[i].children[j].value +'">' + data.platformStyle.detail.children[i].children[j].value +'</label>';
                        j== 6?platformStyleStr+='<br/>':'';
                    }
                    platformStyleStr += '</div></div>';
                }
                $('.platformStyleBox').html(platformStyleStr);


                //鹅颈
                var vehicleStr = '';
                $('#vehicleName').html('<i class="mustRed isShow">*</i>鹅颈：');
                //////////鹅颈样式
                vehicleStr += '<span class="left weight-w">'+ data.vehicleEjingStyle.name +'：</span>';
                for(var i= 0;i<data.vehicleEjingStyle.detail.length;i++){
                    vehicleStr += '<label for="vehicleEjingStyle'+ i +'"><input type="radio" name="vehicleEjingStyle" id="vehicleEjingStyle'+ i +'" class="f12 ml10" value="'+ data.vehicleEjingStyle.detail[i] +'">'+ data.vehicleEjingStyle.detail[i] + '</label>';
                }
                //////////前鹅头样式
                vehicleStr += '<br/><span class="left  weight-w">'+ data.vehicleFrontStyle.name +'：</span>';
                for(var i= 0;i<data.vehicleFrontStyle.detail.length;i++){
                    vehicleStr += '<label for="vehicleFrontStyle'+ i +'"><input type="radio" name="vehicleFrontStyle" id="vehicleFrontStyle'+ i +'" class="f12 ml10" value="'+ data.vehicleFrontStyle.detail[i] +'">'+ data.vehicleFrontStyle.detail[i] + '</label>';
                }
                vehicleStr += '<br/><span class="left  weight-w" style="margin: 5px 0 -5px 0px;display: inline-block;">鹅颈宽度选择与定制：</span><div class="defaultTit" style="display: inline-block;"><i class="defaultIcon" style="margin: 2px 0 5px 0;"></i><span title="支持2位小数"></span></div>';
                //////////鹅颈宽度
                $('#vehicleEjingWidthName').html('<i class="mustRed isShow">*</i>'+ data.vehicleEjingWidth.name + '：');
                vehicleStr += '<div class="vehicleEjingWidth-item-box">';
                for(var i=0;i<data.vehicleEjingWidth.detail.children.length;i++){
                    vehicleStr += '<div class="length-item">';
                    for(var j=0;j<data.vehicleEjingWidth.detail.children[i].children.length;j++) {
                        vehicleStr += '<div class="width-pp-ca-item"><h3>'+ data.vehicleEjingWidth.detail.children[i].value + '/' + data.vehicleEjingWidth.detail.children[i].children[j].value + '</h3>';
                        for(var e=0;e<data.vehicleEjingWidth.detail.children[i].children[j].children.length;e++) {
                            vehicleStr += '<div class="length-right dashed dashed-padding"><span class="left">'+ data.vehicleEjingWidth.detail.children[i].children[j].children[e].value +'：</span>';
                            var vehicleArr = [];
                            for(var a=0;a<data.vehicleEjingWidth.detail.children[i].children[j].children[e].children.length;a++) {
                                for(var b=0;b<data.vehicleEjingWidth.detail.children[i].children[j].children[e].children[a].children.length;b++) {
                                    vehicleArr.push(data.vehicleEjingWidth.detail.children[i].children[j].children[e].children[a].children[b].value);
                                }
                            }
                            var newVehicleArr = vehicleArr.filter(function (element, index, array) {
                                return array.indexOf(element) === index;
                            });
                            for(var c=0;c<newVehicleArr.length;c++){
                                vehicleStr += '<label class="gray-font" attr="vehicleEjingWidthLabel" for="vehicleEjingWidth' + i + j + e + c +'"><input trailBrand="'+ data.vehicleEjingWidth.detail.children[i].value + '" trailCategory="'+ data.vehicleEjingWidth.detail.children[i].children[j].value + '" trailBrandAndtrailCategory="'+ data.vehicleEjingWidth.detail.children[i].value + '/' + data.vehicleEjingWidth.detail.children[i].children[j].value + '" trailModel="'+ data.vehicleEjingWidth.detail.children[i].children[j].children[e].value +'" type="radio" name="vehicleEjingWidth" id="vehicleEjingWidth' + i + j + e + c + '" class="f12 ml10" value="'+ newVehicleArr[c] +'">'+ data.vehicleEjingWidth.name + '：' + newVehicleArr[c] + '</label>';
                            }
                            vehicleStr += '</div>';
                        }
                        vehicleStr += '</div>';
                    }
                    vehicleStr += '</div>';
                }
                vehicleStr += '</div>';
                $('.vehicleBox').html(vehicleStr);


                //小翅膀
                var vehicleWingTypeStr = '';
                $('#vehicleWingTypeName').html('<i class="mustRed isShow">*</i>'+ data.vehicleWingType.name + '：');
                for(var i= 0;i<data.vehicleWingType.detail.length;i++){
                    vehicleWingTypeStr += '<label class="vehicleWingTypeLabel" for="vehicleWingType'+ i +'"><input type="radio" name="vehicleWingType" id="vehicleWingType'+ i +'" class="f12 ml10" value="'+ data.vehicleWingType.detail[i] +'">'+ data.vehicleWingType.detail[i] + '</label>';
                }
                vehicleWingTypeStr += '<div class="defaultTit" style="display: inline-block;"><i class="defaultIcon mr-3"></i><span title="（装小爬梯的车必须先装小翅膀，如不装小翅膀，则小爬梯不用装）"></span></div>';
                $('.vehicleWingTypeBox').html(vehicleWingTypeStr);

                //小翅膀安装方式
                var vehicleWingModeStr = '';
                $('#vehicleWingModeName').html('<i class="mustRed isShow">*</i>'+ data.vehicleWingMode.name + '：');
                for(var i=0;i<data.vehicleWingMode.detail.children.length;i++){
                    for(var j=0;j<data.vehicleWingMode.detail.children[i].children.length;j++) {
                        vehicleWingModeStr += '<label for="vehicleWingMode' + j + i +'"><input type="radio" name="vehicleWingMode" class="f12 ml10" id="vehicleWingMode' + j + i +'" value="' + data.vehicleWingMode.detail.children[i].children[j].value +'">' + data.vehicleWingMode.detail.children[i].children[j].value +'</label>';
                    }
                }
                $('.vehicleWingModeBox').html(vehicleWingModeStr);

                //鹅颈小爬梯
                var vehicleEjingLadderModeStr = '';
                $('#vehicleEjingLadderModeName').html('<i class="mustRed isShow">*</i>'+ data.vehicleEjingLadderMode.name + '：');
                for(var i=0;i<data.vehicleEjingLadderMode.detail.children.length;i++){
                    for(var j=0;j<data.vehicleEjingLadderMode.detail.children[i].children.length;j++) {
                        vehicleEjingLadderModeStr += '<label for="vehicleEjingLadderMode' + j + i +'"><input type="radio" name="vehicleEjingLadderMode" class="f12 ml10" id="vehicleEjingLadderMode' + j + i +'" value="' + data.vehicleEjingLadderMode.detail.children[i].children[j].value +'">' + data.vehicleEjingLadderMode.detail.children[i].children[j].value +'</label>';
                    }
                }
                $('.vehicleEjingLadderModeBox').html(vehicleEjingLadderModeStr);

                //爬梯选装与爬梯宽度
                var ladderStr = '';
                var ladderWidthArr = [];
                var ladderModeArr = [];
                var ladderRandom = '';
                $('#ladderWidthName').html('<i class="mustRed isShow">*</i>爬梯选装与爬梯宽度：');

                //爬梯宽度
                for(var i=0;i<data.ladderWidth.detail.children.length;i++) {
                    for(var j=0;j<data.ladderWidth.detail.children[i].children.length;j++) {
                        for(var e=0;e<data.ladderWidth.detail.children[i].children[j].children.length;e++){
                            ladderWidthArr.push(data.ladderWidth.detail.children[i].children[j].children[e].value);
                        }
                    }
                }
                for(var i=0;i<data.ladderWidth.detail.children.length;i++){
                    ladderStr += '<div class="length-item"><h3>'+ data.ladderMode.detail.children[i].value +'</h3>';
                    //爬梯安装方式
                    ladderStr += '<div class="length-right"><span class="left">爬梯安装方式：</span>';
                    for(var j=0;j<data.ladderMode.detail.children[i].children.length;j++) {
                        ladderStr += '<label attr="ladderModeLabel" class="ladderModeLabel" for="ladderMode' + i + j + '"><input trailBrand="'+ data.ladderMode.detail.children[i].value +'" type="radio" name="ladderMode" class="f12 ml10" id="ladderMode' + i + j + '" value="' + data.ladderMode.detail.children[i].children[j].value + '">' + data.ladderMode.detail.children[i].children[j].value + '</label>';
                    }
                    ladderStr += '</div>';


                    var newladderWidthArr = ladderWidthArr.filter(function (element, index, array) {
                        return array.indexOf(element) === index;
                    });
                    ladderStr += '<div class="length-right"><span class="left">爬梯宽度：</span>';
                    for(var c=0;c<newladderWidthArr.length;c++){
                        ladderStr += '<label attr="ladderWidthLabel" style="display:none;" class="ladderWidthLabel" for="ladderWidth' + c + i + '"><input trailBrand="'+ data.ladderMode.detail.children[i].value +'" type="radio" name="ladderWidth" class="f12 ml10" id="ladderWidth' + c + i + '" value="' + newladderWidthArr[c] + '">' + newladderWidthArr[c] + '</label>';
                    }
                    ladderStr += '<div class="defaultTit" style="display:none;"><i class="defaultIcon mr-3 pt-width"></i><span title="支持2位小数"></span></div>'
                    ladderStr += '</div>';
                    ladderStr += '</div>';
                }
                $('.ladderWidthBox').html(ladderStr);

                // 拼接与抽拉
                var spliceAxisPullStr = '';
                var spliceAxisPullArr = [];
                $('#spliceAxisPullName').html('<i class="mustRed isShow">*</i>'+ data.spliceAxisPull.name + '：');
                for(var i=0;i<data.spliceAxisPull.detail.children.length;i++){
                    for(var j=0;j<data.spliceAxisPull.detail.children[i].children.length;j++) {
                        spliceAxisPullArr.push(data.spliceAxisPull.detail.children[i].children[j].value);
                    }
                }
                var newSpliceAxisPullArr = spliceAxisPullArr.filter(function (element, index, array) {
                    return array.indexOf(element) === index;
                });
                for(var i= 0;i<newSpliceAxisPullArr.length;i++){
                    spliceAxisPullStr += '<label for="spliceAxisPull' + i +'"><input type="radio" name="spliceAxisPull" class="f12 ml10" id="spliceAxisPull' + i +'" value="' + newSpliceAxisPullArr[i] +'">' + newSpliceAxisPullArr[i] +'</label>';
                }
                spliceAxisPullStr += '<div class="defaultTit" style="display: inline-block;"><i class="defaultIcon mr-3"></i><span title="（抽拉范围为不超有效货台面长度的一半，拼接没有范围，直接由客户做定制化要求）"></span></div>'
                $('.spliceAxisPullBox').html(spliceAxisPullStr);

                //支腿安装价格
                var outriggerPriceStr = '<label for="outriggerPrice"><input trailBrand="" type="radio" name="outriggerPrice" class="f12 ml10" id="outriggerPrice" value="不安装(厂配)">不安装(厂配)</label>';
                $('#outriggerPriceName').html('<i class="mustRed isShow">*</i>'+ data.outriggerPrice.name + '：');
                for(var i=0;i<data.outriggerPrice.detail.children.length;i++){
                    outriggerPriceStr += '<div class="length-right"><span class="left">'+ data.outriggerPrice.detail.children[i].value +'：</span>';
                    for(var j=0;j<data.outriggerPrice.detail.children[i].children.length;j++) {
                        outriggerPriceStr += '<label for="outriggerPrice' + j + i +'"><input trailBrand="'+ data.outriggerPrice.detail.children[i].value +'" type="radio" name="outriggerPrice" class="f12 ml10" id="outriggerPrice' + j + i +'" value="' + data.outriggerPrice.detail.children[i].children[j].value +'">' + data.outriggerPrice.detail.children[i].children[j].value +'</label>';
                    }
                    outriggerPriceStr += '</div>';
                }
                $('.outriggerPriceBox').html(outriggerPriceStr);

                //工具箱
                var toolboxStr = '';
                $('#toolboxName').html('<i class="mustRed isShow">*</i>'+ data.toolbox.name + '：');
                for(var i=0;i<data.toolbox.detail.children.length;i++){
                    toolboxStr += '<div class="length-item"><h3>'+ data.toolbox.detail.children[i].value +'</h3>';
                    toolboxStr += '<div class="length-right"><span class="left">支腿安装方式：</span>'
                    for(var j=0;j<data.toolbox.detail.children[i].children.length;j++) {
                        toolboxStr += '<label class="outriggerModeLabel" for="outriggerMode' + j + i +'"><input trailBrand="'+ data.toolbox.detail.children[i].value +'" type="radio" name="outriggerMode" class="f12 ml10" id="outriggerMode' + j + i +'" value="' + data.toolbox.detail.children[i].children[j].value +'">' + data.toolbox.detail.children[i].children[j].value +'</label>';
                    }
                    toolboxStr += '</div>';
                    toolboxStr += '<div class="length-right"><span class="left">支腿安装位置与个数：</span>'
                    for(var j=0;j<data.toolbox.detail.children[i].children.length;j++) {
                        toolboxStr += '<label for="toolbox' + j + i +'" style="display:none;"><input trailBrand="'+ data.toolbox.detail.children[i].value +'" type="radio" name="toolbox" class="f12 ml10" id="toolbox' + j + i +'" value="' + data.toolbox.detail.children[i].children[j].children[0].value +'">' + data.toolbox.detail.children[i].children[j].children[0].value +'</label>';
                    }
                    toolboxStr +='</div>';
                    toolboxStr +='</div>';
                }
                $('.toolboxBox').html(toolboxStr);

                //备胎架选配
                var spareTireCarrierStr = '';
                $('#spareTireCarrierName').html('<i class="mustRed isShow">*</i>'+ data.spareTireCarrier.name + '：');
                for(var i= 0;i<data.spareTireCarrier.detail.length;i++){
                    spareTireCarrierStr += '<label for="spareTireCarrier'+ i +'"><input type="radio" name="spareTireCarrier" id="spareTireCarrier'+ i +'" class="f12 ml10" value="'+ data.spareTireCarrier.detail[i] +'">'+ data.spareTireCarrier.detail[i] + '</label>';
                }
                $('.spareTireCarrierBox').html(spareTireCarrierStr);

                //ABS
                var ABSStr = '';
                $('#ABSName').html('<i class="mustRed isShow">*</i>'+ data.ABS.name + '：');
                for(var i=0;i<data.ABS.detail.children.length;i++){
                    ABSStr += '<div class="length-right"><span class="left">'+ data.ABS.detail.children[i].value +'：</span>';
                    for(var j=0;j<data.ABS.detail.children[i].children.length;j++) {
                        ABSStr += '<label for="ABS' + j + i +'"><input   trailBrand="'+ data.ABS.detail.children[i].value +'" type="radio" name="ABS" class="f12 ml10" id="ABS' + j + i +'" value="' + data.ABS.detail.children[i].children[j].value +'">' + data.ABS.detail.children[i].children[j].value +'</label>';
                    }
                    ABSStr += '</div>';
                }
                $('.ABSBox').html(ABSStr);

                //绳钩样式
                var ropeHookStyleStr = '';
                $('#ropeHookStyleName').html('<i class="mustRed isShow">*</i>'+ data.ropeHookStyle.name + '：');
                for(var i= 0;i<data.ropeHookStyle.detail.length;i++){
                    ropeHookStyleStr += '<label for="ropeHookStyle'+ i +'"><input type="radio" name="ropeHookStyle" id="ropeHookStyle'+ i +'" class="f12 ml10" value="'+ data.ropeHookStyle.detail[i] +'">'+ data.ropeHookStyle.detail[i] + '</label>';
                }
                ropeHookStyleStr += '<div class="defaultTit" style="display: inline-block;"><i class="defaultIcon mr-3"></i><span title="（可定制，默认50厘米间距，间距可调，需告知定制个数）"></span></div>'
                $('.ropeHookStyleBox').html(ropeHookStyleStr);

                //钢材
                var steels = data.steels.detail.children;
                var steelsStr = '';
                var steelsArr = [];
                for(var i=0;i<steels.length;i++) {
                    for(var j=0;j<steels[i].children.length;j++){
                        for(var a=0;a<$('.steels').length;a++){
                            steelsArr.push(steels[i].children[j].value)
                        }
                    }
                }
                for(var i=0;i<$("input[name='trailBrand']").length;i++){
                    steelsStr += $("input[name='trailBrand']").eq(i).val() + '：<span class="steels" style="margin-right:20px;" attr="'+ $("input[name='trailBrand']").eq(i).val() +'">'+ steelsArr[i] +'</span>';
                }
                $('#steelsBox').html(steelsStr);


                $('.loadingBox').hide();
                $('.releaseBox,#navLeft').show();
                cdOptfn.alertBrandMsg(data); //品牌信息介绍
                cdOptfn.initCustomizedInput();//加载定制input
                cdOptfn.linkageHeadMsg();//联动 车头信息 （品牌 型号 马力 鞍座高度）
                carClueCommonOptfn.handleDiffPage(); //不同的页面 不同入口判断
                cgOptfn.autoGenerateData(data); //自动根据选择生成数据  如有效货台面长度  大梁参数 等等信息
                carClueCommonOptfn.changeColorByChecked();//选择的字改变颜色

                carClueCommonOptfn.linkageSelectFun();//页面 联动关系方法
            }else{
                tytTool.toastShow(data.msg, 4000)
            }
        }
    },
    optFn: {
        initData:function () {
            cdArr.cdCommon.otherUrl = tyt.tool.carPathUrl('') + '/idc_vehicle/index.php/v1/vehicle/read';
            tytTool.ajaxFn(cdArr.cdCommon, cdAjaxfn.rendePage);
        },
        getAllTitleCon:function () { //左侧导航相关
            var str = '<ul class="nav_ul">'
            for(var i=0;i<$('.title-nav').length;i++){
                var subTitle = $('.title-nav').eq(i).find('a').text().replace(/\*|：/g,'');
                str += '<li class="item-li" name="'+ $('.title-nav').eq(i).find('a').attr('name') +'"><i class="nav-a" href="#'+ $('.title-nav').eq(i).find('a').attr('name') +'"><em class="right_active left_active" style="display: block;"></em>'+ subTitle +'</i></li>';
            }
            str += '</ul>';
            $('#navLeft').html(str);
            cdOptfn.tipMessageFun();
        },
        leftNavOffsetTop:function(){
            // $('.right-content').on('scroll',function(){
            //     for(var i=0;i<$('.title-nav').length;i++){
            //         if($('.title-nav').eq(i).find('a').offset().top <= 120){
            //             for(var j=0;j<$('.item-li').length;j++){
            //                 if($('.item-li').eq(j).text() == $('.title-nav').eq(i).find('a').text().replace(/\*|：/g,'')){
            //                     $('.item-li').find('i').removeClass('select-item-a');
            //                     $('.item-li').eq(j).find('i').addClass('select-item-a');
            //                 }
            //             }
            //         }
            //     }
            // });
            $('input[type="radio"]').parent('label').click(function(){
                for(var j=0;j<$('.item-li').length;j++){
                    if($('.item-li').eq(j).text() == $(this).parents('.border-black').siblings('label').text().replace(/\*|：/g,'')){
                        $('.item-li').find('i').removeClass('select-item-a');
                        $('.item-li').eq(j).find('i').addClass('select-item-a');
                        if(j >= 18){
                            $('#navLeft').scrollTop(600);
                        }else{
                            $('#navLeft').scrollTop(0);
                        }
                    }
                }
            })


            $('.nav-a').click(function () {
                for(var i=0;i<$('.title-nav').length;i++){
                    if($(this).text() == $('.title-nav').eq(i).find('a').text().replace(/\*|：/g,'')){
                        var top = ($('.title-nav').eq(i).find('a').position().top + 73) - $(this).offset().top;
                        $('.right-content').scrollTop(top);
                    }
                }
            })

            if(window.navigator.userAgent.indexOf("Chrome") !== -1){ //兼容chrome 和 friefox的不一样问题
                $('.right-box').css('padding-bottom','0px');
            }
        },
        errorJumpNav:function(title){
            for(var i=0;i<$('.item-li').length;i++){
                if($('.item-li').eq(i).text() == title){
                    if(i >= 18){
                        $('#navLeft').scrollTop(600);
                    }else{
                        $('#navLeft').scrollTop(0);
                    }
                    $('.item-li').eq(i).find('.nav-a').click();
                }
            }
        },
        changeNavSelected:function (md) { //左侧导航
            $('.item-li').find('i').removeClass('select-item-a');
            $('.item-li').eq(0).find('i').addClass('select-item-a');
            $('.nav-a').click(function () {
                $('.item-li').find('i').removeClass('select-item-a');
                $(this).addClass('select-item-a');
            })
        },
        tipMessageFun:function () {//提示信息  移入移出变化
            for(var i=0;i<$('.defaultIcon').length;i++){
                $('.defaultIcon').eq(i).on('mouseover',function () {
                    $(this).next().html($(this).next().attr('title'))
                })
                $('.defaultIcon').eq(i).on('mouseout',function () {
                    $(this).next().html('')
                })
            }
        },
        initCustomizedInput:function (arr) { //定制相关的input参数
            var arr = ['ladderWidth','vehicleEjingWidth'];
            for(var e=0;e<arr.length;e++){
                for (var i = 0; i < $("input[name='"+ arr[e] +"']").length; i++) {
                    if ($("input[name='"+ arr[e] +"']").eq(i).val().indexOf('定制') != -1 || $("input[name='"+ arr[e] +"']").eq(i).val().indexOf('-') != -1 ) {
                        var regRule = '';
                        var unit = ''
                        if ($("input[name='"+ arr[e] +"']").eq(i).val().indexOf('米') != -1) {
                            unit = '米';
                            regRule = 'onblur="tytTool.onInput($(this),20,2)"';
                        } else if ($("input[name='"+ arr[e] +"']").eq(i).val().indexOf('cm') != -1) {
                            unit = 'cm';
                            regRule = 'onblur="tytTool.onInput($(this),20)"';
                        } else if ($("input[name='"+ arr[e] +"']").eq(i).val().indexOf('吨') != -1) {
                            unit = '吨';
                            regRule = 'onblur="tytTool.onInput($(this),20)"';
                        }
                        var className = $("input[name='"+ arr[e] +"']").eq(i).parents('label').attr('attr') ? $("input[name='"+ arr[e] +"']").eq(i).parents('label').attr('attr').slice(0, -5) + 'Customized' : '';
                        $("input[name='"+ arr[e] +"']").eq(i).parents('label').after('<p class="input loading-custom-input customizedInputbox"><input ' + regRule + ' class="' + className + '" type="text" value=""><span>' + unit + '</span></p>');
                        $("input[name='"+ arr[e] +"']").eq(i).parents('label').next('.loading-custom-input').hide();
                    }
                }

                $("input[name='"+ arr[e] +"']").click(function () {
                    var mark = $(this).parents('label').attr('attr');
                    for(var i=0;i<$(this).length;i++){
                        if(mark == $(this).eq(i).parents('label').attr('attr')){
                            $(this).eq(i).parents('label').next('.loading-custom-input').find('input').val('')
                            $('.loading-custom-input').hide();
                        }
                    }
                    $(this).parents('label').next('.loading-custom-input').show();
                })

            }

            //校验所有自定义范围 input框 范围不对清空
            $(".loading-custom-input").find('input').on('blur',function () {
                var sectionA = $(this).parents('.loading-custom-input').prev('label').text().split('-')[0].match(/\d+/g);
                var sectionB = $(this).parents('.loading-custom-input').prev('label').text().split('-')[1].match(/\d+/g);
                sectionA = Number(sectionA.length > 1?sectionA[0] + '.' + sectionA[1]:sectionA[0]);
                sectionB = Number(sectionB.length > 1?sectionB[0] + '.' + sectionB[1]:sectionB[0]);
                cdOptfn.sectionIsTrue($(this),sectionA,sectionB);
            })
            //校验货台面高度 范围不对清空
            $("#htgdCustomized").on('blur',function () {
                if($('#htgd').text().indexOf('-') != -1){
                    var sectionA = $('#htgd').text().split('-')[0].match(/\d+/g);
                    var sectionB = $('#htgd').text().split('-')[1].match(/\d+/g);
                    sectionA = Number(sectionA.length > 1?sectionA[0] + '.' + sectionA[1]:sectionA[0]);
                    sectionB = Number(sectionB.length > 1?sectionB[0] + '.' + sectionB[1]:sectionB[0]);
                    cdOptfn.sectionIsTrue($(this),sectionA,sectionB);
                }

            })
        },
        sectionIsTrue:function(self,sectionA,sectionB){ // 清空并且提示有误
            if(self.val() != ''){
                if(self.val() < sectionA || self.val() > sectionB ){
                    tytTool.toastShow(self.parents('.border-black').prev('label').text().replace(/\*|：/g,'') + '自定义范围有误', 4000);
                    self.val('');
                }
            }
        },
        initHeadModelOption:function (headBrandVal) {
            var headModelStr = '<option class="firstOption" value="">请选择车头型号</option>'
            for(var i=0;i<cdArr.headBrandData.length;i++){
                if(headBrandVal == cdArr.headBrandData[i].value){
                    for(var j=0;j<cdArr.headBrandData[i].children.length;j++) {
                        headModelStr += '<option class="firstOption" value="' + cdArr.headBrandData[i].children[j].value + '">' + cdArr.headBrandData[i].children[j].value + '</option>'
                    }
                }
            }
            $('#headModel').html(headModelStr);
        },
        initHeadPowerOption:function(headBrandVal,headModelVal){
            var headPowerStr = '<option class="firstOption" value="">请选择车头马力</option>'
            for(var i=0;i<cdArr.headBrandData.length;i++){
                if(headBrandVal == cdArr.headBrandData[i].value){
                    for(var j=0;j<cdArr.headBrandData[i].children.length;j++) {
                        if(headModelVal == cdArr.headBrandData[i].children[j].value) {
                            for(var e=0;e<cdArr.headBrandData[i].children[j].children.length;e++){
                                headPowerStr += '<option class="firstOption" value="' + cdArr.headBrandData[i].children[j].children[e].value + '">' + cdArr.headBrandData[i].children[j].children[e].value + '</option>'
                            }
                        }
                    }
                }
            }
            $('#headPower').html(headPowerStr);
        },
        initSaddleHeight:function(headBrandVal,headModelVal,headPowerVal){
            for(var i=0;i<cdArr.headBrandData.length;i++){
                if(headBrandVal == cdArr.headBrandData[i].value){
                    for(var j=0;j<cdArr.headBrandData[i].children.length;j++) {
                        if(headModelVal == cdArr.headBrandData[i].children[j].value) {
                            for(var e=0;e<cdArr.headBrandData[i].children[j].children.length;e++){
                                if(headPowerVal == cdArr.headBrandData[i].children[j].children[e].value) {
                                    $('#saddleHeight').html(cdArr.headBrandData[i].children[j].children[e].children[0].value)
                                }
                            }
                        }
                    }
                }
            }
        },
        linkageHeadMsg:function(){ //加载联动 的车头品牌  车头型号  车头马力 和鞍座高度
            $('#headBrand').change(function () {
                cdOptfn.initHeadModelOption($(this).val());
            })
            $('#headModel').change(function () {
                cdOptfn.initHeadPowerOption($('#headBrand').val(),$(this).val());
            })
            $('#headPower').change(function () {
                cdOptfn.initSaddleHeight($('#headBrand').val(),$('#headModel').val(),$(this).val());
            })
        },
        alertBrandMsg:function(data){
            var data = data.brandIntroduce.detail.children;
            $('.wh-icon').click(function(){
                var pp = $(this).parents('.defaultTit').prev('label').text();
                for(var i=0;i<data.length;i++){
                    if(data[i].value == pp){
                        tytTool.showToast('品牌简介', data[i].children[0].value,$('.carData'))
                        $('.carData').siblings('.toastBox').addClass('toastBox-t');
                    }
                }
            })
        }
    }
}

var cdArr = carDataInitEnt.arr;
var cdAjaxfn = carDataInitEnt.ajaxfn;
var cdOptfn = carDataInitEnt.optFn;

cdOptfn.initData();
