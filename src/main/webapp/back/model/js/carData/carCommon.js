/*
    carCommon.js for carCommon.hmtl
    author: hey<PERSON>an
*/
var common = {
    myData: {},
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
    },
    ajx: {
        listData: function(data, _currentPage) {
            if (data.code == 200) {
                // console.log(data)
                var data_list = data.data;
                var count = data.count;
                $("#carCommon tr:not('.tableTh')").remove();
                var currentPage = Number(_currentPage),
                    maxPage = Math.ceil(Number(count) / pageSize),
                    rowCount = Number(count);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: maxPage || 1
                }
                if (data_list && maxPage > 0) {
                    $("#totalPages").html(maxPage);
                    $("#currentPage").html(currentPage);
                    $("#rowCount").html(rowCount);
                    tytTool.jqPaginatorExtend(arrParams, 'co.listOpt')
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    tytTool.jqPaginatorExtend(arrParams, 'co.listOpt')
                }
                //正式载入数据

                if (sellRole) {
                    $("#addTypeBtn").remove();
                    $("#checkedBtnId").after(`<li id="addTypeBtn"><input type="button" class="button" value="新增常见车型" onclick="vo.clue('addCommon','')"></li>`)
                }
                var _html = '',
                    _edit = '',
                    recommendation = '',
                    loadType = '';
                if (data_list && data_list.length > 0) {
                    $.each(data_list, function(index, el) {
                        if (sellRole) {
                            _edit = `<a class="info_detail ver_block" onclick="co.deteleOpt(${el.commonId})">删除</a><span class="ver_block ver_span">|</span>
							<a class="info_detail ver_block" onclick="vo.clue('change','${el.commonId}')">修改</a><span class="ver_block ver_span">|</span>`
                        }
                        recommendation = tytTool.NullObj(el.recommendation).length >= 10 ? el.recommendation.substring(0, 10) + '...' : el.recommendation;
                        loadType = tytTool.NullObj(el.loadType).length >= 10 ? el.loadType.substring(0, 10) + '...' : el.loadType;
                        _html += `<tr>
							<td>${tytTool.NullObj(el.commonId)}</td>
							<td><input class="carDataCheck" type="checkbox" value="${tytTool.NullObj(el.commonId)}"></td>
							<td>${tytTool.NullObj(el.vehicleClass)}</td>
							<td>${tytTool.NullObj(el.trailBrand)}</td>
							<td>${tytTool.NullObj(el.trailModel)}</td>
							<td>${tytTool.NullObj(el.trailLength)}</td>
							<td>${tytTool.NullObj(el.trailStyle)}</td>
							<td>${tytTool.NullObj(el.tireStyle)}</td>
							<td>${tytTool.NullObj(el.axleNorms)}</td>
							<td>${tytTool.NullObj(el.load)}</td>
							<td>${tytTool.NullObj(el.ctime)}</td>
							<td>${recommendation}</td>
							<td>${loadType}</td>
							<<td>
								${_edit}<a class="info_detail ver_block" onclick="vo.jumpCarType('hot','${tytTool.NullObj(el.commonId)}')">详情</a>
							</td>
						</tr>`
                    });
                    $("#carCommon").append(_html);
                    tytTool.bindHidePhone()
                } else {
                    _html = '<tr><td colspan="' + $(".tableTh th").length + '">暂无查询数据</td></tr>'
                    $("#carCommon").append(_html);
                }

            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
        deteleData: function(data) {
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 2000);
                setTimeout(() => {
                    tytTool.reloadWin()
                }, 2000);
            } else {
                if (tytTool.NullObj(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        }
    },
    opt: {
        loadList: function() {
            var pageArr = {
                "totalPages": 1,
                "pageSize": pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": co.listOpt
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function(currentPage) {
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            ca.common.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            var page = {
                "currentPage": currentPage,
                "pageSize": pageSize
            }
            ca.common.data = $.extend({}, arr, page)
            ca.common.otherUrl = idc_vehicle + '/index.php/v1/CommonVehicle/read';
            tytTool.ajaxFn(ca.common, cj.listData)
        },
        deteleOpt: function(_id) {
            // ca.common.otherData = currentPage;
            ca.common.data = {
                "commonId": _id
            };
            ca.common.otherUrl = idc_vehicle + '/index.php/v1/CommonVehicle/delete';
            tytTool.ajaxFn(ca.common, cj.deteleData)
        }
    }
}

var cm = common.myData;
var ca = common.arr;
var cj = common.ajx;
var co = common.opt;

//控制权限和列表加载的顺序
$.when(userRole).done(function() {
    co.loadList()
}).fail(function() {
    co.loadList()
})