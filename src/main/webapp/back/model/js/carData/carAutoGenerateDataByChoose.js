// auther: zy
var pageSize = 30;
var carGenerateEnt = {
    arr: {
        cgCommon: {
            url: '',
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded; charset=utf-8',
            data: '',
            otherUrl:''
        }
    },
    ajaxfn: {
    },
    optFn: {
        autoGenerateData:function (data) {
            var htgd = data.htgd;//货台面高度
            var yxhtcd = data.yxhtcd;//有效货台面长度
            var sybcs = data.sybcs;//上翼板
            var xybcs = data.xybcs;//下翼板
            var lbcs = data.lbcs;//立板
            var blcs = data.blcs;//边梁
            var dlcs = data.dlcs;//大梁
            var vehicleWeight = data.vehicleWeight;//自重
            var saleGuidePrice = data.saleGuidePrice;//销售指导价

            $($("input[name='trailBrand']").parents('label')).click(function () {
                cgOptfn.computeSybcs(sybcs);
                cgOptfn.computeXybcs(xybcs);
                cgOptfn.computeLbcs(lbcs);
                cgOptfn.computeBlcs(blcs);
                cgOptfn.computeDlcs(dlcs);
                cgOptfn.computeVehicleWeight(vehicleWeight);
                cgOptfn.computeSaleGuidePrice(saleGuidePrice);
            });
            $($("input[name='trailModel']").parents('label')).click(function () {
                cgOptfn.computeHtgd(htgd);
                cgOptfn.computeYxhtcd(yxhtcd);
                cgOptfn.computeSybcs(sybcs);
                cgOptfn.computeXybcs(xybcs);
                cgOptfn.computeLbcs(lbcs);
                cgOptfn.computeBlcs(blcs);
                cgOptfn.computeDlcs(dlcs);
                cgOptfn.computeVehicleWeight(vehicleWeight);
                cgOptfn.computeSaleGuidePrice(saleGuidePrice);

            });
            $($("input[name='trailLength']").parents('label')).click(function () {
                cgOptfn.computeYxhtcd(yxhtcd);
                cgOptfn.computeSybcs(sybcs);
                cgOptfn.computeXybcs(xybcs);
                cgOptfn.computeLbcs(lbcs);
                cgOptfn.computeBlcs(blcs);
                cgOptfn.computeDlcs(dlcs);
                cgOptfn.computeVehicleWeight(vehicleWeight);
            });
            $($("input[name='trailStyle']").parents('label')).click(function () {
                cgOptfn.computeHtgd(htgd);
                cgOptfn.computeYxhtcd(yxhtcd);
            });
            $($("input[name='tireStyle']").parents('label')).click(function () {
                cgOptfn.computeHtgd(htgd);
                cgOptfn.computeYxhtcd(yxhtcd);
            });
            $($("input[name='axleNorms']").parents('label')).click(function () {
                cgOptfn.computeHtgd(htgd);
                cgOptfn.computeSybcs(sybcs);
                cgOptfn.computeXybcs(xybcs);
                cgOptfn.computeLbcs(lbcs);
                cgOptfn.computeBlcs(blcs);
                cgOptfn.computeDlcs(dlcs);
                cgOptfn.computeVehicleWeight(vehicleWeight);
            });
            $($("input[name='load']").parents('label')).click(function () {
                cgOptfn.computeSybcs(sybcs);
                cgOptfn.computeXybcs(xybcs);
                cgOptfn.computeLbcs(lbcs);
                cgOptfn.computeBlcs(blcs);
                cgOptfn.computeDlcs(dlcs);
                cgOptfn.computeVehicleWeight(vehicleWeight);
            });
        },
        //货台面高度
        computeHtgd:function (htgd){
            setTimeout(function(){
                htgd = htgd.detail.children;
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailStyle = $("input[name='trailStyle']:checked").val();
                var tireStyle = $("input[name='tireStyle']:checked").val();
                var axleNorms = $("input[name='axleNorms']:checked").val();

                $('#htgd').text('');
                console.log(trailModel , trailStyle , tireStyle , axleNorms)
                if(trailModel && trailStyle && tireStyle && axleNorms) {
                    for (var i = 0; i < htgd.length; i++) {
                        if (trailModel && trailModel.slice(0, 3) == htgd[i].value) {
                            for (var j = 0; j < htgd[i].children.length; j++) {
                                if (trailStyle && trailStyle == htgd[i].children[j].value) {
                                    for(var e=0;e<htgd[i].children[j].children.length;e++){
                                        if(tireStyle && tireStyle == htgd[i].children[j].children[e].value){
                                            for(var a=0;a<htgd[i].children[j].children[e].children.length;a++){
                                                if(axleNorms && axleNorms == htgd[i].children[j].children[e].children[a].value){
                                                    $('#htgd').text(htgd[i].children[j].children[e].children[a].children[0].value);
                                                    console.log(htgd[i].children[j].children[e].children[a].children[0].value)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //计算有效货台面长度
        computeYxhtcd:function (yxhtcd){
            setTimeout(function(){
                yxhtcd = yxhtcd.detail.children;
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailLength = $("input[name='trailLength']:checked").val();
                var trailStyle = $("input[name='trailStyle']:checked").val();
                var tireStyle = $("input[name='tireStyle']:checked").val();

                $('#yxhtcd').text('');
                if(trailModel && trailLength && trailStyle && tireStyle ) {
                    for (var i = 0; i < yxhtcd.length; i++) {
                        if (trailModel && trailModel.slice(0, 3) == yxhtcd[i].value) {
                            for (var j = 0; j < yxhtcd[i].children.length; j++) {
                                if (trailModel && trailModel.slice(3) == yxhtcd[i].children[j].value) {
                                    for(var e=0;e<yxhtcd[i].children[j].children.length;e++){
                                        if(trailLength && trailLength == yxhtcd[i].children[j].children[e].value){
                                            for(var a=0;a<yxhtcd[i].children[j].children[e].children.length;a++){
                                                if(trailStyle && trailStyle == yxhtcd[i].children[j].children[e].children[a].value){
                                                    for(var b=0;b<yxhtcd[i].children[j].children[e].children[a].children.length;b++){
                                                        if(tireStyle && tireStyle == yxhtcd[i].children[j].children[e].children[a].children[b].value){
                                                            $('#yxhtcd').text(yxhtcd[i].children[j].children[e].children[a].children[b].children[0].value);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //上翼板参数
        computeSybcs:function(sybcs){
            setTimeout(function(){
                sybcs = sybcs.detail.children;
                var trailBrand = $("input[name='trailBrand']:checked").val();
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailLength = $("input[name='trailLength']:checked").val();
                var axleNorms = $("input[name='axleNorms']:checked").val();
                var load = $("input[name='load']:checked").val();
                $('#sybcs').text('');
                // console.log(trailBrand , trailModel , trailLength , axleNorms , load)

                if(trailBrand && trailModel && trailLength && axleNorms && load ) {
                    for (var z = 0; z < sybcs.length; z++) {
                        if(sybcs[z].value == trailBrand){
                            for (var i = 0; i < sybcs[z].children.length; i++) {
                                if (trailModel && trailModel.slice(0, 3) == sybcs[z].children[i].value) {
                                    for (var j = 0; j < sybcs[z].children[i].children.length; j++) {
                                        if (trailModel && trailModel.slice(3) == sybcs[z].children[i].children[j].value) {
                                            for (var e = 0; e < sybcs[z].children[i].children[j].children.length; e++) {
                                                if (trailLength && trailLength == sybcs[z].children[i].children[j].children[e].value) {
                                                    for (var a = 0; a < sybcs[z].children[i].children[j].children[e].children.length; a++) {
                                                        if (axleNorms && axleNorms == sybcs[z].children[i].children[j].children[e].children[a].value) {
                                                            for (var b = 0; b < sybcs[z].children[i].children[j].children[e].children[a].children.length; b++) {
                                                                if (load && load == sybcs[z].children[i].children[j].children[e].children[a].children[b].value) {
                                                                    $('#sybcs').text(sybcs[z].children[i].children[j].children[e].children[a].children[b].children[0].value);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //下翼板参数
        computeXybcs:function(xybcs){
            setTimeout(function(){
                xybcs = xybcs.detail.children;
                var trailBrand = $("input[name='trailBrand']:checked").val();
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailLength = $("input[name='trailLength']:checked").val();
                var axleNorms = $("input[name='axleNorms']:checked").val();
                var load = $("input[name='load']:checked").val();
                $('#xybcs').text('');
                if(trailBrand && trailModel && trailLength && axleNorms && load ) {
                    for (var z = 0; z < xybcs.length; z++) {
                        if(xybcs[z].value == trailBrand){
                            for (var i = 0; i < xybcs[z].children.length; i++) {
                                if (trailModel && trailModel.slice(0, 3) == xybcs[z].children[i].value) {
                                    for (var j = 0; j < xybcs[z].children[i].children.length; j++) {
                                        if (trailModel && trailModel.slice(3) == xybcs[z].children[i].children[j].value) {
                                            for (var e = 0; e < xybcs[z].children[i].children[j].children.length; e++) {
                                                if (trailLength && trailLength == xybcs[z].children[i].children[j].children[e].value) {
                                                    for (var a = 0; a < xybcs[z].children[i].children[j].children[e].children.length; a++) {
                                                        if (axleNorms && axleNorms == xybcs[z].children[i].children[j].children[e].children[a].value) {
                                                            for (var b = 0; b < xybcs[z].children[i].children[j].children[e].children[a].children.length; b++) {
                                                                if (load && load == xybcs[z].children[i].children[j].children[e].children[a].children[b].value) {
                                                                    $('#xybcs').text(xybcs[z].children[i].children[j].children[e].children[a].children[b].children[0].value);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //立板参数
        computeLbcs:function(lbcs){
            setTimeout(function(){
                lbcs = lbcs.detail.children;
                var trailBrand = $("input[name='trailBrand']:checked").val();
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailLength = $("input[name='trailLength']:checked").val();
                var axleNorms = $("input[name='axleNorms']:checked").val();
                var load = $("input[name='load']:checked").val();

                $('#lbcs').text('');
                if(trailBrand && trailModel && trailLength && axleNorms && load ) {
                    for (var z = 0; z < lbcs.length; z++) {
                        if(lbcs[z].value == trailBrand){
                            for (var i = 0; i < lbcs[z].children.length; i++) {
                                if (trailModel && trailModel.slice(0, 3) == lbcs[z].children[i].value) {
                                    for (var j = 0; j < lbcs[z].children[i].children.length; j++) {
                                        if (trailModel && trailModel.slice(3) == lbcs[z].children[i].children[j].value) {
                                            for (var e = 0; e < lbcs[z].children[i].children[j].children.length; e++) {
                                                if (trailLength && trailLength == lbcs[z].children[i].children[j].children[e].value) {
                                                    for (var a = 0; a < lbcs[z].children[i].children[j].children[e].children.length; a++) {
                                                        if (axleNorms && axleNorms == lbcs[z].children[i].children[j].children[e].children[a].value) {
                                                            for (var b = 0; b < lbcs[z].children[i].children[j].children[e].children[a].children.length; b++) {
                                                                if (load && load == lbcs[z].children[i].children[j].children[e].children[a].children[b].value) {
                                                                    $('#lbcs').text(lbcs[z].children[i].children[j].children[e].children[a].children[b].children[0].value);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //边梁参数
        computeBlcs:function(blcs){
            setTimeout(function(){
                blcs = blcs.detail.children;
                var trailBrand = $("input[name='trailBrand']:checked").val();
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailLength = $("input[name='trailLength']:checked").val();
                var axleNorms = $("input[name='axleNorms']:checked").val();
                var load = $("input[name='load']:checked").val();
                $('#blcs').text('');
                if(trailBrand && trailModel && trailLength && axleNorms && load ) {
                    for (var z = 0; z < blcs.length; z++) {
                        if(blcs[z].value == trailBrand){
                            for (var i = 0; i < blcs[z].children.length; i++) {
                                if (trailModel && trailModel.slice(0, 3) == blcs[z].children[i].value) {
                                    for (var j = 0; j < blcs[z].children[i].children.length; j++) {
                                        if (trailModel && trailModel.slice(3) == blcs[z].children[i].children[j].value) {
                                            for (var e = 0; e < blcs[z].children[i].children[j].children.length; e++) {
                                                if (trailLength && trailLength == blcs[z].children[i].children[j].children[e].value) {
                                                    for (var a = 0; a < blcs[z].children[i].children[j].children[e].children.length; a++) {
                                                        if (axleNorms && axleNorms == blcs[z].children[i].children[j].children[e].children[a].value) {
                                                            for (var b = 0; b < blcs[z].children[i].children[j].children[e].children[a].children.length; b++) {
                                                                if (load && load == blcs[z].children[i].children[j].children[e].children[a].children[b].value) {
                                                                    $('#blcs').text(blcs[z].children[i].children[j].children[e].children[a].children[b].children[0].value);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //大梁参数
        computeDlcs:function(dlcs){
            setTimeout(function(){
                dlcs = dlcs.detail.children;
                var trailBrand = $("input[name='trailBrand']:checked").val();
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailLength = $("input[name='trailLength']:checked").val();
                var axleNorms = $("input[name='axleNorms']:checked").val();
                var load = $("input[name='load']:checked").val();

                $('#dlcs').text('');
                if(trailBrand && trailModel && trailLength && axleNorms && load ) {
                    for (var z = 0; z < dlcs.length; z++) {
                        if(dlcs[z].value == trailBrand){
                            for (var i = 0; i < dlcs[z].children.length; i++) {
                                if (trailModel && trailModel.slice(0, 3) == dlcs[z].children[i].value) {
                                    for (var j = 0; j < dlcs[z].children[i].children.length; j++) {
                                        if (trailModel && trailModel.slice(3) == dlcs[z].children[i].children[j].value) {
                                            for (var e = 0; e < dlcs[z].children[i].children[j].children.length; e++) {
                                                if (trailLength && trailLength == dlcs[z].children[i].children[j].children[e].value) {
                                                    for (var a = 0; a < dlcs[z].children[i].children[j].children[e].children.length; a++) {
                                                        if (axleNorms && axleNorms == dlcs[z].children[i].children[j].children[e].children[a].value) {
                                                            for (var b = 0; b < dlcs[z].children[i].children[j].children[e].children[a].children.length; b++) {
                                                                if (load && load == dlcs[z].children[i].children[j].children[e].children[a].children[b].value) {
                                                                    $('#dlcs').text(dlcs[z].children[i].children[j].children[e].children[a].children[b].children[0].value);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //车辆自重
        computeVehicleWeight:function(vehicleWeight){
            setTimeout(function(){
                vehicleWeight = vehicleWeight.detail.children;
                var trailBrand = $("input[name='trailBrand']:checked").val();
                var trailModel = $("input[name='trailModel']:checked").val();
                var trailLength = $("input[name='trailLength']:checked").val();
                var axleNorms = $("input[name='axleNorms']:checked").val();
                var load = $("input[name='load']:checked").val();
                $('#vehicleWeight').text('');
                if(trailBrand && trailModel && trailLength && axleNorms && load ) {
                    for (var z = 0; z < vehicleWeight.length; z++) {
                        if(trailBrand && trailBrand == vehicleWeight[z].value){
                            for (var i = 0; i < vehicleWeight[z].children.length; i++) {
                                if (trailModel && trailModel.slice(0, 3) == vehicleWeight[z].children[i].value) {
                                    for (var j = 0; j < vehicleWeight[z].children[i].children.length; j++) {
                                        if (trailModel && trailModel.slice(3) == vehicleWeight[z].children[i].children[j].value) {
                                            for (var e = 0; e < vehicleWeight[z].children[i].children[j].children.length; e++) {
                                                if (trailLength && trailLength == vehicleWeight[z].children[i].children[j].children[e].value) {
                                                    for (var a = 0; a < vehicleWeight[z].children[i].children[j].children[e].children.length; a++) {
                                                        if (axleNorms && axleNorms == vehicleWeight[z].children[i].children[j].children[e].children[a].value) {
                                                            for (var b = 0; b < vehicleWeight[z].children[i].children[j].children[e].children[a].children.length; b++) {
                                                                if (load && load == vehicleWeight[z].children[i].children[j].children[e].children[a].children[b].value) {
                                                                    $('#vehicleWeight').text(vehicleWeight[z].children[i].children[j].children[e].children[a].children[b].children[0].value);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        },
        //销售指导价
        computeSaleGuidePrice:function(saleGuidePrice){
            setTimeout(function(){
                saleGuidePrice = saleGuidePrice.detail.children;
                var trailBrand = $("input[name='trailBrand']:checked").val();
                var trailModel = $("input[name='trailModel']:checked").val();
                $('#saleGuidePrice').text('');
                if(trailBrand && trailModel) {
                    for (var z = 0; z < saleGuidePrice.length; z++) {
                        if(trailBrand && trailBrand == saleGuidePrice[z].value){
                            for (var i = 0; i < saleGuidePrice[z].children.length; i++) {
                                if (trailModel && trailModel.slice(0, 3) == saleGuidePrice[z].children[i].value) {
                                    for (var j = 0; j < saleGuidePrice[z].children[i].children.length; j++) {
                                        if (trailModel && trailModel.slice(3) == saleGuidePrice[z].children[i].children[j].value) {
                                            $('#saleGuidePrice').text(saleGuidePrice[z].children[i].children[j].children[0].value);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },100)
        }
    }
}

var cgArr = carGenerateEnt.arr;
var cgAjaxfn = carGenerateEnt.ajaxfn;
var cgOptfn = carGenerateEnt.optFn;

