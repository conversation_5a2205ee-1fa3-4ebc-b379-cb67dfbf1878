/*
    carParamsSort.js for carParamsSort.hmtl
    author: zy
*/
var cps = {
    myData: {},
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'get',
            otherData: '',
            data: '',
        },
        sortCommon: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
            contentType: 'application/x-www-form-urlencoded'
        },
        commonParams: {
            userId: NO(tytTool.parseSearch().userId),
            client: 1,
        },
    },
    ajx: {
        renderDataFun: function (data, type) { //加载类
            if (data.code == 200) {
                var str = '';
                var res = data.data || [];
                for (var k = 0; k < res.length; k++) {
                    str += '<p class="big-title">' + res[k].name + '</p>'
                    if(res[k].child && res[k].child.length > 0) {
                        for (var i = 0; i < res[k].child.length; i++) {
                            str += '<li class="cf"><label class="fl" for="">' + res[k].child[i].name + '：</label><ul id="' + 'Dom' + k + i + '" class="listBox fl ulList">';
                            if(res[k].child[i].child && res[k].child[i].child.length > 0) {
                                for (var j = 0; j < res[k].child[i].child.length; j++) {
                                    var deleteIcon = '<i class="liClose" onclick="cpsO.removeArgFun(event,\'' + res[k].child[i].child[j].id + '\',\'' + res[k].child[i].child[j].name + '\',\'removeWarning\')">✕</i>'
                                    var id = res[k].child[i].child[j].id,
                                        en = res[k].child[i].child[j].name_en;
                                    //长度、车型、承载、品牌、平台样式  不能删除
                                    if(en === 'vehicle_length' || en === 'vehicle_model' || en === 'vehicle_load' || en === 'brand_id' || en === 'platform_style') {
                                        deleteIcon = ''
                                    }
                                    str += '<li id="' + id + '" data-en="'+ en +'" onclick="cpsO.addChildFun(this)">' + res[k].child[i].child[j].name + deleteIcon +'</li>'
                                }
                            }
                            str += '</ul><div class="caoz-btn">\
                                    <a class="info_opt info_add" onclick="cpsO.addArgFun('+ res[k].child[i].id + ',\'' + res[k].child[i].name + '\',\'' + res[k].level + '\')">添加</a>\
                                    <a class="info_opt info_remove" onclick="cpsO.removeFun('+ 'Dom' + k + i + ')">删除</a>\
                                </div></li>';
                        }
                    }
                }
                $('.checkBox').html(str);
                for (var k = 0; k < res.length; k++) {
                    for (var i = 0; i < res[k].child.length; i++) {
                        Sortable.create(document.getElementById('Dom' + k + i), { group: { pull: true, put: true } });
                    }
                }

                $('.addItem').val('新增' + $('.listBox li').eq(0).addClass('active').text().replace(/✕/, ''));
                cpsO.getTable($('.listBox li').eq(0).text().replace(/✕/, ''), $('.listBox li').eq(0).attr('id'));
                //存储默认配置的id
                cpsM.currentId = $('.active').attr('id')
                cpsM.currentEn = $('.active').attr('data-en')
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        getTableData: function (data, other) { //加载类对应的表格
            if (data.code == 200) {
                var res = data.data;
                console.log(cpsM.currentEn);
                //cpsM.currentEn === 'brand_id'  [所选表格为品牌时候,不参加默认设置功能]
                var str = `<tr class="tableTh"><th>${other.name}</th><th>操作</th>${cpsM.currentEn === 'brand_id' ? '' : '<th>是否设置为默认</th>'}</tr>`;
                if (res.length > 0) {
                    var is_default = '',is_default_name = '';
                    $.each(res, function (index, ele) {
                        is_default = ele.is_default == 1 ? '<i class="moren-icon">默认</i>' : ''
                        is_default_name = ele.is_default == 1 ? '默认' : '设为默认'
                        //是否设置为默认的控制
                        var opt_dom = `<td><a class="info_opt tb_btn" onclick="cpsO.changeCheckbox('${ele.id}')"> <input type="checkbox" name="" ${ele.is_default == 1 ? 'checked="checked"' : ''}>  ${is_default_name}</a></td>`;
                        if(cpsM.currentEn === 'brand_id') { //品牌不参加默认设置功能
                            opt_dom = '';
                        }
                        str += `<tr index="0">
                            <td>${ele.value}${is_default}</td>
                            <td><a class="info_opt tb_btn" onclick="cpsO.removeArgFun(event,'${ele.id}','${ele.value}','removeTableParameters')">删除</a></td>
                            ${opt_dom}
                        </tr>`
                    })
                } else {
                    str += '<tr><td colspan="3">暂无查询数据</td></tr>'
                }
                $('.tbodyContent').html(str);
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        addArgFunData: function (data, other) {
            if (data.code == 200) {
                var str = '';
                str += '<label id="newArgTitle">' + other.name + '：</label><select id="newArgId"><option class="firstOption" value="">请选择</option>'
                for (var i = 0; i < data.data.length; i++) {
                    str += '<option class="firstOption" value="' + data.data[i].id + '">' + data.data[i].name + '</option>'
                }
                str += '</select>'
                $('.inputBox').html(str)
                $('.addbox,.outBgColor').show();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        configDelData: function (data) {
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 2000);
                $('.addbox,.outBgColor').hide().attr('id', '');
                cpsO.getData();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        configAddData: function (data) {
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 2000);
                cpsO.getTable($('.addItem').val().replace(/新增/, ''), cpsM.currentId);
                $('.addbox,.outBgColor').hide().attr('id', '');
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        configDeleteData: function (data) {
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 2000);
                cpsO.getTable($('.addItem').val().replace(/新增/, ''), cpsM.currentId);
                $('.addbox,.outBgColor').hide().attr('id', '');
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        addConfigAddData: function (data) {
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 2000);
                $('.addbox,.outBgColor').hide().attr('id', '');
                cpsO.getData();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        allSortData: function (data) {
            if (data.code == 200) {
                tytTool.toastShow(data.msg, 2000);
                cpsO.getData();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        },
        changeCheckData: function (data) {
            if (data.code == 200) {
                cpsO.getTable($('.addItem').val().replace(/新增/, ''), cpsM.currentId);
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000);
                }
            }
        }
    },
    opt: {
        getData: function (key) {
            cpsA.common.otherUrl = `${market}/manage/config/readConf`;
            cpsA.common.data = $.extend({}, cpsA.commonParams, {
                key: key || '',
            });

            tytTool.ajaxFn(cpsA.common, cpsJ.renderDataFun);
        },
        getTable(name, id) { //请求类的表格
            cpsA.common.otherUrl = `${market}manage/config/read`;
            cpsA.common.otherData = {
                name
            }
            cpsA.common.data = $.extend({}, cpsA.commonParams, {
                confId: id
            });
            tytTool.ajaxFn(cpsA.common, cpsJ.getTableData);
        },
        addArgFun(pid, name, level) {
            cps.myData.flag = 'selectPArg';
            cps.myData.level = level;
            cpsA.common.otherData = {
                name
            }
            cpsA.common.otherUrl = `${market}manage/config/configChoose`;
            cpsA.common.data = $.extend({}, cpsA.commonParams, {
                pid: pid
            });
            tytTool.ajaxFn(cpsA.common, cpsJ.addArgFunData);
        },
        removeArg() { //二次确认
            if (cps.myData.flag == 'removeWarning') {
                var id = $('.addbox').attr('id');
                cpsA.common.otherUrl = `${market}manage/config/configDel`;
                cpsA.common.data = $.extend({}, cpsA.commonParams, {
                    id: id
                });
                tytTool.ajaxFn(cpsA.common, cpsJ.configDelData);
            } else if (cps.myData.flag == 'addTableParameters') { //新增列表数据入口
                var id = $('.active').attr('id');
                cpsA.common.otherUrl = `${market}manage/config/add`;
                cpsA.common.data = $.extend({}, cpsA.commonParams, {
                    id: id,
                    value: $('.argInput').val()
                });
                tytTool.ajaxFn(cpsA.common, cpsJ.configAddData);
            } else if (cps.myData.flag == 'removeTableParameters') {
                var len = $("#platList1 tr:not('.tableTh')").length;
                console.log(len);
                if(len == 1) {
                    $('.addbox,.outBgColor').hide().attr('id', '');
                    tytTool.toastShow('列表至少保留一项',2500)
                    return false;
                }
                var id = $('.addbox').attr('id');
                cpsA.common.otherUrl = `${market}manage/config/delete`;
                cpsA.common.data = $.extend({}, cpsA.commonParams, {
                    id: id
                });
                tytTool.ajaxFn(cpsA.common, cpsJ.configDeleteData);
            } else if (cps.myData.flag == 'selectPArg') {//选择后新加父级
                if ($('#newArgId').val() == '') {
                    tytTool.toastShow('请选择' + $('#newArgTitle').text().replace(/：/, ''), 2000);
                    return false;
                }
                cpsA.common.otherUrl = `${market}manage/config/configAdd`;
                cpsA.common.data = $.extend({}, cpsA.commonParams, {
                    id: $('#newArgId').val(),
                    level: cps.myData.level
                });
                tytTool.ajaxFn(cpsA.common, cpsJ.addConfigAddData);
            }
        },
        changeCheckbox(id) {
            cpsA.common.otherUrl = `${market}/Manage/config/setDefault`;
            cpsA.common.data = $.extend({}, cpsA.commonParams, {
                id: id
            });
            tytTool.ajaxFn(cpsA.common, cpsJ.changeCheckData);
        },
        allSortFun() {//排序所有
            var parr = [];
            for (var i = 0; i < $('.ulList li').length; i++) {
                parr.push({
                    id: $('.ulList').find('li').eq(i).attr('id'),
                    rule: i + 1
                });
            }
            cpsA.sortCommon.otherUrl = `${market}manage/config/editRule`;
            cpsA.sortCommon.data = $.extend({}, cpsA.commonParams, {
                rule_data: parr
            });
            console.log(cpsA.sortCommon.data);
            tytTool.ajaxFn(cpsA.sortCommon, cpsJ.allSortData);
        },
        addChildFun($this) {
            //更新当前配置的id
            cpsM.currentId = $($this).attr('id')
            cpsM.currentEn = $($this).attr('data-en')
            for (var i = 0; i < $('.ulList li').length; i++) {
                $('.ulList li').removeClass('active');
            }
            $($this).addClass('active');
            $('.addItem').val('新增' + $($this).text().replace(/✕/, ''));
            cpsO.getTable($($this).text().replace(/✕/, ''), $($this).attr('id'));
        },
        removeFun(id) {
            var len = $('#' + $(id).attr('id')).find('li').length;
            if(len <= 1) {
                tytTool.toastShow('此项不能再删除子项',2500)
                return false;
            }
            $('#' + $(id).attr('id')).find('.liClose').show();
        },
        cancelAllX() {
            $('.liClose').hide();
        },
        removeArgFun(event,id, name, flag) {
            event.stopPropagation();
            cps.myData.flag = flag;
            if (flag == 'removeWarning') {
                $('.inputBox').html('确定删除' + name + '?');
                $('.addbox,.outBgColor').show().attr('id', id);
            } else if (flag == 'addTableParameters') {
                //不做弹框添加 直接左侧列表转向品牌管理列表
                if (cpsM.currentEn === 'brand_id') { //品牌管理
                    tytTool.goToPub(tytTool.getRootPath_web() + '/back/model/html/dealerBusiness/carBrand.html', 'dealerBusiness/carBrand.html', 'userId=' + cpsA.commonParams.userId)
                } else {
                    //品牌、车型、长度、宽度、承载、自重、驱动方式、鞍座高度、平台样式、轮胎型号  ----- 10个字   其余20 
                    var len = 20;
                    if(cpsM.currentEn === 'vehicle_model' || cpsM.currentEn === 'vehicle_length' || cpsM.currentEn === 'vehicle_width' || 
                        cpsM.currentEn === 'vehicle_load' || cpsM.currentEn === 'vehicle_weight' || cpsM.currentEn === 'qdfs' || 
                        cpsM.currentEn === 'saddle_height' || cpsM.currentEn === 'platform_style'
                        //  || cpsM.currentEn === 'tire_model'
                    ) {
                        len = 10;
                    }
                    $('.inputBox').html('<label>' + $('.addItem').val().replace(/新增/, '') + '：</label><input class="argInput" type="text" value="" maxlength="'+ len +'">')
                    $('.addbox,.outBgColor').show();
                }
            } else if (flag == 'removeTableParameters') {
                //不做弹框添加 直接左侧列表转向品牌管理列表
                if (cpsM.currentEn === 'brand_id') { //品牌管理
                    tytTool.goToPub(tytTool.getRootPath_web() + '/back/model/html/dealerBusiness/carBrand.html', 'dealerBusiness/carBrand.html', 'userId=' + cpsA.commonParams.userId)
                    return false;
                }
                $('.inputBox').html('确定删除' + name + '?')
                $('.addbox,.outBgColor').show().attr('id', id);
            }
        },
        diyCarHandle: function () { //定制板车参考信息
            tytTool.openWinAuto('../../html/dealerBusiness/diyCar.html?userId=' + cpsA.commonParams.userId,'name',1200,600)
        }
    },
};

var cpsM = cps.myData;
var cpsA = cps.arr;
var cpsJ = cps.ajx;
var cpsO = cps.opt;
cpsO.getData();
