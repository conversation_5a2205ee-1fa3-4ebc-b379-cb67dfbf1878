var deal = {
    myData: {
        pageSize: 20,
        allList: {},
        wareHouseType: ['0##中心仓','1##城市仓'],
        wareHouseStatus: ['1##正常','2##禁用']
    },
    arr: {

        getList: {
            url: '',
            otherUrl: market + "Manage/WareHouse/read",
            type: 'POST',
            otherData: '',
            data: '',
        },
        submit: {
            url: '',
            otherUrl: ``,
            type: 'post',
            data: '',
        }
    },
    ajx: {
        listData: function (data, _currentPage) { //加载列表成功
            if (data.code == 200) {
                var data_list = data.data ? data.data : [];
                tytTool.pageFun({
                    total: data.count ? data.count : 0,
                    size: dealM.pageSize,
                    current: _currentPage,
                    pageCallback: 'dealO.listOpt'
                })
                //正式载入数据
                var rule = {
                    list: [
                        '仓库编号##wareHouseId',
                        '仓库名称##wareHouseName',
                        '仓库类型##wareHouseType##Fn::tytTool.switchItem({{wareHouseType}},dealM.wareHouseType)',
                        '地址##address##Fn::tytTool.placeRuleTwo({pro:{{province}},city:{{city}},county:{{area}}}, \'\')',
                        '负责人##principal',
                        '状态##wareHouseStatus##Fn::tytTool.switchItem({{wareHouseStatus}},dealM.wareHouseStatus)',
                        '负责人电话##cellPhone',
                        '操作##opt'
                    ],
                    item: {
                        cellPhone: {
                            attr: ['class==phoneHide']
                        }
                    },
                    opt: {
                        dom: `<a class="info_opt" onclick="dealO.updateItem({{wareHouseId}})">编辑</a>`
                    }
                }
                var tmp = tytTool.eachList(data_list, rule)
                dealM.allList = data_list;
                $("#platList").empty().append(tmp.str)
                tytTool.colResizable($("#platList"))
                tytTool.bindHidePhone();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },
        submitData: function (data) { //提交成功
            if(data.code == 200) {
                tytTool.reloadWin()
            } else {
                if(NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2500)
                }
            }
        }
    },
    opt: {
        loadList: function () {
            var pageArr = {
                "totalPages": 1,
                "pageSize": dealM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": dealO.listOpt    //促发函数
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        listOpt: function (currentPage) { //请求列表
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            dealA.getList.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            dealA.getList.data = $.extend({}, commonM.data.common, arr, {
                "currentPage": currentPage,
                "pageSize": dealM.pageSize,
                "isDetail": 0,
                "province": NO($("#tytCity").attr('data-val')).split(',')[0] || '',
                "city": NO($("#tytCity").attr('data-val')).split(',')[1] || '',
                'area': NO($('#tytCity').attr('data-val')).split(',')[2] || ''
            })
            tytTool.ajaxFn(dealA.getList, dealJ.listData)   //ajax请求函数
        },
        updateItem: function (data) {   //修改经销商
            $(".itemdialog .title .t").html("修改仓库")
            var item = dealM.allList.find(function (e) {
                return e.wareHouseId == data
            })
            tytTool.tnameGetHtml($('.itemdialog'), item);
            $("#tytCityWare").attr('data-val', tytTool.removeRuleStr([item.province, item.city, item.area], ',')).val(tytTool.placeRuleTwo({'pro': item.province,'city': item.city,'county': item.area}, ' '))
            $(".itemdialog .title .t").html("修改仓库");
            var htmlStr = ""
            if (item.wareHouseStatus == "1") {
                htmlStr = '<span class="state enable" onclick="dealO.itemdialog($(this))"><span class="i"></span><span class="t">正常</span></span>'
            } else {
                htmlStr = '<span class="state disable" onclick="dealO.itemdialog($(this))"><span class="i"></span><span class="t">禁用</span></span>'
            }
            $(".itemdialog .state_box").html(htmlStr);
            dealO.showHideItem("show");
        },
        showHideItem: function (type) { //交互定义方法
            if (type == "hide") {
                $(".itemdialogdask,.itemdialog").hide()
                tytTool.formReset($(".itemdialog"))
            } else {
                $(".itemdialogdask,.itemdialog").show()

            }
        },
        submitVaild: function (params) { //提交前的表单校验
            var flag = true;
            var wareHouseName = $(".itemdialog input[tname='wareHouseName']");
            if(NO(wareHouseName.val())==""){
                tytTool.toastShow("请输入仓库名称",1500)
                flag = false;
                return false;
            }
            var wareHouseType = $(".itemdialog select[tname='wareHouseType']");
            if(NO(wareHouseType.val())==""){
                tytTool.toastShow("请输入仓库类型",1500)
                flag = false;
                return false;
            }
            var address = $(".itemdialog input[tname='address']");
            if(NO(address.val())==""){
                tytTool.toastShow("请输入仓库地址",1500)
                flag = false;
                return false;
            }
            var principal = $(".itemdialog input[tname='principal']");
            if(NO(principal.val())==""){
                tytTool.toastShow("请输入负责人",1500)
                flag = false;
                return false;
            }
            var cellPhone = $(".itemdialog input[tname='cellPhone']");
            if(NO(cellPhone.val())==""){
                tytTool.toastShow("请输入负责人电话",1500)
                flag = false;
                return false;
            }
            var reg = /^(0|1)[0-9]{10}$/;
            if (!reg.test(cellPhone.val())) {
                tytTool.toastShow('请输入正确的绑定手机号', 1500)
                flag = false;
                return false;
            }
            return flag;
        },
        submitConfirm:function(){ //提交
            if(!dealO.submitVaild()) {
                return false;
            }
            var arr = tytTool.htmlGetTname($(".itemdialog"));
            var url = `${market}Manage/WareHouse/update`;
            if(NO(arr.wareHouseId) == '') {
                delete arr.wareHouseId
                url = `${market}Manage/WareHouse/add`
            }
            dealA.submit.otherUrl = url;
            dealA.submit.data = $.extend({}, commonM.data.common, arr, {
                'province': NO($('#tytCityWare').attr('data-val').split(',')[0]),
                'city': NO($('#tytCityWare').attr('data-val').split(',')[1]),
                'area': NO($('#tytCityWare').attr('data-val').split(',')[2]),
                'address': $('#tytCityWare').val(),
                'wareHouseStatus': $('.state_box .enable').length == 1 ? '1' : '2'
            })
            console.log(dealA.submit.data);
            tytTool.ajaxFn(dealA.submit, dealJ.submitData)

        },
        itemdialog: function (_this) { //编辑框
            if (_this.hasClass("enable")) {
                _this.removeClass("enable")
                _this.addClass("disable")
                _this.find(".t").html("禁用")
            } else {
                _this.removeClass("disable")
                _this.addClass("enable")
                _this.find(".t").html("正常")
            }
        }
    }
}
var dealM = deal.myData;
var dealA = deal.arr;
var dealJ = deal.ajx;
var dealO = deal.opt;
// $(".itemdialog").delegate(".state", "click", function () {
//     if ($(this).hasClass("enable")) {
//         $(this).removeClass("enable")
//         $(this).addClass("disable")
//         $(this).find(".t").html("正常")
//     } else {
//         $(this).removeClass("disable")
//         $(this).addClass("enable")
//         $(this).find(".t").html("禁用")
//     }
// })

$.when(
   
    tytTool.loadCheckTab(commonM.wareManage),
    // commonO.toolSearch('agent,brand',dealO.createSelectFun)
).done(function () {
    dealO.loadList();
}).fail(function () {
    dealO.loadList();
})