/*
    exportFun.js for export and import of Data
    author: heyuyan
*/
// var wcLocal = 'http://datadev.teyuntong.net/idc_dev_wc/idc_pro_market/index.php/'
var exportImport = {
    myData: {
        currentCheck: ''
    },
    arr: {
        status: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        },
        import: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
        },
        export: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: ''
        }
    },
    ajx: {
        updataStatusData: function (data) { //上架下架请求成功
            if (data.code == 200) {
                tytTool.reloadWin()
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 2000)
                }
            }
        }
    },
    opt: {
        checkAll: function (_this) { //全选
            _this.attr('checked') ? $(".checkItem").attr('checked','checked') : $(".checkItem").removeAttr('checked');
        },
        checkItem: function (_this) { //单选
            $(".checkItem:checked").length == $(".checkItem").length ? $("#checkAll").attr('checked','checked') : $("#checkAll").removeAttr('checked');
            if(tytTool.urlHtml(1) == 'carLibrary') { //只有车型库列表才进入执行
                expO.checkExclude(_this)
            }
        },
        checkExclude: function (_this) { //选择某项时候,禁选不同类的
            if(_this.attr('checked')) {
                $(".checkItem").each((i,e)=> {
                    $(e).data('shelves') != _this.data('shelves') ? $(e).hide() : ''
                })
                $("#checkAll").show()
                expM.currentCheck = _this.data('shelves')
            } else {
                $(_this).removeAttr('checked')
                if($(".checkItem:checked").length == 0) {
                    $("#checkAll").hide()
                    $(".checkItem").removeAttr('checked').show()
                }
                expM.currentCheck = ''
            }
        },
        shelves: function (state) { //上架二次确认 
            // 类型(state) 1:上架 0: 下架
            // 类型(state) 2:上架 1: 下架
            var len = $(".checkItem:checked").length;
            if(len == 0) {
                tytTool.toastShow(state == 2 ? '请勾选上架车型' : '请勾选下架车型', 2500)
                return false;
            }
            if(tytTool.urlHtml(1) == 'carLibrary') { //只有车型库列表才进入执行
                if(expM.currentCheck == state) {
                    tytTool.toastShow('所选车型当前状态已经是' + (state == 2 ? '上架' : '下架') + '状态，无需再次更改', 2500)
                    return false
                }
            }
            if(state == 2) {
                tytTool.showAlert('商品上架提示', '确定将选中的<em class="checkNoColor">'+ len +'</em>个车型上架？', 'expO.updataStatus', state);
            } else if(state == 1) {
                tytTool.showAlert('商品下架提示', '确定将选中的 <em class="checkNoColor">'+ len +'</em>个车型下架？下架后将删除车型的推荐信息。', 'expO.updataStatus', state);
            }
        },
        updataStatus: function (status,vId) { //上架,下架 入口 单独下架直接调用此方法
            var ids = vId ? [vId] : [];
            !vId && $(".checkItem:checked").each(function (vIndex,vEle) {
                ids.push($(this).attr('data-id'))
            })
            console.log(ids);
            expA.status.otherUrl = `${market}manage/vehicle/onOff`
            expA.status.data = $.extend({}, commonM.data.common, {
                vehicleIds: ids.join(','),
                type: status
            })
            tytTool.ajaxFn(expA.status, expJ.updataStatusData)
        },
        importOption: function(type){ //导入窗口交互
            if(type == 'show') {
                $(".outBgColor,.excelBox").show();
            } else if(type == 'hide') {
                $(".outBgColor").hide();
                $(".excelBox .alertContent").html('请点击"浏览"选择需要导入的模板文件');
                $(".excelBox h2").html("导入财务数据");
                $("#vehicleFile").attr("value","");
                $(".excelBox").hide();
                $("#uploadFile").removeClass('updataClass')
            }
        },
        fileInfo: function(source, _this){ //选择文件交互
            var f = source.files[0];
            var name = f.name;
            $(".excelBox .alertContent").html(name);
            $(".excelBox h2").html("导入车型");
        },
        importStatus: function () { //导入入口
            if($("#uploadFile").hasClass('updataClass')) {
                return false;
            }
            //选择文件
            var vehicleFile = $("#vehicleFile")[0].files[0];
            if (vehicleFile == null) {
                tytTool.toastShow("请选择导入文件","");
                return;
            }
            var formData = new FormData();
            formData.append("userId", commonM.data.common.userId);
            formData.append("client", commonM.data.common.client);
            formData.append("vehicleFile", vehicleFile);
            $("#uploadFile").addClass('updataClass')
			$.ajax({
				type: "POST",
				url: `${market}manage/vehicle/impVehicle`,
				data: formData,
				contentType: false,
				/**
				 * 必须false才会避开jQuery对 formdata 的默认处理
				 * XMLHttpRequest会对 formdata 进行正确的处理
				 */
				processData: false,
				async: false,
				dataType: "json",
				success: function(data) {
                    if(data.code == 200){
                        tytTool.toastShow(data.msg,"");
                        setTimeout(function(){
                            tytTool.reloadWin();
                        },1000)
                    }else{
                        tytTool.toastShow(data.msg,"");
                        $(".outBgColor").hide();
                        $(".excelBox .alertContent").html('请点击"浏览"选择需要导入的模板文件');
                        $(".excelBox h2").html("导入车型");
                        $("#vehicleFile").attr("value","");
                        $(".excelBox").hide();
                        $("#uploadFile").removeClass('updataClass')
                    }
				},
				error: function(xhr, status) {
					tytTool.showToast('导入车型数据失败,请检查文件及数据', '');
                    $("#uploadFile").removeClass('updataClass')
				}
			});
        },
        exportStatus: function (vehicleStatus) { //批量导出入口
            var arr = tytTool.htmlGetTname($(".searchUl"));
            tytTool.openPostWindow(`${market}manage/vehicle/export`,$.extend({}, commonM.data.common ,arr, {
                'vehicleStatus': vehicleStatus ? vehicleStatus : NO($('#vehicleStatusBox input:checked').val())
            }))
        },
        downModel: function () {
            expA.import.otherUrl = `${market}manage/vehicle/downLoadTemplate`;
            expA.import.data = commonM.data.common;
            tytTool.ajaxFn(expA.import, function (data) {
                if(data.code == 200) {
                    var newWin = NO(data.data) != '' ? window.open(data.data) : ''
                    setTimeout(function () {
                        newWin.close();
                    }, 2000);
                } else {
                    if(NO(data.msg) != '') {
                        tytTool.toastShow(data.msg,2500)
                    }
                }
            })
        }
    }
}

var expM = exportImport.myData;
var expA = exportImport.arr;
var expJ = exportImport.ajx;
var expO = exportImport.opt;