var domain = tytTool.getRootPath_web('owers_boss');
// var domain = 'http://192.168.2.52:8080/manage_war';
var common = {
    myData: {
        checkItem: {
            pcUpgradeList: { //任务列表查询项
                box: '.searchUl=>li',
                list: [
                    {
                        label: '订单ID：',
                        attr: ['tname##taskName', 'maxlength##20', "onblur##commonO.inpBlurValid($(this),'cName')"],
                    },
                    {
                        label: '客户手机号：',
                        attr: ['tname##taskName', 'maxlength##20', "onblur##commonO.inpBlurValid($(this),'cName')"],
                    },
                    {
                        label: '客户姓名：',
                        attr: ['tname##taskName', 'maxlength##20', "onblur##commonO.inpBlurValid($(this),'cName')"],
                    },
                    {
                        label: '保险费用：',
                        boxAttr: ['class##doubleBox'],
                        // attr: ['tname##taskName', 'maxlength##20', "onblur##commonO.inpBlurValid($(this),'cName')"],
                        dom: `<input type="text"><span>-</span><input type="text"><span>万</span>`
                    },
                    {
                        label: '保险保额：',
                        attr: ['tname##taskName', 'maxlength##20', "onblur##commonO.inpBlurValid($(this),'cName')"],
                    },
                    {
                        label: '投保渠道：##select',
                        attr: ['tname##upgradeModel'],
                        option: ['请选择##','静默##0','强制##1','普通##2'],
                    },
                    {
                        label: '保险类型：##select',
                        attr: ['tname##upgradeType'],
                        option: ['请选择##','SDK##0','软件包##1'],
                    },
                    {
                        label: '保险公司：##select',
                        attr: ['tname##upgradeType'],
                        option: ['请选择##','SDK##0','软件包##1'],
                    },
                    {
                        label: '交易状态：##select',
                        attr: ['tname##upgradeType'],
                        option: ['请选择##','SDK##0','软件包##1'],
                    },
                    {
                        label: '支付时间：',
                        attr: ['tname##upgradeStartTime', 'id##startTime', 'readonly##readonly', 'onclick##commonO.startPicker("endTime",true)'],
                    }
                ],
            }
        },
        data: {
            optTask: {},
            userIds: []
        },
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
        },
    },
    ajx: {},
    opt: {
        lookBigImg: function (_this, Small) {
            //base图片窗口浏览
            var size = NO(Small) != '' ? [600, 300] : [1200, 600];
            var _img = _this.attr('data-url') || _this.attr('src');
            var img = new Image(),
                isTop = (window.screen.availHeight - 30 - size[1]) / 2,
                isLeft = (window.screen.availWidth - 10 - size[0]) / 2;
            img.style = '-webkit-user-select:none;margin:auto;display:block;text-align:center;vertical-align: middle;';
            img.src = _img;
            var newWin = window.open(
                '',
                Small,
                'width=' +
                    size[0] +
                    ',height=' +
                    size[1] +
                    ',top=' +
                    isTop +
                    ',left=' +
                    isLeft +
                    ',status=,toolbar=no,menubar=no,location=no,resizable=no,scrollbars=yes,titlebar=no'
            );
            newWin.document.write(img.outerHTML);
            newWin.document.body.style = 'margin:0px;background:#0e0e0e;';
            newWin.document.title = '查看图片';
            newWin.document.close();
        },
        inpFocusSave: function (_this) { //暂存值
            _this.attr('data-oldval',_this.val())
        },
        inpBlurValid: function (_this, _reg, _toast,isFormat,numId) {
            var flag = true;
            var prevVal = numId ? $(numId).val().split(',').join('') : null;
            console.log('--------->',prevVal)
            if (_reg != '' && NO(_this.val()) != '' && !_reg.test(_this.val())) {
                _toast && tytTool.toastShow(_toast, 2500)
                _this.val(_this.attr('data-oldval') || '');
                flag = false;
            }else if(_this.val() < parseInt(prevVal)){
                _toast && tytTool.toastShow('请输入大于前位的数字', 2500)
                _this.val(_this.attr('data-oldval') || '');
                flag = false;
            }else {
                _this.val(isFormat? tytTool.formatSeparator(_this.val()):_this.val());
            }
            return flag;
        },
        eachValid: function (arr, dataStr) {
            var bloon = false,
                dataStr = NO(dataStr) == '' ? 'tname' : dataStr;
            if (NO(arr) != '') {
                $.each(arr, function (index, ele) {
                    var type = ele.split('##')[0],
                        tname = ele.split('##')[1],
                        toast = ele.split('##')[2];
                    var obj = $(`${type}[${dataStr}="${tname}"]`),val = '';

                    if(type == 'select' || type == 'textarea') {
                        val = NO(tytTool.replaceStrHT(obj.val()))
                    } else {
                        val = NO(tytTool.replaceStrHT(obj.html()))
                    }
                    var radioArr = [];
                    if (type == 'input') {
                        val = NO(tytTool.replaceStrHT(obj.val()))
                        if(obj[0].type == 'radio') {
                            obj.each(function (index,ele) {
                                if($(ele).attr('checked')) {
                                    radioArr.push($(ele).val())
                                }
                            })
                            val = radioArr.length == 0 ? '' : radioArr[0]
                        }
                    }  
                    if (val == '') {
                        tytTool.toastShow(toast, 2000);
                        bloon = true;
                        return false;
                    }
                });
            }
            return bloon;
        },
        colResizable: function (cb) {
            $(".tableTh th").width(80)
            $('.JCLRgrips').remove();
            var tabWidth = $('.tableBox').parents('.dataTable').width();
            $('.tableBox').width(tabWidth);
            $('.JCLRgrips').width(tabWidth);
            cb && cb()
            tytTool.colResizable($('.tableBox'));
            
        },
        clearFile: function (e) {
            e.target.value = '';
        },
        onpicking: function (dp) {
            var val = NO(dp.cal.getNewDateStr());
            var date = new Date(val),
                timeString = Date.parse(date);
            $(dp.el).attr('data-time', timeString);
        },
        oncleared: function (dp) {
            $(dp.el).removeAttr('data-time');
        },
        timePicker: function () { //时间控件
            return WdatePicker({
                // maxDate: '#F{$dp.$D(\'ctimeEnd\')}',
                dateFmt: 'yyyy-MM-dd',
                onpicking: commonO.onpicking,
                oncleared: commonO.oncleared,
            });
        },
        startPicker: function (endTime, edge) {
            console.log(endTime, edge)
            var edgeArr = {
                dateFmt: 'yyyy-MM-dd HH:mm:ss',
                minDate: '%y-%M-%d #{%H}:#{%m}:#{%s}',
                maxDate: `#F{$dp.$D(${endTime})}`,
                onpicking: commonO.onpicking,
                oncleared: commonO.oncleared,
            }
           
            if(edge) {
                console.log(edgeArr)
                delete edgeArr.minDate
            }
            //时间控件--开始
            return WdatePicker(edgeArr);
        },
        endPicker: function (startTime, edge) {
            var edgeArr = {
                dateFmt: 'yyyy-MM-dd HH:mm:ss',
                minDate: NO($("#startTime").val()) === '' ? '%y-%M-%d #{%H}:#{%m}:#{%s}' : `#F{$dp.$D(${startTime})}`,
                onpicking: commonO.onpicking,
                oncleared: commonO.oncleared,
            }
            if(edge) {
                edgeArr.minDate = `#F{$dp.$D(${startTime})}`
            }
            //时间控件--结束
            return WdatePicker(edgeArr);
        },
        operate: function (type, opt, param) {
            //操作方法
            var arr = {
                delete: {
                    title: '删除提示',
                    content: '是否删除该任务？',
                    btn: '<button onclick="listO.deleteLine()" class="close_block true_btn fr">是</button>'
                },
                stop: {
                    title: '终止提示',
                    content: '是否终止该任务？',
                    btn: '<button onclick="listO.stopTask()" class="close_block true_btn fr">是</button>'
                }
            }
            if (type == 'close') {
                commonM.data.optTask = {};
                $('.outBgColor,.delOutLook').fadeOut(100);
                $(".luckName,.delTitle").html('')
                $(".ideabtnBox .true_btn").remove()
            } else if (type == 'open') {
                $(".luckName").html(arr[opt].title)
                $(".delTitle").html(arr[opt].content)
                $(".ideabtnBox").append(arr[opt].btn)
                commonM.data.optTask = param;
                $('.outBgColor,.delOutLook').fadeIn(200);
            }
        }
    },
}

var commonM = common.myData;
var commonA = common.arr;
var commonJ = common.ajx;
var commonO = common.opt;

var timer = null;
window.onresize = function () {
    clearTimeout(timer)
    timer = setTimeout(() => {
        if($('.tableBox').length > 0) {
            commonO.colResizable(function (params) {
                $(".tableTh th:last-child").width(190)
            });
        }
    }, 200);
}