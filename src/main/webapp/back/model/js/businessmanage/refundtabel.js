/*
    userCommon.js for model/html/user/*.hmtl
    author: hey<PERSON>an
*/
var flowUrl = tytTool.getRootPath_web();
var common = {
    myData: {
        plat: {//查询项配置-总用户信息管理
            box: '.searchUl=>li',
            list: [
                {
                    label: '商户退单ID：',
                    attr: ['tname##refundOrderNo', 'maxlength##50'],
                },
                {
                    label: '业务订单ID：',
                    attr: ['tname##businessNo', 'maxlength##50'],
                },
                {
                    label: '业务类型：##select',
                    attr: ['tname##orderType', 'maxlength##50'],
                    option: ['全部##',]
                },
                // {
                //     label: '商品名称：##select',
                //     attr: ['tname##subject', 'maxlength##50'],
                //     option: ['全部##',]
                // },
                {
                    label: '退款状态：##select',
                    attr: ['tname##status', 'maxlength##50'],
                    option: ['请选择##','退款中##1','退款失败##3','退款成功##2']
                },
                {
                    label: '申请退款起始时间：',
                    attr: ['tname##startRefundTime', 'id##timeStart', 'readonly##readonly', 'onclick##comO.timeStart()'],
                },
                {
                    label: '申请退款结束时间：',
                    attr: ['tname##endRefundTime', 'id##timeEnd', 'readonly##readonly', 'onclick##comO.timeEnd()'],
                }
            ]
        },
    },
    arr: {},
    ajx: {},
    opt: {
        timeStart: function () { //选择日期控件
            return WdatePicker({
                readOnly: true,
                dateFmt: 'yyyy-MM-dd',
                maxDate: '#F{$dp.$D(\'timeEnd\')}'
            })
        },
        timeEnd: function () {
            return WdatePicker({
                readOnly: true,
                dateFmt: 'yyyy-MM-dd',
                minDate: '#F{$dp.$D(\'timeStart\')}',
                startDate: '#F{$dp.$D(\'timeStart\')}'
            })
        },
        timeSet: function () {
            $("#timeStart").val(tytTool.isToday(-3).dayStr)
            $("#timeEnd").val(tytTool.isToday().dayStr)
        }
    }
}

var comM = common.myData;
var comA = common.arr;
var comJ = common.ajx;
var comO = common.opt;

/*
    userInfoAll.js for userInfoAll.hmtl
    author: syl
*/
var flow = {
    myData: {
        pageSize: 20,
    },
    arr: {
        common: {
            url: '',
            otherUrl: '',
            type: 'post',
            otherData: '',
            data: '',
            dataType : "json",
        }
    },
    ajx: {
        listData: function (data, _currentPage) {
            if (data.code == 200) {
                var data_list = data.data.refundList;
                var currentPage = Number(data.data.pageBean.currentPage) || 1,
                    totalPages = Number(data.data.pageBean.maxPage);
                var arrParams = {
                    currentPage: currentPage || 1,
                    totalPages: totalPages || 1
                }
                if (data_list && totalPages > 0) {
                    $("#totalPages").html(totalPages); // 总页数
                    $("#currentPage").html(currentPage); // 当前页数
                    $("#num-transactions").html(data.data.pageBean.rowCount)
                    $("#rowCount").html(data.data.pageBean.rowCount); // 总数据
                    $("#amount-money").html(data.data.ordersRefundAmount) // 退款金额
                    tytTool.jqPaginatorExtend(arrParams, 'flowO.listOpt')//
                    data_list.forEach(function (item) {
                        var ctime = Number(`${tytTool.NullObj(item.ctime)}`);
                        var mtime = Number(`${tytTool.NullObj(item.mtime)}`);
                        item.ctime = tytTool.timeTrans(ctime, { timeOpt: ['Y', 'M', 'D', 'h', 'm', 's'], splitStr: "/" }).replace(/\s+/g, " ")
                        item.mtime = tytTool.timeTrans(mtime, { timeOpt: ['Y', 'M', 'D', 'h', 'm', 's'], splitStr: "/" }).replace(/\s+/g, " ")
                    })
                } else {
                    $("#totalPages").html(1);
                    $("#currentPage").html(1);
                    $("#rowCount").html(0);
                    $("#num-transactions").html(0)
                    $("#amount-money").html(0)
                    tytTool.jqPaginatorExtend(arrParams, 'flowO.lilass="info_opt"stOpt')
                }
                //正式载入数据
                var rule = {
                    list: [
                        '业务类型##orderType',
                        '业务订单ID##businessNo',
                        '用户IDwidth100##userId',
                        '商户退单ID##refundOrderNo',
                        '商品名称##subject',
                        '支付金额##payAmount',
                        '退款金额##amount',
                        '退款状态##status',
                        '退款时间##ctime',
                        '退款到账时间##mtime'
                    ]
                }
                var tmp = tytTool.eachList(data_list, rule)
                $("#platList").empty().append(tmp.str)
                tytTool.colResizable($("#platList"))
                tytTool.bindHidePhone();
            } else {
                if (NO(data.msg) != '') {
                    tytTool.toastShow(data.msg, 4000)
                }
            }
        },

    },
    opt: {
        loadList: function () {
            var pageArr = {
                "totalPages": 1,
                "pageSize": flowM.pageSize,
                "visiblePages": 7,
                "currentPage": 1,
                "funName": flowO.listOpt    //促发函数
            }
            tytTool.jqPaginator('#pageNumberBox', pageArr);
        },
        getpayTypeData: function () { //获取业务类型数据
            if (comM.plat.list[2].option.length <= 1) {
                tytTool.ajaxFn({
                    url: '',
                    otherUrl: flowUrl + "/ordersPay/getOrdersDict",
                    type: 'post'
                }, function (res) {
                    var data = res.data.orderTypeList;
                    data.forEach(function (e) {
                        comM.plat.list[2].option.push(e+ "##" + e)
                    })
                    // flowO.getbusinessTypeData()
                    tytTool.loadCheckTab(comM.plat, {})  //搜索条
                    comO.timeSet()
                    flowO.loadList();
                })
            }
        },
        getbusinessTypeData: function () { //获取 商品名称数据
            if (comM.plat.list[3].option.length <= 1) {
                tytTool.ajaxFn({
                    url: '',
                    otherUrl: flowUrl + "/ordersPay/getOrdersDict",
                    type: 'post'
                }, function (res) {
                    var data = res.data.subjectList;
                    data.forEach(function (e) {
                        comM.plat.list[3].option.push(e+ "##" + e)
                    })
                    tytTool.loadCheckTab(comM.plat, {})  //搜索条
                    comO.timeSet()
                    flowO.loadList();
                })   //ajax请求函数
            }
        },
        listOpt: function (currentPage) {   //
            currentPage == '' ? currentPage = 1 : currentPage = currentPage;
            flowA.common.otherUrl = flowUrl + '/ordersRefund/ordersRefundList'
            // flowA.common.otherData = currentPage;
            var arr = tytTool.htmlGetTname($(".searchUl"));
            arr.startRefundTime = new Date(arr.startRefundTime).getTime()
            arr.endRefundTime = new Date(arr.endRefundTime).getTime()
            flowA.common.data = $.extend({}, flowM.common, arr, {
                "currentPage": currentPage,
                "pageSize": flowM.pageSize,
            })
            console.log(arr)
            tytTool.ajaxFn(flowA.common, flowJ.listData)   //ajax请求函数
        },
        importList: function () { //导出列表数据
            var arr = tytTool.htmlGetTname($(".searchUl"));
            tytTool.openPostWindow(flowUrl+'/ordersRefund/exportOrdersRefundList', arr, {}, {noClose: true});
        }
    }
}

var flowM = flow.myData;
var flowA = flow.arr;
var flowJ = flow.ajx;
var flowO = flow.opt;

flowO.getpayTypeData()


$(window).resize(function () {
    tytTool.colResizable($("#platList"))
    tytTool.colResizableExtend()
})