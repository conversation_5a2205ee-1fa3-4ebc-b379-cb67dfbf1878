/*弹框样式*/
.outBgColor {
	width: 100%;
	height: 100%;
	position: fixed;
	_position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	background: #000;
	opacity: 0.7;
	filter: alpha(opacity=70);
	z-index: 1010;
}
@color:#333;
@input_border:#ccc;
.insuranceOut {
	margin: -140px 0 0 -260px;
}
.innerTitBox {
	width: 520px;
	position: fixed;
	left: 50%;
	top: 50%;
	border: 4px solid #dae7ff;
	background: #fff;
	z-index: 1012;

	.titInnerBox {
		margin: 40px 0 0 40px;

		.makedTit {
			font-size: 20px;
			color: #333;
			margin-bottom: 12px;
			width: 432px;
			word-wrap: break-word;
		}
		.makedDetil {
			font-size: 12px;
			color: #999;
			width: 434px;
		}
	}
	.makedBtn {
		text-align: right;
		margin: 20px 40px 20px 0;

		button {
			width: 80px;
			height: 32px;
			text-align: center;
			line-height: 32px;
			border: none 0;
			border-radius: 4px;
			margin-left: 12px;
			cursor: pointer;
		}
		.trueBtn {
			background: #3961b2;
			color: #fff;

			&:hover, &:active {
				background: #394db3;
			}
		}
		.falseBtn {
			background: #fff;
			color: #333;
			border: 1px solid #e7e7e7;
		}
	}
	.closeOutBox {
		position: absolute;
		right: 10px;
		top: 10px;
		width: 12px;
		height: 12px;
		background: url(../images/closeQuery.png) no-repeat;
		background-size: 100% 100%;
		cursor: pointer;
	}
}
.defaultStyle {
	font-size: 14px;
	color: @color;
}
.outInp {
	margin-top: 20px;
	label {
		vertical-align: middle;
		font-weight: normal;
		margin-right: 10px;
		line-height: 26px;
		.defaultStyle;
	}
	input {
		vertical-align: middle;
		display: inline-block;
		height: 26px;
		padding: 0 4px;
		border: 1px solid @input_border;
	}
	span {
    vertical-align: bottom;
    display: inline-block;
    padding: 0 4px;
    margin-top: 10px;
	}
	.yuan {
		width: 150px;
		text-align: right;
	}
	.jiaofen {
		width: 120px;
		text-align: left;
	}
}
.errorLineTit {
	display: block;
	height: 20px;
	line-height: 20px;
	margin-left: 125px;
	font-size: 14px;
	color: red;
}