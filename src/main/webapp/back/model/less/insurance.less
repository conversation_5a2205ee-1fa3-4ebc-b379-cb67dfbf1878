@bg_color: #f7f7f7;
@line_color: #e7e7e7;
@color: #333;
@blue: #3961b2;
@tit_color: #26a2d7;
@gray: #999;
@ccc: #ccc;

.contentBox {
	padding: 0 20px;
	width: 100%;
	margin: 0 auto;
	box-sizing: border-box;
}
.defaultStyle {
	font-size: 14px;
	color: @color;
}
.titUrl {
	padding: 20px 0 !important;

	span {
		.defaultStyle;
	}
	.currentPage {
		color: @tit_color;
	}
}

/*查看保险详情*/
.messageTit {
	height: 46px;
	line-height: 46px;
	background: @bg_color;
	box-sizing: border-box;
	padding: 0 10px;
	border: 1px solid @line_color;

	h2 {
		.defaultStyle;
		font-weight: 600;
	}
	label, strong {
		.defaultStyle;
	}
	a {
		font-size: 14px;
		color: @blue;
		cursor: default;
	}
}
.detailBox {
	margin-bottom: 20px;

	.detailListBox {
		width: 100%;
		padding: 8px 10px;
		box-sizing: border-box;
		border-bottom: 1px solid @line_color;

		li {
			line-height: 34px;

			label, span {
				.defaultStyle;
			}
		}
		.oneLine {
			width: 100%;
		}
		.twoLine {
			width: 50%;
		}
	}
	.transferBox {
		display: none;
		border-bottom: 1px solid @line_color;

		.innerTransferBox {
			max-height: 140px;
			overflow-y: auto;
			margin: 8px 0;
			background: #fff;

			.detailListBox {
				border-bottom: none 0;
				padding: 0 10px;
			}
		}
	}
}

/*沟通记录*/
.linkRecordList {
	background: #d5dae6;

	li {
		background: #fff;
	}
	&>li:last-child {
		border-bottom: 1px solid @ccc;
	}
	.reListTopBox {
		position: relative;
		height: 46px;
		line-height: 46px;
		background: @bg_color;
		border-top: 1px solid @ccc;

		.minWidthBox {
			display: inline-block;
			min-width: 320px;
		}
		.reListTop {
			margin-left: 12px;

			span {
				line-height: 46px;
				font-size: 14px;
				color: @color;
			}
			.customName, .customTime {
				font-weight: bold;
			}
			.customTime {
				margin: 0 10px;
			}
			.callLabel {
				margin-left: 60px;
				font-size: 14px;
				color: @gray;
				font-weight: normal;
			}
		}
		.triangle_border {
			margin: 0 auto 20px;
			position: absolute;
			right: 20px;
			top: 0;

			a {
				color: #666;
				text-decoration: none;
			}
			.quest {
				display: block;
				cursor: pointer;
			}
			a.noOverCustom {
				color: #189beb;
				text-decoration: none;
			}
			.popup {
				width: 70px;
				line-height: 24px;
				background: #fff;
				padding: 10px;
				color: @color;
				border-radius: 4px;
				position: absolute;
				top: 45px;
				right: -16px;
				border: 1px solid #d4efff;

				span {
					display: block;
					width: 0;
					height: 0;
					border-width: 0 10px 10px;
					border-style: solid;
					border-color: transparent transparent #d4efff;
					position: absolute;
					top: -10px;
					right: 25px;
					margin-left: -10px;
				}
				em {
					display: block;
					width: 0;
					height: 0;
					border-width: 0 10px 10px;
					border-style: solid;
					border-color: transparent transparent #fff;
					position: absolute;
					top: 1px;
					left: -10px;
				}
				a {
					text-align: center;
					display: block;
					font-weight: normal;
					cursor: pointer;

					&:hover {
						color: #189beb;
					}
				}
			}
		}
	}
	.problemHandle {
		padding: 12px;

		li {
			label {
				float: left;
				width: 70px;
				font-size: 14px;
				line-height: 32px;
				font-weight: normal;
				color: @gray;
			}
			span {
				float: left;
				width: 92%;
				word-break: break-all;
				line-height: 24px;
				margin-top: 4px;
			}
		}
	}
}
.NoHaveList {
	width: 200px;
	margin: 160px auto;

	img {
		display: block;
		width: 98px;
		height: 76px;
		margin: 0 auto;
	}
	p {
		font-size: 16px;
		text-align: center;
		margin-top: 20px;
		color: #999;
	}
}
#pageNumberBigBox {
	margin-bottom: 80px;

	#pageNumberBox {
		padding-left: 17px;
		margin: 30px 0 !important;

		li {
			border-radius: 4px;

			a {
				margin-right: 6px;
				padding: 4px 12px;
				color: @color;
				border-radius: 4px;
				border: 1px solid #ddd;
			}
		}
		.active a, li a:hover {
			color: #fff;
			background-color: @blue;
			border-color: @blue;
		}
		.disabled a:hover {
			color: @color;
			background-color: #fff;
			border-color: #ddd;
		}
	}
	.jumpPage {
		margin: 30px 0 30px 20px;
		font-size: 14px;
		color: @color;

		.keuInput {
			width: 50px;
			height: 30px;
			text-align: center;
			border: 1px solid #ddd;
			border-radius: 4px;
			margin: 0 10px;
		}
		.btnSure {
			width: 60px;
			height: 30px;
			border: none 0;
			border: 1px solid #ddd;
			border-radius: 4px;
			background: #fff;
			margin-left: 10px;
		}
	}
}