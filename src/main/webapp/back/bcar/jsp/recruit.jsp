<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    String path = request.getContextPath();
    String basePath = "https://" + request.getServerName() + path + "/";
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>板车司机招聘列表页</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="back/model/js/bootstrap.js"></script>
    <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
    <script type="text/javascript">
        <%@include file="/back/bcar/js/source.js" %>
    </script>
    <script type="text/javascript">
        <%@include file="/back/bcar/js/source2.js" %>
    </script>
    <script src="back/js/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script type="text/javascript" src="tyt_area/js/tyt_area.js"></script>
</head>

<body>
<div class="contentBox">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>板车司机招聘</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">列表</span>
    </h1>
    <div class="main-content">
        <form action="manage/bcar/recruit/list" method="post">
           <%--  <input id="sheng" name="province" value="${tytCarNews.province}" type="hidden">
            <input id="shi" name="city" value="${tytCarNews.city}" type="hidden">
            <input id="xian" name="county" value="${tytCarNews.county}" type="hidden"> --%>

            <input type="hidden" value="${pageNo}" name="pageNo">
            <!--搜索条件-->
            <div class="searchBox" id="searchCondition">
                <ul class="searchUl searchUlNone cf">
                    <li><label>ID：</label><input name="id" type="text" value="${bCar.id}"></li>
                    <li><label>标题：</label><input name="title" type="text" value="${bCar.title}"></li>
                    <li style="height: 24px">
                         <label>区域：</label>
                            <div style="position: absolute; z-index: 1000">
                                <div class="fl areaDiv twoDiv" style="position: absolute; left: 98px;">
                                    <input id="position" type="text" class="input120" placeholder="请选择地区" onclick="tyt_open_area('1','position','boxDiv','sheng','shi','xian','tyt_area_iframe','3');" readonly="" value="${bCar.province }${bCar.city }${bCar.county }">
                                    <div style="display:none;float:left;position:absolute;z-index:999;top:0;left:-1px" id="boxDiv" class="boxDiv">
                                        <iframe name="tyt_area_iframe" id="tyt_area_iframe" src="back/model/js/tyt_area/area.html" frameborder="0" width="240" scrolling="no" height="400" leftmargin="0" topmargin="0" allowtransparency="true"></iframe>
                                    </div>
                                    <p class="noneInput mtl20"><input id="sheng" name="province" type="text" value="${bCar.province }" class="positionInput"></p>
                                    <p class="noneInput mtl20"><input id="shi" name="city" type="text" value="${bCar.city }" class="positionInput"></p>
                                    <p class="noneInput mtl20"><input id="xian" name="county" type="text" value="${bCar.county }" class="positionInput"></p>
                                    <div class="cb"></div>
                                </div>
                            </div>
                        </li>
                    <li><label>司机职务：</label>
                        <select name="dutyCode">
                            <option value=''>请选择</option>
                        </select>
                    </li>
                    <li><label>薪资待遇：</label>
                        <select name="salaryCode">
                            <option value=''>请选择</option>
                        </select>
                    </li>
                    <li><label>车辆类型：</label>
                        <select name="carTypeCode">
                            <option value=''>请选择</option>
                        </select>
                    </li>
                    <li><label>联系人：</label><input name="telName" type="text" value="${bCar.telName}">
                    </li>
                    <li><label>联系电话：</label><input name="telephone" type="text"
                                                   value="${bCar.telephone}"></li>
                    <li><label>审核状态：</label>
                        <select name="status">
                            <option value="">请选择</option>
                            <option value="3" <c:if test="${bCar.status==3 }">selected</c:if>>审核通过</option>
                            <option value="1" <c:if test="${bCar.status==1 }">selected</c:if>>审核失败</option>
                            <option value="2" <c:if test="${bCar.status==2 }">selected</c:if>>未审核</option>
                        </select>
                    </li>

                    <li><label>展示开关：</label>
                        <select name="displayStatus">
                            <option value="">请选择</option>
                            <option value="0" <c:if test="${bCar.displayStatus==0 }">selected</c:if>>开放</option>
                            <option value="1" <c:if test="${bCar.displayStatus==1 }">selected</c:if>>关闭</option>
                        </select>
                    </li>

                    <li><label>删除：</label>
                        <select name="delStatus">
                            <option value="">请选择</option>
                            <option value="0" <c:if test="${bCar.delStatus==0 }">selected</c:if>>有效</option>
                            <option value="1" <c:if test="${bCar.delStatus==1 }">selected</c:if>>无效</option>
                        </select>
                    </li>

                    <li><label>发布账号：</label><input name="cellPhone" class="input126" type="text"
                                                   value="${bCar.cellPhone}"></li>
                    <li><label>阅读次数：</label><input name="readNbr" type="text" value="${bCar.readNbr}">
                    </li>
                   
                    <li><label>发布日期：</label><input onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" style="width:46px;"
                                                   name="publishTimeStart" type="text"
                                                   value="<fmt:formatDate value="${bCar.publishTimeStart}" pattern="yyyy-MM-dd"/>">-
                        <input onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" style="width:46px;margin-left:4px;" name="publishTimeEnd"
                               type="text"
                               value="<fmt:formatDate value="${bCar.publishTimeEnd}" pattern="yyyy-MM-dd"/>">
                    </li>
                    <%--  <li>
                    <label>每页显示：</label><input class="input126" name="pageSize" type="text" value="${pageSize}">
                    <input class="input126" name="pageSize" type="hidden" value="20">
                    </li> --%>
                </ul>
                <div style="clear:both"></div>

            </div>

            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li><input type="submit" class="button" value="查询"></li>
                        <li><input type="reset" class="button" value="重置"></li>
                    </ul>
                </div>
                <div class="clearfix"></div>
                <div class="messageRight fr">
                    查询到<em>${rowCount}</em>条信息，当前是第<span>${pageNo}</span>页，共<span>${maxPage}</span>页。
                </div>
            </div>
        </form>

        <!--内容列表-->
       <!-- 可拖拽表格 -->
    <div id="receiptInfo" class="receiptInfo" style="display: none;"><i></i><div class="receiptInner cf"><span></span><em>复制</em></div></div>
    <div class="dataTable">
        <table border="0" cellpadding=0 cellspacing=0 class="tableBox" id="table1">
            <tr class="tableTh">
                <th>操作</th>
                <th>ID</th>
                <th>标题</th>
                <th>司机职务</th>
                <th>工作地区</th>
                <th>车辆类型</th>
                <th>薪资待遇</th>
                <th>联系人</th>
                <!-- <th>联系电话</th> -->
                <th>备注</th>
                <!-- <th>发布账号</th> -->
                <th>发布日期</th>
                <th>展示开关</th>
                <th>审核状态</th>
                <th>删除状态</th>
                <th>阅读次数</th>
                <th>收藏次数</th>
            </tr>
            <c:forEach items="${bCarList}" var="bCar">
                <tr>
                    <td><img class="info_detail" src="back/model/images/edit.png" width="20" height="20" alt=""
                             onclick="openWinAuto('manage/bcar/recruit/getById?id=${bCar.id}','newwindow',1100,600)">
                    </td>
                    <td>${bCar.id}</td>
                    <td>${bCar.title}</td>
                    <td>${bCar.duty}</td>
                    <td>${bCar.province}${bCar.city}${bCar.county}</td>
                    <td>${bCar.carType}</td>
                    <td>${bCar.salary}</td>
                    <td>${bCar.telName}</td>
                    <!-- <td class="phoneHide">${bCar.telephone}</td> -->
                    <td>${bCar.remark}</td>
                    <!-- <td>${bCar.cellPhone}</td> -->
                    <td><fmt:formatDate value="${bCar.publishTime}" pattern="yyyy-MM-dd HH:mm:ss" type="both"/></td>
                    <td class="colorBlue">
                        <c:if test="${bCar.displayStatus==0}">开放</c:if>
                        <c:if test="${bCar.displayStatus==1}">关闭</c:if>
                    </td>
                    <td class="colorBlue">
                        <c:if test="${bCar.status==3}">审核通过</c:if>
                        <c:if test="${bCar.status==1}">审核未通过</c:if>
                        <c:if test="${bCar.status==2}">未审核</c:if>
                    </td>
                    <td class="colorBlue">
                        <c:if test="${bCar.delStatus==0}">有效</c:if>
                        <c:if test="${bCar.delStatus==1}">无效</c:if>
                    </td>
                    <td>${bCar.readNbr}</td>
                    <td style="color: #428bca;cursor: pointer;" 
                        onclick="openWinAuto('manage/bcar/recruit/getCollectionById?id=${bCar.id}','newwindow',1100,600)">${bCar.collectionNbr}</td>
                </tr>
            </c:forEach>

        </table>
       </div>
        <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
    </div>
</div>
</body>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
</html>