<%@ page language="java" contentType="text/html; charset=UTF-8"
pageEncoding="UTF-8" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
String path = request.getContextPath();
String basePath = "https://" + request.getServerName() + path + "/";
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>板车司机求职编辑详情页</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/detailPages.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/detailPagesAdd.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="back/model/js/bootstrap.js"></script>
    <script type="text/javascript">
        <%@include file="/back/bcar/js/source.js" %>
    </script>
    <script type="text/javascript">
        function submitData() {
            if (validateForm()) {
                $("#updateBtn").unbind("click");
                $.ajax({
                    type: "POST",
                    url: "manage/bcar/job/update",
                    data: $('#form').serialize(),
                    dataType: "json",
                    success: function (data) {
                        if (data.code == 400 || data.code == 600) {
                            if (confirm(data.msg)) {
                                window.close();
                                window.opener.location.reload();
                            } else {
                                $("#updateBtn").bind("click", submitData);
                            }
                        } else {
                            alert(data.msg);
                            window.close();
                            window.opener.location.reload();
                        }
                    },
                    error: function (xhr, status) {
                        alert("error");
                    }
                });
            } else {

            }

        }
        //表单验证
        function validateForm() {
            var title = $("input[name='title']").val();
            var dutyCode = $("select[name='dutyCode']").val();
            var salaryCode = $("select[name='salaryCode']").val();
            var telName = $("input[name='telName']").val();
            var telephone = $("input[name='telephone']").val();
            /* var province=$("#province").val();
             var city=$("#city").val();
             var county=$("#county").val(); */
             var status = $("select[name='status']").val();
             var cellPhone = $("input[name='cellPhone']").val();
             var age = $("input[name='age']").val();
             var years = $("input[name='years']").val();
             var remark = $("textarea[name='remark']").val();
             var displayStatus = $("select[name='displayStatus']").val();
             var delStatus = $("select[name='delStatus']").val();
             if (title.length <= 0 || dutyCode.length <= 0 || salaryCode.length <= 0
                || telName.length <= 0
                || status.length <= 0
                || displayStatus.length <= 0
                || delStatus.length <= 0) {
                alert("请先完善信息!");
            return false;
        }
        if (telName.length > 20) {
            alert("姓名字数最多20个!");
            return false;
        }
        if (title.length < 2) {
            alert("标题不能少于2个字符!");
            return false;
        }
        if (title.length > 20) {
            alert("标题不能多于20个字符!");
            return false;
        }
        if (age.length > 0) {
            if (isNaN(age) || parseInt(age) < 18 || parseInt(age) > 60) {
                alert("年龄范围在18-60之间");
                return false;
            }

        }
        if (years.length > 0) {
            if (isNaN(years) || parseInt(years) < 0 || parseInt(years) > 45) {
                alert("驾龄范围在0-45之间");
                return false;
            }

        }
        if (age.length > 0 && years.length > 0 && !isNaN(age) && !isNaN(years)) {
            if (parseInt(age) < parseInt(years)) {
                alert("驾龄应该小于年龄");
                return false;
            }
        }
        if (remark.length > 60) {
            alert("备注最多填写60个汉字");
            return false;
        }
        return true;
    }
</script>
</head>

<body>
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>车辆方服务</span><span>&nbsp;&gt;&nbsp;</span><span>板车司机求职</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">编辑详情页</span>
        </h1>
        <div class="main-content">
            <!--系统设置-->
            <form id="form" action="manage/bcar/job/update" method="post">
                <input type="hidden" name="id" value="${bCar.id}">
                <input type="hidden" name="userId" value="${bCar.userId}">
                <div class="systemParameter">
                    <ul class="cf">
                        <li><label><i class="colorRed">*</i>标题：</label><input maxlength="20" name="title"
                          type="text" value="${bCar.title}"></li>
                          <li><label><i class="colorRed">*</i>司机职务：</label><select name="dutyCode">
                            <option>请选择</option>
                        </select></li>
                        <li><label><i class="colorRed">*</i>姓名：</label><input name="telName" type="text"
                          value="${bCar.telName}"></li>
                          <li><label><i class="colorRed">*</i>联系电话：</label>
                            <input name="telephone" disabled type="text" value="${bCar.telephone}">
                            <input name="telephone" type="hidden" value="${bCar.telephone}">
                        </li>
                        <li><label><i class="colorRed">*</i>工作地区：</label>
                            <input disabled class="areaInput" placeholder="省" type="text" value="${bCar.province}">
                            -<input disabled class="areaInput" placeholder="市" type="text" value="${bCar.city}">
                            -<input disabled class="areaInput" placeholder="县" type="text" value="${bCar.county}">
                            <input name="province" id="province" type="hidden" value="${bCar.province}">
                            <input name="city" id="city" type="hidden" value="${bCar.city}">
                            <input name="county" id="county" type="hidden" value="${bCar.county}">
                        </li>
                        <li><label><i class="colorRed">*</i>薪资待遇：</label><select name="salaryCode">
                            <option>请选择</option>
                        </select></li>
                        <li><label>审核状态：</label>
                            <select name="status">
                                <option value="">请选择</option>
                                <option value="3" <c:if test="${bCar.status==3 }">selected</c:if>>审核通过</option>
                                <option value="1" <c:if test="${bCar.status==1 }">selected</c:if>>审核未通过</option>
                                <option value="2" <c:if test="${bCar.status==2 }">selected</c:if>>未审核</option>
                            </select>
                        </li>

                        <li><label>展示开关：</label>
                            <select name="displayStatus" class="select126">
                                <option value="">请选择</option>
                                <option value="0" <c:if test="${bCar.displayStatus==0 }">selected</c:if>>开放</option>
                                <option value="1" <c:if test="${bCar.displayStatus==1 }">selected</c:if>>关闭</option>
                            </select>
                        </li>
                        <li><label>删除：</label>
                            <select name="delStatus" class="select126">
                                <option value="">请选择</option>
                                <option value="0" <c:if test="${bCar.delStatus==0 }">selected</c:if>>有效</option>
                                <option value="1" <c:if test="${bCar.delStatus==1 }">selected</c:if>>无效</option>
                            </select>
                        </li>
                        <li><label>驾龄：</label>
                            <input class="input180" name="years" value="${bCar.years}"></li>
                            <li><label>年龄：</label><input class="input180" name="age" value="${bCar.age}"></li>
                            <li><label>发布日期：</label><span class="pubTime"><fmt:formatDate value="${bCar.publishTime}" pattern="yyyy-MM-dd HH:mm:ss" type="both"/></span></li>
                            <li><label><i class="colorRed">*</i>发布账号：</label>
                                <input disabled class="input180" name="cellPhone" type="text" value="${bCar.cellPhone}">
                                <input class="input180" name="cellPhone" type="hidden" value="${bCar.cellPhone}">
                            </li>
                            <li><label>备注：</label><textarea maxlength="60" class="note" name="remark" cols=""
                                rows="">${bCar.remark}</textarea></li>

                            </ul>
                            <div class="divButton"><input id="updateBtn" type="button" class="button" value="修改"
                              onclick="submitData();"></div>
                          </div>
                      </form>

                  </div>
              </div>
          </body>
          </html>
