@charset "utf-8";

/* CSS Reset Document */
body,html {
  background: #fff;
}
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, button, textarea, p,
 blockquote, th, td, img, select, option, a, span, em, strong, i {
  margin: 0;
  padding: 0;
}
body, form, fieldset, input, textarea, select, option {
  font: normal 14px Arial, 'Microsoft Yahei', Verdana, Geneva, sans-serif;
  color: #333;
}
table {
  border-spacing: 0;
  border-collapse: collapse;
}
fieldset, img {
  border: 0;
}
address, caption, cite, code, dfn, em, strong, th, var {
  font-weight: normal;
  font-style: normal;
}
ol, ul, li {
  list-style: none;
}
a {
  text-decoration: none;
}
button,input {
  outline: none;
  border: none 0;
}
capation, th {
  text-align: left;
}
h1, h2, h3, h4, h5, h6 {
  font-size: 100%;
  font-weight: normal;
}
textarea {
  resize: none;
}
q:before, q:after {
  content: ' ';
}
abbr, acronym {
  border: 0;
}
input {
  border: none;
}
a,a:hover {
  text-decoration: none;
}
ol,ul,li {
  list-style: none;
}
.tc {
  text-align: center;
}
i {
  font-style: normal;
}

.cf:after {
  display: block;
  clear: both;
  content: '';
}
.cf {
  zoom: 1;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
body,html {
  min-width: 1100px;
}