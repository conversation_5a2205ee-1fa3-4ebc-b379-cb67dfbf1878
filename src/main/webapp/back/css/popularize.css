
/*
Model 2:搜索条件
*/
.searchBox {
	min-width: 1100px;
	position: relative;
	padding: 18px 0 4px;
	border: 1px solid #e7e7e7;
	background: #f7f7f7;
}
.searchUl {
	overflow: hidden;
}
.searchBox .searchUlNone {
	height: auto !important;
	overflow: inherit !important;
}


.searchUl li {
	float: left;
	width: 200px;
	display: inline-block;
	margin: 0 0 14px 16px;
	font-size: 0;
	*word-spacing: -1px;
}

.searchUl li label {
	float: left;
	font-size: 14px;
	display: inline-block;
	width: 98px;
	text-align: right;
	color: #333;
	line-height: 24px;
	font-weight: normal;
}
.searchUl li input {
	display: inline-block;
	width: 98px;
	height: 24px;
	line-height: 24px;
	border: 1px solid #ccc;
	background: #fff;
}

.searchUl li select {
	display: inline-block;
	width: 100px;
	height: 24px;
	border: 1px solid #ccc;
	background: #fff;
}

.systemParameter {
	margin-bottom: 20px;
}
.systemParameter ul li {
	margin-top: 8px;
	padding-bottom: 8px;    /*  border-bottom:1px solid #ddd;	*/
}
.systemParameter ul li label {
	font-weight: 700;
	line-height: 25px;
	display: inline-block;
	float: left; 
	width: 130px;
	height: 25px;
	text-align: right;
	vertical-align: top;
}
.systemParameter ul li label i {
	font-style: normal;
	padding-right: 5px;
	color: #f00;
}

.systemParameter ul li input {
	width: 180px;
	height: 25px;
	line-height: 25px;
	border: 1px solid #ccc;
	padding-left: 5px;
}
.button,
.systemParameter ul li input.button {
	width: auto;
	font-size: 14px;
	line-height: 32px;
	height: 32px;
	padding: 0 20px;
	color: #fff;
	border: none 0;
	border-radius: 4px;
	background: #3961b2;
	border: none;
	outline: none;
	cursor: pointer;
	text-decoration: none;
}

.contentBox {
  padding: 0 20px;
  width: 1000px;
  margin: 0 auto;
}

/*
Model 1:页面定位
*/
.titUrl {
    padding: 20px 0!important;
    margin-bottom: 20px;
    border-bottom: 1px solid #e7e7e7;
}
.titUrl span {
  font-size: 14px;
  color: #333;
}
.titUrl .currentPage {
  color: #26a2d7;
}

  /*
		表格样式
  */
.dataTable {
	min-width: 1100px;
	width: 100%;
}
.tableBox {
	width: 100%;
	border: 1px solid #e7e7e7;
}
.tableBox th {
	font-size: 14px;
	padding: 0 !important;
	text-align: center;
	color: #333;
	background: #efefef;
}
.tableBox tr {
	height: 44px;
	text-align: center;
	border-top: 1px solid transparent;
	border-bottom: 1px solid #e7e7e7;
}
.usertableBox tr {
	text-align: left;
}
.tableBox .tableTh {
	line-height: 60px;
	height: 60px;
	border-top: none 0;
	background: #efefef;
}
.tableBox tr:nth-child(2n+1) {
	background-color: #f7f7f7;
}
.tableBox tr.tableTh:hover {
	border-top: 1px solid #e7e7e7;
	border-bottom: 1px solid #e7e7e7;
	background: #e8efff;
}
.tableBox tr:hover {
	border-top: 1px solid #dde8ff;
	border-bottom: 1px solid #dde8ff;
	background: #e8efff;
}
th, td {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	border-right: 1px solid #e7e7e7;
}
/*
Model 3:搜索按钮
*/
.pageMessage {
	padding: 20px 0 0;
}

.pageMessage ul li {
	float: left;
	margin-left: 10px;
}

.searchBtn {
	display: block;
	margin-bottom: 8px;
}
.searchBtn li {
	float: left;
	margin-right: 12px;
	margin-bottom: 12px;
}

.searchBtn li input {
	font-size: 14px;
	line-height: 32px;
	height: 32px;
	padding: 0 12px;
	color: #fff;
	border: none 0;
	border-radius: 4px;
	background: #3961b2;
}
.info_detail {
	cursor: pointer;
}
.addInnerLook {
	padding: 20px 20px 0;
}

/*
	==============活动设置==============
*/
.tableHead {
	width: 400px;
	height: 34px;
	border: 1px solid #e7e7e7;
	border-radius: 10px 10px 10px 10px;
}
.tableHead li{
	width: 50%;
	line-height: 32px;
	text-align: center;
	cursor: pointer;
}
.now_act {
	border-radius: 10px 0 0 10px;
}
.hositry_body {
	padding-top: 20px;
}
.hositry_act {
	border-radius: 0 10px 10px 0;
}
.tableHead li:active,.tableHead li.active{
	background: #3961b2;
	color: #fff;
}
.now_body h2 {
	padding: 10px 20px;
	line-height: 32px;
	font-size: 14px;
	color: red;
}
.now_body .btnBox {
	height: auto;
}
.backBtn {
	background: #ccc;
}
.errorBlock {
	color:#ff3333;
	text-align: center;
	margin-top: 30px;
  height: 40px;
  line-height: 40px;
}
.addAwardList .dataTable {
  width: 670px;
  height: 270px;
  overflow-y: auto;
}
.addAwardList .errorBlock {
	margin: 0;
}
.updateBtn {
  margin-right: 40px;
}
.backBtn {
  background: #ccc;
}
.add_submit {
  width: 200px;
  margin: 0 auto;
}

.pinfoActive {
  display: inline-block;
  width: 88px!important;
}

.noAward {
  width: 860px;
  height: 200px;
  margin: 0 auto;
  border: 1px solid #000;
  text-align: center;
  line-height: 200px;
  cursor: pointer;
}
#addAward {
  height: 30px;
  line-height: 30px;
  margin-top:  -3px;
}
.addInnerLook .titleBlock {
	height: 40px;
	line-height: 40px;
	color: #ccc;
}
.addInnerLook .errorBlock {
	height: 40px;
	line-height: 40px;
}

.areaDiv {
	position: relative;
}
.noneInput {
	display: none;
}
/*解决ie7下面层级错乱,,,*/
body, html {
	*z-index: 1;
}
.twoDiv {
	*position: relative;
	*z-index: 5!important;
}
.areaDiv {
	*position: relative;
	*z-index: 10;
}
.searchCondition .input60 {
	width: 56px;
}
.messageRight {
	height: 32px;
	line-height: 32px;
	font-size: 14px;
	clear: both;
	margin-top: -32px;
}
.messageRight em {
	color: #f33;
}
.messageRight span {
	color: #333;
}
.messageRight .default_data {
	padding: 0 3px;
	color: #428bca;
}