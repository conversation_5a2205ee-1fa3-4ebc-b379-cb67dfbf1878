@charset "utf-8";
/* CSS Document */

/*登录============================*/

body, div, dl, dt, dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td,img,option{margin:0;padding:0;}
	
form,fieldset,input,textarea,select {
   font:normal 14px Arial, 'Microsoft Yahei', Verdana, Geneva, sans-serif;	
	}
	
ol,ul {list-style:none;}

.clearfix:before,.clearfix:after{ 
    content:""; 
    display:table; 
} 
.clearfix:after{clear:both;} 
.clearfix{ 
    *zoom:1;/*IE/7/6*/ 
} 

.clear {
  clear:both;	
	}

.errorBlock {
  font-size:14px;
  color:#f00;
  padding-top:5px;
  padding-left:105px;	
	}

.loginBody ul li.noMargin {
  margin-top:0;
}  

.loginBg {
  width:100%;
  height:100%;
  background:#13121f;
  position:relative; 
  overflow:hidden;
	}
	
.loginBgImg {
  width:100%;
  height:1080px;
  background-image:url(../images/login_bg.jpg);
  background-repeat:no-repeat;
  background-position:left top;
  background-attachment:fixed;
  background-size:auto;
	}
	
.loginDiv {
  width:380px;
  height:300px;
  position:fixed;
  top:44%;
  left:50%;
  margin-top:-155px;
  margin-left:-190px;
  vertical-align:middle;
	}
	
.loginHead {
  font:normal 38px/38px "Microsoft Yahei", Verdana, Geneva, sans-serif; 
  color:#fff;
  text-align:center;	
  margin-bottom: 24px;
	}
	
.loginBody {
  background:#fff;
  padding:30px 34px;
	}

 .loginBody ul li {
 position:relative;
  margin-top:14px;
  } 

.loginBody ul li label {
  width:105px;
  height: 119px;
  background:url(../images/login_icon.png) no-repeat;
  text-indent: -9999px;

}
	
.loginBody ul li .phoneNumber,
.loginBody ul li .password,
.loginBody ul li .verifiCode {
  float:left;
  text-indent:-9999px; 
  direction: ltr;
 }

.loginBody ul li .phoneNumberDefault{
  width: 18px;
  height: 20px;
  background-position: -14px -12px;
  position: absolute;
  left: 10px;
  top: 9px;
 }
 .loginBody ul li .phoneNumberFocus{
  width: 18px;
  height: 20px;
  background-position: -40px -12px;
  position: absolute;
  left: 10px;
  top: 9px;
 }
	
.loginBody ul li .passwordDefault{
  width: 18px;
  height: 22px;
  background-position: -14px -43px;
  position: absolute;
  left: 10px;
  top: 9px;
	}

.loginBody ul li .passwordFocus{
  width: 18px;
  height: 22px;
  background-position: -40px -43px;
  position: absolute;
  left: 10px;
  top: 9px;
  }
	
.loginBody ul li .verifiCodeDefault{
  width: 18px;
  height: 18px;
  background-position: -13px -80px;
  position: absolute;
  left: 10px;
  top: 11px;
	}

  .loginBody ul li .verifiCodeFocus{
  width: 18px;
  height: 18px;
  background-position: -40px -80px;
  position: absolute;
  left: 10px;
  top: 11px;
  }
	
.loginBody ul li .phoneNumberInput,
.loginBody ul li .passwordInput{
  width: 260px;
  float:left;
  height:38px;
  line-height:38px;
  border:1px solid #e5e5e5;
  padding-left: 50px;
 }
	
.loginBody ul li .verifiCodeInput {
  float:left;
  width: 157px;
  height:38px;
  line-height:38px;
  border:1px solid #e5e5e5;
  padding-left: 50px;
 }
	
.loginBody ul li .verifiCodeImg {
  margin-left: 7px;
  width: 94px;
  display: inline-block;
  height: 38px;
  border: 1px solid #ccc;
 }

.loginBody ul li .changeOne {
  float:left;
  cursor:pointer;
	}
	
.checkbox::-moz-placeholder {
  color: #999;
  opacity: 1;
}

:-ms-input-placeholder{color:#999;}  

.checkbox::-webkit-input-placeholder {
  color: #999;
}
	
.loginBody ul li input[type="checkbox"] {
  width:13px;
  height:13px;
  background:#fff;
  float:left;
  margin:2px 0 0 35px;
  border:none;
  padding:0;	
	}
	
.loginBody ul li .rememberMe {
  float:left;
  font:normal 12px "Microsoft Yahei", Verdana, Geneva, sans-serif;
  color:#383838;
  margin-left:5px;
	}
	
.loginBody .buttonsDiv {
  margin:14px auto 0;	
	}
	
.loginBody .buttonsDiv .loginBtn,
.loginBody .buttonsDiv .resetBtn {
  width:148px;
  height:38px;
  line-height:38px;
  display:inline-block;
  border:none;
  font:normal 16px "Microsoft Yahei", Verdana, Geneva, sans-serif;
  color:#fff;
  border-radius:2px;
  cursor:pointer;
  outline:none;	
  text-align:center;
	}
	
.loginBody .buttonsDiv .loginBtn {
  background:#1961b2;
  float: left;
	}
	
.loginBody .buttonsDiv .resetBtn {
  background:#f6884c;
  float:right;
	}
	
.loginBottom {
  width:442px;
  /* height:3px; */
  margin:0 auto;
  background: url(../images/hr.png) no-repeat bottom left;
  font:normal 12px "Microsoft Yahei", Verdana, Geneva, sans-serif;
  color:#fff;
  position:fixed;
  bottom:5%;
  left:50%;
  margin-left: -221px;
  padding-bottom: 20px;
 }

.loginBottomP{
  width: 442px;
  margin:0 auto;
  font-size: 16px;
  text-align: center;
 }