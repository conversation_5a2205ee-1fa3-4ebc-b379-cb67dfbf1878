.systemParameter {
	margin-bottom: 20px;
}
.systemParameter ul li {
	margin-top: 8px;
	padding-bottom: 8px;    /*  border-bottom:1px solid #ddd;	*/
}
.systemParameter ul li label {
	font-weight: 700;
	line-height: 25px;
	display: inline-block;
	float: left; 
	width: 130px;
	height: 25px;
	text-align: right;
	vertical-align: top;
}
.systemParameter ul li label i {
	font-style: normal;
	padding-right: 5px;
	color: #f00;
}

.systemParameter ul li input {
	width: 180px;
	height: 25px;
	line-height: 25px;
	border: 1px solid #ccc;
	padding-left: 5px;
}
.systemParameter .popularize_check label,
.systemParameter .popularize_check input,
.systemParameter .popularize_check span,
.systemParameter .allArea label,
.systemParameter .allArea input,
.systemParameter .allArea span {
  vertical-align: middle;
}
.systemParameter .allArea {
  margin-right: 20px;
}
.systemParameter .popularize_check input,
.systemParameter .allArea input,
.systemParameter .areaDiv .area_radio {
  width: auto;
  margin: 0 6px 0 0; 
}
.systemParameter .areaDiv .area_radio {
  vertical-align: middle;
  display: inline-block;
  width: 14px;
  height: 14px;
}
.systemParameter .area_radio {
  display: inline-block;
  width: 14px!important;
  cursor: pointer;
}
.systemParameter .popularize_row {
  margin-left: 130px;
}
.systemParameter .popularize_row input {
  width: 50px;
  margin: 0 6px;
  text-align: center;
  padding-right: 5px;
}
.systemParameter .popularize_row span {
  margin-right: 6px;
}

.systemParameter ul li input.areaInput{
	width: 90px;
	height: 25px;
	line-height: 25px;
	border: 1px solid #ccc;
	padding-left: 5px;
}
.systemParameter ul li select.areaSelect{
	width: 97px;
	height: 25px;
	line-height: 25px;
	border: 1px solid #ccc;
	padding-left: 5px;
}


.systemParameter ul li .pubTime,
.systemParameter ul li .lineHeight25 {
	height: 25px;
	line-height: 25px;
}

.systemParameter ul li select {
	width: 180px;
	height: 25px;
	line-height: 25px;
	border: 1px solid #ccc;
}
.systemParameter ul li .note {
	width: 300px;
	height: 120px;
	padding: 5px;
	resize: none;
	border: 1px solid #ccc;
}
.systemParameter ul li .bigImg {
	display: inline-block;
	margin-left: 40px;
}
.textarea {
	padding: 5px;
	resize: none;
	border: 1px solid #ccc;
}
.systemParameter .divButton {
	margin: 20px 0 0 130px;
}
.systemParameter .divButton .button {
	margin-right: 20px;
}
#backBtn {
  background: #ccc;
}
.redBg {
	background: #f00;
}
.systemParameter ul li .sfzImg {
	float: left;
	width: 187px;
	min-height: 25px;
}

.systemParameter ul li .remarks {
	margin-left: 5px;
	color:#666;
}

.systemParameter ul li.merchantBusinessLi input,
.systemParameter ul li.finalOpinion input {
 	width: auto;
 	padding-left: 0;
 	vertical-align: middle;
 	margin:0 2px 0 6px;
}

.systemParameter ul li.merchantBusinessLi .merchantBusinessSpan {
	width:737px;
	float: left;
}

.systemParameter ul li .merchatImage input,
.systemParameter ul li.merchantStatus input{
	width: auto;
	vertical-align: middle;
	margin-top: 0;
}

.systemParameter ul li.merchantStatus input{
   margin-left:8px;
}

.systemParameter ul li .maintainerMap {
	float: left;
	margin-top: 10px;
}

.errorSpan {
	text-align: left;
	margin: 20px 0 20px 130px;
}
.errorTold {
  height: 20px;
  line-height: 20px;
  text-align: left;
  margin: 20px 0 20px 130px;
  color: red;
  font-size: 14px;
}

.systemParameter ul li .suggestTion {
  float: left;
  line-height: 25px;
}

.systemParameter ul li .suggestTion input{
	width:60px;
	margin-right: 5px;
}

.systemParameter ul li.processingContent em {
	display: block;
	font-weight: 700;
	font-size: 16px;
}

.upCont {
  width: 100%;
  padding-bottom: 20px;
  border-bottom: 1px solid #ccc;
}
.upCont em {
  height: 25px;
  line-height: 25px;
  width: 100%;
  padding-left: 56px;
  vertical-align: top;
}
.upCont .note {
  display: block;
  width: 500px;
  height: 180px;
  border: 1px solid #ccc;
  resize: none;
  padding: 10px;
  margin: 20px 0 0  56px;
}
.upCont span {
  display: inline-block;
  width: 80%;
  line-height: 25px;
  vertical-align: middle;
  word-wrap: break-word;
}
.pageContext {
  padding: 20px 0;
}
.pageContext h2 {
  font-size: 16px;
  font-weight: bold;
  margin-left: 50px;
}
.contBox {
  margin: 20px 0;
}
.contAbout {
}
.contTit {
  width: 116px;
}
.contAbout .processingLog {
  width: 100%;
  height: 25px;
  line-height: 25px;
}
.contAbout div span {
  margin-right: 30px;
  font-size: 14px;
}
.dataCont {
  display: inline-block;
  width: 120px;
  overflow: hidden;
}
.textCont {
  display: inline-block;
  width: 300px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.nowlb {
  display: inline-block;
  width: 130px;
  vertical-align: top;
  text-align: right;
  line-height: 25px;
}
.pageContext .note {
  width: 60%;
  height: 80px;
  border: 1px solid #ccc;
  vertical-align: middle;
  resize: none;
  padding: 10px;
  margin-right: 26px;
}
.xing {
  width: 12px;
  color: #eb3030;
  vertical-align: middle;
  height: 21px;
  line-height: 24px;
  font-size: 14px;
  text-align: center;
  margin-left: -12px;
  display: inline-block;
  font-style: normal;
}
.suggestBox {
  margin: 10px 0;
}
.suggestBox .labelrad {
  margin-right: 26px;
  vertical-align: middle;
  cursor: pointer;
}
.suggestBox .labelrad input {
  cursor: pointer;
}
.suggestBox  input {
  vertical-align: middle;
  margin: 0;
  margin-right: 6px;
}
.suggestcheck, .suggestBox {
  height: 36px;
  line-height: 36px;
}
.suggestcheck input {
  width: 54px;
  height: 36px;
  line-height: 36px;
  border: 1px solid #ccc;
  margin: 0 26px 0 6px;
}
.error {
  color: red;
  vertical-align: middle;
}

/*=====================样式合并======================*/
/*
  何钰焱
*/
.img_scope_box img {
  margin: -25px auto 0;
  display: block;
}
.img_scope_box h2 {
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  color: #333;
  background: #efefef;
}
.img_scope {
  width: 100%;
  margin:0 auto 50px;
  overflow-y: auto;
}

.employeeOutbox li {
  line-height: 40px;
  color: #333;
}
.employeeOutbox li label {
  display: inline-block;
  width: 120px;
  line-height: 40px;
  text-align: right;
  font-weight: 700;
  color: #333;
}
.employeeOutbox li input,
.employeeOutbox li select {
  color: #333;
}
.employeeOutbox .pro_city_coun {
  width: 100px;
  height: 26px;
  line-height: 26px;
  padding-left: 6px;
  border: 1px solid #ccc;
}
.cursor_none {
  cursor: no-drop;
}
.employeeOutbox li img {
  margin-right: 6px;
}
.employeeOutbox li select {
  width: 100px;
  height: 26px;
  line-height: 26px;
  padding-left: 6px;
  border: 1px solid #ccc;
}
.employeeMsgbox {
  height: 260px;
  margin: 40px 0;
}
.employeeMsgbox span {
  display: block;
  height: 80px;
  width: 100%;
  text-align: center;
  font-size: 14px;
  color: red;
}
.employeeMsgbox input {
  margin: 0 auto;
  display: block;
  font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding: 0 12px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
  cursor: pointer;
}
.outUploadimg {
  margin-left: 120px;

}
.innerUploadimg {
  width: 130px;
  margin-right: 20px;
}
.innerUploadimg img {
  display: block;
  margin: 0 auto;
}
.innerUploadimg a {
  display: block;
  font-size: 14px;
  text-align: center;
  line-height: 24px;
  color: #428bca;
}
/*
  车辆认证管理-详情
*/
.detailBox .detailUlcar label {
  width: 100px;
}
.detailBox .detailUlcar input {
  width: 102px;
}
.detailBox .detailUlcar select {
  width: 110px;
}
.detailBox .detailUlcar span {
  width: 120px;
}
.detailBox .detailUlcar font {
  width: 10px;
  height: 10px;
  display: inline-block;
  color: red;
  vertical-align: middle;
  font-size: 14px;
  margin-left: 4px;
}
.detailUlimg li {
  width: 500px;
  float: left;
}
.detailUlimg li h3 {
  // line-height: 40px;
  padding: 10px 0;
  text-align: center;
}
.detailUlimg  li h3 span {
    display: inline-block;
    height: 20px;
    line-height: 20px;
  vertical-align: middle;
  margin-right: 6px;
}
.detailUlimg li h3 i {    
  width: 10px;
    height:20px;
    line-height: 26px;
    display: inline-block;
    color: red;
    vertical-align: middle;
    font-size: 14px;
    margin-left: 4px;
}
.detailUlimg li img {
  display: block;
  width: 400px;
  height: 400px;
  margin: 0 auto;
}
.detailUlimg li .headDrive {
  width: 500px;
}
.rotateBtn  {
  margin: 0 auto;
  width: 130px;
  height: 40px;
  line-height: 40px;
}
.rotateBtn a {
  font-size: 14px;
  color: #428bca;
  vertical-align: middle;
}
.rotateBtn span {
  vertical-align: middle;
  color: #428bca;
  margin: 0 6px;
}
.fileBtn {
  position: relative;
  width: 100%;
  height: 32px;
  margin: 20px 0;
}
.fileBtn input,.fileBtn span {
  position: absolute;
  left: 50%;
  top: 50%;
  display: block;
  font-size: 14px;
  width: 80px;
    line-height: 32px;
    height: 32px;
    margin: -16px 0 0 -40px;
    color: #fff;
    border: none 0;
    border-radius: 4px;
    background: #3961b2;
  cursor: pointer;
}
.fileBtn input {
  z-index: 50;
  opacity: 0;
  filter: alpha(opacity=0);
}
.fileBtn span {
  text-align: center;
  z-index: 20;
}
.driveMessage {
  width: 1000px;
  line-height: 40px;
  text-align: center;
  color: red;
  font-size: 16px;
}
.innerBtn .btnTrun {
  display: block;
  width: 80px;
  height: 32px;
  line-height: 32px;
  border-radius: 6px;
  cursor: pointer;
}
.carbtnBox {
  width: 100%;
  height: 150px;

}
#errorSpanId {
  display: block;
  text-align: center;
  line-height: 40px;
  height: 40px;
  color: red;
  font-size: 14px;
}
.innercarBtn {
  width: 240px;
  margin: 0 auto;
}
.innercarBtn input,.innercarBtn a {
  display: inline-block;
  padding: 0 12px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 14px;
  color: #fff;
  border-radius: 6px;
  background: #3961b2;
  cursor: pointer;
}
.innercarBtn input {
  margin-right: 10px;
}

/* 违约/编辑=============================== */

.systemParameter .dataContP {
  padding:2px 0;
}
.pinfoActive {
  display: inline-block;
  width: 88px!important;
}