/*ready this dome if this page have detailPages.*/


.contentBox {
	padding: 0 20px;
	width: 1000px;
	margin: 0 auto;
}

/*
Model 1:页面定位
*/
.titUrl {
    padding: 20px 0!important;
    margin-bottom: 20px;
    border-bottom: 1px solid #e7e7e7;
}
.titUrl span {
	font-size: 14px;
	color: #333;
}
.titUrl .currentPage {
	color: #26a2d7;
}

/*
Model 2:内容板块
*/
.detailBox {
	width: 1000px;
	height: auto;
	border: 1px solid #e7e7e7;
	margin-bottom: 20px;
}
.detailTit {
	height: 46px;
	line-height: 46px;
	padding-left: 20px;
	background: #f7f7f7;
	font-size: 14px;
	color: #333;	
	font-weight: 700;
}
.detailUl {
	padding-top: 20px;

}
.detailUl li {
	width: 228px;
	float: left;
	display: inline;
	margin: 0 0 20px 16px;
	font-size: 0;
	*word-spacing: -1px;
}
.detailUl label {
	display: inline-block;
	width: 80px;
	height: 24px;
	line-height: 24px;
	vertical-align: middle;
	font-size: 14px;
	color: #333;
}
.detailUl input,.detailUl select,.detailUl span {
	display: inline-block;
	width: 130px;
	vertical-align: middle;
	font-size: 14px;
	color: #333;
}
.detailUl select {
	height: 24px;
	line-height: 24px;
}
.detailUl span {
	height: 24px;
	line-height: 24px;
}
.detailUl input {
	width: 122px;
	height: 20px;
	line-height: 22px;
	padding-left: 6px;
	border: 1px solid #ccc;
	background: #fff;
	font-size: 14px;
	color: #666;
}
.detailUl span {
	width: 145px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.detailBox .dataJiao label {
	width: 84px;
}
.detailBox .dataJiao span {
	width: 140px;
}
.mustRed {
    font-size: 14px;
    margin-left: 6px;
    vertical-align: middle;
    color: red;
}

.checkboxUl h3 {
	height: 16px;
	line-height: 16px;
	margin-left: 16px;
	display: block;
	width: 180px;
	font-size: 14px;
	color: #333;
}
.checkboxUl .contCheck {
	margin-bottom: 20px;
	height: 16px;
	line-height: 16px;
}
.checkboxUl .contCheck h3,.checkboxUl .contCheck em,.checkboxUl .contCheck span {
	font-size: 14px;
	color: #333;
	height: 16px;
	line-height: 16px;
}
.checkConfig {
	width: 800px;
}
.checkConfig label {
	margin-bottom: 20px;
	height: 16px;
	line-height: 16px;
	display: inline-block;
	width: 100px;
	margin-right: 16px;
}
.checkConfig input {
	display: inline-block;
	width: 16px;
	height: 16px;
	margin-right: 6px;
	border: 1px solid #ccc; 
	vertical-align: middle;
}
.checkConfig em {
	font-style: normal;
	font-size: 14px;
	vertical-align: middle;
}

.btnBox {
	position: relative;
	width: 1000px;
	height: 120px;
}
.btnBox span {
	position: absolute;
	width: 180px;
	height: 30px;
	left:50%;
	top: 50%;
	height: 30px;
	line-height: 30px;
	color: red;
	font-size: 14px;
	margin: -15px 0 0 -224px;
}
.innerBtn {
	position: absolute;
	left: 50%;
	top: 50%;
	width: 180px;
	height: 30px;
	margin: -15px 0 0 -90px;
}
.innerBtn button {
	border: none 0;
	outline: none;
	display: inline-block;
	width: 80px;
	height: 30px;
	font-size: 14px;
	border-radius: 5px; 
	cursor: pointer;
}
.innerBtn .btnTrun {
	margin-right: 20px;
	background: #3961b2;
	color: #fff;
}
.innerBtn .btnClose {
	background: #e7e7e7;
	color: #666;
}
.btnBox .innerOneBtn {
	width: 80px;
	height: 30px;
	margin: -15px 0 0 -40px;
}


.areaDiv {
	position: relative;
}
.noneInput {
	display: none;
}
/*解决ie7下面层级错乱,,,*/
body, html {
	*z-index: 1;
}
.twoDiv {
	*position: relative;
	*z-index: 5!important;
}
.areaDiv {
	*position: relative;
	*z-index: 10;
}
.searchCondition .input60 {
	width: 56px;
}


/* 核心资料管理=================== */
.coreDataManagement h2 {
	font-size: 18px;
	width: 100%;
	height: 40px;
	line-height: 40px;
	color: #333;
	background: #efefef;
	text-indent: 2em;
}
.coreDataManagement ul li {
	width:320px;
	float: left;
	margin-top:16px;
}

.coreDataManagement ul li label {
	width:85px;
	text-align:right;
}

.coreDataManagement ul li .sec_input,
.coreDataManagement ul li .input120,
.coreDataManagement ul li .sec_select {
	width:118px;
	height: 24px;
	line-height: 24px;
	border: 1px solid #ccc;
}
.coreDataManagement ul li .sec_select180,
.coreDataManagement ul li .sec_input180 {
	width: 180px;
}

.coreDataManagement ul li .sec_input,
.coreDataManagement ul li .input120 {
	padding-left: 5px;
}

.button,
.systemParameter ul li input.button {
	width: auto;
	font-size: 14px;
	line-height: 32px;
	height: 32px;
	padding: 0 20px;
	color: #fff;
	border: none 0;
	border-radius: 4px;
	background: #3961b2;
	border: none;
	outline: none;
	cursor: pointer;
	text-decoration: none;
}

.default {
	background: #e7e7e7;
	color: #666;
}

.mt60 {
	margin-top:60px;
}

.ml15 {
	margin-left:15px;
}

.grayColor {
	color:#666;
}

.errorBlock {
	color:#ff3333;
	text-align: center;
	margin-top: 30px;
  height: 40px;
  line-height: 40px;
}
.errorBlockNomal {
	text-align: inherit;
	margin-left: 120px;
}
.labelWidth {
	overflow: hidden;
}

.imgPreview {
	display:block;
	margin-top: 20px;
}

.detailUl input.w_32 {
	width: 32%;
}

.detailUl input.w_66 {
	width: 66%;
}

.detailUl li.w_100,
.detailUl li table.w_100 {
	width: 100%;
}

.detail_textarea {
	width: 100%;
	margin-top: 16px;
}
.detail_textarea label {
	width: 88px;
}
.detail_textarea textarea {
	width: 600px;
	height: 160px;
	outline: none;
}
.Management_abtn {
    width: 100px;
    height: 32px;
    display: block;
    margin: 0 auto;
	color: #fff;
	background: #3961b2;
}
.Management_abtn:hover {
	color: #fff;
	background: #3961b2;
}


/*
	APP-消息管理 > 通知栏推送消息
*/
  .titBlackred {
    line-height: 30px;
    margin-left: 90px;
    color:#428bca;
    font-size: 14px;
  }
  .nom_coreDataManagement .sec_select {
    width: 150px!important;
  }
  .nom_coreDataManagement .sec_input {
    width: 148px!important;
  }
  .nom_coreDataManagement ul li {
  	width: 800px;
    float: initial;
  }
  .detail_textarea  label {
    text-align: right;
    vertical-align: top;
  }
  .nom_coreDataManagement .mustRed {
  	margin-right: 4px;
  }
  .nom_coreDataManagement .detail_textarea textarea {
	width: 260px;
	height: 100px;
  }
  .nom_coreDataManagement .nom_text {
  	text-align: left;
  	margin: 30px 0 60px 90px;
  }

  .pb30 {
  	padding-bottom: 30px;
  }


  /*
		用户反馈汇总管理 > 用户意见反馈 > 编辑
  */
.ideaCont {
	width: 700px;
}
.whiteIdea {
	float: left;
	width: 700px;
	line-height: 25px;
}
.ideaFinal li label,.ideaFinal li span {
	float: left;
	line-height: 25px;
}
.ideaFinal li span {
	width: 700px;    
	word-break: break-all;
}
.finalOpinion em {
	display: inline-block;
	width: 60px;
	line-height: 25px;
	vertical-align: middle;
}
.finalOpinion em label,.finalOpinion em input {
	cursor: pointer;
}
.finalOpinion em label {
	font-weight: normal!important;
	width: auto!important;
}
.backSuggest input {
	width: 300px!important;
	margin-left: 0!important;
}
