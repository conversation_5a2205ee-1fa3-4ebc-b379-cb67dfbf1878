<!-- 提示框 -->
<div class="alert alert-danger fade in" id="alert_danger" role="alert" style="z-index:1004;display: none">
      <button type="button" class="close" aria-label="Close" id="close_btn">×</button>
      <h4>提示</h4>
      <p class="alertContent">此用户已认证通过，无需上传</p>
      <div class="tc buttonBox"> 
        <input type="button" class="button" id="btn_default" value="我知道了">
      </div>
</div>

<!-- 选择用户注册身份 -->
<div class="alert alert-default fade in" id="alert_default" role="alert" style="z-index:1004;display: none">
      <button type="button" class="close" aria-label="Close" id="close_identity_btn">×</button>
      <h4>请先选中用户注册身份</h4>
      <div id="shenfenDiv" class="shenfenDiv">

      <!--  <div class="identityDiv">
      <p class="identity-label">车辆方：</p>
        <ul class="fl">
          <li><span class="btn btn-default">个人车主</span></li>
          <li><span class="btn btn-default">运输公司或车队</span></li>
          <li><span class="btn btn-default">板车司机</span></li>
          <li><span class="btn btn-default">板车服务</span></li>           
        </ul>
        <div class="clearfix"></div>
      </div>  --> 
      
      </div>         
      <div class="tc buttonBox">    
        <button type="button" class="grayButton" id="close_upload_btn">关闭</button>  
        <button type="button" class="button" id="upload_auth_btn">上传认证</button>        
      </div>
      <p class="error-block" id="warningTipId">注：此用户已上传过用户信息，如果进行此操作，将会将用户信息覆盖</p>
</div>


<div class="outLook">
      <div class="innerLook">
        <h2>何钰焱</h2>
        <div class="searchLook cf">
          <input class="fl" type="text" placeholder="搜索内容">
          <button class="fr">搜索</button>
        </div>
        <p class="errorMsg">错了错了全错了</p>
      </div>
      <i class="closelookBtn">×</i>
    </div>


<!-- 弹出框js -->
<!--透明层-->
<div class="Uptransparentbox" style="display: none;"></div>
<script>

$(function(){
$("#upload_auth_btn").click(function(){
	addIdentity();
});	

$('.identity-authen1').on('click', function(event) {
  event.preventDefault();
   /*Act on the event */
//  $('#alert_danger').show();
 $(".transparentbox,#alert_danger").show();
});

// 点击取消和关闭按钮

$('#close_btn, #btn_default').on('click', function(event) {
  event.preventDefault();
  /* Act on the event */
//  $('#alert_danger').fadeOut('fast'); 
 $(".transparentbox,#alert_danger").hide();
});

//选择用户身份 
$('.identity-authen2').on('click', function(event) {
  event.preventDefault();
  /* Act on the event */
  $('#alert_default').show();

});

$('#close_identity_btn, #close_upload_btn').on('click', function(event) {
  event.preventDefault();
  /* Act on the event */
//  $('#alert_default').fadeOut('fast');
 $(".transparentbox,#alert_default").hide();
});

});

//改变选中状态函数
function changeSpanSelected(obj){
	var currentClassName=obj.className;
	if(currentClassName.indexOf("selected")>0){
		obj.className=currentClassName.replace("selected","");
	}else{
		$("#shenfenDiv ul li span").attr("class","btn btn-default");
		obj.className=currentClassName+" selected";
	}
}

</script>