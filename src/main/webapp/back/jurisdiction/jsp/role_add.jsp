<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    String path = request.getContextPath();
    String basePath = "https://" + request.getServerName() + path + "/";
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>角色添加</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="back/model/css/base.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/theme.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/font-awesome.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/detailPages.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/page.css" type="text/css" rel="stylesheet">
    <link href="back/jurisdiction/jsp/css/roleAdd.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="back/model/js/bootstrap.js"></script>
</head>

<body class="theme-blue">
<div class="content" style="padding-top: 0;">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>企业员工管理</span><span>&nbsp;&gt;&nbsp;</span><span>角色管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">角色添加</span>
    </h1>
    <div class="main-content">
        <div class="systemParameter">
            <ul>
                <li><label><i class="colorRed">*</i>角色名称：
                </label><input maxlength="20" class="input180" name="roleName"
                               type="text" value="${role.roleName}"><span id="ne" class="colorRed"
                                                                          style="display: none"> 该角色名称已存在！</span>
                </li>
                <li><label><i class="colorRed">*</i>排序：
                </label><input maxlength="20" class="input180" name="sort"
                           type="text" value="${role.sort}">
                </li>
                <li><label>拥有所有权限：
                </label>
                    <span class="radioOridinal off firstRadio">
                    	<input id="yes" type="radio" class="radioclass" name="isAll" value="1">是
                    </span>
                    <span class="radioOridinal on">
                    	<input id="no" type="radio" class="radioclass" name="isAll" checked value="0">否
                    </span>
                </li>
            </ul>
        </div>
        <div id="jurisdiction" class="main-content quanxian" style="border-top: 1px solid #ccc;">
            <div id="jurisdiction_left">
            	<ul></ul>
            </div>
            <!-- 右侧div,所有左侧菜单对应的内容，根据左侧所选菜单项动态显示对应div -->
            <div id="jurisdiction_right">
            </div>
        </div>
    </div>
    <div class="submitBtn" ><input id="btn" type="button" class="button" value="提交"
                                  onclick="submitData();"></div>

</div>
</body>
<script type="text/javascript" src="back/jurisdiction/jsp/js/roleAdd.js"></script>
</html>
