<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    String path = request.getContextPath();
    String basePath = "https://" + request.getServerName() + path + "/";
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>机构修改</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/detailPages.css" type="text/css" rel="stylesheet">

    <script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="back/model/js/bootstrap.js"></script>
</head>

<body class="theme-blue">
    <div class="contentBox">
        <h1 class="titUrl">
            <span>当前所在位置：</span><span>企业员工管理</span><span>&nbsp;&gt;&nbsp;</span><span>机构管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">机构修改</span>
        </h1>
        <form id="form">
    	<input type="hidden" id="upperInstitutionId" value="${institution.pid }">
    	<input type="hidden" id="institutionNameId" value="${institution.institutionName}">
    	<input type="hidden" id="institutionNoId" value="${institution.institutionNo}">
        <input id="id" name="id" type="hidden" value="${institution.id}">
        <div class="main-content">
            <div class="coreDataManagement">
                <ul class="cf">
                    <li><label>机构名称：
                    </label><input maxlength="20" class="sec_input sec_input180" name="institutionName"
                                   type="text" value="${institution.institutionName}"><i class="mustRed">*</i>
                    </li>

                    <li><label>机构编码：
                    </label><input maxlength="20" class="sec_input sec_input180" name="institutionNo"
                                   type="text" value="${institution.institutionNo}" placeholder="请输入三位不重复的正整数"><i class="mustRed">*</i>
                    </li>

                    <li><label>上级机构：</label><select name="pid" class="sec_select sec_select180">
                        <option value="0">无上级机构</option>
                    </select><i class="mustRed">*</i></li>

                </ul>
                <div class="clear"></div>

            </div>
        </div>
        <p class="errorBlock helpText" id="errorMsgSpan"></p>
        <div class="tc mt60"><input id="btn" type="button" class="button" value="提交"
                                      onclick="submitData();"></div>
    </form>
</div>
</body>


<script type="text/javascript">
function errorMsg(msg){
	$("#errorMsgSpan").html(msg).show();
}
    function getAllInstitution() {
        $.ajax({
            type: "get",
            url: '<%=request.getContextPath()%>/jurisdiction/institution/ajx_queryOtherInstitution',
            async: false,
            data: {institutionId: $("#id").val()},
            dataType: "json",
            success: function (data) {
                $("#institution").empty();
                if (data != "0") {
                    for (var i = 0; i < data.length; i++) {
                    	if ($('#upperInstitutionId').val() == data[i][0]) {
                            $("select[name='pid']").append("<option selected value='" + data[i][0] + "'>" + data[i][1] + "</option>");
                    	} else {
	                        $("select[name='pid']").append("<option value='" + data[i][0] + "'>" + data[i][1] + "</option>");
                    	}
                    }
                }
            },
            error: function (XHR, textStatus, errorThrown) {
                alert("网络有点慢，请重试！");
            }
        });

    }
    getAllInstitution();


    function submitData() {
        var institutionName = $("input[name='institutionName']").val(),
        	institutionNo = $("input[name='institutionNo']").val(),
            pid = $("select[name='pid']").val(),
            id = $("input[name='id']").val(),
            institutionNameOld = $("#institutionNameId").val();

        if (validateForm(institutionName, institutionNo)) {
            if (confirm("您确认修改该条机构信息吗?")) {
                $("#btn").unbind("click");
                $.ajax({
                    type: "POST",
                    url: "<%=request.getContextPath()%>/jurisdiction/institution/update",
                    data: $('#form').serialize(),
                    dataType: "json",
                    success: function (data) {
                        if (data.code == 400 || data.code == 600) {
                            if (confirm(data.msg)) {
                                window.close();
                                window.opener.location.reload();
                            } else {
                                $("#btn").bind("click", submitData);
                            }
                        } else {
                            alert(data.msg);
                            window.close();
                            window.opener.location.reload();
                        }
                    },
                    error: function (xhr, status) {
                        alert("error");
                    }
                });
            }

        }

    }
    
    var institutionNameFlag=true;
    var institutionNoFlag=true;
    //表单验证
    function validateForm(institutionName, institutionNo) {
    	errorMsg("");
        if (institutionName.length <= 0){
        	errorMsg("机构名称不能为空！");
        	return false;
        }
        if (institutionNo.length !=4||isNaN(institutionNo)){
        	errorMsg("机构编码只能是四位数的正整数，且不能重复！");
        	return false;
        }
        if(!institutionNameFlag){
        	errorMsg("机构名称已经存在！");
        	institutionNameFlag=false;
        }
        if(!institutionNoFlag){
        	errorMsg("机构编码只能是四位数的正整数，且不能重复！");
        	return false;
        }

        return true;
    }

    $(document).ready(function () {
        var institutionName = $("input[name='institutionName']");
        var institutionNo = $("input[name='institutionNo']");
        var institutionNameOld = $('#institutionNameId').val();
        var institutionNoOld = $('#institutionNoId').val()


        institutionName.blur(function () {
            if (institutionName.val() != null && institutionName.val().trim().length > 0) {
                $.ajax({
                    type: "POST",
                    url: '<%=request.getContextPath()%>/jurisdiction/institution/ajx_queryInstitutionByInstitutionName',
                    async: false,
                    data: {institutionName: institutionName.val()},
                    dataType: "json",
                    success: function (data) {
                        if (data.length > 0 && $('#institutionNameId').val().trim() != institutionName) {
                        	errorMsg("机构名称已经存在！");
                        	institutionNameFlag=false;
                        } else {
                        	errorMsg("");
                        	institutionNameFlag=true;
                        }
                    },
                    error: function (XHR, textStatus, errorThrown) {
                        alert("网络有点慢，请重试！");
                        institutionNameFlag=false;
                    }
                });
            } else {
            	errorMsg("机构名称不能为空");
            	institutionNameFlag=false;
            }
        });

        institutionNo.blur(function () {
            if (institutionNo.val() != null && institutionNo.val().trim().length > 0) {
                $.ajax({
                    type: "POST",
                    url: '<%=request.getContextPath()%>/jurisdiction/institution/ajx_queryInstitutionByInstitutionNo',
                    async: false,
                    data: {institutionNo: institutionNo.val()},
                    dataType: "json",
                    success: function (data) {
                        if ($('#institutionNoId').val() !=institutionNo.val().trim() && data.length > 0) {
                        	errorMsg("机构编码只能是四位数的正整数，且不能重复！");
                        	institutionNoFlag=false;
                        } else {
                        	var regex = /^\d{4}$/;
                        	if (!regex.test(institutionNo.val().trim())) {
                        		errorMsg("机构编码只能是四位数的正整数，且不能重复！");
                        		institutionNoFlag=false;
                        	} else {
                        		errorMsg("");
                        		institutionNoFlag=true;
                        	}
                        }
                    },
                    error: function (XHR, textStatus, errorThrown) {
                        alert("网络有点慢，请重试！");
                        institutionNoFlag=false;
                    }
                });
            } else {
            	errorMsg("机构编码只能是四位数的正整数，且不能重复！");
            	institutionNoFlag=false;
            }
        });

    });


</script>

</html>
