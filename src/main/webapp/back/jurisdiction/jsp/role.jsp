<%@ page import="java.util.Map" %>
<%@ page import="com.tyt.jurisdiction.bean.JurisdictionQueryBean" %><%--15011400208
  Created by IntelliJ IDEA.
  User: douge
  Date: 16-7-11
  Time: 下午6:21
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
    String path = request.getContextPath();
    String basePath = "https://" + request.getServerName() + path + "/";

    Map<Long, JurisdictionQueryBean> jurisdictionMap = (Map<Long, JurisdictionQueryBean>) session.getAttribute("jurisdictions");
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>角色列表</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">
    

    <script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="back/model/js/bootstrap.js"></script>
    <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
    <script type="text/javascript">
    function toAddRole(url){
    	openWin2(url);
    	//window.open(url,'newwindow','width=1200,height=1000,top=0,left=90,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,status=no');
    }
    </script>
</head>

<body>

<div class="contentBox">
    <div class="titUrl cf">
        <h1 class=" fl">
            <span>当前所在位置：</span><span>角色管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">角色列表</span>
        </h1>
    </div>
    <div class="main-content">

        <!--搜索条件-->
        <form action="jurisdiction/role/list" method="post">
            <input type="hidden" value="${pageNo}" name="pageNo">
            <input type="hidden" name="menuId" value="${curMenu.id }">
                <div class="searchBox" id="searchCondition">
                    <ul class="searchUl searchUlNone cf">
                        <li><label>ID：</label><input class="input126" name="id" type="text" value="${role.id}"></li>
                        <li><label>角色名：</label><input class="input126" name="roleName" type="text" value="${role.roleName}">
                        </li>
                    </ul>
                <div class="clearfix"></div>
            </div>

            <!--页面信息-->
            <div class="pageMessage">
            <c:if test="${! empty subMenus }">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                         <c:forEach items="${subMenus }" var="menu">
               <!-- 2链接；3submit;4reset;5button 6底部按钮-->
				<c:if test="${menu.type==3 }"><li><input class="button" type="submit" value="${menu.jurisdictionName }"></li> </c:if>
				<c:if test="${menu.type==4 }"><li><input class="button" type="reset" value="${menu.jurisdictionName }"></li></c:if>
				<c:if test="${menu.type==5 }"><li><input class="button" type="button" value="${menu.jurisdictionName }" onclick="${menu.url}"></li></c:if>
				</c:forEach>
                    </ul>
                </div>
                <div class="clearfix"></div>
           </c:if>
                <div class="messageRight fr">
                    查询到<em>${rowCount}</em>条信息，当前是第<span>${pageNo}</span>页，共<span>${maxPage}</span>页。
                </div>
            </div>
        </form>

        <!--内容列表-->
            <div class="dataTable">         
                <table width="100%" border="0" class="tableBox">
            <tr class="tableTh">
                <th width="80"><input type="checkbox" id="checkBtn" onclick="checkAll();"/></th>
                <th>ID</th>
                <th>角色名</th>
                <th>排序</th>
                <th>操作选择</th>
            </tr>
            <c:forEach items="${roleList}" var="roleBean">

                <tr>
                    <td><input type="checkbox" name="ccc" value="${roleBean.id}"/></td>
                    <td>${roleBean.id}</td>
                    <td>${roleBean.roleName}</td>
                    <td>${roleBean.sort}</td>
                    <td>
				        <c:forEach items="${listMenus }" var="menu" varStatus="status">
	                        <!-- 2链接；3submit;4reset;5button 6底部按钮-->
					        <c:if test="${menu.type==2 and status.index != fn:length(listMenus) - 1 }">
					        	<%--<a  class="info_detail ver_block" onclick="${menu.url}${roleBean.id }')">${menu.jurisdictionName}</a>--%>
                                <a  class="info_detail ver_block" onclick="clickA('back/jurisdiction/jsp/new_role_edit.html?roleId='+ ${roleBean.id})">${menu.jurisdictionName}</a>
                                <span class="ver_block ver_span">|</span>
					        </c:if>
					        <c:if test="${menu.type==2 and status.index == fn:length(listMenus) - 1 }">
					        	<a  class="info_detail ver_block" onclick="${menu.url}${roleBean.id }')">${menu.jurisdictionName}</a> 
					        </c:if>
				        </c:forEach>
                    </td>
                </tr>
            </c:forEach>
        </table>
    </div>
        <!--批量管理按钮-->
        <c:if test="${! empty subMenus }">
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <li>
                        <c:forEach items="${subMenus }" var="menu">
                       <!-- 2链接；3submit;4reset;5button,6底部按钮 -->
        				<c:if test="${menu.type==6 }"><input class="button" type="button" value="${menu.jurisdictionName }" onclick="${menu.url}"></c:if>
        				</c:forEach>
                        </li>
                    </ul>
                </div>
            </div>
        </c:if>
        <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
    </div>
</div>
</body>
<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>

<script type="text/javascript">
    function checkAll() {
        $("input[name='ccc']").prop("checked", $("#checkBtn").is(':checked'));
    }

    function clickA(url) {
    	//openWin(url);
    	//openWin2(url);
        window.open(url, 'newwindow', 'width=1200,height=1000,top=0,left=90,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,status=no');
    }
    function clickDel(url) {
        var statu = confirm("删除后，用户权限就立刻删除，确定吗？");
        if (!statu) {
            return false;
        }
        $.ajax({
            type: "get",
            url: url,
            async: false,
            dataType: "json",
            success: function (data) {
                if (data.code == 600 || data.code == 400) {
                    if (confirm(data.msg)) {
                        //window.close();
                        location.reload();
                    }
                } else {
                    alert(data.msg);
                    //window.close();
                    location.reload();
                }
            },
            error: function (XHR, textStatus, errorThrown) {
                alert("网络有点慢，请重试！");
            }
        });
    }

    function getAllChk() {  //jquery获取复选框值
        var s = '';
        $("input[name='ccc']:checked").each(function () {
            s += $(this).val() + ',';
        });
        return s;
    }

    function batchDelete(url) {
        var ids = getAllChk();
        //alert(ids);
        if (null != ids && "" != ids.trim()) {
            var statu = confirm("删除后，用户权限就立刻删除，确定吗？");
            if (!statu) {
                return false;
            }
            $.ajax({
                type: "get",
                url:  url,
                async: false,
                data: {roleId: ids},
                dataType: "json",
                success: function (data) {
                    if (data.code == 600 || data.code == 400) {
                        if (confirm(data.msg)) {
                            location.reload();
                        }
                    } else {
                        alert(data.msg);
                        $("input[type='submit']").click()
                        //location.reload();
                    }
                },
                error: function (XHR, textStatus, errorThrown) {
                    alert("网络有点慢，请重试！");
                }
            });
        } else {
        	alert("请选择数据");
        }
    }
    


</script>
</html>