<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>角色修改</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="../../model/css/base.css" type="text/css" rel="stylesheet">
    <link href="../../model/css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="../../model/css/theme.css" type="text/css" rel="stylesheet">
    <link href="../../model/css/font-awesome.css" type="text/css" rel="stylesheet">
    <link href="../../model/css/page.css" type="text/css" rel="stylesheet">
    <link href="../../model/css/detailPages.css" type="text/css" rel="stylesheet">
    <link href="../../jurisdiction/jsp/css/roleAdd.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="../../model/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="../../model/js/bootstrap.js"></script>
    <script src="../../model/js/tyt_common.js"></script>
</head>
<body class="theme-blue">
<input type="hidden" name="id" value="${role.id}">
<input type="hidden" id="roleNameId" value="${role.roleName}">
<div class="content" style="padding-top: 0;">
    <h1 class="titUrl">
        <span>当前所在位置：</span><span>企业员工管理</span><span>&nbsp;&gt;&nbsp;</span><span>角色管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">角色修改</span>
    </h1>
    <div class="main-content">
        <div class="systemParameter">
            <ul>
                <li>
                    <label><i class="colorRed">*</i>角色名称：</label>
                    <input maxlength="20" id="roleName" class="input180" name="roleName" type="text" value=""><span id="ne" class="colorRed" style="display: none"> 该角色名称已存在！</span>
                </li>
                <li>
                    <label><i class="colorRed">*</i>排序：</label>
                    <input maxlength="20" id="sort" class="input180" name="sort" type="text" value="">
                </li>
                <li style="display: none">
                    <label>拥有所有权限：</label>
                    <span class="radioOridinal off firstRadio">
                        <input id="yes" type="radio" class="radioclass" name="isAll" value="1">是
                    </span>
                    <span class="radioOridinal off">
                        <input id="no" type="radio" class="radioclass" name="isAll" value="0">否
                    </span>
                </li>
            </ul>
        </div>
        <div id="jurisdiction" class="main-content quanxian" style="border-top: 1px solid #ccc;">
            <div class="htmleaf-container">
                <div class="demo">
                    <div class="container">
                        <div class="row">
                            <div class="col-md-offset-3 col-md-6 w96">
                                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                                    <img class="loading-childjurisdictions" src="./img/loading-childjurisdictions.gif" alt="*"/>

                                    <!--<div class="panel panel-default">-->
                                        <!--<div class="panel-heading" role="tab" id="headingOne">-->
                                            <!--<h4 class="panel-title">-->
                                                <!--<a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseOne" aria-expanded="true" aria-controls="collapseOne">-->
                                                    <!--Section 1-->
                                                <!--</a>-->
                                            <!--</h4>-->
                                        <!--</div>-->
                                        <!--<div id="collapseOne" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne">-->
                                            <!--<div class="panel-body">-->
                                                <!--<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent nisl lorem, dictum id pellentesque at, vestibulum ut arcu. Curabitur erat libero, egestas eu tincidunt ac, rutrum ac justo. Vivamus condimentum laoreet lectus, blandit posuere tortor aliquam vitae. Curabitur molestie eros. </p>-->
                                            <!--</div>-->
                                        <!--</div>-->
                                    <!--</div>-->


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="submitBtn newSubmitBtn"><input id="btn" type="button" class="button" value="提交" onclick="optFn.submitData();"></div>

</body>
<script type="text/javascript" src="../../jurisdiction/jsp/js/newRoleEdit.js"></script>
</html>
<script>

</script>
