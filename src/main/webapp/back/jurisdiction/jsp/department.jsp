<%@ page import="java.util.Map" %>
<%@ page import="com.tyt.jurisdiction.bean.JurisdictionQueryBean" %><%--15011400208
  Created by IntelliJ IDEA.
  User: douge
  Date: 16-7-11
  Time: 下午6:21
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
    String path = request.getContextPath();
    String basePath = "https://" + request.getServerName() + path + "/";

    //Map<Long, JurisdictionQueryBean> jurisdictionMap = (Map<Long, JurisdictionQueryBean>) session.getAttribute("jurisdictions");
%>
<!doctype html>
<html>
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>部门列表</title>
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableModel.css" type="text/css" rel="stylesheet">
    <link href="back/model/css/tableAlert.css" type="text/css" rel="stylesheet">

    <script type="text/javascript" src="back/model/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="back/model/js/bootstrap.js"></script>
    <script type="text/javascript" src="back/jurisdiction/jsp/js/common.js"></script>
    <script type="text/javascript">
    function toOpenDepartment(url){
    	openWinAuto(url,'winName',1100,600);
    }
    </script>
</head>

<body>

<div class="contentBox">
    <div class="titUrl cf">
        <h1 class=" fl">
            <span>当前所在位置：</span><span>部门管理</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">部门列表</span>
        </h1>
    </div>
    <div class="main-content">

        <!--搜索条件-->
        <form action="jurisdiction/department/list" method="post">
            <input type="hidden" value="${curMenu.id}" name="menuId">
            <input type="hidden" value="${pageNo}" name="pageNo">
			<input type="hidden" value="${department.institutionId }" id="blongedInstitutionId">
                <div class="searchBox" id="searchCondition">
                    <ul class="searchUl searchUlNone cf">
                    <li><label>ID：</label><input class="input126" name="id" type="text" value="${department.id}"></li>
                    <li><label>部门名称：</label><input class="input126" name="departmentName" type="text"
                                                   value="${department.departmentName}"></li>
                    <li><label>部门编号：</label><input class="input126" name="departmentNo" type="text"
                                                   value="${department.departmentNo}"></li>
                    <li><label>所属机构：</label><select class="select126" name="institutionId" id="institution">
                        <option value="-1">不限</option>
                    </select></li>
                </ul>
                <div class="clearfix"></div>
            </div>

            <!--页面信息-->
            <div class="pageMessage">
                <div class="searchBtn">
                    <ul class="btnBox cf">
                        <c:forEach items="${subMenus }" var="menu">
		                <!-- 2链接；3submit;4reset;5button -->
						<c:if test="${menu.type==3 }"><li><input class="button" type="submit" value="${menu.jurisdictionName }"></li> </c:if>
						<c:if test="${menu.type==4 }"><li><input class="button" type="reset" value="${menu.jurisdictionName }"></li></c:if>
						<c:if test="${menu.type==5 }"><li><input class="button" type="button" value="${menu.jurisdictionName }" onclick="${menu.url}"></li></c:if>
						</c:forEach>
                    </ul>
                </div>
                <div class="clearfix"></div>

                <div class="messageRight fr">
                    查询到<em>${rowCount}</em>条信息，当前是第<span>${pageNo}</span>页，共<span>${maxPage}</span>页。
                </div>
            </div>
        </form>

        <!--内容列表-->
            <div class="dataTable">         
                <table width="100%" border="0" class="tableBox fuckList">
                    <tr class="tableTh">
                        <th width="80">ID</th>
                        <th>部门名称</th>
                        <th>部门编号</th>
                        <th>部门人数</th>
                        <th>所属机构</th>
                        <th>操作</th>
                    </tr>
                    <c:forEach items="${departmentList}" var="departmentBean">

                        <tr id="tr${departmentBean.id}" nextChildId="#${departmentBean.id}#" level="1" class="level1">
                            <td>${departmentBean.id}</td>
                            
                            <c:choose>
                            <c:when test="${departmentBean.childrenNum > 0}">
                            <td class="tdLeft" onclick="clickImg(${departmentBean.id});" style="cursor: pointer;">
                            <img id="${departmentBean.id}"  src="<%=request.getContextPath()%>/back/image/u911.png">
                            <i></i><span >${departmentBean.departmentName}</span>
                            </td>
                            </c:when>
                            <c:otherwise>
                            <td class="tdLeft">
                            <i style="margin-left:54px;"></i><span>${departmentBean.departmentName}</span>
                            </td>
                            </c:otherwise>
                            </c:choose>
                            <td>${departmentBean.departmentNo}</td>
                            <td>${departmentBean.employeeNum}</td>
                            <td>${departmentBean.institutionName}</td>
                            <td id="td${departmentBean.id}">
        	                    <c:forEach items="${listMenus }" var="menu" varStatus="status">
        		                        <!-- 2链接；3submit;4reset;5button 6底部按钮-->
        						        <c:if test="${menu.type==2 and status.index != fn:length(listMenus) - 1 }">
        						        	<a class="info_detail ver_block" onclick="${menu.url}${departmentBean.id }')">${menu.jurisdictionName}</a>
                                            <span class="ver_block ver_span">|</span>
        						        </c:if>
        						        <c:if test="${menu.type==2 and status.index == fn:length(listMenus) - 1 }">
        						        	<a class="info_detail ver_block" onclick="${menu.url}${departmentBean.id }')">${menu.jurisdictionName}</a> 
        						        </c:if>
        				        </c:forEach>
                            </td>
                        </tr>
                        <tbody id="tbody${departmentBean.id}"></tbody>
                    </c:forEach>
                </table>
            </div>

        <jsp:include page="/back/jsp/footer.jsp" flush="true"/>
    </div>
</div>

</body>

<script type="text/javascript" src="back/menu/js/colResizable-1.6.min.js"></script>
<script type="text/javascript" src="back/menu/js/infoMessage.js"></script>
<script type="text/javascript">
	changeLineColor();
    function getAllInstitution() {
        $.ajax({
            type: "get",
            url: 'jurisdiction/institution/ajx_queryInstitution',
            async: false,
            dataType: "json",
            success: function (data) {
                for (var i = 0; i < data.length; i++) {
                	if ($("#blongedInstitutionId").val() == data[i][0]) {
	                    $("#institution").append("<option selected value='" + data[i][0] + "'>" + data[i][1] + "</option>");
                	} else {
	                    $("#institution").append("<option value='" + data[i][0] + "'>" + data[i][1] + "</option>");
                	}
                }
            },
            error: function (XHR, textStatus, errorThrown) {
                alert("网络有点慢，请重试！");
            }
        });

    }
    getAllInstitution();

    function checkAll() {
        $("input[name='ccc']").prop("checked", $("#checkBtn").is(':checked'));
    }

    function clickA(url) {
    	openWinAuto(url,'winName',1100,600);
        //window.open(url, 'newwindow', 'width=600,height=1000,top=0,right=0,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,status=no');
    }
    function clickDel(url, childrenNum) {
    	/* if (!childrenNum) {
    		var statu = confirm("确定要删除此部门吗?");
            if (!statu) {
                return false;
            }
    	} else {
    		alert("请先删除或转移下属部门和员工");
    		return false;
    	} */
        $.ajax({
            type: "POST",
            url: url,
            async: false,
            dataType: "json",
            success: function (data) {
                if (data.code == 600 || data.code == 400) {
                    /* if (confirm(data.msg)) {
                        //window.close();
                        location.reload();
                    } */
                    alert(data.msg);
                } else {
                    alert(data.msg);
                    //window.close();
                    location.reload();
                }
            },
            error: function (XHR, textStatus, errorThrown) {
                alert("网络有点慢，请重试！");
            }
        });
    }

    function clickImg(id) {
        var img = $("#" + id);
        if (img.attr("src").indexOf("u911.png")!=-1) {
            img.prop("src", "back/image/u911_selected.png");
            /* var tb = $("#tbody" + id);
            if ($("#tbody" + id).children().length <= 0) {
                getChildDepartmentsByPid(img.attr("id"))
            } else {
                $("#tbody" + id).show();
            } */
            getChildDepartmentsByPid(img.attr("id"));
        } else {
        	// 修改显示图标
        	img.prop("src", "back/image/u911.png");
            // 删除所有子元素
            removeChildren(id);
        }
    	changeLineColor();
    }
    
    function removeChildren(curTrId) {
    	var trArr = $('tr');
    	for (var i = 0; i < trArr.length; i++) {
    		var curTr = $(trArr[i]);
    		if (curTr.attr('parentId') && curTr.attr('parentId').indexOf('#' + curTrId + '#') != -1) {
    			curTr.remove();
    		}
    	}
    }

    function getChildDepartmentsByPid(id) {
        $.ajax({
            type: "POST",
            url: 'jurisdiction/department/ajx_queryChildDepartmentsByPid',
            async: false,
            data: {pid: id},
            dataType: "json",
            success: function (data) {
                printDepartments(data);
            },
            error: function (XHR, textStatus, errorThrown) {
                alert("网络有点慢，请重试！");
            }
        });
    }
    
    function printDepartments(data) {
        for (var i = 0; i < data.length; i++) {
            var id = data[i][0];
            var childrenNum = data[i][1];
            var departmentName = data[i][2];
            var departmentNo = data[i][3];
            var employeeNum = data[i][4];
            var institutionName = data[i][5];
            var pid = data[i][6];
            var td = $("#td" + pid).children("a");


            var updateUrl = "";
            if (td.eq(0).text().trim() != "*")
                updateUrl = td.eq(0).attr("onclick").toString().split('=')[0] + "=" + id + "')";
        

        var delUrl = "";
        if (td.eq(1).text().trim() != "*") {
            delUrl = td.eq(1).attr("onclick").toString().split('=')[0] + "=" + id + "', " + childrenNum + ")";
        }
        var curTr = $("#tr" + pid),
			nextChildId = curTr.attr("nextChildId"),
			newNextChildId = nextChildId + id + "#",
			level = parseInt(curTr.attr("level")),
			newLevel = level + 1,
			levelCls = "level" + newLevel,
			paddingLeft = 30 * (newLevel - 1);
        if (childrenNum > 0) {
            $("#tr" + pid).after("<tr level='" + newLevel + "' id='tr" + id + "' parentId='" + nextChildId + "' nextChildId='" + newNextChildId + "'><td>" + id + "</td><td class='tdLeft' style='padding-left: " + paddingLeft + "px!important;cursor: pointer;' onclick='clickImg(" + id + ");' ><img id='" + id + "' src='back/image/u911.png'><i></i><span>" + departmentName + "</span></td><td>" + departmentNo + "</td><td>" + employeeNum + "</td><td>" + institutionName + "</td><td id='td" + id + "'><a class='info_detail ver_block'  id='u" + id + "'>" + td.eq(0).text() + "</a><span class='ver_block ver_span'>|</span><a class='info_detail ver_block'  id='d" + id + "'>" + td.eq(1).text() + "</a></td></tr>");
            $("#u" + id).attr("onclick", updateUrl);
            $("#d" + id).attr("onclick", delUrl);
        } else {
            $("#tr" + pid).after("<tr id='tr" + id + "' parentId='" + nextChildId + "'><td>" + id + "</td><td class='tdLeft' style='padding-left: " + paddingLeft + "px!important;'><i style='margin-left:60px;'></i><span>" + departmentName + "</span></td><td>" + departmentNo + "</td><td>" + employeeNum + "</td><td>" + institutionName + "</td><td><a class='info_detail ver_block'  id='u" + id + "'>" + td.eq(0).text() + "</a><span class='ver_block ver_span'>|</span><a class='info_detail ver_block'  id='d" + id + "'>" + td.eq(1).text() + "</a></td></tr>");
            $("#u" + id).attr("onclick", updateUrl);
            $("#d" + id).attr("onclick", delUrl);
        }
        }
    }
    function changeLineColor(){
       $(".fuckList").find('tr:odd').css('background','#fff');
       $(".fuckList").find('tr:even').css('background','#f1f1f1');
    }
</script>
</html>