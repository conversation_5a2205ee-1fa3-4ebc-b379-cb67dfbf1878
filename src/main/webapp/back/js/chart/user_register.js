function checkDateType(dateType) {
	var flag = true;
	var startDate = $('#startDate').val();
	var endDate = $('#endDate').val();
	if (!startDate) {
		alert("查询日期范围不能为空！");
		flag = false;
		return false;
	}
	var daysDiff = DateDiff(startDate, endDate);

	if (dateType == "week" && (daysDiff < 7 || daysDiff > 210)) {
		alert("按周查询日期范围为7到210天，您当前查询范围为：" + daysDiff + "天");
		return false;
	}
	if (dateType == "month" && (daysDiff < 30 || daysDiff > 900)) {
		alert("按月查询日期范围为30到900天，您当前查询范围为：" + daysDiff + "天");
		return false;
	}
	if (dateType == "day" && daysDiff >60) {
		alert("按日查询日期范围为1到60天，您当前查询范围为：" + daysDiff + "天");
		return false;
	}
	return flag;
}

function doDateType(name) {
	var flag = checkDateType(name);
	if (flag) {
		$("input[name='compareType']").val("oneLine");
		$("input[name='dateType']").val(name);
		$("#form1").submit();
	}
}

function sbmtTerminalCompare() {
	$("input[name='compareType']").val("Multi-terminal");
	$("#form1").submit();
}

function doCancelCompare() {
	// $('#cancel').click(function() {
	$('#modal').hide();
	$('#compareChoose').hide();
	$('#city-A').hide();
	$('#provinceCompare').hide();
	$("#address").val("");
	$('.selectedProvince').empty();
	selectedProvinces = "";

	// });
}
function doAreaMenu() {
	// $('#areaCompare').click(function() {
	/*
	 * 显示模态窗口和区域选择框
	 */
	$('#modal').show();
	$('#compareChoose').show();
	// });
}
function sbmtIdCardCompare() {
	$("#compareType").val("IdCard");
}
function checkDateRange() {
	/*
	 * 计算当前查询的时间范围 选择日期区间 30《T《900天才能选择月 选择日期区间需 7《T《210天 才能选择周 选择日期区间需 1《T《60天
	 * 才能选择日
	 */
	var flag = true;
	var startDate = $('#startDate').val();
	var endDate = $('#endDate').val();
	var dateType = $("input[name='dateType']").val();
	if (!startDate) {
		alert("查询日期范围不能为空！");
		return false;
	}
	var daysDiff = DateDiff(startDate, endDate);

	if (dateType == "week" && (daysDiff < 7 || daysDiff > 210)) {
		alert("按周查询日期范围为7到210天，您当前查询范围为：" + daysDiff + "天");
		return false;
	}
	if (dateType == "month" && (daysDiff < 30 || daysDiff > 900)) {
		alert("按月查询日期范围为30到900天，您当前查询范围为：" + daysDiff + "天");
		return false;
	}
	if (dateType == "day" && daysDiff >60) {
		alert("按日查询日期范围为1到60天，您当前查询范围为：" + daysDiff + "天");
		return false;
	}
	var compareType = $("#compareType").val();
	// alert(compareType);
	if (compareType == 'IdCard') {
		var val = $("#cardId").val();
		var idCard = $("#cardId").val().split(",");

		if (idCard.length < 2 || val == '') {
			alert("最少选择两个身份！");
			return false;
		} else if (idCard.length > 7) {
			alert("身份最多选择7个！");
			return false;
		}
	}
	if (compareType == 'compareProvince') {
		var val = $("#selectedProvinces").val();
		var idCard = $("#selectedProvinces").val().split(",");
		if (idCard.length < 2 || val == '') {
			alert("最少选择两个省份！");
			return false;
		} else if (idCard.length > 7) {
			alert("省份最多选择7个！");
			return false;
		}
	}
	if (compareType == 'compareCity') {
		var val = $("#address").val();
		var idCard = $("#address").val().split(",");
		if (idCard.length < 3 || val == '') {
			alert("最少选择两个市！");
			return false;
		} else if (idCard.length > 8) {
			alert("市最多选择7个！");
			return false;
		}
	}
	return flag;
}
var cardNameArr=new Array();
var cardIdArr =new Array();
var shengNameArr =new Array();

var cardName = "";
var cardId = "";
var shengName = "";
function doAddID(name, id, obj) {

	var param = "btn-default";
	var param2 = "btn-info";
	
	$("#compareType").val("IdCard");
	cardName = $("#cardName").val();
	cardId = $("#cardId").val();
	if(cardName != '' && cardNameArr.length == 0){
		cardNameArr = cardName.split(",");
		cardIdArr = cardId.split(",");
	}
	//alert(cardNameArr.length);
	if (cardNameArr.length < 7 && $(obj).attr("class").indexOf(param2) == -1) {
		changeBtnCss(param, param2, obj);
		
		cardNameArr.push(name);
		cardIdArr.push(id);

		$("#cardName").val(cardNameArr.toString());
		$("#cardId").val(cardIdArr.toString());
//		if (cardId.indexOf(id + ",") == -1) {
//			cardName = cardName + name + ",";
//			cardId = cardId + id + ",";
//			$("#cardName").val(cardName);
//			$("#cardId").val(cardId);
//		} else {
//			cardName = cardName.replace(","+name + ",", ",");
//			cardId = cardId.replace(","+id + ",", ",");
//			$("#cardName").val(cardName);
//			$("#cardId").val(cardId);
//		}
	} else if (cardNameArr.length <= 7 && $(obj).attr("class").indexOf(param) == -1) {
		var arrNew = new Array();
		var arrNew2 = new Array();
		for (var k = 0; k < cardNameArr.length; k++) {
			if(cardNameArr[k]!=name){
				arrNew.push(cardNameArr[k]);
				arrNew2.push(cardIdArr[k]);
			}
		}
		
		cardNameArr = arrNew;
		cardIdArr = arrNew2;
		$("#cardName").val(cardNameArr.toString());
		$("#cardId").val(cardIdArr.toString());
		
		changeBtnCss(param, param2, obj);
	
	}else {
		alert("身份最多选择7个！");
	}
}
function doAddSheng(obj, name) {
	var param = "btn-default";
	var param2 = "btn-info";
	
	$("#compareType").val("compareProvince");
	shengName = $("#selectedProvinces").val();
	if(shengName != '' && shengNameArr.length == 0){
		shengNameArr = shengName.split(",")	
	}
	if (shengNameArr.length < 7 && $(obj).attr("class").indexOf(param2) == -1) {
		changeBtnCss(param, param2, obj);
		shengNameArr.push(name);
		$("#selectedProvinces").val(shengNameArr.toString());
//		if (shengName.indexOf(name) == -1) {
//			shengName = shengName + name + ",";
//			$("#selectedProvinces").val(shengName);
//		} else {
//			shengName = shengName.replace(","+name + ",", ",");
//			$("#selectedProvinces").val(shengName);
//		}
	}else if (shengNameArr.length <= 7 && $(obj).attr("class").indexOf(param) == -1) {
		changeBtnCss(param, param2, obj);
//		shengName = shengName.replace("," + name + ",", ",");
//		$("#selectedProvinces").val(shengName);
		var arrNew = new Array();
		for (var k = 0; k < shengNameArr.length; k++) {
			if(shengNameArr[k]!=name){
				arrNew.push(shengNameArr[k]);
			}
		}
		
		shengNameArr = arrNew;
		$("#selectedProvinces").val(shengNameArr.toString());
		
	} else {
		alert("省份最多选择7个！");
	}
}

function doDelID() {
	cardName = "";
	cardId = "";
	cardNameArr=new Array();
	cardIdArr =new Array();

	$("#cardName").val(cardName);
	$("#cardId").val(cardId);
	var param = "btn-default";
	var param2 = "btn-info";
	$("#myModal .modal-body .btn").each(function(index) {
		if ($(this).attr("class").indexOf(param2) != -1) {
			var cls = $(this).attr("class").replace(param2, param);
			$(this).attr("class", cls);
		}
	});

}
function doDelShengName() {
	shengName = "";
	shengNameArr =new Array();
	$("#selectedProvinces").val(shengName);
	var param = "btn-default";
	var param2 = "btn-info";
	$("#areaModal .modal-body #home .btn ").each(function(index) {
		if ($(this).attr("class").indexOf(param2) != -1) {
			var cls = $(this).attr("class").replace(param2, param);
			$(this).attr("class", cls);
		}
	});
}
function doDelShiName() {
	$("#address").val("");
}
function doSheng() {
	$("#compareType").val("compareProvince");
}
function doShi() {
	$("#compareType").val("compareCity");
}
function doTopIdCard(obj) {
	// var aa = $("#compareType").val();
	// if(aa.indexOf("topCardId")==-1){
	$("#compareType").val("topCardId");
	// }else if(aa.indexOf("IdCard")==-1){
	// $("#compareType").val("IdCard");
	// }
}
function doAreaType(obj) {
	// var aa = $("#compareType").val();
	// if(aa.indexOf("compareProvince")==-1){
	// $("#compareType").val("compareProvince");
	// }else if(aa.indexOf("compareCity")==-1){
	// $("#compareType").val("compareCity");
	// }else if(aa.indexOf("topArea")==-1){
	$("#compareType").val("topArea");
	// }

}
// function doAreaCity(){
// $("#compareType").val("compareCity");
// $('#citymyModal').modal('show');
//	
// }

function changeBtnCss(param, param2, obj) {
	// var param = "btn-primary";
	// var param2 = "btn-info";

	if ($(obj).attr("class").indexOf(param) != -1) {
		var cls = $(obj).attr("class").replace(param, param2);
		$(obj).attr("class", cls);
	} else if ($(obj).attr("class").indexOf(param2) != -1) {
		var cls = $(obj).attr("class").replace(param2, param);
		$(obj).attr("class", cls);
	}
}

function hideDiv(id) {
	$('#' + id).hide();
}

function undulpicate(array) {
	for ( var i = 0; i < array.length; i++) {
		for ( var j = i + 1; j < array.length; j++) {
			// 注意 ===
			if (array[i] === array[j]) {
				array.splice(j, 1);
				j--;
			}
		}
	}
	return array;
}
function sbmtCity() {
	// alert($("#address").val());
	var cities = $("#address").val().split(',');
	cities = undulpicate(cities);

	if (cities < 3) {
		alert("请至少选择两个市");
		return;
	}
	var cityStr = "";
	for ( var i = 0; i < cities.length - 1; i++) {
		// alert(cities[i]);
		cityStr = cityStr + cities[i] + ",";
	}
	cityStr = cityStr.substring(0, cityStr.length - 1);
	var startDate = $('#startDate').val(), endDate = $('#endDate').val(), deliverType = $(
			'#deliverType').val();
	if (!startDate || !endDate) {
		alert("查询日期范围不能为空！");
		return;
	}

	window.location.href = "/tyt_manage/chart/userRegisterCompare?compareType=compareCity&startDate="
			+ startDate
			+ "&endDate="
			+ endDate
			+ "&selectedCities="
			+ encodeURI(encodeURI(cityStr)) + "&deliverType=" + deliverType;
}
function changeTime() {
	var startDate = $('#startDate').val();
	var endDate = $('#endDate').val();
	var timeText = $('#timeText').val();
	var days = DateDiff(endDate, startDate);
	// alert(days);
	var newDate = DateAdd(timeText, days);
	$('#startDate').val(newDate);
	$('#endDate').val(timeText);
}
function doNearTime() {
	var startDate = $('#startDate').val();
	// var endDate = $('#endDate').val();
	// var days = DateDiff(endDate, startDate);
	// var newDate = DateAdd(startDate, days);
	// $('#startDate').val(newDate);
	// $('#endDate').val(startDate);
	$('#timeText').val(startDate);
	$("#compareType").val("nearTime");

}
function sbmtTimeCompare() {
	// var dateType = $(".cur").attr("type");
	// alert(dateType);
	// checkDateRange();
	$("#compareType").val("compareTime");
	$("#form1").submit();
	// 调用查询函数
	// find(dateType, true);
}

// 计算天数差的函数，通用
function DateDiff(sDate1, sDate2) { // sDate1和sDate2是2006-12-18格式
	var oDate1, oDate2, iDays;
	var aDate = sDate1.split('-').join('/');
	oDate1 = new Date(aDate);
	aDate = sDate2.split('-').join('/');
	oDate2 = new Date(aDate);
	iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24) + 1; // 把相差的毫秒数转换为天数
	return iDays;
}

// 计算前n天
function DateAdd(sdate, days) {
	var a = new Date(sdate);
	a = a.valueOf();
	a = a - days * 24 * 60 * 60 * 1000;
	a = new Date(a);
	return a.Format("yyyy-MM-dd");
}
Date.prototype.Format = function(fmt) { // author: meizz
	var o = {
		"M+" : this.getMonth() + 1, // 月份
		"d+" : this.getDate(), // 日
		"h+" : this.getHours(), // 小时
		"m+" : this.getMinutes(), // 分
		"s+" : this.getSeconds(), // 秒
		"q+" : Math.floor((this.getMonth() + 3) / 3), // 季度
		"S" : this.getMilliseconds()
	// 毫秒
	};
	if (/(y+)/.test(fmt))
		fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "")
				.substr(4 - RegExp.$1.length));
	for ( var k in o)
		if (new RegExp("(" + k + ")").test(fmt))
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k])
					: (("00" + o[k]).substr(("" + o[k]).length)));
	return fmt;
}
