(function() {

	function stripHtml(value) {
		// remove html tags and space chars
		return value.replace(/<.[^<>]*?>/g, " ").replace(/&nbsp;|&#160;/gi, " ")
		// remove punctuation
		.replace(/[.(),;:!?%#$'\"_+=\/\-“”’]*/g, "");
	}

	$.validator.addMethod("maxWords", function(value, element, params) {
		return this.optional(element) || stripHtml(value).match(/\b\w+\b/g).length <= params;
	}, $.validator.format("Please enter {0} words or less."));

	$.validator.addMethod("minWords", function(value, element, params) {
		return this.optional(element) || stripHtml(value).match(/\b\w+\b/g).length >= params;
	}, $.validator.format("Please enter at least {0} words."));

	$.validator.addMethod("rangeWords", function(value, element, params) {
		var valueStripped = stripHtml(value),
			regex = /\b\w+\b/g;
		return this.optional(element) || valueStripped.match(regex).length >= params[0] && valueStripped.match(regex).length <= params[1];
	}, $.validator.format("Please enter between {0} and {1} words."));
	
	jQuery.validator.addMethod("isMoney", function(value, element) {   
	    var money = /^\d*\.?\d{1,2}$/;
	    return this.optional(element) || (money.test(value));
	}, "金额格式不正确,请重新填写");
	jQuery.validator.addMethod("isNull", function(value, element) {
		var valueLength=value.replace(/(^s*)|(s*$)/g, "").length;
	    return valueLength>0;
	}, $.validator.format("{0}"));
	jQuery.validator.addMethod("isDigits", function(value, element) {
		var days = /^-?[1-9]+[0-9]*$/;
	    return days.test(value);
	}, $.validator.format("请输入正确的{0}!"));

}());
