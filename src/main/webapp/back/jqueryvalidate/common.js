$().ready(function(){
	
	var validator =$("#form").validate({
		/*只是调试*/
//	   debug: true,
//	   onfocusout:false,
		/*验证规则*/
	   rules: {
//	            firstname: {required: true},
//	            email: {required: true,email: true },
//	            password: {required: true,minlength: 5},
//	            confirm_password: {required: true,minlength: 5,equalTo: "#password"},
	            money:{isNull:"金额不能为空",isMoney:true},//金额
	            years:{isNull:"请选择开通年限!"},
	            extralDays:{isNull:"赠送天数不能为空",isDigits:"赠送天数",max:10000},//天数
	            note:{maxlength:50},//加时备注
	            bank:{maxlength:30},//银行
	          },
	  errorClass:"errorBlock pl223",
	  errorElement:"span",
      errorPlacement: function(error, element) { 
    	    $("#error").html("");
    	    error.appendTo($("#error"));  
    	},
	  highlight: function(element, errorClass) { 
		  if($(element).attr("id")=="note"){
			  $(element).removeClass().addClass("input500"); 
		  }else{
			  $(element).removeClass().addClass("input180"); 
		  }
		},
	  submitHandler:function(form){
	        $("#send_btn").attr({"disabled":"disabled"});
	        form.submit();
	    },

	});
	/*重置按钮*/
	$("#reset").click(function(label) {
        validator.resetForm();
    });	
});