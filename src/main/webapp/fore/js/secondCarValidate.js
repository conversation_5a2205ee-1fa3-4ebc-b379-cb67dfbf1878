function clearTitle(obj) {
	if (obj.value == "例：急售一辆载重100吨的大板车") {
		obj.value = "";
	}
}

function showCarName1() {
	document.getElementById("carName1").style.display = "block";
	document.getElementById("carName2").style.display = "none";
	document.getElementById("carName3").style.display = "none";
}

function showCarName2() {
	document.getElementById("carName1").style.display = "none";
	var arr = document.getElementsByName("carName");
	for (var i = 0; i < arr.length; i++) {
		arr[i].checked = false;
	}
	document.getElementById("carName2").style.display = "block";
	document.getElementById("carName3").style.display = "none";
}

function showCarName3() {
	document.getElementById("carName1").style.display = "none";
	var arr = document.getElementsByName("carName");
	for (var i = 0; i < arr.length; i++) {
		arr[i].checked = false;
	}
	document.getElementById("carName2").style.display = "none";
	document.getElementById("carName3").style.display = "block";
}
$(document).ready(function() {
	$("#wishes").keyup(function() {
		var str = $('#wishes').val();
		if (str.length > 300)
			$('#wishes').val(str.substring(0, 300));
	});
	$("#resetId").click(function() {
		// $("span").html("");
	});
	$("#fabu").mousemove(function() {
		$("#fabu").css("background-color", "#fea000");
	});
	$("#fabu").mouseout(function() {
		$("#fabu").css("background-color", "#fd8000");
	});
	$("#fabu").mousedown(function() {
		$("#fabu").css("background-color", "#fe3000");
	});
	$("#fabu").mouseup(function() {
		$("#fabu").css("background-color", "#fd8000");
	});
	$("#resetId").mousemove(function() {
		$("#resetId").css("background-color", "#fea000");
	});
	$("#resetId").mouseout(function() {
		$("#resetId").css("background-color", "#fd8000");
	});
	$("#resetId").mousedown(function() {
		$("#resetId").css("background-color", "#fe3000");
	});
	$("#resetId").mouseup(function() {
		$("#resetId").css("background-color", "#fd8000");
	});
});

function checkTitle() {
	var flag = false;
	var title = document.getElementById("title").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (title == "" || title == "例：急售一辆载重100吨的大板车") {
		document.getElementById("put1").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>标题不能为空！</span>";
	} else if (title.length < 2 || title.length > 20) {
		document.getElementById("put1").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>标题长度2——20</span>";
	} else {
		document.getElementById("put1").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		flag = true;
	}
	return flag;
}

function checkMileage() {
	var flag = false;
	var reg = new RegExp(/^[0-9]+(.[0-9]{1,2})?$/);
	var mileage = document.getElementById("mileage").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (mileage == "") {
		document.getElementById("put5").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>行驶里程不能为空！</span>";
	} else if (!reg.test(mileage)) {
		document.getElementById("put5").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>小数点后最多两位！</span>";
	} else {
		document.getElementById("put5").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		flag = true;
	}
	return flag;
}

function checkBearing() {
	document.getElementById("put6").innerHTML = "";
	var flag = false;
	var reg = new RegExp(/^[1-9]\d*$/);
	var bearing = document.getElementById("bearing").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (!reg.test(bearing)) {
		document.getElementById("put6").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效（整数）载重值</span>";
	} else {
		flag = true;
		if (bearing != 1)
			document.getElementById("put6").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}
	return flag;
}

function checkHorsePower() {
	document.getElementById("put7").innerHTML = "";
	var flag = false;
	var reg = new RegExp(/^[1-9]\d*$/);
	var horsePower = document.getElementById("horsePower").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (!reg.test(horsePower)) {
		document.getElementById("put7").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效（整数）马力值</span>";
	} else {
		flag = true;
		if (horsePower != 1)
			document.getElementById("put7").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}
	return flag;
}
/*function checkQQ(){
	var flag=false;
	var reg = new RegExp("[0-9][0-9]{4,}"); 
	var qq=document.getElementById("qq").value; 
    if(!reg.test(qq)){ 
    document.getElementById("put13").innerHTML="<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效(数字)qq</span>"; 
    }else if(qq==""){
    	flag=true;
        document.getElementById("put13").innerHTML="<p class='pp'><img src='../fore/image/zq.png'></p>";
    }else{
    document.getElementById("put13").innerHTML="<p class='pp'><img src='../fore/image/zq.png'></p>";
    flag=true;
    }
    return flag;
}*/
function checkQQ() {
	var flag = false;
	var qq = document.getElementById("qq").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (qq == "" || qq == "0") {
		flag = true;
		document.getElementById("put13").innerHTML = "<p class='pp'></p>";
	} else if (isNaN(qq)) {
		flag = false;
		document.getElementById("put13").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效(数字)qq</span>";
	} else if (qq < 0 || qq.length > 11) {
		flag = false;
		document.getElementById("put13").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效(数字)qq</span>";
	} else {
		document.getElementById("put13").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		flag = true;
	}
	return flag;
}

function checkTelName() {
	var flag = false;
	var telName = document.getElementById("telName").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (telName == "") {
		document.getElementById("put10").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效联系人</span>";
	} else {
		document.getElementById("put10").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		flag = true;
	}
	return flag;
}

function checkTelephone() {
	var flag = false;
	var telephone = document.getElementById("telephone").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (telephone == "" || telephone == "请写有效联系电话") {
		document.getElementById("put11").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>联系电话不能为空!</span>";
	} else {
		document.getElementById("put11").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		return true;
	}
	return flag;
}

function checkAddress() {
	var flag = false;
	var province = document.getElementById("province").value;
	if (province == "") {
		document.getElementById("put12").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>工作地址不能为空!</span>";
	} else {
		flag = true;
		document.getElementById("put12").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}
	return flag;
}
/*function checkSaveDay(){
    var flag=true;
	var saveDay = document.getElementById("date_3").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, ""); 
	if(saveDay!=null){
	document.getElementById("put17").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}else if (saveDay == ""||saveDay=="请选择有效截止日期") {
		document.getElementById("put17").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>信息有效截止日期不能为空</span>";
		flag=false;
	}
	return flag;
}*/

function checkAll() {
	if (checkTitle() && checkAddress() && checkTelName() && checkTelephone() && checkQQ() && checkMileage() && checkBearing() && checkHorsePower() /*&&checkSaveDay()*/ ) {
		return true;
	} else {
		return false;
	}
}