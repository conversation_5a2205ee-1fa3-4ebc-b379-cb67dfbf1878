/*!
 * jquery.fn.upload.js
 * <AUTHOR>
 * @version 1.2
 */
(function(g,k,d){var i="jquery-upload",n=k.document,j=n.body,a=n.createElement("input").files===null||"files" in n.createElement("input"),c=!!window.FormData,o=a&&c,h=function(){},f={action:"",headers:{},data:null,name:"",oncomplete:h,onsuccess:h,onerror:h,onprogress:h};g.fn.upload=function(r){var s=g.type(r)==="string",q=s?{}:g.extend({},f,r),p=[].slice.call(arguments);return this.each(function(){var t=g(this).data(i);if(!t){g(this).data(i,t=new b(this,q));}else{if(!s){t.options=g.extend({},t.options,r);}}if(s){t[r]&&t[r].apply(t,p.slice(1));}else{t._init();}});};g.fn.upload.defaults=f;g.support.inputFiles=a;g.support.formData=c;var b=function(q,p){this.element=q;this.options=p;};b.prototype={_init:function(){if(!e(this.element)){return;}o?l.call(this):m.call(this);},blob:function(q,p){l.call(this,q,p);},files:function(p){l.call(this,p);},file:function(p){l.call(this,p);}};function l(u,p){var y=this,w=arguments.length,z=this.options,x=this.element,r,q,s=0,v=0,t=new FormData();z.name=z.name||x.name||"upload";if(w===0){r=x.files||[];}else{if(w===1){r=u.length?u:[u];}else{q=u;}}if(q){t.append(z.name,q,p);}else{if(r&&r.length&&r.length>1){for(;v<r.length;v++){t.append(z.name+"[]",r[v]);}}else{if(r&&r.length===1){t.append(z.name,r[v]);}else{return z.oncomplete.call(x);}}}for(v in z.data){t.append(v,z.data[v]);}g.ajax({xhr:function(){var A=g.ajaxSettings.xhr();A.upload.onprogress=g.proxy(z.onprogress,x);return A;},headers:z.headers,url:z.action,type:"post",dataType:"json",data:t,processData:false,contentType:false}).done(g.proxy(z.onsuccess,x)).fail(g.proxy(z.onerror,x)).always(g.proxy(z.oncomplete,x));}function m(){var w=this,u=w.element,z=w.options,q=Date.now(),y,A,p,r,v="",t,x={};if(!u.value){return z.oncomplete.call(u);}z.name=z.name||u.name||"upload";g(u).appendTo(A);y=g('<iframe src="javascript:false" name="iframe-name-'+q+'" style="display:none;"></iframe>').appendTo(j);A=g('<form action="'+z.action+'" method="post" enctype="multipart/form-data" target="iframe-name-'+q+'" style="display:none;"></form>').appendTo(j);p=g('<input type="submit">').appendTo(A);r=g(u).clone(true,true).insertAfter(u);for(t in z.data){v+='<input type="hidden" name="'+t+'" value="'+z.data[t]+'">';}if(v){g(v).appendTo(A);}y.load(function(){var B=g(this).contents().text();try{x=g.parseJSON(B);}catch(C){}g.isEmptyObject(x)?z.onerror.call(u,x):z.onsuccess.call(u,x);s();}).error(function(){z.onerror.call(u,x);s();});p.trigger("click");function s(){y.remove();A.remove();z.oncomplete.call(u,x);}}function e(p){return g(p).prop("tagName")==="INPUT"&&g(p).attr("type")==="file";}})($,this);