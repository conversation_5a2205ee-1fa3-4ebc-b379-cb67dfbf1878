//循环遍历p a标签
/*function init(){
	var list=document.getElementById("list");
	var arr=list.getElementsByTagName("p");
	for(var i = 0; i < arr.length; i++){
		var array=arr[i].getElementsByTagName("a");
		if(array.length>0){
			for(var j = 0; j < array.length; j++){
				if(array[j].name=="buxian")array[j].className=array[j].className.substring(0,6)+" currentA";
				else  array[j].className=array[j].className.substring(0,6);
			}
		}
	}
}*/
$(function(){
	/*$("p a").click(function(){
		 document.forms[0].submit();
	 });*/
	 
	 $("#search").click(function(){
		 document.forms[0].submit();
	 });
	 $("#title5 select").change(function(){
		 document.forms[0].submit();
	 });
	
	
	/*//点击标题改变查询条件
	$("#qiuzhi").click(function(){
		init();
		$("#qiuzhi").removeClass().addClass("ac");
		$("#zhaopin").removeClass().addClass("ab");
		$("#title3").hide();
		$("#title4").show();
	});
	$("#zhaopin").click(function(){
		init();
		$("#qiuzhi").removeClass().addClass("ab");
		$("#zhaopin").removeClass().addClass("ac");
		$("#title3").show();
		$("#title4").hide();
	});
	//查询条件选择*/
	
	$("p a").click(function(){
		var parent = this.parentNode;
		//覆查询值
		if(parent.id=='title6'){$("#model").val($(this).index()-1);}
		if(parent.id=='title1')$("#carName").val($(this).index()-1);
		if(parent.id=='title3')$("#price").val($(this).index()-1);
		if(parent.id=='title4')$("#carAge").val($(this).index()-1);
		/*var arr=parent.getElementsByTagName("a");*/
		/*var titles=new Array("leibie","quyu","biaoqian","jialing","yuexin");
		var flag=true;//是否标题标记位
		for(var i = 0; i < titles.length; i++){
			if(this.id==titles[i]) {
				flag=flase;
				break;
			}
		}*/
		/*if(flag){
		  for (var i = 0; i < arr.length; i++) {
			  arr[i].className=arr[i].className.replace("currentA","");
	        }
		  if(this.id==""||this.id=="buxian")this.className=this.className+" currentA";
		}*/
		if($(this).index()>0)document.forms[0].submit();
	});
});
