function clearTitle(obj) {
	if (obj.value == "例：求职一份大板车司机的工作") {
		obj.value = "";
	}
}

function clearAge(obj) {
	if (obj.value == "请输入数字") {
		obj.value = "";
	}
}

function clearYears(obj) {
	if (obj.value == "请输入数字") {
		obj.value = "";
	}
}


$(document).ready(function() {
	$("#describe").keyup(function() {
		var str = $('#describe').val();
		if (str.length > 300)
			$('#describe').val(str.substring(0, 300));
	});
	$("#resetId").click(function() {
		// $("span").html("");
	});
	$("#fabu").mousemove(function() {
		$("#fabu").css("background-color", "#fea000");
	});
	$("#fabu").mouseout(function() {
		$("#fabu").css("background-color", "#fd8000");
	});
	$("#fabu").mousedown(function() {
		$("#fabu").css("background-color", "#fe3000");
	});
	$("#fabu").mouseup(function() {
		$("#fabu").css("background-color", "#fd8000");
	});
	$("#resetId").mousemove(function() {
		$("#resetId").css("background-color", "#fea000");
	});
	$("#resetId").mouseout(function() {
		$("#resetId").css("background-color", "#fd8000");
	});
	$("#resetId").mousedown(function() {
		$("#resetId").css("background-color", "#fe3000");
	});
	$("#resetId").mouseup(function() {
		$("#resetId").css("background-color", "#fd8000");
	});
});

function checkTitle() {
	var flag = false;
	var title = document.getElementById("title").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (title == "" || title == "例：求职一份大板车司机的工作") {
		document.getElementById("put1").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>求职标题不能为空！</span>";
	} else if (title.length < 2 || title.length > 20) {
		document.getElementById("put1").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>标题长度2——20</span>";
	} else {
		document.getElementById("put1").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		flag = true;
	};
	return flag;
}


function checkTelName() {
	var flag = false;
	var telName = document.getElementById("telName").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (telName == "") {
		document.getElementById("put14").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>联系人不能为空</span>";
	} else {
		document.getElementById("put14").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		flag = true;
	};
	return flag;
}

function checkAge() {
	var flag = false;
	var age = document.getElementById("age").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (!age.match(/^\+?[1-9][0-9]*$/)) {
		document.getElementById("put8").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写正整数!</span>";
	} else if (age < 18 || age > 60) {
		document.getElementById("put8").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>年龄填写范围18-60!</span>";
	} else {
		flag = true;
		document.getElementById("put8").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}
	return flag;
}

function checkYears() {
	var flag = false;
	var years = document.getElementById("years").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (!years.match(/^\+?[1-9][0-9]*$/)) {
		document.getElementById("put9").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写正整数!</span>";
	} else if (years < 0 || years > 45) {
		document.getElementById("put9").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>驾龄填写范围0-45!</span>";
	} else {
		flag = true;
		document.getElementById("put9").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}
	return flag;
}

function checkTelephone() {
	var flag = false;
	var telephone = document.getElementById("telephone").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (telephone == "" || telephone == "请写有效联系电话") {
		document.getElementById("put15").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>联系电话不能为空!</span>";
	} else {
		flag = true;
		document.getElementById("put15").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	};
	return flag;
}

function checkAddress() {
	var flag = false;
	var province = document.getElementById("province").value;
	if (province == "") {
		document.getElementById("put11").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>工作地址不能为空!</span>";
	} else {
		flag = true;
		document.getElementById("put11").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}
	return flag;
}

function checkBirthAddress() {
	var flag = false;
	var province = document.getElementById("birthProvince").value;
	if (province == "") {
		document.getElementById("put12").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>籍贯不能为空!</span>";
	} else {
		flag = true;
		document.getElementById("put12").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}
	return flag;
}

function checkQQ() {
	var flag = false;
	var qq = document.getElementById("qq").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "");
	if (qq == "" || qq == "0") {
		flag = true;
		document.getElementById("put16").innerHTML = "<p class='pp'></p>";
	} else if (isNaN(qq)) {
		flag = false;
		document.getElementById("put16").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效(数字)qq</span>";
	} else if (qq < 0 || qq.length > 11) {
		flag = false;
		document.getElementById("put16").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>请填写有效(数字)qq</span>";
	} else {
		document.getElementById("put16").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
		flag = true;
	}
	return flag;
}
/*function checkSaveDay(){
    var flag=true;
	var saveDay = document.getElementById("date_3").value.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, ""); 
	if(saveDay!=null){
	document.getElementById("put17").innerHTML = "<p class='pp'><img src='../fore/image/zq.png'></p>";
	}else if (saveDay == ""||saveDay=="请选择有效截止日期") {
		document.getElementById("put17").innerHTML = "<p class='pp'><img src='../fore/image/cw.png'></p><span style='color:#F00;font-size:14px;line-height:40px;'>信息有效截止日期不能为空</span>";
		flag=false;
	}
	return flag;
}*/
function checkAll() {
	if (checkTitle() && checkAddress() && checkBirthAddress() && checkTelName() && checkAge() && checkYears() && checkTelephone() && checkQQ() /*&&checkSaveDay()*/ ) {
		return true;
	} else {
		return false;
	}
}