<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!doctype html>
<html>
<head>
<base href="<%=basePath%>">
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
<meta http-equiv="X-UA-Compatible" content="IE=Edge，chrome=1">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-touch-fullscreen" content="YES">
<meta content="telephone=no" name="format-detection" />
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<script src="fore/js/jquery-1.11.0.min.js"></script>
<!-- <title>二维码车辆认证</title> -->
<link href="fore/style/erweima_base.css" type="text/css" rel="stylesheet">
<link href="fore/style/erweima_page.css" type="text/css" rel="stylesheet">
<script type="text/javascript">
function errorMsg(id, Msg) {
    var err = $(".error-tips"), t = $("#" + id).offset().top, w;
    err.empty().html(Msg);
    w = err.outerWidth();
    err.css({"marginLeft" : -w / 2 + "px","top" : t - 50 + "px"}).fadeIn();
    setTimeout(function() {err.fadeOut();}, 2000);
}

$(function(){
	$("#upload-cmt").click(function(){		
		window.location.href='<%=basePath%>fore/erweima/car/init?userId=${car.userId}';	
		$('#upload-cmt').attr('disabled',"true");
	});
});
</script>

<style type="text/css">

* {
  margin:0;
  padding:0;	
	}

/*错误提示 begin*/
.error-tips{z-index:10001;display:none;position:absolute;left:50%;padding:5px;border:2px solid #838b8b;border-radius:5px;-moz-border-radius:5px;-webkit-border-radius:5px;background:#838b8b;white-space:nowrap;font-size:14px;color: white;}
/*错误提示 end*/
.choose-cities{z-index:10001;display:none;position:absolute;left:50%;padding:5px;border:2px solid #5798ff;border-radius:5px;-moz-border-radius:5px;-webkit-border-radius:5px;background:#f8fcff;white-space:nowrap;font-size:14px;}
.choose-cities table{border-collapse:collapse;}
.choose-cities table tr td{
	border: 1px solid green; /*边框*/
	width:30px;             /*单元格宽度*/
	height:30px;             /*单元格高度*/
	text-align:center;       /*单元格文字居中对齐*/
	background-color: #dcdcdc;
	font-weight: bold;
}
</style>
</head>

<body>
<div class="wrap">
  <header>
    <h1>车辆认证</h1>
  </header>
  
  <div class="main-content">
  
   <p class="prompt-p tc">您的信息已提交成功！<br>请保持电话畅通，工作人员会尽快联系您！</p>
   
   <!--按钮-->
    <div class="button-div">
        <button id="upload-cmt">去认证更多车辆</button>
    </div>

</div>
</div>
<div class="error-tips"></div>
<c:if test="${!empty errMsg }">
<script type="text/javascript">
errorMsg("upload-cmt", "${errMsg}");
</script>
</c:if>
</body>
</html>
