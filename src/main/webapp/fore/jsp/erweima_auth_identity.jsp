<%@page import="com.tyt.util.AppConfig"%>
<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html>
<html>
<head>
<base href="<%=basePath%>">
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-touch-fullscreen" content="YES">
<meta content="telephone=no" name="format-detection" />
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<!--<title>实名认证</title>-->
<link href="fore/style/erweima_base.css" type="text/css" rel="stylesheet">
<link href="fore/style/erweima_page.css" type="text/css" rel="stylesheet">
<script src="fore/js/jquery-1.11.0.min.js"></script>
<script type="text/javascript" src="fore/jsp/test_files/megapix-image.js"></script>
<script type="text/javascript" src="fore/jsp/test_files/IdCard-Validate.js"></script>
<script type="text/javascript">
//判断访问终端
var browser={
    versions:function(){
        var u = navigator.userAgent;
        return {
            trident: u.indexOf('Trident') > -1, //IE内核
            presto: u.indexOf('Presto') > -1, //opera内核
            webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
            gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1,//火狐内核
            mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或者uc浏览器
            iPhone: u.indexOf('iPhone') > -1 , //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
            webApp: u.indexOf('Safari') == -1, //是否web应该程序，没有头部与底部
            weixin: u.toLowerCase().match(/MicroMessenger/i) == 'micromessenger', //是否微信
            qq: u.match(/\sQQ/i) == " qq" //是否QQ
        };
    }()
};

//当前选择的input[type=file]
var inputFile = null;
$(function(){
	
	$("#upload-cmt").click(function(){
		var receiverNameVal = $("#name").val(); // 机主姓名
	    var idCardVal = $("#idcard").val(); // 证件号码
	    var temp = CustInfoCheck.checkReceiverName(receiverNameVal)&& CustInfoCheck.checkIdCard(idCardVal);
		if(!temp)return false;
	    var f2 = $("#main_url");
	    if(!(f2.attr("suc"))){
	    	errorMsg("upload-cmt", "请上传您的手持身份证照片");
	        return false;
	    }
	    if(!$('#isAccept').is(':checked')){
	    	errorMsg("upload-cmt", "请选择特运通用户协议");
	        return false;
	    }
	    document.getElementById("form1").submit();
	    $('#upload-cmt').attr('disabled',"true");
	});
	//选择文件
	$("input[type='file']").change(function(evt){
		$("img.upload-imgs").attr("src","fore/images/loading.gif").attr("width","50px").attr("height","50px");
	    if(evt.target.files.length == 0){
	        return;
	    }
	    var f = evt.target.files[0], 
	        $this = $(this), 
	        type = this.value.substr(this.value.lastIndexOf(".")).toLowerCase() ,
	        type2 = f.type;
	    if(type2==""){
	        errorMsg("upload-cmt", "照片格式不正确");
	        return;
	    }
	    //选择的图片非bmp、jpg、jpeg时，清空文件选择，展示提示框
	    if(!(type == ".png" ||type == ".jpg" || type == ".bmp" || /^image\/(jpeg|jpg|bmp|png)$/.test(type2))){
	        errorMsg("upload-cmt", "照片格式不正确");
	        //清空file
	        $this.wrap('<form>').closest('form').get(0).reset();
	        $this.unwrap();
	        inputFile = $this;
	        return;
	    }
	    
	    var reader = new FileReader();
	    reader.onload = (function (f) {
	        return function (e) {

	            //获取照片的URL对象（base64）
	            var dataURL = e.target.result;
	            var preview = $this.siblings("img.upload-imgs");

	            //创建Image对象
	            var image = new Image();
	            image.src = dataURL;
	            
	            image.onload = function() {
	                var resized = resizeMe(image);
	                //删除原来的图片
	                $this.siblings(".upload-files").remove();
	                var newinput = $("<input>");
	                //将canvas的返回结果(base64的图片数据)放到新建的input中
	                newinput.attr("type", "text").attr("style","display:none")
	                    .attr("value", resized).attr("class", "upload-files")
	                    .attr("id", $this.attr("id")+"2").attr("name", $this.attr("id")+"_2");
	                $this.parent().append(newinput);
	                //预览图片
	                //preview.attr("src", resized).attr("width","255px").attr("height","");
                    $("img.upload-imgs").attr("src",resized).attr("width","255px").attr("height","");
	                var imgIcon = $this.parent(".img-w");
	                /* imgIcon.find(".camera").hide();
	                imgIcon.find(".camera-edit").show(); */
	                imgIcon.find(".watermark").show();
	                $this.attr("suc",true);
	                $this.wrap('<form>').closest('form').get(0).reset();
	                $this.unwrap();
	            }
	        };
	    })(f);
	    //读取文件的缓冲数组流，读取完毕后执行onload
	    reader.readAsDataURL(f);
	});
	
	var errMsg="${errMsg}";
	if($.trim(errMsg)!='pass'&&!isEmpty($.trim(errMsg))){
		errorMsg("upload-cmt",errMsg);
        return;
	};	
});



function errorMsg(id, Msg) {
    var err = $(".error-tips"), t = $("#" + id).offset().top, w;
    err.empty().html(Msg);
    w = err.outerWidth();
    err.css({"marginLeft" : -w / 2 + "px","top" : t - 50 + "px"}).fadeIn();
    setTimeout(function() {err.fadeOut();}, 2000);
}

function isEmpty(browseHistoryValue) {
    return typeof browseHistoryValue === 'undefined'
        || browseHistoryValue === 'undefined|undefined' || browseHistoryValue === null
        || browseHistoryValue === '' || browseHistoryValue === '|';
};

//通过canvas截取固定大小（宽度为max_width，高自适应）的图片
function resizeMe(img,maxW,maxH,nocheck) {
    var canvas = document.createElement('canvas');
    var width = img.width;
    var height = img.height;
    var max_width = maxW || 640;
    var max_height = maxH || 640;
    var max_size = 300;//k
    if (width > max_width) {
        height *= max_width / width;
        height = Math.round(height);
        width = max_width;
    }
    if(height > max_height){
        width *= max_height / height;
        width = Math.round(width);
        height = max_height;
    }
    
    //将图片放入canvas，并重置canvas大小
    if(browser.versions.ios || browser.versions.webApp){
        var mpImg = new MegaPixImage(img);
        mpImg.render(canvas, { width: width, height: height });
    }else{
        canvas.width = width;
        canvas.height = height;
        var ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, width, height);
    }

    var res, quality = 1,resSize,ratio = 1;
    res = canvas.toDataURL("image/jpeg",quality); // 截取canvas对应的jpg图片，并且画质为70%（默认就是70%，可以改变）

    // Android 2.x, Android 4.1.2 与 4.3 的 toDataURL 不支持jpeg格式；
    if(res.substr(0,6) == "data:," || res.substr(0,"data:image/png".length) == "data:image/png"){
        var encoder = new JPEGEncoder();
        res = encoder.encode(canvas.getContext("2d").getImageData(0,0,width,height), quality * 100, true);
    }
    resSize = Math.ceil(res.length/1024); //k
    if(resSize > max_size && !nocheck){
        ratio = Math.ceil(Math.sqrt(max_size/resSize)*100)/100;
        if(ratio >= .9){
            ratio -= .1;
        }
        res = resizeMe(img,max_width*ratio,max_height*ratio,true);
    }
    return res;
}

var CustInfoCheck = {
	    // 机主姓名验证
	    checkReceiverName : function(receiverName) {
	        psptTypeCode = "02";
	        if ($.trim(receiverName) == "" || $.trim(receiverName) == "请输入您的真实姓名") {
	            errorMsg("upload-cmt", "请填写您的真实姓名");
	            return false;
	        }
	        if (chineseLen($.trim(receiverName)) > 10) {
	            errorMsg("upload-cmt", "真实姓名过长，请您最多输入10个汉字。");
	            return false;
	        }
	        if (chineseLen($.trim(receiverName)) < 2) {
	            errorMsg("upload-cmt", "真实姓名必须至少包含2个汉字");
	            return false;
	        }
	        if (stringHelper.checkScript(receiverName)) {
	            errorMsg("upload-cmt", "真实姓名包含非法字符。");
	            return false;
	        }
	        return true;
	    },
	    // 证件号码校验
	    checkIdCard : function(idCardVal) {
	        // 身份证
	        if (stringHelper.checkScript(idCardVal)) {
	            errorMsg("upload-cmt", "证件号码包含非法字符");
	            return false;
	        } else if (!IdCardValidate(idCardVal)) {
	            errorMsg("upload-cmt", "请输入正确的身份证号");
	            return false;
	        }
	        return true;
	    },
	    checkUploadImg :function(){
	        if(!$("#main_url_2").hasClass("hide")){
	            errorMsg("upload-cmt", '请上传您的手持证件照片');
	            return false;
	        }
	        return true;
	    }
}

//判断汉字个数
function chineseLen(txt) {
    var n = 0;
    for ( var i = 0, len = txt.length; i < len; i++) {
        if (/[\u4E00-\u9FA5]/.test(txt.charAt(i)))
            n++;
    }
    return n;
}

//判断非法字符
function stringHelper() {
}

stringHelper.checkScript = function(text) {
    var flag = false;
    var scriptWord = "<|>|script|alert|{|}|#|$|'|\"|:|;|&|*|@@|%|^|?";
    var words = scriptWord.split('|');
    for ( var i = 0; i < words.length; i++) {
        if (text.indexOf(words[i]) != -1) {
            flag = true;
            break;
        }
    }
    return flag;
};

function isEmpty(browseHistoryValue) {
    return typeof browseHistoryValue === 'undefined'
        || browseHistoryValue === 'undefined|undefined' || browseHistoryValue === null
        || browseHistoryValue === '' || browseHistoryValue === '|';
};
</script>
<style>
/*错误提示 begin*/
.error-tips{z-index:10001;display:none;position:absolute;left:50%;padding:5px;border:2px solid #838b8b;border-radius:5px;-moz-border-radius:5px;-webkit-border-radius:5px;background:#838b8b;white-space:nowrap;font-size:14px;color: white;}
/*错误提示 end*/
</style>
</head>
<body>
<form id="form1" action="fore/erweima/user/auth" method="post">
<input type="hidden"  id="userId" name="userId" value="${userId}">
<input type="hidden"  id="formTocken" name="formTocken" value="${formTocken}">
<div class="wrap">
  <header><h1>发货实名认证</h1></header>
  <div class="main-content">
   <div class="car-au">
    <ul>
     <li>
        <label>请您输入真实姓名：</label>
        <input type="text" class="input" placeholder="请输入您的真实姓名" id="name" name="realName" value="${identity.realName}">
     </li>
     <li>
        <label>请您输入身份证号：</label>
        <input type="text" class="input" placeholder="请输入您的身份证号" id="idcard" name="identity" value="${identity.identity}">
     </li>
     <li>
        <label>请您上传身份照片：</label>
        <div class="img-upload-w">
         <div class="img-upload-sec">
          <div class="img-w bd1">
      <img class="upload-imgs upload-imgs1" src="fore/images/shenfenzheng.png" width="255" alt="">
      <div class="file-input-w">
         <div class="camera">
              <img src="fore/images/xiangji.png" width="40" height="33" alt="">
          </div>
      </div>
      <input class="file-input select-file" type="file" id="main_url" name="files[]" accept="image/*">
    </div>
         </div>
        </div>
     </li>
     <li class="mt10"><input type="checkbox" checked="checked" name="isAccept" id="isAccept">我接受<a class="text-color" href="fore/jsp/agreement.html">《特运通用户协议》</a></li>
  </ul>
 </div> 
   <div class="submit-button-div"><button id="upload-cmt">提交认证</button></div>
  </div>
</div>
</form>
<div class="error-tips"></div>
</body>
</html>
