<%@page import="com.tyt.util.AppConfig"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>实名认证</title>
</head>
<body>
	<jsp:include page="auth_head.jsp"></jsp:include>

	<table width="100%" border="1">
		<tr height="100%">
			<td width="30%" align="center">认证信息</td>
			<td width="70%" align="center">实名认证</td>
		</tr>

		<tr height="100%">
			<td width="30%">

				<table width="100%">
				
					<tr>
						<td width="100%" colspan="1" align="center">
							<table width="100%" border="1">

								<tr>
									<td>账号：</td>
									<td>${user.cellPhone }</td>
									<td><c:if test="${user.verifyFlag == 0}">未认证</c:if> <c:if
											test="${user.verifyFlag  == 1}">已认证</c:if> <c:if
											test="${user.verifyFlag  == 2}">认证中</c:if></td>
								</tr>


							</table>
						</td>

					</tr>

				</table> <jsp:include page="auth_problem.jsp"></jsp:include>
			</td>

			<td width="70%" align="center">
				<form action="<%=request.getContextPath()%>/fore/user/identity/save"
					name="identityForm" method="post" enctype="multipart/form-data"
					id="form1">
					<Table width="100%" align="center">
						<Tr align="center">
							<td align="center" style="color: red" colspan="3">${message}</Td>
						</Tr>
						<Tr align="center">
							<td align="right" colspan="1">姓名</td>
							<td align="left" colspan="1">${userIdentity.realName} </Td>
							<td align="left" height="30"><div id="realNameTip"></div></td>
						</Tr>
						<Tr>
							<td align="right" colspan="1">身份证号</td>
							<td align="left" colspan="1">${userIdentity.identity}
							</Td>
							<td align="left"><div id="identityTip" style="width: 280px"></div></td>
						</Tr>

						<Tr>
							<td align="right" colspan="1">身份证正面照</td>
							<td align="left" colspan="1">
								<div>
									<img id="ImgPr2" width="200" height="200"
										src="<%=AppConfig.getProperty("picture.path.domain") %>${userIdentity.mainurl}" />
								</div>
							</Td>
							<td align="left"><div id="mainUrlPicTip"
									style="width: 280px"></div></td>
						</Tr>
						

					</Table>
				</form>
			</td>

		</tr>
	</table>
</body>
</html>