<%@ page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<link rel="stylesheet" href="<%=request.getContextPath()%>/fore/css/zhaopin.css" type="text/css" />
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/emp.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/cityquery.js"></script>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<title>特运通-招聘与求职信息平台</title>
<script type="text/javascript">
function toDetail(id,pageNo){
	var form=document.forms[0];
	form.action="<%=request.getContextPath()%>/fore/seek/detail?id="+id+"&pageNo="+pageNo;
	form.submit();
}
</script> 
</head>
<body>
<div class="container">
<form  action="<%=request.getContextPath()%>/fore/seek/list" method="post">
 <div class="logobox"><!--header-->
    	<img src="<%=request.getContextPath()%>/fore/image/zp.png">
    	<!-- style="margin-left:2%" -->
        <div  class="searchBox">
        	<input value="${employeeQueryBean.search}" name="search">
            <a href="#" id="search"></a>
        </div>
        <div class="logoright"><a href="<%=request.getContextPath()%>/fore/toinsert?to=emp">免费发布招聘</a><a href="<%=request.getContextPath()%>/fore/toinsert?to=seek">免费发布求职</a></div>
 </div>
 <div class="listbox" id="list">
<div class="list">
<a  id="zhaopin" <c:if test="${employeeQueryBean.distinguish==1}">class="ac"</c:if> <c:if test="${employeeQueryBean.distinguish!=1}">class="ab"</c:if> href="<%=request.getContextPath()%>/fore/employee/list?distinguish=1">招聘信息列表</a>
<a  id="qiuzhi" <c:if test="${employeeQueryBean.distinguish==2}">class="ac"</c:if> <c:if test="${employeeQueryBean.distinguish!=2}">class="ab"</c:if>  href="<%=request.getContextPath()%>/fore/seek/list?distinguish=2">求职信息列表</a>
</div>
<%-- <p id="title1">
  <a class="list01" id="leibie">类别：</a> 
  <a name="buxian"  href="#"  class="list02<c:if test="${employeeQueryBean.position==0}"> currentA</c:if>">不限</a>
  <a  href="#"  class="list05<c:if test="${employeeQueryBean.position==1}"> currentA</c:if>">&nbsp;大板车司机</a>
  <a  href="#"  class="list15<c:if test="${employeeQueryBean.position==2}"> currentA</c:if>">17.5米平板车司机</a>
  <a  href="#"  class="list15<c:if test="${employeeQueryBean.position==3}"> currentA</c:if>">三线六桥板车司机</a>
  <a  href="#"  class="list07<c:if test="${employeeQueryBean.position==4}"> currentA</c:if>">特种车司机</a>
  <a  href="#"  class="list04<c:if test="${employeeQueryBean.position==5}"> currentA</c:if>">临时司机</a>
  <a  href="#"  class="list05<c:if test="${employeeQueryBean.position==6}"> currentA</c:if>">其他车辆司机</a>
</p>
<p id="title3">
 <a class="list01" id="leibie">职务：</a>
 <a name="buxian"  href="#"  class="list02<c:if test="${employeeQueryBean.duty==0}"> currentA</c:if>">不限</a>
  <a  href="#"  class="list05<c:if test="${employeeQueryBean.duty==1}"> currentA</c:if>">&nbsp;主司机</a>
  <a  href="#"  class="list07<c:if test="${employeeQueryBean.duty==2}"> currentA</c:if>">副司机</a>
</p>--%>
<p id="title2">
  <a class="list01" id="quyu">区域：</a><a href="#"  class="list02 currentA" name="buxian" onclick="area();">不限</a>
  &nbsp;&nbsp;<select id="province" name="province"style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list09"><option value="">选择省份</option></select>
  <select id="city" name="city" style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list10"><option value="">选择市</option></select>
  <select id="county" name="county" style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list11"><option value="">选择县区</option></select>
  <input type="hidden" id="province00" value="${employeeQueryBean.province }">
  <input type="hidden" id="city00" value="${employeeQueryBean.city }">
  <input type="hidden" id="county00" value="${employeeQueryBean.county }"></p>
<%--<c:if test="${employeeQueryBean.distinguish==1}"> 
 <p id="title3">
   <a class="list01" id="biaoqian">标签：</a>
   <a href="#"  class="list02<c:if test="${employeeQueryBean.subsidy==''}"> currentA</c:if>" name="buxian">不限</a>
   <a href="#"  class="list07<c:if test="${employeeQueryBean.subsidy=='五险一金'}"> currentA</c:if>">五险一金</a>
   <a href="#"  class="list07<c:if test="${employeeQueryBean.subsidy=='年底双薪'}"> currentA</c:if>">年底双薪</a>
   <a href="#"  class="list07<c:if test="${employeeQueryBean.subsidy=='交通补助'}"> currentA</c:if>">交通补助</a>
   <a href="#"  class="list07<c:if test="${employeeQueryBean.subsidy=='加班补助'}"> currentA</c:if>">加班补助</a>
   <a href="#"  class="list07<c:if test="${employeeQueryBean.subsidy=='周末双休'}"> currentA</c:if>">周末双休</a>
   <a href="#"  class="list02<c:if test="${employeeQueryBean.subsidy=='包吃'}"> currentA</c:if>">包吃</a>
   <a href="#"  class="list02<c:if test="${employeeQueryBean.subsidy=='包住'}"> currentA</c:if>">包住</a>
   <a href="#"  class="list02<c:if test="${employeeQueryBean.subsidy=='餐补'}"> currentA</c:if>">餐补</a>
   <a href="#"  class="list02<c:if test="${employeeQueryBean.subsidy=='房补'}"> currentA</c:if>">房补</a>
   <a href="#"  class="list02<c:if test="${employeeQueryBean.subsidy=='话补'}"> currentA</c:if>">话补</a>
</p>
</c:if>
  <p id="title4">
	  <a class="list01" id="jialing" >驾龄：</a>
	  <a href="#"  class="list02<c:if test="${employeeQueryBean.years==0}"> currentA</c:if>" name="buxian">不限</a>
	  <a href="#"  class="list04<c:if test="${employeeQueryBean.years==1}"> currentA</c:if>">&nbsp;2年以下</a>
	  <a href="#"  class="list06<c:if test="${employeeQueryBean.years==2}"> currentA</c:if>">2-5年</a>
	  <a href="#"  class="list06<c:if test="${employeeQueryBean.years==3}"> currentA</c:if>">5-7年</a>
	  <a href="#"  class="list04<c:if test="${employeeQueryBean.years==4}"> currentA</c:if>">7-10年</a>
	  <a href="#"  class="list04<c:if test="${employeeQueryBean.years==5}"> currentA</c:if>">10-15年</a>  
	  <a href="#"  class="list04<c:if test="${employeeQueryBean.years==6}"> currentA</c:if>">15-20年</a>  
	  <a href="#"  class="list04<c:if test="${employeeQueryBean.years==7}"> currentA</c:if>">20-25年</a>  
	  <a href="#"  class="list04<c:if test="${employeeQueryBean.years==8}"> currentA</c:if>">25-30年</a> 
	  <a href="#"  class="list04<c:if test="${employeeQueryBean.years==9}"> currentA</c:if>">30年以上</a>                                                                 
  </p>
  <p id="title5">
  <a class="list01" id="yuexin">月薪：</a>
  <a name="buxian" href="#"  class="list02<c:if test="${employeeQueryBean.salary==0}"> currentA</c:if>">不限</a>
  <a href="#"  class="list02<c:if test="${employeeQueryBean.salary==1}"> currentA</c:if>">&nbsp;面议    </a>
  <a href="#"  class="list03<c:if test="${employeeQueryBean.salary==2}"> currentA</c:if>">5000以下 </a>
  <a href="#"  class="list03<c:if test="${employeeQueryBean.salary==3}"> currentA</c:if>">5000-6000</a>
  <a href="#"  class="list03<c:if test="${employeeQueryBean.salary==4}"> currentA</c:if>">6000-7000</a>
  <a href="#"  class="list03<c:if test="${employeeQueryBean.salary==5}"> currentA</c:if>">7000-8000</a>
  <a href="#"  class="list03<c:if test="${employeeQueryBean.salary==6}"> currentA</c:if>">8000以上 </a>
  </p> --%>
    <P id="title6"><span>更多</span>
  <select class="list12" style="margin-right:10px;width:110px; height:25px; font-size:14px;" name="position">
	                    <option value="">司机类型</option>
	                    <option value="<%=Constant.POSITION1%>"<c:if test="${employeeQueryBean.position==1}">selected</c:if>>13.5米大板车司机</option>      
	                    <option value="<%=Constant.POSITION2%>"<c:if test="${employeeQueryBean.position==2}">selected</c:if>>17.5米大板车司机</option>      
	                    <option value="<%=Constant.POSITION3%>"<c:if test="${employeeQueryBean.position==3}">selected</c:if>>6.8米/9.6米单机板车司机</option>   
	                    <option value="<%=Constant.POSITION4%>"<c:if test="${employeeQueryBean.position==4}">selected</c:if>>特种车司机</option>           
	                    <option value="<%=Constant.POSITION5%>"<c:if test="${employeeQueryBean.position==5}">selected</c:if>>临时司机</option>            
	                    <option value="<%=Constant.POSITION6%>"<c:if test="${employeeQueryBean.position==6}">selected</c:if>>其他车辆司机</option>          
	 </select>
	 <select class="list12" style="margin-right:10px;width:105px; height:25px; font-size:14px;" name="salary">
	                    <option value="">薪资待遇</option>
	                    <option value="<%=Constant.SALARY1%>"<c:if test="${employeeQueryBean.salary==1}"> selected</c:if>>面议</option>
                        <option value="<%=Constant.SALARY2%>"<c:if test="${employeeQueryBean.salary==2}"> selected</c:if>>5000以下 </option>
                        <option value="<%=Constant.SALARY3%>"<c:if test="${employeeQueryBean.salary==3}"> selected</c:if>>5000-6000</option>
                        <option value="<%=Constant.SALARY4%>"<c:if test="${employeeQueryBean.salary==4}"> selected</c:if>>6000-7000</option> 
                        <option value="<%=Constant.SALARY5%>"<c:if test="${employeeQueryBean.salary==5}"> selected</c:if>>7000-8000</option> 
                        <option value="<%=Constant.SALARY6%>"<c:if test="${employeeQueryBean.salary==6}"> selected</c:if>>8000以上 </option>   
	</select>
	 <select class="list12" style="margin-right:10px;width:90px; height:25px; font-size:14px;" name="duty"> 
	                   <option value="">职务</option>
	                   <option value="<%=Constant.DUTY1%>"<c:if test="${employeeQueryBean.duty==1}">selected</c:if>>主司机 </option>   
	                   <option value="<%=Constant.DUTY2%>"<c:if test="${employeeQueryBean.duty==2}">selected</c:if>>副司机 </option>          
	 </select>                                          
	 <select class="list12" style="margin-right:10px;width:85px; height:25px; font-size:14px;" name="years"> 
	                  <option value="">驾龄</option>
                      <option value="<%=Constant.YEARS1%>"<c:if test="${employeeQueryBean.years==1}">selected</c:if>>5年以下</option>                                                                                  
                      <option value="<%=Constant.YEARS2%>"<c:if test="${employeeQueryBean.years==2}">selected</c:if>>5-10年</option>                                                                                       
                      <option value="<%=Constant.YEARS3%>"<c:if test="${employeeQueryBean.years==3}">selected</c:if>>10年以上</option>                                                                                       
	  </select>
  <select class="list10" style="margin-right:10px;width:85px; height:25px; font-size:14px;"  name="age">
                    <option value="">年龄</option>
					<option value="<%=Constant.AGE1%>"<c:if test="${employeeQueryBean.age==1}">selected</c:if>>18-30岁</option>
					<option value="<%=Constant.AGE2%>"<c:if test="${employeeQueryBean.age==2}">selected</c:if>>30-40岁</option>
					<option value="<%=Constant.AGE3%>"<c:if test="${employeeQueryBean.age==3}">selected</c:if>>40-50岁</option>
					<option value="<%=Constant.AGE4%>"<c:if test="${employeeQueryBean.age==4}">selected</c:if>>50岁以上</option>
					
	</select>
<select class="list12" style="margin-right:10px;width:60px; height:25px; font-size:14px;" name="education">
					<option  value="">学历 </option>
					<option  value="<%=Constant.EDUCATION0%>"<c:if test="${employeeQueryBean.education==0}">selected</c:if>>无要求  </option>
					<option  value="<%=Constant.EDUCATION1%>"<c:if test="${employeeQueryBean.education==1}">selected</c:if>>高中  </option>
					<option  value="<%=Constant.EDUCATION2%>"<c:if test="${employeeQueryBean.education==2}">selected</c:if>>技校  </option>
					<option  value="<%=Constant.EDUCATION3%>"<c:if test="${employeeQueryBean.education==3}">selected</c:if>>中专  </option>
					<option  value="<%=Constant.EDUCATION4%>"<c:if test="${employeeQueryBean.education==4}">selected</c:if>>大专  </option>
					<option  value="<%=Constant.EDUCATION5%>"<c:if test="${employeeQueryBean.education==5}">selected</c:if>>本科  </option>
					<option  value="<%=Constant.EDUCATION6%>"<c:if test="${employeeQueryBean.education==6}">selected</c:if>>其它  </option>
</select>
	
 </P>                  
 </div> 
 <input type="hidden" name="duty"  id="duty" value="${employeeQueryBean.duty}"/>
 <input type="hidden" name="education"  id="education" value="${employeeQueryBean.education}"/>
 <input type="hidden" name="position"  id="position" value="${employeeQueryBean.position}"/>
 <input type="hidden" name="age"  id="age" value="${employeeQueryBean.age}"/>
 <%-- <input type="hidden" name="subsidy"   id="subsidy" value="${employeeQueryBean.subsidy}"/> --%>
 <input type="hidden" name="years"     id="years" value="${employeeQueryBean.years}"/>
 <input type="hidden" name="salary"    id="salary" value="${employeeQueryBean.salary}"/>
 <input type="hidden" name="distinguish"    id="distinguish" value="${employeeQueryBean.distinguish}"/>
 <input type="hidden" name="pageNo" value="${pageNo }"/>
</form>
<div class="bj">
           <c:forEach items="${seeks}" var="seek" varStatus="i">
           <c:choose>
           <c:when test="${user.cellPhone==seek.cellPhone}">
           <div style="background-color:#e1e8eb;">
            <div onmouseout="this.className='liebiao'" onmouseover="this.className='liebiao01'" onclick="toDetail(${seek.id},${(pageNo-1)*8+i.index});">
					<div class="neirongbox">
						<form class="neirong01">
						       <div class="title"><a class="element1">${seek.title}<img src="<%=request.getContextPath()%>/fore/image/zhishi.png"></a>
						       <span class="element6">自己发布</span>
						       <a class="element7" href="<%=request.getContextPath()%>/fore/seek/delete?id=${seek.id}" onclick="{if(confirm('确定要删除这条信息吗?')){return true;}return false;}">删除</a>
						       </div>
                               <a  class="element2">联系人：${seek.telName}</a> 
                               <a  class="element3">联系电话：<span>${seek.telephone}</span></a>
					           <a  class="element4">工作地址：${seek.province}${seek.city}${seek.county}</a>
						</form>
							<div class="neirong02">发布日期：<fmt:formatDate value="${seek.ctime}" type="both"/></div>
					</div>
					</div>
					</div>
           </c:when>
           <c:otherwise>
           <div onmouseout="this.className='liebiao'" onmouseover="this.className='liebiao01'" onclick="toDetail(${seek.id},${(pageNo-1)*8+i.index});">
					<div class="neirongbox">
						<form class="neirong01">
						       <div class="title"><a class="element1">${seek.title}<img src="<%=request.getContextPath()%>/fore/image/zhishi.png"></a>
						       <c:forEach items="${seekCollects}" var="seekCollects">	
						       <c:if test="${seekCollects.seekId==seek.id}"><a class="element5">已浏览</a></c:if>
						       </c:forEach> 
						       </div>
                               <a  class="element2">联系人：${seek.telName}</a> 
                               <a  class="element3">联系电话：<span>${seek.telephone}</span></a>
					           <a  class="element4">工作地址：${seek.province}${seek.city}${seek.county}</a>
						</form>
							<div class="neirong02">发布日期：<fmt:formatDate value="${seek.ctime}" type="both"/></div>
					</div>
					</div>
           </c:otherwise>
           </c:choose>
			</c:forEach>
		</div> 
<jsp:include  page="page.jsp" flush="true"/> 
</div>
</body>
</html>
