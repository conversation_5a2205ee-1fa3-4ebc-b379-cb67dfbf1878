<%@page import="com.tyt.util.AppConfig"%>
<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript" src="fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="fore/js/baidutongji.js"></script>
<link href="fore/style/base.css" type="text/css" rel="stylesheet">
<link href="fore/style/common.css" type="text/css" rel="stylesheet">
<link href="fore/style/page.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="fore/js/baidutongji.js"></script>
<link rel="stylesheet" type="text/css" href="fore/css/privateCenter.css">
<title>个人中心--个人资料</title>
<script type="text/javascript">
	function goURL(page){
		  window.location.href="<%=request.getContextPath()%>/pc/secondcar/list?pageNo="+page+"&status="+getStatus();		
	  }
</script>
</head>

<body>
 <div class="container o-hidden">
    
     <!--banner-->
    <div class="ad"><img src="fore/images/banner.jpg"  alt=""></div>
     <!--主体内容区域-->
    <div class="wrap-cotent o-hidden">
    
    
    
    <!--车辆认证主要内容区-->
    <div class="main fr">
    <div class="my-information">
    <!-- <div class="mainright">
    <div class="fenlei"> -->
        	<input type="hidden" name="pageNo" value="${pageNo }"/>
        	<div class="shoucang">
        	<div class="et2">
        	<a href="pc/collect/list">全部</a>
        	</div>
        	<div class="et2">
        	<a href="pc/transport/list">货物信息</a>
        	</div>
        	<div class="et2">
        	<a href="pc/employee/list">招聘与求职</a>
        	</div>            
        	<div class="et1">
        	<a href="pc/secondcar/list">二手车交易</a>
        	</div>            
        	<div class="et2">
        	<a href="pc/newcar/list">新车信息</a>
        	</div>  
        	</div>
            <table>
           	  <tr>
           	  <tr>
           	  <td class="biaotou">信息类别</td>
           	  <td class="biaotou">发布信息名称</td>
           	  <td class="biaotou">发布时间</td>
           	  <td class="biaotou">信息状态</td>
           	  <td class="biaotou">操作</td>
           	  </tr>
           	   <c:forEach items="${s3}" var="pcSecond">
           	  <tr>
           	  <td>
           	  <c:choose>
           	  <c:when test="${pcSecond.distinguish==1}">待售二手车</c:when>
           	  <c:otherwise>求购二手车</c:otherwise>
           	  </c:choose>
           	  </td>
              <td class="td_name">
              <c:choose>
           	  <c:when test="${pcSecond.distinguish==1}"><a href="<%=request.getContextPath()%>/pc/secondcar/detail?id=${pcSecond.id}">${pcSecond.title}</a></c:when>
           	  <c:otherwise><a href="<%=request.getContextPath()%>/pc/forsecond/detail?id=${pcSecond.id}">${pcSecond.title}</a></c:otherwise>
           	  </c:choose>
              </td>
              <td><fmt:formatDate value="${pcSecond.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${pcSecond.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td>
              <c:choose>
           	  <c:when test="${pcSecond.distinguish==1}">
                  <a href="<%=request.getContextPath()%>/pc/secondcar/cancel?id=${pcSecond.id}&collectFlag=1" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </c:when>
              <c:otherwise>
              <a href="<%=request.getContextPath()%>/pc/forsecond/cancel?id=${pcForSecond.id}&collectFlag=1" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
              <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </c:otherwise>
              </c:choose>
              </td>
              </tr>
              </c:forEach>
         </table>
<jsp:include  page="pc_page.jsp" flush="true"/>
<!-- </div>
</div> -->
       </div>
    </div>
    
    <!--个人中心侧边内容区-->
    <div class="sideBar">
      <div class="onesCenter-nav">
      <ul>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/user/list'" ><a>个人资料</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/user/telbook/list'"><a>联系电话管理</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/user/alipay'"><a>会员缴费</a></li>
         <li class="active" onclick="window.location.href='<%=request.getContextPath()%>/pc/collect/list'"><a>我的收藏</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/allInfo/list'"><a>我的发布</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/blockInfo/list'"><a>投诉</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/toadvice'"><a>意见反馈</a></li>
      </ul>
      </div>
    </div>
   </div>
 </div>
  <jsp:include  page="erweima.jsp" flush="true"/>
</body>
</html>
