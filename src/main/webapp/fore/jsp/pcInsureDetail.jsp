<%@ page import="com.tyt.util.Constant" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<script language="javascript" src="<%=request.getContextPath()%>/fore/js/jquery.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/image_size.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/ajax_collect.js"></script>
<%-- <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/baixinghui.css" media="all" charset="UTF-8" />
 --%><%--<script language="javascript" src="<%=request.getContextPath()%>/fore/js/baixinghui.js"></script> --%>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/xiangqing.css">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>特运通-保险详情</title>
</head>
<body >
<div class="container">
	<div class="logobox"><!--header-->
    	<img src="<%=request.getContextPath()%>/fore/image/insure.png">
    	<div class="logoright"></div>
    </div>
<div class="listbox">
<div class="list"><p class="list01"><c:out value="${insure.title}"/></p>

</div>
<p class="list03">发布日期：<fmt:formatDate value="${insure.ctime}" type="both"/>
</p>
<div class="list06"><p class="lb01">公司名称：<span><c:if test="${insure.company==1}">中国人寿</c:if><c:if test="${insure.company==2}">中国平安</c:if><c:if test="${insure.company==3}">泰康人寿</c:if><c:if test="${insure.company==4}">太平洋保险</c:if></span></p></div>
<div class="list06"><p class="lb04">地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;址：<span><c:out value="${insure.province }"/><c:out value="${insure.city}"/><c:out value="${insure.county }"/></span></p></div>
<%--<div class="list06"> <p class="lb01">险&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;种： <span><c:if test="${insure.kind==1}">交强险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==2}">第三者责任险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==3}">车辆损失险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==4}">不计免赔特约险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==5}">盗抢险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==6}">车上座位责任险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==7}">玻璃单独破碎险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==8}">自燃险</c:if></span>
                                                                                    <span><c:if test="${insure.kind==9}">新增设备损失险</c:if></span>
 </p> --%>
<div class="list06"><p class="lb03">联系电话：<span><c:out value="${insure.telephone }"/></span></p></div>                     
<div class="list06"><p class="lb04">联&nbsp;系&nbsp;人&nbsp;：<span><c:out value="${insure.telName}"/></span></p></div>

<div class="list13">
<p class="lb09">详细描述：<br><span><c:out value="${insure.detail}"/></span></p>
</div>
<div class="list13">
<p class="lb09">图片展示：<br></p>
<%-- <div id="main">
<ul>
<c:forEach items="${images}" var="image">
            <li>
                <img src="<%=request.getContextPath()%>/uploadInsureImages/${image}" alt="${image}" />
                <a href="#">点点看</a>
            </li>
</c:forEach>
</ul>
<br>
</div> --%>

<c:forEach items="${images}" var="image">
                <img  src="https://www.teyuntong.net/uploadimg/${image}" border="0" width="0" height="0" onload="AutoResizeImage(400,600,this)"  alt="${image}" />
</c:forEach>

<div class="xiafang">
<div class="fanhui">
<a  href="javascript:window.history.back()"  id="ffh">返回</a>
</div> 
</div>
</div>
</div>
</div>
</body>
</html>