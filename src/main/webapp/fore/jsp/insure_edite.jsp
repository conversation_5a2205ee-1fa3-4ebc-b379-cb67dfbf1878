<%-- <%@page import="com.tyt.model.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ page import="com.tyt.model.Insure"%>
<%@ page import="com.tyt.util.Constant"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<script type="text/javascript"
	src="<%=request.getContextPath()%>/foreground/js/jquery-1.7.1.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/foreground/js/jquery-upload.min.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/foreground/js/imgvalidate.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/foreground/js/insureValidate.js"></script>
<link rel="stylesheet" type="text/css"
	href="<%=request.getContextPath()%>/foreground/css/fabu.css">
<title>保险广告信息发布</title>
</head>
<script type="text/javascript">
$(document).ready(function(){
		 $("p input").click(function(){
			 javascript:document.forms[0].submit();
		 });
		 
		 $("#search").click(function(){
			 javascript:document.forms[0].submit();
		 });
		 
		 $("p select").change(function(){
			 javascript:document.forms[0].submit();
		 });
		 /*省市县级联*/
		 var province=$("#province00").val();
		 var city=$("#city00").val();
		 var county=$("#county00").val();
		 $.getJSON("<%=request.getContextPath()%>/ProvinceServlet?p1="
						+ province + "&c1=" + city + "&c2=" + county, function(
						data) {

					/*省份*/
					$("#province").html("");
					$("#province").append("<option value=''>选择省份</option>");

					for (key in data.promap) {
						if (data.p1 == key)
							$("#province").append(
									"<option value="+key+" selected>"
											+ data.promap[key] + "</option>");
						else
							$("#province").append(
									"<option value="+key+">" + data.promap[key]
											+ "</option>");
					}
					;

					/*单击省份事件*/
					$("#province").change(
							function() {
								$("#city").html("");
								$("#city").append(
										"<option value=''>选择市</option>");
								for (key in data.citymap) {
									if (this.value == key.substring(0, key
											.indexOf("-")))
										//if(data.c1==key)$("#city").append("<option value="+key+" selected>"+data.citymap[key]+"</option>");
										$("#city").append(
												"<option value="+key+" >"
														+ data.citymap[key]
														+ "</option>");
								}
								;
							});

					/*单击市事件*/
					$("#city").change(
							function() {
								$("#county").html("");
								$("#county").append(
										"<option value=''>选择县区</option>");
								for (key in data.countymap) {
									if (this.value == key.substring(0, key
											.indexOf("*"))) {
										//if(data.c2==key)$("#county").append("<option value="+key+" selected>"+data.countymap[key]+"</option>");}
										$("#county").append(
												"<option value="+key+">"
														+ data.countymap[key]
														+ "</option>");
									}
								}
								;

							});

					/*如果省份有选择的话*/
					if (data.p1 != '') {

						$("#city").html("");
						$("#city").append("<option value=''>选择市</option>");
						for (key in data.citymap) {
							if (data.p1 == key.substring(0, key.indexOf("-"))) {
								if (data.c1 == key)
									$("#city").append(
											"<option value="+key+" selected>"
													+ data.citymap[key]
													+ "</option>");
								else
									$("#city").append(
											"<option value="+key+" >"
													+ data.citymap[key]
													+ "</option>");
							}
						}
						;

						$("#county").html("");
						$("#county").append("<option value=''>选择县区</option>");
						for (key in data.countymap) {
							if (data.c1 == key.substring(0, key.indexOf("*"))) {
								if (data.c2 == key)
									$("#county").append(
											"<option value="+key+" selected>"
													+ data.countymap[key]
													+ "</option>");
								else
									$("#county").append(
											"<option value="+key+">"
													+ data.countymap[key]
													+ "</option>");
							}
						}
						;

					}
					;
				});
			});
</script>
<body>
	<!--头部-->
	<div class="container">
		<div class="logobox">
			<!--header-->
			<img src="<%=request.getContextPath()%>/foreground/image/insure.png">
		</div>
		<div class="listbox">
			<div class="list">
				<a class="ab" href="">保险广告信息发布</a>
			</div>
		</div>
		<!--选项卡-->

		<div class="bj">
			<form
				action="<%=request.getContextPath()%>/foreground/insure/publish"
				method="post" name="publishForm1" onsubmit="return checkAll()"
				enctype="multipart/form-data">
				<input type="hidden" name="id">
				<input type="hidden" name="status" value="0">
				<div class="bj08">
					<div class="bj006">
						<img class="bb"
							src="<%=request.getContextPath()%>/foreground/image/xinghao.png">
						<a class="yy">发布账号</a> <input type="text" id="cellPhone" name="cellPhone" value="${cellPhone}" disabled/>
						<input type="hidden" id="cellPhone" name="cellPhone" value="${cellPhone}"/>
					</div>
					<span id="put0"></span>
				</div>
				<div class="bj08">
					<div class="bj006">
						<img class="bb"
							src="<%=request.getContextPath()%>/foreground/image/xinghao.png">
						<a class="yy">标&nbsp;&nbsp;&nbsp;&nbsp;题</a> <input type="text" id="title" name="title"
							value="${insure.title}" onfocus="clearTitle(this)"
							onblur="javascript:checkTitle()" />
					</div>
					<span id="put1"></span>
				</div>
				<div class="bj02">
					<div class="xuanze01">
						<a class="xuanze03"> <img class="bb"
							src="<%=request.getContextPath()%>/foreground/image/xinghao.png">
							<a class="yy">险&nbsp;&nbsp;&nbsp;&nbsp;种</a> 
							<input type="checkbox" name="kind" value="货物运输保险" <c:if test="${fn:contains(insure.kind,'货物运输保险')}">checked</c:if>><a class="yy02">货物运输保险</a>
							<input type="checkbox" name="kind" value="责任保险" <c:if test="${fn:contains(insure.kind,'责任保险')}">checked</c:if>><a class="yy02">责任保险</a>
							<input type="checkbox" name="kind" value="意外伤害险"><c:if test="${fn:contains(insure.kind,'意外伤害险')}">checked</c:if>><a class="yy02">意外伤害险</a>
							<input type="checkbox" name="kind" value="企业责任险"><c:if test="${fn:contains(insure.kind,'企业责任险')}">checked</c:if>><a class="yy02">企业责任险</a>
						</a>
					</div>
				</div>

				<div class="bj08">
					<div class="bj006">
						<img class="bb"
							src="<%=request.getContextPath()%>/foreground/image/xinghao.png">
						<a class="yy">公司名称</a> 
						<input type="text" id="company" name="company" value="${insure.company}" onfocus="clearCompany(this)"
							onblur="javascript:checkCompany()">
					</div>
					<span id="put2"></span>
				</div>

				<div class="bj08">
					<div class="bj03">
						<img class="bb"
							src="<%=request.getContextPath()%>/foreground/image/xinghao.png">
						<a class="yy">地&nbsp;&nbsp;&nbsp;&nbsp;址</a> <select id="province"
							name="province" onblur="javascript:checkAddress()" class="list09"><option
								value="">选择省份</option></select> <select id="city" name="city"
							class="list10"><option value="">选择市</option></select> <select
							id="county" name="county" class="list11"><option
								value="">选择县区</option></select> <input type="hidden" id="province00">
						<input type="hidden" id="city00"> <input type="hidden"
							id="county00">
					</div>
					<span id="put11"></span>
				</div>

				<div class="bj08">
					<div class="bj05">
						<img class="bb"
							src="<%=request.getContextPath()%>/foreground/image/xinghao.png">
						<a class="yy">联&nbsp;系&nbsp;人</a> <input type="text" id="name"
							name="name" value="${insure.name}" onfocus="clearName(this)"
							onblur="javascript:checkName()">
					</div>
					<span id="put4"></span>
				</div>

				<div class="bj08">
					<div class="bj03">
						<img class="bb"
							src="<%=request.getContextPath()%>/foreground/image/xinghao.png">
						<a class="yy">联系电话</a> <input type="text" id="telephone"
							name="telephone" value="${sessionScope.webuser.cellPhone}" onfocus="clearTelephone(this)"
							onblur="javascript:checkTelephone()">
					</div>
					<span id="put5"></span>
				</div>
				<div class="bj08">
					<div class="bj03">
						&nbsp;&nbsp;<a class="yy">图片上传</a> <input type="file" name="images"
							id="images" onchange="preview5(this)" style="size: 30;" /> <input
							type="button" value="增加" onclick="addFile();"
							style="height: 25px; width: 40px;">
					</div>
					<div id="more" style="float: left; margin-left: 16%;"></div>
				</div>
                <div class="bj07">
					<a class="yy02">&nbsp;详细描述&nbsp;&nbsp;&nbsp;</a>
				<textarea id="detail" name="detail" cols=60 rows=6>
					${insure.detail}
                </textarea>
				</div>
				
                <div class="fabubox">
					<input type="submit" class="fabu01" onclick="javaScript:checkAll()" value="确认修改"> 
					<input type="reset" class="fabu02"  value="重置" id="resetId">
				</div>
			</form>
		</div>
	</div>
</body>
</html> --%>