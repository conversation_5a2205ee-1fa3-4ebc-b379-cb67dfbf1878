<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<html>
<head>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/privateCenter.css">
<title>特运通-联系电话管理</title> 
</head>
<script type="text/javascript">
function changeVerifyCode(id){
	var count = 60;
	var countdown = setInterval(CountDown, 1000);
	function CountDown() {
		$(id).attr("disabled", true);
		$(id).val(count + "秒后重新获取");
		if (count == 0) {
			$(id).val("重新获取验证码").removeAttr("disabled");
			clearInterval(countdown);
		}
		count--;
	}
}	
$(function() {
	//短信验证码事件
		$('.verify_code').click(function() {
			if(getValue("input[name='tel']")){
				$("#errorMessage").html("电话号码必须填写");
			}else{
				$("#errorMessage").html("");
				changeVerifyCode(".verify_code");
				$.ajax({
					type : "post",
					async : false, // 同步请求
					url : "<%=request.getContextPath()%>/pc/web/verify/get",
					data : $('#form').serialize(),
					dataType : "text",
					success : function(data) {
						$("#errorMessage").html("");
						if(data=="null"){
							$("#errorMessage").html("请填写电话号码");
						}
                        if(data=="not login"){
							window.location.href="<%=request.getContextPath()%>/fore/jsp/failure.jsp";
						}
						
					},
					error : function() {
						$("#errorMessage").html("请稍后重试");
					}
				});
			}
			
		});
		//语音验证码事件
		$('.verify_code_voice').click(function() {
			var count = 60;
			var countdown = setInterval(CountDown, 1000);
			function CountDown() {
				$(".verify_code_voice").attr("disabled", true);
				$(".verify_code_voice").val(count + "秒后重新获取");
				if (count == 0) {
					$(".verify_code_voice").val("重新获取验证码").removeAttr("disabled");
					clearInterval(countdown);
				}
				count--;
			}
		});
	
});

function checkNull(){
	if(getValue("input[name='tel']")){
		$("#errorMessage").html("请填写电话号码");
		return false;
	}
	if(getValue("input[name='verifyCode']")){
		$("#errorMessage").html("请填写验证码");
		return false;
	}
}

function getValue(id){
	return $(id).val().replace(
			/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "")=="";
}
</script>
<style>
#header,#content,#footer{width:100%;} 
/* 以上代码设置三个共用样式 */ 
#header{height:20px;background-color: #1c86ee;color: #ffffff;} 
#content{ height:auto;padding-left: 30px;padding-top: 10px;} 
#add{padding-top: 50px;padding-left: 100px;}
#add table{border-collapse:separate;border-spacing:10px;}  
#add table td{width:250px;text-align: center;}
</style>
<body>
<div class="all">
<div class="container">
  <div class="logobox"><!--header-->
    	<img src="<%=request.getContextPath()%>/fore/image/all.png">
        <div class="logoright">
				<%-- <a href="<%=request.getContextPath()%>/pc/toadvice">意见反馈</a> --%>
		</div>
 </div>
<div class="maincontainer">   
<div class="mainleft">
			  <div class="mulu"><a href="<%=request.getContextPath()%>/pc/user/list">我的资料</a></div>
		      <div class="thismulu"><a href="<%=request.getContextPath()%>/pc/user/telbook/list">联系电话管理</a></div>
		      <div class="mulu"><a href="<%=request.getContextPath()%>/pc/allInfo/list">我的发布</a></div>
           	  <div class="mulu"><a href="<%=request.getContextPath()%>/pc/myinfo/list">发货管理</a></div>
           	  <div class="mulu"><a href="<%=request.getContextPath()%>/pc/collect/list">我的收藏</a></div>
           	  <div class="mulu"><a href="<%=request.getContextPath()%>/pc/toadvice">意见反馈</a></div>
		      <div class="zhapian"><a href="<%=request.getContextPath()%>/pc/blockInfo/list">投诉诈骗</a></div>
</div>
<div class="right">
<div id="header">电话管理-联系人添加</div> 
<div id="content">
<div id="add">
   <form id="form" action="<%=request.getContextPath()%>/pc/user/telbook/save" method="post" onsubmit="return checkNull();" >
   <table>
   <tr><td style="cursor:pointer;" colspan="2">添加联系手机</td><td  style="cursor:pointer;" colspan="2" onclick="javascript:window.location.href='<%=request.getContextPath()%>/fore/jsp/pc_homephone_add.jsp'" >添加联系座机</td></tr>
   <tr class="cell_phone"><td style="border: 0;text-align: left;" background="" colspan="4">
       手机号码：
   <input name="tel" value="${tel}" style="border:none; border-bottom:1px solid #838b8b;" >&nbsp;&nbsp;&nbsp;&nbsp;
   </td></tr>
   <tr class="cell_phone"><td style="border: 0;text-align: left;" colspan="4"  >输入验证码：
   <input name="verifyCode" value="${verifyCode}" style="border:none; border-bottom:1px solid #838b8b;" >
   <input type="button" class="verify_code"  value="获取验证码"   style="width:130px; font-size:16px;font-family:'黑体';color:red;">
   </td></tr>
   <tr><td style="border: 0;text-align:center;" colspan="4"  >
   <input type="submit" value="添加" style="width:40px; font-size:16px;font-family:'黑体';color:blue;">
   <input type="button" value="返回" style="width:40px; font-size:16px;font-family:'黑体';color:blue;" onclick="window.location.href='<%=request.getContextPath()%>/pc/user/telbook/list'" >
   <input name="type" value="0" type="hidden">
   </td></tr>
   <tr><td><span id="errorMessage" style="color:red;">${msg}</span></td></tr>
   </table>
   </form>
</div> 
</div>
</div>
</div>
</div>
<jsp:include  page="erweima.jsp" flush="true"/>
</div>
</body>
</html>