<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.tyt.model.SecondCar"%>
<%@ page import="com.tyt.util.Constant"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/cityinsert.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/secondCarValidate.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/insert_submit.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-upload.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/showOrHide.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/imgvalidate.js"></script>
<script type="text/javascript" charset="utf-8"  src="<%=request.getContextPath()%>/fore/js/jquery-ui-datepicker.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/jquery-ui.css" />
<script type="text/javascript" charset="utf-8"  src="<%=request.getContextPath()%>/fore/js/endDays.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/fabu.css">
<title>特运通-发布待售二手车</title>
</head>
<body>
	<div class="container">
		<div class="logobox">
			<!--header-->
			<img src="<%=request.getContextPath()%>/fore/image/secondcar.png">
			<div class="logoright">
				<a href="<%=request.getContextPath()%>/fore/secondcar/list?distinguish=1">返回首页</a>
			</div>
		</div>
		<div class="listbox">
			<div class="list">
				<a class="ab" href="javascript:void(0)">待售二手车信息发布</a>
			</div>
		</div>
		<!--选项卡-->
		<div class="bj"><span style="color: red;font-size: 18px">${duplicate}</span>
		<form  action="<%=request.getContextPath()%>/fore/secondcar/publish"  method="post" name="publishForm1" onsubmit="return checkAll();" enctype="multipart/form-data">
		    <input type="hidden" name="distinguish" value="1">
		    <div class="bj08">
			<div class="bj006">
				<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
				<a class="yy">标&nbsp;&nbsp;&nbsp;&nbsp;题</a>
				<input type="text" id="title" name="title"  value="例：急售一辆载重100吨的大板车" onfocus="clearTitle(this)" onblur="checkTitle()" /></div>
			<span id="put1"></span>
			</div>
          <div class="bj02">                                                                       
					<div class="xuanze01">                                                               
						&nbsp;&nbsp;<a class="yy">类&nbsp;&nbsp;&nbsp;&nbsp;型</a>                                                             
					</div>                                                                               
					<a class="xuanze02"> 
					&nbsp;<input type="radio" name="model" onclick="javascript:showCarName1()" value="<%=Constant.MODEL1%>" checked="checked"> <a class="yy yy01">牵引头</a>
					<input type="radio" name="model" onclick="javascript:showCarName2()" value="<%=Constant.MODEL2%>"><a class="yy ">挂车</a>
					<input type="radio" name="model" onclick="javascript:showCarName3()" value="<%=Constant.MODEL3%>"><a class="yy ">牵引头加挂车</a>                                                             
					</a>                                                                                 
				     </div> 
				    <div class="bj02"  id="carName1" style="display:block;">
					<div class="xuanze01">
				    &nbsp;&nbsp;<a class="yy">品&nbsp;&nbsp;&nbsp;&nbsp;牌</a>
					</div>
					<a class="xuanze03">
					&nbsp;<input type="radio" name="carName" value="<%=Constant.CARNAME1%>"><a class="yy">德龙</a> 
				    <input type="radio" name="carName" value="<%=Constant.CARNAME2%>"><a class="yy">欧曼</a>
					<input type="radio" name="carName" value="<%=Constant.CARNAME3%>"><a class="yy">东风</a> 
				    <input type="radio" name="carName" value="<%=Constant.CARNAME4%>"><a class="yy">北奔</a>
				    <input type="radio" name="carName" value="<%=Constant.CARNAME5%>"><a class="yy">解放</a> 
				    <input type="radio" name="carName" value="<%=Constant.CARNAME6%>"><a class="yy">一汽重卡</a>
				    <input type="radio" name="carName" value="<%=Constant.CARNAME7%>"><a class="yy">其它</a> 
				   </a></div>
				   <div class="bj02"  id="carName2" style="display:none;"></div>
				   <div class="bj02"  id="carName3" style="display:none;"></div> 
	    
	   
	  	
        <div class="bj08">
			<div class="bj03">
				<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
				<a class="yy">车源地址</a>
			    <select id="province" name="province" onblur="javascript:checkAddress()" class="list09"><option value="">选择省份</option></select>
                <select id="city" name="city"  class="list10"><option value="">选择市</option></select>
                <select id="county" name="county" class="list11"><option value="">选择县区</option></select>
			    <input type="hidden" id="province00">
			    <input type="hidden" id="city00">
			    <input type="hidden" id="county00"></div>
			   <span id="put12"></span>
	   </div> 
        <div class="bj08">
	  	<div class="bj03">
	  	<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
	    <a class="yy">联&nbsp;系&nbsp;人</a>
		<input type="text" id="telName" name="telName" value="${telName}" onblur="javascript:checkTelName()" /></div> 
        <span id="put10"></span>
	    </div>
	    <div class="bj08">
	  	<div class="bj03">
	  	<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
	    <a class="yy">联系电话</a>
	    <input type="text" id="telephone" name="telephone" value="${cellPhone}"  onblur="checkTelephone()">
	    <input type="hidden" id="cellPhone" name="cellPhone" value="${cellPhone}">
					</div>
        <span id="put11"></span>
        </div>
        <div class="bj08">
	  	<div class="bj03">
	    &nbsp;&nbsp;<a class="yy">Q&nbsp;&nbsp;Q号码</a>
	    <input type="text" id="qq" name="qq"  value="${qq}"  onblur="checkQQ()" /></div>
	    <span id="put13"></span>
	    </div>
	    <!-- <div class="bj08">
			<div class="bj03">
			&nbsp;&nbsp;<a class="yy" style="color:red">有效期至</a>
			<input type="text" id="date_3" name="saveDay" value="请选择有效截止日期"  onblur="checkSaveDay"  readonly />
			</div>
			<span id="put17"></span>
		</div> -->
		
		<div id="moreInfo" style="display:none;">
		<div class="bj04">
	  	&nbsp;&nbsp;<a class="yy">待售价格</a>
	    <select name="price" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
	  	           	<option  value="<%=Constant.PRICE1 %>" >面议</option>
	  	           	<option  value="<%=Constant.PRICE2 %>" >2万以下</option>
	  	            <option  value="<%=Constant.PRICE3 %>" >2-5万</option>
	  	            <option  value="<%=Constant.PRICE4 %>" >5-15万</option>
	  	           	<option  value="<%=Constant.PRICE5 %>" >15-25万</option>
	  	           	<option  value="<%=Constant.PRICE6 %>" >25万以上</option>
	  	           </select>
             <span id="put3"></span>
	  	</div>
		 <div class="bj04">
	         &nbsp;&nbsp;<a class="yy">车&nbsp;&nbsp;&nbsp;&nbsp;龄</a>
	         <select name="carAge" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
	         	  <option  value="<%=Constant.CARAGE1 %>" >1年以下</option>
	         	  <option  value="<%=Constant.CARAGE2 %>" >1-2年</option>
	         	  <option  value="<%=Constant.CARAGE3 %>" >2-3年</option>
	         	  <option  value="<%=Constant.CARAGE4 %>" >3-4年</option>
	         	  <option  value="<%=Constant.CARAGE5 %>" >4年以上</option>
	         </select>
             <span id="put4"></span>
	  	</div>
	  	
	  	<div class="bj08">
	  	<div class="bj03">
	  	<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
	    <a class="yy">行驶里程</a>
	    <input type="text" id="mileage" name="mileage"  value="1" onblur="checkMileage()"/>万公里</div>
        <span id="put5"></span>
	  	</div>
	  	<div class="bj08">
	  	<div class="bj03">
		&nbsp;&nbsp;<a class="yy">额定载重</a>
		<input type="text" id="bearing" name="bearing"   value="1"  onblur="checkBearing()"/>吨</div>
	    <span id="put6"></span>
	  	</div>
	  	<div class="bj08">
	    <div class="bj03">
		&nbsp;&nbsp;<a class="yy">最大马力</a>
	    <input type="text" id="horsePower" name="horsePower"   value="1"  onblur="checkHorsePower()"/>马力</div>
	    <span id="put7"></span>
	  	</div>
	  	<div class="bj02">
		  	<div class="xuanze01">
			 &nbsp;&nbsp;<a class="yy">是否分期</a></div>
		    &nbsp;&nbsp;&nbsp;<input type="radio" name="subsection" value="<%=Constant.SUBSECTION1 %>" checked="checked"/><a class="yy02 yy01">是</a>
		    <input type="radio" name="subsection" value="<%=Constant.SUBSECTION2%>"/><a class="yy02 yy01">否</a>
	    <span id="put9"></span>
	  	</div>
	  	<div class="bj02">
		  	<div class="xuanze01">
			 <a class="yy">重大事故历史</a></div>
		    <input type="radio" name="history" value="<%=Constant.HISTORY1 %>" checked="checked"/><a class="yy02 yy01">无</a>
		    <input type="radio" name="history" value="<%=Constant.HISTORY2%>"/><a class="yy02 yy01">有</a>
	    <span id="put9"></span>
	  	</div>
		</div>
		<div id="another" class="m1" >
			<div class="f1">
			</div>
			<div class="f2">
			<a  href="javascript:void(0)" onclick="showMoreInfo()">更多选项（价格，车龄，分期等）<img src="<%=request.getContextPath()%>/fore/image/slide_down.png"></a>
			</div>
			<div class="f1"></div>
			</div>
			
			<div class="l1" id="less" >
			<div class="f3">
			</div>
			<div class="f4">
			<a href="javascript:void(0)" onclick="hideMoreInfo()">收起 <img src="<%=request.getContextPath()%>/fore/image/slide_up.png"></a>
			</div>
			<div class="f3"></div>
			</div>
		    <div style="height:30px;width:400px;">
					<div class="bjimage">
						&nbsp;&nbsp;<a class="yy">图片上传</a> 
						<input type="file" name="images"  style="width:200px;height:25px;" id="images" onchange="preview5(this)" /> 
						<input type="button" value="增加"  onclick="addFile();" style="background-color:#fe3000;color:#fff; border:0px; height:20px;text-align:center; width:40px;margin-left:0px;">
					</div></div>
			<div id="more" style="float:left;padding-top:0px;margin-top:2px;margin-left:103px;font-size:18px;width:300px;"></div>
		    <div class="bj07">&nbsp;&nbsp;&nbsp;
			<div class="z1" >
				<div class="d1"><p class="p1">&nbsp;&nbsp;卖家寄语&nbsp;&nbsp;</p><p class="p2">（300字以内）</p></div>
				</div>
				<div class="z2"><textarea id="wishes" name="wishes" rows="6" cols="50" ></textarea></div>
			</div>
		    
		    <div class="fabubox">
			    <input type="button" onclick="go();" class="fabu01" id="fabu" value="立即发布">
                <input type="reset" class="fabu02" value="重置"  id="resetId"> 
		    </div>
		</form>
	</div>
 </div>
</body>
</html>