<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!doctype html>
<html>
<head>
<base href="<%=basePath%>">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
<title>二维码登陆页面</title>
<script src="fore/js/jquery-1.11.0.min.js"></script>
<style type="text/css">
/*错误提示 begin*/
.error-tips{z-index:10001;display:none;position:absolute;left:50%;padding:5px;border:2px solid #5798ff;border-radius:5px;-moz-border-radius:5px;-webkit-border-radius:5px;background:#f8fcff;white-space:nowrap;font-size:14px;}
/*错误提示 end*/
</style>
<script type="text/javascript">
var CustInfoCheck = {
		 // 手机验证
	    checkMobel : function(mobile) {
	        if ($.trim(mobile) == "" || $.trim(mobile) == "请填写手机号码") {
	            errorMsg("login_btn", "手机号码不能为空");
	            return false;
	        }
	        if (!checkMobiles($.trim(mobile))) {
	            errorMsg("login_btn", "手机号码格式不正确");
	            return false;
	        }
	        return true;
	    },
	    checkPassword : function(password) {
	        if ($.trim(password) == "" || $.trim(password) == "请填写密码") {
	            errorMsg("login_btn", "密码不能为空");
	            return false;
	        }
	        if (!checkPassword($.trim(password))) {
	            errorMsg("login_btn", "密码长度错误");
	            return false;
	        }
	        return true;
	    }
};

//手机号格式校验
function checkMobiles(theForm) {
    return /^((13|15|18|14|17)+\d{9})$/.test(theForm);
}

function checkPassword(password) {
    return /^[a-zA-Z0-9]{6,16}$/.test(password);
}

$(function(){
	
	$("#login_btn").click(function(){
		var cellPhoneVal = $("#cellPhone").val(); // 机主姓名
		var passwordVal = $("#password").val();
	    var temp = CustInfoCheck.checkMobel(cellPhoneVal)&&CustInfoCheck.checkPassword(passwordVal);
		if(!temp)return;
	    document.forms[0].submit();

	});
	var errMsg="${errMsg}";
	if($.trim(errMsg)!='pass'){
		errorMsg("login_btn",errMsg);
        return;
	}
});

function errorMsg(id, Msg) {
    var err = $(".error-tips"), t = $("#" + id).offset().top, w;
    err.empty().html(Msg);
    w = err.outerWidth();
    err.css({"marginLeft" : -w / 2 + "px","top" : t - 50 + "px"}).fadeIn();
    setTimeout(function() {err.fadeOut();}, 2000);
}
</script>
</head>
<body>
<form action="fore/erweima/user/login" method="post">
<input type="hidden" name="userSign" id="userSign" value="${userSign}">
手机号<input name="cellPhone" id="cellPhone"><br/>
密码<input type="password" name="password" id="password"><br/>
<input id="login_btn" type="button" value="确定">
</form>
<div class="error-tips"></div>
</body>
</html>