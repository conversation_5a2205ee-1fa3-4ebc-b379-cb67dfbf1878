<%@page import="com.tyt.util.AppConfig"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript" src="fore/js/jquery.js"></script>
<link href="fore/style/base.css" type="text/css" rel="stylesheet">
<link href="fore/style/common.css" type="text/css" rel="stylesheet">
<link href="fore/style/page.css" type="text/css" rel="stylesheet">
<link href="fore/style/tanchukuang.css" type="text/css" rel="stylesheet">
<script src="fore/js/jquery.artwl.thickbox.js" type="text/javascript"></script>
<script type="text/javascript">
        $(function () {
            $.artwl_bind({ showbtnid: "btn_show", title: "", content: $("#Content").html() });
        });

        function test() {
            alert("Before close");
            $.artwl_close({ callback: other });
        }

        function other() {
            alert("After close");
        }
		
</script>
<title>认证中心--发货方认证</title>
</head>

<body>
 <div class="container">
     <!--banner-->
    <div class="ad"><img src="fore/images/banner.gif" alt=""></div>
    <div class="tab o-hidden">
    <c:if test="${user.userSign!=2&&user.userSign!=3}">
        <ul>
           <li><a class="tab-active" href="fore/user/identity/info">发货方认证</a></li>
           
           <li><a href="fore/car/carList">车辆方认证</a></li>
        </ul>
        </c:if>
     </div>
      <p class="instruction">
        <span class="instruction-prompt">
        温馨提示：</span>
为保证您的合法权益，本平台施行实名认证制度。
1、上传个人身份证照片，1分钟轻松完成认证；2、认证后即可享有发货权限，与全国车主建立联系；
注：本平台将严格履行《特运通用户协议》规定，对您的认证信息进行保密；
      </p>
     
     <!--主体内容区域-->
    <div class="wrap-cotent o-hidden">
   
    <!--发货方认证主要内容区-->
    <div class="main fr">
      <div class="div-au-identity">
       <h2 class="h2-au-identity">实名认证</h2>
        <!--提示信息-->  
        <c:if test="${user.verifyFlag == 2}">
       <p class="help-au-identity">提交成功，资料审核中，客服人员将于24小时内与您联系。</p>
       </c:if>
       <ul>
          <li><label>身份证姓名：</label>${userIdentity.realName}</li>
          <li><label>身份证号码：</label>${userIdentity.identity}</li>
          <li><label class="vt">身份证正面照：</label>
          <img id="ImgPr2" width="200" height="200" class="img-au-identity"
		    src="<%=AppConfig.getProperty("picture.path.domain") %>${userIdentity.mainurl}" />
          </li>
          <li class="agreement"></li>
          <li class="agreement"> </li>
          <li><div class="div-button-green"></div></li>
       </ul>
       
        
      </div>
      
       
    </div>
    
    <!--发货方认证侧边内容区-->
    <div class="sideBar">
       <!--认证信息-->
       <div class="div-au-infor">
        <h2 class="h2-au-infor">认证信息</h2>
        <ul>
        <li class="o-hidden border-none">
        <span class="gray-color fl">账号：</span><span class="fl">${user.cellPhone }</span>
         <c:if test="${user.verifyFlag == 0}">
         <span class="todo fr vm">未认证</span>
         </c:if>
         <c:if test="${user.verifyFlag == 2}">
         <span class="doing fr vm">认证中</span>
         </c:if>
          <c:if test="${user.verifyFlag == 1}">
          <span class="did fr vm">已通过</span>
          </c:if>
          </li>
          </ul>
       </div> 
         <!--常见问题-->
       <div class="div-au-problem">
        <h2 class="h2-au-problem">常见问题</h2>
        <ol>
          <li id="btn_show"><a href="javascript:void(0);">1.手机如何快速完成认证？</a></li>
        </ol>
       </div> 
    </div>
    
    </div>
 </div>
  <jsp:include  page="erweima.jsp" flush="true"/>
</body>
</html>
