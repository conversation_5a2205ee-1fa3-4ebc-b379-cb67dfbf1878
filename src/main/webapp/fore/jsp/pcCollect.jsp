<%@page import="com.tyt.util.AppConfig"%>
<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript" src="fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="fore/js/baidutongji.js"></script>
<link href="fore/style/base.css" type="text/css" rel="stylesheet">
<link href="fore/style/common.css" type="text/css" rel="stylesheet">
<link href="fore/style/page.css" type="text/css" rel="stylesheet">
<script type="text/javascript" src="fore/js/baidutongji.js"></script>
<link rel="stylesheet" type="text/css" href="fore/css/privateCenter.css">
<title>个人中心--个人资料</title>
<script type="text/javascript">
	function goURL(page){
		  window.location.href="<%=request.getContextPath()%>/pc/collect/list?pageNo="+page+"&status="+getStatus();		
	  }
</script>
</head>

<body>
 <div class="container o-hidden">
    <!--banner-->
    <div class="ad"><img src="fore/images/banner.jpg"  alt=""></div>
     
     <!--主体内容区域-->
    <div class="wrap-cotent o-hidden">
    
    <!--车辆认证主要内容区-->
    <div class="main fr">
    <div class="my-information">
    <!-- <div class="mainright">
    <div class="fenlei"> -->
        	<input type="hidden" name="pageNo" value="${pageNo }"/>
        	<div class="shoucang">
        	<div class="et1">
        	<a href="pc/collect/list">全部</a>
        	</div>
        	<div class="et2">
        	<a href="pc/transport/list">货物信息</a>
        	</div>
        	<div class="et2">
        	<a href="pc/employee/list">招聘与求职</a>
        	</div>            
        	<div class="et2">
        	<a href="pc/secondcar/list">二手车交易</a>
        	</div>            
        	<div class="et2">
        	<a href="pc/newcar/list">新车信息</a>
        	</div>  
        	</div>
            <table>
           	  <tr>
           	  <tr>
           	  <td class="biaotou">信息类别</td>
           	  <td class="biaotou">发布信息名称</td>
           	  <td class="biaotou">发布时间</td>
           	  <td class="biaotou">信息状态</td>
           	  <td class="biaotou">操作</td>
           	  </tr>
           	  <c:forEach items="${transports}" var="transport">
           	  <tr>
           	  <td>货物信息</td> 
              <td class="td_name"><a href="pc/transport/detail?id=${transport.id}">${transport.taskContent}</a></td>
              <td><fmt:formatDate value="${transport.ctime}" type="both"/></td>
              <td><c:if test="${transport.status==1}">有效</c:if><c:if test="${transport.status==0}">无效</c:if></td>
              <td class="collect-btn">
              <img src="fore/image/cancelCollect.png">
              </td>
              </tr>
              </c:forEach>
           	    
           	  <c:forEach items="${employees}" var="employee">
           	  <tr>
           	  <td class="td_type">招聘</td> 
              <td class="td_name"><a href="pc/employee/detail?id=${employee.id}">${employee.title}</a></td>
              <td class="td_time"><fmt:formatDate value="${employee.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${employee.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>
              <td class="collect-btn">
                  <a href="pc/employee/cancel?id=${employee.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach>
              
              <c:forEach items="${seeks}" var="seek">
           	  <tr>
           	  <td class="td_type">求职</td> 
              <td class="td_name"><a href="pc/seek/detail?id=${seek.id}">${seek.title}</a></td>
              <td class="td_time"><fmt:formatDate value="${seek.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${seek.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td class="collect-btn">
                  <a href="pc/seek/cancel?id=${seek.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach>  
               
              <c:forEach items="${secondcars}" var="secondcar" >
           	  <tr>
           	  <td class="td_type">待售二手车</td> 
              <td class="td_name"><a href="pc/secondcar/detail?id=${secondcar.id}">${secondcar.title}</a></td>
              <td class="td_time"><fmt:formatDate value="${secondcar.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${secondcar.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td class="collect-btn">
                  <a href="pc/secondcar/cancel?id=${secondcar.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${forseconds}" var="forsecond">
           	  <tr>
           	  <td class="td_type">求购二手车</td>
              <td class="td_name"><a href="pc/forsecond/detail?id=${forsecond.id}">${forsecond.title}</a></td>
              <td class="td_time"><fmt:formatDate value="${forsecond.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${forsecond.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td class="collect-btn">
                  <a href="pc/forsecond/cancel?id=${forsecond.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${newcars}" var="newcar">
           	  <tr>
           	  <td class="td_type">新车</td>
              <td class="td_name"><a href="pc/newcar/detail?id=${newcar.id}">${newcar.title}</a></td>
              <td class="td_time"><fmt:formatDate value="${newcar.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${newcar.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td class="collect-btn">
                  <a href="pc/newcar/cancel?id=${newcar.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${insures}" var="insure">
           	  <tr>
           	  <td class="td_type">保险</td> 
              <td class="td_name"><a href="pc/insure/detail?id=${insure.id}">${insure.title}</a></td>
              <td class="td_time"><fmt:formatDate value="${insure.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${insure.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>             
              <td class="collect-btn">
                  <a href="pc/insure/cancel?id=${insure.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${takecars}" var="takecar">
           	  <tr>
           	  <td class="td_type">带车</td> 
              <td class="td_name"><a href="pc/takecar/detail?id=${takecar.id}">${takecar.title}</a></td>
              <td class="td_time"><fmt:formatDate value="${takecar.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${takecar.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>             
              <td class="collect-btn">
                  <a href="pc/takecar/cancel?id=${takecar.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
         </table>
<jsp:include  page="pc_page.jsp" flush="true"/>
<!-- </div>
</div> -->
       </div>
    </div>
    
    <!--个人中心侧边内容区-->
    <div class="sideBar">
      <div class="onesCenter-nav">
     <ul>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/user/list'" ><a>个人资料</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/user/telbook/list'"><a>联系电话管理</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/user/alipay'"><a>会员缴费</a></li>
         <li class="active" onclick="window.location.href='<%=request.getContextPath()%>/pc/collect/list'"><a>我的收藏</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/allInfo/list'"><a>我的发布</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/blockInfo/list'"><a>投诉</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/toadvice'"><a>意见反馈</a></li>
      </ul>
      </div>
    </div>
   </div>
 </div>
 <jsp:include  page="erweima.jsp" flush="true"/>
</body>
</html>
