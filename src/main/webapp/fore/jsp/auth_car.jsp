<%@page import="com.tyt.util.AppConfig"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = "https://"
			+ request.getServerName()
			+ path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<script type="text/javascript" src="fore/js/jquery.js"></script>
<link href="fore/style/base.css" type="text/css" rel="stylesheet">
<link href="fore/style/common.css" type="text/css" rel="stylesheet">
<link href="fore/style/page.css" type="text/css" rel="stylesheet">
<link href="fore/style/tanchukuang.css" type="text/css" rel="stylesheet">
<script src="fore/js/jquery.artwl.thickbox.js" type="text/javascript"></script>
<script type="text/javascript">
        $(function () {
            $.artwl_bind({ showbtnid: "btn_show", title: "", content: $("#Content").html() });
        });

        function test() {
            alert("Before close");
            $.artwl_close({ callback: other });
        }

        function other() {
            alert("After close");
        }
		
    </script>
<SCRIPT src="fore/js/jquery.ui.draggable.js" type=text/javascript></SCRIPT>
<!-- 对话框核心JS文件和对应的CSS文件-->
<SCRIPT src="fore/js/jquery.alerts.js" type=text/javascript></SCRIPT>
<LINK media=screen href="fore/js/jquery.alerts.css" type=text/css
	rel=stylesheet>
<!-- 示例代码 -->
<script src="fore/js/uploadPreview.js" type="text/javascript"></script>
<!-- 表单校验加载 -->
<link type="text/css" rel="stylesheet"
	href="fore/js/formValidator/style/validator.css" />
<script src="fore/js/formValidator/formValidator.js"
	type="text/javascript"></script>
<script src="fore/js/formValidator/formValidatorRegex.js"
	type="text/javascript"></script>


<title>车辆认证</title>
<script type="text/javascript">

	$(function() {
		$("#tailDrivingPic").uploadPreview({
			Img : "ImgPr2",
			Width : 200,
			Height : 200
			//Callback:showTailMsg
		});
		$("#headDrivingPic").uploadPreview({
			Img : "ImgPr",
			Width : 200,
			Height : 200,
			Callback:showHeadMsg
		});
	});

	$(document).ready(function() {
		//注册表单校验form
		$.formValidator.initConfig({
			formid : "form1",
			onerror : function(msg) {
			},
			onsuccess : function() {
				if(tijiao()){
					$("#submit_button").val("提交中...");
			        $("#submit_button").attr("disabled", "disabled");
				return true;
				}
				return false;
			}
		});
		
		$("#headCity").formValidator({
			tipid : "headNoTip",
			onshow:" ",
			onfocus:" ",
			oncorrect:"选择正确"
			})
			.inputValidator({
				min:1,
				onerror: "请选择车头牌号"})
				.defaultPassed();
		/**
		 $("#tailCity").formValidator({
			tipid : "tailNoTip",
			onshow:" ",
			onfocus:" ",
			oncorrect:"选择正确"
			})
			.inputValidator({
				min:1,
				onerror: "请选择持车牌号"})
				.defaultPassed();
**/
		
		$("#headNo").formValidator({
			tipid : "headNoTip",
			empty : false,
			onfocus : "请输入车头牌号",
			onshow : "请输入车头牌号",
			oncorrect : "恭喜,输入正确"
		}).inputValidator({
			min : 5,
			max : 10,
			onerror : "输入长度5-10位,请确认"
		});
	/**	 $("#tailNo").formValidator({
			tipid : "tailNoTip",
			empty : false,
			onfocus : "请输入挂车牌号",
			onshow : "请输入车头牌号",
			oncorrect : "恭喜,输入正确"
		}).inputValidator({
			min : 5,
			max : 10,
			onerror : "输入长度5-10位,请确认"
		}); 
	**/
});
	
	function qingkong(){		
		$('#headNo').val('');
		$('#tailNo').val('');
		$('#headDrivingPic').val('');
		$('#tailDrivingPic').val('');
		$('#ImgPr2').attr('src','<%=request.getContextPath()%>/fore/image/2.jpg');
		$('#ImgPr').attr('src','<%=request.getContextPath()%>/fore/image/2.jpg');
		$("#tailCity").val('');
		$("#headCity").val("");
		$("#headDrivingPic").after($("#headDrivingPic").clone().val(""));
		$("#headDrivingPic").remove();
		$("#tailDrivingPic").after($("#tailDrivingPic").clone().val(""));
		$("#tailDrivingPic").remove();

	}

	function showHeadMsg() {

		if (tijiao2('headDrivingPic', 'ImgPr', '车头行驶本照片')) {
			$("#headDrivingPicTip").removeClass();
			$("#headDrivingPicTip").addClass("onCorrect");
			$("#headDrivingPicTip").html("照片选择正确");
		}
	}
	function showTailMsg() {
		if (tijiao2('tailDrivingPic', 'ImgPr2', '挂车行驶本照片')) {
			$("#tailDrivingPicTip").removeClass();
			$("#tailDrivingPicTip").addClass("onCorrect");
			$("#tailDrivingPicTip").html("照片选择正确");
		}
	}

	function tijiao() {
		
		if($('#isAccept').is(':checked')){

			if (tijiao2('headDrivingPic', 'ImgPr', '车头行驶本照片')) {
				if ("" != $("#tailCity").val() && "" != $("#tailNo").val()
						&& tijiao2('tailDrivingPic', 'ImgPr2', '车头行驶本照片')) {
					$("#tailCityTip").removeClass();
					$("#tailCityTip").html("");
					$("#tailDrivingPicTip").removeClass();
					$("#tailDrivingPicTip").html("");
					return true;
				} else {
					if ("" == $("#tailCity").val() && "" == $("#tailNo").val()
							&& !tijiao2('tailDrivingPic', 'ImgPr2', '车头行驶本照片')) {
						$("#tailCityTip").removeClass();
						$("#tailCityTip").html("");
						$("#tailDrivingPicTip").removeClass();
						$("#tailDrivingPicTip").html("");
						return true;
					} else {
						if ("" == $("#tailCity").val()) {
							$("#tailCityTip").removeClass();
							$("#tailCityTip").addClass("onError");
							$("#tailCityTip").html("挂车车牌头字母不能为空");
							$("#tailDrivingPicTip").removeClass();
							$("#tailDrivingPicTip").html("");
							return false;
						}
						if ("" == $("#tailNo").val()) {
							$("#tailCityTip").removeClass();
							$("#tailCityTip").addClass("onError");
							$("#tailCityTip").html("挂车号码不能为空");
							$("#tailDrivingPicTip").removeClass();
							$("#tailDrivingPicTip").html("");
							return false;
						} else if ($("#tailNo").val().length < 5
								|| $("#tailNo").val().length > 10) {
							$("#tailCityTip").removeClass();
							$("#tailCityTip").addClass("onError");
							$("#tailCityTip").html("输入长度5-10位,请确认");
							$("#tailDrivingPicTip").removeClass();
							$("#tailDrivingPicTip").html("");
							return false;
						}			
						
						if (!tijiao2('tailDrivingPic', 'ImgPr2', '车头行驶本照片')) {
							$("#tailCityTip").removeClass();
							$("#tailCityTip").html("");
							return false;
						}
					}
				}
			}
		}else{
			jAlert('请选择特运通用户协议', '车辆认证');
		}
		
		return false;
	}

	function tijiao2(fileuploade, tempimg, msg) {

		var maxsize = 10 * 1024 * 1024;//2M
		var errMsg = "上传的附件文件不能超过2M！！！";
		var tipMsg = "您的浏览器暂不支持计算上传文件的大小，确保上传文件不要超过2M，建议使用IE、FireFox、Chrome浏览器。";
		var browserCfg = {};
		var ua = window.navigator.userAgent;
		if (ua.indexOf("MSIE") >= 1) {
			browserCfg.ie = true;
		} else if (ua.indexOf("Firefox") >= 1) {
			browserCfg.firefox = true;
		} else if (ua.indexOf("Chrome") >= 1) {
			browserCfg.chrome = true;
		}
		try {
			var obj_file = document.getElementById(fileuploade);
			if (obj_file.value == "") {
				$("#" + fileuploade + "Tip").removeClass();
				$("#" + fileuploade + "Tip").addClass("onError");
				$("#" + fileuploade + "Tip").html("请先选择" + msg + "");
				//jAlert('请先选择上传文件', '图片选择错误');
				return false;
			}
			var filesize = 0;
			if (browserCfg.firefox || browserCfg.chrome) {
				filesize = obj_file.files[0].size;
			} else if (browserCfg.ie) {
				var obj_img = document.getElementById(tempimg);
				obj_img.dynsrc = obj_file.value;
				filesize = obj_img.fileSize;
			} else {
				// alert(tipMsg);
				//jAlert(tipMsg, '图片选择错误');
				return true;
			}
			if (filesize == -1) {
				//alert(tipMsg);
				//jAlert(tipMsg, '图片选择错误');
				return true;
			} else if (filesize > maxsize) {
				// alert(errMsg);
				$("#" + fileuploade + "Tip").removeClass();
				$("#" + fileuploade + "Tip").addClass("onError");
				$("#" + fileuploade + "Tip").html("请先选择" + msg + "'图片选择错误'");
				//jAlert(errMsg, '图片选择错误');
				return false;
			} else {
				//alert("文件大小符合要求");
				//jAlert('文件大小符合要求', '图片选择错误');  
				return true;
			}
		} catch (e) {
			return true;
		}
	}
</script>
</head>

<body>
	<div class="container o-hidden">
<!--banner-->
			<div class="ad">
				<img src="fore/images/banner.gif" alt="">
			</div>
			<div class="tab o-hidden">
				<ul>
					<li><a href="fore/user/identity/info">发货方认证</a></li>
					<li><a class="tab-active" href="fore/car/carList">车辆方认证</a></li>
				</ul>
			</div>

			<p class="instruction">
				<span class="instruction-prompt"> 温馨提示：</span> 为保证您的合法权益，本平台施行认证制度。
				认证后您将享有以下特权： 1、货物信息更新更快，找货成交快人一步；2、可在手机版特运通同时进行多车搜索，内容更多，货物更全；

				注：本平台将严格履行《特运通用户协议》之规定，对您的认证信息进行保密；
			</p>

		<!--主体内容区域-->
		<div class="wrap-cotent o-hidden">
			
			<form action="<%=request.getContextPath()%>/fore/car/save"
				name="carForm" method="post" enctype="multipart/form-data"
				id="form1">
				<input type="hidden" name="formTocken" value="${formTocken}" />
				<!--车辆认证主要内容区-->
				<div class="main fr">
					<div class="div-au-identity">

						<h2 class="h2-au-identity">车辆认证</h2>
						<p class="help-au-identity">请保持电话畅通，工作人员会尽快联系您并为您核实资料，审核通过后就可以发货了。</p>

						<!--车头号码认证-->
						<div class="div-au-car">
							<h2 align="center" style="color: red">${message}</h2>
							<div class="car-number">
								<label>车头车牌号码</label> <select name="headCity" id="headCity"
									class="car-select">
									<option value="">选择</option>
									<option value="京">京</option>
									<option value="津">津</option>
									<option value="冀">冀</option>
									<option value="晋">晋</option>
									<option value="蒙">蒙</option>
									<option value="辽">辽</option>
									<option value="吉">吉</option>
									<option value="黑">黑</option>
									<option value="沪">沪</option>
									<option value="苏">苏</option>
									<option value="浙">浙</option>
									<option value="皖">皖</option>
									<option value="闽">闽</option>
									<option value="赣">赣</option>
									<option value="鲁">鲁</option>
									<option value="豫">豫</option>
									<option value="湘">湘</option>
									<option value="鄂">鄂</option>
									<option value="粤">粤</option>
									<option value="琼">琼</option>
									<option value="桂">桂</option>
									<option value="甘">甘</option>
									<option value="陕">陕</option>
									<option value="新">新</option>
									<option value="青">青</option>
									<option value="宁">宁</option>
									<option value="渝">渝</option>
									<option value="川">川</option>
									<option value="贵">贵</option>
									<option value="云">云</option>
									<option value="藏">藏</option>
									<option value="台">台</option>
									<option value="澳">澳</option>
									<option value="港">港</option>
								</select> <input type="text" id="headNo" name="headNo"> <span
									class="error-block" id="headCityTip"></span> <span
									class="error-block" id="headNoTip"></span>
							</div>
							<!-- <img id="ImgPr" class="textarea-car"
				src="<%=AppConfig.getProperty("picture.path.domain")%>/data/pictures/user/123.png" />
         -->
							<div class="car-number-img">
								<label class="vb">车头行驶本照片</label>
								<input type="file" name="headDrivingPic" id="headDrivingPic"
									value="" class="select-img"> <span
									id="headDrivingPicTip" class="error-block"></span>
									 <p class="help-block ml118 mt10">请上传一张小于512kb的.jpeg、.png、.bmp、.tiff等格式的图片</p>
									
							</div>
						</div>

						<!--挂车号码认证-->
						<div class="div-au-car pt20">
							<div class="car-number">
								<label>挂车车牌号码</label> <select name="tailCity" id="tailCity"
									class="car-select">
									<option value="">选择</option>
									<option value="京">京</option>
									<option value="津">津</option>
									<option value="冀">冀</option>
									<option value="晋">晋</option>
									<option value="蒙">蒙</option>
									<option value="辽">辽</option>
									<option value="吉">吉</option>
									<option value="黑">黑</option>
									<option value="沪">沪</option>
									<option value="苏">苏</option>
									<option value="浙">浙</option>
									<option value="皖">皖</option>
									<option value="闽">闽</option>
									<option value="赣">赣</option>
									<option value="鲁">鲁</option>
									<option value="豫">豫</option>
									<option value="湘">湘</option>
									<option value="鄂">鄂</option>
									<option value="粤">粤</option>
									<option value="琼">琼</option>
									<option value="桂">桂</option>
									<option value="甘">甘</option>
									<option value="陕">陕</option>
									<option value="新">新</option>
									<option value="青">青</option>
									<option value="宁">宁</option>
									<option value="渝">渝</option>
									<option value="川">川</option>
									<option value="贵">贵</option>
									<option value="云">云</option>
									<option value="藏">藏</option>
									<option value="台">台</option>
									<option value="澳">澳</option>
									<option value="港">港</option>
								</select> <input type="text" id="tailNo" name="tailNo"> <span
									id="tailCityTip" class="error-block"></span> <span
									id="tailNoTip" class="error-block"></span>
							</div>
							<!--  <img id="ImgPr2" class="textarea-car"
										src="<%=AppConfig.getProperty("picture.path.domain")%>/data/pictures/user/123.png" />
        -->
							<div class="car-number-img">
								<label class="vb">挂车行驶本照片</label>
								<input type="file" name="tailDrivingPic" id="tailDrivingPic"
									value="" class="select-img">	 <span
									id="tailDrivingPicTip" class="error-block"></span>
									<p class="help-block ml118 mt10">请上传一张小于512kb的.jpeg、.png、.bmp、.tiff等格式的图片</p>
							</div>
						</div>
	 <p class="agreement ml150"><input type="checkbox" id="isAccept" class="checkbox" checked="checked" >我接受<a href="fore/jsp/agreement.html">《特运通用户协议》</a></p>
						
						<!--按钮-->
						<div class="div-button o-hidden mt30">
							<input type="submit" id="submit_button" class="button-green" value="提交"> <input
								type="button" class="button-yellow" value="清空"
								onclick="qingkong()">
						</div>
					</div>

				</div>
			</form>
			<!--发货方认证侧边内容区-->
			<div class="sideBar">

				<!--认证信息-->
				<div class="div-au-infor">
					<h2 class="h2-au-infor">认证信息</h2>
					<ul>
						<c:forEach items="${carList }" var="car">
							<li class="o-hidden"><img
								src="fore/images/dabanchetubiao.png" width="30" height="25"
								alt=""> <span class="fl"><a
									href="fore/car/detail?id=${car.id}">${car.headCity }${car.headNo }</a></span>
								<c:if test="${car.auth == 1}">
									<span class="did fr">已认证</span>
								</c:if> <c:if test="${car.auth == 2}">
									<span class="todo fr">认证失败</span>
								</c:if> <c:if test="${car.auth == 0}">
									<span class="doing fr">认证中</span>
								</c:if></li>
						</c:forEach>

					</ul>

				</div>

				<!--常见问题-->
				<div class="div-au-problem">
					<h2 class="h2-au-problem">常见问题</h2>
					<ol>
           <li id="btn_show"><a href="javascript:void(0);">1.手机如何快速完成认证？</a></li>
					</ol>
				</div>
			</div>

		</div>
	</div>
	<jsp:include page="erweima.jsp" flush="true" />
</body>
</html>