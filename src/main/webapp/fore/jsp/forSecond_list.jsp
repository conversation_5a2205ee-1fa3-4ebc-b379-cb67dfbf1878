<%@ page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<link rel="stylesheet" href="<%=request.getContextPath()%>/fore/css/zhaopin.css" type="text/css" />
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/scar.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/cityquery.js"></script>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<title>特运通-二手车信息品台</title> 
<script type="text/javascript">
function toDetail(id,pageNo){
	var form=document.forms[0];
	form.action="<%=request.getContextPath()%>/fore/forsecond/detail?id="+id+"&pageNo="+pageNo;
	form.submit();
}
</script>
</head>
<body>
<div class="container">
<form action="<%=request.getContextPath()%>/fore/forsecond/list" method="post">
	<div class="logobox"><!--header-->
    	<img src="<%=request.getContextPath()%>/fore/image/secondcar.png">
        <div class="searchBox">
        	<input value="${secondCarBean.search}" name="search">
            <a href="#" id="search"></a>
        </div>
                <div class="logoright">
				<a href="<%=request.getContextPath()%>/fore/toinsert?to=secondCar">发布待售二手车</a>
				<a href="<%=request.getContextPath()%>/fore/toinsert?to=forSecond">发布求购二手车</a>
			    </div>    </div>
 <div class="listbox2" id="list">
 
<div class="list" >
<a  id="zhaopin" <c:if test="${secondCarBean.distinguish==1}"> class="ac"</c:if> <c:if test="${secondCarBean.distinguish!=1}">class="ab"</c:if> href="<%=request.getContextPath()%>/fore/secondcar/list?distinguish=1">待售二手车信息列表</a>
<a  id="qiuzhi" <c:if test="${secondCarBean.distinguish==2}"> class="ac"</c:if> <c:if test="${secondCarBean.distinguish!=2}">class="ab"</c:if>  href="<%=request.getContextPath()%>/fore/forsecond/list?distinguish=2">求购二手车信息列表</a>
</div>
<p id="title6">
<a class="list01" id="leixing">类型：</a>
			  <a name="buxian"  href="#"  class="list02<c:if test="${secondCarBean.model==0}"> currentA</c:if>">不限</a>
              <a  href="#"  class="list03<c:if test="${secondCarBean.model==1}"> currentA</c:if>">牵引头</a>
              <a  href="#"  class="list04<c:if test="${secondCarBean.model==2}"> currentA</c:if>">挂车</a>
              <a  href="#"  class="list05<c:if test="${secondCarBean.model==3}"> currentA</c:if>">牵引头加挂车</a>
</p>
<p id="title1">
				  <a class="list01" id="leibie">品牌：</a> 
				  <c:if test="${secondCarBean.model==0}">
				  <a name="buxian"  href="#"  class="list02<c:if test="${secondCarBean.carName==0}"> currentA</c:if>">不限</a>
                  </c:if>
				  <c:if test="${secondCarBean.model==1}">
				  <a name="buxian"  href="#"  class="list02<c:if test="${secondCarBean.carName==0}"> currentA</c:if>">不限</a>
				  <a  href="#"  class="list02<c:if test="${secondCarBean.carName==1}"> currentA</c:if>">德龙</a>
				  <a  href="#"  class="list02<c:if test="${secondCarBean.carName==2}"> currentA</c:if>">欧曼</a>
				  <a  href="#"  class="list02<c:if test="${secondCarBean.carName==3}"> currentA</c:if>">东风</a>
				  <a  href="#"  class="list02<c:if test="${secondCarBean.carName==4}"> currentA</c:if>">北奔</a>
				  <a  href="#"  class="list02<c:if test="${secondCarBean.carName==5}"> currentA</c:if>">解放</a>
				  <a  href="#"  class="list04<c:if test="${secondCarBean.carName==6}"> currentA</c:if>">一汽重卡</a>
				  <a  href="#"  class="list02<c:if test="${secondCarBean.carName==7}"> currentA</c:if>">其它 </a>
                  </c:if>
                  <c:if test="${secondCarBean.model==2}">
				  <a name="buxian"  href="#"  class="list02<c:if test="${secondCarBean.carName==0}"> currentA</c:if>">不限</a>
                  </c:if>
                  <c:if test="${secondCarBean.model==3}">
				  <a name="buxian"  href="#"  class="list02<c:if test="${secondCarBean.carName==0}"> currentA</c:if>">不限</a>
                  </c:if>
           </p>

<p id="title2">
  <a class="list01" id="quyu">区域：</a><a href="#"  class="list02 currentA" name="buxian" onclick="area();">不限</a>
  &nbsp;&nbsp;<select id="province" name="province" style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list09"></select>
  <select id="city" name="city"style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list10"><option value="">选择市</option></select>
  <select id="county" name="county"style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list11"><option value="">选择县区</option></select>
  <input type="hidden" id="province00" value="${secondCarBean.province }">
  <input type="hidden" id="city00" value="${secondCarBean.city }">
  <input type="hidden" id="county00" value="${secondCarBean.county }"></p>
  <%-- <p id="title3">
	  <a class="list01" id="jialing" >价格：</a>
	  <a href="#"  class="list02<c:if test="${secondCarBean.price==0}"> currentA</c:if>" name="buxian">不限</a>
	  <a href="#"  class="list08<c:if test="${secondCarBean.price==1}"> currentA</c:if>">面议</a>
	  <a href="#"  class="list08<c:if test="${secondCarBean.price==2}"> currentA</c:if>">2万以下</a>
	  <a href="#"  class="list08<c:if test="${secondCarBean.price==3}"> currentA</c:if>">2-5万</a>
	  <a href="#"  class="list04<c:if test="${secondCarBean.price==4}"> currentA</c:if>">5-15万</a>
	  <a href="#"  class="list04<c:if test="${secondCarBean.price==5}"> currentA</c:if>">15-25万</a>
	  <a href="#"  class="list04<c:if test="${secondCarBean.price==6}"> currentA</c:if>">25万以上</a>
  </p>
 <p id="title4">
  <a class="list01" id="yuexin">车龄：</a>
  <a name="buxian" href="#"  class="list02<c:if test="${secondCarBean.carAge==0}"> currentA</c:if>">不限</a>
  <a href="#"  class="list04<c:if test="${secondCarBean.carAge==1}"> currentA</c:if>">&nbsp;&nbsp;1年以下</a>
  <a href="#"  class="list06<c:if test="${secondCarBean.carAge==2}"> currentA</c:if>">1-2年</a>
  <a href="#"  class="list06<c:if test="${secondCarBean.carAge==3}"> currentA</c:if>">2-3年</a>
  <a href="#"  class="list04<c:if test="${secondCarBean.carAge==4}"> currentA</c:if>">3-4年</a>
  <a href="#"  class="list04<c:if test="${secondCarBean.carAge==5}"> currentA</c:if>">4年以上</a>
  </p> --%>
 <p id="title5"><span>更多</span>
 <select class="list10" style="margin-right:10px;width:100px; height:25px; font-size:14px;" name="subsection">
		            <option value="<%=Constant.SUBSECTION0%>"<c:if test="${secondCarBean.subsection==0}">selected</c:if>>是否分期</option>
	                <option value="<%=Constant.SUBSECTION1%>"<c:if test="${secondCarBean.subsection==1}">selected</c:if>>是</option>
	                <option value="<%=Constant.SUBSECTION2%>"<c:if test="${secondCarBean.subsection==2}">selected</c:if>>否</option>
 </select>
 <select class="list10" style="margin-right:10px;width:100px; height:25px; font-size:14px;" name="price">
 			        <option value="">价格</option>
	                <option value="<%=Constant.PRICE1%>"<c:if test="${secondCarBean.price==1}">selected</c:if>>面议</option>
	                <option value="<%=Constant.PRICE2%>"<c:if test="${secondCarBean.price==2}">selected</c:if>>2万以下</option>
	                <option value="<%=Constant.PRICE3%>"<c:if test="${secondCarBean.price==3}">selected</c:if>>2-5万</option>
	                <option value="<%=Constant.PRICE4%>"<c:if test="${secondCarBean.price==4}">selected</c:if>>5-15万</option>
	                <option value="<%=Constant.PRICE5%>"<c:if test="${secondCarBean.price==5}">selected</c:if>>15-25万</option>
	                <option value="<%=Constant.PRICE6%>"<c:if test="${secondCarBean.price==6}">selected</c:if>>25万以上</option>
	               
 
 </select>
 
 <select class="list10" style="margin-right:10px;width:100px; height:25px; font-size:14px;" name="carAge">
 					<option value="">车龄</option>
	                <option value="<%=Constant.CARAGE1%>"<c:if test="${secondCarBean.carAge==1}">selected</c:if>>1年以下</option>
	                <option value="<%=Constant.CARAGE2%>"<c:if test="${secondCarBean.carAge==2}">selected</c:if>>1-2年</option>      
	                <option value="<%=Constant.CARAGE3%>"<c:if test="${secondCarBean.carAge==3}">selected</c:if>>2-3年</option>     
	                <option value="<%=Constant.CARAGE4%>"<c:if test="${secondCarBean.carAge==4}">selected</c:if>>3-4年</option>    
	                <option value="<%=Constant.CARAGE5%>"<c:if test="${secondCarBean.carAge==5}">selected</c:if>>4年以上 </option>          
 </select>                                     
 
 </p>

 </div>
 <input type="hidden" name="model"     id="model" value="${secondCarBean.model}"/>
 <input type="hidden" name="subsection" id="subsection" value="${secondCarBean.subsection}"/>
 <input type="hidden" name="carName"   id="carName" value="${secondCarBean.carName}"/>
 <input type="hidden" name="price"     id="price" value="${secondCarBean.price}"/>
 <input type="hidden" name="carAge"    id="carAge" value="${secondCarBean.carAge}"/>
 <input type="hidden" name="distinguish"    id="distinguish" value="${secondCarBean.distinguish}"/>
 <input type="hidden" name="pageNo" value="${pageNo }"/>
</form>
<div class="bj">
     <c:forEach items="${forSeconds}" var="forSecond" varStatus="i">
     <c:choose>
     <c:when test="${user.cellPhone==forSecond.cellPhone}">
      <div style="background-color:#e1e8eb;">
			<div onmouseout="this.className='liebiao02'" onmouseover="this.className='liebiao01'" onclick="toDetail(${forSecond.id},${(pageNo-1)*8+i.index});">
			<div class="neirongbox">
				<form class="neirong01">
					<div class="title"><a class="element1">
					  ${forSecond.title}<img src="<%=request.getContextPath()%>/fore/image/zhishi.png">
					</a>
					<span class="element6">自己发布</span>
					<a class="element7" href="<%=request.getContextPath()%>/fore/forsecond/delete?id=${forSecond.id}" onclick="{if(confirm('确定要删除这条信息吗?')){return true;}return false;}">删除</a>
					</div>
					<a class="element2" >联系人：${forSecond.telName}</a>
					<a class="element3" >联系电话：<span>${forSecond.telephone}</span></a>
					<a class="element4">车源地址：${forSecond.province}${forSecond.city}${forSecond.county}</a>
				</form>
				<div>
					<div class="neirong02">发布日期：<fmt:formatDate value="${forSecond.ctime}" type="both"/></div>
				</div>
			</div>
		</div>
		</div>
     </c:when>
     <c:otherwise>
     <div onmouseout="this.className='liebiao'" onmouseover="this.className='liebiao01'"  onclick="toDetail(${forSecond.id},${(pageNo-1)*8+i.index});">
			<div class="neirongbox">
				<form class="neirong01">
					<div class="title"><a class="element1">
					  ${forSecond.title}<img src="<%=request.getContextPath()%>/fore/image/zhishi.png">
					</a>
					<c:forEach items="${forsecondcollects}" var="forsecondcollects">	
						       <c:if test="${forsecondcollects.forSecondId==forSecond.id}"><a class="element5">已浏览</a></c:if>
					</c:forEach> 
					</div>
					<a class="element2" >联系人：${forSecond.telName}</a>
					<a class="element3" >联系电话：<span>${forSecond.telephone}</span></a>
					<a class="element4">车源地址：${forSecond.province}${forSecond.city}${forSecond.county}</a>
				</form>
				<div>
					<div class="neirong02">发布日期：<fmt:formatDate value="${forSecond.ctime}" type="both"/></div>
				</div>
			</div>
		</div>
     </c:otherwise>
     </c:choose>
        </c:forEach>

		</div>
<jsp:include  page="page.jsp" flush="true"/> 
</div>
      </body>
</html>
