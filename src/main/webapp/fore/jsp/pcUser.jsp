<%@page import="com.tyt.util.AppConfig"%>
<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path+"/";
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript" src="fore/js/jquery-1.11.0.min.js"></script>
<!-- <script type="text/javascript" src="fore/js/baidutongji.js"></script> -->
<link href="fore/style/base.css" type="text/css" rel="stylesheet">
<link href="fore/style/common.css" type="text/css" rel="stylesheet">
<link href="fore/style/page.css" type="text/css" rel="stylesheet">
<script>
var _hmt = _hmt || [];
</script>
<title>个人中心--个人资料</title>
</head>

<body>

 <div class="container o-hidden">
    <!--banner-->
    <div class="ad"><img src="fore/images/banner.jpg" alt=""></div>
     
     <!--主体内容区域-->
    <div class="wrap-cotent o-hidden">
    
    <!--车辆认证主要内容区-->
    <div class="main fr">
    
       <!--我的资料-->
       <div class="my-information">
         <h2 class="h2-my-information">我的资料</h2>
         <div class="div-my-information o-hidden">
         <c:choose>
			<c:when test="${empty center.user.headUrl }">
				<img class="information-img" 
					src="fore/images/touxiang.png" />
			</c:when>
			<c:otherwise>
				<img class="information-img"
					src="<%=AppConfig.getProperty("picture.path.domain") %>${center.user.headUrl}" width="120" height="120" />
			</c:otherwise>
		 </c:choose> 
         
         <div class="my-info-content o-hidden">
            <p><span class="w100 gray-color tr">账&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号：</span>${center.user.cellPhone}</p>
            <p><span class="w100 gray-color tr">用&nbsp;&nbsp;户&nbsp;&nbsp;名：</span>${center.user.userName}
            
            </p>
             <p><span class="w100 gray-color tr">信&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;誉：</span>
             <c:choose>
            <c:when test="${center.user.userType==1&&center.user.serveDays>0}">
            <img class="name-img" src="fore/images/vip.png" width="25" height="30" alt="vip">
            </c:when>
            <c:otherwise>
            <img class="name-img" src="fore/images/vipGray.png" width="25" height="30" alt="vip">
            </c:otherwise>
            </c:choose>
            <c:if test="${center.user.verifyFlag==1 }">
            <img class="name-img" src="fore/images/nameAu.png" width="25" height="30" alt="实名认证">
            </c:if>
            <c:if test="${center.user.verifyFlag!=1 }">
            <img class="name-img" src="fore/images/nameAuGray.png" width="25" height="30" alt="实名认证">
            </c:if>
            <c:if test="${center.user.userSign!= 2&&center.user.userSign != 3}">
            <c:if test="${center.user.isCar==1}">
            <img class="name-img" src="fore/images/carAu.png" width="25" height="30" alt="车辆认证">
            </c:if>
            <c:if test="${center.user.isCar!=1}">
            <img class="name-img" src="fore/images/carAuGray.png" width="25" height="30" alt="车辆认证">
            </c:if>
            </c:if>
             </p>
            <p><span class="w100 gray-color tr">注册身份：</span>
            <c:if test="${center.user.userSign == 2||center.user.userSign == 3}">发货方</c:if>
		    <c:if test="${center.user.userSign == 7}">司机</c:if>	
		    <c:if test="${center.user.userSign!=2&&center.user.userSign!=3&&center.user.userSign!=7}">车主</c:if>
            </p>
            <p class="mt10"><a href="fore/user/head/info" id="baidu_tongji_xiugaigerenxinxi_button" onclick="_hmt.push(['_trackEvent', 'style', 'click', 'xiugaigerenxinxi'])"><img  src="fore/images/xiugaigerenxinxi.png" width="100" height="30" alt="修改个人信息"></a></p>
         </div> 
        </div> 
       </div>
       
       <!--我的账户-->
       <div class="my-account">
         <h2 class="h2-my-account">我的账户</h2>
         <p class="p-my-account">
         <c:if test="${center.user.userType==1&&center.user.serveDays>0}">
         <span class="gray-color">vip会员到期日期：</span>
         <fmt:formatDate value="${center.user.endTime}"  pattern="yyyy.MM.dd" ></fmt:formatDate>
         </c:if>
         <a href="pc/user/alipay">
         <img id="baidu_tongji_jiaofei_button" src="fore/images/jiaofei.png" width="100"  height="30" alt="缴费"></a>
         <c:if test="${!(center.user.userType==1&&center.user.serveDays>0)}">
         <span class="error-block ml10">开通正式会员，您将享有更多权限和功能。</span>
         </c:if>
         </p>
       </div>
       
       <!--认证情况-->
       <div class="authentication">
         <h2 class="h2-authentication">认证情况</h2>
         <ul>
            <c:if test="${center.user.userSign!= 2&&center.user.userSign != 3}">
            <li>车辆认证<span class="car-au-num gray-color">(${center.carPassSize}辆)</span>
            <c:if test="${center.carPassSize>0}"><span class="did mr15">已认证</span></c:if>
            <c:if test="${center.carPassSize==0}">
            <span class="help-block">尚未完成车辆认证，认证后信誉度更高，信息更安全。</span></c:if></li>
            </c:if>
            <li class="bor-bot-none">发货实名认证
            <c:if test="${center.user.verifyFlag==2}"><span class="doing mr15 ml15">认证中</span></c:if>
            <c:if test="${center.user.verifyFlag==1}"><span class="did mr15 ml15">已认证</span></c:if>
            <c:if test="${center.user.verifyFlag==0}"><span class="todo mr15 ml15">未认证</span>
            <span class="help-block">尚未完成实名认证，认证后马上拥有发货权限。</span>
            </c:if>
            </li>
         </ul>
       </div>
    </div>
    
    <!--个人中心侧边内容区-->
    <div class="sideBar">
      <div class="onesCenter-nav">
      <ul>
         <li  class="active" onclick="window.location.href='<%=request.getContextPath()%>/pc/user/list'" ><a>个人资料</a></li>
         <li  onclick="window.location.href='<%=request.getContextPath()%>/pc/user/telbook/list'"><a>联系电话管理</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/user/alipay'"><a>会员缴费</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/collect/list'"><a>我的收藏</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/allInfo/list'"><a>我的发布</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/blockInfo/list'"><a>投诉</a></li>
         <li onclick="window.location.href='<%=request.getContextPath()%>/pc/toadvice'"><a>意见反馈</a></li>
      </ul>
      </div>
    </div>
   </div>
 </div>
<jsp:include  page="erweima.jsp" flush="true"/>
</body>
</html>
