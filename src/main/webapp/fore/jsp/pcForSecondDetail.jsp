<%@page import="com.tyt.model.SecondCar"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page import="com.tyt.util.Constant" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/ajax_collect.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/xiangqing.css">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>特运通-求购二手车详情</title>
</head>
<body >
<div class="container">
	<div class="logobox"><!--header-->
    	<img src="<%=request.getContextPath()%>/fore/image/secondcar.png">
    	<div class="logoright"></div>
    </div>
<div class="listbox">
 
 <div class="list">
<p class="list01"><c:out value="${forSecond.title}"/></p>
</div>
<p class="list03">发布日期：<fmt:formatDate value="${forSecond.ctime}" type="both"/><%-- <c:if test="${forSecond.saveDay<=3}"><span>该信息有效期仅剩：<c:out value="${forSecond.saveDay}"/>天</span></c:if> --%></p>

<div class="list06"><p class="lb01">类&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型：<span><c:if test="${forSecond.model==1}">牵引头</c:if>         
                                               <c:if test="${forSecond.model==2}">挂车</c:if>        
                                               <c:if test="${forSecond.model==3}">牵引头加挂车</c:if></span></p>
<p class="lb05">车源地址：<span><c:out value="${forSecond.province }"/>
<c:out value="${forSecond.city}"/>
<c:out value="${forSecond.county }"/></span></p></div>

<div class="list06"><p class="lb03">联系电话：<span><c:out value="${forSecond.telephone}"/></span></p>  
<p class="lb04">行程要求：<span><c:out value="${forSecond.mileage}"/>万公里以内</span></p></div>

<div class="list06"><p class="lb01">联&nbsp;系&nbsp;人&nbsp;：<span><c:out value="${forSecond.telName}"/></span></p>
<p class="lb04">额定载重：<span><c:out value="${forSecond.bearing}"/>吨</span></p></div>

<div class="list06"><p class="lb01">Q&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Q：<span><c:out value="${forSecond.qq}"/></span></p>
<p class="lb04">马&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;力：<span><c:out value="${forSecond.horsePower}"/></span></p>  	             
</div>
<div class="list06">
<p class="lb03">价&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;格：<span><c:if test="${forSecond.price== 1}">面议</c:if></span>
<span><c:if test="${forSecond.price== 2}">2万以下 </c:if></span>
<span><c:if test="${forSecond.price== 3}">2-5万 </c:if></span>
<span><c:if test="${forSecond.price== 4}">5-15万</c:if></span>
<span><c:if test="${forSecond.price== 5}">15-25万</c:if></span>
<span><c:if test="${forSecond.price== 6}">25万以上</c:if></span>
 </p>
<p class="lb05">是否分期：<span><c:if test="${forSecond.subsection==1}">是</c:if><c:if test="${forSecond.subsection==2}">否</c:if></span></p>
                            </div>

<div class="list06"><p class="lb01">品&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;牌：<span><c:if test="${forSecond.carName==1}">德龙 </c:if><c:if test="${forSecond.carName==2}">欧曼</c:if>
<c:if test="${forSecond.carName==3}">东风</c:if><c:if test="${forSecond.carName==4}">北奔</c:if><c:if test="${secondCar.carName==5}">解放</c:if><c:if test="${forSecond.carName==6}">一汽重卡</c:if> 
<c:if test="${forSecond.carName==7}">其它 </c:if></span></p>
<p class="lb04">车&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄：<span><c:if test="${forSecond.carAge==1}">1年以下</c:if>           
<c:if test="${forSecond.carAge==2}">1-2年</c:if> 
<c:if test="${forSecond.carAge==3}">2-3年</c:if> 
<c:if test="${forSecond.carAge==4}">3-4年</c:if>
<c:if test="${forSecond.carAge==5}">4年以上</c:if></span></p>
 </div>



<div class="list12">
<p class="lb09">买家说明：<br><span><c:out value="${forSecond.wishes}"/></span></p>
</div>
<div class="xiafang">
<div class="fanhui">
<a href="javascript:window.history.back()"  id="ffh">返回</a>
</div>
</div>
</div>
</div>
</body>
</html>