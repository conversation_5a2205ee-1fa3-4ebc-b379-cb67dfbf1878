<%@page import="com.tyt.model.Employee"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page import="com.tyt.util.Constant" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/ajax_collect.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/xiangqing.css">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>特运通-运输信息详情列表</title>
</head>
<body>
<div class="container">
<div class="logobox"><!--header-->
	<img src="<%=request.getContextPath()%>/fore/image/huowu.png">
</div>
<div class="listbox">
<div class="list">
<p class="list01">${transport.startPoint}-${transport.destPoint}
&nbsp;&nbsp;&nbsp;&nbsp;${transport.taskContent}</p></div>
<p class="list03">发布日期：<fmt:formatDate value="${transport.ctime}" type="both"/>
</p>
<div class="list06"><p class="lb03">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;QQ：<span>
<c:choose><c:when test="${serveDays==0 }">******</c:when><c:otherwise>${transport.pubQQ}</c:otherwise></c:choose>
</span></p></div>
<div class="list06"><p class="lb03">联系电话：<span>
<c:choose><c:when test="${serveDays==0 }">******</c:when><c:otherwise>${transport.tel}</c:otherwise></c:choose>
</span></p></div>



<div class="xiafang">
<div class="fanhui">
<a href="javascript:window.history.back()"  id="ffh">返回</a>
</div>
</div>
</div>
</div>
</body>
</html>