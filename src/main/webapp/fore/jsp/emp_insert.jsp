 <%@ page language="java" contentType
 ="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.tyt.model.Employee"%>
<%@ page import="com.tyt.util.Constant"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/empValidate.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/insert_submit.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/cityinsert.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/showOrHide.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/fabu.css">
<script type="text/javascript" charset="utf-8"  src="<%=request.getContextPath()%>/fore/js/jquery-ui-datepicker.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/jquery-ui.css" />
<script type="text/javascript" charset="utf-8"  src="<%=request.getContextPath()%>/fore/js/endDays.js"></script>
<title>特运通-发布招聘</title>
</head>
<body >
    <div class="container">
		<div class="logobox">
			<!--header-->
			<img src="<%=request.getContextPath()%>/fore/image/zp.png">
			<div class="logoright">
				<a href="<%=request.getContextPath()%>/fore/employee/list?distinguish=1">返回首页</a>
			</div>
		</div>
		<div class="listbox">
			<div class="list">
				<a class="ab" href="javascript:void(0)">招聘信息发布</a>
			</div>
		</div>
		<!--选项卡-->

		<div class="bj">&nbsp;&nbsp;&nbsp;&nbsp;<span style="color: red;font-size: 18px">${duplicate}</span>
		<form  action="<%=request.getContextPath()%>/fore/employee/publish" method="post" name="publishForm1"  onsubmit="return checkAll()">
		<input type="hidden" name="distinguish" value="1">
			<div class="bj08">
			<div class="bj006">
				<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
				<a class="yy">标&nbsp;&nbsp;&nbsp;&nbsp;题</a>
				<input type="text" id="title" name="title"  value="例：急招一名大板车司机"
					onfocus="clearTitle(this)" onblur="javascript:checkTitle()"
					/></div>
			<span id="put1"></span>
			</div>
			<div class="bj08">
			<div class="bj03">
				<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
				<a class="yy">工作地址</a>
			    <select id="province" name="province" style="margin-right:10px;width:100px; height:25px; font-size:14px;" onblur="javascript:checkAddress()"  class="list09"><option value="">选择省份</option></select>
                <select id="city" name="city" style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list10"><option value="">选择市</option></select>
                <select id="county" name="county" style="margin-right:10px;width:100px; height:25px; font-size:14px;" class="list11"><option value="">选择县区</option></select>
			    </div>
			    <span id="put11"></span>
			</div>
            <div class="bj08">
			<div class="bj05">
				<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
				<a class="yy">联&nbsp;系&nbsp;人</a>
				<input type="text" id="telName" name="telName" value="${telName}" onblur="javascript:checkTelName()" /></div> 
				<span id="put14"></span>
			</div>
	       <div class="bj08">
			<div class="bj03">
				<img class="bb" src="<%=request.getContextPath()%>/fore/image/xinghao.png">
				<a class="yy">联系电话</a>
				<input type="text" id="telephone" name="telephone" value="${cellPhone}"  onblur="checkTelephone()">
				<input type="hidden" id="cellPhone" name="cellPhone" value="${cellPhone}">
					</div><span id="put15"></span>
			</div>
			<div class="bj08">
			<div class="bj03">
				&nbsp;&nbsp;<a class="yy">Q&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Q</a>&nbsp;<input type="text" id="qq" name="qq" value="${qq}" onblur="checkQQ()"/></div> <span id="put16"></span>
			</div>
			<div id="moreInfo" style="display:none;">
			<div class="bj02">
				<div class="xuanze01">
					&nbsp;&nbsp;<a class="yy">招聘身份</a>
				</div>
				<a class="xuanze02">
					&nbsp;<input type="radio" name="identity"  onclick="javascript:showCC()" value="<%=Constant.IDENTITY1 %>" > 
					<a class="yy yy01">公司</a> 
				    <input type="radio" name="identity" onclick="javascript:hideCC()" value="<%=Constant.IDENTITY2 %>"checked="checked"> 
				    <a class="yy">个人</a>
				</a>
			</div>

			<div class="bj02">
				<div class="xuanze01">
					&nbsp;&nbsp;<a class="yy">性别要求</a>
				</div>
				<a class="xuanze02">
					&nbsp;<input type="radio" name="sex" value="<%=Constant.SEX1%>" checked="checked">
					<a class="yy yy01">男</a> 
					<input type="radio" name="sex" value="<%=Constant.SEX2%>"> 
					<a class="yy">女</a>
				</a>
			</div>
            <div class="bj08">
			<div class="bj04">
				&nbsp;&nbsp;<a class="yy">职位名称</a>
				<select name="position" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
					<option value="<%=Constant.POSITION1%>">13.5米大板车司机</option>
					<option value="<%=Constant.POSITION2%>">17.5米大板车司机 </option>
					<option value="<%=Constant.POSITION3%>">6.8米/9.6米单机板车司机</option>
					<option value="<%=Constant.POSITION4%>">特种车司机</option>
					<option value="<%=Constant.POSITION5%>">临时司机</option>
					<option value="<%=Constant.POSITION6%>">其他车辆司机</option>
				</select> <span id="put7"></span>
			</div>
			</div>
			
			<div class="bj02">
				<div class="xuanze01">
					&nbsp;&nbsp;<a class="yy">职&nbsp;&nbsp;&nbsp;&nbsp;务</a>
				</div>
				<a class="xuanze02">
					&nbsp;<input type="radio" name="duty" value="<%=Constant.DUTY1%>" checked="checked"> 
					<a class="yy yy01">主司机</a> 
				    <input type="radio" name="duty"  value="<%=Constant.DUTY2%>"> 
				    <a class="yy">副司机</a>
				</a>
			</div>
            <div class="bj08">
			<div class="bj05">
				&nbsp;&nbsp;<a class="yy">招聘人数</a>
				<input type="text" id="count" name="count" value="1"
					onfocus="clearCount(this)"
					onblur="javascript:checkCount()" />人</div>
					<span id="put4"></span>
			</div>

			<div class="bj04">
			    &nbsp;&nbsp;<a class="yy">薪资待遇</a>
				<select name="salary" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
						<option value="<%=Constant.SALARY1%>">面议   </option>
						<option value="<%=Constant.SALARY2%>">5000以下</option>
						<option value="<%=Constant.SALARY3%>">5000-6000</option>
						<option value="<%=Constant.SALARY4%>">6000-7000</option>
						<option value="<%=Constant.SALARY5%>">7000-8000</option>
						<option value="<%=Constant.SALARY6%>">8000以上 </option>
					</select><span id="put5"></span>
			</div>
      
			<div class="bj04">
		        &nbsp;&nbsp;<a class="yy">学历要求</a>
				<select name="education" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
					<option  value="<%=Constant.EDUCATION0%>">无要求 </option>
					<option  value="<%=Constant.EDUCATION1%>">高中  </option>
					<option  value="<%=Constant.EDUCATION2%>">技校  </option>
					<option  value="<%=Constant.EDUCATION3%>">中专  </option>
					<option  value="<%=Constant.EDUCATION4%>">大专  </option>
					<option  value="<%=Constant.EDUCATION5%>">本科  </option>
					<option  value="<%=Constant.EDUCATION6%>">其它  </option>
				</select> <span id="put6"></span>
			</div>
            
			
           <div class="bj08">
			<div class="bj04">
					&nbsp;&nbsp;<a class="yy">驾&nbsp;&nbsp;&nbsp;&nbsp;龄</a>
					<select name="years" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
					<option value="<%=Constant.YEARS0%>">无要求</option>
					<option value="<%=Constant.YEARS1%>">5年以下</option>
					<option value="<%=Constant.YEARS2%>">5-10年</option>
					<option value="<%=Constant.YEARS3%>">10年以上</option>
					</select>
				    <span id="put8"></span>
			</div>
			</div>
			<div class="bj08">
            <div class="bj04">
				    &nbsp;&nbsp;<a class="yy">年&nbsp;&nbsp;&nbsp;&nbsp;龄</a>
					<select name="age" style="margin-right:10px;width:100px; height:25px; font-size:14px;">
					<option value="<%=Constant.AGE0%>">无要求</option>
					<option value="<%=Constant.AGE1%>">18-30岁</option>
					<option value="<%=Constant.AGE2%>">30-40岁</option>
					<option value="<%=Constant.AGE3%>">40-50岁</option>
					<option value="<%=Constant.AGE4%>">50岁以上</option>
					</select>
				 <span id="put9"></span>
			</div>
			</div>
			 
            <div class="bj08" id="cc" style="display: none;">
			<div class="bj006">
				&nbsp;&nbsp;<a class="yy">公司名称</a>
				<input type="text" id="company" name="company"/></div><span id="put10"></span>
			</div>

            <%-- <div class="bj02">
					<div class="xuanze01">
				    &nbsp;&nbsp;<a class="yy">福利待遇</a>
					</div>
					<a class="xuanze03">
					&nbsp;<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY1%>"> 
					<a class="yy">五险一金</a> 
					<input type="checkbox" name="subsidy"value="<%=Constant.SUBSIDY2%>"> 
					<a class="yy">包吃</a>
					<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY3%>">
					<a class="yy">包住</a>
					<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY4%>"> 
					<a class="yy">年底双薪</a> 
					<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY5%>">
					<a class="yy">周末补助</a>
					<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY6%>">
					<a class="yy">交通补助</a>
					<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY7%>">
					<a class="yy">加班补助</a>
					<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY8%>">
					<a class="yy">餐补</a>
					<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY9%>">
					<a class="yy">话补</a>
					<input type="checkbox" name="subsidy" value="<%=Constant.SUBSIDY10%>">
					<a class="yy">房补</a>
				</a>
				</div> --%>
		    </div>
            <div id="another" class="m1" >
			<div class="f1">
			</div>
			<div class="f2">
			<a  href="javascript:void(0)" onclick="showMoreInfo()">更多选项（职位，驾龄，薪资等）<img src="<%=request.getContextPath()%>/fore/image/slide_down.png"></a>
			</div>
			<div class="f1"></div>
			</div>
			
			<div class="l1" id="less" >
			<div class="f3">
			</div>
			<div class="f4">
			<a href="javascript:void(0)" onclick="hideMoreInfo()">收起 <img src="<%=request.getContextPath()%>/fore/image/slide_up.png"></a>
			</div>
			<div class="f3"></div>
			</div>
			<div class="bj07">&nbsp;&nbsp;&nbsp;
				<div class="z1" >
				<div class="d1"><p class="p1">职位要求描述</p><p class="p2">（300字以内）</p></div>
				</div>
				<div class="z2"><textarea id="pDescribe" rows="6" cols="50"  name="pDescribe" ></textarea></div>
			</div>

			<div class="bj07">&nbsp;&nbsp;&nbsp;
			<div class="z1" >
				<div class="d1"><p class="p1">薪资待遇描述</p><p class="p2">（300字以内）</p></div>
				</div>
				<div class="z2"><textarea id="sDescribe" rows="6" cols="50"  name="sDescribe" ></textarea></div>
			</div>
				 
		    <!-- <div class="bj08">
			<div class="bj03">
			&nbsp;&nbsp;<a class="yy" style="color:red">有效期至</a>
			<input type="text" id="date_3" name="saveDay" value="请选择有效截止日期"  onblur="checkSaveDay"  readonly />天默认
			</div>
			<span id="put17"></span>
			</div> -->
			
			
			
		    <div class="fabubox">
			    <input type="button" class="fabu01" onclick="go();" id="fabu" value="立即发布">
                <input type="reset" class="fabu02" value="重置" id="resetId">
		    </div>
		</form>
	</div>
 </div>
</body>
</html>