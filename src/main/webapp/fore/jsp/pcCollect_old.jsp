<%@page import="com.tyt.util.Constant"%>
<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<html>
<head>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/jquery-1.7.1.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/fore/js/baidutongji.js"></script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/fore/css/privateCenter.css">
<title>无标题文档</title>
<script type="text/javascript">
	function goURL(page){
		  window.location.href="<%=request.getContextPath()%>/pc/collect/list?pageNo="+page+"&status="+getStatus();		
	  }
</script>  
</head>
<body>
<div class="all">
<div class="container">
  <div class="logobox"><!--header-->
    	<img src="<%=request.getContextPath()%>/fore/image/all.png">
        <div class="logoright">
				<%-- <a href="<%=request.getContextPath()%>/pc/toadvice">意见反馈</a> --%>
		</div>
    </div>
 <div class="maincontainer">   
<div class="mainleft">
			  <div class="mulu"><a href="<%=request.getContextPath()%>/pc/user/list">我的资料</a></div>
		      <div class="mulu"><a href="<%=request.getContextPath()%>/pc/user/telbook/list">联系电话管理</a></div>
		      <div class="mulu"><a href="<%=request.getContextPath()%>/pc/allInfo/list">我的发布</a></div>
           	  <div class="mulu"><a href="<%=request.getContextPath()%>/pc/myinfo/list">发货管理</a></div>
           	  <div class="thismulu"><a href="<%=request.getContextPath()%>/pc/collect/list">我的收藏</a></div>
           	  <div class="mulu"><a href="<%=request.getContextPath()%>/pc/toadvice">意见反馈</a></div>
		      <div class="zhapian"><a href="<%=request.getContextPath()%>/pc/blockInfo/list">投诉诈骗</a></div>
</div>
<div class="mainright">
<div class="fenlei">
        	<input type="hidden" name="pageNo" value="${pageNo }"/>
        	<div class="shoucang">
        	<div class="et1">
        	<a href="<%=request.getContextPath()%>/pc/collect/list">全部</a>
        	</div>
        	<div class="et2">
        	<a href="<%=request.getContextPath()%>/pc/transport/list">货物信息</a>
        	</div>
        	<div class="et2">
        	<a href="<%=request.getContextPath()%>/pc/employee/list">招聘与求职</a>
        	</div>            
        	<div class="et2">
        	<a href="<%=request.getContextPath()%>/pc/secondcar/list">二手车交易</a>
        	</div>            
        	<div class="et2">
        	<a href="<%=request.getContextPath()%>/pc/newcar/list">新车信息</a>
        	</div>  
        	<div class="et2">
        	<a href="<%=request.getContextPath()%>/pc/takecar/list">带车信息</a>
        	</div>
        	</div>
            <table>
           	  <tr>
           	  <tr>
           	  <td class="biaotou">信息类别</td>
           	  <td class="biaotou">发布信息名称</td>
           	  <td class="biaotou">发布时间</td>
           	  <td class="biaotou">信息状态</td>
           	  <td class="biaotou">操作</td>
           	  </tr>
           	  <c:forEach items="${transports}" var="transport">
           	  <tr>
           	  <td>货物信息</td> 
              <td><a href="<%=request.getContextPath()%>/pc/transport/detail?id=${transport.id}">${transport.taskContent}</a></td>
              <td><fmt:formatDate value="${transport.ctime}" type="both"/></td>
              <td><c:if test="${transport.status==1}">有效</c:if><c:if test="${transport.status==0}">无效</c:if></td>
              <td>
              <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png">
              </td>
              </tr>
              </c:forEach>
           	    
           	  <c:forEach items="${employees}" var="employee">
           	  <tr>
           	  <td>招聘</td> 
              <td><a href="<%=request.getContextPath()%>/pc/employee/detail?id=${employee.id}">${employee.title}</a></td>
              <td><fmt:formatDate value="${employee.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${employee.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>
              <td>
                  <a href="<%=request.getContextPath()%>/pc/employee/cancel?id=${employee.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach>
              
              <c:forEach items="${seeks}" var="seek">
           	  <tr>
           	  <td>求职</td> 
              <td><a href="<%=request.getContextPath()%>/pc/seek/detail?id=${seek.id}">${seek.title}</a></td>
              <td><fmt:formatDate value="${seek.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${seek.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td>
                  <a href="<%=request.getContextPath()%>/pc/seek/cancel?id=${seek.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach>  
               
              <c:forEach items="${secondcars}" var="secondcar" >
           	  <tr>
           	  <td>待售二手车</td> 
              <td><a href="<%=request.getContextPath()%>/pc/secondcar/detail?id=${secondcar.id}">${secondcar.title}</a></td>
              <td><fmt:formatDate value="${secondcar.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${secondcar.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td>
                  <a href="<%=request.getContextPath()%>/pc/secondcar/cancel?id=${secondcar.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${forseconds}" var="forsecond">
           	  <tr>
           	  <td>求购二手车</td>
              <td><a href="<%=request.getContextPath()%>/pc/forsecond/detail?id=${forsecond.id}">${forsecond.title}</a></td>
              <td><fmt:formatDate value="${forsecond.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${forsecond.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td>
                  <a href="<%=request.getContextPath()%>/pc/forsecond/cancel?id=${forsecond.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${newcars}" var="newcar">
           	  <tr>
           	  <td>新车</td>
              <td><a href="<%=request.getContextPath()%>/pc/newcar/detail?id=${newcar.id}">${newcar.title}</a></td>
              <td><fmt:formatDate value="${newcar.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${newcar.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>              
              <td>
                  <a href="<%=request.getContextPath()%>/pc/newcar/cancel?id=${newcar.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${insures}" var="insure">
           	  <tr>
           	  <td>保险</td> 
              <td><a href="<%=request.getContextPath()%>/pc/insure/detail?id=${insure.id}">${insure.title}</a></td>
              <td><fmt:formatDate value="${insure.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${insure.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>             
              <td>
                  <a href="<%=request.getContextPath()%>/pc/insure/cancel?id=${insure.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
              
              <c:forEach items="${takecars}" var="takecar">
           	  <tr>
           	  <td>带车</td> 
              <td><a href="<%=request.getContextPath()%>/pc/takecar/detail?id=${takecar.id}">${takecar.title}</a></td>
              <td><fmt:formatDate value="${takecar.ctime}" type="both"/></td>
              <td>
              <c:choose>
              <c:when test="${takecar.status==2}">有效</c:when>
              <c:otherwise>无效</c:otherwise>
              </c:choose>
              </td>             
              <td>
                  <a href="<%=request.getContextPath()%>/pc/takecar/cancel?id=${takecar.id}&collectFlag=2" onclick="{if(confirm('确定要取消收藏吗?')){return true;}return false;}">
                  <img src="<%=request.getContextPath()%>/fore/image/cancelCollect.png"></a>
              </td>
              </tr>
              </c:forEach> 
         </table>
<jsp:include  page="pc_page.jsp" flush="true"/> </div>
</div></div>
</div>
<jsp:include  page="erweima.jsp" flush="true"/>

</div>
</body>
</html>