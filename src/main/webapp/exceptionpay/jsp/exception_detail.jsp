<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%
  String path = request.getContextPath();
  String basePath = "https://"+request.getServerName()+path;
%>
<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>违约-查看</title>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">

  <link href="<%=basePath%>/back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
  <link href="<%=basePath%>/back/model/css/tableBase.css" type="text/css" rel="stylesheet">
  <link href="<%=basePath%>/back/model/css/detailPages.css" type="text/css" rel="stylesheet">
  <link href="<%=basePath%>/back/model/css/detailPagesAdd.css" type="text/css" rel="stylesheet">
  <link rel="stylesheet" href="back/model/css/companyNav.css">
  <link type="text/css" rel="stylesheet" href="<%=basePath%>/back/css/viewer.min.css" />
  <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
  <!--[if lt IE 9]>
  <script src="http://cdn.bootcss.com/html5shiv/3.7.2/html5shiv.min.js"></script>
  <script src="js/respond.min.js"></script>
  <![endif]-->
  <style>
    .contAbout .processingLog {
      height: auto;
    }
    .textContent {
      display: inline-block;
      width: 600px;
      white-space: normal;
    }
    .dataCont {
      vertical-align: top;
      display: inline-block;
      width: 155px;
      overflow: hidden;
    }
    .supplementImg{
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: auto;
    }
    .imgListBox{
      padding-left: 128px;
    }
    .imgListBox img{
      margin: 0px 10px 10px 0px;
    }
    .exceptionImgbox{
      margin: 16px 0px 0px;
    }
    .exceptionImgbox img {
      display: block;
      width: 110px;
      height: 110px;
      cursor: pointer;
    }
    .nowlb{
      vertical-align: middle !important;
    }
    .uplodaImg{
      margin-left: -20px;
    }
    .content-title{
      font-size: 18px;
      font-weight: 700;
    }
    .content-evaluate li{
      margin: 15px;
    }
    .content-evaluate .finalOpinion span{
      margin-left: 5px;
    }
    .pageContext .cf label{
      display: inline-block;
      width: 130px;
      vertical-align: top;
      text-align: right;
      line-height: 25px;
    }
  </style>
</head>
<body>

<div class="contentBox">
  <h1 class="titUrl">
    <span>当前所在位置：</span><span>用户反馈汇总管理</span><span>&nbsp;&gt;&nbsp;</span><span>违约/异常已处理-订金</span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">查看</span>
  </h1>
  <div class="main-content">
    <div class="systemParameter">
      <ul class="cf">
        <c:if test="${obj.invoiceTransport == '1' || obj.invoiceTransport == 1}">
          <li class="cf">
            <label> </label>
            <span style="font-size:20px;color: #ee0a24" class="lineHeight25" >开专票</span>
            <c:if test="${obj.isAssignOrder == '1' || obj.isAssignOrder == 1}">
              <span style="font-size:20px;color: #ee0a24" class="lineHeight25">货主指派单(订金为0元)</span>
            </c:if>
          </li>
        </c:if>
        <c:if test="${obj.thirdPartyPlatformType=='1'|| obj.thirdPartyPlatformType==1 }">
          <li class="cf" name="thirdparty_platform_type" id="thirdparty_platform_type" value="${obj.thirdPartyPlatformType}">
            <label>货源成交渠道：</label>
            <span class="lineHeight25">运满满</span>
          </li>
          <li class="cf">
            <label>运满满订单编号：</label>
            <span class="lineHeight25">${obj.thirdPartyPlatformOrderNo}</span>
          </li>
        </c:if>
        <li class="cf">
          <label>货源ID：</label>
          <span class="lineHeight25">${obj.tsId}</span>&nbsp;&nbsp;
          <a style="cursor:pointer" onclick="openWinAuto('back/model/html/transport/info_huowuxiangqing.html?goodsId=${obj.tsId}','infoDetailWin',1100,600)">查看</a>
        </li>
        <li class="cf">
          <label>运单号：</label>
          <span class="lineHeight25">${obj.tsOrderNo}</span>
        </li>
        <li class="cf">
          <label>货源类型：</label>
          <c:choose>
            <c:when test="${obj.excellentGoods==0}">普通货源</c:when>
            <c:when test="${obj.excellentGoods==1 && obj.excellentGoods == 1 && obj.excellentCar2 == 1}">优车2.0</c:when>
            <c:when test="${obj.excellentGoods==1 && obj.excellentGoods == 1 && obj.excellentCar2 != 1}">优车1.0</c:when>
            <c:when test="${obj.excellentGoods==2}">专车货源</c:when>
            <c:otherwise>--</c:otherwise>
          </c:choose>
        </li>
        <li class="cf">
          <label>是否抽佣：</label>
          <c:choose>
            <c:when test="${obj.commissionTransport == 1}">是</c:when>
            <c:when test="${obj.commissionTransport == 0}">否</c:when>
          </c:choose>
        </li>
        <li class="cf">
          <label>用车类型：</label>
          <span class="lineHeight25">${useCarTypeStr}</span>
        </li>
        <li class="cf">
           <label>发货渠道：</label>
           <c:choose>
             <c:when test="${obj.sourceType==1}">用户发布</c:when>
             <c:when test="${obj.sourceType==2}">调度代发</c:when>
             <c:when test="${obj.sourceType==3}">个人货主</c:when>
             <c:when test="${obj.sourceType==4}">运满满货源</c:when>
             <c:otherwise>--</c:otherwise>
           </c:choose>
        </li>
        <li class="cf">
          <label>找车方式：</label>
          <c:choose>
            <c:when test="${obj.publishType==1}">电议</c:when>
            <c:when test="${obj.publishType==2}">一口价</c:when>
            <c:otherwise>--</c:otherwise>
          </c:choose>
        </li>
        <li class="cf">
          <label>运费金额：</label>
          <span class="lineHeight25">
            <fmt:formatNumber type="number" value="${obj.carriageFee}" pattern="0.00" maxFractionDigits="2"/>元
          </span>
        </li>
        <li class="cf">
          <label>订单编号：</label>
          <span class="lineHeight25">${obj.orderId}</span>
        </li>
        <li class="cf">
          <label>是否超额保障：</label>
          <c:choose>
            <c:when test="${obj.excessCoverage == 1}">是</c:when>
            <c:when test="${obj.excessCoverage == 0}">否</c:when>
          </c:choose>
          <span class="lineHeight25" style="color: #ee0a24">  ${obj.userIdentity}</span>
        </li>
        <li class="cf">
          <div>
            <label>车主账号：</label>
            <span class="lineHeight25">${obj.payCellPhone}</span>
            <span class="lineHeight25" style="color: #ee0a24">下单等级：V${obj.carryPointLevel}</span>
          </div>

        </li>
        <li class="cf">
          <label>发货方账号：</label>
          <span class="lineHeight25">${obj.uploadCellphone}</span>
        </li>
        <li class="cf">
          <label>是否直客：</label>
          <c:choose>
            <c:when test="${obj.directCustomer == 1}">是</c:when>
            <c:when test="${obj.directCustomer == 0}">否</c:when>
          </c:choose>
        </li>
        <li class="cf">
          <label>是否使用保障金：</label>
          <span class="lineHeight25">
             <c:if test="${exAudit.deductUserId !=null }">
               是
             </c:if>

             <c:if test="${exAudit.isApplyCompensate == null }">
               否
             </c:if>
          </span>
        </li>
        <li class="cf">
          <label>补偿账号：</label>
          <span class="lineHeight25">${exAudit.deductUserPhone !=null?exAudit.deductUserPhone:""}</span>
        </li>
        <li class="cf">
          <label>订金金额：</label>
          <span class="lineHeight25">
          	 <fmt:formatNumber type="number" value="${obj.totalOrderAmount*0.01}" pattern="0.00" maxFractionDigits="2"/>元
          </span>
          <c:if test="${obj.refundFlag == 1}">(退还)</c:if>
          <c:if test="${obj.refundFlag == 0}">(不退还)</c:if>
          <span class="lineHeight25">
          	（减免 <fmt:formatNumber type="number" value="${obj.couponAmount*0.01}" pattern="0.00" maxFractionDigits="2"/>元，
          </span>
          <span class="lineHeight25">
          	实付 <fmt:formatNumber type="number" value="${obj.payAmount*0.01}" pattern="0.00" maxFractionDigits="2"/>元)
          </span>
        </li>
        <li class="cf" id="publishUserName_li">
            <label>代调人员：</label>
            <span class="lineHeight25">${obj.publishUserName}</span>
            </span>
          </li>
          <li class="cf" id="technicalServiceFee_li">
            <label>技术服务费：</label>
            <span class="lineHeight25">${obj.technicalServiceFee*0.01}元（减免0.00元，实付${obj.technicalServiceFee*0.01}元）</span>
            </span>
          </li>

        <li class="cf">
          <label>上报人身份：</label>
          <span class="lineHeight25">
            <c:choose>
              <c:when test="${obj.exParty=='1' || obj.exParty==1}">车主</c:when>
              <c:otherwise>货主</c:otherwise>
            </c:choose>
          </span>
        </li>
<%--        <li class="cf">--%>
<%--          <label>装车状态：</label>--%>
<%--          <c:choose>--%>
<%--            <c:when test="${obj.loadingStatus=='1' || obj.loadingStatus==1}">已装车</c:when>--%>
<%--            <c:when test="${obj.loadingStatus=='0' || obj.loadingStatus==0}">未装车</c:when>--%>
<%--            <c:otherwise>--</c:otherwise>--%>
<%--          </c:choose>--%>
<%--          <c:choose>--%>
<%--            <c:when test="${obj.loadingStatus==1 && obj.loadingChildStatus==1}">-运输中</c:when>--%>
<%--            <c:when test="${obj.loadingStatus==1 && obj.loadingChildStatus==2}">-已送达未卸货</c:when>--%>
<%--            <c:when test="${obj.loadingStatus==1 && obj.loadingChildStatus==3}">-已送达已卸货</c:when>--%>
<%--            <c:when test="${obj.loadingStatus==0 && obj.loadingChildStatus==1}">-车方未出发</c:when>--%>
<%--            <c:when test="${obj.loadingStatus==0 && obj.loadingChildStatus==2}">-出发未到装货地</c:when>--%>
<%--            <c:when test="${obj.loadingStatus==0 && obj.loadingChildStatus==3}">-车方已到装货地</c:when>--%>
<%--            <c:otherwise></c:otherwise>--%>
<%--          </c:choose>--%>
<%--        </li>--%>
        <li class="cf">
          <label>上报时间：</label>
          <span class="lineHeight25"><fmt:formatDate value="${obj.exTime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
        </li>
        <li class="cf">
          <label>上报类型：</label>
          <span class="lineHeight25">
            <c:choose>
              <c:when test="${obj.exParty=='1' || item.exParty==1}">
                <c:forEach items="${ex_party_car}" var="item">
                  <c:if test="${obj.exType==item.value}">
                    ${item.name}
                  </c:if>
                </c:forEach>
              </c:when>
              <c:otherwise>
                <c:forEach items="${ex_party_goods}" var="item">
                  <c:if test="${obj.exType==item.value}">
                    ${item.name}
                  </c:if>
                </c:forEach>
              </c:otherwise>
            </c:choose>
          </span>
        </li>

        <c:forEach items="${vouchers}" var="item">
          <c:if test="${item.exParty=='1'}">
            <c:if test="${item.voucherType==1}">
              <li class="cf">
                <label>车方上报原因：</label>
                <span class="lineHeight25">${item.proofReason}</span>
              </li>
              <li class="cf">
                <label>车方上报凭证：</label>
                <div class="photoWareBox cf imgListBox" data-tit="2">
                  <div class="exceptionImgbox fl supplementImg img-${item.id}">
                    <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                    </c:forEach>
                  </div>
                </div>
              </li>
            </c:if>
            <c:if test="${item.voucherType==2}">
              <li class="cf">
                <label>车方补充时间：</label>
                <span class="lineHeight25"><fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
              </li>
              <li class="cf">
                <label>车方补充原因：</label>
                <span class="lineHeight25">${item.proofReason}</span>
              </li>
              <li class="cf">
                <label>车方补充凭证：</label>
                <div class="photoWareBox cf imgListBox" data-tit="2">
                  <div class="exceptionImgbox fl supplementImg img-${item.id}">
                    <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                    </c:forEach>
                  </div>
                </div>
              </li>
            </c:if>
          </c:if>
          <c:if test="${item.exParty=='2'}">
            <c:if test="${item.voucherType==1}">
              <li class="cf">
                <label>货方上报原因：</label>
                <span class="lineHeight25">${item.proofReason}</span>
              </li>
              <li class="cf">
                <label>货方上报凭证：</label>
                <div class="photoWareBox cf imgListBox" data-tit="2">
                  <div class="exceptionImgbox fl supplementImg img-${item.id}">
                    <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                    </c:forEach>
                  </div>
                </div>
              </li>
            </c:if>
            <c:if test="${item.voucherType==2}">
              <li class="cf">
                <label>货方补充时间：</label>
                <span class="lineHeight25"><fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
              </li>
              <li class="cf">
                <label>货方补充原因：</label>
                <span class="lineHeight25">${item.proofReason}</span>
              </li>
              <li class="cf">
                <label>货方补充凭证：</label>
                <div class="photoWareBox cf imgListBox" data-tit="2">
                  <div class="exceptionImgbox fl supplementImg img-${item.id}">
                    <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                    </c:forEach>
                  </div>
                </div>
              </li>
            </c:if>
          </c:if>
        </c:forEach>

        <li class="cf">
          <label>操作人：</label>
          <span class="lineHeight25">${obj.actionName}</span>
        </li>

        <c:if test="${obj.exStatus=='3'}">
          <li class="cf">
            <label>用户撤销时间：</label>
            <span class="lineHeight25"><fmt:formatDate value="${obj.cancelExTime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
          </li>
        </c:if>
      </ul>
      <div class="upCont"></div>

      <div class="pageContext">
        <h2>处理内容</h2>
        <div class="contBox cf">
          <label class="nowlb fl">处理记录：</label>
          <div class="contAbout fl">
            <c:forEach items="${records}" var="item">
              <div class="processingLog">
            <span class="dataCont">
            <fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" />
            </span>
                <span class="textContent">${item.opinion}</span>
              </div>
            </c:forEach>
          </div>
        </div>
        <div class="nowSuggest">
          <label class="nowlb" for="">本次处理意见：</label>
          <span>${obj.resultOpinion}</span>
        </div>
        <li class="cf">
          <label>纠纷类型：</label>
          <span class="lineHeight25">
              <c:forEach items="${dispute_type}" var="item">
                <c:if test="${obj.disputeType == item.value }">${item.name}</c:if>
              </c:forEach>
          </span>
        </li>
        <li class="cf">
          <label>放空公里数：</label>
          <span class="lineHeight25">
            ${obj.emptyingKm}公里
          </span>
        </li>
        <li class="cf">
          <label>放空车型：</label>
          <span class="lineHeight25">
              <c:forEach items="${emptying_cartype}" var="item">
                <c:if test="${obj.emptyingCartype == item.value }">${item.name}</c:if>
              </c:forEach>
              <c:if test="${obj.emptyingCartypeDetail != null && obj.emptyingCartypeDetail != '' }">
                - ${obj.emptyingCartypeDetail}
              </c:if>
          </span>
        </li>
        <li class="cf">
          <label>放空原因：</label>
          <span class="lineHeight25">
              <c:forEach items="${emptying_reason}" var="item">
                <c:if test="${obj.emptyingReason == item.value }">${item.name}</c:if>
              </c:forEach>
              <c:if test="${obj.emptyingReasonDetail != null && obj.emptyingReasonDetail != '' }">
                - ${obj.emptyingReasonDetail}
              </c:if>
          </span>
        </li>

        <li class="cf">
          <label>是否申请补偿：</label>
          <span class="lineHeight25">
            <c:if test="${exAudit.isApplyCompensate=='1' }">
              是
            </c:if>

             <c:if test="${exAudit.isApplyCompensate!='1' }">
               否
             </c:if>
          </span>
        </li>
        <c:if test="${exAudit.isApplyCompensate=='1' }">
          <li class="cf">
            <label>补偿对象：</label>
            <span class="lineHeight25">
            <c:if test="${exAudit.compensateTarget=='1' }">
              车方
            </c:if>

             <c:if test="${exAudit.compensateTarget=='2' }">
               货方
             </c:if>
          </span>
          </li>
          <li class="cf">
            <label>补偿类型：</label>
            <span class="lineHeight25">
              <c:if test="${exAudit.compensateType=='1' }">
                订金补偿
              </c:if>
             <c:if test="${exAudit.compensateType=='2' }">
               放空补偿
             </c:if>
              <c:if test="${exAudit.compensateType=='3' }">
                压车补偿
              </c:if>
              <c:if test="${exAudit.compensateType=='4' }">
                距离加价补偿
              </c:if>
             <c:if test="${exAudit.compensateType=='5' }">
               吨位加价补偿
             </c:if>
              <c:if test="${exAudit.compensateType=='6' }">
                拉跑货补偿
              </c:if>
              <c:if test="${exAudit.compensateType=='7' }">
                倒卖货补偿
              </c:if>
             <c:if test="${exAudit.compensateType=='8' }">
               倒送货补偿
             </c:if>
              <c:if test="${exAudit.compensateType=='9' }">
                防溢出补偿
              </c:if>
              <c:if test="${exAudit.compensateType=='10' }">
                其他
              </c:if>
          </span>
          </li>
          <li class="cf">
            <label>补偿方式：</label>
            <span class="lineHeight25">
            <c:if test="${exAudit.compensateMethod=='1' }">
              订金减免券
            </c:if>
             <c:if test="${exAudit.compensateMethod=='2' }">
               现金红包
             </c:if>
          </span>
          </li>

          <li class="cf">
            <label>补偿原因：</label>
            <span class="lineHeight25">${exAudit.compensateReason}</span>
          </li>
          <li class="cf">
            <label>补偿金额：</label>
            <span class="lineHeight25">${exAudit.compensateAmount}</span>
          </li>
          <li class="cf">
            <label>保障类型：</label>
            <span class="lineHeight25">
              <c:if test="${exAudit.safeguardType=='1' }">
                司机等级保障
              </c:if>
             <c:if test="${exAudit.safeguardType=='2' }">
               优车保障
             </c:if>
              <c:if test="${exAudit.safeguardType=='3' }">
                专车保障
              </c:if>
              <c:if test="${exAudit.safeguardType=='4' }">
                直客保障
              </c:if>
             <c:if test="${exAudit.safeguardType=='5' }">
               代调保障
             </c:if>
              <c:if test="${exAudit.safeguardType=='6' }">
                专属客服保障
              </c:if>
          </span>
          </li>
          <li class="cf">
            <label style ="width:210px; display: inline-block;">货方线下已赔付金额：</label>
            <span class="lineHeight25">${exAudit.goodsAmountOffline}</span>
          </li>
        </c:if>

        <!-- 是否申请扣除货方保证金（优货）-->
        <li class="cf">
          <label style ="width:210px; display: inline-block;">是否申请扣除货方保证金(优货)：</label>
          <span class="lineHeight25">
             <c:if test="${depositHandleAudit!=null && (depositHandleAudit.finalAuditStatus==0 || depositHandleAudit.finalAuditStatus==1)}">
               是
             </c:if>
            <c:if test="${depositHandleAudit==null || depositHandleAudit.finalAuditStatus==2}">
              否
            </c:if>
          </span>
        </li>
        <c:if test="${depositHandleAudit!=null && (depositHandleAudit.finalAuditStatus==0 || depositHandleAudit.finalAuditStatus==1)}">
          <li class="cf">
            <label>扣款原因：</label>
            <span class="lineHeight25">${depositHandleAudit.refundReason}</span>
          </li>
          <li class="cf">
            <label>扣款金额：</label>
            <span class="lineHeight25">${depositHandleAudit.refundAmount}</span>
          </li>
          <li class="cf">
            <label>扣除审核状态：</label>
            <span class="lineHeight25">
              <c:if test="${depositHandleAudit.finalAuditStatus==0}">
                审核中
              </c:if>
              <c:if test="${depositHandleAudit.finalAuditStatus==1}">
                审核通过
              </c:if>
              <c:if test="${depositHandleAudit.finalAuditStatus==2}">
                审核不通过
              </c:if>
            </span>
          </li>
          <li class="cf">
            <label>审核人：</label>
            <span class="lineHeight25">${depositHandleAudit.auditUserName}</span>
          </li>
        </c:if>

        <li class="cf">
          <label>是否违约：</label>
          <span class="lineHeight25">
            <c:if test="${obj.isBreak=='0' }">
              否
            </c:if>
             <c:if test="${obj.isBreak=='1' }">
               违规
             </c:if>
             <c:if test="${obj.isBreak=='2' }">
               严重违规
             </c:if>
            </span>
        </li>
        <div class="suggestcheck checkLook">
          <label class="nowlb" for="">订金建议分配：</label>
          <label class="labelrad" for="">车主：</label>
          <span><%-- ${obj.carAmount!=null?obj.carAmount:"0"} --%>
         <fmt:formatNumber type="number" value="${obj.carAmount!=null?obj.carAmount*0.01:'0'}" pattern="0.00" maxFractionDigits="2"/>
         元</span>
          <label class="labelrad" for="">发货方：</label>
          <span><%-- ${obj.goodsAmount!=null?obj.goodsAmount:"0"} --%>
                 <fmt:formatNumber type="number" value="${obj.goodsAmount!=null?obj.goodsAmount*0.01:'0'}" pattern="0.00" maxFractionDigits="2"/>
                   元</span>
        </div>
        <div class="suggestcheck checkLook">
          <label class="nowlb" for="">技术服务费分配：</label>
          <label class="labelrad" for="">我司：</label>
          <span>
          <fmt:formatNumber type="number" value="${obj.platformServiceAmount!=null?obj.platformServiceAmount*0.01:'0'}" pattern="0.00" maxFractionDigits="2"/>
          元</span>
          <label class="labelrad" for="">车主：</label>
          <span>
          <fmt:formatNumber type="number" value="${obj.carServiceAmount!=null?obj.carServiceAmount*0.01:'0'}" pattern="0.00" maxFractionDigits="2"/>
          元</span>
        </div>
        <!-- 是否完单后退技术服务费 -->
        <li class="cf">
          <label style ="width:210px; display: inline-block;">是否完单后退技术服务费：</label>
          <span class="lineHeight25">
             <c:if test="${techRefundAudit != null && techRefundAudit.finalAuditStatus != 5}">
               是
             </c:if>
             <c:if test="${techRefundAudit == null || techRefundAudit.finalAuditStatus == 5}">
               否
             </c:if>
          </span>
        </li>
        <c:if test="${techRefundAudit != null && techRefundAudit.finalAuditStatus != 5}">
          <div class="suggestcheck checkLook">
            <label class="nowlb" for="">完单后技术服务费退还：</label>
            <label class="labelrad" for="">我司：</label>
            <span>${refundPlatformServiceAmount}元</span>
            <label class="labelrad" for="">车主：</label>
            <span>${refundCarServiceAmount}元</span>
          </div>
        </c:if>
        <li class="cf">
          <label class="nowlb">责任方：</label>
          <c:forEach items="${ex_fault_party}" var="item">
            <c:if test="${obj.exFaultParty==item.value }">
              ${item.name}
            </c:if>
          </c:forEach>
        </li>
        <c:if test="${fn:indexOf('137',obj.exFaultParty) >= 0 }">
          <li class="cf">
            <label class="nowlb">货方责任人：</label>
            <span>${obj.responsiblePartyTwo==1?'货站':'货主'}</span>
          </li>
        </c:if>

        <div class="nowSuggest">
          <label class="nowlb" for="">修改上报类型：</label>
          <c:choose>
            <c:when test="${obj.exFaultParty=='1' || item.exFaultParty==1}">
              <c:forEach items="${goods_ex_fault_party}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}</c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='2' || item.exFaultParty==2}">
              <c:forEach items="${car_ex_fault_party}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}</c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='3' || item.exFaultParty==3}">
              <c:forEach items="${unclear_ex_fault_party}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}</c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='4' || item.exFaultParty==4}">
              <c:forEach items="${both_no_ex_fault_party}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}
                </c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='6' || item.exFaultParty==6}">
              <c:forEach items="${goods_car_ex_fault_party_new}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}
                </c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='7' || item.exFaultParty==7}">
              <c:forEach items="${goods_ex_fault_party_new}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}
                </c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='8' || item.exFaultParty==8}">
              <c:forEach items="${car_unclear_ex_fault_party_new}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}
                </c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='9' || item.exFaultParty==9}">
              <c:forEach items="${car_ex_fault_party_new}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}
                </c:if>
              </c:forEach>
            </c:when>
            <c:when test="${obj.exFaultParty=='10' || item.exFaultParty==10}">
              <c:forEach items="${goods_unclear_ex_fault_party_new}" var="item">
                <c:if test="${obj.exTypeOther==item.value }">
                  ${item.name}
                </c:if>
              </c:forEach>
            </c:when>
            <c:otherwise>
              <c:choose>
                <c:when test="${obj.exParty=='1' || item.exParty==1}">
                  <c:forEach items="${ex_party_car}" var="item">
                    <c:if test="${obj.exTypeOther==item.value}">
                      ${item.name}
                    </c:if>
                  </c:forEach>
                </c:when>
                <c:otherwise>
                  <c:forEach items="${ex_party_goods}" var="item">
                    <c:if test="${obj.exTypeOther==item.value}">
                      ${item.name}
                    </c:if>
                  </c:forEach>
                </c:otherwise>
              </c:choose>
            </c:otherwise>
          </c:choose>
          <c:if test="${obj.exTypeOtherDetail != null && obj.exTypeOtherDetail != '' }">
            - ${obj.exTypeOtherDetail}
          </c:if>
        </div>
<%--        <label class="nowlb">核实装车状态：</label>--%>
<%--        <c:choose>--%>
<%--          <c:when test="${obj.verifyLoadingStatus=='1' || obj.verifyLoadingStatus==1}">已装车</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus=='0' || obj.verifyLoadingStatus==0}">未装车</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus=='2' || obj.verifyLoadingStatus==2}">已卸车</c:when>--%>
<%--          <c:otherwise>--</c:otherwise>--%>
<%--        </c:choose>--%>
<%--        <c:choose>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==1 && obj.verifyLoadingChildStatus==1}">- 运输中</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==1 && obj.verifyLoadingChildStatus==2}">- 已送达未卸货</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==1 && obj.verifyLoadingChildStatus==3}">- 已送达已卸货</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==0 && obj.verifyLoadingChildStatus==1}">- 车方未出发</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==0 && obj.verifyLoadingChildStatus==2}">- 出发未到装货地</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==0 && obj.verifyLoadingChildStatus==3}">- 车方已到装货地</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==2 && obj.verifyLoadingChildStatus==1}">- 未付运费</c:when>--%>
<%--          <c:when test="${obj.verifyLoadingStatus==2 && obj.verifyLoadingChildStatus==2}">- 已付运费</c:when>--%>

<%--          <c:otherwise></c:otherwise>--%>
<%--        </c:choose>--%>

        <li class="cf">
          <label>订单状态：</label>
          <span class="lineHeight25">
            <c:if test="${obj.orderContinue=='0' }">
              订单取消
            </c:if>
             <c:if test="${obj.orderContinue=='1' }">
               订单继续
             </c:if>

              <c:choose>
                <c:when test="${obj.orderNewStatus==5}">- 待接单</c:when>
                <c:when test="${obj.orderNewStatus==10}">- 待签署</c:when>
                <c:when test="${obj.orderNewStatus==15}">- 待装货</c:when>
                <c:when test="${obj.orderNewStatus==20}">- 待收货/待卸货</c:when>
                <c:when test="${obj.orderNewStatus==25}">- 待收运费/待付运费</c:when>
                <c:when test="${obj.orderNewStatus==30}">- 已完成</c:when>
                <c:when test="${obj.orderNewStatus==35}">- 已取消</c:when>
                <c:otherwise></c:otherwise>
              </c:choose>
            </span>
        </li>

        </li>
        <div class="contentBox">
          <div class="main-content">
            <ul class="cf">
              <li class="cf">
                <label class="uplodaImg"><i class="xing">*</i>上传凭证照片：</label>
                <div id="uploadFileEmpty"></div>
                <div class="photoWareBox cf" id="backListBox" data-tit="2">
                  <div class="BackImgbox fl"  style="">
                    <input type="hidden" id="businessId" name="businessId" value="${obj.id}">
                    <input type="hidden" id="fileType"   name="fileType"   value="1">
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <c:if test="${obj.lastCarBlackListOrdersLog!=null}">
          <div class="contBox cf">
            <label class="nowlb fl">【车方处罚】</label>
          </div>
          <div class="contBox cf">
            <label class="nowlb fl">是否加入黑名单：</label>
            <div class="contAbout fl">
              <c:if test="${obj.lastCarBlackListOrdersLog.restrictActionType>0}">
                <c:if test="${obj.lastCarBlackListOrdersLog.restrictActionType==1 && obj.lastCarBlackListOrdersLog.restrictNum == -1}">永久拉黑</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.restrictActionType==1 && obj.lastCarBlackListOrdersLog.restrictNum > 0}">拉黑${obj.lastCarBlackListOrdersLog.restrictNum}天</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.restrictActionType==2 && obj.lastCarBlackListOrdersLog.restrictNum > 0}">增加拉黑${obj.lastCarBlackListOrdersLog.restrictNum}天</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.restrictActionType==3 && obj.lastCarBlackListOrdersLog.restrictNum > 0}">减少拉黑${obj.lastCarBlackListOrdersLog.restrictNum}天</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.restrictActionType==4}">解除拉黑</c:if>
              </c:if>
            </div>
          </div>

          <div class="contBox cf">
            <label class="nowlb fl">找货限制：</label>
            <div class="contAbout fl">
              <c:if test="${obj.lastCarBlackListOrdersLog.carRestrictActionType>0}">
                <c:if test="${obj.lastCarBlackListOrdersLog.carRestrictActionType==1 && obj.lastCarBlackListOrdersLog.carRestrictNum == -1}">永久限制找货</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.carRestrictActionType==1 && obj.lastCarBlackListOrdersLog.carRestrictNum > 0}">限制找货${obj.lastCarBlackListOrdersLog.carRestrictNum}天</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.carRestrictActionType==2 && obj.lastCarBlackListOrdersLog.carRestrictNum > 0}">增加${obj.lastCarBlackListOrdersLog.carRestrictNum}天</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.carRestrictActionType==3 && obj.lastCarBlackListOrdersLog.carRestrictNum > 0}">减少${obj.lastCarBlackListOrdersLog.carRestrictNum}天</c:if>
                <c:if test="${obj.lastCarBlackListOrdersLog.carRestrictActionType==4}">解除处罚</c:if>
              </c:if>
            </div>
          </div>

          <div class="contBox cf">
            <label class="nowlb fl">原因：</label>
            <div class="contAbout fl">
                ${obj.lastCarBlackListOrdersLog.restrictCause}
            </div>
          </div>
        </c:if>

        <c:if test="${obj.lastGoodsBlackListOrdersLog!=null}">
          <div class="contBox cf">
            <label class="nowlb fl">【货方处罚】</label>
          </div>
          <div class="contBox cf">
            <label class="nowlb fl">是否加入黑名单：</label>
            <div class="contAbout fl">
              <c:if test="${obj.lastGoodsBlackListOrdersLog.restrictActionType>0}">
                <c:if test="${obj.lastGoodsBlackListOrdersLog.restrictActionType==1 && obj.lastGoodsBlackListOrdersLog.restrictNum == -1}">永久拉黑</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.restrictActionType==1 && obj.lastGoodsBlackListOrdersLog.restrictNum > 0}">拉黑${obj.lastGoodsBlackListOrdersLog.restrictNum}天</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.restrictActionType==2 && obj.lastGoodsBlackListOrdersLog.restrictNum > 0}">增加拉黑${obj.lastGoodsBlackListOrdersLog.restrictNum}天</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.restrictActionType==3 && obj.lastGoodsBlackListOrdersLog.restrictNum > 0}">减少拉黑${obj.lastGoodsBlackListOrdersLog.restrictNum}天</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.restrictActionType==4}">解除拉黑</c:if>
              </c:if>
            </div>
          </div>

          <div class="contBox cf">
            <label class="nowlb fl">找货限制：</label>
            <div class="contAbout fl">
              <c:if test="${obj.lastGoodsBlackListOrdersLog.goodsRestrictActionType>0}">
                <c:if test="${obj.lastGoodsBlackListOrdersLog.goodsRestrictActionType==1 && obj.lastGoodsBlackListOrdersLog.goodsRestrictNum == -1}">永久限制发货</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.goodsRestrictActionType==1 && obj.lastGoodsBlackListOrdersLog.goodsRestrictNum > 0}">限制发货${obj.lastGoodsBlackListOrdersLog.goodsRestrictNum}天</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.goodsRestrictActionType==2 && obj.lastGoodsBlackListOrdersLog.goodsRestrictNum > 0}">增加${obj.lastGoodsBlackListOrdersLog.goodsRestrictNum}天</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.goodsRestrictActionType==3 && obj.lastGoodsBlackListOrdersLog.goodsRestrictNum > 0}">减少${obj.lastGoodsBlackListOrdersLog.goodsRestrictNum}天</c:if>
                <c:if test="${obj.lastGoodsBlackListOrdersLog.goodsRestrictActionType==4}">解除处罚</c:if>
              </c:if>
            </div>
          </div>

          <div class="contBox cf">
            <label class="nowlb fl">原因：</label>
            <div class="contAbout fl">
                ${obj.lastGoodsBlackListOrdersLog.restrictCause}
            </div>
          </div>
        </c:if>

        <label class="nowlb fl">    </label>
        <c:if test="${obj.remark!=null}">
          <div class="contBox cf">
            <label class="nowlb fl">备注：</label>
            <div class="contAbout fl">
                ${obj.remark}
            </div>
          </div>
        </c:if>

        <c:if test="${obj.blackListOrdersLogList!=null}">
          <div class="contBox cf">
            <label class="nowlb fl">【处罚处理记录】</label>
          </div>
          <div class="contBox cf">
            <div class="contAbout fl">
              <c:forEach items="${obj.blackListOrdersLogList}" var="item">
                <div class="processingLog">
                  <span class="dataCont">
                    <fmt:formatDate value="${item.createTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                  </span>
                  <span class="textContent">
                    <c:if test="${item.restrictActionType>0}">
                      <c:if test="${item.restrictActionType==1 && item.restrictNum == -1}">永久拉黑</c:if>
                      <c:if test="${item.restrictActionType==1 && item.restrictNum > 0}">拉黑${item.restrictNum}天</c:if>
                      <c:if test="${item.restrictActionType==2 && item.restrictNum > 0}">增加拉黑${item.restrictNum}天</c:if>
                      <c:if test="${item.restrictActionType==3 && item.restrictNum > 0}">减少拉黑${item.restrictNum}天</c:if>
                      <c:if test="${item.restrictActionType==4}">解除拉黑</c:if>
                    </c:if>
                  <c:if test="${item.restrictActionType>0 && item.carRestrictActionType>0}">/</c:if>
                   <c:if test="${item.carRestrictActionType>0}">
                     <c:if test="${item.carRestrictActionType==1 && item.carRestrictNum == -1}">永久限制找货</c:if>
                     <c:if test="${item.carRestrictActionType==1 && item.carRestrictNum > 0}">限制找货${item.carRestrictNum}天</c:if>
                     <c:if test="${item.carRestrictActionType==2 && item.carRestrictNum > 0}">限制找货增加${item.carRestrictNum}天</c:if>
                     <c:if test="${item.carRestrictActionType==3 && item.carRestrictNum > 0}">限制找货减少${item.carRestrictNum}天</c:if>
                     <c:if test="${item.carRestrictActionType==4}">解除限制找货处罚</c:if>
                   </c:if>
                  <c:if test="${(item.carRestrictActionType>0 || item.restrictActionType>0) && item.goodsRestrictActionType>0}">/</c:if>
                   <c:if test="${item.goodsRestrictActionType>0}">
                     <c:if test="${item.goodsRestrictActionType==1 && item.goodsRestrictNum == -1}">永久限制发货</c:if>
                     <c:if test="${item.goodsRestrictActionType==1 && item.goodsRestrictNum > 0}">限制发货${item.goodsRestrictNum}天</c:if>
                     <c:if test="${item.goodsRestrictActionType==2 && item.goodsRestrictNum > 0}">限制发货增加${item.goodsRestrictNum}天</c:if>
                     <c:if test="${item.goodsRestrictActionType==3 && item.goodsRestrictNum > 0}">限制发货减少${item.goodsRestrictNum}天</c:if>
                     <c:if test="${item.goodsRestrictActionType==4}">解除限制发货处罚</c:if>
                   </c:if>
                </span>
                  <span class="textContent">
                      ${item.restrictCause}
                  </span>
                </div>
              </c:forEach>
            </div>
          </div>
        </c:if>

        <c:forEach items="${evaluates}" var="item">
          <div class="contentBox content-evaluate">
            <c:if test="${item.evaluateIdentity=='1'}">
              <ul class="car-box">
                <span class="content-title">车方服务评价</span>
                <li class="finalOpinion cf" id="">
                  <label>判责处理结果:</label>
                  <span>
                  <c:choose>
                    <c:when test="${item.dealResultEvaluate=='1' || item.dealResultEvaluate==1}">满意</c:when>
                    <c:when test="${item.dealResultEvaluate=='2' || item.dealResultEvaluate==2}">一般</c:when>
                    <c:when test="${item.dealResultEvaluate=='3' || item.dealResultEvaluate==3}">不满意</c:when>
                    <c:otherwise>--</c:otherwise>
                  </c:choose>
                </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label>判责处理时长:</label>
                  <span>
              <c:choose>
                <c:when test="${item.dealTimeEvaluate=='1' || item.dealTimeEvaluate==1}">满意</c:when>
                <c:when test="${item.dealTimeEvaluate=='2' || item.dealTimeEvaluate==2}">一般</c:when>
                <c:when test="${item.dealTimeEvaluate=='3' || item.dealTimeEvaluate==3}">不满意</c:when>
                <c:otherwise>--</c:otherwise>
              </c:choose>
                </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label>客服服务态度:</label>
                  <span>
                  <c:choose>
                    <c:when test="${item.serviceAttitudeEvaluate=='1' || item.serviceAttitudeEvaluate==1}">满意</c:when>
                    <c:when test="${item.serviceAttitudeEvaluate=='2' || item.serviceAttitudeEvaluate==2}">一般</c:when>
                    <c:when test="${item.serviceAttitudeEvaluate=='3' || item.serviceAttitudeEvaluate==3}">不满意</c:when>
                    <c:otherwise>--</c:otherwise>
                  </c:choose>
                </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label>备注:</label>
                  <span>
                      ${item.remark}
                  </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label style="width: 150px;">车方服务评价提交时间:</label>
                  <span> <fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
                </li>
              </ul>
            </c:if>
            <c:if test="${item.evaluateIdentity=='2'}">
              <ul class="goods-box">
                <span class="content-title">货方服务评价</span>
                <li class="finalOpinion cf" id="">
                  <label>判责处理结果:</label>
                  <span>
                  <c:choose>
                    <c:when test="${item.dealResultEvaluate=='1' || item.dealResultEvaluate==1}">满意</c:when>
                    <c:when test="${item.dealResultEvaluate=='2' || item.dealResultEvaluate==2}">一般</c:when>
                    <c:when test="${item.dealResultEvaluate=='3' || item.dealResultEvaluate==3}">不满意</c:when>
                    <c:otherwise>--</c:otherwise>
                  </c:choose>
                </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label>判责处理时长:</label>
                  <span>
                  <c:choose>
                    <c:when test="${item.dealTimeEvaluate=='1' || item.dealTimeEvaluate==1}">满意</c:when>
                    <c:when test="${item.dealTimeEvaluate=='2' || item.dealTimeEvaluate==2}">一般</c:when>
                    <c:when test="${item.dealTimeEvaluate=='3' || item.dealTimeEvaluate==3}">不满意</c:when>
                    <c:otherwise>--</c:otherwise>
                  </c:choose>
                </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label>客服服务态度:</label>
                  <span>
                  <c:choose>
                    <c:when test="${item.serviceAttitudeEvaluate=='1' || item.serviceAttitudeEvaluate==1}">满意</c:when>
                    <c:when test="${item.serviceAttitudeEvaluate=='2' || item.serviceAttitudeEvaluate==2}">一般</c:when>
                    <c:when test="${item.serviceAttitudeEvaluate=='3' || item.serviceAttitudeEvaluate==3}">不满意</c:when>
                    <c:otherwise>--</c:otherwise>
                  </c:choose>
                </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label>备注:</label>
                  <span>
                      ${item.remark}
                  </span>
                </li>
                <li class="finalOpinion cf" id="">
                  <label style="width: 150px;">货方服务评价提交时间:</label>
                  <span>  <fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
                </li>
              </ul>
            </c:if>
          </div>
        </c:forEach>
        <div class="divButton">
          <button class="button" onclick="javascript:window.close();">返回</button>
        </div>
      </div>
    </div>

  </div>

  <script type="text/javascript" src="<%=basePath%>/exceptionpay/js/jquery-1.11.1.min.js"></script>
  <script type="text/javascript" src="<%=basePath%>/back/model/js/viewer.min.js"></script>
  <script type="text/javascript" src="<%=basePath%>/exceptionpay/js/bootstrap.js"></script>
  <script type="text/javascript" src="<%=basePath%>/back/model/js/tyt_common.js"></script>
  <script type="text/javascript" src="<%=basePath%>/back/jurisdiction/jsp/js/common.js"></script>
  <script type="text/javascript" src="<%=basePath%>/back/jsp/js/car.js"></script>
  <!-- 异常上报文件处理js-->
  <script type="text/javascript" src="<%=basePath%>/exceptionpay/js/file_info_ex_detail.js"></script>
  <script type="text/javascript" src="<%=basePath%>/back/jsp/js/car.js"></script>
  <script type="text/javascript">
    $(".contTit").css('lineHeight',$(".contBox").height()+'px');
  </script>
</body>
</html>
<script type="text/javascript">

  function forwardPage(){

  }
</script>

