<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%
String path = request.getContextPath();
String basePath = "https://"+request.getServerName()+path;
%>
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>违约-编辑</title>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<link href="back/model/css/bootstrap.css" type="text/css" rel="stylesheet">
<link href="back/model/css/tableBase.css" type="text/css" rel="stylesheet">
<link href="back/model/css/detailPages.css" type="text/css" rel="stylesheet">
<link href="back/model/css/detailPagesAdd.css" type="text/css" rel="stylesheet">
<link rel="stylesheet" href="back/model/css/companyNav.css">
<link type="text/css" rel="stylesheet" href="<%=basePath%>/back/css/viewer.min.css" />
<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
  <script src="http://cdn.bootcss.com/html5shiv/3.7.2/html5shiv.min.js"></script>
  <script src="js/respond.min.js"></script>
<![endif]-->


    <link href="<%=basePath%>/exceptionpay/css/punish.css"
    " type="text/css" rel="stylesheet">
    <style>

        .textContent {
            display: inline-block;
            width: 600px;
            white-space: normal;
        }
        .dataCont {
            vertical-align: top;
            display: inline-block;
            width: 155px;
            overflow: hidden;
        }
        .supplementImg{
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            height: auto;
        }
        .imgListBox{
            padding-left: 128px;
        }
        .imgListBox img{
            margin: 0px 10px 10px 0px;
        }
        .exceptionImgbox{
            margin: 16px 0px 0px;
        }
        .exceptionImgbox img {
            display: block;
            width: 110px;
            height: 110px;
            cursor: pointer;
        }
        .nowlb{
            vertical-align: middle !important;
        }
        .empty,#emptying_cartype_detail,#emptying_reason_detail,#ex_type_other_detail{
            display: none;
        }
        #compensateCellphone {

        }
        #compensateCellphone > span {
            margin: 0 0 0 5px;
        }
        #compensateCellphone > em {
            color: red;
        }
        #compensateCellphone .inpColor {
            color: #278dfb;
        }
        .deductUserIdWarp {
            width: 180px;
            height: 27px;
            position: relative;
            float: left;
            border: 1px solid #ccc;
        }
        .trapDown {

        }
        .innerWarp {
            line-height: 26px;
        }
        .innerWarp > span {
            margin: 0 2px 0 6px;
            width: 146px;
        }
        .showSearchBtn {
            height: 22px;
            width: 22px;
            display: inline-block;
            cursor: pointer;
            background-image: url(<%=basePath%>/back/image/u911_selected.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            padding: 10px;
            position: absolute;
            right: 0;
            top: 2px;
        }
        .firstItem {

        }
        .firstItem > input {
            border: none 0!important;
            padding: 0 4px;
            box-sizing: border-box;
        }
        .firstItem > i {
            height: 22px;
            width: 22px;
            display: inline-block;
            cursor: pointer;
            background-image: url(<%=basePath%>/back/image/nav-search-checked.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            padding: 10px;
            position: absolute;
            right: 0;
            top: 2px;
        }
        .searchItem {
            position: absolute;
            left: -1px;
            top: 26px;
            width: 180px;
            min-width: 26px;
            border: 1px solid #ccc;
            border-top: none 0;
            background: #fff;
            border-radius: 0 0 4px 4px;
        }
        .searchItem > li {
            margin-top: 0!important;
            padding: 0!important;
            line-height: 26px;
            border-bottom: 1px solid #ccc;
            color: #428bca;
            cursor: pointer;
        }
        .lastItem {
            border-bottom: none 0!important;
            text-align: center;
        }
        .text-red{
            color: red ;
        }
    </style>
</head>
<body>
<c:if test="${error!=null && error!=''}">
<script>
alert("${error}");
</script>
</c:if>

<div class="contentBox">
  <h1 class="titUrl">
      <span>当前所在位置：</span><span>用户反馈汇总管理</span><span>&nbsp;&gt;&nbsp;</span><span>违约/异常待处理-定金 </span><span>&nbsp;&gt;&nbsp;</span><span class="currentPage">编辑</span>
  </h1>
  <div class="main-content">
   <form  method="post" id="formId" >
    <input type="hidden" value="${obj.id}" name="id" id="id"/>
    <input type="hidden" value="${obj.id}" name="ex_id" id="ex_id"/>
    <input type="hidden" value="${obj.userId}" name="user_id" id="user_id"/>
    <input type="hidden" value="${obj.payUserId}" name="pay_user_id" id="pay_user_id"/>
    <input type="hidden" value="${obj.tsOrderNo}" name="ts_order_no" id="ts_order_no"/>
    <input type="hidden" value="${obj.orderId}" name="order_id" id="order_id"/>
    <input type="hidden" value="${obj.payAmount*0.01}" name="payAmount" id="payAmount"/>
    <input type="hidden" value="${obj.totalOrderAmount*0.01}" name="totalOrderAmount" id="totalOrderAmount"/>
    <input type="hidden" value="${obj.couponAmount*0.01}" name="couponAmount" id="couponAmount"/>
    <input type="hidden" value="${user.userName}" name="action_name" id="action_name"/>
    <input type="hidden" value="${user.cellPhone}" name="action_cell_phone" id="action_cell_phone"/>
    <input type="hidden" value="${user.id}" name="action_user_id" id="action_user_id"/>
    <input type="hidden" value="${obj.isComplaint}" name="isComplaint" id="isComplaint"/>
    <input type="hidden" value="${obj.excellentGoods}" name="excellentGoods" id="excellentGoods"/>
    <input type="hidden" value="${availableBalance}" name="available_balance" id="available_balance"/>
    <input type="hidden" value="${obj.technicalServiceFee*0.01}" name="technicalServiceFee" id="technicalServiceFee"/>
    <input type="hidden" value="${obj.thirdPartyPlatformType}" name="thirdPartyPlatformType" id="thirdPartyPlatformType"/>
    <input type="hidden" value="${obj.carBlackListOrdersList}" name="thirdPartyPlatformType" id="carBlackListOrdersList"/>
    <input type="hidden" value="${obj.carBlackForever}" name="thirdPartyPlatformType" id="carBlackForever"/>
    <input type="hidden" value="${obj.goodsBlackForever}" name="thirdPartyPlatformType" id="goodsBlackForever"/>
    <input type="hidden" value="${obj.carLimitForever}" name="thirdPartyPlatformType" id="carLimitForever"/>
    <input type="hidden" value="${obj.goodsLimitForever}" name="thirdPartyPlatformType" id="goodsLimitForever"/>
    <input type="hidden" value="${techRefundAudit.finalAuditStatus}" name="techRefundFinalAuditStatus" id="techRefundFinalAuditStatus"/>
    <input type="hidden" value="${isEditTechRefund}" name="isEditTechRefund" id="isEditTechRefund"/>

    <div class="systemParameter">
      <ul class="cf">

          <c:if test="${obj.invoiceTransport == '1' || obj.invoiceTransport == 1}">
              <li class="cf">
                  <label> </label>
                  <span style="font-size:20px;color: #ee0a24" class="lineHeight25">开专票</span>
                  <c:if test="${obj.isAssignOrder == '1' || obj.isAssignOrder == 1}">
                      <span style="font-size:20px;color: #ee0a24" class="lineHeight25">货主指派单(订金为0元)</span>
                  </c:if>
              </li>
          </c:if>
          <c:if test="${obj.thirdPartyPlatformType=='1'|| obj.thirdPartyPlatformType==1 }">
                  <li class="cf" name="thirdparty_platform_type" id="thirdparty_platform_type" value="${obj.thirdPartyPlatformType}">
                      <label>货源成交渠道：</label>
                      <span class="lineHeight25">运满满</span>
                 </li>
                 <li class="cf">
                    <label>运满满订单编号：</label>
                    <span class="lineHeight25">${obj.thirdPartyPlatformOrderNo}</span>
                </li>
          </c:if>
          <li class="cf">
              <label><c:if test="${obj.guaranteeGoods == 1}"><span style="
				border: 1px solid orange;
				padding: 3px;
				color: orange;
				margin-right: 5px;
			">保障</span></c:if>货源ID：</label>
              <span class="lineHeight25">${obj.tsId}</span>&nbsp;&nbsp;
              <a style="cursor:pointer" onclick="openWinAuto('back/model/html/transport/info_huowuxiangqing.html?goodsId=${obj.tsId}','infoDetailWin',1100,600)">查看</a>
          </li>
          <li class="cf">
              <label>运单号：</label>
              <span class="lineHeight25">${obj.tsOrderNo}</span>
          </li>
          <li class="cf">
              <label>货源类型：</label>
              <c:choose>
                  <c:when test="${obj.excellentGoods==0}">普通货源</c:when>
                  <c:when test="${obj.excellentGoods==1 && obj.excellentGoods == 1 && obj.excellentCar2 == 1}">优车2.0</c:when>
                  <c:when test="${obj.excellentGoods==1 && obj.excellentGoods == 1 && obj.excellentCar2 != 1}">优车1.0</c:when>
                  <c:when test="${obj.excellentGoods==2}">专车货源</c:when>
                  <c:otherwise>--</c:otherwise>
              </c:choose>
          </li>
          <li class="cf">
              <label>是否抽佣：</label>
              <c:choose>
                  <c:when test="${obj.commissionTransport == 1}">是</c:when>
                  <c:when test="${obj.commissionTransport == 0}">否</c:when>
              </c:choose>
          </li>
          <li class="cf">
              <label>用车类型：</label>
              <span class="lineHeight25">${useCarTypeStr}</span>&nbsp;&nbsp;
          </li>
          <li class="cf">
             <label>发货渠道：</label>
             <c:choose>
               <c:when test="${obj.sourceType==1}">用户发布</c:when>
               <c:when test="${obj.sourceType==2}">调度代发</c:when>
               <c:when test="${obj.sourceType==3}">个人货主</c:when>
               <c:when test="${obj.sourceType==4}">运满满货源</c:when>
               <c:otherwise>--</c:otherwise>
             </c:choose>
          </li>
          <li class="cf">
            <label>找车方式：</label>
            <c:choose>
                <c:when test="${obj.publishType==1}">电议</c:when>
                <c:when test="${obj.publishType==2}">一口价</c:when>
                <c:otherwise>--</c:otherwise>
              </c:choose>
          </li>
          <li class="cf">
            <label>运费金额：</label>
            <span class="lineHeight25">
                <fmt:formatNumber type="number" value="${obj.carriageFee}" pattern="0.00" maxFractionDigits="2"/>元
            </span>
          </li>

          <li class="cf">
              <label>订单编号：</label>
              <span class="lineHeight25">${obj.orderId}</span>
              <a style="cursor:pointer" onclick="openWinAuto('back/model/html/transport/info_orders.html?id=${obj.orderId}','infoDetailWin',1100,600)">查看</a>
          </li>
          <li class="cf">
              <label>是否超额保障：</label>
              <c:choose>
                  <c:when test="${obj.excessCoverage == 1}">是</c:when>
                  <c:when test="${obj.excessCoverage == 0}">否</c:when>
              </c:choose>
              <span class="lineHeight25" style="color: #ee0a24">  ${obj.userIdentity}</span>
          </li>
          <li class="cf">
              <div>
                  <label>车主账号：</label>
                  <span class="lineHeight25">${obj.payCellPhone}</span>
                  <span class="lineHeight25" style="color: #ee0a24">下单等级：V${obj.carryPointLevel}</span>
              </div>

          </li>
          <li class="cf">
            <label>发货方账号：</label>
            <span class="lineHeight25">${obj.uploadCellphone}</span>
          </li>
          <li class="cf">
              <label>是否直客：</label>
              <c:choose>
                  <c:when test="${obj.directCustomer == 1}">是</c:when>
                  <c:when test="${obj.directCustomer == 0}">否</c:when>
              </c:choose>
          </li>
          <li class="cf">
              <label>订金金额：</label>
              <span class="lineHeight25" id="infoMoney" data-pay="<fmt:formatNumber type="number" value="${obj.totalOrderAmount*0.01}"  />">
              <fmt:formatNumber type="number" value="${obj.totalOrderAmount*0.01}" pattern="0.00" maxFractionDigits="2"/>元
              <c:if test="${obj.refundFlag == 1}">(退还)</c:if>
              <c:if test="${obj.refundFlag == 0}">(不退还)</c:if>
              （减免<fmt:formatNumber type="number" value="${obj.couponAmount*0.01}" pattern="0.00" maxFractionDigits="2"/>元，
                实付<fmt:formatNumber type="number" value="${obj.payAmount*0.01}" pattern="0.00" maxFractionDigits="2"/>元）
             <span></span> <!--减免金额和实付金额 -->
          </span>
          </li>
          <li class="cf">
              <label>订金状态：</label>
              <span class="lineHeight25">
                <c:choose>
                    <c:when test="${obj.costStatus == 15}">
                        已支付
                    </c:when>
                    <c:when test="${obj.costStatus == 21}">
                        拒绝退款
                    </c:when>
                    <c:when test="${obj.costStatus == 25}">
                        异常上报
                    </c:when>
                    <c:when test="${obj.costStatus == 30}">
                        退款中
                    </c:when>
                    <c:when test="${obj.costStatus == 35}">
                        已退款
                    </c:when>
                    <c:when test="${obj.costStatus == 40}">
                        已打款
                    </c:when>
                    <c:when test="${obj.costStatus == 45}">
                        自动收款
                    </c:when>
                    <c:when test="${obj.costStatus == 50}">
                        异常完成
                    </c:when>
                    <c:when test="${obj.costStatus == 55}">
                        延迟付款
                    </c:when>
                    <c:when test="${obj.costStatus == 211}">
                        拒绝退款延迟付款
                    </c:when>
                    <c:otherwise>
                    </c:otherwise>
                </c:choose>
              </span>
          </li>
          <li class="cf" id="publishUserName_li">
              <label>代调人员：</label>
              <span class="lineHeight25">${obj.publishUserName}</span>
              </span>
          </li>
          <li class="cf" id="technicalServiceFee_li">
              <label>技术服务费：</label>
              <span class="lineHeight25">${obj.technicalServiceFee*0.01}元（减免0.00元，实付${obj.technicalServiceFee*0.01}元）</span>
              </span>
          </li>

          <li class="cf">
              <label>上报人身份：</label>
              <span class="lineHeight25">
            <c:choose>
                <c:when test="${obj.exParty=='1' || obj.exParty==1}">车主</c:when>
                <c:otherwise>货主</c:otherwise>
            </c:choose>
          </span>
          </li>
<%--          <li class="cf">--%>
<%--              <label>装车状态：</label>--%>
<%--                <c:choose>--%>
<%--                    <c:when test="${obj.loadingStatus==1}">已装车</c:when>--%>
<%--                    <c:when test="${obj.loadingStatus==0}">未装车</c:when>--%>
<%--                    <c:otherwise>--</c:otherwise>--%>
<%--                </c:choose>--%>
<%--                <c:choose>--%>
<%--                    <c:when test="${obj.loadingStatus==1 && obj.loadingChildStatus==1}">-运输中</c:when>--%>
<%--                    <c:when test="${obj.loadingStatus==1 && obj.loadingChildStatus==2}">-已送达未卸货</c:when>--%>
<%--                    <c:when test="${obj.loadingStatus==1 && obj.loadingChildStatus==3}">-已送达已卸货</c:when>--%>
<%--                    <c:when test="${obj.loadingStatus==0 && obj.loadingChildStatus==1}">-车方未出发</c:when>--%>
<%--                    <c:when test="${obj.loadingStatus==0 && obj.loadingChildStatus==2}">-出发未到装货地</c:when>--%>
<%--                    <c:when test="${obj.loadingStatus==0 && obj.loadingChildStatus==3}">-车方已到装货地</c:when>--%>
<%--                    <c:otherwise></c:otherwise>--%>
<%--                </c:choose>--%>
<%--          </li>--%>
          <li class="cf">
              <label>上报时间：</label>
              <span class="lineHeight25"><fmt:formatDate value="${obj.exTime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
          </li>
        <li class="cf">
          <label>上报类型：</label>
          <span class="lineHeight25">
		  <c:choose>
      			<c:when test="${obj.exParty=='1' || item.exParty==1}">
					<c:forEach items="${ex_party_car}" var="item">
		      		<c:if test="${obj.exType==item.value}">
						${item.name}
					</c:if>
		            </c:forEach>
				</c:when>
	       		<c:otherwise>
	       			<c:forEach items="${ex_party_goods}" var="item">
		      		<c:if test="${obj.exType==item.value}">
						${item.name}
					</c:if>
		            </c:forEach>
	       		</c:otherwise>
			</c:choose>
          </span>
        </li>
          <c:forEach items="${vouchers}" var="item">
              <c:if test="${item.exParty=='1'}">
                  <c:if test="${item.voucherType==1}">
                      <li class="cf">
                          <label>车方上报原因：</label>
                          <span class="lineHeight25">${item.proofReason}</span>
                      </li>
                      <li class="cf">
                          <label>车方上报凭证：</label>
                          <div class="photoWareBox cf imgListBox" data-tit="2">
                              <div class="exceptionImgbox fl supplementImg img-${item.id}">
                                  <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                                  </c:forEach>
                              </div>
                          </div>
                      </li>
                  </c:if>
                  <c:if test="${item.voucherType==2}">
                      <li class="cf">
                          <label>车方补充时间：</label>
                          <span class="lineHeight25"><fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
                      </li>
                      <li class="cf">
                          <label>车方补充原因：</label>
                          <span class="lineHeight25">${item.proofReason}</span>
                      </li>
                      <li class="cf">
                          <label>车方补充凭证：</label>
                          <div class="photoWareBox cf imgListBox" data-tit="2">
                              <div class="exceptionImgbox fl supplementImg img-${item.id}">
                                  <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                                  </c:forEach>
                              </div>
                          </div>
                      </li>
                  </c:if>
              </c:if>
              <c:if test="${item.exParty=='2'}">
                  <c:if test="${item.voucherType==1}">
                      <li class="cf">
                          <label>货方上报原因：</label>
                          <span class="lineHeight25">${item.proofReason}</span>
                      </li>
                      <li class="cf">
                          <label>货方上报凭证：</label>
                          <div class="photoWareBox cf imgListBox" data-tit="2">
                              <div class="exceptionImgbox fl supplementImg img-${item.id}">
                                  <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                                  </c:forEach>
                              </div>
                          </div>
                      </li>
                  </c:if>
                  <c:if test="${item.voucherType==2}">
                      <li class="cf">
                          <label>货方补充时间：</label>
                          <span class="lineHeight25"><fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
                      </li>
                      <li class="cf">
                          <label>货方补充原因：</label>
                          <span class="lineHeight25">${item.proofReason}</span>
                      </li>
                      <li class="cf">
                          <label>货方补充凭证：</label>
                          <div class="photoWareBox cf imgListBox" data-tit="2">
                              <div class="exceptionImgbox fl supplementImg img-${item.id}">
                                  <c:forEach items="${item.pictureVouchers}" var="imgUrl">
                                      <img src="${imgUrl}" alt="" onclick="carO.lookBigImg('img-${item.id}')">
                                  </c:forEach>
                              </div>
                          </div>
                      </li>
                  </c:if>
              </c:if>
          </c:forEach>
        <li class="cf">
          <label>操作人：</label>
          <span class="lineHeight25">${obj.actionName}</span>
        </li>


      <li class="cf">
          <label>上报内容：</label>
          <textarea class="note" name="ex_other" cols="" rows="" readonly="readonly">${obj.exOther}</textarea>
        </li>
      <li class="cf">
          <label>处理记录：</label>
            <div class="fl">
             <c:forEach items="${records}" var="item">
              <p class="dataContP">
              <span class="dataCont">
              <fmt:formatDate value="${item.ctime}" pattern="yyyy-MM-dd HH:mm:ss" />
              </span>
              <span class="textContent">${item.opinion}</span>
              </p>
          </c:forEach>
          </div>

        </li>
      <li class="cf">
          <label><i class="xing">*</i>本次处理意见：</label>
          <textarea  class="note" name="opinion" id="opinion" cols="" rows="" maxlength="500" placeholder="最多输入500个字"></textarea>
        <p id="checklen" class="error" style="margin-left: 130px;"></p>
        </li>
          <li class="cf">
              <label><i class="xing">*</i>纠纷类型：</label>
              <select name="dispute_type" id="dispute_type">
                  <option value=''>请选择</option>
                  <c:forEach items="${dispute_type}" var="item">
                      <option value=${item.value} key= ${item.id} <c:if test="${obj.disputeType==item.value }">selected</c:if> >${item.name}</option>
                  </c:forEach>
              </select>
          </li>
          <li class="empty cf" id="">
              <label><i class="xing">*</i>放空公里数：</label>
              <input type="text" id="emptying_km" name="emptying_km" value="${obj.emptyingKm}" onblur="value=keepTwoDecimal(value,true)" onkeyup="value=keepTwoDecimal(value)">
              <span>公里</span>
              <span class="emptyAmountTip" style="display: none; color:red;margin-left: 10px;"></span>
          </li>
          <li class="empty cf">
              <label><i class="xing">*</i>放空车型：</label>
              <select name="emptying_cartype" id="emptying_cartype">
                  <option value=''>请选择</option>
                  <c:forEach items="${emptying_cartype}" var="item">
                      <option value=${item.value} key= ${item.id} <c:if test="${obj.emptyingCartype==item.value }">selected</c:if> >${item.name}</option>
                  </c:forEach>
              </select>
              <input type="text" onblur="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" onkeyup="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')"  id="emptying_cartype_detail" maxlength="20" name="emptying_cartype_detail" value="${obj.emptyingCartypeDetail}">
          </li>
          <li class="empty cf">
              <label><i class="xing">*</i>放空原因：</label>
              <select name="emptying_reason" id="emptying_reason">
                  <option value=''>请选择</option>
                  <c:forEach items="${emptying_reason}" var="item">
                      <option value=${item.value} key= ${item.id} <c:if test="${obj.emptyingReason==item.value }">selected</c:if> >${item.name}</option>
                  </c:forEach>
              </select>
              <input type="text" onblur="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" onkeyup="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" id="emptying_reason_detail" maxlength="20" name="emptying_reason_detail" value="${obj.emptyingReasonDetail}">
          </li>
        <li class="finalOpinion cf" id="is_apply_compensate">
            <label><i class="xing">*</i>是否申请补偿：</label>
            <span><input type="radio" id="isAppY" name="is_apply_compensate" value="1" <c:if test="${exAudit.isApplyCompensate==1}">checked="checked"</c:if>">是</span>
            <span><input type="radio" id="isAppN" name="is_apply_compensate" value="2" class="checked" <c:if test="${exAudit.isApplyCompensate!=1}">checked="checked"</c:if>">否</span>
        </li>

      <div class="finalOpinion cf" id="compensateContent" style="display: none">
        <li class="finalOpinion cf" id="">
            <label><i class="xing">*</i>补偿对象：</label>
            <select name="compensate_target" id="compensate_target" onchange="compO.changeTarget($(this))">
                <option value="">请选择</option>
                <option value="1" <c:if test="${exAudit.compensateTarget=='1'}">selected</c:if> >车方</option>
                <option value="2" <c:if test="${exAudit.compensateTarget=='2'}">selected</c:if> >货方</option>
            </select>
        </li>
        <li class="finalOpinion cf" id="compensateCellphone" style="display: none;">
            <label>车主保障金账号：</label>
            <div class="deductUserIdWarp">
                <i class="trapDown"></i>
                <div class="innerWarp">
                    <i class="showSearchBtn" onclick="compO.showSearch(event)"></i>
                    <span id="deductUserId">请选择</span>
                    <input id="deductUserIdInput" name="deductUserId" type="hidden" value="" />
                </div>
                <ul class="searchItem" style="display: none;">
                    <li class="firstItem">
                        <i class="searchBtn" onclick="compO.searchPhone(event, $(this))"></i>
                        <input type="text" maxlength="11" value="" onclick="event.stopPropagation();">
                    </li>
                    <li class="lastItem" onclick="compO.searchChoose(event)">请选择</li>
                </ul>
            </div>
            <span>余额：</span><em>¥0</em>
        </li>
        <li class="finalOpinion cf" id="">
            <label><i class="xing">*</i>补偿类型：</label>
            <select name="compensate_type" id="compensate_type">
                <option value="">请选择</option>
                <option value="1" <c:if test="${exAudit.compensateType=='1'}">selected</c:if> >订金补偿</option>
                <option value="2" <c:if test="${exAudit.compensateType=='2'}">selected</c:if> >放空补偿</option>
                <option value="3" <c:if test="${exAudit.compensateType=='3'}">selected</c:if> >压车补偿</option>
                <option value="4" <c:if test="${exAudit.compensateType=='4'}">selected</c:if> >距离加价补偿</option>
                <option value="5" <c:if test="${exAudit.compensateType=='5'}">selected</c:if> >吨位加价补偿</option>
                <option value="6" <c:if test="${exAudit.compensateType=='6'}">selected</c:if> >拉跑货补偿</option>
                <option value="7" <c:if test="${exAudit.compensateType=='7'}">selected</c:if> >倒卖货补偿</option>
                <option value="8" <c:if test="${exAudit.compensateType=='8'}">selected</c:if> >倒送货补偿</option>
                <option value="9" <c:if test="${exAudit.compensateType=='9'}">selected</c:if> >防溢出补偿</option>
                <option value="10" <c:if test="${exAudit.compensateType=='10'}">selected</c:if> >其他</option>
            </select>
        </li>
        <li class="finalOpinion cf" id="">
            <label><i class="xing">*</i>补偿方式：</label>
            <select name="compensate_method" id="compensate_method">
                <option value="">请选择</option>
                <option value="1" <c:if test="${exAudit.compensateMethod=='1'}">selected</c:if> >订金减免券</option>
                <option value="2" <c:if test="${exAudit.compensateMethod=='2'}">selected</c:if> >现金红包</option>
            </select>
        </li>
        <li class="cf" id="">
            <label><i class="xing">*</i>补偿原因：</label>
            <input type="text" onblur="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" onkeyup="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" id="compensate_reason" maxlength="20" name="compensate_reason" value="${exAudit.compensateReason}">
        </li>
        <li class="finalOpinion cf" id="">
            <label><i class="xing">*</i>补偿金额：</label>
            <input type="text" id="compensate_amount" name="compensate_amount" value="${exAudit.compensateAmount}" onblur="amountBlur()" onkeyup="compO.amountOpt(event)" maxlength="6"> </input>
            <span>元</span>
            <span class="amountTip" style="display: none; color:red;margin-left: 10px;"></span>
        </li>
          <li class="finalOpinion cf" id="">
              <label><i class="xing">*</i>保障类型：</label>
              <select name="safeguard_type" id="safeguard_type">
                  <option value="">请选择</option>
                  <option value="1" <c:if test="${exAudit.safeguardType=='1'}">selected</c:if> >司机等级保障</option>
                  <option value="2" <c:if test="${exAudit.safeguardType=='2'}">selected</c:if> >优车保障</option>
                  <option value="3" <c:if test="${exAudit.safeguardType=='3'}">selected</c:if> >专车保障</option>
                  <option value="4" <c:if test="${exAudit.safeguardType=='4'}">selected</c:if> >直客保障</option>
                  <option value="5" <c:if test="${exAudit.safeguardType=='5'}">selected</c:if> >代调保障</option>
                  <option value="6" <c:if test="${exAudit.safeguardType=='6'}">selected</c:if> >专属客服保障</option>
              </select>
              <span class="safeguardTip" style="display: none; color:red;margin-left: 10px;"></span>
          </li>
          <li class="finalOpinion cf" id="">
              <label style ="width:210px; display: inline-block;"><i class="xing">*</i>货方线下已赔付金额：</label>
              <input type="number" id="goods_amount_offline" name="goods_amount_offline" value="${exAudit.goodsAmountOffline}" maxlength="6"> </input>
              <span>元</span>
              <span class="goodsAmountTip" style="display: none; color:red;margin-left: 10px;"></span>
          </li>
        <li class="finalOpinion cf" id="">
            <label>补偿审核状态：</label>
            <c:if test="${exAudit.auditStatus==0}"><span class="lineHeight25 auditStatus">审核中</span></c:if>
            <c:if test="${exAudit.auditStatus==1}"><span class="lineHeight25 auditStatus">审核通过</span></c:if>
            <c:if test="${exAudit.auditStatus==2}"><span class="lineHeight25 auditStatus">审核不通过</span></c:if>
        </li>
        <li class="finalOpinion cf" id="">
            <label>审核人：</label>
            <span class="lineHeight25">${exAudit.userName}</span>
        </li>
      </div>

      <!-- 是否申请扣除货方保证金（优货）-->
      <li class="finalOpinion cf" id="is_apply_compensate">
          <label style ="width:210px; display: inline-block;" ><i class="xing">*</i>是否申请扣除货方保证金(优货)：</label>
          <span><input type="radio" id="isApplyDepositY" name="is_apply_deposit" value="1" <c:if test="${depositHandleAudit!=null && (depositHandleAudit.finalAuditStatus==0 || depositHandleAudit.finalAuditStatus==1)}">checked="checked"</c:if>">是</span>
          <span><input type="radio" id="isApplyDepositN" name="is_apply_deposit" value="2" class="checked" <c:if test="${depositHandleAudit==null || depositHandleAudit.finalAuditStatus==2}">checked="checked"</c:if>">否</span>
      </li>
      <div class="finalOpinion cf" id="depositContent" style="display: none;">
          <li class="cf" id="">
              <label><i class="xing">*</i>扣款原因：</label>
              <input type="text" onblur="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" onkeyup="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" id="refund_reason" maxlength="20" name="refund_reason" value="${depositHandleAudit.refundReason}">
          </li>
          <li class="finalOpinion cf" id="">
              <label><i class="xing">*</i>扣款金额：</label>
              <input type="text" id="refund_amount" name="refund_amount" value="${depositHandleAudit.refundAmount}" onblur="refundAmountBlur()" onkeyup="" maxlength="6"> </input>
              <span>元</span>
              <span class="refundAmountTip" style="display: none; color:red;margin-left: 10px;"></span>
          </li>
          <li class="finalOpinion cf" id="">
              <label>扣除审核状态：</label>
              <c:if test="${depositHandleAudit.finalAuditStatus==0}"><span class="lineHeight25 finalAuditStatus">审核中</span></c:if>
              <c:if test="${depositHandleAudit.finalAuditStatus==1}"><span class="lineHeight25 finalAuditStatus">审核通过</span></c:if>
              <c:if test="${depositHandleAudit.finalAuditStatus==2}"><span class="lineHeight25 finalAuditStatus">审核不通过</span></c:if>
          </li>
          <li class="finalOpinion cf" id="">
              <label>审核人：</label>
              <span class="lineHeight25">${depositHandleAudit.auditUserName}</span>
          </li>
      </div>
     <li class="finalOpinion cf" id="is_apply_refund_tech_fee">
            <label style ="width:186px; display: inline-block;"><i class="xing">*</i>是否完单后退技术服务费：</label>
            <span><input type="radio" id="isApplyTechRefundY" name="is_refund_tech_fee" value="1" <c:if test="${techRefundAudit != null && techRefundAudit.finalAuditStatus != 5}">checked="checked"</c:if>">是</span>
            <span><input type="radio" id="isApplyTechRefundN" name="is_refund_tech_fee" value="2" class="checked" <c:if test="${techRefundAudit == null || techRefundAudit.finalAuditStatus == 5}">checked="checked"</c:if>">否</span>
     </li>
     <li class="cf" style="display: none" id="refundServiceContent">
          <label class="fl"><i class="xing">*</i>技术服务费退还：</label>
          <div class="suggestTion">
              <span class="labelrad ml15">我司：</span><input type="number" placeholder="" name="refund_platform_service_amount" id="refund_platform_service_amount" value="${refundPlatformServiceAmount}">元
              <span class="labelrad">车主：</span><input type="number" placeholder="" name="refund_car_service_amount" id="refund_car_service_amount" readonly="readonly" value="${refundCarServiceAmount}">元
              <span class="error" id="refundServiceAmountError"></span>
          </div>
      </li>
      <li class="finalOpinion cf" id="is_last">
          <label><i class="xing">*</i>是否为最终意见：</label>
          <span><input id="ra1" name="is_last" value="1" type="radio">是</span>
          <span><input class="checked" id="ra2" name="is_last" value="0" type="radio" checked>否</span>
        </li>

          <li id="order_status" class="finalOpinion cf" style="display: none">
              <label><i class="xing">*</i>订单状态：</label>

              <span><input name="orderNewStatusTop" value="0" type="radio">订单取消</span>
              <span style="margin: 0 8px 0 36px;"><input name="orderNewStatusTop" value="1" type="radio" checked>订单继续</span>

              <span>当前订单状态： </span>
              <select name="orderNewStatus" id="orderNewStatus">
<%--                  <option value="5" key= "待接单" <c:if test="${obj.orderNewStatus=='5' }">selected</c:if> >待接单</option>--%>
                    <c:if test="${obj.invoiceTransport == '1' || obj.invoiceTransport == 1}">
                  <option value="10" key= "待签署" <c:if test="${obj.orderNewStatus=='10' }">selected</c:if> >待签署</option>
                    </c:if>
                  <option value="15" key= "待装货" <c:if test="${obj.orderNewStatus=='15' }">selected</c:if> >待装货</option>
                  <option value="20" key= "待收货/待卸货" <c:if test="${obj.orderNewStatus=='20' }">selected</c:if> >待收货/待卸货</option>
                    <c:if test="${obj.invoiceTransport == '1' || obj.invoiceTransport == 1}">
                        <option value="25" key= "待收运费/待付运费" <c:if test="${obj.orderNewStatus=='25' }">selected</c:if> >待收运费/待付运费</option>
                    </c:if>
                  <option value="30" key= "已完成" <c:if test="${obj.orderNewStatus=='30' }">selected</c:if> >已完成</option>
<%--                  <option value="35" key= "已取消" <c:if test="${obj.orderNewStatus=='35' }">selected</c:if> >已取消</option>--%>
              </select>
          </li>

          <c:if test="${paidAmount > 0}">
              <li class="finalOpinion cf">
                  <label> </label>
                  <span style="color: red;">货主已预付运费${paidAmount}元，订单取消请谨慎操作。</span>
              </li>
          </c:if>

          <li class="finalOpinion cf" id="loadingStatus">
              <label><i class="xing">*</i>核实装车状态：</label>
              <span><input id="loadingStatus1" name="loadingStatus" value="1" type="radio" checked>已装车</span>
              <span><input id="loadingStatus2" name="loadingStatus" value="0" type="radio" >未装车</span>
              <span><input id="loadingStatus3" name="loadingStatus" value="2" type="radio" >已卸车</span>
              <span class="error" id="loadingStatusError"></span>
          </li>
          <li class="finalOpinion cf" id="loadYesChildStatus">
              <label> </label>
              <c:forEach items="${load_y_child_type}" var="item">
                  <input type="checkbox" name="loadYesChildStatus" value=${item.value}> ${item.name} &nbsp;
              </c:forEach>
          </li>
          <li class="finalOpinion cf" id="loadNoChildStatus" hidden>
              <label> </label>
              <c:forEach items="${load_n_child_type}" var="item">
                  <input type="checkbox" name="loadNoChildStatus" value=${item.value}> ${item.name} &nbsp;
              </c:forEach>
          </li>
          <li class="finalOpinion cf" id="unloadStatus" hidden>
              <label> </label>
              <c:forEach items="${unload_type}" var="item">
                  <input type="checkbox" name="unloadStatus" value=${item.value}> ${item.name} &nbsp;
              </c:forEach>
          </li>
        <li class="suggestcheck cf" style="display: none">
          <label class="fl"><i class="xing">*</i>订金建议分配：</label>
            <c:if test="${obj.isAssignOrder != 1}">
                <div class="suggestTion">
                    <span class="labelrad ml15">发货方：</span><input type="number" placeholder="" name="goods_amount" id="goods_amount">元
                    <span class="labelrad">车主：</span><input type="number" placeholder="" name="car_amount" id="car_amount" readonly="readonly">元
                    <span class="error" id="amountError"><!-- 输入金额大于所交订金 --></span>
                </div>
            </c:if>
            <c:if test="${obj.isAssignOrder == 1}">
                <div class="suggestTion">
                    <span class="labelrad ml15">发货方：</span><input type="number" placeholder="" name="goods_amount"
                                                                     id="goods_amount" value="0" disabled>元
                    <span class="labelrad">车主：</span><input type="number" placeholder="" name="car_amount"
                                                              id="car_amount" readonly="readonly" value="0" disabled>元
                    <span class="error" id="amountError"><!-- 输入金额大于所交订金 --></span>
                </div>
            </c:if>

        </li>
          <li class="cf" style="display: none" id="service_amount_div">
              <label class="fl"><i class="xing">*</i>技术服务费退还：</label>
              <div class="suggestTion">
                  <span class="labelrad ml15">我司：</span><input type="number" placeholder="" name="platform_service_amount" id="platform_service_amount">元
                  <span class="labelrad">车主：</span><input type="number" placeholder="" name="car_service_amount" id="car_service_amount" readonly="readonly">元
                  <span class="error" id="serviceAmountError"></span>
              </div>
          </li>
          <li class="cf">
              <label><i class="xing">*</i>责任方：</label>
              <select name="ex_fault_party" id="ex_fault_party">
                  <option value=''>请选择</option>
                <c:forEach items="${ex_fault_party}" var="item">
                    <option value=${item.value} key= ${item.id} <c:if test="${obj.exFaultParty==item.value }">selected</c:if>>${item.name}</option>
                </c:forEach>
              </select>
              <span class="error" id="exFaultPartyError"></span>
          </li>
          <li class="cf" id="responsible_party_two_box" style="display: none">
              <label><i class="xing">*</i>货方责任人：</label>
              <select name="responsible_party_two" id="responsible_party_two">
                  <option value=''>请选择</option>
                  <option value="1" selected="${obj.responsiblePartyTwo ==1}">货站</option>
                  <option value="2" selected="${obj.responsiblePartyTwo ==2}">货主</option>
              </select>
              <span class="error" id="responsiblePartyTwoError"></span>
          </li>

          <li class="cf">
              <label><i class="xing">*</i>修改上报类型：</label>
              <select name="ex_type_other" id="ex_type_other">
                  <option value=''>请选择</option>
                  <c:choose>
                      <c:when test="${obj.exFaultParty=='1' || item.exFaultParty==1}">
                          <c:forEach items="${goods_ex_fault_party}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='2' || item.exFaultParty==2}">
                          <c:forEach items="${car_ex_fault_party}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='3' || item.exFaultParty==3}">
                          <c:forEach items="${unclear_ex_fault_party}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='4' || item.exFaultParty==4}">
                          <c:forEach items="${both_no_ex_fault_party}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='6' || item.exFaultParty==6}">
                          <c:forEach items="${goods_car_ex_fault_party_new}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='7' || item.exFaultParty==7}">
                          <c:forEach items="${goods_ex_fault_party_new}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='8' || item.exFaultParty==8}">
                          <c:forEach items="${car_unclear_ex_fault_party_new}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='9' || item.exFaultParty==9}">
                          <c:forEach items="${car_ex_fault_party_new}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:when test="${obj.exFaultParty=='10' || item.exFaultParty==10}">
                          <c:forEach items="${goods_unclear_ex_fault_party_new}" var="item">
                              <option value=${item.value} <c:if test="${obj.exTypeOther==item.value }">selected</c:if>>${item.name}</option>
                          </c:forEach>
                      </c:when>
                      <c:otherwise>
                          暂无
                      </c:otherwise>
                  </c:choose>
              </select>
              <input type="text" onblur="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" onkeyup="value=value.replace(/[^(\u4E00-\u9FA5)|^(。？！，、；：“”‘’（）《》〈〉【】『』「」﹃﹄〔〕…—～﹏￥)]/g,'')" maxlength="20" id="ex_type_other_detail" name="ex_type_other_detail" value="${obj.exTypeOtherDetail}">
              <span class="error" id="exTypeOtherError"></span>
          </li>
          <li class="finalOpinion cf" id="isBreak" hidden>
              <label><i class="xing">*</i>是否违约：</label>

              <select name="isBreak">
                <option value="0" selected>否</option>
                <option value="1">违规</option>
                <option value="2">严重违规</option>
            </select>
              <!-- <span><input id="breakSerious" name="isBreak" value="2" type="radio" >严重违约</span>
              <span><input id="breakYes" name="isBreak" value="1" type="radio" >违约</span>
              <span><input class="checked" id="breakNo" name="isBreak" value="0" type="radio" checked>否</span> -->
          </li>
      </ul>
    </div>
   </form>
  </div>
 </div>
<div class="contentBox">
  <div class="main-content">
   <div class="systemParameter">
    <ul class="cf">
        <li class="cf">
            <label><i class="xing">*</i>上传凭证照片：</label>
            <div id="uploadFileEmpty"></div>
            <form id="uploadFileMore" name="uploadFileMore" action="" method="post" enctype="multipart/form-data" target="upIfrMore">
                <div class="photoWareBox cf" id="backListBox" data-tit="2">
                    <div class="BackImgbox fl"  style="">
                        <input type="hidden" name="businessId" value="${obj.id}">
                        <input type="hidden" name="fileType"   value="1">
                        <input type="file"   name="uploadFile" id="fileField" class="fileBtn"
                               accept="image/bmp,image/jpeg,image/png" onchange="ecFn.doUpload($('#uploadFileMore'),1)" >
                        <!-- multiple -->
                        <img src="back/model/images/addPic.png" alt="">
                    </div>
                </div>
            </form>
         </li>
      </ul>
  </div>
 </div>
</div>
<%--处罚模块ui--%>
<div class="punish contentBox" style="display:none" id="punish-box"   >
    <h1>处罚</h1>
<%-- 车方处罚--%>
    <div class="punish-box car-punish-box" style="display: none">
        <div class="punish-title">
            <span>[车方处罚]</span>
            <button class="button check-record"  onclick="blackListOrdersLog()">操作记录</button>
        </div>
        <ui>
            <c:if test="${obj.carBlackStatus==0}">
                <li class="finalOpinion isBackBox cf" >
                    <label><i class="xing">*</i>是否加入黑名单：</label>
                    <span><input  name="isBlackCar" tname="isBlackCar" value="0" type="radio" checked>否</span>
                    <span class="ml-10">
                        <input class="checked carBlackForever" tname="isBlackCar"   name="isBlackCar" value="1" type="radio" >是
                    </span>
                    <span class="ml-10 text-red carBlackForeverStr" style="display: none">该用户已被长期拉黑</span>
                </li>
                <%--    是否加入黑名单 :是 展示        --%>
                <li class="finalOpinion cf blockSetting" style="display: none" >
                    <label><i class="xing">*</i>拉黑期限：</label>
                    <span><input tname="blackTime" name="blackTime" value="0" type="radio">永久拉黑</span>
                    <span class="ml-10">
                    <input class="checked" tname="blackTime" name="blackTime" value="1" type="radio">拉黑
                    <input type="number"  id="carBlackNum" class="punish-input"  max="1000"> 天
                </span>
                </li>
            </c:if>
            <c:if test="${obj.carBlackStatus==1}">
                <c:forEach items="${obj.carBlackListOrdersList}" var="item">
                    <c:if test="${item.type==1}">
                        <%--找货限制字段替换  --%>
                        <li class="finalOpinion cf" id="carPenaltyInformation">
                            <div class="penalty-item">
                                <div class="penalty-title">
                                    <span>拉黑处罚信息 <span class="ml-10 text-red carBlackForeverStr" style="display: none">该用户已被长期拉黑</span> </span>
                                    <input  class="my-button" type="button" data-status="${item.status}"
                                            data-days = '${item.days}' data-type = '${item.type}'
                                            edit-type="carBlackActionType##carBlackNum"
                                            onclick="openDialog('edit', $(this));" value="编辑" />
                                </div>
                                <div class="penalty-cell">
                                    <p>

                                        <label>运单ID：</label>
                                        <span>${item.orderNo}</span>
                                        <label class="ml-20">关联异常上报数据ID：</label>
                                        <span>
                                            <c:forEach items="${item.exIdList}" var="exIdItem">
                                                <c:if test="${exIdItem.exId==obj.id}">
                                                    ${exIdItem.exId}
                                                </c:if>
                                                <c:if test="${exIdItem.exId!=obj.id}">
                                                    <a href="javascript:;" onclick="goDetail(${exIdItem.exId},${exIdItem.exStatus})">${exIdItem.exId}</a>
                                                </c:if>
                                            </c:forEach>
                                        </span>
                                    </p>
                                    <p>
                                        <label>拉黑（总天数）：</label>
                                        <span>${item.restrictNum}天</span>
                                    </p>
                                    <label>处罚时间：</label>
                                    <c:forEach items="${item.restrictTimeList}" var="restrictTimeListItem">
                                        <p class="dataContP">
                                            <span>
                                                <fmt:formatDate value="${restrictTimeListItem.startTime}"
                                                                pattern="yyyy-MM-dd HH:mm:ss"/>-
                                                <fmt:formatDate value="${restrictTimeListItem.endTime}"
                                                                pattern="yyyy-MM-dd HH:mm:ss"/>
                                            </span>
                                        </p>
                                    </c:forEach>
                                    <div id="carBlackActionType" data-isEdit="0" class="font-red"></div>
                                </div>
                            </div>
                        </li>
                    </c:if>

                </c:forEach>
            </c:if>
            <c:if test="${obj.carLimitStatus==0}">
                <li class="finalOpinion cf">
                    <label>找货限制：</label>
                    <select name="" id="carLimit" tname="carLimit" class="carLimitForever" ></select>
                    <span class="ml-10 text-red carLimitForeverStr" style="display: none">该用户已被长期限制找货</span>
                </li>
            </c:if>
            <c:if test="${obj.carLimitStatus==1}">
                <c:forEach items="${obj.carBlackListOrdersList}" var="item">
                    <c:if test="${item.type==3}">
                        <%--找货限制字段替换  --%>
                        <li class="finalOpinion cf" id="carPenaltyInformation">
                            <div class="penalty-item">
                                <div class="penalty-title">
                                    <span>限制找货处罚信息 <span class=" ml-10 text-red carLimitForeverStr" style="display: none">该用户已被长期限制找货</span></span>
                                    <input  class="my-button" type="button" data-status="${item.status}"
                                            data-days = '${item.days}'  data-type = '${item.type}'
                                            edit-type="carRestrictActionType##carRestrictNum"
                                            onclick="openDialog('edit', $(this));" value="编辑" />
                                </div>
                                <div class="penalty-cell">
                                    <p>
                                        <label>运单ID：</label>
                                        <span>${item.orderNo}</span>
                                        <label class="ml-20">关联异常上报数据ID：</label>
                                        <span>
                                            <c:forEach items="${item.exIdList}" var="exIdItem">
                                                <c:if test="${exIdItem.exId==obj.id}">
                                                    ${exIdItem.exId}
                                                </c:if>
                                                <c:if test="${exIdItem.exId!=obj.id}">
                                                    <a href="javascript:;" onclick="goDetail(${exIdItem.exId},${exIdItem.exStatus})">${exIdItem.exId}</a>
                                                </c:if>
                                            </c:forEach>
                                        </span>
                                    </p>
                                    <p>
                                        <label>处罚找货（总天数）：</label>
                                        <span>${item.restrictNum}天</span>
                                    </p>
                                    <label>处罚时间：</label>
                                    <c:forEach items="${item.restrictTimeList}" var="restrictTimeListItem">
                                        <p class="dataContP">
                                            <span><fmt:formatDate value="${restrictTimeListItem.startTime}"
                                                                  pattern="yyyy-MM-dd HH:mm:ss"/>-
                                                <fmt:formatDate value="${restrictTimeListItem.endTime}"
                                                    pattern="yyyy-MM-dd HH:mm:ss"/>
                                            </span>
                                        </p>
                                    </c:forEach>
                                    <div id="carRestrictActionType" data-isEdit="0" class="font-red"></div>
                                </div>
                            </div>
                        </li>
                    </c:if>
                </c:forEach>
            </c:if>

            <li class="finalOpinion blackReason car-rea cf">
                <label>原因：</label>
                <select name="" id="carReason" class="reason ">
                    <option value=""></option>
                </select>
                <select name="" >
                    <option value="">请选择</option>
                </select>
                <select name="" >
                    <option value="">请选择</option>
                </select>
                <select name="">
                    <option value="">请选择</option>
                </select>

            </li>
        </ui>
    </div>
<%--货方处罚--%>
    <div class="punish-box ml-20 good-punish-box" style="display: none">
        <div class="punish-title">
            <span>[货方处罚]</span>
            <button class="button check-record" onclick="blackListOrdersLog()">操作记录</button>
        </div>
        <ui>
            <c:if test="${obj.goodsBlackStatus==0}">
                <li class="finalOpinion isBackBox cf">
                    <label><i class="xing">*</i>是否加入黑名单：</label>
                    <span><input tname="isBlackGood"  name="isBlackGood" value="0" type="radio" checked >否</span>
                    <span class="ml-10">
                        <input class="checked goodsBlackForever " tname="isBlackGood"  name="isBlackGood" value="1" type="radio" >是
                    </span>
                    <span class="ml-10 text-red goodsBlackForeverStr" style="display: none">该用户已被长期拉黑</span>
                </li>
                <%--    是否加入黑名单 :是 展示        --%>
                <li class="finalOpinion cf blockSetting" style="display: none" >
                    <label><i class="xing">*</i>拉黑期限：</label>
                    <span><input tname="blackTimeGood" name="blackTimeGood"  value="0" type="radio">永久拉黑</span>
                    <span class="ml-10">
                    <input class="checked"  tname="blackTimeGood" name="blackTimeGood" value="1" type="radio">拉黑
                    <input  type="number" tname="goodRestrictNum" class="punish-input"  max="1000" id="goodRestrictNum"> 天
                </span>
                </li>
            </c:if>
            <c:if test="${obj.goodsBlackStatus==1}">
                <c:forEach items="${obj.goodsBlackListOrdersList}" var="item">
                    <c:if test="${item.type==1}">
                        <li class="finalOpinion cf" id="carPenaltyInformation">
                            <div class="penalty-item">
                                <div class="penalty-title">
                                    <span>拉黑处罚信息 <span class="ml-10 text-red goodsBlackForeverStr" style="display: none">该用户已被长期拉黑</span></span>
                                    <input  class="my-button" type="button" data-status="${item.status}"
                                            data-days = '${item.days}'  data-type = '${item.type}'
                                            edit-type="goodsBlackActionType##goodsBlackNum"

                                            onclick="openDialog('edit', $(this));" value="编辑" />
                                </div>
                                <div class="penalty-cell">
                                    <p>
                                        <label>运单ID：</label>
                                        <span>${item.orderNo}</span>
                                        <label class="ml-20">关联异常上报数据ID：</label>
                                        <span>
                                            <c:forEach items="${item.exIdList}" var="exIdItem">
                                                <c:if test="${exIdItem.exId==obj.id}">
                                                    ${exIdItem.exId}
                                                </c:if>
                                                <c:if test="${exIdItem.exId!=obj.id}">
                                                    <a href="javascript:;" onclick="goDetail(${exIdItem.exId},${exIdItem.exStatus})">${exIdItem.exId}</a>
                                                </c:if>
                                            </c:forEach>
                                        </span>
                                    </p>
                                    <p>
                                        <label>拉黑（总天数）：</label>
                                        <span>${item.restrictNum}天</span>
                                    </p>
                                    <label>处罚时间：</label>
                                    <c:forEach items="${item.restrictTimeList}" var="restrictTimeListItem">
                                        <p class="dataContP">
                                        <span>
                                            <fmt:formatDate value="${restrictTimeListItem.startTime}" pattern="yyyy-MM-dd HH:mm:ss" />-
                                            <fmt:formatDate value="${restrictTimeListItem.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
                                        </p>
                                    </c:forEach>
                                    <div id="goodsBlackActionType" data-isEdit="0" class="font-red"></div>
                                </div>
                            </div>
                        </li>
                    </c:if>

                </c:forEach>
            </c:if>
            <c:if test="${obj.goodsLimitStatus==0}">
                <li class="finalOpinion cf">
                    <label>发货限制：</label>
                    <select name="" id="goodLimit" tname="goodLimit" class="goodsLimitForever"></select>
                    <span class="ml-10 text-red goodsLimitForeverStr" style="display: none">该用户已被长期限制发货</span>
                </li>
            </c:if>
            <c:if test="${obj.goodsLimitStatus==1}">
                <c:forEach items="${obj.goodsBlackListOrdersList}" var="item">
                    <c:if test="${item.type==2}">
                        <%--找货限制字段替换  --%>
                        <li class="finalOpinion cf" id="carPenaltyInformation">
                            <div class="penalty-item">
                                <div class="penalty-title">
                                    <span>限制发货处罚信息 <span class="ml-10 text-red goodsLimitForeverStr" style="display: none">该用户已被长期限制发货</span></span>
                                    <input  class="my-button" type="button" data-status="${item.status}"
                                            data-days = '${item.days}' data-type = '${item.type}'
                                            edit-type="goodsRestrictActionType##goodsRestrictNum"
                                            onclick="openDialog('edit', $(this));" value="编辑" />
                                </div>
                                <div class="penalty-cell">
                                    <p>
                                        <label>运单ID：</label>
                                        <span>${item.orderNo}</span>
                                        <label class="ml-20">关联异常上报数据ID：</label>
                                        <span>
                                            <c:forEach items="${item.exIdList}" var="exIdItem">
                                                <c:if test="${exIdItem.exId==obj.id}">
                                                    ${exIdItem.exId}
                                                </c:if>
                                                <c:if test="${exIdItem.exId!=obj.id}">
                                                    <a href="javascript:;" onclick="goDetail(${exIdItem.exId},${exIdItem.exStatus})">${exIdItem.exId}</a>
                                                </c:if>
                                            </c:forEach>
                                        </span>
                                    </p>
                                    <p>
                                        <label>处罚发货（总天数）：</label>
                                        <span>${item.restrictNum}天</span>
                                    </p>
                                    <label>处罚时间：</label>
                                    <c:forEach items="${item.restrictTimeList}" var="restrictTimeListItem">
                                        <p class="dataContP">
                                        <span><fmt:formatDate value="${restrictTimeListItem.startTime}" pattern="yyyy-MM-dd HH:mm:ss" />-
                                        <fmt:formatDate value="${restrictTimeListItem.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
                                        </p>
                                    </c:forEach>
                                    <div id="goodsRestrictActionType" data-isEdit="0" class="font-red"></div>
                                </div>
                            </div>
                        </li>
                    </c:if>
                </c:forEach>
            </c:if>


            <li class="finalOpinion blackReason good-rea cf">
                <label>原因：</label>
                <select name="" class="reason" id="goodReason">
                </select>
                <select name="" >
                    <option value="">请选择</option>
                </select>
                <select name="" >
                    <option value="">请选择</option>
                </select>
                <select name="">
                    <option value="">请选择</option>
                </select>
            </li>
            </li>
        </ui>
    </div>
    <div class="remark finalOpinion">
        <label>备注：</label>
        <textarea id="limitRemark" class="note"  maxlength="1000" ></textarea>
    </div>


</div>
<div class="divButton">
<%--    <input  class="my-button" type="button" data-status="3" data-days = '5' data-type = '1'--%>
<%--            onclick="openDialog('edit', $(this));"--%>
<%--            edit-type="carRestrictActionType##carRestrictNum"value="编辑"/>--%>
    <input  class="button" type="button" onclick="submitForm();" value="提交" />
</div>

<%--弹窗ui--%>
<div class="dialog" id="dialog" open-type=""  edit-type=""  style="display:none">
    <div class="dialog-box">
        <div class="dialog-title">
            <span id="dialog-title">编辑</span>
            <em onclick="dialogClose()">x</em>
        </div>
    <%--编辑dom start --%>
        <div class="dialog-content" id="punishEdit" style="display:none">
            <div class="item">
                <label>类型：</label>
                <span><input  name="punishEdit" value="0" data-val="0"  type="radio" data-msg="增加">增加处罚天数</span>
                <span><input  name="punishEdit" value="1" data-val="1" type="radio" data-msg="减少" >减少处罚天数</span>
                <span><input  name="punishEdit" value="2" data-val="2" type="radio" data-msg="解除处罚" >解除处罚</span>
                <span><input  name="punishEdit" value="3" data-val="3" type="radio" data-msg='永久拉黑' >永久拉黑</span>
            </div>
            <div class="item" id="addDay" style="display: none" >
                <label>输入天数：</label>
                <input  name="" class="input-text" id="addDayInput" value=""  max="1000"  type="number">天数</span>
                <span class="ml-10" id="addDayTime"></span>
            </div>
        </div>
        <%--编辑dom end --%>
        <%-- 计算用户最终处罚时间弹窗  start--%>
        <div class="dialog-content peration-records computing-time" id="computingTime" style="display:none" >
            <div class="cell">
                <div class="records-title">
                    [车方]
                </div>
                <div class="records-item">
                    <p>
                        <span class="records-time">找货限制</span>
                        <span class="record-result ml-10" id="carRestrictTime"></span>
                    </p>
                    <p>
                        <span class="records-time">拉黑</span>
                        <span class="record-result ml-10" id="carBlackListTime"></span>
                    </p>
                </div>

            </div>
            <div class="cell pl-10">
                <div class="records-title">
                    [货方]
                </div>
                <div class="records-item">
                    <p>
                        <span class="records-time">发货限制</span>
                        <span class="record-result ml-10" id="goodsRestrictTime"></span>
                    </p>
                    <p>
                        <span class="records-time">拉黑</span>
                        <span class="record-result ml-10" id="goodsBlackListTime"></span>
                    </p>
                </div>
            </div>

            <div class="tip">

            </div>

        </div>
        <%-- 计算用户最终处罚时间弹窗  end--%>
        <%--操作记录弹窗dom start --%>
        <div  class="dialog-content" id="perationRecords" style="display:none" >
          <div class="peration-records">
              <div class="cell">
                  <div class="records-title">
                      [车方处罚操作记录]
                  </div>
                 <div id="records-car">
                 </div>
              </div>
              <div class="cell pl-10">
                  <div class="records-title">
                      [货方处罚操作记录]
                  </div>
                  <div id="records-goods">
                  </div>
              </div>
          </div>
        </div>
        <div class="dialog-footer">
            <button  onclick="dialogClose()" >取消</button>
            <button  onclick="dialogSubmit()">确定</button>
        </div>
    </div>
</div>
</body>
<script>
    window.JSESSION_ID_TRUE = true;
</script>
<script type="text/javascript" src="<%=basePath%>/exceptionpay/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="<%=basePath%>/back/model/js/viewer.min.js"></script>
<script type="text/javascript" src="<%=basePath%>/exceptionpay/js/bootstrap.js"></script>
<script type="text/javascript" src="<%=basePath%>/back/model/js/tyt_common.js"></script>
<script type="text/javascript" src="<%=basePath%>/back/jurisdiction/jsp/js/common.js"></script>
<!-- 异常处理详情页面js-->
<script type="text/javascript" src="<%=basePath%>/exceptionpay/js/exception_infofee_edit_detail.js"></script>
<!-- 异常上报文件处理js-->
<script type="text/javascript" src="<%=basePath%>/exceptionpay/js/file_info.js"></script>
<script type="text/javascript" src="<%=basePath%>/back/jsp/js/car.js"></script>
<style>
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    input[type="number"]{
        -moz-appearance: textfield;
    }

    .delete_btn{
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        border: solid 1px #20538D;
        text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.4);
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(0, 0, 0, 0.2);
        -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(0, 0, 0, 0.2);
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(0, 0, 0, 0.2);
        background: #4479BA;
        color: #FFF;
        padding: 2px 3px;
        text-decoration: none;
        cursor:pointer;
    }
    .font-red{
        color: red;
        padding: 10px 0px;
        font-size: 16px;

    }

</style>
</html>
