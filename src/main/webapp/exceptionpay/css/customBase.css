@charset 'utf-8';

/* CSS Reset Document */
body, html {
  background: #fff;
}
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, button, textarea, p, blockquote, th, td, img, select, option, a, span, em, strong, i {
  margin: 0;
  padding: 0;
}
body, form, fieldset, input, textarea, select, option {
  font: normal 14px Arial, 'Microsoft Yahei', Verdana, Geneva, sans-serif;
  color: #333;
}
table {
  border-spacing: 0;
  border-collapse: collapse;
}
fieldset, img {
  border: 0;
}
address, caption, cite, code, dfn, em, strong, th, var {
  font-weight: normal;
  font-style: normal;
}
ol, ul, li {
  list-style: none;
}
a {
  text-decoration: none;
}
button, input {
  outline: none;
  border: none 0;
}
capation, th {
  text-align: left;
}
h1, h2, h3, h4, h5, h6 {
  font-size: 100%;
  font-weight: normal;
}
textarea {
  resize: none;
}
q:before, q:after {
  content: ' ';
}
abbr, acronym {
  border: 0;
}
input {
  border: none;
}
input[type=number] {
  -moz-appearance: textfield;
}
input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
a, a:hover {
  text-decoration: none;
}
ol, ul, li {
  list-style: none;
}
.tc {
  text-align: center;
}
i {
  font-style: normal;
}
.cf:after {
  display: block;
  clear: both;
  content: '';
}
.cf {
  zoom: 1;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
body, html {
  min-width: 1000px;
}
label {
  font-style: normal;
  font-weight: normal;
}
#pageNumberBox li a {
  margin-right: 6px;
  padding: 4px 12px;
  color: #333;
}
#pageNumberBox li a, #pageNumberBox li {
  border-radius: 4px;
}
#pageNumberBox a {
  border: 1px solid #ddd;
}
#pageNumberBox .active a {
  color: #fff;
  background-color: #189beb;
  border-color: #189beb;
}
#pageNumberBigBox {
  margin-bottom: 80px;
}
.pageNumberBox {
  margin: 30px 0 !important;
}
.jumpPage {
  margin: 30px 0 30px 20px;
  font-size: 14px;
  color: #333;
}
.totalPage {
}
.keuInput {
  width: 50px;
  height: 30px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 0 10px;
}
.btnSure {
  width: 60px;
  height: 30px;
  border: none 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  margin-left: 10px;
}

/*模态框共用*/
.outMoTaiBgColor {
  width: 100%;
  height: 100%;
  position: fixed;
  _position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: #000;
  opacity: 0.7;
  filter: alpha(opacity=70);
  z-index: 10;
}
.innerTitBox {
  width: 520px;
  position: fixed;
  left: 50%;
  top: 50%;
  border: 4px solid #65c5ff;
  background: #fff;
  z-index: 12;
}
.innerTransport {
  margin: -88px 0 0 -264px;
}
.closeOutBox {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 12px;
  height: 12px;
  background: url(../images/closeDefault.png) no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
.titInnerBox {

  /* width: 440px; */
  margin: 40px 0 0 40px;
}
.makedTit {
  font-size: 20px;
  color: #333;
  margin-bottom: 12px;
  width: 432px;
  word-wrap: break-word;
}
.makedDetil {
  font-size: 12px;
  color: #999;
  width: 434px;
}
.makedBtn {
  text-align: right;
  margin: 40px 50px 20px 0;
}
.makedBtn button {
  width: 80px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  border: none 0;
  border-radius: 4px;
  margin-left: 12px;
  cursor: pointer;
}
.makedBtn .trueBtn {
  border: 1px solid #189beb;
  background: #189beb;
  color: #fff;
}
.makedBtn .falseBtn {
  background: #fff;
  color: #333;
  border: 1px solid #189beb;
}

/*复制框小提示*/
#copyBg {
  position: fixed;
  _position: absolute;
  left: 50%;
  top: 50%;
  width: auto;
  height: 40px;
  margin: -20px 0 0 -50px;
  border-radius: 6px;
  z-index: 800;
}
#copyBg .bgDiv {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.6;
  filter: alpha(opacity=60);
  border-radius: 6px;
}
#copyBg .textDiv {
  height: 40px;
  line-height: 40px;
  position: relative;
  left: 0;
  top: 0;
  text-align: center;
  color: #fff;
  font-size: 14px;
  display: inline-block;
  padding: 0 20px;
}

/*toast提示框*/
.toastBox {
  width: 290px;
  height: 110px;
  border-radius: 5px;
  background: #000;
  opacity: 0.8;
  filter: alpha(opacity=80);
  position: fixed;
  left: 50%;
  top: 50%;
  margin: -55px 0 0 -145px;
  z-index: 1200;
}
.toastBoxbg {
  width: 290px;
  height: 110px;
  position: absolute;
  left: 0;
  top: 0;
}
.toastBox h3 {
  margin-top: 20px;
  font-size: 16px;
  text-align: center;
  line-height: 34px;
  color: #fff;
}
.toastBox p {
  font-size: 16px;
  text-align: center;
  line-height: 20px;
  color: #fff;
}
.toastBoxInner i {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 12px;
  height: 12px;
  background: url(../images/closeQuery.png) no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

/* 我知道了框 */
.commonTitContent {
	font-size: 14px;
    line-height: 28px;
    text-align: left;
    margin: 20px 0;
    padding: 0 16px;
    box-sizing: border-box;
}
.commonTitContent em {
	color: #999;
	font-size: 14px;
	text-align: left
}

.commonSecondConfirm {
	width: 100%;
	height: 100%;
	position: fixed;
	_position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	background: #000;
	opacity: 0.7;
	filter: alpha(opacity=70);
	z-index: 10;
}
.commonStopCome {
	position: fixed;
	z-index: 10;
	top: 50%;
	left: 50%;
	width: 460px;
	height: 220px;
	margin-top: -110px;
	margin-left: -200px;
	background: #fff;
	z-index: 100;
	border-radius: 10px;
	-webkit-box-shadow: 0 0 5px #ccc;
	-moz-box-shadow: 0 0 5px #ccc;
	box-shadow: 0 0 5px #ccc;
}
.commonIdeabtnlabel {
    width: 90px;
	position: absolute;
    left: 50%;
    margin: 0 0 0 -43px;
    bottom: 28px;
}
.commonInnerLook {
	height: 100%;
	position: relative;
}
.commonInnerLook h2 {
	height: 40px;
	line-height: 40px;
	background: #d4e2ff;
	color: #4168b9;
	font-size: 16px;
	border-radius: 10px 10px 0 0;
	text-align: center;
}

.commonSearchLook input {
	display: inline-block;
	width: 232px;
	height: 30px;
	border: 1px solid #ccc;
	padding-left: 6px;
}
.commonSearchLook button {
	cursor: pointer;
	width: 80px;
	height: 32px;
	border: none 0;
	line-height: 32px;
	text-align: center;
	background: #4168b9;
	color: #fff;
	font-size: 14px;
	border-radius: 6px;
}
/* 新增搜索条件duhw 2020.12.09 */
.search-list {
  padding: 20px;
  border-bottom: 5px solid #d5dae6;
}
.search-list .addAboveBtn {
  margin-top: 0;
}
.search-list .form-control {
  margin-right: 15px;
  width: 120px;
}