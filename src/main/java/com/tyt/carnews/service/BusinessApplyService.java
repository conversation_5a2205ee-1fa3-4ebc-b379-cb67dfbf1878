package com.tyt.carnews.service;

import java.util.List;

import com.tyt.carnews.bean.BusinessApplyBean;
import com.tyt.model.BusinessApply;
import com.tyt.model.PageBean;
import com.tyt.service.base.BaseService;

public interface BusinessApplyService extends BaseService<BusinessApply, Long> {

	/**
	 * 商家入驻列表查询
	 * @param queryBean
	 * @param pageBean
	 * @return
	 */
	List<BusinessApplyBean> getList(BusinessApplyBean queryBean, PageBean pageBean);

	/**
	 * 商家入驻申请处理
	 * @param applyBean
	 */
	void updateApply(BusinessApply applyBean);
	
	/**
	 * 商家入驻申请导出功能
	 * @param list
	 * @return
	 */
	StringBuffer getStringCsv(List<BusinessApplyBean> list);

}
