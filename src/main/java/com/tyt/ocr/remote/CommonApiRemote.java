package com.tyt.ocr.remote;

import com.tyt.driver.bean.IdCardTwoElementVerifyDTO;
import com.tyt.driver.bean.IdCardTwoElementVerifyVO;
import com.tyt.ocr.bean.*;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/28 上午11:06
 */
public interface CommonApiRemote {

    @Data
    class CommonApiResponse<T> {
        private int code;
        private String msg;
        private T data;
        private Date timestamp;

        public boolean isSuccess() {
            return code == 200;
        }
    }

    /**
     * 营业执照
     */
    @POST("cardOcr/businessLicenseOcr")
    Call<CommonApiResponse<OcrBusinessLicenseVo>> businessLicenseOcr(@Query("url") String url);

    /**
     * 身份证正面
     */
    @POST("cardOcr/idCardFrontOcr")
    Call<CommonApiResponse<OcrIdCardFrontVo>> idCardFrontOcr(@Query("url") String url);

    /**
     * 身份证反面
     */
    @POST("cardOcr/idCardBackOcr")
    Call<CommonApiResponse<OcrIdCardBackVo>> idCardBackOcr(@Query("url") String url);

    /**
     * 驾驶证正面
     */
    @POST("cardOcr/driverLicenseFrontOcr")
    Call<CommonApiResponse<DriverLicenseFrontVo>> driverLicenseFrontOcr(@Query("url") String url);

    /**
     * 驾驶证反面
     */
    @POST("cardOcr/driverLicenseBackOcr")
    Call<CommonApiResponse<DriverLicenseBackVo>> driverLicenseBackOcr(@Query("url") String url);

    /**
     * 道路运输证
     */
    @POST("cardOcr/roadTransportOcr")
    Call<CommonApiResponse<RoadTransportVo>> roadTransportOcr(@Query("url") String url);

    /**
     * 行驶证主页
     */
    @POST("cardOcr/vehicleLicenseMainOcr")
    Call<CommonApiResponse<VehicleLicenseFrontVo>> vehicleLicenseMainOcr(@Query("url") String url);

    /**
     * 行驶证副页
     */
    @POST("cardOcr/vehicleLicenseBackOcr")
    Call<CommonApiResponse<VehicleLicenseBackVo>> vehicleLicenseBackOcr(@Query("url") String url);


    /**
     * 获取AI外呼任务列表接口
     */
    @GET("cticloud/task/query")
    Call<CommonApiResponse<CticloudResp<TaskQueryResult>>> queryTask(@Query("name") String name,@Query("timeType") Integer timeType,@Query("startTime") String startTime,@Query("endTime") String endTime);

    /**
     * AI外呼导入接口
     */
    @POST("cticloud/task/importTaskTel")
    Call<CommonApiResponse<CticloudResp<ImportTaskTelResponse>>> importTaskTel(@Body ImportTaskTelRequest importTaskTelRequest);

    /**
     * 火山引擎二要素.
     * @param req req
     * @return Call
     */
    @POST("volcengine/business_security/id_card/two_element_verify")
    Call<CommonApiResponse<IdCardTwoElementVerifyVO>> twoElementVerify(@Body IdCardTwoElementVerifyDTO req);
}
