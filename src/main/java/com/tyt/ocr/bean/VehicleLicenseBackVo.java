package com.tyt.ocr.bean;

import lombok.Data;

/**
 * 行驶证副页
 *
 * <AUTHOR>
 * @date 2023/4/28 11:13
 */
@Data
public class VehicleLicenseBackVo extends OcrDataVo {

    //检验记录
    private String inspectionRecord;

    //核定载质量
    private String approvedLoad;

    //整备质量
    private String curbWeight;

    //外廓尺寸
    private String overallSize;

    //核定载人数
    private String passengerNumber;

    //总质量
    private String totalMass;

    //燃油类型
    private String fuelType;

    //准牵引总质量
    private String tractionMass;

    //备注
    private String remark;

    //档案编号
    private String fileNumber;

    //号牌号码
    private String carNumber;

    //证芯编号
    private String chipNumber;

}
