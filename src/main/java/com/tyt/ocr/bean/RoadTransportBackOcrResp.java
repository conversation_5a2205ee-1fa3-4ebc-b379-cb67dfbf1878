package com.tyt.ocr.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * 道路运输证正页OCR识别结果类
 */
@Data
public class RoadTransportBackOcrResp implements Serializable {

    /**
     * 道路运输证号
     */
    private String roadTransportCard;
    /**
     * 业户名称
     */
    private String name;
    /**
     * 车牌号码
     */
    private String plateNum;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 座(吨)位
     */
    private String maximumCapacity;
    /**
     * 车辆尺寸
     */
    private String vehicleSize;
    /**
     * 核发机关（非必有，依赖对应运输证板式）
     */
    private String issuingAuthority;
    /**
     * 发证日期
     */
    private String issueDate;
    /**
     * 经营许可证
     */
    private String businessLicenseNo;
    /**
     * 经营范围
     */
    private String businessScope;
}
