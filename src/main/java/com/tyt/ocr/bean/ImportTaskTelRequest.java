package com.tyt.ocr.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18 10:38
 */
@NoArgsConstructor
@Data
public class ImportTaskTelRequest {

    /**
     * 任务Id
     */
    @JsonProperty("taskId")
    private String taskId;
    /**
     * 批次名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 优先级	默认0，值越大越优先
     */
    @JsonProperty("priority")
    private Integer priority;
    /**
     * 是否排重	0.不排重 1.任务内排重 2.导入号码排重，默认为1
     */
    @JsonProperty("isRepeat")
    private Integer isRepeat;
    /**
     * 批次Id	传此值表示在批次中增加号码
     */
    @JsonProperty("fileId")
    private Integer fileId;
    /**
     * 是否自动启动任务	0:不自动启动 1:自动启动，默认为0
     */
    @JsonProperty("importTelAutoStart")
    private Integer importTelAutoStart;
    /**
     * 任务号码列表	CtiLinkTaskTel中只有Tel是必选字段
     */
    @JsonProperty("taskTelList")
    private List<TaskTelListDTO> taskTelList;

    @NoArgsConstructor
    @Data
    @AllArgsConstructor
    public static class TaskTelListDTO {
        /**
         * 必选	电话号
         */
        @JsonProperty("tel")
        private String tel;
        /**
         * 可选	备选号码，tel呼叫不通时，呼叫备选号码
         * 最多支持8个，号码之间用英文逗号","分隔
         */
        @JsonProperty("backupTels")
        private String backupTels;
        /**
         * 可选	属性，json格式
         */
        @JsonProperty("property")
        private String property;
        /**
         * 可选	优先级，默认为0，值越大优先级越高
         */
        @JsonProperty("priority")
        private Integer priority;
        /**
         * 可选	电话号对应的外显号码
         */
        @JsonProperty("clid")
        private String clid;
        /**
         * 可选	使用clidGroup需要账号支持按标识路由，使用此参数是clid参数无效
         */
        @JsonProperty("clidGroup")
        private String clidGroup;
    }
}
