package com.tyt.ocr.bean;

import lombok.Data;

import java.util.Date;

/**
 * 道路运输证
 *
 * <AUTHOR>
 * @date 2023/4/28 14:21
 */
@Data
public class RoadTransportVo extends OcrDataVo {

    //车辆号牌
    private String carNumber;

    //经济类型
    private String economicType;

    //经营范围
    private String businessScope;

    //车辆类型
    private String vehicleType;

    //吨座位
    private String tonSeat;

    //备注
    private String remark;

    //经营许可证
    private String businessLicense;

    //车辆毫米_高
    private String vehicleHeight;

    //车辆毫米_宽
    private String vehicleWidth;

    //发证日期
    private String issueDateText;
    private Date issueDate;

    //地址
    private String address;

    //车辆毫米_长
    private String vehicleLength;

    //业户名称
    private String ownerName;

    //初领日期
    private String firstRegisterDateText;
    private Date firstRegisterDate;

}
