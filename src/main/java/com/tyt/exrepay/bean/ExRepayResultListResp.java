package com.tyt.exrepay.bean;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 追偿成功列表类
 *
 * <AUTHOR>
 * @since 2024-2-29 14:38:19
 */
@Data
public class ExRepayResultListResp implements Serializable {

    @ExcelProperty("ID")
    private Long id;

    @ExcelProperty("偿还身份")
    private String repayTarget;

    @ExcelProperty("用户名")
    private String trueName;

    @ExcelProperty("用户账号")
    private String cellPhone;

    @ExcelProperty("用户ID")
    private Long repayUserId;

    @ExcelProperty("运单号")
    private String tsOrderNo;

    @ExcelProperty("赔付金额")
    private BigDecimal compensateAmount;

    @ExcelProperty("赔付时间")
    private Date compensateTime;

    @ExcelProperty("到账金额")
    private BigDecimal repayAmount;

    @ExcelProperty("到账时间")
    private Date repayTime;

    @ExcelProperty("支付渠道")
    private String payChannel;

    @ExcelProperty("追偿支付商户单号")
    private String payTradeNo;
}
