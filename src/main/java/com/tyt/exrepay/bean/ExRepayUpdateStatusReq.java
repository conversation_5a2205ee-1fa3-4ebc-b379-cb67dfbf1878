package com.tyt.exrepay.bean;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 追偿修改状态请求参数类
 *
 * <AUTHOR>
 * @since 2023-11-6 19:37:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExRepayUpdateStatusReq implements Serializable {

    public static final int ACTION_TYPE_REPAY = 1;

    public static final int ACTION_TYPE_CANCEL = 2;

    public static final int ACTION_TYPE_DELETE = 3;

    /**
     * 追偿ID
     */
    @NotNull(message = "追偿ID为空")
    @Min(value = 0, message = "追偿ID不合法")
    private Long repayId;
    /**
     * 操作类型：1-发起追偿；2-取消追偿；3-删除追偿；
     */
    @NotNull(message = "操作类型不能为空")
    private Integer actionType;
}
