package com.tyt.exrepay.service;


import com.tyt.exrepay.bean.ExRepayListReq;
import com.tyt.exrepay.bean.ExRepayListResp;
import com.tyt.exrepay.bean.ExRepayUpdateAmountReq;
import com.tyt.exrepay.bean.ExRepayUpdateStatusReq;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.mybatis.mapper.model.TransportWaybillExRepay;
import com.tyt.util.page.PageData;

/**
 * <p>
 * 异常上报追偿发起表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-02-28
 */
public interface TransportWaybillExRepayService {

    /**
     * 根据ID查询追偿信息
     *
     * @param repayId 追偿ID
     * @return 追偿信息
     */
    TransportWaybillExRepay getRepayById(Long repayId);

    /**
     * 查询追偿信息列表
     *
     * @param req 搜索条件类
     * @return 追偿列表
     */
    PageData<ExRepayListResp> getRepayList(ExRepayListReq req);

    /**
     * 新增追偿信息
     *
     * @param repayInfo 追偿信息
     */
    void saveRepayInfo(TransportWaybillExRepay repayInfo);

    /**
     * 更新追偿金额
     *
     * @param req 追偿ID和金额
     * @param curUser 操作用户
     */
    void updateAmount(ExRepayUpdateAmountReq req, EmployeeQueryBean curUser);

    /**
     * 更新追偿状态
     *
     * @param req 追偿ID和状态
     * @param repay 追偿信息
     * @param curUser 操作用户
     */
    void updateStatus(ExRepayUpdateStatusReq req, TransportWaybillExRepay repay, EmployeeQueryBean curUser);

    /**
     * 根据异常上报ID构建追偿信息
     *
     * @param exId 异常上报ID
     * @return 追偿信息
     */
    TransportWaybillExRepay buildRepayInfo(Long exId);
}
