package com.tyt.exrepay.service;


import com.tyt.exrepay.bean.ExRepayResultListReq;
import com.tyt.exrepay.bean.ExRepayResultListResp;
import com.tyt.mybatis.mapper.model.TransportWaybillExRepayResult;
import com.tyt.util.page.PageData;

import java.util.List;

/**
 * <p>
 * 异常上报追偿结果表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-02-28
 */
public interface TransportWaybillExRepayResultService {

    PageData<ExRepayResultListResp> getRepayResultList(ExRepayResultListReq req);

    List<ExRepayResultListResp> getRepayResultListNoPage(ExRepayResultListReq req);
}
