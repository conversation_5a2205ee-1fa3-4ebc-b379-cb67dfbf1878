package com.tyt.infofee.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.alibaba.fastjson.JSON;
import com.tyt.common.bean.ShortMsgBean;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.util.Constant;
import com.tyt.util.RandomUtil;
import com.tyt.util.SerialNumUtil;
import com.tyt.web.base.BaseController;

@Controller
@RequestMapping("/withdrawVerify")
public class WithdrawVerifyController extends BaseController {
	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;
	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;
	
	private static String WITHDRAW_VERIFY_TEMPLATE_CODE="SMS_140120152";

	/**
	 * 发送验证码
	 * @param userId
	 * @param phoneNum
	 * @return
	 */
	@RequestMapping(value = "/sendVerify")
	@ResponseBody
	public ResultMsgBean sendVerify(String userId, String phoneNum) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			ShortMsgBean shortMsgBean = new ShortMsgBean();
			String content = messageTmplService.getSmsTmpl(Constant.WITHDRAW_VERIFY_MSG_KEY, "验证码{verifycode}，请尽快输入验证码完成身份验证，若非本人操作请忽略");
			// 生成验证码
			String verifycode = RandomUtil.getSixRandom();
			// 保存验证码，用于验证
			RedisUtil.set(Constant.WITHDRAW_VERIFY_CODE_REDIS_KEY + phoneNum, verifycode, (int)Constant.CACHE_EXPIRE_TIME_30M);
			content = StringUtils.replaceEach(content, new String[] {"{verifycode}"}, new String[] {verifycode});
			shortMsgBean.setContent(content);
			shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			String messageSerailNum = SerialNumUtil.generateSeriaNum();
			shortMsgBean.setMessageSerailNum(messageSerailNum);
			shortMsgBean.setIsVerifyCode(1);
			shortMsgBean.setVerifyCode(verifycode);
			shortMsgBean.setTemplateCode(WITHDRAW_VERIFY_TEMPLATE_CODE);
			shortMsgBean.setRemark("");
			shortMsgBean.setCell_phone(phoneNum);
			String messageContent = JSON.toJSONString(shortMsgBean);
			tytMqMessageService.addMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			tytMqMessageService.sendMqMessage(messageSerailNum, messageContent);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
		}
		return result;
	}

	/**
	 * 验证验证码
	 * @param userId
	 * @param phoneNum
	 * @return
	 */
	@RequestMapping(value = "/validateVerifyNum")
	@ResponseBody
	public ResultMsgBean validateVerifyNum(String userId, String phoneNum, String verifyNum, HttpServletRequest request) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			String realVerifyNum = RedisUtil.get(Constant.WITHDRAW_VERIFY_CODE_REDIS_KEY + phoneNum);
			if (StringUtils.isEmpty(verifyNum)) {
				result.setCode(300);
				result.setMsg("验证码不能为空");
			} else if (!verifyNum.equals(realVerifyNum)) {
				result.setCode(300);
				result.setMsg("请输入正确的验证码");
			} else {
				HttpServletRequest httpRequest = (HttpServletRequest) request;
				HttpSession session = httpRequest.getSession();
				session.setAttribute(Constant.WITHDRAW_VALIDATE_SESSION_KEY, Constant.WITHDRAW_VALIDATE_OK);
				result.setCode(200);
				result.setMsg("验证成功");
				RedisUtil.del(Constant.WITHDRAW_VERIFY_CODE_REDIS_KEY + phoneNum);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
		}
		return result;
	}
}
