package com.tyt.infofee.controller;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.tyt.service.common.redis.RedisUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqDrawMoneyMsg;
import com.tyt.infofee.bean.WithDrawApplyInfoBean;
import com.tyt.infofee.bean.WithdrawApplyReqMsgBean;
import com.tyt.infofee.bean.YiBaoTransferBean;
import com.tyt.infofee.service.InfofeeService;
import com.tyt.infofee.service.LianLianTransferService;
import com.tyt.infofee.service.TytTransferOrderService;
import com.tyt.infofee.service.YiBaoTransferService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.config.TytConfigService;
import com.tyt.util.AppConfig;
import com.tyt.util.Constant;
import com.tyt.util.CsvWriter;
import com.tyt.util.LockUtil;
import com.tyt.util.MD5Util;
import com.tyt.util.TimeUtil;
import com.tyt.web.base.BaseController;

@Controller
@RequestMapping("/infofee")
public class InfofeeController extends BaseController {
    @Resource(name = "infofeeService")
    private InfofeeService infofeeService;
    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "tytTransferOrderService")
    private TytTransferOrderService tytTransferOrderService;

    @Resource(name = "yiBaoTransferService")
    private YiBaoTransferService yiBaoTransferService;
    @Resource(name = "lianLianTransferService")
    private LianLianTransferService lianLianTransferService;
    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    /**
     *
     * @param pageNo
     * @param request
     * @return
     */
    @RequestMapping("/withdrawApply")
    public String withdrawApply(Integer pageNo, WithdrawApplyReqMsgBean withdrawApplyReqMsgBean, HttpServletRequest request) {
        logger.info("get user withdraw message, request msg is: " + withdrawApplyReqMsgBean);
        String goUrl = "";
        try {
            if (pageNo == null) {
                // 第一次加载默认显示未打款数据
                withdrawApplyReqMsgBean.setTransferStatus("1");
                pageNo = 1;
            }
            // 判断是否已经验证了验证码
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpSession session = httpRequest.getSession();
            if (Constant.WITHDRAW_VALIDATE_OK.equals(session.getAttribute(Constant.WITHDRAW_VALIDATE_SESSION_KEY))) {
                List<WithDrawApplyInfoBean> withdrawApplyInfoBeans = infofeeService.getWithdrawApply(pageNo, withdrawApplyReqMsgBean, request);
                request.setAttribute("withdrawApplyInfoBeans", withdrawApplyInfoBeans);
                request.setAttribute("withdrawApplyReqMsgBean", withdrawApplyReqMsgBean);
                goUrl = "back/jsp/infofee/infofeeWithdrawApplyList";
            } else {
                EmployeeQueryBean curLoginUser = getCurrentUser(httpRequest);
                request.setAttribute("curLoginUser", curLoginUser);
                goUrl = "back/jsp/infofee/validateWithdraw";
            }
        } catch (Exception e) {
            logger.error("get user withdraw message failed, the error message is: " + e);
        }
        return goUrl;
    }

    /**
     *
     * @param id
     *            提款申请主键
     * @param transferBankName
     *            提款申请银行
     * @param transferType
     *            转账方式 1 微信 2 支付宝 3 银行卡 4 其他
     * @param transferStaus
     *            转账状态 转帐状态，1：未转帐 2：已转帐 3：转账失败
     * @param failureReason
     *            转账失败原因
     * @param request
     * @param menuId
     * @return
     */
    @RequestMapping("/updateWithdrawApply")
    @ResponseBody
    public void updateWithdrawApply(String id, String transferBankName, String transferType, String transferStaus, String failureReason, HttpServletRequest request, HttpServletResponse response) {
        logger.info("update withdraw apply message");
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "更新数据");
        try {
            EmployeeQueryBean user = getCurrentUser(request);
            infofeeService.updateWithdrawApply(user.getRealName(), id, transferBankName, transferType, transferStaus, failureReason);
            // 如果是转账成功则发送提现成功消息，如果是转账失败则发送转账失败消息
            MqDrawMoneyMsg drawMoneyMsg = infofeeService.saveMessage(id, transferStaus, failureReason);
            tytMqMessageService.sendMqMessage(drawMoneyMsg.getMessageSerailNum(), JSON.toJSONString(drawMoneyMsg));
        } catch (Exception e) {
            logger.error("update withdraw apply message failed, the error message is: " + e);
        }
        printJSON(request, response, msgBean);
        return;
    }


    /**
     *
     * @param id  提款申请主键
     * @return
     */
    @RequestMapping("/updateWithdrawRecordStatus")
    @ResponseBody
    public ResultMsgBean updateWithdrawRecordStatus(String id) {
        logger.info("updateWithdrawReordStatus withdraw apply message");
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "审核成功");
        try {
            msgBean =infofeeService.updateWithdrawRecordStatus(id);
        } catch (Exception e) {
            logger.error("update withdraw apply message failed, the error message is: " + e);
        }
        return msgBean;
    }
    
    @RequestMapping("/excel/export")
    public void exportFinancialFlow(Integer pageNo, WithdrawApplyReqMsgBean withdrawApplyReqMsgBean, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 结果
            List<WithDrawApplyInfoBean> withdrawApplyInfoBeans = infofeeService.getWithdrawApplyAll(withdrawApplyReqMsgBean);
            // 导出操作
            String CSV_RN = "\r\n";// 列分隔符
            // 数据头部
            StringBuilder content = new StringBuilder("ID,会员帐号/认证姓名,提现申请时间,卡号/持卡人姓名,提现金额,打款状态/渠道,操作时间" + CSV_RN);
            // 解析数据
            if (withdrawApplyInfoBeans != null && withdrawApplyInfoBeans.size() > 0) {
                for (WithDrawApplyInfoBean withDrawApplyInfoBean : withdrawApplyInfoBeans) {
                    objectUserToCvsString(content, withDrawApplyInfoBean);
                    content.append(CSV_RN);
                }
            }
            // 导出
            CsvWriter.exportCsv("用户提现记录", content.toString(), response, "GBK");
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
    }
    private void objectUserToCvsString(StringBuilder content, WithDrawApplyInfoBean withDrawApplyInfoBean) {
        content.append(withDrawApplyInfoBean.getId()).append(",");
        content.append(withDrawApplyInfoBean.getUserAccount() + "/" + withDrawApplyInfoBean.getUserAuthName()).append(",");
        content.append(withDrawApplyInfoBean.getWithdrawApplyTime()).append(",");
        content.append(withDrawApplyInfoBean.getCardNum() + "/" + withDrawApplyInfoBean.getCardOwnerName()).append(",");
        content.append(Integer.valueOf(withDrawApplyInfoBean.getWithdrawAmmount()) / 100).append(",");
        content.append(withDrawApplyInfoBean.getWithdrawStatusAndChannelValue()).append(",");
        content.append(withDrawApplyInfoBean.getOperateTime()).append(",");
    }
    /**
     * 提现管理点击处理创建转账订单
     * @param withdrawId
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/createTransferOrder")
    @ResponseBody
    public ResultMsgBean transferRequest(
            @RequestParam(value="withdrawId", required=true) Long withdrawId,
            HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean msgBean = new ResultMsgBean();
        try {
            logger.info("transfer create order get redis lock begin, withdrawId is: " + withdrawId);
            // 同步锁，防止并发生成多个转账订单
            if (LockUtil.lockObject("3", String.valueOf(withdrawId), 10)) {
                logger.info("transfer create order get redis lock success, withdrawId is " + withdrawId);
                Boolean flg = tytTransferOrderService.getOrederStatusByWithdrawId(withdrawId);
                if (flg) {
                    msgBean.setCode(203);
                    msgBean.setMsg("该订单已处理");
                    return msgBean;
                }
                String orderId = tytTransferOrderService.saveOrder(withdrawId);
                if (StringUtils.isNotBlank(orderId)) {
                    msgBean.setCode(200);
                    msgBean.setMsg("订单创建成功");
                    msgBean.setData(orderId);
                } else {
                    msgBean.setCode(201);
                    msgBean.setMsg("订单创建失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            msgBean.setCode(500);
            msgBean.setMsg("服务器错误");
        } finally {
            logger.info("transfer create order release redis lock success, withdrawId is " + withdrawId);
            // 操作完毕，释放锁
            LockUtil.unLockObject("3", String.valueOf(withdrawId));
        }
        return msgBean;
    }
    /**
     * 新转账接口（提现转账）
     * @param userId 用户ID
     * @param cardNum 银行卡号
     * @param withdrawId 提款申请对应id
     * @param transferType 预留选着转账的第三方，1 易宝  2 连连
     * @return
     */
    @RequestMapping("/transferRequest")
    @ResponseBody
    public ResultMsgBean transferRequest(
            @RequestParam(value="withdrawId", required=true) Long withdrawId,
            @RequestParam(value="orderId", required=true) String orderId,
            @RequestParam(value="bankName", required=true) String bankName,
            @RequestParam(value="cardNum", required=true) String cardNum,
            @RequestParam(value="transferType", required=false) Integer transferType,
            HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean msgBean = new ResultMsgBean();
        HttpSession session = request.getSession();
        String order = (String)session.getAttribute(orderId);
        if(StringUtils.isBlank(order)) {
            session.setAttribute(orderId, orderId);
        }else {
            msgBean.setCode(300);
            msgBean.setMsg("订单已在处理中,请勿重复提交订单");
            return msgBean;
        }
        try {
            logger.info("do transfer request get redis lock begin, withdrawId is: " + withdrawId);
            // 同步锁，防止并发生成多个转账订单
            if (LockUtil.lockObject("3", String.valueOf(withdrawId), 10)) {
                logger.info("do transfer request get redis lock success, withdrawId is "
                        + withdrawId);
                if (StringUtils.isNotBlank(orderId)) {
                    // 先根据提现表id到订单表中查询，该条提现记录是否已处理（包括处理中，转账成功，转账失败）如若已处理，直接返回
                    Boolean flg = tytTransferOrderService.getOrederStatusByWithdrawId(withdrawId);
                    if (flg) {
                        msgBean.setCode(203);
                        msgBean.setMsg("该订单已处理");
                        return msgBean;
                    }
                    // 请求转账
                    YiBaoTransferBean msg = null;
                    if (transferType != null && transferType > 0) {
                        tytTransferOrderService.updateBankByOrderId(orderId, bankName,
                                transferType);
                        if (transferType == 1) {// 如果transferType=1调用易宝转账
                            msg = yiBaoTransferService.saveRequest(orderId, request, response);
                        } else if (transferType == 2) {// transferType=2调用连连转账
                            msg = lianLianTransferService.saveReaquest(orderId);
                        }
                    } else {
                        Integer intValue = configService.getIntValue("withdrawal_transfer_type", 2);
                        tytTransferOrderService.updateBankByOrderId(orderId, bankName, intValue);
                        if (intValue == 1) {
                            msg = yiBaoTransferService.saveRequest(orderId, request, response);
                        } else {
                            msg = lianLianTransferService.saveReaquest(orderId);
                        }
                    }
                    EmployeeQueryBean user = getCurrentUser(request);
                    String realName = (user == null ? "系统自动转账" : user.getRealName());
                    if (msg.getResultStatus() == 1) {
                        // 如果受理成功，修改提状态为 4;处理中
                        infofeeService.updateWithdrawApply(realName,
                                withdrawId.toString(), bankName, "3", "4", "");
                        msgBean.setCode(200);
                        msgBean.setMsg("处理中");
                        infofeeService.updateBankCardName(cardNum, bankName);
                    } else if (msg.getResultStatus() == 3 || msg.getResultStatus() == 4) {
                        // 操作后，状态置成 处理中，避免二次操作
                        //EmployeeQueryBean user = getCurrentUser(request);
                        infofeeService.updateWithdrawApply(realName,
                                withdrawId.toString(), bankName, "3", "4", "设置处理中状态，避免二次操作");

                        // 此状态为返回结果：3.银行转账成功 或 4.转账失败，更新提现表
                        String transferStaus = msg.getResultStatus() == 3 ? "2" : "3";
                        infofeeService.updateWithdrawStatus(transferStaus, withdrawId,
                                msg.getResultFaileMsg());
                        // 如果是转账成功则发送提现成功消息，如果是转账失败则发送转账失败消息
                        MqDrawMoneyMsg drawMoneyMsg = infofeeService.saveMessage(
                                withdrawId.toString(), transferStaus, msg.getResultFaileMsg());
                        tytMqMessageService.sendMqMessage(drawMoneyMsg.getMessageSerailNum(),
                                JSON.toJSONString(drawMoneyMsg));
                        if (msg.getResultStatus() == 3) {
                            msgBean.setCode(204);
                            msgBean.setMsg("转账成功");
                        } else if (msg.getResultStatus() == 4) {
                            msgBean.setCode(205);
                            msgBean.setMsg("转账失败,失败原因:" + msg.getResultFaileMsg());
                        }
                    } else {
                        // 如果转账实时受理失败，返回页面失败原因
                        msgBean.setCode(201);
                        msgBean.setMsg(msg.getResultFaileMsg());
                    }
                } else {
                    msgBean.setCode(202);
                    msgBean.setMsg("订单创建失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            msgBean.setCode(500);
            msgBean.setMsg("服务器错误");
        } finally {
            // 操作完毕，释放锁
            LockUtil.unLockObject("3", String.valueOf(withdrawId));
        }
        if(msgBean.getCode()>0) {
            session.removeAttribute(orderId);
        }
        return msgBean;
    }

    /**
     * 批量自动转账
     * @param validateSign 验签 规则为MD5("yyyy-MM-dd"+privateKey)
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/batchTransfer")
    @ResponseBody
    public ResultMsgBean batchTransfer(String validateSign,
                                       HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean msgBean = new ResultMsgBean();
        Long startTime = System.currentTimeMillis();
        logger.info("batch transfer begin,validateSign is: " + validateSign+",startTime:"+startTime);
        // 验证validateSign
        if (validateSign(validateSign)) {
            logger.info("batch transfer validate ok");
            // 建立线程池
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    boolean isSuccess = RedisUtil.setNx("batch_transfer_list_key", "batch_transfer",600);
                    if (isSuccess){
                        List<WithDrawApplyInfoBean> withdrawApplyInfoBeans = infofeeService.getWithdrawApply();
                        logger.info("batch transfer withdrawApplyInfoBeans: " + withdrawApplyInfoBeans + ", size: " + withdrawApplyInfoBeans.size());
                        WithDrawApplyInfoBean curApply;
                        String withdrawId;
                        for (int i = 0; i < withdrawApplyInfoBeans.size(); i++) {
                            // 创建订单
                            curApply = withdrawApplyInfoBeans.get(i);
                            withdrawId = curApply.getId();
                            ResultMsgBean createOrderRes = transferRequest(Long.valueOf(withdrawId), request, response);
                            logger.info("batch transfer withdrawId: " + withdrawId + " createOrderRes: " + createOrderRes.getCode());
                            // 只有创建订单成功才继续操作
                            if (createOrderRes != null && createOrderRes.getCode().intValue() == 200) {
                                logger.info("batch transfer withdrawId: " + withdrawId + " begin transfer");
                                // 转账
                                String orderId = createOrderRes.getData().toString();
                                String bankName = curApply.getCardBlongedBank();
                                String cardNum = curApply.getCardNum();
                                Integer transferType = null;
                                ResultMsgBean transferResult = transferRequest(Long.valueOf(withdrawId), orderId, bankName, cardNum, transferType, request, response);
                                logger.info("batch transfer withdrawId: " + withdrawId
                                        + " transfer end, result code is:" + transferResult.getCode()
                                        + ", msg is: " + transferResult.getMsg());
                            }
                        }
                        Long needTime = System.currentTimeMillis()-startTime;
                        logger.info("batch transfer finish，耗时："+ needTime);
                    }
                }
            });
            // 关闭线程
            executorService.shutdown();
        } else {
            msgBean.setCode(300);
            msgBean.setMsg("验签失败");
        }
        logger.info("batch transfer end");
        return msgBean;
    }

    private boolean validateSign(String validateSign) {
        String date = TimeUtil.formatDate(new Date());
        String realSign = MD5Util.GetMD5Code(date + AppConfig.getProperty("batch.transter.private.key"));
        logger.info("batch transfer,validateSign is: " + validateSign + ",realSign is: " + realSign);
        return realSign.equalsIgnoreCase(validateSign);
    }
}
