package com.tyt.infofee.enums;


import lombok.Getter;

/**
 * 异常枚举类
 */
@Getter
public enum ExceptionEnum {
    REQUEST_PARAM_NULL(300001, "请求参数为空"),
    MIN_VALUE_CAN_NOT_GREATER_THAN_MAX_VALUE(300002, "最小值不能大于最大值"),
    UPDATE_FAIL(300003, "修改失败"),
    SAVE_FAIL(300004, "添加失败"),
    FEE_CONFIG_OVERLAP(300005, "阶梯内的数字范围不能有交集"),
  ;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String message;

    ExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}