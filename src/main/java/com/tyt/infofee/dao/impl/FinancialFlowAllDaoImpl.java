package com.tyt.infofee.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.infofee.dao.FinancialFlowDao;
import com.tyt.model.FinancialFlow;

@Repository("financialFlowAllDao")
public class FinancialFlowAllDaoImpl extends BaseDaoImpl<FinancialFlow, Long> implements FinancialFlowDao {
	public FinancialFlowAllDaoImpl() {
		this.setEntityClass(FinancialFlow.class);
	}
}