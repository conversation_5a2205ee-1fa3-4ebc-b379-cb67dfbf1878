package com.tyt.infofee.service.impl;


import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.infofee.service.DrawMoneyApplyService;
import com.tyt.infofee.service.TytTransferOrderService;
import com.tyt.model.TytDrawMoneyApply;
import com.tyt.model.TytTransferOrder;
import com.tyt.payment.util.MakeOrderNum;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.config.TytConfigService;
@Service("tytTransferOrderService")
public class TytTransferOrderServiceImpl extends BaseServiceImpl<TytTransferOrder, Long> implements TytTransferOrderService {

	@Resource(name = "tytConfigService")
	private TytConfigService configService;
	@Resource(name = "drawMoneyApplyService")
	private DrawMoneyApplyService drawMoneyApplyService;
	
	@Resource(name = "tytTransferOrderDao")
	public void setBaseDao(BaseDao<TytTransferOrder, Long> tytTransferOrderDao) {
		super.setBaseDao(tytTransferOrderDao);
	}
	@Override
	public String saveOrder(Long withdrawId) {
		String sql="select * from tyt_transfer_order where withdrawal_id=? and transfer_status=? ORDER BY id DESC";
		List<TytTransferOrder> list = this.getBaseDao().queryForList(sql, new Object[] {withdrawId,1});
		if(list!=null && list.size()>0) {
			return list.get(0).getOrderId();
		}
		TytDrawMoneyApply apply = drawMoneyApplyService.getById(withdrawId.intValue());
		TytTransferOrder order=new TytTransferOrder();
		if(apply!=null ) {
			order.setWithdrawalId(withdrawId);
			order.setTransferStatus(1);//1。创建
			Integer amo=Integer.valueOf(apply.getDrawAccount());
			NumberFormat n = new DecimalFormat(("0.00"));
			order.setAmount(n.format(amo/100.0));
			order.setAccountName(apply.getBankCardOwner());
			order.setAccountNumber(apply.getBankCardNumber());
			order.setOrderId("800"+MakeOrderNum.getOrderNo());//订单号
			order.setBatchNo("801"+MakeOrderNum.getOrderNo());//批次号
			order.setTransferType(1);//1。单笔打款
			order.setTransferBankName(apply.getCardBlongedBank());//银行名称
//			order.setTransferPlatform(1);//转账平台 1 ：易宝  2  连连
			this.getBaseDao().insert(order);
		}
		return order.getOrderId();
	}
	@Override
	public void updateOrder(String orderId,Integer status, String errorMsg) {
		if(errorMsg!=null) {
			//失败
			String sql="UPDATE tyt_transfer_order SET transfer_status=?,transfer_fail_message=? ,utime=? WHERE order_id=? ";
			this.getBaseDao().executeUpdateSql(sql, new Object[] {status,errorMsg,new Date(),orderId});
		}else {
			//受理中/成功
			String sql="UPDATE tyt_transfer_order SET transfer_status=?,utime=? WHERE order_id=? ";
			this.getBaseDao().executeUpdateSql(sql, new Object[] {status,new Date(),orderId});
		}
		
	}
	@Override
	public TytTransferOrder getBeanByOrderId(String orderId) {
		String sql="select * from tyt_transfer_order where order_id=? ";
		 List<TytTransferOrder> list = this.getBaseDao().queryForList(sql, new Object[] {orderId});
		 if(list!=null && list.size()>0) {
			 return list.get(0);
		 }
		return null;
	}
	@Override
	public void updateBankByOrderId(String orderId, String bankName,Integer transferType) {
		String sql="UPDATE tyt_transfer_order SET transfer_bank_name=? ,transfer_platform=?  WHERE order_id=? ";
		this.getBaseDao().executeUpdateSql(sql, new Object[] {bankName,transferType,orderId});
		
	}
	@Override
	public Boolean getOrederStatusByWithdrawId(Long withdrawId) {
		Boolean flg=false;
		String sql="SELECT * FROM tyt_transfer_order WHERE (transfer_status=? OR transfer_status=?) AND withdrawal_id=?";
		 List<TytTransferOrder> list = this.getBaseDao().queryForList(sql, new Object[] {2,3,withdrawId});
		 if(list!=null && list.size()>0) {
			 flg=true;
			 return flg;
		 }
		return flg;
	}
}
