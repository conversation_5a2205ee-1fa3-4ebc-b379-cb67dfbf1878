package com.tyt.infofee.service.impl;


import com.tyt.dao.base.BaseDao;
import com.tyt.infofee.service.WithdrawalBalanceService;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.model.WithdrawalBalance;
import com.tyt.service.base.BaseServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/29 10:49
 */
@Service("withdrawalBalanceService")
public class WithdrawalBalanceServiceImpl extends BaseServiceImpl<WithdrawalBalance,Long> implements WithdrawalBalanceService {

    @Resource(name="withdrawalBalanceDao")
    public void setBaseDao(BaseDao<WithdrawalBalance, Long> withdrawalBalanceServiceDao) {
        super.setBaseDao(withdrawalBalanceServiceDao);
    }

    @Override
    public Map<String, Object> getWithdrawalBalanceList(String startTime, String endTime, Integer page, Integer size) {
        Map<String, Object> map=new HashMap<>();
        try {
            StringBuffer conditionBuffer = new StringBuffer();
            StringBuffer conditionCount = new StringBuffer();
            List<Object> params = new ArrayList<Object>();
            Map<String,Object> paramMap = new HashMap<String,Object>();
            if (StringUtils.isNotBlank(startTime)){
                conditionBuffer.append(" and create_time> ?");
                conditionCount.append(" and create_time> :startTime");
                params.add(startTime+" 00:00:00");
                paramMap.put("startTime",startTime+" 00:00:00");
            }
            if (StringUtils.isNotBlank(endTime)){
                conditionBuffer.append(" and create_time< ?");
                conditionCount.append(" and create_time< :endTime");
                paramMap.put("endTime",endTime+" 23:59:59");
                params.add(endTime+" 23:59:59");
            }
            StringBuffer sql=new StringBuffer("select id id,withdrawal_balance withdrawalBalance,create_time createTime from tyt_withdrawal_balance where 1=1");
            StringBuffer sqlCount=new StringBuffer("select count(1)  from tyt_withdrawal_balance where 1=1");
            Map<String, Type> scalarMap = new HashMap<String, Type>();
            scalarMap.put("id", Hibernate.LONG);
            scalarMap.put("withdrawalBalance", Hibernate.BIG_DECIMAL);
            scalarMap.put("createTime", Hibernate.TIMESTAMP);
            sql.append(conditionBuffer+" ORDER BY id desc LIMIT ?, ?");
            params.add((page-1)*size);
            params.add(size);
            List<WithdrawalBalance> search = this.getBaseDao().search(sql.toString(), scalarMap, WithdrawalBalance.class, params.toArray());
            BigInteger rowCount = this.getBaseDao().queryByMap(sqlCount.append(conditionCount).toString(), paramMap);
            map.put("code", ReturnCodeConstant.OK);
            map.put("msg","查询成功");
            map.put("Data",search);
            map.put("count",rowCount);
            return map;
        }catch (Exception e){
            e.printStackTrace();
            map.put("code",ReturnCodeConstant.ERROR);
            map.put("msg","服务器错误");
            return map;
        }
    }
}
