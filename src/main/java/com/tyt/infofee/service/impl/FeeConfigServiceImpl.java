package com.tyt.infofee.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.EditTytFeeConfigBean;
import com.tyt.infofee.bean.TytFeeConfigBean;
import com.tyt.infofee.enums.DelStatusEnum;
import com.tyt.infofee.enums.ExceptionEnum;
import com.tyt.infofee.service.FeeConfigService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytFeeConfig;
import com.tyt.manager.mapper.base.TytFeeConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/05/28 19:52
 */
@Service
@Slf4j
public class FeeConfigServiceImpl implements FeeConfigService {

    @Autowired
    private TytFeeConfigMapper tytFeeConfigMapper;

    @Override
    public List<TytFeeConfig> listFeeConfig() {
        return tytFeeConfigMapper.listFeeConfig();
    }

    @Override
    public int saveFeeConfigList(List<TytFeeConfigBean> feeConfigBeanList, EmployeeQueryBean currentUser) {
        log.info("saveFeeConfigList:{}", JSON.toJSONString(feeConfigBeanList));
        try {
            //按照sort_id排序
            feeConfigBeanList.sort(Comparator.comparing(TytFeeConfigBean::getSortId));
            //遍历feeConfigBeanList ，判断是否有最小值大于最大值的情况
            //数字范围不能有交集
            for (int i = 0; i < feeConfigBeanList.size() - 1; i++) {
                TytFeeConfigBean currentBean = feeConfigBeanList.get(i);
                if (currentBean.getMinVal().compareTo(currentBean.getMaxVal()) > 0) {
                    throw new Exception(ExceptionEnum.MIN_VALUE_CAN_NOT_GREATER_THAN_MAX_VALUE.getMessage());
                }
                TytFeeConfigBean nextBean = feeConfigBeanList.get(i+1);
                // 检查当前对象的maxVal是否大于下一个对象的minVal，表示交叉
                if (currentBean.getMaxVal().compareTo(nextBean.getMinVal()) > 0) {
                    throw new Exception(ExceptionEnum.FEE_CONFIG_OVERLAP.getMessage());
                }

            }
            List<TytFeeConfig> tytFeeConfigList = feeConfigBeanList.stream()
                    .map(feeConfigBean -> {
                        TytFeeConfig tytFeeConfig = new TytFeeConfig();
                        tytFeeConfig.setMinVal(feeConfigBean.getMinVal());
                        tytFeeConfig.setMaxVal(feeConfigBean.getMaxVal());
                        tytFeeConfig.setRatio(feeConfigBean.getRatio());
                        tytFeeConfig.setSortId(feeConfigBean.getSortId());
                        if (StringUtils.isEmpty(feeConfigBean.getType())) {
                            tytFeeConfig.setType("0");
                        }
                        tytFeeConfig.setCreateUserId(currentUser.getId());
                        return tytFeeConfig;
                    }).collect(Collectors.toList());

            return tytFeeConfigMapper.insertFeeConfigs(tytFeeConfigList);
        } catch (Exception e) {
            log.error("saveFeeConfigList param: {}", JSON.toJSONString(feeConfigBeanList), e);
        }
        return -1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int editFeeConfig(List<EditTytFeeConfigBean> feeConfigBeanList, EmployeeQueryBean currentUser) {
        log.info("editFeeConfig:{}", JSON.toJSONString(feeConfigBeanList));
        try {
            feeConfigBeanList.sort(Comparator.comparing(TytFeeConfigBean::getSortId));
            //数字范围不能有交集
            for (int i = 0; i < feeConfigBeanList.size() -1; i++) {
                TytFeeConfigBean currentBean = feeConfigBeanList.get(i);
                if (currentBean.getMinVal().compareTo(currentBean.getMaxVal()) > 0) {
                    throw new Exception(ExceptionEnum.MIN_VALUE_CAN_NOT_GREATER_THAN_MAX_VALUE.getMessage());
                }
                TytFeeConfigBean nextBean = feeConfigBeanList.get(i+1);
                // 检查当前对象的maxVal是否大于下一个对象的minVal，表示交叉
                if (currentBean.getMaxVal().compareTo(nextBean.getMinVal()) > 0) {
                    throw new Exception(ExceptionEnum.FEE_CONFIG_OVERLAP.getMessage());
                }
            }
            List<TytFeeConfig> tytFeeConfigs = listFeeConfig();
            //把之前的置为删除状态
            if (CollectionUtils.isNotEmpty(tytFeeConfigs)) {
                List<Long> idList = tytFeeConfigs.stream().map(TytFeeConfig::getId)
                        .collect(Collectors.toList());
                idList.removeIf(id -> id == null || id <= 0);
                if (CollectionUtils.isNotEmpty(idList)) {
                    tytFeeConfigMapper.updateFeeConfigs(currentUser.getId(), idList);
                }
            }
            List<TytFeeConfig> tytFeeConfigList = feeConfigBeanList.stream()
                    .map(feeConfigBean -> {
                        TytFeeConfig tytFeeConfig = new TytFeeConfig();
                        tytFeeConfig.setMinVal(feeConfigBean.getMinVal());
                        tytFeeConfig.setMaxVal(feeConfigBean.getMaxVal());
                        tytFeeConfig.setRatio(feeConfigBean.getRatio());
                        tytFeeConfig.setSortId(feeConfigBean.getSortId());
                        if (StringUtils.isEmpty(feeConfigBean.getType())) {
                            tytFeeConfig.setType("0");
                        }
                        tytFeeConfig.setCreateUserId(currentUser.getId());
                        return tytFeeConfig;
                    }).collect(Collectors.toList());
            //批量保存
            return tytFeeConfigMapper.insertFeeConfigs(tytFeeConfigList);
        } catch (Exception e) {
            log.error("editFeeConfig param: {}", JSON.toJSONString(feeConfigBeanList), e);
        }
        return -1;
    }
}
