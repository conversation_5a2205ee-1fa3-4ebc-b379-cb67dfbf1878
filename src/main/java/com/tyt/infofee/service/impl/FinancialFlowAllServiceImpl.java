package com.tyt.infofee.service.impl;

import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;
import com.tyt.dao.base.BaseDao;
import com.tyt.infofee.bean.FinancialFlowAllQueryBean;
import com.tyt.infofee.service.FinancialFlowAllService;
import com.tyt.model.FinancialFlow;
import com.tyt.model.PageBean;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;
import com.tyt.util.EmptyUtil;

@Service("financialFlowAllService")
public class FinancialFlowAllServiceImpl extends BaseServiceImpl<FinancialFlow, Long> implements FinancialFlowAllService{
		@Resource(name = "financialFlowAllDao")
		public void setBaseDao(BaseDao<FinancialFlow, Long> financialFlowAllDao) {
			super.setBaseDao(financialFlowAllDao);
		}
		@Resource(name="userService")
		private UserService userService;
		
		@SuppressWarnings("deprecation")
		@Override
		public List<FinancialFlowAllQueryBean> getAllFinancialFlowList(PageBean pageBean, FinancialFlowAllQueryBean conditionBean) throws Exception {
			// 查询流水表记录总条数
			StringBuffer countBuffer = new StringBuffer("SELECT count(*) FROM tyt_financial_flow f left join tyt_user u on u.id=f.user_id where 1=1");
			StringBuffer conditionBuffer = new StringBuffer();
			List<Object> params = new ArrayList<Object>();
			
			if (StringUtils.isNotEmpty(conditionBean.getCellPhone())) {
				Long userId = userService.getUserIdByCellPhone(conditionBean.getCellPhone());
				if (userId==null) {
					return null;
				}else {
					if (objectIsNotEmpty(conditionBean.getUserId()) && !userId.toString().equals(conditionBean.getUserId().toString())) {
						return null;
					}
					conditionBuffer.append(" and f.user_id=?");
					params.add(userId);
				}
			}
			if (objectIsNotEmpty(conditionBean.getUserId()) && !StringUtils.isNotEmpty(conditionBean.getCellPhone())) {
				conditionBuffer.append(" and f.user_id=?");
				params.add(conditionBean.getUserId());
			}
			if (objectIsNotEmpty(conditionBean.getFlowType())) {
				conditionBuffer.append(" and f.flow_type=?");
				params.add(conditionBean.getFlowType());
			}
			
			if (objectIsNotEmpty(conditionBean.getStartTime())) {
				conditionBuffer.append(" and f.create_time>=? ");
				params.add(conditionBean.getStartTime());
			}
			if (objectIsNotEmpty(conditionBean.getEndTime())) {
				conditionBuffer.append(" and f.create_time<=? ");
				params.add(conditionBean.getEndTime() + " 23:59:59");
			}
			if (objectIsNotEmpty(conditionBean.getTransfterBank())) {
				conditionBuffer.append(" and f.transfter_bank=?");
				params.add(conditionBean.getTransfterBank());
			}
			if (objectIsNotEmpty(conditionBean.getMaxMoneyAmount())) {
				conditionBuffer.append(" and abs(f.money_amount)<=?");
				params.add(conditionBean.getMaxMoneyAmount()*100);
			}
			if (objectIsNotEmpty(conditionBean.getMinMoneyAmount())) {
				conditionBuffer.append(" and abs(f.money_amount)>=?");
				params.add(conditionBean.getMinMoneyAmount()*100);
			}
            if (objectIsNotEmpty(conditionBean.getWaybillNumber())) {
                conditionBuffer.append(" and f.waybill_number=?");
                params.add(conditionBean.getWaybillNumber());
            }
			if (objectIsNotEmpty(conditionBean.getAccountId())) {
				conditionBuffer.append(" and f.account_id=?");
				params.add(conditionBean.getAccountId());
			}
			BigInteger count = this.getBaseDao().query(countBuffer.append(conditionBuffer).toString(), params.toArray());
			if (count == null || count.longValue() <= 0) {
				return null;// 没有记录，直接返回null
			}
			// 查询记录详情
			int currentPage = pageBean.getCurrentPage();// 当前页数
			int pageSize = pageBean.getPageSize();// 页面的大小,默认为一页显示5条数据
			if (currentPage <= 0) {
				currentPage = 1;
			}
			if (pageSize <= 0) {
				pageSize = 50;
			}
			pageBean.setRowCount(count.longValue());
			// 查询记录详情
			StringBuffer selectBuffer = new StringBuffer("SELECT u.true_name trueName,u.cell_phone cellPhone,f.user_id userId,f.id,f.create_time createTime,f.flow_type flowType,f.transfter_bank transfterBank,f.waybill_number waybillNumber,f.money_amount moneyAmount"
					+  ",f.bank_name bankName,f.before_reaminging beforeRemain,f.after_remaining afterRemain ,f.orde_number ordeNumber,f.apply_id applyId,f.trade_id tradeId ,f.account_id accountId, " + 
					"(SELECT ttw.`ts_id` FROM tyt.`tyt_transport_waybill` ttw WHERE ttw.`ts_order_no`=f.`waybill_number`) 'tsId' "
					+ " FROM tyt_financial_flow f left join tyt_user u on u.id=f.user_id where 1=1");
			Map<String, Type> scalarMap = new HashMap<String, Type>();
			scalarMap.put("id", Hibernate.LONG);
			scalarMap.put("trueName", Hibernate.STRING);
			scalarMap.put("cellPhone", Hibernate.STRING);
			scalarMap.put("createTime", Hibernate.TIMESTAMP);
			scalarMap.put("flowType", Hibernate.INTEGER);
			scalarMap.put("userId", Hibernate.INTEGER);
			scalarMap.put("transfterBank", Hibernate.INTEGER);
			scalarMap.put("waybillNumber", Hibernate.STRING);
			scalarMap.put("moneyAmount", Hibernate.STRING);
			scalarMap.put("bankName", Hibernate.STRING);
			scalarMap.put("beforeRemain", Hibernate.LONG);
			scalarMap.put("afterRemain", Hibernate.LONG);
			scalarMap.put("ordeNumber", Hibernate.STRING);
			scalarMap.put("applyId", Hibernate.INTEGER);
			scalarMap.put("tradeId", Hibernate.INTEGER);
			scalarMap.put("accountId", Hibernate.INTEGER);
			scalarMap.put("tsId", Hibernate.STRING);
			return this.getBaseDao().search(selectBuffer.append(conditionBuffer).append(" order by f.id desc").toString(), scalarMap, FinancialFlowAllQueryBean.class, params.toArray(), currentPage, pageSize);
		}
		private boolean objectIsNotEmpty(Object obj) {
			return EmptyUtil.objectIsNotEmpty(obj);
		}
		@Override
		public void objectUserToCvsString(StringBuilder content,
				List<FinancialFlowAllQueryBean> financialFlowList) {
		String CSV_RN = "\r\n";// 列分隔符
		for (FinancialFlowAllQueryBean queryBean : financialFlowList) {
			// ID,用户ID,手机号,流水生成日期,交易类型,转账银行,运单号,金额(元),银行名称,
			// 交易前金额(元),交易后金额(元),订单号,货源ID,提款申请ID,订单ID,账户ID
			content.append(queryBean.getId()).append(",");
			content.append(queryBean.getUserId()).append(",");
            content.append(queryBean.getAccountId()).append(",");
//			content.append(queryBean.getCellPhone()).append(",");
			content.append(queryBean.getCreateTime()).append(",");
			String flowTypeName = "";
			if (queryBean.getFlowType() != null) {
				switch (queryBean.getFlowType().intValue()) {
				case 0:
					flowTypeName = "用户转帐";
					break;
				case 1:
					flowTypeName = "支付信息费";
					break;
				case 2:
					flowTypeName = "收到信息费";
					break;
				case 3:
					flowTypeName = "退回信息费";
					break;
				case 4:
					flowTypeName = "转出信息费";
					break;
				case 6:
					flowTypeName = "提现";
					break;
				case 7:
					flowTypeName = "异常上报";
					break;
				case 8:
					flowTypeName = "支付手续费";
					break;
				case 9:
					flowTypeName = "转入会员费";
					break;
				case 10:
					flowTypeName = "支付会员费";
					break;
				case 11:
				    flowTypeName = "好友推荐奖励收益";
				    break;
				case 100:
				     flowTypeName = "冻结信息费";
                     break;
                case 110:
                    flowTypeName = "平台入账账户流水";
                    break;
                case 120:
                    flowTypeName = "平台手续费流水";
                    break;
                case 130:
                    flowTypeName = "平台出账账户流水";
                    break;
                case 140:
                    flowTypeName = "平台余额账户流水";
                    break;
				}
			}
			content.append(flowTypeName).append(",");
			String transferBankName = "";
			if (queryBean.getTransfterBank() != null) {
				switch (queryBean.getTransfterBank().intValue()) {
				case 1:
					transferBankName = "支付宝";
					break;
				case 2:
					transferBankName = "微信";
					break;
				case 3:
					transferBankName = "易宝";
					break;
				}
			}
			content.append(transferBankName).append(",");
			content.append(StringUtils.isEmpty(queryBean.getWaybillNumber()) ? "" : queryBean.getWaybillNumber()).append(",");
			DecimalFormat df = new DecimalFormat("0.00");
			df.setRoundingMode(RoundingMode.HALF_UP);
			String moneyAmount = "";
			if (queryBean.getMoneyAmount() != null) {
			    moneyAmount = df.format(Double.valueOf(queryBean.getMoneyAmount()) / 100.0);
			}
			content.append(moneyAmount).append(",");
			content.append(StringUtils.isEmpty(queryBean.getBankName()) ? "" : queryBean.getBankName()).append(",");
			String beforeRemain = "";
			if (queryBean.getBeforeRemain() != null) {
				beforeRemain = df.format(Double.valueOf(queryBean.getBeforeRemain()) / 100.0);
			}
			content.append(beforeRemain).append(",");
			String afterRemain = "";
			if (queryBean.getAfterRemain() != null) {
				afterRemain = df.format(Double.valueOf(queryBean.getAfterRemain()) / 100.0);
			}
			content.append(afterRemain).append(",");
			content.append(StringUtils.isEmpty(queryBean.getOrdeNumber()) ? "" : "订单号:" + queryBean.getOrdeNumber()).append(",");
			content.append(StringUtils.isEmpty(queryBean.getTsId()) ? "" : queryBean.getTsId()).append(",");
			content.append(queryBean.getApplyId() == null ? "" : queryBean.getApplyId()).append(",");
			content.append(queryBean.getTradeId() == null ? "" : queryBean.getTradeId()).append(",");
			content.append(queryBean.getAccountId() == null ? "" : queryBean.getAccountId()).append(",");
			content.append(CSV_RN);
		}
	}
}
