package com.tyt.infofee.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.tyt.abtest.service.PromoCouponWriteOffService;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.common.service.TytSequenceService;
import com.tyt.dao.base.BaseDao;
import com.tyt.dispatch.service.TytDispatchCompanyService;
import com.tyt.enums.*;
import com.tyt.equipment.service.TytTransportWaybillExService;
import com.tyt.goods.bean.FinancialData;
import com.tyt.infofee.bean.InfoFeeOrderExport;
import com.tyt.infofee.bean.TransportOrderQueryBean;
import com.tyt.infofee.bean.TransportOrderResultBean;
import com.tyt.infofee.enums.OrderDataType;
import com.tyt.infofee.enums.RefundFlagEnum;
import com.tyt.infofee.service.FinancialFlowService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TytFinancialInOutAccountService;
import com.tyt.invoice.enums.OrderStatusEnum;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.*;
import com.tyt.manager.mapper.base.TytConfirmOrderMapper;
import com.tyt.manager.mapper.base.TytTransportOrderSnapshotMapper;
import com.tyt.manager.mapper.base.TytTransportTechnicalOrderMapper;
import com.tyt.model.*;
import com.tyt.payment.service.OrderService;
import com.tyt.peopleorders.bean.CreateOrderBean;
import com.tyt.peopleorders.bean.IntentionInfoBean;
import com.tyt.peopleorders.service.EcaContractService;
import com.tyt.peopleorders.service.TransportMtService;
import com.tyt.peopleorders.service.TytCarOwnerIntentionService;
import com.tyt.schedule.service.ScheduleTravelService;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.car.CarService;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.transport.TransportBusiness;
import com.tyt.service.transport.TransportMainService;
import com.tyt.service.transport.TransportService;
import com.tyt.service.user.UserService;
import com.tyt.techRefund.service.TechRefundService;
import com.tyt.util.*;
import com.tyt.util.httpclient.HttpClient;
import com.tyt.util.httpclient.ResponseStatus;
import com.tyt.web.back.internal.bean.OrdersQueryBean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpException;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * User: Administrator Date: 13-11-10 Time: 下午5:10
 */
@Service("transportOrdersService")
public class TransportOrdersServiceImpl extends
        BaseServiceImpl<TytTransportOrders, Long> implements
        TransportOrdersService {
    private static final Logger logger = LoggerFactory.getLogger(TransportOrdersServiceImpl.class);
    private static final String REFUND_REASON = "refund_reason";

	@Resource(name = "tytBubbleService")
	TytBubbleService tytBubbleService;
	
	@Resource(name = "tytSequenceService")
	TytSequenceService tytSequenceService;
	

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Autowired
	private PromoCouponWriteOffService promoCouponWriteOffService;

	@Autowired
	private TransportMtService mtService;

	@Autowired
	private FinancialFlowService financialFlowService;

	@Autowired
	private TytFinancialInOutAccountService inOutAccountService;

	@Autowired
	private TransportService transportService;

	@Autowired
	private TytCarOwnerIntentionService carOwnerIntentionService;

	@Autowired
	private TransportOrdersService transportOrdersService;

	@Autowired
	private TransportMtService transportMtService;

	@Resource(name = "transportBusiness")
	private TransportBusiness transportBusiness;

	@Autowired
	private TytConfigService tytConfigService;

	@Resource(name="userService")
	private UserService userService;

	@Resource(name = "carService")
	private CarService carService;

	// 合同相关
	@Resource(name = "ecaContractService")
	private EcaContractService ecaContractService;

	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;

	@Autowired
	private ScheduleTravelService scheduleTravelService;

	@Autowired
	private OrderService orderService;

    @Autowired
    private TytTransportOrderSnapshotMapper transportOrderSnapshotMapper;

	@Autowired
	private TechRefundService techRefundService;

	@Autowired
	private TytTransportWaybillExService transportWaybillExService;

	@Autowired
	private TytDispatchCompanyService tytDispatchCompanyService;

	@Autowired
	private TytConfirmOrderMapper tytConfirmOrderMapper;

	@Autowired
	private TytTransportTechnicalOrderMapper tytTransportTechnicalOrderMapper;

	/**
	 * 代调钱包
	 */
	private static final String cashBackDdPayerId = AppConfig.getProperty("group.cashBack.dd.payerId");

	/**
	 * 抽佣钱包
	 */
	private static final String cashBackCyPayerId = AppConfig.getProperty("group.cashBack.cy.payerId");

	@Override
	@Resource(name = "transportOrdersDao")
	public void setBaseDao(
			BaseDao<TytTransportOrders, Long> transportOrdersDao) {
		super.setBaseDao(transportOrdersDao);
	}


	@SuppressWarnings("deprecation")
	@Override
	public List<TransportOrderResultBean> getOrderList(PageBean pageBean,
			TransportOrderQueryBean conditionBean)throws Exception {

		String uploadCellphone = conditionBean.getUploadCellphone();

		Long conditionUserId = null;
		if(StringUtils.isNotBlank(uploadCellphone)){
			conditionUserId = userService.getIdByCellPhone(uploadCellphone);
			if(conditionUserId == null){
				return new ArrayList<>();
			}
		}

		//查询条件及参数值
		StringBuffer conditionBuffer=new StringBuffer();
		List<Object> conditionParams=new ArrayList<Object>();
		if(objectIsNotEmpty(conditionBean.getStartCreateTime())){
			conditionBuffer.append(" and o.create_time>=?");
			conditionParams.add(conditionBean.getStartCreateTime());
		}
		if(objectIsNotEmpty(conditionBean.getEndCreateTime())){
			conditionBuffer.append(" and o.create_time<?");
			conditionParams.add(TimeUtil.addDay(conditionBean.getEndCreateTime(), 1) );
		}
		//技术服务费退款开始时间
		if(objectIsNotEmpty(conditionBean.getTechRefundStartTime())){
			conditionBuffer.append(" and tto.refund_time >= ?");
			conditionParams.add(conditionBean.getTechRefundStartTime());
		}
		//技术服务费退款结束时间
		if(objectIsNotEmpty(conditionBean.getTechRefundEndTime())){
			conditionBuffer.append(" and tto.refund_time < ?");
			conditionParams.add(TimeUtil.addDay(conditionBean.getTechRefundEndTime(), 1));
		}
		//折扣金额最小值
		if(objectIsNotEmpty(conditionBean.getDiscountAmountMin())){
			conditionBuffer.append(" and (o.total_order_amount - o.pay_amount)/100 >= ?");
			conditionParams.add(Integer.parseInt(conditionBean.getDiscountAmountMin()));
		}
        //折扣金额最大值
		if(objectIsNotEmpty(conditionBean.getDiscountAmountMax())){
			conditionBuffer.append(" and (o.total_order_amount - o.pay_amount)/100 <= ?");
			conditionParams.add(Integer.parseInt(conditionBean.getDiscountAmountMax()));
		}

		if(objectIsNotEmpty(conditionBean.getCarriageFeeMin())){
			conditionBuffer.append(" and o.carriage_fee>=?");
			conditionParams.add(conditionBean.getCarriageFeeMin());
		}
		if(objectIsNotEmpty(conditionBean.getCarriageFeeMax())){
			conditionBuffer.append(" and o.carriage_fee<=?");
			conditionParams.add(conditionBean.getCarriageFeeMax());
		}


		if(objectIsNotEmpty(conditionBean.getPayAmountMin())){
			conditionBuffer.append(" and o.pay_amount>=?");
			conditionParams.add(conditionBean.getPayAmountMin().multiply(new BigDecimal("100.00")));
		}
		if(objectIsNotEmpty(conditionBean.getPayAmountMax())){
			conditionBuffer.append(" and o.pay_amount<=?");
			conditionParams.add(conditionBean.getPayAmountMax().multiply(new BigDecimal("100.00")));
		}

		if(objectIsNotEmpty(conditionBean.getTecFeeMin())){
			conditionBuffer.append(" and o.tec_service_fee >= ?");
			conditionParams.add(conditionBean.getTecFeeMin().multiply(new BigDecimal("100.00")));
		}
		if(objectIsNotEmpty(conditionBean.getTecFeeMax())){
			conditionBuffer.append(" and o.tec_service_fee <= ?");
			conditionParams.add(conditionBean.getTecFeeMax().multiply(new BigDecimal("100.00")));
		}

		if(objectIsNotEmpty(conditionBean.getPayCellPhone())){
			/*conditionBuffer.append(" and o.pay_cell_phone=?");
			conditionParams.add(conditionBean.getPayCellPhone());*/
			// 改为通过用户id查询
			Long payUserId = userService.getIdByCellPhone(conditionBean.getPayCellPhone());
			conditionBuffer.append(" and o.pay_user_id=?");
			conditionParams.add(payUserId);
		}
		if(objectIsNotEmpty(conditionBean.getPayType())){
		    if ("1".equals(conditionBean.getPayType())){
                conditionBuffer.append(" and o.pay_sub_channel like ?");
                conditionParams.add("ALIPAY%");
            }else if ("2".equals(conditionBean.getPayType())){
                conditionBuffer.append(" and o.pay_sub_channel like ?");
                conditionParams.add("AGRT%");
            }else if ("3".equals(conditionBean.getPayType())){
                conditionBuffer.append(" and o.pay_sub_channel like ?");
                conditionParams.add("WECHAT%");
            }else if ("4".equals(conditionBean.getPayType())){
                conditionBuffer.append(" and o.pay_type=?");
                conditionParams.add(conditionBean.getPayType());
            }else if ("5".equals(conditionBean.getPayType())){
                conditionBuffer.append(" and o.pay_sub_channel=?");
                conditionParams.add("BALANCE");
            }
		}
		if(objectIsNotEmpty(conditionBean.getRobStatus())){
			conditionBuffer.append(" and o.rob_status=?");
			conditionParams.add(conditionBean.getRobStatus());			
		}
		if(objectIsNotEmpty(conditionBean.getPublishType())){
			conditionBuffer.append(" and ts.publish_type=?");
			conditionParams.add(conditionBean.getPublishType());
		}
		//信息费状态：10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
		if(objectIsNotEmpty(conditionBean.getCostStatus())){
			//update by sissy  151 代表手动点击延迟付款  211 代表车方拒绝退款延迟付款
			if("151".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status=15 and o.delay_status=1 ");
			}else if("211".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status in (15,21) and o.delay_status=2 ");
			}else if("15".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status=15 and o.delay_status=0 ");
			}else if("21".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status=21 and o.delay_status=0 ");
			}else{
				conditionBuffer.append(" and o.cost_status=?");
				conditionParams.add(conditionBean.getCostStatus());
			}
		}
		if(objectIsNotEmpty(conditionBean.getTsOrderNo())){
			conditionBuffer.append(" and o.ts_order_no=?");
			conditionParams.add(conditionBean.getTsOrderNo());	
		}
		if(objectIsNotEmpty(conditionBean.getThirdPartyPlatformOrderNo())){
			conditionBuffer.append(" and o.thirdparty_platform_order_no=?");
			conditionParams.add(conditionBean.getThirdPartyPlatformOrderNo());
		}
		if(objectIsNotEmpty(conditionBean.getTsId())){
			conditionBuffer.append(" and o.ts_id=?");
			conditionParams.add(conditionBean.getTsId());
		}
		if(conditionUserId != null){
			conditionBuffer.append(" and o.user_id=? ");
			conditionParams.add(conditionUserId);
		}
		//支付状态 0待支付 1支付失败 2支付成功
		if(objectIsNotEmpty(conditionBean.getPayStatus())){
			conditionBuffer.append(" and o.pay_status=?");
			conditionParams.add(conditionBean.getPayStatus());
		}
		//退款状态 1.退款中 2.退款成功 3.退款失败
		if(objectIsNotEmpty(conditionBean.getRefundStatus())){
			conditionBuffer.append(" and o.refund_status=?");
			conditionParams.add(conditionBean.getRefundStatus());
		}
		//订单ID
		if(objectIsNotEmpty(conditionBean.getId())){
			conditionBuffer.append(" and o.id=?");
			conditionParams.add(conditionBean.getId());
		}

		//兼容处理接单状态查询
		if (objectIsNotEmpty(conditionBean.getSeckillGoods())&&1==conditionBean.getSeckillGoods()) {
			if(objectIsNotEmpty(conditionBean.getOrderAllocateState())){
				conditionBuffer.append(" and ts.seckill_goods=1 and ts.order_allocate_state=? ");
				conditionParams.add(conditionBean.getOrderAllocateState());
			}
		}
		//货源类型
		if (objectIsNotEmpty(conditionBean.getExcellentGoods())) {
			conditionBuffer.append(" and ts.excellent_goods=?");
			conditionParams.add(conditionBean.getExcellentGoods());
		}
		//是否代调
		if (objectIsNotEmpty(conditionBean.getIsDispatch())) {
			//代调
			if (1 == conditionBean.getIsDispatch()) {
				conditionBuffer.append(" and ( ts.source_type=2 or ts.source_type=5 or  (ts.source_type=1 and o.user_id in  (select distinct user_id from tyt_dispatch_company where is_valid = 1)) )");
			} else {
				conditionBuffer.append(" and !( ts.source_type=2 or ts.source_type=5 or  (ts.source_type=1 and o.user_id in  (select distinct user_id from tyt_dispatch_company where is_valid = 1)) )");
			}
		}
		//货源来源
		if (objectIsNotEmpty(conditionBean.getTsourceType()) && conditionBean.getTsourceType() != 0) {
			//运满满
			Integer tsourceType = conditionBean.getTsourceType();
			//调度客服
			if (TransportSourceTypeEnum.DISPATCH.getCode().equals(tsourceType)) {
				conditionBuffer.append(" and  ts.source_type=2  ");
				//运满满货源
			} else if (TransportSourceTypeEnum.YMM.getCode().equals(tsourceType)) {
				conditionBuffer.append(" and  ts.source_type=4  ");
				//	5宏信货源
			} else if (TransportSourceTypeEnum.HX.getCode().equals(tsourceType)) {
				conditionBuffer.append(" and  ts.source_type=5  ");
			} else {
				conditionBuffer.append(" and  (ts.source_type not in (2,4,5) or ts.source_type is  null) ");
			}
		}
		//是否会员
		if(objectIsNotEmpty(conditionBean.getIsMember())){
			if(UserPermissionStatusEnum.VALID.getCode().equals(conditionBean.getIsMember())){
				conditionBuffer.append(" and  occ.is_member=1 ");
			}else{
				conditionBuffer.append(" and  (occ.is_member != 1  or occ.is_member is null ) ");
			}
		}
		//是否是S城直客（0:否 1:是）
		if (objectIsNotEmpty(conditionBean.getIsScityDirectCustomer())) {
			conditionBuffer.append(" and ts.is_scity_direct_customer=?");
			conditionParams.add(conditionBean.getIsScityDirectCustomer());
		}

		logger.info("getOrderList conditionBean is 【{}】conditionParams is 【{}】",JSON.toJSONString(conditionBean), JSON.toJSONString(conditionParams));
		//查询财务数据的sql语句
		StringBuffer financialSql = new StringBuffer("select count(*) paySuccessCount,sum(IFNULL(o.pay_fee_amount,0.00))/100 payFeeAmount,sum(IFNULL(o.carriage_fee,0.00)) payCarriageFee,sum(IFNULL(o.pay_amount,0.00))/100 paySuccessAmount from tyt_transport_orders o  left join tyt_transport_order_snapshot ts on  ts.order_id = o.id " +
				" left join tyt_transport_technical_order tto on tto.technical_service_no = o.technical_service_no " +
				" left join tyt_order_carry_point occ on o.id = occ.order_id where 1=1");
		financialSql.append(conditionBuffer);
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("paySuccessCount",Hibernate.INTEGER);
		scalarMap.put("paySuccessAmount",Hibernate.BIG_DECIMAL);
		scalarMap.put("payFeeAmount",Hibernate.BIG_DECIMAL);
		scalarMap.put("payCarriageFee",Hibernate.BIG_DECIMAL);
		List<FinancialData> financialDataList = this.getBaseDao().search(financialSql.toString(), scalarMap, FinancialData.class, conditionParams.toArray());
		if(financialDataList != null && financialDataList.size()>0){
			FinancialData financialData = financialDataList.get(0);
			conditionBean.setFinancialData(financialData);
		}

		StringBuffer countBuffer=new StringBuffer("select count(*) from tyt_transport_orders o  left join tyt_transport_order_snapshot ts on  ts.order_id = o.id left join tyt_transport_technical_order tto on tto.technical_service_no = o.technical_service_no " +
				"  left join tyt_order_carry_point occ on o.id = occ.order_id  where 1=1");
		countBuffer.append(conditionBuffer);

		BigInteger total=this.getBaseDao().query(countBuffer.toString(), conditionParams.toArray());
        if(total!=null&&total.longValue()>0L){
        	pageBean.setRowCount(total.longValue());
        	StringBuffer selectBuffer=new StringBuffer(
        			"select o.id,o.task_content taskContent,o.ts_order_no tsOrderNo,o.thirdparty_platform_order_no thirdPartyPlatformOrderNo,o.pay_cell_phone payCellPhone,"
        			+ "o.rob_status robStatus,o.pay_status payStatus,o.delay_status delayStatus,o.create_time createTime,o.pay_type payType,"
        			+ "o.carriage_fee carriageFee,IFNULL(o.total_order_amount,0) totalOrderAmount,IFNULL(o.coupon_amount,0) couponAmount,o.pay_amount payAmount,IFNULL(o.refund_amount,0) refundAmount,IFNULL(o.car_amount,0) carAmount,IFNULL(o.goods_amount,0) goodsAmount,o.pay_fee_amount payFeeAmount,o.ts_id tsId,o.cost_status costStatus,"
					+ "o.upload_cellphone uploadCellphone,o.user_id userId,o.pay_user_id payUserId,o.op_id opId,o.op_name opName,o.pay_order_no payOrderNo, "
					+ "o.pay_sub_channel paySubChannel,ts.publish_type publishType,"
					+ "o.refund_arrival_time refundArrivalTime,o.refund_status refundStatus,o.refund_err_msg refundErrMsg, refund_reason as refundReason,o.refund_flag refundFlag,"
					+ "o.car_service_amount carServiceAmount,o.mtime mtime,o.order_new_status orderNewStatus,o.cancel_status cancelStatus,o.delay_refund_status delayRefundStatus,o.de_refund_dueDate deRefundDueDate,"
					+ "tto.technical_service_fee tecServiceFee,tto.status tecFeePayStatus,tto.ctime tecFeePayTime,IFNULL(occ.is_member,0) isMember,"
					+ "tto.refund_amount tecFeeRefundAmount,tto.refund_status tecFeeRefundStatus,tto.refund_time tecFeeRefundTime,"
					+ "ts.is_scity_direct_customer isScityDirectCustomer,"
					+ "ts.seckill_goods seckillGoods,"
					+ "ts.order_allocate_state orderAllocateState,"
					+ "tto.refund_type refundType "
					+ " from tyt_transport_orders o left join tyt_transport_order_snapshot ts on ts.order_id = o.id "
					+ " left join tyt_transport_technical_order tto on tto.technical_service_no = o.technical_service_no "
					+ " left join tyt_order_carry_point occ on o.id = occ.order_id where 1=1 ");
        	selectBuffer.append(conditionBuffer).append(" order by o.id desc");
        	Map<String,Type> typeMap=new HashMap<String,Type>();
        	typeMap.put("id", Hibernate.LONG);
        	typeMap.put("taskContent", Hibernate.STRING);
        	typeMap.put("tsOrderNo", Hibernate.STRING);
			typeMap.put("thirdPartyPlatformOrderNo", Hibernate.STRING);
        	typeMap.put("payCellPhone", Hibernate.STRING);
        	typeMap.put("robStatus", Hibernate.STRING);
        	typeMap.put("payStatus", Hibernate.STRING);
        	typeMap.put("createTime", Hibernate.TIMESTAMP);
        	typeMap.put("payType", Hibernate.STRING);
			typeMap.put("carriageFee", Hibernate.LONG);
        	typeMap.put("payAmount", Hibernate.LONG);
			typeMap.put("couponAmount", Hibernate.LONG);
			typeMap.put("totalOrderAmount", Hibernate.LONG);
        	typeMap.put("payFeeAmount", Hibernate.LONG);
			typeMap.put("refundAmount", Hibernate.LONG);
			typeMap.put("carAmount", Hibernate.LONG);
			typeMap.put("goodsAmount", Hibernate.LONG);
        	typeMap.put("tsId", Hibernate.LONG);
        	//信息费状态
			typeMap.put("costStatus", Hibernate.STRING);
			//发货方手机号
			typeMap.put("uploadCellphone", Hibernate.STRING);
			//发货方手机号
			typeMap.put("userId", Hibernate.LONG);
			//发货方手机号
			typeMap.put("payUserId", Hibernate.LONG);
			//操作人Id
			typeMap.put("opId", Hibernate.LONG);
			//操作人姓名
			typeMap.put("opName", Hibernate.STRING);
			//支付订单号
			typeMap.put("payOrderNo", Hibernate.STRING);
			//支付子渠道
            typeMap.put("paySubChannel", Hibernate.STRING);
			//货源类型 1电议 2一口价
			typeMap.put("publishType", Hibernate.STRING);
            //退款到账时间
			typeMap.put("refundArrivalTime", Hibernate.TIMESTAMP);
			//退款状态 1.退款中 2.退款成功 3.退款失败
			typeMap.put("refundStatus", Hibernate.STRING);
			//退款异常信息
			typeMap.put("refundErrMsg", Hibernate.STRING);
			//退款异常信息
			typeMap.put("delayStatus", Hibernate.INTEGER);
			// 退款原因
			typeMap.put("refundReason", Hibernate.STRING);

            typeMap.put("refundFlag", Hibernate.INTEGER);
			//服务费最终分配-车方
			typeMap.put("carServiceAmount", Hibernate.LONG);
			//修改时间
			typeMap.put("mtime", Hibernate.TIMESTAMP);
			//技术服务费 单位分
			typeMap.put("tecServiceFee", Hibernate.LONG);
			//技术服务费支付状态 0待支付 1支付成功 2支付失败
			typeMap.put("tecFeePayStatus", Hibernate.INTEGER);
			//技术服务费支付时间
			typeMap.put("tecFeePayTime", Hibernate.TIMESTAMP);
			//技术服务费退还金额
			typeMap.put("tecFeeRefundAmount", Hibernate.LONG);
			//技术服务费退款状态
			typeMap.put("tecFeeRefundStatus", Hibernate.INTEGER);
			//技术服务费退款时间
			typeMap.put("tecFeeRefundTime", Hibernate.TIMESTAMP);
			//是否会员
			typeMap.put("isMember", Hibernate.INTEGER);
            //订单状态：5.待接单 10.待签署 15.待装货 20.待卸货/待收货 25.待收运费/待付运费 30.已完成 35.已取消
			typeMap.put("orderNewStatus", Hibernate.INTEGER);
			//取消状态  0 未取消 1取消中 待车主处理 2 车主同意取消  3货主拒绝取消  4 系统拒绝取消
			typeMap.put("cancelStatus", Hibernate.INTEGER);
			//延迟退款状态 0 未延迟退款 1 延迟退款
			typeMap.put("delayRefundStatus", Hibernate.INTEGER);
			//预计退款到账日期
			typeMap.put("deRefundDueDate", Hibernate.TIMESTAMP);
            //是否是S城直客（0:否 1:是）
			typeMap.put("isScityDirectCustomer", Hibernate.INTEGER);
			//是否是秒抢货源：1是0否
			typeMap.put("seckillGoods", Hibernate.INTEGER);
			//订单分配状态 0初始化 1分配成功 2分配失
			typeMap.put("orderAllocateState", Hibernate.INTEGER);
			//退款类型：1.业务退款 2.线下退款
			typeMap.put("refundType", Hibernate.INTEGER);

			List <TransportOrderResultBean> transportOrderList = this.getBaseDao().search(selectBuffer.toString(), typeMap, TransportOrderResultBean.class, conditionParams.toArray(), pageBean.getCurrentPage(), pageBean.getPageSize());
			//获取对应的最近一笔支付订单的订单号，设置支付订单号，给前端使用
			if(CollectionUtils.isNotEmpty(transportOrderList)){
				for (TransportOrderResultBean transportOrderResultBean : transportOrderList) {
					//关联订单号
					String payOrderNo = transportOrderResultBean.getPayOrderNo();
					//支付人手机号
					String payCellPhone = transportOrderResultBean.getPayCellPhone();
                    //根据关联订单号和手机号查询支付订单信息
					Order order = orderService.getByPayOrderAndCellPhone(payOrderNo, payCellPhone);
					if(order != null){
						//支付订单号
						String orderId = order.getOrderId();
						transportOrderResultBean.setOrderId(orderId);
					}
					if ("4".equals(transportOrderResultBean.getPayType())){
                        transportOrderResultBean.setPayChannelName("线下支付");
                    }else{
                        if (StringUtils.isNotBlank(transportOrderResultBean.getPaySubChannel())){
                            if (transportOrderResultBean.getPaySubChannel().startsWith("WECHAT")){
                                transportOrderResultBean.setPayChannelName("微信");
                            }else if (transportOrderResultBean.getPaySubChannel().startsWith("ALIPAY")){
                                transportOrderResultBean.setPayChannelName("支付宝");
                            }else if (transportOrderResultBean.getPaySubChannel().startsWith("AGRT")){
                                transportOrderResultBean.setPayChannelName("银行卡");
                            }else if (transportOrderResultBean.getPaySubChannel().startsWith("BALANCE")){
                                transportOrderResultBean.setPayChannelName("余额");
                            }
                        }
                    }

					TransportMain transport =
							transportMainService.getTransportMainForId(transportOrderResultBean.getTsId());
					if (transport != null) {
						transportOrderResultBean.setExcellentGoods(transport.getExcellentGoods());
						transportOrderResultBean.setSourceType(transport.getSourceType());
					}
					//当优惠券金额大于0时 查看获取用户优惠券Id
					if(transportOrderResultBean.getCouponAmount()!=null&&transportOrderResultBean.getCouponAmount()>0){
						PromoCouponWriteoff couponWriteOff = promoCouponWriteOffService.getCouponWriteOffByOrderId(transportOrderResultBean.getId().toString());
						if(couponWriteOff!=null){
							transportOrderResultBean.setCouponId(couponWriteOff.getCouponId().longValue());
						}
					}


					Long dispatchRefundAmount = 0L;
					Long exRefundAmount = 0L;
					Long completeRefundAmount = 0L;
					Long offlineRefundAmount = 0L;
					//技术服务费退款状态 0未退款 1.退款中 2.退款成功 3.退款失败
					Integer tecFeeRefundStatus = 0;
					//技术服务费退款时间
					Date tecFeeRefundTime = null;

					//订金状态
					String costStatus = transportOrderResultBean.getCostStatus();
                    //退款类型：1.业务退款 2.线下退款
					Integer refundType = transportOrderResultBean.getRefundType();
					if(StringUtils.isNotBlank(costStatus)){
                        //已退款
						if(35 == Integer.parseInt(costStatus) && refundType != null && 1 == refundType){
							dispatchRefundAmount = transportOrderResultBean.getTecFeeRefundAmount();
							if(dispatchRefundAmount == null){
								dispatchRefundAmount = 0L;
							}else{
								tecFeeRefundStatus = transportOrderResultBean.getTecFeeRefundStatus();
								tecFeeRefundTime = transportOrderResultBean.getTecFeeRefundTime();
							}
						}
						if(50 == Integer.parseInt(costStatus)){ //异常上报处理完成
							exRefundAmount = transportOrderResultBean.getCarServiceAmount();
							if(exRefundAmount == null){
								exRefundAmount = 0L;
							}else{
								tecFeeRefundStatus = 2;
								tecFeeRefundTime = transportOrderResultBean.getMtime();
							}

						}
                        //如果是线下退款，返回技术服务费线下退款金额
						if(refundType != null && 2 == refundType){
							offlineRefundAmount = transportOrderResultBean.getTecFeeRefundAmount();
							if(offlineRefundAmount == null){
								offlineRefundAmount = 0L;
							}else{
								tecFeeRefundStatus = transportOrderResultBean.getTecFeeRefundStatus();
								tecFeeRefundTime = transportOrderResultBean.getTecFeeRefundTime();
							}
						}
						//完单退还技术服务费(投诉入口)
						TytTransportWaybillEx transportWaybillEx = transportWaybillExService.getTransportWaybillExByOrderIdAndOrderType(transportOrderResultBean.getId(), 1);
						if (Objects.nonNull(transportWaybillEx)){
							TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditByExId(transportWaybillEx.getId());
							if (Objects.nonNull(techRefundFinanceAudit)){
								BigDecimal refundAmount = techRefundFinanceAudit.getRefundAmount();
								if(refundAmount != null){
									completeRefundAmount = refundAmount.longValue();
									tecFeeRefundStatus = techRefundFinanceAudit.getRefundStatus();
									tecFeeRefundTime = techRefundFinanceAudit.getAuditTime();
								}
							}
						}
						//完单退还技术服务费(客服新增入口)
						TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditByOrderId(transportOrderResultBean.getId());
						if (Objects.nonNull(techRefundFinanceAudit)){
							BigDecimal refundAmount = techRefundFinanceAudit.getRefundAmount();
							if(refundAmount != null){
								completeRefundAmount = refundAmount.longValue();
								tecFeeRefundStatus = techRefundFinanceAudit.getRefundStatus();
								tecFeeRefundTime = techRefundFinanceAudit.getAuditTime();
							}
						}
					}
					Long refundCarServiceAmount = dispatchRefundAmount + exRefundAmount + completeRefundAmount + offlineRefundAmount;
					if(refundCarServiceAmount > 0L){
						String refundTecServiceFeeInfo = refundCarServiceAmount + "元（业务退回" + dispatchRefundAmount + "元）（异常上报退回" + exRefundAmount + "元）（完单后退回" + completeRefundAmount + "元）（线下退款"+ offlineRefundAmount + "元）";
						transportOrderResultBean.setRefundTecServiceFeeInfo(refundTecServiceFeeInfo);
						//技术服务费退款状态 0未退款 1.退款中 2.退款成功 3.退款失败
						transportOrderResultBean.setTecFeeRefundStatus(tecFeeRefundStatus);
						//技术服务费退款时间

						transportOrderResultBean.setTecFeeRefundTime(tecFeeRefundTime);
					}
					//折扣金额
					if(transportOrderResultBean.getTotalOrderAmount() != null
					&& transportOrderResultBean.getPayAmount() != null){
						transportOrderResultBean.setDiscountAmount(transportOrderResultBean.getTotalOrderAmount() / 100 - transportOrderResultBean.getPayAmount() / 100 + "");
					}

					String carStatusDesc = infoFeeStatusDesc(false, false, transportOrderResultBean.getRefundFlag(), String.valueOf(transportOrderResultBean.getCostStatus()),
							transportOrderResultBean.getDelayStatus(), transportOrderResultBean.getCancelStatus(), transportOrderResultBean.getDelayRefundStatus(), transportOrderResultBean.getDeRefundDueDate(),
							transportOrderResultBean.getOrderNewStatus(), OrderDataType.ORDER_LIST.getCode());

					String goodsStatusDesc = infoFeeStatusDesc(false, true, transportOrderResultBean.getRefundFlag(), String.valueOf(transportOrderResultBean.getCostStatus()),
							transportOrderResultBean.getDelayStatus(), transportOrderResultBean.getCancelStatus(), transportOrderResultBean.getDelayRefundStatus(), transportOrderResultBean.getDeRefundDueDate(),
							transportOrderResultBean.getOrderNewStatus(), OrderDataType.ORDER_LIST.getCode());


					String statusDesc = "车方：" + carStatusDesc + "\n货方：" + goodsStatusDesc;
					transportOrderResultBean.setStatusDesc(statusDesc);

					//如果订单状态为空或者退款中，则将退款金额字段设置为空，不显示
					if(StringUtils.isBlank(transportOrderResultBean.getRefundStatus())
					|| "1".equals(transportOrderResultBean.getRefundStatus())){
						transportOrderResultBean.setRefundAmount(null);
					}

					//订金状态（已退款，已打款，自动收款，异常完成） and 技术服务费>0，可以操作技术服务费线下退款
					if(StringUtils.isNotBlank(costStatus)
					&& ("35".equals(costStatus)
					|| "40".equals(costStatus)
					|| "45".equals(costStatus)
					|| "50".equals(costStatus))
					&& transportOrderResultBean.getTecServiceFee() != null
					&& transportOrderResultBean.getTecServiceFee() > 0L
					&& dispatchRefundAmount == 0L
					&& exRefundAmount == 0L
					&& completeRefundAmount == 0L){
						transportOrderResultBean.setOfflineRefundFlag(1);
					}else{
						transportOrderResultBean.setOfflineRefundFlag(0);
					}

				}
			}
			//todo 临时增加日志
			logger.info("getOrderList conditionBean: {} res:{}", JSON.toJSONString(conditionBean), JSON.toJSONString(transportOrderList));
			return transportOrderList;
        }else{
        	return null;
        }
		
	}


	public static String infoFeeStatusDesc(boolean isPc, boolean isOwner, Integer refundFlag,String costStatus,Integer delayStatus,Integer cancelStatus,
										   Integer delayRefundStatus,Date deRefundDueDate, Integer orderNewStatus, Integer orderDataType) {
		String today = TimeUtil.formatDateTime(new Date(System.currentTimeMillis()));
		//剩余自动结算天数=实际应到账时间-当前日期
		try {
			int tDay=0;
			if(null != deRefundDueDate){
				tDay = TimeUtil.getDays(today, TimeUtil.formatDateTime(deRefundDueDate));
			}
			if(isOwner && RefundFlagEnum.退还.getId().equals(refundFlag)){
				//货方退还
				switch (costStatus){
					case "10":
						return "待支付";
					case "15":
						if(1==cancelStatus){
							return"已取消等待车方确认";
						}
						if(delayRefundStatus==0){
							if(tDay>1){
								return "车方已支付订金";
							}else{
								return "车方已支付订金，即将自动退还";
							}
						}else{
							if(tDay>1){
								return"车方已支付订金";
							}else{
								return"车方已支付订金，即将自动退还";
							}
						}
					case "22":
						if(1==cancelStatus){
							return"已取消等待车方确认";
						}
						return "车方已支付订金";
					case "35":
						return"订金已退还";
//                    case "40":
//                        return"收到订金(可提现)");
//                        continue;
//                    case "45":
//                        return"收到订金(可提现)");
//                        continue;
					case "30":
						if(delayRefundStatus==0){
							if(tDay>1){
								return"车方申请退还订金";
							}else{
								return"车方申请退还订金，即将自动退还";
							}
						}else{
							if(tDay>1){
								return"车方已支付订金";
							}else{
								return"车方已支付订金，即将自动退还";
							}
						}
//                    case "21":
//                        return"车方拒绝退款，订金延时到账");
//                        continue;
					case "25":
						return"已发起异常上报，请联系客服解决";
					case "50":
						return"异常上报处理完成";
					default:
				}
			}else if(isOwner && !RefundFlagEnum.退还.getId().equals(refundFlag)){
				//货方不退还
				switch (costStatus){
					case "10":
						return"待支付";
					case "15":
						if(delayStatus==1){
							return"车方已操作延迟付款";
						}else if (delayStatus==2){
							return"车方拒绝退款，订金延时到账";
						}else{
							return"车方已支付订金";
						}
					case "35":
						return"订金已退还";
					case "40":
						return"收到订金(可提现)";
					case "45":
						return"收到订金(可提现)";
					case "30":
						return"已退款等待车主收款";
					case "21":
						return"车方拒绝退款，订金延时到账";
					case "25":
						return"已发起异常上报，请联系客服解决";
					case "50":
						return"异常上报处理完成";
					default:
						return "";
				}
			}else if(!isOwner && RefundFlagEnum.退还.getId().equals(refundFlag)){
				//车方退还
				//订单状态为待卸货
				if(!isPc && orderNewStatus != null
						&& OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode() == orderNewStatus
						&& OrderDataType.ORDER_LIST.getCode() == orderDataType){
					switch (costStatus){
						case "15":
							if(1==cancelStatus){
								return"已取消等待车方确认";
							}
							return"待货方确认收货和退回订金";
						case "35":
							return"待货方确认收货";
						case "25":
							return"已发起异常上报，请联系客服解决";
						case "50":
							return"待货方确认收货";
						default:
							return "待货方确认收货";
					}
				}else{
					switch (costStatus){
						case "10":
							return"待支付";
						case "15":
							if(1==cancelStatus){
								return"已取消等待车方确认";
							}
							if(delayRefundStatus==0){
								return"订金已支付";
							}else{
								return"货方已操作延迟退款";
							}
						case "22":
							return "已拒绝退款，货方确认收货后订金退回";
						case "35":
							return"订金已退还";

//                    case "40":
//                        return"收到订金(可提现)");
//                        continue;
//                    case "45":
//                        return"收到订金(可提现)");
//                        continue;
						case "30":
							if(delayRefundStatus==0){
								return"已申请退还订金，等待货方确认";
							}else{
								return"货方已操作延迟退款";
							}

//                    case "21":
//                        return"车方拒绝退款，订金延时到账");
//                        continue;
						case "25":
							return"已发起异常上报，请联系客服解决";
						case "50":
							return"异常上报处理完成";
						default:
							return "";
					}
				}
			}else{
				//车方不退还
				//订单状态为待卸货
				if(!isPc && orderNewStatus != null
						&& OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode() == orderNewStatus
						&& OrderDataType.ORDER_LIST.getCode() == orderDataType){
					switch (costStatus){
						case "25":
							return"已发起异常上报，请联系客服解决";
						case "50":
							return "待货方确认收货";
						default:
							return "待货方确认收货";
					}
				}else{
					switch (costStatus){
						case "10":
							return"待支付";
						case "15":
							if(delayStatus==1){
								return"订金已延迟付款";
							}else if (delayStatus==2){
								return"已拒绝退款，订金已延迟支付";
							}else{
								return"订金已支付";
							}
						case "35":
							return"订金已退还";
						case "40":
							return"订金付款已确认";
						case "45":
							return"订金付款已确认";
						case "30":
							return"货方退款等待确认";
						case "21":
							return"已拒绝退款，订金已延迟支付";
						case "25":
							return"已发起异常上报，请联系客服解决";
						case "50":
							return"异常上报处理完成";
						default:
							return "";
					}
				}
			}
		} catch (Exception e) {
			logger.error("信息费订金状态转换出现异常:", e);
		}
		return "";
	}

	private boolean objectIsNotEmpty(Object obj){
		return EmptyUtil.objectIsNotEmpty(obj);
	}


	@Override
	public boolean saveChangeOrder(Long orderId) throws Exception {
		TytTransportOrders transportOrder=this.getBaseDao().getByIdForLock(orderId);
		//记录不存在
		if(transportOrder==null){
			return false;
		}
		//支付成功的不能取消
		if(transportOrder.getPayStatus().equals("2")){
			return false;
		}
		//只有待支付的才能取消
		if(!transportOrder.getRobStatus().equals("0")){
			return false;
		}
		// 取消订单
		boolean changeFlag=this.saveChangeRobStatusById(orderId, "11");
		if(!changeFlag){
			return false;
		}
		//车主的待支付气泡减一
		tytBubbleService.updateBubbleNumber(transportOrder.getPayUserId(), "1", "1", -1);
		return true;
	}

	/**
	 * @Description  取消待支付订单（信息费改版新方法）
	 * <AUTHOR>
	 * @Date  2019/1/19 16:15
	 * @Param [orderId, loginUser]
	 * @return boolean
	 **/
	@Override
	public boolean saveChangeOrderNew(Long orderId, EmployeeQueryBean loginUser) throws Exception {
		TytTransportOrders transportOrder=this.getBaseDao().getByIdForLock(orderId);
		//记录不存在
		if(transportOrder==null){
			return false;
		}
		//支付成功的不能取消
		if(transportOrder.getPayStatus().equals("2")){
			return false;
		}
		//只有待支付的才能取消
		if(!transportOrder.getRobStatus().equals("0")){
			return false;
		}
		//信息费状态不为10待支付，不能取消
		Integer costStatus = transportOrder.getCostStatus();
		if(costStatus != null && costStatus.intValue() != 10) {
			return false;
		}
		// 取消订单
		boolean changeFlag=this.saveChangeRobStatusByIdNew(orderId, "11",6, loginUser);
		if(!changeFlag){
			return false;
		}
		//车主的待支付气泡减一
		tytBubbleService.updateBubbleNumber(transportOrder.getPayUserId(), "1", "1", -1);
		return true;
	}

	@Override
	public boolean saveChangeRobStatusById(Long orderId, String robStatus)throws Exception {
		String updateSQL="update tyt_transport_orders set rob_status=:robStatus"
				+ ",mtime=NOW(),sort_id=:sortId"
				+ " where  id=:id and rob_status=:ruleRobStatus and pay_status=:payStatus";
		Map<String,Object> paramsMap=new HashMap<String,Object>();
		paramsMap.put("robStatus", robStatus);
		paramsMap.put("sortId", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME));
		paramsMap.put("id", orderId);
		paramsMap.put("ruleRobStatus", 0);
		paramsMap.put("payStatus", 0);
		return this.executeUpdateSql(updateSQL, paramsMap)>0;
	}

	/**
	 * @Description   改变订单状态（信息费改版新方法）
	 * <AUTHOR>
	 * @Date  2019/1/19 16:23
	 * @Param [orderId, robStatus, costStatus]
	 * @return boolean
	 **/
	@Override
	public boolean saveChangeRobStatusByIdNew(Long orderId, String robStatus, Integer costStatus, EmployeeQueryBean loginUser) throws Exception {
		String updateSQL="update tyt_transport_orders set rob_status=:robStatus,cost_status=:costStatus,"
				+ "pay_status=1,mtime=NOW(),"
				+ "op_id=:opId,op_name=:opName "
				+ " where id=:id and rob_status=:ruleRobStatus and pay_status=:payStatus";
		Map<String,Object> paramsMap=new HashMap<String,Object>();
		paramsMap.put("robStatus", robStatus);
		paramsMap.put("costStatus", costStatus);
		paramsMap.put("id", orderId);
		paramsMap.put("ruleRobStatus", 0);
		paramsMap.put("payStatus", 0);
		//操作人Id
		paramsMap.put("opId", loginUser.getId());
		//操作人姓名
		paramsMap.put("opName", loginUser.getUserName());

		return this.executeUpdateSql(updateSQL, paramsMap)>0;
	}

    /**
     * @Description  批量设置成交和撤销，改变待支付信息费的状态
     * <AUTHOR>
     * @Date  2019/1/19 18:00
     * @Param [tsId, robStatus, costStatus]
     * @return boolean
     **/
	@Override
	public boolean updateInfoFeeOrdersStatus(Long tsId, String robStatus, Integer costStatus) throws Exception {
		String updateSQL="update tyt_transport_orders set rob_status=:robStatus" +
								",cost_status=:costStatus,pay_status=1,mtime=NOW()" +
								" where ts_id=:tsId and cost_status=10";

		Map<String, Object> paramsMap = new HashMap<String, Object>();
		//接单状态 11接单失败
		paramsMap.put("robStatus", robStatus);
		//信息费状态： 6待支付关闭
		paramsMap.put("costStatus", costStatus);
		paramsMap.put("tsId", tsId);

		return this.executeUpdateSql(updateSQL, paramsMap)>0;
	}

	@Override
	public TytTransportOrders getTytTransportOrders(Long tsId, Long payUserId){
		TransportMain transportMain=transportMainService.getTransportMainForId(tsId);
		String hql="from TytTransportOrders t where ctime>? and ctime<=? and  tsId=? and payUserId=?  ";
		List <TytTransportOrders>list=this.getBaseDao().find(hql,TimeUtil.today(),new Date(), transportMain.getId(),payUserId);
		if(list!=null&& list.size()>0){
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<TytTransportOrders> getPayOrders(String tsOrderNo){
		String hql="from TytTransportOrders t where tsOrderNo=? and rob_status > 0 and pay_status = 2";
		return this.getBaseDao().find(hql, tsOrderNo);
	}

	@Override
	public TytTransportOrders getByTsOrderNOAndPayUserId(String tsOrderNo,Long userId){
		String hql="from TytTransportOrders t where tsOrderNo=? and  payUserId=? and payStatus = 2 order by id desc";
		List<TytTransportOrders> tytTransportOrders = this.getBaseDao().find(hql, tsOrderNo, userId);
		if(Objects.isNull(tytTransportOrders)){
			return null;
		}
		return tytTransportOrders.get(0);
	}
	@SuppressWarnings("deprecation")
	@Override
	public List<OrdersQueryBean> getOrders(PageBean pageBean,
			OrdersQueryBean queryBean) throws Exception{
		//查询条件及参数值
		StringBuffer conditionBuffer=new StringBuffer();
		List<Object> conditionParams=new ArrayList<Object>();
		int count=0;
		if(objectIsNotEmpty(queryBean.getTsOrderNo())){
			conditionBuffer.append(" and o.ts_order_no=?");
			conditionParams.add(queryBean.getTsOrderNo());
			count++;
		}
		if(objectIsNotEmpty(queryBean.getTsId())){
			conditionBuffer.append(" and o.ts_id=?");
			conditionParams.add(queryBean.getTsId());
			count++;
		}

        Long uploadUserId = queryBean.getUserId();
        if(objectIsNotEmpty(queryBean.getUploadCellphone())){
			Long userId = userService.getUserIdByCellPhone(queryBean.getUploadCellphone());

			if(userId == null){
				return null;
			} else {
				if(uploadUserId != null && !uploadUserId.equals(userId)) {
					return null;
				} else {
                    uploadUserId = userId;
				}
			}
		}
        if(uploadUserId != null) {
            conditionBuffer.append(" and o.user_id=?");
            conditionParams.add(uploadUserId);

            count++;
        }

        Long payUserId = queryBean.getPayUserId();
        String payCellPhone = queryBean.getPayCellPhone();

        if(StringUtils.isNotBlank(payCellPhone)){
            Long dbPayUserId = userService.getUserIdByCellPhone(payCellPhone);

            if(dbPayUserId == null){
                return null;
            } else {
                if(payUserId != null && !payUserId.equals(dbPayUserId)) {
                    return null;
                } else {
                    payUserId = dbPayUserId;
                }
            }
        }
        if(payUserId != null) {
            conditionBuffer.append(" and o.pay_user_id=?");
            conditionParams.add(payUserId);
            count++;
        }

        if(objectIsNotEmpty(queryBean.getStartCtime())){
			conditionBuffer.append(" and o.ctime>=?");
			conditionParams.add(queryBean.getStartCtime());
			count++;
		}
		if(objectIsNotEmpty(queryBean.getEndCtime())){
			Date date = TimeUtil.weeHours(queryBean.getEndCtime(), 1);
			conditionBuffer.append(" and o.ctime<=?");
			conditionParams.add(date);
			count++;
		}
		if (count==0) {
			queryBean.setStartCtime(TimeUtil.dateDiff(-1));
			conditionBuffer.append(" and o.ctime>=?");
			conditionParams.add(queryBean.getStartCtime());
		}
		conditionBuffer.append(" AND (ttos.seckill_goods in (0,2) OR (ttos.seckill_goods = 1 AND ttos.order_allocate_state = 1  AND o.order_new_status IN (10, 15, 20, 25, 30, 35)))");
		StringBuffer countBuffer=new StringBuffer("select count(*) from tyt_transport_orders o LEFT JOIN tyt_transport_order_snapshot ttos ON o.id = ttos.order_id where 1=1");
		countBuffer.append(conditionBuffer);
		BigInteger total=this.getBaseDao().query(countBuffer.toString(), conditionParams.toArray());
		if(total!=null&&total.longValue()>0L){
			pageBean.setRowCount(total.longValue());
		    StringBuffer selectBuffer=new StringBuffer(
		        "select o.id,o.ts_order_no tsOrderNo,o.ctime ctime,o.upload_cellphone uploadCellphone, o.user_id userId,o.pay_user_id payUserId,o.pay_cell_phone payCellPhone,o.tec_service_fee tecServiceFee,"
		        + "o.ts_id tsId,o.rob_status robStatus,o.pay_status payStatus,o.create_time createTime,"
		        + "o.pay_amount payAmount,o.pay_end_time payEndTime,o.mtime mtime,o.refund_flag refundFlag,o.refund_reason as refundReason,cost_status as costStatus from tyt_transport_orders o" +
						" LEFT JOIN tyt_transport_order_snapshot ttos ON o.id = ttos.order_id where 1=1 ");
		    selectBuffer.append(conditionBuffer).append(" order by o.id desc");
		    Map<String,Type> typeMap=new HashMap<String,Type>();
		    typeMap.put("id", Hibernate.LONG);
		    typeMap.put("tsOrderNo", Hibernate.STRING);
		    typeMap.put("ctime", Hibernate.TIMESTAMP);
		    typeMap.put("uploadCellphone", Hibernate.STRING);
		    typeMap.put("userId", Hibernate.LONG);
		    typeMap.put("payUserId", Hibernate.LONG);
		    typeMap.put("payCellPhone", Hibernate.STRING);
		    typeMap.put("tsId", Hibernate.LONG);
		    typeMap.put("robStatus", Hibernate.STRING);
		    typeMap.put("payStatus", Hibernate.STRING);
		    typeMap.put("createTime", Hibernate.TIMESTAMP);
		    typeMap.put("payAmount", Hibernate.LONG);
			typeMap.put("tecServiceFee", Hibernate.LONG);
		    typeMap.put("payEndTime", Hibernate.TIMESTAMP);
		    typeMap.put("mtime", Hibernate.TIMESTAMP);
		    typeMap.put("refundReason", Hibernate.STRING);
		    typeMap.put("costStatus", Hibernate.INTEGER);
            typeMap.put("refundFlag", Hibernate.INTEGER);
			return this.getBaseDao().search(selectBuffer.toString(), typeMap, OrdersQueryBean.class, conditionParams.toArray(), pageBean.getCurrentPage(), pageBean.getPageSize());
		}else{
		    return null;
		}
		
	}
	@Override
	public TytTransportOrders getDetail(Long id){
		TytTransportOrders orders = this.getBaseDao().getByIdForLock(id);
		return orders;
	}

    @Override
    public TytTransportOrderSnapshot getOrderSnapshot(Long orderId) {

        TytTransportOrderSnapshot orderSnapshot = transportOrderSnapshotMapper.getOrderSnapshot(orderId);

        return orderSnapshot;
    }

    @Override
	public int getNoPayNum(Long srcMsgId) {
		String countSql = "SELECT COUNT(1) FROM tyt.`tyt_transport_orders` tto WHERE tto.`ts_id`=? AND tto.`rob_status`=? ";
		BigInteger rowCount = this.getBaseDao().query(countSql, new Object[] { srcMsgId, 0 });
		return rowCount.intValue();
	}
	
	@Override
	public int getPayAndNoPayNum(Long srcMsgId) {
		String countSql = "SELECT COUNT(1) FROM tyt.`tyt_transport_orders` tto WHERE tto.`ts_id`=? AND tto.`rob_status` IN (?,?,?,?,?,?)";
		BigInteger rowCount = this.getBaseDao().query(countSql, new Object[] { srcMsgId, 0, 1, 4, 5, 6, 7 });
		return rowCount.intValue();
	}

	/**
	 * 线下信息费/无信息费 成交流程
	 * @param orderBean
	 * @param currentUser
	 * @return
	 * @throws Exception
	 */
	@Override
	public ResultMsgBean createOfflineOrder(CreateOrderBean orderBean, EmployeeQueryBean currentUser) throws Exception {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		TytCarOwnerIntention tytCarOwnerIntention = carOwnerIntentionService.getById(orderBean.getId());
		//意向状态0未支付1创建订单2支付成功,3退款4成交
		tytCarOwnerIntention.setStatus(4);
		tytCarOwnerIntention.setPayAmount(Math.round(orderBean.getMoney() * 100));
//		tytCarOwnerIntention.setInfoFeeType(InfoFeeTypeEnum.ONLINE.getId()); //默认线上
		if(orderBean.getOfflinePaid() == 1){
			tytCarOwnerIntention.setInfoFeeType(InfoFeeTypeEnum.OFFLINE.getId());
		}
		if(orderBean.getNoInfoFee() == 1){
			tytCarOwnerIntention.setInfoFeeType(InfoFeeTypeEnum.NONE.getId());
		}
		Date currentTime = new Date();
		tytCarOwnerIntention.setUtime(currentTime);
		carOwnerIntentionService.update(tytCarOwnerIntention);
		long srcMsgId = orderBean.getSrcMsgId();
		TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
		Transport transport = this.getByGoodsId(transportMain.getId());



		long t1 = System.currentTimeMillis();
		logger.info("支付运前信息费service处理开始时间【{}】ms", t1);
		logger.info("info business save savePayOrderBusiness srcMsgId: " + srcMsgId);
		if (transport == null) {
			resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
			resultMsgBean.setMsg("goodsId所代表的对象不存在");
			return resultMsgBean;
		}

		long t2 = System.currentTimeMillis();
		logger.info("支付运前信息费锁表结束时间【{}】ms,用时【{}】ms", t2, t2 - t1);
		// 判读是不是昨天的数据
		boolean isHistoryGoods = transport.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(currentTime)).getTime();
		// 货源是否处于发布中，以确定是否可以被支付
		if (!isHistoryGoods && transport.getIsInfoFee().equals("1") && transport.getStatus() == 1) {
			String transportNo = transport.getTsOrderNo();
			TytTransportOrders tytTransportOrders = transportOrdersService.saveOfflineOrder(transport, orderBean, currentUser);
			Long orderId = tytTransportOrders.getId();
			if (orderBean.getOfflinePaid() == 1){
				//暂时不记录线下信息费流水
//				offlinePaidFlow(orderBean, currentTime, tytTransportOrders);
			}

			if (orderId == null || orderId.longValue() <= 0) {
				resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_FAIL_TO_MAKE_ORDER);
				resultMsgBean.setMsg("创建订单失败！");
				return resultMsgBean;
			}

			//成交
			// 修改意向表除当前车主外的支付成功订单状态修改为已退款
			carOwnerIntentionService.updateAgree(String.valueOf(srcMsgId), tytTransportOrders.getPayUserId().toString());
			List<TransportMt> transportMts = mtService.search(" srcMsgId=? and status=? ", new Object[] { Long.valueOf(srcMsgId), 1 }, null);
			logger.info("transport mt updateIntentionBox mt result is: " + transportMts + " , srcMsgId is: " + srcMsgId);
			// 如果获取的结果个数不是1则认为是操作失败
			if (transportMts == null || transportMts.size() != 1) {
				logger.info("transport mt updateIntentionBox no mt result");
				resultMsgBean.setCode(201);
				return resultMsgBean;
			} else {
				TransportMt transportMt = transportMts.get(0);
				logger.info("transport mt updateIntentionBox only mt result is: " + transportMt + " , srcMsg id is: " + srcMsgId);
				transportMt.setStrikePrice(Math.round(tytCarOwnerIntention.getCashOffer()));
				transportMt.setOilPrice(tytCarOwnerIntention.getOilOffer());
				transportMt.setFirstPayMoney(Math.round(orderBean.getMoney()*100));
				transportMt.setFirstPayUserId(tytTransportOrders.getPayUserId());
				transportMt.setBeforePhone(tytCarOwnerIntention.getCarPhone());
				transportMt.setDriverPhone(tytCarOwnerIntention.getCarPhone());
				transportMt.setCarryPhone(tytCarOwnerIntention.getCarPhone());
				transportMt.setInfoFeeType(tytTransportOrders.getInfoFeeType());
				transportMt.setStatus(4); //已成交
                transportMt.setFollowGoodsStatus(FollowGoodsStatusEnum.TRANSPORTING.getId()); //线下信息费 跟车状态 运输中
                transportMt.setClosingTime(currentTime);

                //根据承运车辆Id取出承运车辆信息(下面的电话属性赋值可能会覆盖之前的值，以本次修改为准)
				Car carInfo = carService.getById(tytCarOwnerIntention.getCarId());
				User user = userService.getById(carInfo.getUserId());
				//承运人姓名
				transportMt.setCarryName(user.getUserName());
				//承运人手机号
				transportMt.setCarryPhone(user.getCellPhone());
				//主司机姓名
				transportMt.setDriverName(carInfo.getDriverName());
				//主司机手机号
				transportMt.setDriverPhone(carInfo.getDriverPhone());
				//副司机姓名
				transportMt.setSecondaryDriverName(carInfo.getSecondaryDriverName());
				//副司机手机号
				transportMt.setSecondaryDriverPhone(carInfo.getSecondaryDriverPhone());
				//随车司机电话
				transportMt.setFollowDriverPhone(carInfo.getFollowDriverPhone());
				mtService.update(transportMt);

				//2019-06-12 调度平台app，达成意向时生成一条行程单信息
				addTravelInfo(transportMt, carInfo);
			}

			//达成意向
			transportMtService.updateOperator(String.valueOf(srcMsgId), currentUser.getRealName(), currentUser.getId());
			//信息费旧版本才做以下动作

			IntentionInfoBean intentionInfo = new IntentionInfoBean();
			/**
			 * 	private String transportId;
			 *  private String srcMsgId;
			 *  private String firstPayUserId;
			 *
			 */
			intentionInfo.setTransportId(String.valueOf(srcMsgId));
			intentionInfo.setSrcMsgId(String.valueOf(srcMsgId));
			intentionInfo.setFirstPayUserId(String.valueOf(tytTransportOrders.getPayUserId()));
			transportBusiness.updateStatusBusiness(4, srcMsgId, 1);

			if(!TytSwitchUtil.isNewInfofeeVersion()) {
				// 初始化合同信息 modify by guyw 20180110
				TransportMt transportMtNew = new TransportMt();
				BeanUtils.copyProperties(transportMts.get(0), transportMtNew);
				transportMtNew.setFirstPayUserId(tytTransportOrders.getPayUserId());


				//todo 合同信息油卡
				transportMtNew.setStrikePrice(Math.round(tytCarOwnerIntention.getCashOffer()));
				transportMtNew.setOilPrice(tytCarOwnerIntention.getOilOffer());
				ecaContractService.initEcaInfo(transportMtNew, currentUser);
				// 修改货源的位置授权状态为授权
				transportMtService.updateLocationGrantStatus(intentionInfo.getTransportId(), 1);
				notifyBIStartLocation(intentionInfo.getTransportId());

			}
			// 返回结果
			return resultMsgBean;

		} else {
			resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_NOT_ALLOWED_TO_PAY);
			resultMsgBean.setMsg("货源状态不用许创建订单！");
			return resultMsgBean;
		}

	}

	/**
	 * @Description  生成一条行程单信息
	 * <AUTHOR>
	 * @Date  2019/6/12 18:03
	 * @Param [transportMt, carInfo]
	 * @return void
	 **/
	private void addTravelInfo(TransportMt transportMt, Car carInfo) {

		if(transportMt != null && carInfo != null){
			//行程单对象
			TytScheduleTravel scheduleTravel = new TytScheduleTravel();
			scheduleTravel.setUserId(carInfo.getUserId());
			scheduleTravel.setCarId(carInfo.getId());
			scheduleTravel.setHeadCity(carInfo.getHeadCity());
			scheduleTravel.setHeadNo(carInfo.getHeadNo());
			scheduleTravel.setTailCity(carInfo.getTailCity());
			scheduleTravel.setTailNo(carInfo.getTailNo());
			//车牌号(包含“车头拍照号码+挂车拍照号码”，例如“京P12345京F1234挂”)
			String carHeadNo = carInfo.getHeadCity()+carInfo.getHeadNo()
					+carInfo.getTailCity()+carInfo.getTailNo();
			scheduleTravel.setCarHeadNo(carHeadNo);
			scheduleTravel.setStartPoint(transportMt.getStartPoint());
			scheduleTravel.setStartProvinc(transportMt.getStartProvinc());
			scheduleTravel.setStartCity(transportMt.getStartCity());
			scheduleTravel.setStartArea(transportMt.getStartArea());
			scheduleTravel.setDestPoint(transportMt.getDestPoint());
			scheduleTravel.setDestProvinc(transportMt.getDestProvinc());
			scheduleTravel.setDestCity(transportMt.getDestCity());
			scheduleTravel.setDestArea(transportMt.getDestArea());
			scheduleTravel.setStartAddressDetail(transportMt.getStartDetailAdd());
			scheduleTravel.setDestAddressDetail(transportMt.getDestDetailAdd());
			scheduleTravel.setStartLongitude(transportMt.getStartLongitudeValue());
			scheduleTravel.setStartLatitude(transportMt.getStartLatitudeValue());
			scheduleTravel.setDestLongitude(transportMt.getDestLongitudeValue());
			scheduleTravel.setDestLatitude(transportMt.getDestLatitudeValue());
			scheduleTravel.setDistance(transportMt.getDistanceValue());
			//行程单的id，对应的是mt表的id，而不是ts_id
			scheduleTravel.setTsId(transportMt.getId());
			scheduleTravel.setTaskContent(transportMt.getTaskContent());
			scheduleTravel.setWeight(transportMt.getWeight());
			scheduleTravel.setLenght(transportMt.getLength());
			scheduleTravel.setWide(transportMt.getWide());
			scheduleTravel.setHigh(transportMt.getHigh());
			scheduleTravel.setPrice(transportMt.getStrikePrice());
			//scheduleTravel.setCost();
			Date loadingTime = transportMt.getLoadingTime();
			if(loadingTime != null){
				scheduleTravel.setLoadTime(new Timestamp(loadingTime.getTime()));
			}
			//scheduleTravel.setUnloadTime();
			scheduleTravel.setCompany(transportMt.getCompany());
			scheduleTravel.setCtime(TimeUtil.getTimeStamp());
			//行程单状态（1.正常 2.异常终止）
			scheduleTravel.setStatus(1);
			//行程记录Id
			Long travelId = (Long) scheduleTravelService.addSave(scheduleTravel);
		    logger.info("添加行程单信息成功，行程单Id为：" + travelId);
		}
	}

	//线下信息费流水
	private void offlinePaidFlow(CreateOrderBean orderBean, Date currentTime, TytTransportOrders tytTransportOrders) {
		//保存流水4、出入帐2 TytFinancialInOutAccount TytFinancialFlow
		//FlowTypeEnum.支付信息费
		FinancialFlow financialFlow1 = new FinancialFlow();
		financialFlow1.setFlowType((short) FlowTypeEnum.支付信息费.getId());
		financialFlow1.setCreateTime(currentTime);
		financialFlow1.setUserId(Math.toIntExact(tytTransportOrders.getPayUserId()));
		financialFlow1.setTransfterBank((short) 4);//4线下信息费
		financialFlow1.setWaybillNumber(tytTransportOrders.getTsOrderNo());
		financialFlow1.setMoneyAmount("-"+ orderBean.getMoney() * 100);
		financialFlowService.add(financialFlow1);
		//FlowTypeEnum.收到信息费
		FinancialFlow financialFlow2 = new FinancialFlow();
		financialFlow2.setFlowType((short) FlowTypeEnum.收到信息费.getId());
		financialFlow2.setCreateTime(currentTime);
		financialFlow2.setUserId(Math.toIntExact(tytTransportOrders.getPayUserId()));
		financialFlow2.setTransfterBank((short) 4);//4线下信息费
		financialFlow2.setWaybillNumber(tytTransportOrders.getTsOrderNo());
		financialFlow2.setMoneyAmount("+"+ orderBean.getMoney() * 100);
		financialFlowService.add(financialFlow2);
		//FlowTypeEnum.用户转账
		FinancialFlow financialFlow3 = new FinancialFlow();
		financialFlow3.setFlowType((short) FlowTypeEnum.用户转账.getId());
		financialFlow3.setCreateTime(currentTime);
		financialFlow3.setUserId(Math.toIntExact(tytTransportOrders.getPayUserId()));
		financialFlow3.setTransfterBank((short) 4);//4线下信息费
		financialFlow3.setWaybillNumber(tytTransportOrders.getTsOrderNo());
		financialFlow3.setMoneyAmount("+"+ orderBean.getMoney() * 100);
		financialFlowService.add(financialFlow3);
		//FlowTypeEnum.支付手续费
		FinancialFlow financialFlow4 = new FinancialFlow();
		financialFlow4.setFlowType((short) FlowTypeEnum.支付手续费.getId());
		financialFlow4.setCreateTime(currentTime);
		financialFlow4.setUserId(Math.toIntExact(tytTransportOrders.getPayUserId()));
		financialFlow4.setTransfterBank((short) 4);//4线下信息费
		financialFlow4.setWaybillNumber(tytTransportOrders.getTsOrderNo());
		financialFlow4.setMoneyAmount("-0");
		financialFlowService.add(financialFlow4);

		TytFinancialInOutAccount inOutAccount1 = new TytFinancialInOutAccount();
		inOutAccount1.setAccountType(AccountTypeEnum.信息费支付.getId());
		inOutAccount1.setUserId(Math.toIntExact(tytTransportOrders.getPayUserId()));
		inOutAccount1.setCreateTime(currentTime);
		inOutAccount1.setMoneySymbol("+");
		inOutAccount1.setWaybillNumber(tytTransportOrders.getTsOrderNo());
		inOutAccount1.setAccount(Math.round(orderBean.getMoney())*100);
		inOutAccount1.setPayType(4); //线下信息费
		inOutAccount1.setTradeTime(currentTime);
		inOutAccountService.add(inOutAccount1);


		TytFinancialInOutAccount inOutAccount2 = new TytFinancialInOutAccount();
		inOutAccount2.setAccountType(AccountTypeEnum.信息费手续费支付.getId());
		inOutAccount2.setUserId(Math.toIntExact(tytTransportOrders.getPayUserId()));
		inOutAccount2.setCreateTime(currentTime);
		inOutAccount2.setMoneySymbol("+");
		inOutAccount2.setWaybillNumber(tytTransportOrders.getTsOrderNo());
		inOutAccount2.setAccount(0);
		inOutAccount2.setPayType(4); //线下信息费
		inOutAccount2.setTradeTime(currentTime);
		inOutAccountService.add(inOutAccount2);
	}


	private void notifyBIStartLocation(String tsId) throws HttpException, IOException {
		TransportMt transportMt = transportMtService.getByTsId(Long.valueOf(tsId));
		logger.info("notifyBIStartLocation query mt srcMsgId is: " + tsId + " , result is: " + transportMt);
		if (transportMt != null) {
			TytCarOwnerIntention carOwnerIntention = null;
			List<TytCarOwnerIntention> carOwnerIntentionList = carOwnerIntentionService.getTytCarOwnerIntentionList(transportMt.getSrcMsgId(), 1);
			logger.info("notifyBIStartLocation query car owner intention srcMsgId is: " + transportMt.getSrcMsgId() + " , result is: "
					+ carOwnerIntentionList);
			if (carOwnerIntentionList != null && carOwnerIntentionList.size() == 1) {
				carOwnerIntention = carOwnerIntentionList.get(0);
				Car car = null;
				if (carOwnerIntention != null) {
					if (carOwnerIntention.getCarId() == null) {
						car = carService.getFirstAuthCar(transportMt.getFirstPayUserId());
					} else {
						car = carService.getById(carOwnerIntention.getCarId());
					}
					logger.info("notifyBIStartLocation query car carId is: " + carOwnerIntention.getCarId() + " , result is: " + car);
					if (car != null) {
						/*
						 * api_sign的算法：api_sign = md5(私钥+路径+api_time)
						 * 私钥：9d13dcadb5dbff5d0a93b6389ac5c89a
						 * 路径和api_time：/idc/index.php
						 * /apibi/transport/transport_send_count_rank_list
						 * ?api_time=2017-06-29 13:52:00
						 * 举例：md5("9d13dcadb5dbff5d0a93b6389ac5c89a" +
						 * "/idc/index.php/apibi/transport/transport_send_count_rank_list?api_time=2017-06-29 13:52:00"
						 * ) 计算结果为：8a4a1c11fcdc8440df4898d2726a7934
						 * http://*************:
						 * 82/idc/index.php/apibi/zhiyun/tyt_add_car_track_collect
						 * ?api_time=2018-01-10
						 * 16:43:00&car_no=京A12345|陕YH0009|陕YH0008|陕YH0007
						 * &track_day_type
						 * =everyday&track_day_long=30&api_sign=97675d387d
						 * 52e2450aaa5fc3ac0983a1
						 */
						String biBase = tytConfigService.getStringValue(Constant.SYNC_CAR_BI_HTTP_BASE_KEY, "http://*************");
						String biPath = tytConfigService.getStringValue(Constant.SYNC_CAR_BI_PATH_KEY,
								"/idc/index.php/apibi/zhiyun/tyt_add_car_track_collect");
						String biPrivateKey = tytConfigService.getStringValue(Constant.SYNC_CAR_BI_PRIVATEKEY_KEY, "9d13dcadb5dbff5d0a93b6389ac5c89a");
						String biApiTime = TimeUtil.formatDateTime(new Date());
						StringBuffer sb = new StringBuffer();
						String signOriginal = sb.append(biPrivateKey).append(biPath).append("?api_time=").append(biApiTime).append("&car_no=")
								.append(car.getHeadCity()).append(car.getHeadNo()).append("&track_day_type=everyday").append("&track_day_long=-1")
								.toString();
						String sign = MD5Util.GetMD5Code(signOriginal);
						logger.info("notifyBIStartLocation notify bi biBase is: " + biBase + " , biPath is: " + biPath + " , biPrivateKey is: "
								+ biPrivateKey + ", sign is: " + sign + ", signOriginal is: " + signOriginal);
						HttpClient httpClient = new HttpClient();
						sb.setLength(0);
						sb.append(biBase).append(biPath).append("?api_time=").append(biApiTime);
						sb.append("&car_no=").append(car.getHeadCity()).append(car.getHeadNo());
						sb.append("&track_day_type=everyday");
						sb.append("&track_day_long=-1");
						sb.append("&api_sign=").append(sign);
						String url = sb.toString();
						logger.info("notifyBIStartLocation url is: " + url);
						ResponseStatus responseStatus = httpClient.get(url);
						logger.info("notifyBIStartLocation notify bi response status is: " + responseStatus.getStatusCode()
								+ " , response content is: " + responseStatus.getContent());
					}
				}
			}
		}
	}

	private Transport getByGoodsId(Long goodsId) throws Exception {
		TransportMain oldTransportMain = transportMainService.getById(goodsId);
		if (oldTransportMain != null) {
			Transport oldTransport = new Transport();
			BeanUtils.copyProperties(oldTransportMain, oldTransport);
			logger.info("根据货物ID查询最新的货物ID,goodsId:{}", oldTransport.getId());
			return oldTransport;
		} else {
			Transport oldTransport = transportService.getById(goodsId);
			oldTransportMain= transportMainService.getById(oldTransport.getSrcMsgId());
			if (oldTransportMain!=null) {
				Transport transport = new Transport();
				BeanUtils.copyProperties(oldTransportMain, transport);
				logger.info("根据货物ID查询最新的货物ID,goodsId:{}", transport.getId());
				return transport;
			}

		}
		return  null;

	}

	@Override
	public TytTransportOrders saveOfflineOrder(Transport transport, CreateOrderBean orderBean, EmployeeQueryBean currentUser) {
		Date todayDate = new Date();
		// 费率
		String infoRate = tytConfigService.getStringValue("infoFeeRate");
		if (infoRate == null || infoRate.trim().equals("")) {
			infoRate = "0.004";
		}
		TytTransportOrders tytTransportOrders = new TytTransportOrders();
		tytTransportOrders.setStartPoint(transport.getStartPoint());
		tytTransportOrders.setDestPoint(transport.getDestPoint());
		tytTransportOrders.setTaskContent(transport.getTaskContent());
		tytTransportOrders.setTel(transport.getTel());
		tytTransportOrders.setPubTime(transport.getPubTime());
		tytTransportOrders.setCtime(transport.getReleaseTime());
		tytTransportOrders.setUploadCellphone(transport.getUploadCellPhone());
		tytTransportOrders.setUserId(transport.getUserId());
		tytTransportOrders.setLinkman(transport.getLinkman());
		tytTransportOrders.setTel3(transport.getTel3());
		tytTransportOrders.setTel4(transport.getTel4());
		tytTransportOrders.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME));
		tytTransportOrders.setTsOrderNo(transport.getTsOrderNo());
		tytTransportOrders.setTsId(transport.getId());
		tytTransportOrders.setPayUserId(orderBean.getCarUserId()); //付款方为车方
		tytTransportOrders.setPayCellPhone(orderBean.getPhone());
		tytTransportOrders.setRemark(orderBean.getRemark());
		tytTransportOrders.setCreateBy(currentUser.getUserName());
		tytTransportOrders.setAgreeTime(new Date());
		if(orderBean.getNoInfoFee() == 1){
			tytTransportOrders.setInfoFeeType(InfoFeeTypeEnum.NONE.getId());
		}else if(orderBean.getOfflinePaid() == 1){
			tytTransportOrders.setInfoFeeType(InfoFeeTypeEnum.OFFLINE.getId());
		}
		tytTransportOrders.setPayLinkPhone(orderBean.getPhone());
		tytTransportOrders.setRobStatus("5");// 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货
		if(TytSwitchUtil.isNewInfofeeVersion()){
			tytTransportOrders.setCostStatus(InfofeeStatusEnum.已支付.getId());
		}
		logger.info("set robstatus {}", tytTransportOrders.getRobStatus());
		// 5车主装货完成 6系统装货完成 7异常上报
		// 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货
		tytTransportOrders.setPayStatus("2"); // 支付状态0待支付1支付失败2支付成功
		tytTransportOrders.setCreateTime(todayDate);// 接单时间
		tytTransportOrders.setPayNo(null);// 内部支付流水号
		tytTransportOrders.setPayType("4");// 支付方式 1支付宝 2连连银行卡 3微信 4线下
		tytTransportOrders.setPayAmount(new BigDecimal(orderBean.getMoney()).movePointRight(2).longValue());
//		tytTransportOrders.setPayFeeAmount(MoneyTools.getBankInterest(orderBean.getMoney() + "", infoRate).movePointRight(2).longValue());// 支付手续费金额单位分
		tytTransportOrders.setPayEndTime(todayDate);// 支付完成时间
		tytTransportOrders.setAgreeTime(todayDate);// 同意装货时间（成交时间）
		tytTransportOrders.setLoadTime(todayDate);// 装货完成时间
		tytTransportOrders.setRefuseTime(null);// 拒绝装货完成时间
		tytTransportOrders.setMtime(todayDate);
		this.add(tytTransportOrders);
		return tytTransportOrders;
	}


	/**
	 * @Description 更新信息费表的接单状态 rob_status 信息费状态 cost_status
	 * <AUTHOR>
	 * @Date  2018/12/29 17:20
	 * @Param [orderId, robStatus]
	 * @return int
	 **/
	@Override
	public int updateTransportOrdersStatus(Long orderId, String robStatus, Integer costStatus,String orderNewStatus,Integer freightStatus, Integer payServiceCharge,Map<String, String> reqMap) {
		String loadingStatus = reqMap.get("loadingStatus");
		String loadingChildStatus = reqMap.get("loadingChildStatus");
		//更新信息费表的接单状态
		if(payServiceCharge != null && payServiceCharge == 0){
			payServiceCharge = null;
		}
		//更新订单表信息
		String sql;
		if("1".equals(loadingStatus)){
			sql = "update tyt_transport_orders set loading_status=1,loading_child_status = :loadingChildStatus,load_time=now(), rob_status = :robStatus,cost_status = :costStatus, pay_service_charge =:payServiceCharge, order_new_status = :orderNewStatus, freight_status = :freightStatus where id = :orderId";
		}else{
			sql = "update tyt_transport_orders set loading_status=0,loading_child_status = :loadingChildStatus,rob_status = :robStatus,cost_status = :costStatus, pay_service_charge =:payServiceCharge, order_new_status = :orderNewStatus, freight_status = :freightStatus where id = :orderId";
		}
		Map<String, Object> map = new HashMap<String,Object>();
		map.put("robStatus",robStatus);
		map.put("costStatus",costStatus);
		map.put("orderId",orderId);
		map.put("payServiceCharge",payServiceCharge);
		map.put("loadingChildStatus",loadingChildStatus);
		map.put("orderNewStatus",orderNewStatus);
		map.put("freightStatus",freightStatus);
		int result = this.getBaseDao().executeUpdateSql(sql,map);
		TytTransportOrders byId = this.getById(orderId);
		if(ObjectUtil.equal(byId.getIsDealCar(),1)){
			this.getBaseDao().executeUpdateSql("update tyt_transport_orders set is_deal_car = 0 where id = ? ", new Object[]{orderId});
		}
		logger.info("信息费表的接单状态更新成功！更新成功条数：{}",result);
		return result;
	}

	/**
	 * @Description  更新运单表的信息费状态 info_status
	 * <AUTHOR>
	 * @Date  2018/12/29 17:27
	 * @Param [tsOrderNo, infoStatus]
	 * @return int
	 **/
	@Override
	public int updateTransportWayBillsStatus(String tsOrderNo, String infoStatus) {
		//更新信息费表的接单状态
		String sql = "update tyt_transport_waybill set info_status = :infoStatus where ts_order_no = :tsOrderNo";
		Map<String, Object> map = new HashMap<String,Object>();
		map.put("infoStatus",infoStatus);
		map.put("tsOrderNo",tsOrderNo);

		int result = this.getBaseDao().executeUpdateSql(sql,map);
		logger.info("运单表的信息费运单状态更新成功！更新成功条数：{}",result);
		return result;
	}

	/**
	 * 信息费是否已自动收款
	 * @param srcMsgId
	 * @return
	 */
	@Override
	public boolean moneyIsRecieved(Long srcMsgId) {
		String sql = "select * from tyt_transport_orders where ts_id = ? and cost_status = 45";
		Object[] params = {srcMsgId};
		List<TytTransportOrders> ordersList = this.getBaseDao().queryForList(sql, params);
		return ordersList != null && ordersList.size() > 0;
	}

	@Override
	public List<TytTransportOrders> getFrozenTransportOrders(Long userId) {
		String sql = "SELECT * FROM tyt_transport_orders tro WHERE tro.user_id = ? and tro.cost_status IN ( 15, 20, 21, 25,30) ;";
		Object[] params = {userId};
		List<TytTransportOrders> ordersList = this.getBaseDao().queryForList(sql, params);
		return ordersList;
	}

	@Override
	public TytTransportOrders getTransportOrdersByOrderId(Long orderId) {
		String sql = "SELECT * FROM tyt_transport_orders WHERE id = ?;";
		Object[] params = {orderId};
		List<TytTransportOrders> ordersList = this.getBaseDao().queryForList(sql, params);
		if(ordersList!=null&&ordersList.size()>0){
			return ordersList.get(0);
		}
		return null;
	}

	/**
	 *  订金列表csv文件头部数据与列表数据拼接的方法
	 * @param content 导出内容
	 * @param orderList 订金列表
	 * @return void
	 */
	@Override
	public void orderListToCvsString(StringBuilder content, List<TransportOrderResultBean> orderList) {
		// 行分隔符
		String CSV_RN = "\r\n";
		for (TransportOrderResultBean orderBean : orderList) {
			//ID
			content.append(orderBean.getId() == null?"":orderBean.getId()).append(",");
			//货源编号
			content.append(orderBean.getTsId()== null?"":orderBean.getTsId()).append(",");
			//货物内容
			content.append(StringUtils.isBlank(orderBean.getTaskContent())?"":orderBean.getTaskContent()).append(",");
			//找车方式 1电议 2 一口价
			String publishType="";
			if (StringUtils.isNotBlank(orderBean.getPublishType())){
				if ("1".equals(orderBean.getPublishType())){
					publishType = "电议";
				}else if ("2".equals(orderBean.getPublishType())){
					publishType = "一口价";
				}
			}
			content.append(publishType).append(",");
			//货源类型 是否是优车货源（0:否 1：是）
			String excellentGoods="";
			if (orderBean.getExcellentGoods()!=null){
				if (1!=orderBean.getExcellentGoods()){
					excellentGoods = "普通货源";
				}else if (1==orderBean.getExcellentGoods()){
					excellentGoods = "优车货源";
				}
			}
			content.append(excellentGoods).append(",");
			//货源来源 1货主 2调度客服 3个人货主 4:运满满货源
			String sourceType="";
			if (orderBean.getSourceType()!=null&&orderBean.getExcellentGoods()!=null){
				 if (4==orderBean.getSourceType()&&orderBean.getExcellentGoods()==1){
					sourceType = "满帮";
				}else if(4!=orderBean.getSourceType()&&orderBean.getExcellentGoods()==1){
					 sourceType = "特运通";
				 }
			}
			content.append(sourceType).append(",");
			//业务单号
			content.append(StringUtils.isBlank(orderBean.getTsOrderNo())? "" :orderBean.getTsOrderNo()).append(",");
			//运满满订单号
			content.append(StringUtils.isBlank(orderBean.getThirdPartyPlatformOrderNo())? "" :orderBean.getThirdPartyPlatformOrderNo()).append(",");
			//订单号
			content.append(StringUtils.isBlank(orderBean.getOrderId())? "" :orderBean.getOrderId()).append(",");
			//下单时间
			content.append(orderBean.getCreateTime() == null? "" :TimeUtil.formatDateTime(orderBean.getCreateTime())).append(",");
			//接单状态 0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）12车方取消订单    13异常处理完成 ',
			String robStatus="";
			if (StringUtils.isNotBlank(orderBean.getRobStatus())){
				if ("0".equals(orderBean.getRobStatus())){
					robStatus = "待接单";
				}else if ("1".equals(orderBean.getRobStatus())){
					robStatus = "接单成功";
				}else if ("2".equals(orderBean.getRobStatus())){
					robStatus= "货主拒绝";
				}else if ("3".equals(orderBean.getRobStatus())){
					robStatus= "系统拒绝";
				}else if ("4".equals(orderBean.getRobStatus())){
					robStatus= "同意装货";
				}else if ("5".equals(orderBean.getRobStatus())){
					robStatus= "车主装货完成";
				}else if ("6".equals(orderBean.getRobStatus())){
					robStatus= "系统装货完成";
				}else if ("7".equals(orderBean.getRobStatus())){
					robStatus= "异常上报";
				}else if ("8".equals(orderBean.getRobStatus())){
					robStatus= "货主撤销货源退款";
				}else if ("9".equals(orderBean.getRobStatus())){
					robStatus= "系统撤销货源退款";
				}else if ("10".equals(orderBean.getRobStatus())){
					robStatus= "车主取销装货";
				}else if ("11".equals(orderBean.getRobStatus())){
					robStatus= "接单失败";
				}else if ("12".equals(orderBean.getRobStatus())){
					robStatus= "车方取消订单";
				}else if ("13".equals(orderBean.getRobStatus())){
					robStatus= "异常处理完成";
				}
			}
			content.append(robStatus).append(",");
			//订金状态 10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
			String costStatus="";
			if (StringUtils.isNotBlank(orderBean.getCostStatus())){
				if ("5".equals(orderBean.getCostStatus())){
					costStatus = "货源撤销退款";
				}else if ("6".equals(orderBean.getCostStatus())){
					costStatus = "待支付关闭";
				}else if ("10".equals(orderBean.getCostStatus())){
					costStatus = "待支付";
				}else if ("15".equals(orderBean.getCostStatus())&& "0".equals(orderBean.getDelayStatus()) ){
					costStatus = "已支付";
				}else if ("20".equals(orderBean.getCostStatus())){
					costStatus= "已冻结";
				}else if ("21".equals(orderBean.getCostStatus())&& "0".equals(orderBean.getDelayStatus()) ){
					costStatus= "拒绝退款";
				}else if ("25".equals(orderBean.getCostStatus())){
					costStatus= "异常上报";
				}else if ("30".equals(orderBean.getCostStatus())){
					costStatus= "退款中";
				}else if ("35".equals(orderBean.getCostStatus())){
					costStatus= "已退款";
				}else if ("40".equals(orderBean.getCostStatus())){
					costStatus= "已打款";
				}else if ("45".equals(orderBean.getCostStatus())){
					costStatus= "自动收款";
				}else if ("50".equals(orderBean.getCostStatus())){
					costStatus= "异常完成";
				}else if ("15".equals(orderBean.getCostStatus())&& "1".equals(orderBean.getDelayStatus()) ){
					costStatus = "延迟付款";
				}else if ("21".equals(orderBean.getCostStatus())&& "2".equals(orderBean.getDelayStatus()) ){
					costStatus= "拒绝退款延迟";
				}
			}
			content.append(costStatus).append(",");
			//支付状态
			String payStatus="";
			if (StringUtils.isNotBlank(orderBean.getPayStatus())){
				if ("0".equals(orderBean.getPayStatus())){
					payStatus = "待支付";
				}else if ("1".equals(orderBean.getPayStatus())){
					payStatus = "支付失败";
				}else if ("2".equals(orderBean.getPayStatus())){
					payStatus= "支付成功";
				}
			}
			content.append(payStatus).append(",");
			//支付渠道
			content.append(StringUtils.isBlank(orderBean.getPayChannelName())? "" :orderBean.getPayChannelName()).append(",");
			//订金金额(元) 订金是否退还（0不退还；1退还）
			String payAmount=String.valueOf(orderBean.getPayAmount()/100);
			if (orderBean.getRefundFlag()!=null){
				if (0==orderBean.getRefundFlag()){
					payAmount = payAmount+"(不退还)";
				}else if (1==orderBean.getRefundFlag()){
					payAmount = payAmount+"(退还)";
				}
			}
			content.append(payAmount).append(",");
			//折扣金额=订单总金额-支付金额
			content.append(orderBean.getTotalOrderAmount() == null||orderBean.getPayAmount()==null? "" :orderBean.getTotalOrderAmount()/100-orderBean.getPayAmount()/100).append(",");
			//券面额
			content.append(orderBean.getCouponAmount() == null? "" :orderBean.getCouponAmount()/100).append(",");
			//用户优惠券ID
			content.append(orderBean.getCouponId() == null? "" :orderBean.getCouponId()).append(",");
			//支付手续费(元)
			content.append(orderBean.getPayFeeAmount()==null? "" :orderBean.getPayFeeAmount()/100).append(",");
			//运费(元)
			content.append(orderBean.getCarriageFee()==null? "" :orderBean.getCarriageFee()).append(",");
			//退款状态
			String refundStatus="";
			if (StringUtils.isNotBlank(orderBean.getRefundStatus())){
				if ("1".equals(orderBean.getRefundStatus())){
					refundStatus = "退款中";
				}else if ("2".equals(orderBean.getRefundStatus())){
					refundStatus = "退款成功";
				}else if ("3".equals(orderBean.getRefundStatus())){
					refundStatus= "退款失败";
				}
			}
			content.append(refundStatus).append(",");
			//退款金额
			content.append(orderBean.getRefundAmount()==null? "" :orderBean.getRefundAmount()/100).append(",");
			//车方订金分配金额
			content.append(orderBean.getCarAmount()==null? "" :orderBean.getCarAmount()/100).append(",");
			//货方订金分配金额
			content.append(orderBean.getGoodsAmount()==null? "" :orderBean.getGoodsAmount()/100).append(",");
			//退款原因
			content.append(StringUtils.isBlank(orderBean.getRefundReason())? "" :"\""+orderBean.getRefundReason()+"\"").append(",");
			//退款到账时间
			content.append(orderBean.getRefundArrivalTime() == null? "" :TimeUtil.formatDateTime(orderBean.getRefundArrivalTime())).append(",");
			content.append(CSV_RN);
		}
	}

	@Override
	public void exportInfoFee(HttpServletResponse response, PageBean pageBean, TransportOrderQueryBean conditionBean) throws Exception {
		String uploadCellphone = conditionBean.getUploadCellphone();
		Long conditionUserId = null;
		if (StringUtils.isNotBlank(uploadCellphone)) {
			conditionUserId = userService.getIdByCellPhone(uploadCellphone);
			if (conditionUserId == null) {
				logger.info("exportInfoFee conditionUserId  null uploadCellphone: {} " ,uploadCellphone);
				return;
			}
		}

		//查询条件及参数值
		StringBuffer conditionBuffer=new StringBuffer();
		List<Object> conditionParams=new ArrayList<Object>();
		buildConditionBufferAndParams(conditionBean, conditionBuffer, conditionParams, conditionUserId);

		//查询财务数据的sql语句
		StringBuffer countBuffer=new StringBuffer("select count(*) from tyt_transport_orders o  left join tyt_transport_order_snapshot ts on  ts.order_id = o.id left join tyt_transport_technical_order tto on tto.technical_service_no = o.technical_service_no  " +
				"   left join tyt_order_carry_point occ on o.id = occ.order_id where 1=1");
		countBuffer.append(conditionBuffer);

		BigInteger total=this.getBaseDao().query(countBuffer.toString(), conditionParams.toArray());
        logger.info("exportInfoFee total: {}",total );
		if (Objects.isNull(total) || total.longValue() == 0L) {
			return;
		}
		pageBean.setRowCount(total.longValue());
		StringBuffer selectBuffer=new StringBuffer(
				"select o.id,o.task_content taskContent,o.ts_order_no tsOrderNo,o.thirdparty_platform_order_no thirdPartyPlatformOrderNo,o.pay_cell_phone payCellPhone,"
						+ "o.rob_status robStatus,o.pay_status payStatus,o.delay_status delayStatus,o.create_time createTime,o.pay_type payType,"
						+ "o.carriage_fee carriageFee,IFNULL(o.total_order_amount,0) totalOrderAmount,IFNULL(o.coupon_amount,0) couponAmount,o.pay_amount payAmount,IFNULL(o.refund_amount,0) refundAmount,IFNULL(o.car_amount,0) carAmount,IFNULL(o.goods_amount,0) goodsAmount,o.pay_fee_amount payFeeAmount,o.ts_id tsId,o.cost_status costStatus,"
						+ "o.upload_cellphone uploadCellphone,o.user_id userId,o.pay_user_id payUserId,o.op_id opId,o.op_name opName,o.pay_order_no payOrderNo, "
						+ "o.pay_sub_channel paySubChannel,ts.publish_type publishType,ts.source_type sourceType,"
						+ "o.refund_arrival_time refundArrivalTime,o.refund_status refundStatus,o.refund_err_msg refundErrMsg, refund_reason as refundReason,o.refund_flag refundFlag,"
						+ "o.car_service_amount carServiceAmount,o.mtime mtime,o.order_new_status orderNewStatus,o.cancel_status cancelStatus,o.delay_refund_status delayRefundStatus,o.de_refund_dueDate deRefundDueDate,o.technical_service_no technicalServiceNo,"
						+ "tto.technical_service_fee tecServiceFee,tto.status tecFeePayStatus,tto.ctime tecFeePayTime,"
						+ "tto.refund_amount tecFeeRefundAmount,tto.refund_status tecFeeRefundStatus,tto.refund_time tecFeeRefundTime,occ.is_member isMember "
						+ " from tyt_transport_orders o left join tyt_transport_order_snapshot ts on ts.order_id = o.id "
						+ " left join tyt_transport_technical_order tto on tto.technical_service_no = o.technical_service_no" +
						"  left join tyt_order_carry_point occ on o.id = occ.order_id where 1=1 ");
		selectBuffer.append(conditionBuffer).append(" order by o.id desc");
		//组装hibernateMap参数
		Map<String, Type> typeMap = createHibernateMap();
        //设置导出相关参数
		String createTimeStr = TimeUtil.formatDateTimeYYYYHHMM(new Date());
		//设置相关参数
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		// 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
		String fileName = URLEncoder.encode("信息费订单导出" + createTimeStr, "UTF-8");
		response.setHeader("Content-disposition", "attachment;filename="+ fileName + ".xlsx");

		ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(response.getOutputStream(), InfoFeeOrderExport.class);
		ExcelWriter excelWriter = excelWriterBuilder.build();

		ExcelWriterSheetBuilder sheetBuilder = excelWriterBuilder.sheet("信息费订单导出");
		WriteSheet writeSheet = sheetBuilder.build();

		try {
			while(true){
				List <TransportOrderResultBean> transportOrderList = this.getBaseDao().search(selectBuffer.toString(), typeMap, TransportOrderResultBean.class, conditionParams.toArray(), pageBean.getCurrentPage(), pageBean.getPageSize());
				//获取对应的最近一笔支付订单的订单号，设置支付订单号，给前端使用
				if(CollectionUtils.isEmpty(transportOrderList)){
					logger.info("exportInfoFee conditionUserId transportOrderList currentPage: {} " ,pageBean.getCurrentPage());
					return;
				}
				logger.info("exportInfoFee transportOrderList currentPage: {} size: {}", pageBean.getCurrentPage(), transportOrderList.size());

				List<Order> orderList= orderService.listOrderByPayOrderAndCellPhone(transportOrderList);
                List<TransportMain>  transportMainList = transportMainService.listTransportMain(transportOrderList);
                List<PromoCouponWriteoff> promoCouponWriteoffList = promoCouponWriteOffService.listPromoCouponWriteoffByOrderIdList(transportOrderList);

				Map<String, Order> payOrderNoPhoneOrderMap = orderList.stream()
						.collect(Collectors.toMap(order -> order.getPayOrderNo() + "," + order.getCellPhone(), order -> order, (k1, k2) -> k1));
				Map<Long, TransportMain> tsIdTransportMainMap = transportMainList.stream()
						.collect(Collectors.toMap(TransportMain::getId, transportMain -> transportMain, (k1, k2) -> k1));
				Map<String, PromoCouponWriteoff> orderIdPromoCouponMap = promoCouponWriteoffList.stream()
						.collect(Collectors.toMap(PromoCouponWriteoff::getOrderId, promoCouponWriteoff -> promoCouponWriteoff, (k1, k2) -> k1));

				for (TransportOrderResultBean transportOrderResultBean : transportOrderList) {
					//关联订单号
					String payOrderNo = transportOrderResultBean.getPayOrderNo();
					//支付人手机号
					String payCellPhone = transportOrderResultBean.getPayCellPhone();
					//根据关联订单号和手机号查询支付订单信息
					Order order = payOrderNoPhoneOrderMap.get(payOrderNo + "," + payCellPhone);
					if(order != null){
						//支付订单号
						String orderId = order.getOrderId();
						transportOrderResultBean.setOrderId(orderId);
					}
					if ("4".equals(transportOrderResultBean.getPayType())){
						transportOrderResultBean.setPayChannelName("线下支付");
					}else{
						if (StringUtils.isNotBlank(transportOrderResultBean.getPaySubChannel())){
							if (transportOrderResultBean.getPaySubChannel().startsWith("WECHAT")){
								transportOrderResultBean.setPayChannelName("微信");
							}else if (transportOrderResultBean.getPaySubChannel().startsWith("ALIPAY")){
								transportOrderResultBean.setPayChannelName("支付宝");
							}else if (transportOrderResultBean.getPaySubChannel().startsWith("AGRT")){
								transportOrderResultBean.setPayChannelName("银行卡");
							}else if (transportOrderResultBean.getPaySubChannel().startsWith("BALANCE")){
								transportOrderResultBean.setPayChannelName("余额");
							}
						}
					}

					TransportMain transport = tsIdTransportMainMap.get(transportOrderResultBean.getTsId());
					if (transport != null) {
						transportOrderResultBean.setExcellentGoods(transport.getExcellentGoods());
						transportOrderResultBean.setSourceType(transport.getSourceType());
					}
					//当优惠券金额大于0时 查看获取用户优惠券Id
					if(transportOrderResultBean.getCouponAmount()!=null&&transportOrderResultBean.getCouponAmount()>0){
						PromoCouponWriteoff couponWriteOff = orderIdPromoCouponMap.get(transportOrderResultBean.getId().toString());
						if(couponWriteOff!=null){
							transportOrderResultBean.setCouponId(couponWriteOff.getCouponId().longValue());
						}
					}

					Long refundCarServiceAmount = 0L;
					Long dispatchRefundAmount = 0L;
					Long exRefundAmount = 0L;
					Long completeRefundAmount = 0L;
					//订金状态
					String costStatus = transportOrderResultBean.getCostStatus();
					Date tecFeeRefundTime = null;
					if(StringUtils.isNotBlank(costStatus)){
						//已退款
						if(35 == Integer.parseInt(costStatus)){
							dispatchRefundAmount = transportOrderResultBean.getTecFeeRefundAmount();
							if(dispatchRefundAmount == null){
								dispatchRefundAmount = 0L;
							}
							tecFeeRefundTime = transportOrderResultBean.getTecFeeRefundTime();
						}else if(50 == Integer.parseInt(costStatus)){ //异常上报处理完成
							exRefundAmount = transportOrderResultBean.getCarServiceAmount();
							if(exRefundAmount == null){
								exRefundAmount = 0L;
							}
							tecFeeRefundTime = transportOrderResultBean.getMtime();
						}
						//完单退还技术服务费(投诉入口)
						TytTransportWaybillEx transportWaybillEx = transportWaybillExService.getTransportWaybillExByOrderIdAndOrderType(transportOrderResultBean.getId(), 1);
						if (Objects.nonNull(transportWaybillEx)){
							TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditByExId(transportWaybillEx.getId());
							if (Objects.nonNull(techRefundFinanceAudit)){
								BigDecimal refundAmount = techRefundFinanceAudit.getRefundAmount();
								if(refundAmount != null){
									completeRefundAmount = refundAmount.longValue();
									tecFeeRefundTime = techRefundFinanceAudit.getAuditTime();
								}
							}
						}
						//完单退还技术服务费(客服新增入口)
						TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditByOrderId(transportOrderResultBean.getId());
						if (Objects.nonNull(techRefundFinanceAudit)){
							BigDecimal refundAmount = techRefundFinanceAudit.getRefundAmount();
							if(refundAmount != null){
								completeRefundAmount = refundAmount.longValue();
								tecFeeRefundTime = techRefundFinanceAudit.getAuditTime();
							}
						}
					}
					refundCarServiceAmount = dispatchRefundAmount  + exRefundAmount + completeRefundAmount;
					if(refundCarServiceAmount > 0L){
						String refundTecServiceFeeInfo = refundCarServiceAmount + "元（业务退回" + dispatchRefundAmount  + "元）（异常上报退回" + exRefundAmount + "元）（完单后退回" + completeRefundAmount + "元）";
						transportOrderResultBean.setRefundTecServiceFeeInfo(refundTecServiceFeeInfo);
						transportOrderResultBean.setTecFeeRefundTime(tecFeeRefundTime);
					}
					transportOrderResultBean.setCompleteRefundAmount(completeRefundAmount);
					transportOrderResultBean.setRefundCarServiceAmount(refundCarServiceAmount);


					String carStatusDesc = infoFeeStatusDesc(false, false, transportOrderResultBean.getRefundFlag(), String.valueOf(transportOrderResultBean.getCostStatus()),
							transportOrderResultBean.getDelayStatus(), transportOrderResultBean.getCancelStatus(), transportOrderResultBean.getDelayRefundStatus(), transportOrderResultBean.getDeRefundDueDate(),
							transportOrderResultBean.getOrderNewStatus(), OrderDataType.ORDER_LIST.getCode());

					String goodsStatusDesc = infoFeeStatusDesc(false, true, transportOrderResultBean.getRefundFlag(), String.valueOf(transportOrderResultBean.getCostStatus()),
							transportOrderResultBean.getDelayStatus(), transportOrderResultBean.getCancelStatus(), transportOrderResultBean.getDelayRefundStatus(), transportOrderResultBean.getDeRefundDueDate(),
							transportOrderResultBean.getOrderNewStatus(), OrderDataType.ORDER_LIST.getCode());

					String statusDesc = "车方：" + carStatusDesc + "\n货方：" + goodsStatusDesc;
					transportOrderResultBean.setStatusDesc(statusDesc);
				}
				List<InfoFeeOrderExport> infoFeeOrderExportList = convertInfoFeeOrderExportList(transportOrderList);
				excelWriter.write(infoFeeOrderExportList, writeSheet);
				pageBean.setCurrentPage(pageBean.getCurrentPage()+1);
			}
		} catch (Exception e) {
			logger.error("导出信息费订单异常", e);
		} finally {
			excelWriter.finish();
		}


	}

	/**
	 * 对象转换
	 * @param transportOrderList 订单列表
	 * @return List<InfoFeeOrderExport> 导出对象
	 */
	private  List<InfoFeeOrderExport> convertInfoFeeOrderExportList(List<TransportOrderResultBean> transportOrderList) {

		//查询所有的代调会员
		List<Long> dispatchUserIdList = tytDispatchCompanyService.listAllValidUserID();
		logger.info("listAllValidUserID size: {}", dispatchUserIdList.size());
		//转换
		return transportOrderList.stream()
				.map(orderBean -> {
					InfoFeeOrderExport infoFeeOrderExport = new InfoFeeOrderExport();
					infoFeeOrderExport.setId(orderBean.getId() == null ? "" : orderBean.getId() + "");
					infoFeeOrderExport.setTsId(orderBean.getTsId() == null ? "" : orderBean.getTsId() + "");
					infoFeeOrderExport.setTaskContent(StringUtils.isBlank(orderBean.getTaskContent()) ? "" : orderBean.getTaskContent());
					String publishType = "";
					//找车方式 1电议 2 一口价
					if (StringUtils.isNotBlank(orderBean.getPublishType())) {
						if ("1".equals(orderBean.getPublishType())) {
							publishType = "电议";
						} else if ("2".equals(orderBean.getPublishType())) {
							publishType = "一口价";
						}
					}
					infoFeeOrderExport.setPublishType(publishType);
					String excellentGoods = "";
					if (orderBean.getExcellentGoods() != null) {
						if (2 == orderBean.getExcellentGoods()) {
							excellentGoods = "专车货源";
						} else if (1 == orderBean.getExcellentGoods()) {
							excellentGoods = "优车货源";
						}else{
							excellentGoods = "普通货源";
						}
					}
					infoFeeOrderExport.setExcellentGoods(excellentGoods);

					//货源来源 1货主 2调度客服 3个人货主 4:运满满货源
					String sourceType = "";
					if (orderBean.getSourceType() != null && orderBean.getExcellentGoods() != null) {
						if (4 == orderBean.getSourceType() && orderBean.getExcellentGoods() == 1) {
							sourceType = "满帮";
						} else if (4 != orderBean.getSourceType() && orderBean.getExcellentGoods() == 1) {
							sourceType = "特运通";
						}
					}
					infoFeeOrderExport.setSourceType(sourceType);
					//业务单号
					infoFeeOrderExport.setTsOrderNo(StringUtils.isBlank(orderBean.getTsOrderNo()) ? "" : orderBean.getTsOrderNo());
					//运满满订单号
					infoFeeOrderExport.setThirdPartyPlatformOrderNo(StringUtils.isBlank(orderBean.getThirdPartyPlatformOrderNo()) ? "" : orderBean.getThirdPartyPlatformOrderNo());
					infoFeeOrderExport.setOrderId(StringUtils.isBlank(orderBean.getOrderId()) ? "" : orderBean.getOrderId());
					//下单时间
					infoFeeOrderExport.setCreateTime(orderBean.getCreateTime() == null ? "" : TimeUtil.formatDateTime(orderBean.getCreateTime()));
					//接单状态 0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）12车方取消订单    13异常处理完成 ',
					String robStatus = "";
					if (StringUtils.isNotBlank(orderBean.getRobStatus())) {
						if ("0".equals(orderBean.getRobStatus())) {
							robStatus = "待接单";
						} else if ("1".equals(orderBean.getRobStatus())) {
							robStatus = "接单成功";
						} else if ("2".equals(orderBean.getRobStatus())) {
							robStatus = "货主拒绝";
						} else if ("3".equals(orderBean.getRobStatus())) {
							robStatus = "系统拒绝";
						} else if ("4".equals(orderBean.getRobStatus())) {
							robStatus = "同意装货";
						} else if ("5".equals(orderBean.getRobStatus())) {
							robStatus = "车主装货完成";
						} else if ("6".equals(orderBean.getRobStatus())) {
							robStatus = "系统装货完成";
						} else if ("7".equals(orderBean.getRobStatus())) {
							robStatus = "异常上报";
						} else if ("8".equals(orderBean.getRobStatus())) {
							robStatus = "货主撤销货源退款";
						} else if ("9".equals(orderBean.getRobStatus())) {
							robStatus = "系统撤销货源退款";
						} else if ("10".equals(orderBean.getRobStatus())) {
							robStatus = "车主取销装货";
						} else if ("11".equals(orderBean.getRobStatus())) {
							robStatus = "接单失败";
						} else if ("12".equals(orderBean.getRobStatus())) {
							robStatus = "车方取消订单";
						} else if ("13".equals(orderBean.getRobStatus())) {
							robStatus = "异常处理完成";
						}
					}
					infoFeeOrderExport.setRobStatus(robStatus);
					//订金状态 10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
					String costStatus = "";
					if (StringUtils.isNotBlank(orderBean.getCostStatus())) {
						if ("5".equals(orderBean.getCostStatus())) {
							costStatus = "货源撤销退款";
						} else if ("6".equals(orderBean.getCostStatus())) {
							costStatus = "待支付关闭";
						} else if ("10".equals(orderBean.getCostStatus())) {
							costStatus = "待支付";
						} else if ("15".equals(orderBean.getCostStatus()) && 0 == orderBean.getDelayStatus()) {
							costStatus = "已支付";
						} else if ("20".equals(orderBean.getCostStatus())) {
							costStatus = "已冻结";
						} else if ("21".equals(orderBean.getCostStatus()) && 0 == orderBean.getDelayStatus()) {
							costStatus = "拒绝退款";
						} else if ("25".equals(orderBean.getCostStatus())) {
							costStatus = "异常上报";
						} else if ("30".equals(orderBean.getCostStatus())) {
							costStatus = "退款中";
						} else if ("35".equals(orderBean.getCostStatus())) {
							costStatus = "已退款";
						} else if ("40".equals(orderBean.getCostStatus())) {
							costStatus = "已打款";
						} else if ("45".equals(orderBean.getCostStatus())) {
							costStatus = "自动收款";
						} else if ("50".equals(orderBean.getCostStatus())) {
							costStatus = "异常完成";
						} else if ("15".equals(orderBean.getCostStatus()) &&  1 == orderBean.getDelayStatus()) {
							costStatus = "延迟付款";
						} else if ("21".equals(orderBean.getCostStatus()) &&  2 == orderBean.getDelayStatus()) {
							costStatus = "拒绝退款延迟";
						}
					}
					infoFeeOrderExport.setCostStatus(costStatus);
					//支付状态
					String payStatus = "";
					if (StringUtils.isNotBlank(orderBean.getPayStatus())) {
						if ("0".equals(orderBean.getPayStatus())) {
							payStatus = "待支付";
						} else if ("1".equals(orderBean.getPayStatus())) {
							payStatus = "支付失败";
						} else if ("2".equals(orderBean.getPayStatus())) {
							payStatus = "支付成功";
						}
					}
					infoFeeOrderExport.setPayStatus(payStatus);
					//支付渠道
					infoFeeOrderExport.setPayChannelName(StringUtils.isBlank(orderBean.getPayChannelName()) ? "" : orderBean.getPayChannelName());
					//订金金额(元) 订金是否退还（0不退还；1退还）
					if(Objects.nonNull(orderBean.getPayAmount())){
						String payAmount = String.valueOf(orderBean.getPayAmount() / 100);
						if (orderBean.getRefundFlag() != null) {
							if (0 == orderBean.getRefundFlag()) {
								payAmount = payAmount + "(不退还)";
							} else if (1 == orderBean.getRefundFlag()) {
								payAmount = payAmount + "(退还)";
							}
						}
						infoFeeOrderExport.setPayAmount(payAmount);
					}

					infoFeeOrderExport.setDiscountAmount(orderBean.getTotalOrderAmount() == null || orderBean.getPayAmount() == null ? "" : orderBean.getTotalOrderAmount() / 100 - orderBean.getPayAmount() / 100 + "");
					infoFeeOrderExport.setCouponAmount(orderBean.getCouponAmount() == null ? "" : orderBean.getCouponAmount() / 100 + "");
					infoFeeOrderExport.setCouponId(orderBean.getCouponId() == null ? "" : orderBean.getCouponId() + "");
					//支付手续费(元)
					infoFeeOrderExport.setPayFeeAmount(orderBean.getPayFeeAmount() == null ? "" : orderBean.getPayFeeAmount() / 100 + "");
					//运费(元)
					infoFeeOrderExport.setCarriageFee(orderBean.getCarriageFee() == null ? "" : orderBean.getCarriageFee() + "");
					//退款状态
					String refundStatus = "";
					if (StringUtils.isNotBlank(orderBean.getRefundStatus())) {
						if ("1".equals(orderBean.getRefundStatus())) {
							refundStatus = "退款中";
						} else if ("2".equals(orderBean.getRefundStatus())) {
							refundStatus = "退款成功";
						} else if ("3".equals(orderBean.getRefundStatus())) {
							refundStatus = "退款失败";
						}
					}
					infoFeeOrderExport.setRefundStatus(refundStatus);
					if(StringUtils.isNotBlank(orderBean.getRefundStatus()) && "2".equals(orderBean.getRefundStatus())){
						//退款金额
						infoFeeOrderExport.setRefundAmount(orderBean.getRefundAmount() == null ? "" : orderBean.getRefundAmount() / 100 + "");
						//退款原因
						infoFeeOrderExport.setRefundReason(orderBean.getRefundReason() == null ? "" : orderBean.getRefundReason() + "");
						//退款到账时间
						infoFeeOrderExport.setRefundArrivalTime(orderBean.getRefundArrivalTime() == null ? "" : TimeUtil.formatDateTime(orderBean.getRefundArrivalTime()));
					}

					//车方订金分配金额
					infoFeeOrderExport.setCarAmount(orderBean.getCarAmount() == null ? "" : orderBean.getCarAmount() / 100 + "");
					//货方订金分配金额
					infoFeeOrderExport.setGoodsAmount(orderBean.getGoodsAmount() == null ? "" : orderBean.getGoodsAmount() / 100 + "");
                    //技术服务费
					infoFeeOrderExport.setTecServiceFee(orderBean.getTecServiceFee() == null ? "" : orderBean.getTecServiceFee() + "");

                    //技术服务费支付状态
					String tecFeePayStatus = "";
					if (Objects.nonNull(orderBean.getTecFeePayStatus())) {
						if (0 == orderBean.getTecFeePayStatus()) {
							tecFeePayStatus = "待支付";
						} else if (1 == orderBean.getTecFeePayStatus()) {
							tecFeePayStatus = "支付失败";
						} else if (2 == orderBean.getTecFeePayStatus()) {
							tecFeePayStatus = "支付成功";
						}
						infoFeeOrderExport.setTecFeePayStatus(tecFeePayStatus);
					}
					infoFeeOrderExport.setTecFeePayTime(orderBean.getTecFeePayTime() == null ? "" : TimeUtil.formatDateTime(orderBean.getTecFeePayTime()));
					//退款金额
					infoFeeOrderExport.setTecFeeRefundAmount(orderBean.getTecFeeRefundAmount() == null ? "" : orderBean.getTecFeeRefundAmount() / 100 + "");

					String tecFeeRefundStatus = "";
					String refundTecServiceFeeInfo = orderBean.getRefundTecServiceFeeInfo();
					Long refundTecServiceAmount = extractRefundTecServiceAmount(refundTecServiceFeeInfo);
					if(refundTecServiceAmount > 0 ){
						tecFeeRefundStatus = "退款成功";
						infoFeeOrderExport.setTecFeeRefundTime(orderBean.getTecFeeRefundTime() == null ? "" : TimeUtil.formatDateTime(orderBean.getTecFeeRefundTime()));
					}else{
						tecFeeRefundStatus = "未退款";
					}
					infoFeeOrderExport.setTecFeeRefundStatus(tecFeeRefundStatus);


					//是否代调
					if(Objects.nonNull(orderBean.getSourceType())){
						if(TransportSourceTypeEnum.DISPATCH.getCode().equals(orderBean.getSourceType()) || TransportSourceTypeEnum.HX.getCode().equals(orderBean.getSourceType())){
							infoFeeOrderExport.setIsDispatch("是");
						}else if(TransportSourceTypeEnum.OWNER.getCode().equals(orderBean.getSourceType()) ){
							if(org.apache.commons.collections.CollectionUtils.isNotEmpty(dispatchUserIdList) && dispatchUserIdList.contains(orderBean.getUserId())){
								infoFeeOrderExport.setIsDispatch("是");
							}else{
								infoFeeOrderExport.setIsDispatch("否");
							}
						}
						else{
							infoFeeOrderExport.setIsDispatch("否");
						}
					}
					//货源来源
					if(Objects.nonNull(orderBean.getSourceType())){
						if(TransportSourceTypeEnum.DISPATCH.getCode().equals(orderBean.getSourceType())){
							infoFeeOrderExport.setTsourceType("调度客服");
						}else if(TransportSourceTypeEnum.YMM.getCode().equals(orderBean.getSourceType())){
							infoFeeOrderExport.setTsourceType("运满满货源");
						}else if(TransportSourceTypeEnum.HX.getCode().equals(orderBean.getSourceType())){
							infoFeeOrderExport.setTsourceType("宏信货源");
						}else{
							infoFeeOrderExport.setTsourceType("平台自有货源");
						}
					}
					//车方是否会员
					if (Objects.nonNull(orderBean.getIsMember()) && UserPermissionStatusEnum.VALID.getCode().equals(orderBean.getIsMember())) {
						infoFeeOrderExport.setIsMember("是");
					} else {
						infoFeeOrderExport.setIsMember("否");
					}

					//技术服务费总退款
					if(StringUtils.isNotEmpty(orderBean.getRefundTecServiceFeeInfo())){
						infoFeeOrderExport.setTecFeeRefundAmount(orderBean.getRefundTecServiceFeeInfo());
					}
					//技术服务费-业务退款
					if(Objects.nonNull(orderBean.getTecFeeRefundAmount())){
						infoFeeOrderExport.setTecFeeBizRefundAmount(orderBean.getTecFeeRefundAmount() + "");
					}
					//技术服务费-异常上报-车方
					if(Objects.nonNull(orderBean.getCarServiceAmount()) && StringUtils.isNotEmpty(costStatus) && "异常完成".equals(costStatus)){
						infoFeeOrderExport.setTecFeeExRefundAmount(orderBean.getCarServiceAmount() + "");
					}
					//技术服务费-完单后线上退款
					if(Objects.nonNull(orderBean.getCompleteRefundAmount())){
					   infoFeeOrderExport.setTecFeeComplaintOrderRefundAmount(orderBean.getCompleteRefundAmount() + "");
					}
					//车主id
					if(Objects.nonNull(orderBean.getPayUserId())){
						infoFeeOrderExport.setPayUserId(orderBean.getPayUserId() + "");
					}
					//订单状态
					String orderNewStatusDesc = orderNewStatusDesc(orderBean.getOrderNewStatus());
					infoFeeOrderExport.setOrderNewStatus(orderNewStatusDesc);
					//更新时间
					if(Objects.nonNull(orderBean.getMtime())){
						infoFeeOrderExport.setMtime(TimeUtil.formatDateTime(orderBean.getMtime()));
					}
					//订单状态描述
					if(StringUtils.isNotBlank(orderBean.getStatusDesc())){
						infoFeeOrderExport.setStatusDesc(orderBean.getStatusDesc());
					}
					//订金商户订单号
					if(StringUtils.isNotBlank(orderBean.getTsOrderNo())){
						Order order = orderService.getOrderByOrderNumAndStatus(orderBean.getTsOrderNo(), orderBean.getPayUserId(), 2);
						if(Objects.nonNull(order)){
							String payServiceOrderNo = order.getPayServiceOrderNo();
							infoFeeOrderExport.setMerchantOrderNo(payServiceOrderNo);

							//到账时间
							String thirdpartyOrderSerialNum = order.getThirdpartyOrderSerialNum();
							Example example = new Example(TytConfirmOrder.class);
							Example.Criteria criteria = example.createCriteria();
							criteria.andEqualTo("innerTradeNo", thirdpartyOrderSerialNum);
							criteria.andEqualTo("confirmStatus", 2);
							List<TytConfirmOrder> confirmOrderList = tytConfirmOrderMapper.selectByExample(example);
							if(CollectionUtils.isNotEmpty(confirmOrderList)){
								TytConfirmOrder tytConfirmOrder = confirmOrderList.get(0);
								if(Objects.nonNull(tytConfirmOrder)) {
									//担保交易确认内容
									String confirmContent = tytConfirmOrder.getConfirmContent();
									//根据担保交易确认内容中收款人账号，判断钱包类型
									if (StringUtils.isNotBlank(confirmContent) && StringUtils.isNotBlank(cashBackDdPayerId) && StringUtils.isNotBlank(cashBackCyPayerId)) {
										if (confirmContent.contains(cashBackDdPayerId) || confirmContent.contains(cashBackCyPayerId)) {
											infoFeeOrderExport.setArriveTime(TimeUtil.formatDateTime(tytConfirmOrder.getMtime()));
										}
									}
								}
							}
						}
					}
					//技术服务费商户订单号
				    if(StringUtils.isNotBlank(orderBean.getTechnicalServiceNo())){
						Order order = orderService.getOrderByOrderNumAndStatus(orderBean.getTechnicalServiceNo(), orderBean.getPayUserId(), 2);
						if(Objects.nonNull(order)){
							String payServiceOrderNo = order.getPayServiceOrderNo();
							infoFeeOrderExport.setTecMerchantOrderNo(payServiceOrderNo);
						}
					}
					//发货人ID
					if(Objects.nonNull(orderBean.getUserId())){
						infoFeeOrderExport.setPubUserId(String.valueOf(orderBean.getUserId()));
					}

					return infoFeeOrderExport;
				}).collect(Collectors.toList());
	}

	/**
	 * 订单状态描述信息
	 * @param orderNewStatus
	 * @return
	 */
	public static String orderNewStatusDesc(Integer orderNewStatus){
		if(orderNewStatus != null){
			switch (orderNewStatus) {
				case 5:
					return "待接单";
				case 10:
					return "待签署";
				case 15:
					return "待装货";
				case 20:
					return "待卸货/待收货";
				case 25:
					return "待收运费/待付运费";
				case 30:
					return "已完成";
				case 35:
					return "已取消";
				default:
					return "";
			}
		} else {
			return "";
		}

	}

	/**
	 * 提取退款金额前数字
	 *
	 * @param refundTecServiceFeeInfo
	 * @return Long
	 */
	private static Long extractRefundTecServiceAmount(String refundTecServiceFeeInfo) {
		if(StringUtils.isEmpty(refundTecServiceFeeInfo)){
			return 0L;
		}
		String regex = "^(\\d+).*元";
		java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
		java.util.regex.Matcher matcher = pattern.matcher(refundTecServiceFeeInfo);
		if (matcher.find()) {
			// 第一个括号中的匹配项是我们要找的结果
			String amount = matcher.group(1);
			return Long.parseLong(amount);
		}
		return 0L;
	}

	/**
	 * 构造查询参数
	 * @return
	 */
	private static Map<String, Type> createHibernateMap() {
		Map<String,Type> typeMap=new HashMap<String,Type>();
		typeMap.put("id", Hibernate.LONG);
		typeMap.put("taskContent", Hibernate.STRING);
		typeMap.put("tsOrderNo", Hibernate.STRING);
		typeMap.put("thirdPartyPlatformOrderNo", Hibernate.STRING);
		typeMap.put("payCellPhone", Hibernate.STRING);
		typeMap.put("robStatus", Hibernate.STRING);
		typeMap.put("payStatus", Hibernate.STRING);
		typeMap.put("createTime", Hibernate.TIMESTAMP);
		typeMap.put("payType", Hibernate.STRING);
		typeMap.put("carriageFee", Hibernate.LONG);
		typeMap.put("payAmount", Hibernate.LONG);
		typeMap.put("couponAmount", Hibernate.LONG);
		typeMap.put("totalOrderAmount", Hibernate.LONG);
		typeMap.put("payFeeAmount", Hibernate.LONG);
		typeMap.put("refundAmount", Hibernate.LONG);
		typeMap.put("carAmount", Hibernate.LONG);
		typeMap.put("goodsAmount", Hibernate.LONG);
		typeMap.put("tsId", Hibernate.LONG);
		//信息费状态
		typeMap.put("costStatus", Hibernate.STRING);
		//发货方手机号
		typeMap.put("uploadCellphone", Hibernate.STRING);
		//发货方手机号
		typeMap.put("userId", Hibernate.LONG);
		//发货方手机号
		typeMap.put("payUserId", Hibernate.LONG);
		//操作人Id
		typeMap.put("opId", Hibernate.LONG);
		//操作人姓名
		typeMap.put("opName", Hibernate.STRING);
		//支付订单号
		typeMap.put("payOrderNo", Hibernate.STRING);
		//支付子渠道
		typeMap.put("paySubChannel", Hibernate.STRING);
		//货源类型 1电议 2一口价
		typeMap.put("publishType", Hibernate.STRING);
		//退款到账时间
		typeMap.put("refundArrivalTime", Hibernate.TIMESTAMP);
		//退款状态 1.退款中 2.退款成功 3.退款失败
		typeMap.put("refundStatus", Hibernate.STRING);
		//退款异常信息
		typeMap.put("refundErrMsg", Hibernate.STRING);
		//退款异常信息
		typeMap.put("delayStatus", Hibernate.INTEGER);
		// 退款原因
		typeMap.put("refundReason", Hibernate.STRING);

		typeMap.put("refundFlag", Hibernate.INTEGER);
		//服务费最终分配-车方
		typeMap.put("carServiceAmount", Hibernate.LONG);
		//修改时间
		typeMap.put("mtime", Hibernate.TIMESTAMP);
		//技术服务费 单位分
		typeMap.put("tecServiceFee", Hibernate.LONG);
		//技术服务费支付状态 0待支付 1支付成功 2支付失败
		typeMap.put("tecFeePayStatus", Hibernate.INTEGER);
		//技术服务费支付时间
		typeMap.put("tecFeePayTime", Hibernate.TIMESTAMP);
		//技术服务费退还金额
		typeMap.put("tecFeeRefundAmount", Hibernate.LONG);
		//技术服务费退款状态
		typeMap.put("tecFeeRefundStatus", Hibernate.INTEGER);
		//技术服务费退款时间
		typeMap.put("tecFeeRefundTime", Hibernate.TIMESTAMP);
		//是否会员
		typeMap.put("isMember", Hibernate.INTEGER);
		//调度类型
		typeMap.put("sourceType", Hibernate.INTEGER);
		//订单状态：5.待接单 10.待签署 15.待装货 20.待卸货/待收货 25.待收运费/待付运费 30.已完成 35.已取消
		typeMap.put("orderNewStatus", Hibernate.INTEGER);
		//取消状态  0 未取消 1取消中 待车主处理 2 车主同意取消  3货主拒绝取消  4 系统拒绝取消
		typeMap.put("cancelStatus", Hibernate.INTEGER);
		//延迟退款状态 0 未延迟退款 1 延迟退款
		typeMap.put("delayRefundStatus", Hibernate.INTEGER);
		//预计退款到账日期
		typeMap.put("deRefundDueDate", Hibernate.TIMESTAMP);
        //技术服务费单号
		typeMap.put("technicalServiceNo", Hibernate.STRING);
		return typeMap;
	}


	/**
	 * 构建查询条件
	 * @param conditionBean
	 * @param conditionBuffer
	 * @param conditionParams
	 * @param conditionUserId
	 * @throws Exception
	 */
	private void buildConditionBufferAndParams(TransportOrderQueryBean conditionBean, StringBuffer conditionBuffer, List<Object> conditionParams, Long conditionUserId) throws Exception {
		if(objectIsNotEmpty(conditionBean.getStartCreateTime())){
			conditionBuffer.append(" and o.create_time>=?");
			conditionParams.add(conditionBean.getStartCreateTime());
		}
		if(objectIsNotEmpty(conditionBean.getEndCreateTime())){
			conditionBuffer.append(" and o.create_time<?");
			conditionParams.add(TimeUtil.addDay(conditionBean.getEndCreateTime(), 1) );
		}
		//技术服务费退款开始时间
		if(objectIsNotEmpty(conditionBean.getTechRefundStartTime())){
			conditionBuffer.append(" and tto.refund_time >= ?");
			conditionParams.add(conditionBean.getTechRefundStartTime());
		}
		//技术服务费退款结束时间
		if(objectIsNotEmpty(conditionBean.getTechRefundEndTime())){
			conditionBuffer.append(" and tto.refund_time < ?");
			conditionParams.add(TimeUtil.addDay(conditionBean.getTechRefundEndTime(), 1));
		}
		//折扣金额最小值
		if(objectIsNotEmpty(conditionBean.getDiscountAmountMin())){
			conditionBuffer.append(" and (o.total_order_amount - o.pay_amount)/100 >= ?");
			conditionParams.add(Integer.parseInt(conditionBean.getDiscountAmountMin()));
		}
		//折扣金额最大值
		if(objectIsNotEmpty(conditionBean.getDiscountAmountMax())){
			conditionBuffer.append(" and (o.total_order_amount - o.pay_amount)/100 <= ?");
			conditionParams.add(Integer.parseInt(conditionBean.getDiscountAmountMax()));
		}

		if(objectIsNotEmpty(conditionBean.getCarriageFeeMin())){
			conditionBuffer.append(" and o.carriage_fee>=?");
			conditionParams.add(conditionBean.getCarriageFeeMin());
		}
		if(objectIsNotEmpty(conditionBean.getCarriageFeeMax())){
			conditionBuffer.append(" and o.carriage_fee<=?");
			conditionParams.add(conditionBean.getCarriageFeeMax());
		}


		if(objectIsNotEmpty(conditionBean.getPayAmountMin())){
			conditionBuffer.append(" and o.pay_amount>=?");
			conditionParams.add(conditionBean.getPayAmountMin().multiply(new BigDecimal("100.00")));
		}
		if(objectIsNotEmpty(conditionBean.getPayAmountMax())){
			conditionBuffer.append(" and o.pay_amount<=?");
			conditionParams.add(conditionBean.getPayAmountMax().multiply(new BigDecimal("100.00")));
		}

		if(objectIsNotEmpty(conditionBean.getTecFeeMin())){
			conditionBuffer.append(" and o.tec_service_fee >= ?");
			conditionParams.add(conditionBean.getTecFeeMin().multiply(new BigDecimal("100.00")));
		}
		if(objectIsNotEmpty(conditionBean.getTecFeeMax())){
			conditionBuffer.append(" and o.tec_service_fee <= ?");
			conditionParams.add(conditionBean.getTecFeeMax().multiply(new BigDecimal("100.00")));
		}

		if(objectIsNotEmpty(conditionBean.getPayCellPhone())){
			/*conditionBuffer.append(" and o.pay_cell_phone=?");
			conditionParams.add(conditionBean.getPayCellPhone());*/
			// 改为通过用户id查询
			Long payUserId = userService.getIdByCellPhone(conditionBean.getPayCellPhone());
			conditionBuffer.append(" and o.pay_user_id=?");
			conditionParams.add(payUserId);
		}
		if(objectIsNotEmpty(conditionBean.getPayType())){
			if ("1".equals(conditionBean.getPayType())){
				conditionBuffer.append(" and o.pay_sub_channel like ?");
				conditionParams.add("ALIPAY%");
			}else if ("2".equals(conditionBean.getPayType())){
				conditionBuffer.append(" and o.pay_sub_channel like ?");
				conditionParams.add("AGRT%");
			}else if ("3".equals(conditionBean.getPayType())){
				conditionBuffer.append(" and o.pay_sub_channel like ?");
				conditionParams.add("WECHAT%");
			}else if ("4".equals(conditionBean.getPayType())){
				conditionBuffer.append(" and o.pay_type=?");
				conditionParams.add(conditionBean.getPayType());
			}else if ("5".equals(conditionBean.getPayType())){
				conditionBuffer.append(" and o.pay_sub_channel=?");
				conditionParams.add("BALANCE");
			}
		}
		if(objectIsNotEmpty(conditionBean.getRobStatus())){
			conditionBuffer.append(" and o.rob_status=?");
			conditionParams.add(conditionBean.getRobStatus());
		}
		if(objectIsNotEmpty(conditionBean.getPublishType())){
			conditionBuffer.append(" and ts.publish_type=?");
			conditionParams.add(conditionBean.getPublishType());
		}
		//信息费状态：10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
		if(objectIsNotEmpty(conditionBean.getCostStatus())){
			//update by sissy  151 代表手动点击延迟付款  211 代表车方拒绝退款延迟付款
			if("151".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status=15 and o.delay_status=1 ");
			}else if("211".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status in (15,21) and o.delay_status=2 ");
			}else if("15".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status=15 and o.delay_status=0 ");
			}else if("21".equals(conditionBean.getCostStatus())){
				conditionBuffer.append(" and o.cost_status=21 and o.delay_status=0 ");
			}else{
				conditionBuffer.append(" and o.cost_status=?");
				conditionParams.add(conditionBean.getCostStatus());
			}
		}
		if(objectIsNotEmpty(conditionBean.getTsOrderNo())){
			conditionBuffer.append(" and o.ts_order_no=?");
			conditionParams.add(conditionBean.getTsOrderNo());
		}
		if(objectIsNotEmpty(conditionBean.getThirdPartyPlatformOrderNo())){
			conditionBuffer.append(" and o.thirdparty_platform_order_no=?");
			conditionParams.add(conditionBean.getThirdPartyPlatformOrderNo());
		}
		if(objectIsNotEmpty(conditionBean.getTsId())){
			conditionBuffer.append(" and o.ts_id=?");
			conditionParams.add(conditionBean.getTsId());
		}
		if(conditionUserId != null){
			conditionBuffer.append(" and o.user_id=? ");
			conditionParams.add(conditionUserId);
		}
		//支付状态 0待支付 1支付失败 2支付成功
		if(objectIsNotEmpty(conditionBean.getPayStatus())){
			conditionBuffer.append(" and o.pay_status=?");
			conditionParams.add(conditionBean.getPayStatus());
		}
		//退款状态 1.退款中 2.退款成功 3.退款失败
		if(objectIsNotEmpty(conditionBean.getRefundStatus())){
			conditionBuffer.append(" and o.refund_status=?");
			conditionParams.add(conditionBean.getRefundStatus());
		}
		//订单ID
		if(objectIsNotEmpty(conditionBean.getId())){
			conditionBuffer.append(" and o.id=?");
			conditionParams.add(conditionBean.getId());
		}
		//货源类型
		if (objectIsNotEmpty(conditionBean.getExcellentGoods())) {
			conditionBuffer.append(" and ts.excellent_goods=?");
			conditionParams.add(conditionBean.getExcellentGoods());
		}
		//是否代调
		if (objectIsNotEmpty(conditionBean.getIsDispatch())) {
			//代调
			if (1 == conditionBean.getIsDispatch()) {
				conditionBuffer.append(" and ( ts.source_type=2 or ts.source_type=5 or  (ts.source_type=1 and o.user_id in  (select distinct user_id from tyt_dispatch_company where is_valid = 1)) )");
			} else {
				conditionBuffer.append(" and !( ts.source_type=2 or ts.source_type=5 or  (ts.source_type=1 and o.user_id in  (select distinct user_id from tyt_dispatch_company where is_valid = 1)) )");
			}
		}
		//货源来源
		if (objectIsNotEmpty(conditionBean.getTsourceType()) && conditionBean.getTsourceType() != 0) {
			//运满满
			Integer tsourceType = conditionBean.getTsourceType();
			//调度客服
			if (TransportSourceTypeEnum.DISPATCH.getCode().equals(tsourceType)) {
				conditionBuffer.append(" and  ts.source_type=2  ");
				//运满满货源
			} else if (TransportSourceTypeEnum.YMM.getCode().equals(tsourceType)) {
				conditionBuffer.append(" and  ts.source_type=4  ");
				//	5宏信货源
			} else if (TransportSourceTypeEnum.HX.getCode().equals(tsourceType)) {
				conditionBuffer.append(" and  ts.source_type=5  ");
			} else {
				conditionBuffer.append(" and ( ts.source_type not in (2,4,5) or ts.source_type is  null) ");
			}
		}
		//是否会员
		if(objectIsNotEmpty(conditionBean.getIsMember())){
			if(UserPermissionStatusEnum.VALID.getCode().equals(conditionBean.getIsMember())){
				conditionBuffer.append(" and  occ.is_member=1 ");
			}else{
				conditionBuffer.append(" and  (occ.is_member != 1  or occ.is_member is null ) ");
			}
		}
		conditionBuffer.append(" and (tto.technical_service_fee > 0 or o.coupon_amount > 0 or ( ts.source_type=2 or ts.source_type=5 or  (ts.source_type=1 and o.user_id in  (select distinct user_id from tyt_dispatch_company where is_valid = 1)) ) )");
		logger.info("buildConditionBufferAndParams conditionBean is 【{}】conditionParams is 【{}】",JSON.toJSONString(conditionBean), JSON.toJSONString(conditionParams));
	}

	/**
	 * 更新技术服务费线下退款信息
	 *
	 * @param orderId
	 * @param tecFeeRefundAmount
	 * @return
	 */
	@Override
	public int updateOfflineRefundInfo(Long orderId, Long tecFeeRefundAmount) {
		int result = 0;
		//1.查询运单信息
		TytTransportOrders transportOrders = this.getById(orderId);
		//2.根据运单信息查询技术服务费信息
		if(Objects.nonNull(transportOrders)){
			TytTransportTechnicalOrder transportTechnicalOrder = tytTransportTechnicalOrderMapper.selectNoRefundByTechnicalServiceNo(transportOrders.getTechnicalServiceNo());
			if(Objects.nonNull(transportTechnicalOrder)){
				transportTechnicalOrder.setRefundStatus((byte) 2);
				transportTechnicalOrder.setRefundTime(new Date());
				transportTechnicalOrder.setRefundArrivalTime(new Date());
				transportTechnicalOrder.setRefundAmount(tecFeeRefundAmount);
				//退款类型：1.业务退款 2.线下退款
				transportTechnicalOrder.setRefundType(2);
                return tytTransportTechnicalOrderMapper.updateByPrimaryKey(transportTechnicalOrder);
			}
		}
		return result;
	}
}
