package com.tyt.infofee.service;

import java.util.List;
import com.tyt.infofee.bean.FinancialInOutAccountQueryBean;
import com.tyt.infofee.bean.FinancialInOutAccountQueryResultBean;
import com.tyt.infofee.bean.FinancialInOutDetail;
import com.tyt.model.PageBean;
import com.tyt.model.TytFinancialInOutAccount;
import com.tyt.service.base.BaseService;

public interface TytFinancialInOutAccountService extends BaseService<TytFinancialInOutAccount, Long> {
	/**
	 * 财务出入账查询数据
	 * 
	 * @param pageBean
	 * @param conditionBean
	 * @return
	 */
	List<FinancialInOutAccountQueryResultBean> getFinancialInOutAccountList(PageBean pageBean, FinancialInOutAccountQueryBean conditionBean) throws Exception;

	/**
	 * 财务出入账查询详情
	 * 
	 * @param inOutAccountId
	 * @return
	 * @throws Exception
	 */
	FinancialInOutDetail getDetail(Long inOutAccountId) throws Exception;

	/**
	 * 获取银行卡
	 * 
	 * @return
	 * @throws Exception
	 */
	List<String> getDistinctBankName() throws Exception;

	/**
	 * 导出数据
	 * 
	 * @param content
	 * @param u
	 */
//	public void objectUserToCvsString(StringBuilder content, FinancialInOutAccountQueryResultBean financialInOutAccount);

	/**
	 * 获取所有数据
	 * 
	 * @param conditionBean
	 * @return
	 * @throws Exception
	 */
	List<FinancialInOutAccountQueryResultBean> getAllFinancialInOutAccountList(FinancialInOutAccountQueryBean conditionBean) throws Exception;

}
