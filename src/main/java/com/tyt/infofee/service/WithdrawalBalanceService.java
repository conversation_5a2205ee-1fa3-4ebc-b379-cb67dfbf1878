package com.tyt.infofee.service;

import com.tyt.model.WithdrawalBalance;
import com.tyt.service.base.BaseService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/29 10:41
 */
public interface WithdrawalBalanceService extends BaseService<WithdrawalBalance, Long> {
    /**
     * 获取可提现余额页面数据
     * @param startTime
     * @param endTime
     * @param page
     * @param size
     * @return
     */
    Map<String,Object> getWithdrawalBalanceList(String startTime, String endTime, Integer page, Integer size);
}
