package com.tyt.infofee.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.tyt.manager.entity.base.TytDepositFinanceAudit;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Objects;

@Data
public class FinancialDepositAuditExportBean implements Serializable {

    /**
     * 用户id
     */
    @ExcelProperty("用户id")
    private Long userId;
    /**
     * 保证金类型：1.退还保证金(用户提交的申请类型) 2.扣除保证金(客服提交的申请类型)
     */
    @ExcelProperty("保证金类型")
    private String depositAmountType;

    /**
     * 保证金金额
     */
    @ExcelProperty("保证金金额")
    private BigDecimal depositAmount;

    /**
     * 申请审批时间
     */
    @ExcelProperty("申请审批时间")
    private String ctime;

    /**
     * 审批时间
     */
    @ExcelProperty("批准时间")
    private String applyAuditTime;

    /**
     * 审批人
     */
    @ExcelProperty("审批人")
    private String auditUserName;

    public static FinancialDepositAuditExportBean fromFinancialDepositResultBean(TytDepositFinanceAudit tytDepositFinanceAudit) {
        FinancialDepositAuditExportBean financialDepositAuditExportBean = new FinancialDepositAuditExportBean();


        financialDepositAuditExportBean.setUserId(tytDepositFinanceAudit.getUserId());

        // 保证金类型
        String depositAmountTypeText;
        Integer depositAmountType = tytDepositFinanceAudit.getDepositAmountType();
        if (Objects.equals(1, depositAmountType)) {
            depositAmountTypeText = "退还保证金";
        } else if (Objects.equals(2, depositAmountType)) {
            depositAmountTypeText = "扣除保证金";
        } else {
            depositAmountTypeText = String.valueOf(depositAmountType);
        }
        financialDepositAuditExportBean.setDepositAmountType(depositAmountTypeText);

        // 金额
        financialDepositAuditExportBean.setDepositAmount(tytDepositFinanceAudit.getDepositAmount());

        // 时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (tytDepositFinanceAudit.getCtime() != null) {
            financialDepositAuditExportBean.setCtime(dateFormat.format(tytDepositFinanceAudit.getCtime()));
        }
        if (tytDepositFinanceAudit.getApplyAuditTime() != null) {
            financialDepositAuditExportBean.setApplyAuditTime(dateFormat.format(tytDepositFinanceAudit.getApplyAuditTime()));
        }

        financialDepositAuditExportBean.setAuditUserName(tytDepositFinanceAudit.getAuditUserName());

        return financialDepositAuditExportBean;
    }
}
