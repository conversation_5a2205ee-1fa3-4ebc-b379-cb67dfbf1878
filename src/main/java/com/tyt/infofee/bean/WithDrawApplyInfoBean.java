package com.tyt.infofee.bean;

import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;

/**
 * 封装用户申请提款信息的实体
 *
 * <AUTHOR>
 * @date 2017年9月11日上午11:51:40
 * @description
 */
public class WithDrawApplyInfoBean {
	/*
	 * 提款申请id
	 */
	private String id;
	/*
	 * 提款用户账号
	 */
	private String userAccount;
	/*
	 * 用户认证姓名
	 */
	private String userAuthName;
	/*
	 * 提款申请时间
	 */
	private String withdrawApplyTime;
	/*
	 * 提款卡卡号
	 */
	private String cardNum;
	/*
	 * 提款卡所属人
	 */
	private String cardOwnerName;
	/*
	 * 提款金额（分）
	 */
	private String withdrawAmmount;
	/*
	 * 打款状态 1：未转帐 2：已转帐 3:转账失败
	 */
	private String withDrawStatus;
	/*
	 * 打款渠道 1 微信 2 支付宝 3 银行卡 4 其他
	 */
	private String channel;
	/*
	 * 操作时间
	 */
	private String operateTime;
	/*
	 * 打款失败原因
	 */
	private String withdrawFailMessage;
	/*
	 * 提现银行卡所属银行
	 */
	private String cardBlongedBank;
	private String deliverTypeOne;

	/*
	 * 提现对账状态
	 */
	private String withdrawRecordStatus;
	/*
	 * 提现对账备注
	 */
	private String withdrawRecordRemark;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCardBlongedBank() {
		return cardBlongedBank;
	}

	public void setCardBlongedBank(String cardBlongedBank) {
		this.cardBlongedBank = cardBlongedBank;
	}

	public String getWithdrawFailMessage() {
		return withdrawFailMessage;
	}

	public void setWithdrawFailMessage(String withdrawFailMessage) {
		this.withdrawFailMessage = withdrawFailMessage;
	}

	public String getUserAccount() {
		return userAccount;
	}

	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	public String getUserAuthName() {
		return userAuthName;
	}

	public void setUserAuthName(String userAuthName) {
		this.userAuthName = userAuthName;
	}

	public String getWithdrawApplyTime() {
		return (withdrawApplyTime != null && withdrawApplyTime.length() == 21) ? withdrawApplyTime.substring(0, withdrawApplyTime.length() - 2) : withdrawApplyTime;
	}

	public void setWithdrawApplyTime(String withdrawApplyTime) {
		this.withdrawApplyTime = withdrawApplyTime;
	}

	public String getCardNum() {
		return cardNum;
	}

	public void setCardNum(String cardNum) {
		this.cardNum = cardNum;
	}

	public String getCardOwnerName() {
		return cardOwnerName;
	}

	public void setCardOwnerName(String cardOwnerName) {
		this.cardOwnerName = cardOwnerName;
	}

	public String getWithdrawAmmount() {
		return withdrawAmmount;
	}

	public Integer getWithdrawAmmountValue() {
		return StringUtils.isNotEmpty(withdrawAmmount) ? Integer.valueOf(withdrawAmmount) / 100 : null;
	}
	public String getWithdrawRecordStatus() {
		return withdrawRecordStatus;
	}

	public void setWithdrawRecordStatus(String withdrawRecordStatus) {
		this.withdrawRecordStatus = withdrawRecordStatus;
	}

	public String getWithdrawRecordRemark() {
		return withdrawRecordRemark;
	}

	public void setWithdrawRecordRemark(String withdrawRecordRemark) {
		this.withdrawRecordRemark = withdrawRecordRemark;
	}


	public String getChannelValue() {
		String channelValue = "";
		if (StringUtils.isEmpty(channel)) {
			channelValue = "其他";
		} else {
			// 打款渠道 1 微信 2 支付宝 3 银行卡 4 其他
			switch (Integer.valueOf(channel)) {
				case 1:
					channelValue = "微信";
					break;
				case 2:
					channelValue = "支付宝";
					break;
				case 3:
					channelValue = "银行卡";
					break;
				case 4:
					channelValue = "其他";
					break;
			}
		}
		return channelValue;
	}

	public String getWithdrawStatusAndChannelValue() {
		String withdrawStatusAndChannelValue = "";
		if (StringUtils.isNotEmpty(withDrawStatus)) {
			if ("1".equals(withDrawStatus)) {
				withdrawStatusAndChannelValue = "未打款";
			} else if ("2".equals(withDrawStatus)) {
				withdrawStatusAndChannelValue = "打款成功/";
				if (StringUtils.isEmpty(channel)) {
					withdrawStatusAndChannelValue += "其他";
				} else {
					// 打款渠道 1 微信 2 支付宝 3 银行卡 4 其他
					switch (Integer.valueOf(channel)) {
						case 1:
							withdrawStatusAndChannelValue += "微信";
							break;
						case 2:
							withdrawStatusAndChannelValue += "支付宝";
							break;
						case 3:
							withdrawStatusAndChannelValue += "银行卡";
							break;
						case 4:
							withdrawStatusAndChannelValue += "其他";
							break;
					}
				}
			} else if ("3".equals(withDrawStatus)) {
				withdrawStatusAndChannelValue = "打款失败/" + withdrawFailMessage;
			}
		}
		return withdrawStatusAndChannelValue;
	}

	public void setWithdrawAmmount(String withdrawAmmount) {
		this.withdrawAmmount = withdrawAmmount;
	}

	public String getWithDrawStatus() {
		return withDrawStatus;
	}

	public void setWithDrawStatus(String withDrawStatus) {
		this.withDrawStatus = withDrawStatus;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getOperateTime() {
		return (operateTime != null && operateTime.length() == 21) ? operateTime.substring(0, operateTime.length() - 2) : operateTime;
	}

	public void setOperateTime(String operateTime) {
		this.operateTime = operateTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

	public String getDeliverTypeOne() {
		return deliverTypeOne;
	}

	public void setDeliverTypeOne(String deliverTypeOne) {
		this.deliverTypeOne = deliverTypeOne;
	}
}
