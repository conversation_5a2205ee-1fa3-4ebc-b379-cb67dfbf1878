package com.tyt.infofee.bean;

import com.tyt.model.PageBean;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class FinancialDepositResultBean extends PageBean {
    /**
     * 财务保证金记录id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 保证金类型
     */
    private Integer depositAmountType;
    /**
     * 审批时间
     */
    private Date applyAuditTime;

    /**
     * 申请审批时间
     */
    private Date ctime;

    /**
     * 保证金金额
     */
    private BigDecimal depositAmount;

    /**
     * 审批人
     */
    private String auditUserName;
    /**
     * 审批状态  0.待处理 1.批准 2.拒绝
     */
    private Integer auditStatus;

}
