package com.tyt.infofee.bean;
import java.math.BigDecimal;
import java.util.List;

/**
 * 信息费异常上报确认请求Tpay服务实体
 */
public class TpayInfoFeeMerchantOrdersConfirm extends MqBaseMessageBean {
	/**
	 * 用户ID
	 */
	private String userId;
	/**
	 * 商品名称或主题
	 */
	private String subject;
	/**
	 *  本次总金额
	 */
	private BigDecimal confirmAmount;
	/**
	 * 运单号
	 */
	private String tsOrderNo;
	/**
	 * 该笔异常上报的付款人userId（此处为车方userId）
	 */
	private String payUserId;

	/**
	 * 收款人信息列表
	 */
	private List<PayeeInfoConfirm> payeeInfoList;

	/**
	 * 确认渠道来源(1.异常上报 2.装货完成确 3.接受部分退款确认)
	 */
	private Integer confirmChannel;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public BigDecimal getConfirmAmount() {
		return confirmAmount;
	}

	public void setConfirmAmount(BigDecimal confirmAmount) {
		this.confirmAmount = confirmAmount;
	}

	public String getTsOrderNo() {
		return tsOrderNo;
	}

	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}

	public String getPayUserId() {
		return payUserId;
	}

	public void setPayUserId(String payUserId) {
		this.payUserId = payUserId;
	}

	public List<PayeeInfoConfirm> getPayeeInfoList() {
		return payeeInfoList;
	}

	public void setPayeeInfoList(List<PayeeInfoConfirm> payeeInfoList) {
		this.payeeInfoList = payeeInfoList;
	}

	public Integer getConfirmChannel() {
		return confirmChannel;
	}

	public void setConfirmChannel(Integer confirmChannel) {
		this.confirmChannel = confirmChannel;
	}
}
