package com.tyt.upgrade.service;

import com.tyt.model.RelTaskUser;
import com.tyt.model.TytUpgradeTemplate;
import com.tyt.model.TytUpgradeTemplateTask;
import com.tyt.upgrade.dto.TaskDTO;
import org.apache.commons.lang3.tuple.Pair;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 升级任务模版
 * <AUTHOR>
 * @date 2020-07-30 19:03:27
 */
public interface UpgradeTemplateTaskService {

    /**
     * 根据ID获取升级任务
     * @param id
     * <AUTHOR>
     * @return
     * @date 2020-07-30 19:05:45
     */
    TytUpgradeTemplateTask findById(long id);

    /**
     * 保存升级任务
     * @param taskDTO 新增任务信息
     * <AUTHOR>
     * @return
     * @date 2020-07-30 19:05:45
     */
    Pair<Integer, String> saveTemplateTask(TaskDTO taskDTO) throws Exception;

    /**
     * 修改任务状态
     * @param tytUpgradeTemplateTask
     * <AUTHOR>
     * @return
     * @date 2020-07-31 16:20:39
     */
    void changeTaskStatus(TytUpgradeTemplateTask tytUpgradeTemplateTask);

    /**
     * 分页获取任务列表
     * @param pageNo
     * @param pageSize
     * @param taskTitle
     * @param taskStatus
     * @param operator
     * <AUTHOR>
     * @return
     * @date 2020-07-31 16:32:41
     */
    Map<String, Object> queryPageTask(int pageNo,int pageSize,String taskTitle, Integer taskStatus, String operator);

    /**
     * 分页获取任务用户列表
     * @param pageNo
     * @param pageSize
     * @param taskId
     * @param carOwnerUpgradeStatus
     * @param cargoStationUpgradeStatus
     * @param userPhone
     * <AUTHOR>
     * @return
     * @date 2020-08-03 13:34:48
     */
    Map<String, Object> queryPageTaskUserList(int pageNo,int pageSize,int taskId, Integer carOwnerUpgradeStatus, Integer cargoStationUpgradeStatus, String userPhone);


    /**
     * 修改用户弹框状态
     * @param relTaskUser
     * <AUTHOR>
     * @return
     * @date 2020-07-31 16:20:39
     */
    int changeUserStatus(RelTaskUser relTaskUser);

    /**
     * 删除任务用户
     * @param id
     * <AUTHOR>
     * @return
     * @date 2020-07-31 16:20:39
     */
    void deleteUser(long id);

    /**
     * 导出
     * @param taskId
     * @param carOwnerUpgradeStatus
     * @param cargoStationUpgradeStatus
     * @param userPhone
     * <AUTHOR>
     * @return
     * @date 2020-08-03 16:48:53
     */
    void export(int taskId, Integer carOwnerUpgradeStatus, Integer cargoStationUpgradeStatus, String userPhone, HttpServletResponse response) throws IOException;



}
