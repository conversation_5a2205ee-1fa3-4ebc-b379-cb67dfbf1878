package com.tyt.upgrade.service.impl;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.PageBean;
import com.tyt.model.TytUpgradeRecord;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.upgrade.bean.UpgradeRecordBean;
import com.tyt.upgrade.bean.UpgradeRecordQueryBean;
import com.tyt.upgrade.enums.UpgradeStasusEnum;
import com.tyt.upgrade.service.UpgradeRecordService;
import com.tyt.util.StringUtil;
import org.apache.tools.ant.util.DateUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/7/31 16:16
 * @Version 1.0
 **/
@Service("upgradeRecordService")
public class UpgradeRecordServiceImpl extends BaseServiceImpl<TytUpgradeRecord,Long> implements UpgradeRecordService {

    @Resource(name = "tytUpgradeRecordDao")
    public void setBaseDao(BaseDao<TytUpgradeRecord, Long> tytUpgradeRecordDao) {
        super.setBaseDao(tytUpgradeRecordDao);
    }


    @Override
    public List<UpgradeRecordBean> getRecordList(UpgradeRecordQueryBean queryBean, PageBean pageBean) {
        StringBuffer conditionSql = new StringBuffer();
        Map<String,Object> paramMap=new HashMap<String,Object>();
        if(StringUtil.isNotEmpty(queryBean.getTaskTitle())){
            conditionSql.append(" AND task.task_title LIKE :taskTitle");
            paramMap.put("taskTitle", "%"+queryBean.getTaskTitle()+"%");
        }

        if(null!=queryBean.getTemplateId()){
            conditionSql.append(" AND task.template_id = :templateId");
            paramMap.put("templateId", queryBean.getTemplateId());
        }

        if(null!=queryBean.getTaskId()){
            conditionSql.append(" AND rtu.task_id =:taskId");
            paramMap.put("taskId", queryBean.getTaskId());
        }

        if(StringUtil.isNotEmpty(queryBean.getUserPhone())){
            conditionSql.append(" AND tur.user_phone =:userPhone");
            paramMap.put("userPhone", queryBean.getUserPhone());
        }

        if(null !=queryBean.getCargoStationUpgradeStatus()){
            conditionSql.append(" AND tur.cargo_station_upgrade_status = :cargoStationUpgradeStatus");
            paramMap.put("cargoStationUpgradeStatus", queryBean.getCargoStationUpgradeStatus());
        }

        if(null!= queryBean.getCarOwnerUpgradeStatus()){
            conditionSql.append(" AND tur.car_owner_upgrade_status =:carOwnerUpgradeStatus");
            paramMap.put("carOwnerUpgradeStatus", queryBean.getCarOwnerUpgradeStatus());
        }

        StringBuffer countSQL = new StringBuffer("SELECT COUNT(1) FROM tyt_upgrade_record tur LEFT JOIN rel_task_user rtu ON rtu.user_id = tur.user_id LEFT JOIN tyt_upgrade_templatetask task ON rtu.task_id = task.id WHERE 1=1");
        countSQL.append(conditionSql);
        BigInteger rowCount = this.getBaseDao().queryByMap(countSQL.toString(), paramMap);
        if(rowCount==null||rowCount.longValue()<=0L){
            return null;
        }
        pageBean.setRowCount(rowCount.longValue());

        StringBuffer sql = new StringBuffer("SELECT tur.id,tur.user_id userId,tur.user_phone userPhone," +
                "IFNULL(task.task_title, '其它') taskTitle,IFNULL(task.template_name,'') templateName,IFNULL(tur.real_name,'') realName,IFNULL(tur.user_area,'') userArea,"+
                "IFNULL(tur.cargo_station_upgrade_status,0) cargoStationUpgradeStatus,IFNULL(tur.car_owner_upgrade_status,0) carOwnerUpgradeStatus," +
                "tur.car_owner_login_time carOwnerLoginTime,tur.cargo_station_login_time cargoStationLoginTime,task.id taskId FROM tyt_upgrade_record tur "+
                "LEFT JOIN rel_task_user rtu ON rtu.user_id = tur.user_id LEFT JOIN tyt_upgrade_templatetask task ON rtu.task_id = task.id WHERE 1=1");

        sql.append(conditionSql);

        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
        map.put("id", Hibernate.LONG);
        map.put("userId", Hibernate.LONG);
        map.put("userPhone", Hibernate.STRING);
        map.put("taskTitle", Hibernate.STRING);
        map.put("templateName", Hibernate.STRING);
        map.put("realName", Hibernate.STRING);
        map.put("userArea", Hibernate.STRING);
        map.put("cargoStationUpgradeStatus", Hibernate.INTEGER);
        map.put("carOwnerUpgradeStatus", Hibernate.INTEGER);
        map.put("carOwnerLoginTime", Hibernate.TIMESTAMP);
        map.put("cargoStationLoginTime", Hibernate.TIMESTAMP);
        map.put("taskId", Hibernate.LONG);

        return this.getBaseDao().searchByName(sql.toString(), map, UpgradeRecordBean.class, paramMap, pageBean.getCurrentPage(), pageBean.getPageSize());

    }

    @Override
    public StringBuffer getStringCsv(List<UpgradeRecordBean> list) {
        StringBuffer content = new StringBuffer();
        String header = "用户ID,任务标题,任务模板,手机号,真实姓名,归属地,登录APP,登录时间,升级状态" +  "\r\n";

        content.append(header);
        if (list == null || list.size() <= 0){
            return content;
        }

        for(UpgradeRecordBean upgradeRecordBean : list){
            StringBuffer row = new StringBuffer();
            row.append(upgradeRecordBean.getUserId()).append(",");
            row.append(upgradeRecordBean.getTaskTitle()).append(",");
            row.append(upgradeRecordBean.getTemplateName()).append(",");
            row.append(upgradeRecordBean.getUserPhone()).append(",");
            row.append(upgradeRecordBean.getRealName()).append(",");
            row.append(upgradeRecordBean.getUserArea()).append(",");

            String carOwnerLoginTimeStr = formatDate(upgradeRecordBean.getCarOwnerLoginTime());;
            String cargoStationLoginTimeStr = formatDate(upgradeRecordBean.getCargoStationLoginTime());

            if(upgradeRecordBean.getCargoStationUpgradeStatus().equals(UpgradeStasusEnum.UPGRADE.getVal())&&
                    upgradeRecordBean.getCarOwnerUpgradeStatus().equals(UpgradeStasusEnum.UPGRADE.getVal())){
                row.append("车主版/货站版").append(",");
                row.append(carOwnerLoginTimeStr+"/"+cargoStationLoginTimeStr).append(",");
                row.append("车主版完成/货站版完成").append(",");
            }else if(upgradeRecordBean.getCargoStationUpgradeStatus().equals(UpgradeStasusEnum.UPGRADE.getVal())&&
                    upgradeRecordBean.getCarOwnerUpgradeStatus().equals(UpgradeStasusEnum.NONE.getVal())){
                row.append("-/货站版").append(",");
                row.append("-/"+cargoStationLoginTimeStr).append(",");
                row.append("-/货站版完成").append(",");
            }else if(upgradeRecordBean.getCargoStationUpgradeStatus().equals(UpgradeStasusEnum.NONE.getVal())&&
                    upgradeRecordBean.getCarOwnerUpgradeStatus().equals(UpgradeStasusEnum.UPGRADE.getVal())){
                row.append("车主版/-").append(",");
                row.append(carOwnerLoginTimeStr+"/-").append(",");
                row.append("车主版完成/-").append(",");
            }else{
                row.append("-").append(",");
                row.append("-").append(",");
                row.append("-").append(",");
            }


            content.append(row).append("\r\n");
        }

        return content;
    }

    private String formatDate(Date date) {
        String strDate = null;
        if(null != date){
            strDate = DateUtils.format(date, "yyyy-MM-dd HH:mm");
        }else{
            strDate = "-";
        }
        return strDate;
    }
}
