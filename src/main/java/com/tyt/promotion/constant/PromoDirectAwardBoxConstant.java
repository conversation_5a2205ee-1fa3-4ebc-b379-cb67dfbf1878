package com.tyt.promotion.constant;

/**
 * <AUTHOR>
 * @since 2023/11/03 15:07
 */
public class PromoDirectAwardBoxConstant {
    /**
     * 商品类型 4 发货次卡
     */
    public static final Integer GOODS_TYPE_PUBLISH_GOODS = 4;
    /**
     * 商品类型 7 找货次卡
     */
    public static final Integer GOODS_TYPE_SEARCH_GOODS = 7;

    /**
     * 活动类型 1发货会员
     */
    public static final Integer GOODS_TYPE_GOODS_VIP = 1;
    /**
     * 活动类型 2.找货会员
     */
    public static final Integer GOODS_TYPE_CAR_VIP = 2;
    /**
     * 商品类型 13 曝光卡
     */
    public static final Integer GOODS_TYPE_EXPOSURE_GOODS = 13;

    /**
     * 礼包类型 1 优惠券
     */
    public static final Integer AWARD_TYPE_COUPON = 1;
    /**
     * 礼包类型 2 商品
     */
    public static final Integer AWARD_TYPE_GOODS = 2;

    /**
     * promo_direct_user_activity表status 1.未发奖
     */
    public static final int  PROMO_DIRECT_USER_ACTIVITY_NO=1;
    /**
     *  promo_direct_user_activity表status  2.已发奖
     */
    public static final int  PROMO_DIRECT_USER_ACTIVITY_YES=2;
    /**
     *  promo_direct_user_activity表status   3.未注册用户
     */
    public static final int  PROMO_DIRECT_USER_ACTIVITY_NO_USER=3;
    /**
     *  promo_direct_user_activity表status   4 发奖中
     */
    public static final int  PROMO_DIRECT_USER_ACTIVITY_AWARD=4;
    /**
     * promo_direct_user_activity表status  6撤销发奖
     */
    public static final int  PROMO_DIRECT_USER_ACTIVITY_CANCEL=6;

    /**
     * promo_gift_box表礼包状态 1:有效 2:无效 3:删除
     */
    public static final Integer GIFT_BOX_STATUS_EFFECTIVE = 1;
    /**
     * promo_gift_box表礼包状态 1:有效 2:无效 3:删除
     */
    public static final Integer GIFT_BOX_STATUS_INVALID = 2;





}
