package com.tyt.promotion.service.impl;

import com.tyt.authCommon.bean.ExposureRecordInfo;
import com.tyt.authCommon.service.UserPermissionMQService;
import com.tyt.coupon.service.SendCouponMQService;
import com.tyt.dao.base.BaseDao;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.*;
import com.tyt.promotion.bean.PromoDirectListBean;
import com.tyt.promotion.constant.PromoDirectAwardBoxConstant;
import com.tyt.promotion.service.PromoDirectBoxGiftService;
import com.tyt.promotion.service.PromoDirectUserActivityService;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;
import com.tyt.util.AppConfig;
import com.tyt.util.Constant;
import com.tyt.util.CsvReader;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service("promoDirectUserActivityService")
@Slf4j
public class PromoDirectUserActivityServiceImpl extends BaseServiceImpl<PromoDirectUserActivity, Long> implements PromoDirectUserActivityService {

    @Resource(name = "promoDirectUserActivityDao")
    public void setBaseDao(BaseDao<PromoDirectUserActivity, Long> promoDirectUserActivityDao) {
        super.setBaseDao(promoDirectUserActivityDao);
    }

    @Resource(name="userService")
    private UserService userService;
    @Resource(name="promoDirectBoxGiftService")
    private PromoDirectBoxGiftService promoDirectBoxGiftService;
    @Resource(name="userPermissionMQService")
    private UserPermissionMQService userPermissionMQService;
    @Resource(name="sendCouponMQService")
    private SendCouponMQService sendCouponMQService;
    @Resource(name="userBuyGoodsService")
    private UserBuyGoodsService userBuyGoodsService;

    @Override
    public List<PromoDirectListBean> getActivityList(String cellPhone,Long userId, Integer status, PageBean pageBean) {
        Map<String,Object> paramMap=new HashMap<String,Object>();
        StringBuffer conditionSQL=new StringBuffer();
        if(StringUtils.isNotBlank(cellPhone)){
            conditionSQL.append(" and ua.`cell_phone`=:cellPhone");
            paramMap.put("cellPhone", cellPhone);
        }
        if(null != userId){
            conditionSQL.append(" and ua.`user_id`=:userId");
            paramMap.put("userId", userId);
        }
        if (null != status) {
            conditionSQL.append(" and ua.`status`=:status");
            paramMap.put("status", status);
        }

        StringBuffer countSQL=new StringBuffer("select count(*) FROM `promo_direct_user_activity` ua where 1=1 ");
        countSQL.append(conditionSQL);
        BigInteger rowCount=this.getBaseDao().queryByMap(countSQL.toString(), paramMap);
        if(rowCount==null||rowCount.longValue()<=0L){
            return null;
        }
        pageBean.setRowCount(rowCount.longValue());
        StringBuffer querySQL = new StringBuffer("SELECT ua.`id` id,ua.user_id userId,ua.`cell_phone` cellPhone,ua.`activity_name` activityName,ua.`award_box_id` awardBoxId,ab.`box_content` boxContent,ua.`mtime` mtime,ua.status status FROM `promo_direct_user_activity` ua LEFT JOIN `promo_direct_award_box` ab ON ua.`award_box_id` = ab.`id` where 1=1 ");
        querySQL.append(conditionSQL);
        querySQL.append(" ORDER BY ua.id DESC");
        Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
        map.put("id", Hibernate.LONG);
        map.put("userId", Hibernate.LONG);
        map.put("cellPhone", Hibernate.STRING);
        map.put("activityName", Hibernate.STRING);
        map.put("awardBoxId", Hibernate.LONG);
        map.put("boxContent", Hibernate.STRING);
        map.put("status", Hibernate.INTEGER);
        map.put("mtime", Hibernate.TIMESTAMP);
        return this.getBaseDao().searchByName(querySQL.toString(), map, PromoDirectListBean.class, paramMap, pageBean.getCurrentPage(), pageBean.getPageSize());
    }

    @Override
    public ResultMsgBean saveExcel(MultipartFile fileField, ResultMsgBean msgBean) throws Exception {
        String str="";
        if (fileField != null && !fileField.isEmpty()) {
            String excelUrl = renameExcelBase(fileField, "excel");
            fileField.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + excelUrl));
            List<List<String>> importList = CsvReader.csvToListNew(AppConfig.getProperty("picture.file.path.domain") + excelUrl);
            if (importList == null){
                msgBean.setMsg("文件编码格式不正确");
                msgBean.setCode(TypedResultMsgBean.ERROR);
                return msgBean;
            }
            //异步导入 建立线程池
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            executorService.execute(() -> {
                try {
                    int flag =0;
                    for(List<String> list : importList){
                        if(CollectionUtils.isNotEmpty(list)){
                            flag ++;
                            if (flag > Constant.IMPORT_LIMIT){
                                flag = 0;
                                Thread.sleep(Constant.IMPORT_THREAD_SLEEP_MILLISECOND);
                            }
                            PromoDirectUserActivity userActivity = new PromoDirectUserActivity();
                            userActivity.setUserId(Long.valueOf(list.get(0)));
                            userActivity.setActivityName(list.get(1));
                            userActivity.setAwardBoxId(Long.parseLong(list.get(2)));
                            userActivity.setStatus(1);
                            userActivity.setCtime(new Date());
                            userActivity.setMtime(new Date());
                            if (StringUtils.isNotBlank(list.get(3))){
                                userActivity.setGainType(Integer.parseInt(list.get(3)));
                            }
                            add(userActivity);
                        }
                    }
                }catch (Exception e){
                    log.error("中奖名单导入失败：",e);
                }
            });
            // 关闭线程
            executorService.shutdown();

            if (importList.size()>Constant.IMPORT_LIMIT){
                str+="导入中，请耐心等待，刷新页面确认后再发奖，切勿重复操作";
            }else{
                str+="导入成功";
            }

            msgBean.setCode(200);
            msgBean.setMsg(str);
            return msgBean;
        }
        return msgBean;
    }

    @Override
    public void updateAward(EmployeeQueryBean curUser) throws Exception{
        //获取未发放奖励用户
        Date threeDays = TimeUtil.addDay(TimeUtil.weeHours(new Date(),0),-3);
        String sql="SELECT * FROM `promo_direct_user_activity` WHERE status=1 and ctime>?";
        List<PromoDirectUserActivity> list = this.getBaseDao().queryForList(sql,new Object[]{threeDays});
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        StringBuilder ids = new StringBuilder();
        for (PromoDirectUserActivity userActivity : list) {
            ids.append(userActivity.getId()).append(",");
        }
        String updateSql2 = "UPDATE `promo_direct_user_activity` SET `status`=4,`mtime`=NOW(),`update_user_id`=?,`update_user_name`=? WHERE id in("+ids.substring(0,ids.length()-1)+")";
        this.getBaseDao().executeUpdateSql(updateSql2, new Object[]{curUser.getId(),curUser.getRealName()});
        //异步发放奖励 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(() -> {
            int i = 0;
            for (PromoDirectUserActivity userActivity : list) {
                i++;
                Long userId = userActivity.getUserId();
                try {
                    if (i > 15) {
                        i = 0;
                        Thread.sleep(Constant.IMPORT_THREAD_SLEEP_MILLISECOND);
                    }
                    User user = userService.getCellPhoneByUserId(userId);
                    Integer gainType = userActivity.getGainType();
                    //手机号是否已注册
                    if (null == user) {
                        //修改为未注册
                        promoDirectBoxGiftService.updateDirectAward(3, null, userId, curUser, userActivity.getId());
                    } else {
                        List<PromoDirectBoxGift> giftList = promoDirectBoxGiftService.getBoxGift(userActivity.getAwardBoxId());
                        if (CollectionUtils.isNotEmpty(giftList)) {
                            for (PromoDirectBoxGift promoDirectBoxGift : giftList) {
                                if (promoDirectBoxGift.getAwardType() == 1) {
                                    //发礼包
                                    sendCouponMQService.sendCouponByActivityId2MQ(userId, promoDirectBoxGift.getAwardId());
                                } else if (promoDirectBoxGift.getAwardType() == 2) {
                                    //记录购买商品记录
                                    TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, promoDirectBoxGift.getAwardId().longValue(), "manage_give", 2, curUser);
                                    //发商品
                                    ExposureRecordInfo exposureRecordInfo = ExposureRecordInfo.builder()
                                            .sendType(1)
                                            .gainType(gainType == null ? 4 : gainType)
                                            .build();
                                    userPermissionMQService.sendUserPermission2MQ(userId, promoDirectBoxGift.getAwardId().longValue(), 2, userBuyGoods, exposureRecordInfo);
                                }
                            }
                        }
                        //修改为已发奖状态
                        promoDirectBoxGiftService.updateDirectAward(2, user.getCellPhone(), userId, curUser, userActivity.getId());
                    }
                } catch (Exception e) {
                    log.error("发奖失败：", e);
                    //发奖失败
                    promoDirectBoxGiftService.updateDirectAward(7, null, userId, curUser, userActivity.getId());
                }
            }
        });
        // 关闭线程
        executorService.shutdown();
    }

    @Override
    public void updateCancelAward(EmployeeQueryBean curUser,Integer status){
        //获取未发放奖励用户
        String updateSql2 = "UPDATE `promo_direct_user_activity` SET `status`=?,`mtime`=NOW(),`update_user_id`=?,`update_user_name`=? WHERE status=1";
        this.getBaseDao().executeUpdateSql(updateSql2, new Object[]{status,curUser.getId(),curUser.getRealName()});
    }
    @Override
    public void promoDirectListToCvsString(StringBuilder content, List<PromoDirectListBean> list) {
        for (PromoDirectListBean promoDirectBean : list) {
            //编号
            content.append(promoDirectBean.getId()).append(",");
            //中奖人手机号
            content.append(promoDirectBean.getCellPhone()).append(",");
            //活动名称
            content.append(StringUtils.isNotBlank(promoDirectBean.getActivityName())?promoDirectBean.getActivityName():"").append(",");
            //礼包内容
            content.append(StringUtils.isNotBlank(promoDirectBean.getBoxContent())?promoDirectBean.getBoxContent():"").append(",");
            //发奖时间
            content.append(promoDirectBean.getMtime()).append(",");
            //发奖状态
            content.append(promoDirectBean.getStatus()).append(",");
            content.append("\r\n");
        }
    }

    @Override
    public boolean getByStatus(int status) {
        String sql = "SELECT id FROM promo_direct_user_activity WHERE status=? limit 1";
        BigInteger obj = this.getBaseDao().query(sql, new Object[]{status});
        return obj != null && obj.intValue() > 0;
    }
}
