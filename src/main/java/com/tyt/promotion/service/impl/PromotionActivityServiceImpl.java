package com.tyt.promotion.service.impl;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytPromotionActivity;
import com.tyt.promotion.service.PromotionActivityService;
import com.tyt.service.base.BaseServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("promotionActivityService")
public class PromotionActivityServiceImpl extends BaseServiceImpl<TytPromotionActivity, Long> implements PromotionActivityService {
	public static Logger logger = LoggerFactory.getLogger(PromotionActivityServiceImpl.class);

	@Resource(name = "promotionActivityDao")
	public void setBaseDao(BaseDao<TytPromotionActivity, Long> promotionActivityDao) {
		super.setBaseDao(promotionActivityDao);
	}
}