package com.tyt.promotion.service;

import com.tyt.model.PromoGiftBox;
import com.tyt.service.base.BaseService;

import java.math.BigDecimal;
import java.sql.Date;


public interface PromoGiftBoxService extends BaseService<PromoGiftBox,Long> {

    Long savePromoGiftBox(String name, Integer status, Integer totalNum, BigDecimal totalAmount) throws Exception ;


    void updateById(Integer totalNum, BigDecimal totalAmount,Long giftId);

}
