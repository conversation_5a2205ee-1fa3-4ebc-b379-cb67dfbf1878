package com.tyt.carinsurance.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.carteam.bean.CarTeamQueryBean;
import com.tyt.model.PageBean;

import java.util.List;


/**
 * @Description  车险询价列表接口返回实体对象
 * <AUTHOR>
 * @Date  2019/3/20 16:05
 * @Param
 * @return
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarInsuranceResponseBean {

    //登录人角色是否是车险业务人员 0否 1:是
    private Integer isCarInsuranceRole;

    private List<CarInsuranceDetailBean> carInsuranceInquiryList;
    private PageBean pageBean;
    private CarInsuranceDetailBean carInsuranceDetailBean;

    public Integer getIsCarInsuranceRole() {
        return isCarInsuranceRole;
    }

    public void setIsCarInsuranceRole(Integer isCarInsuranceRole) {
        this.isCarInsuranceRole = isCarInsuranceRole;
    }

    public List<CarInsuranceDetailBean> getCarInsuranceInquiryList() {
        return carInsuranceInquiryList;
    }

    public void setCarInsuranceInquiryList(List<CarInsuranceDetailBean> carInsuranceInquiryList) {
        this.carInsuranceInquiryList = carInsuranceInquiryList;
    }

    public PageBean getPageBean() {
        return pageBean;
    }

    public void setPageBean(PageBean pageBean) {
        this.pageBean = pageBean;
    }

    public CarInsuranceDetailBean getCarInsuranceDetailBean() {
        return carInsuranceDetailBean;
    }

    public void setCarInsuranceDetailBean(CarInsuranceDetailBean carInsuranceDetailBean) {
        this.carInsuranceDetailBean = carInsuranceDetailBean;
    }
}
