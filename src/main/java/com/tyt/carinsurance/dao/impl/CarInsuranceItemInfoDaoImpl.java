package com.tyt.carinsurance.dao.impl;

import com.tyt.carinsurance.dao.CarInsuranceItemInfoDao;
import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.CarInsuranceItemInfo;
import org.springframework.stereotype.Repository;

/**
 * @Description  车险询价保项保额数据层实现类
 * <AUTHOR>
 * @Date  2019/3/12 14:59
 * @Param
 * @return
 **/
@Repository("carInsuranceItemInfoDao")
public class CarInsuranceItemInfoDaoImpl extends BaseDaoImpl<CarInsuranceItemInfo, Integer> implements CarInsuranceItemInfoDao {

    public CarInsuranceItemInfoDaoImpl() {
        this.setEntityClass(CarInsuranceItemInfo.class);
    }

}
