package com.tyt.chart.controller;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.chart.bean.TytGeoDict;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.TytSource;
import com.tyt.service.chart.UserChartService;
import com.tyt.service.inforecord.InfoRecordService;
import com.tyt.service.user.UserService;
import com.tyt.util.ChartUtil;
import com.tyt.util.Constant;
import com.tyt.util.ParseXmlUtil;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;
import com.tyt.web.base.BaseController;

/**
 * 
 * <AUTHOR>
 * @date 2016-5-12下午2:52:51
 * @description 报表的控制器
 */
@Controller
@RequestMapping("/chart")
public class ChartController extends BaseController {

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "userChartService")
	private UserChartService userChartService;

	@Resource(name = "infoRecordService")
	private InfoRecordService infoRecordService;

	private static List<String> PROVICES = new ArrayList<String>();
	private static Map<String, List<String>> PROVICES_CITY = new HashMap<String, List<String>>();
	private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	private static List<TytSource> sourceList = new ArrayList<TytSource>();
//	private static List<TytSource> checkSignList = TytSourceUtil.getSourceList("deliver_type");
	private static List<TytSource> checkSignList = new ArrayList<TytSource>();

	//Bean初始化完成后调用初始化方法
	@PostConstruct
	public void init(){
		// 用户来源
		sourceList = TytSourceUtil.getSourceList("source");
		checkSignList = TytSourceUtil.getSourceList("deliver_type");
		/*
		 * 由于设计原因核准身份"待定"的value值为空串导致不好处理，这里就给其设置一个value值
		 */
		Iterator<TytSource> iterator = checkSignList.iterator();
		TytSource tytSource;
		while (iterator.hasNext()) {
			tytSource = iterator.next();
			if ("待定".equals(tytSource.getName())) {
				tytSource.setValue("-100");
			}
		}
		System.out.println("ChartController初始化成功！==========================");
	}

	static {
		// 一共三项，第一项是所有的省，第二项是所有省对应的市
		List<Map<String, Object>> allData = ParseXmlUtil.parseXml();
		Map<String, Object> allProvices = allData.get(0);
		Map<String, Object> allCities = allData.get(1);
		Set<String> allProvincesKeys = allProvices.keySet();
		Set<String> allCitiesKeys = allCities.keySet();
		Iterator<String> iteProvincesKeys = allProvincesKeys.iterator();
		Iterator<String> iteCitiesKeys = allCitiesKeys.iterator();
		/*
		 * 获取所有的省，并将其对应的市存储起来
		 */
		String provinceKey;
		String province;
		String cityKey;
		// List<String> cities;
		while (iteProvincesKeys.hasNext()) {
			List<String> cities = new ArrayList<String>();
			provinceKey = iteProvincesKeys.next();
			province = (String) allProvices.get(provinceKey);
			// 将省信息保存起来
			PROVICES.add(province);
			/*
			 * 根据key获取省所有的市
			 */
			while (iteCitiesKeys.hasNext()) {
				cityKey = iteCitiesKeys.next();
				if (cityKey.startsWith(provinceKey + "-")) {
					cities.add((String) allCities.get(cityKey));
				}
			}
			// 注意重新初始化迭代器
			iteCitiesKeys = allCitiesKeys.iterator();
			// 保存省对应的所有市
			PROVICES_CITY.put(province, cities);
		}

		// 用户来源
//		sourceList = TytSourceUtil.getSourceList("source");
		/*
		 * 由于设计原因核准身份"待定"的value值为空串导致不好处理，这里就给其设置一个value值
		 */
//		Iterator<TytSource> iterator = checkSignList.iterator();
//		TytSource tytSource;
//		while (iterator.hasNext()) {
//			tytSource = iterator.next();
//			if ("待定".equals(tytSource.getName())) {
//				tytSource.setValue("-100");
//			}
//		}
	}

	/**
	 * 用户注册统计
	 */
	@RequestMapping("/userRegister")
	public String userregister(HttpServletRequest request, String registerType, String deliverType, String province, String city, String startDate, String endDate, String dateType, String compareType, String selectedProvinces, String selectedCities, String selectedDeliverType, String timeText, String cardName) {
		logger.info("get user register data chart");
		try {
			// 登陆验证
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			request.setAttribute("checkSignList", checkSignList);
			if (startDate == null && endDate == null) {
				/*
				 * 计算30天前的秒值
				 */
				endDate = sdf.format(new Date());
				startDate = sdf.format(TimeUtil.addDay(endDate, -30));
			}

			compareType = compareType == null ? "oneLine" : compareType;
			dateType = dateType == null ? "day" : dateType;
			timeText = timeText == null ? endDate : timeText;
			/*
			 * 准备数据源
			 */
			Map<String, String> resultMap = new HashMap<String, String>();
			if ("oneLine".equals(compareType.trim())) {

				/*
				 * 默认条件查询条件为如下： 分析角度：累计用户 身份:不限 地区：不限 分析时间:最近一个月(含今天) 按日分析
				 */
				resultMap = userService.getUserRegisterData(registerType, deliverType, province, city, startDate, endDate, dateType);
				// 单线报表
				request.setAttribute("chartType", "singleLine");
				request.setAttribute("chartobj", ChartUtil.getChartConfigureParam());
			} else if ("topArea".equals(compareType.trim())) {
				/*
				 * 按市获取注册量前五
				 */
				resultMap = userService.getUserRegisterTopFiveByCity(registerType, startDate, endDate, deliverType, dateType);
				// 多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareProvince".equals(compareType.trim())) {
				/*
				 * 获取选择的省的数据
				 */
				selectedProvinces = java.net.URLDecoder.decode(selectedProvinces, "UTF-8");
				resultMap = userService.getUserRegisterCompareByProvince(registerType, startDate, endDate, deliverType, dateType, selectedProvinces);
				// 多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareCity".equals(compareType.trim())) {
				/*
				 * 市级区域比较
				 */
				selectedCities = java.net.URLDecoder.decode(selectedCities, "UTF-8").replace("市", "");
				resultMap = userService.getUserRegisterCompareByCities(registerType, startDate, endDate, deliverType, dateType, selectedCities);
				// 多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("topCardId".equals(compareType.trim())) {
				/*
				 * 身份前五
				 */
				resultMap = userService.getUserRegisterTopByIdCard(registerType, province, city, startDate, endDate, dateType);
				// 多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("IdCard".equals(compareType.trim())) {
				/*
				 * 身份比较
				 */
				resultMap = userService.getUserRegisterCompareByIdCard(registerType, province, city, startDate, endDate, dateType, selectedDeliverType);
				// 多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("nearTime".equals(compareType.trim())) {
				/*
				 * 近期同比
				 */
				resultMap = userService.getUserRegisterByNearTime(registerType, deliverType, province, city, startDate, endDate, dateType);

				// 多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			} else if ("compareTime".equals(compareType.trim())) {
				/*
				 * 自定义时段对比
				 */
				resultMap = userService.getUserRegisterByTime(registerType, deliverType, province, city, startDate, endDate, dateType, timeText);

				// 多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			}
			request.setAttribute("compareType", compareType);
			request.setAttribute("registerType", registerType);
			request.setAttribute("deliverType", deliverType);
			request.setAttribute("curProvince", province);
			request.setAttribute("curCity", city);
			request.setAttribute("startDate", startDate);
			request.setAttribute("endDate", endDate);
			if (dateType == null || "".equals(dateType.trim()) || "undefined".equals(dateType)) {
				dateType = "day";
			}
			request.setAttribute("dateType", dateType);
			request.setAttribute("selectedProvinces", selectedProvinces);
			request.setAttribute("selectedCities", selectedCities);
			request.setAttribute("selectedDeliverType", selectedDeliverType);
			request.setAttribute("timeText", timeText);
			request.setAttribute("cardName", cardName);
			request.setAttribute("provinces", PROVICES);
			if (PROVICES_CITY.get(province) != null) {
				request.setAttribute("cities", PROVICES_CITY.get(province));
			}

			request.setAttribute("actualData", resultMap == null ? "" : resultMap);
			request.setAttribute("dataset", resultMap == null ? "" : resultMap.get("dataset"));
			request.setAttribute("categories", resultMap == null ? "" : resultMap.get("categories"));

		} catch (Exception e) {
			logger.info("get user register data chart failed, the error message is: " + e);
		}
		return "back/jsp/chart/user_register";
	}

	@RequestMapping("/goods")
	public String goods(HttpServletRequest request, String startDate, String endDate) {
		logger.info("get goods data chart");
		try {
			// 登陆验证
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			/**
			 * 默认条件查询条件为如下： 默认查询最近两个月的数据
			 */
			Map<String, String> actualData = infoRecordService.getGoodsData(startDate, endDate);
			if (startDate == null && endDate == null) {
				/*
				 * 计算30天前的秒值
				 */
				Date date = new Date(System.currentTimeMillis());
				endDate = sdf.format(date);
				try {
					long startTime = TimeUtil.addDay(endDate, -30).getTime();
					date = new Date(startTime);
					startDate = sdf.format(date);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			request.setAttribute("startDate", startDate);
			request.setAttribute("endDate", endDate);
			request.setAttribute("actualData", actualData);
			request.setAttribute("chartobj", ChartUtil.getChartConfigureParam());
		} catch (Exception e) {
			logger.info("get goods data chart failed, the error message is: " + e);
		}
		return "back/jsp/chart/goods";
	}

	/**
	 * 用户登录统计：比较
	 */
	@RequestMapping("/userlogin")
	public String userlogin(HttpServletRequest request, String terminal, String deliverType, String province, String city,
							String startDate, String endDate, String dateType, String compareType,
							String selectedDeliverType, String selectedProvinces, String selectedCities,
							String timeText, String cardName) {
		logger.info("get user login data chart");
		try {
			// 登陆验证
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			Map<String, String> actualData = new HashMap<String, String>();
			compareType = compareType == null ? "oneLine" : compareType;
			if (dateType == null || "".equals(dateType.trim()) || "undefined".equals(dateType)) {
				dateType = "day";
			}
			if (startDate == null && endDate == null) {
				/*
				 * 计算30天前的秒值
				 */
				// Date date = new Date(System.currentTimeMillis());
				// endDate = sdf.format(date);
				// long startTime = TimeUtil.addDay(endDate, -30).getTime();
				// date = new Date(startTime);
				// startDate = sdf.format(date);
				endDate = sdf.format(new Date());
				startDate = sdf.format(TimeUtil.addDay(endDate, -30));
			}
			timeText = timeText == null ? endDate : timeText;
			if ("oneLine".equals(compareType) || compareType == null) {
				/*
				 * 进入默认条件查询条件为如下： 终端：不限 身份:不限 地区：不限 分析时间:最近一个月(含今天) 按日分析
				 */
				actualData = userService.getUserLoginData(terminal, deliverType, province, city, startDate, endDate, dateType);
				request.setAttribute("chartType", "line");
				request.setAttribute("chartobj", ChartUtil.getChartConfigureParam());
			} else if ("Multi-terminal".equals(compareType.trim())) {
				/*
				 * 多终端比较
				 */
				actualData = userService.getUserLoginByMultiTerminal(deliverType, province, city, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareProvince".equals(compareType.trim())) {
				/*
				 * 获取选择的省的数据
				 */
				actualData = userService.getUserLoginByProvinces(terminal, deliverType, province, city, startDate, endDate, dateType, selectedProvinces);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareCity".equals(compareType.trim())) {
				/*
				 * 市级区域比较
				 */
				actualData = userService.getUserLoginByCity(terminal, deliverType, province, city, startDate, endDate, dateType, selectedCities);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("topArea".equals(compareType.trim())) {
				/*
				 * 地区前五
				 */
				actualData = userService.getUserLoginByTopArea(terminal, deliverType, province, city, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());

			} else if ("topCardId".equals(compareType.trim())) {
				/*
				 * 身份前五
				 */
				actualData = userService.getUserLoginByTopDeliverType(terminal, deliverType, province, city, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("IdCard".equals(compareType.trim())) {
				/*
				 * 身份比较
				 */
				actualData = userService.getUserLoginByDeliverType(terminal, deliverType, province, city, startDate, endDate, dateType, selectedDeliverType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("nearTime".equals(compareType.trim())) {
				/*
				 * 近期同比
				 */
				actualData = userService.getUserLoginByNearTime(terminal, deliverType, province, city, startDate, endDate, dateType);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			} else if ("compareTime".equals(compareType.trim())) {
				/*
				 * 自定义时段对比
				 */
				actualData = userService.getUserLoginByTime(terminal, deliverType, province, city, startDate, endDate, dateType, timeText);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			}

			request.setAttribute("terminal", terminal);
			request.setAttribute("compareType", compareType);
			request.setAttribute("deliverType", deliverType);
			request.setAttribute("curProvince", province);
			request.setAttribute("curCity", city);
			request.setAttribute("startDate", startDate);
			request.setAttribute("endDate", endDate);
			request.setAttribute("dateType", dateType);
			request.setAttribute("selectedProvinces", selectedProvinces);
			request.setAttribute("selectedCities", selectedCities);
			request.setAttribute("selectedDeliverType", selectedDeliverType);
			request.setAttribute("timeText", timeText);
			request.setAttribute("cardName", cardName);
			request.setAttribute("provinces", PROVICES);
			request.setAttribute("checkSignList", checkSignList);
			if (PROVICES_CITY.get(province) != null) {
				request.setAttribute("cities", PROVICES_CITY.get(province));
			}

			request.setAttribute("actualData", actualData == null ? "" : actualData);
			request.setAttribute("dataset", actualData == null ? "" : actualData.get("dataset"));
			request.setAttribute("categories", actualData == null ? "" : actualData.get("categories"));

		} catch (Exception e) {
			logger.info("get user login data chart failed, the error message is: " + e);
		}
		return "back/jsp/chart/user_login";
	}

	/**
	 * 用户付费统计：比较
	 */
	@RequestMapping("/userPayCompare")
	public String userPayCompare(HttpServletRequest request, String compareType, String angle, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String selectedProvinces, String selectedCities, String selectedDeliverType, String timeText, String cardName) {
		logger.info("userPayCompare data chart");
		try {
			// 登陆验证
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}

			if (startDate == null && endDate == null) {
				/*
				 * 计算30天前的秒值
				 */
				// Date date = new Date(System.currentTimeMillis());
				// endDate = sdf.format(date);
				// long startTime = TimeUtil.addDay(endDate, -30).getTime();
				// date = new Date(startTime);
				// startDate = sdf.format(date);
				endDate = sdf.format(new Date());
				startDate = sdf.format(TimeUtil.addDay(endDate, -30));
			}
			compareType = compareType == null ? "oneLine" : compareType;
			dateType = dateType == null ? "day" : dateType;
			timeText = timeText == null ? endDate : timeText;
			/*
			 * 准备数据源
			 */
			Map<String, String> resultMap = new HashMap<String, String>();
			if ("oneLine".equals(compareType.trim())) {
				/*
				 * 单线报表
				 */
				resultMap = userChartService.getUserPayMap(angle, deliverType, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType);
				// 区分是单线报表
				request.setAttribute("chartType", "line");
				request.setAttribute("chartobj", ChartUtil.getChartConfigureParam());
			} else if ("topArea".equals(compareType.trim())) {
				/*
				 * 地区前五
				 */
				resultMap = userChartService.getUserPayMapByTopArea(angle, deliverType, cTimeStart, cTimeEnd, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareProvince".equals(compareType.trim())) {
				/*
				 * 获取选择的省的数据
				 */
				resultMap = userChartService.getUserPayMapByProvinces(angle, deliverType, cTimeStart, cTimeEnd, startDate, endDate, dateType, selectedProvinces);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareCity".equals(compareType.trim())) {
				/*
				 * 市级区域比较
				 */
				resultMap = userChartService.getUserPayMapByCities(angle, deliverType, cTimeStart, cTimeEnd, startDate, endDate, dateType, selectedCities);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("nearTime".equals(compareType.trim())) {
				/*
				 * 近期同比
				 */
				resultMap = userChartService.getUserPayMapByNearTime(angle, deliverType, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			} else if ("compareTime".equals(compareType.trim())) {
				/*
				 * 自定义时段对比
				 */
				resultMap = userChartService.getUserPayMapByTime(angle, deliverType, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType, timeText);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			}
			request.setAttribute("compareType", compareType);
			request.setAttribute("angle", angle);
			request.setAttribute("deliverType", deliverType);
			request.setAttribute("curProvince", province);
			request.setAttribute("curCity", city);
			request.setAttribute("cTimeStart", cTimeStart);
			request.setAttribute("cTimeEnd", cTimeEnd);
			request.setAttribute("startDate", startDate);
			request.setAttribute("endDate", endDate);
			request.setAttribute("dateType", dateType);
			request.setAttribute("selectedProvinces", selectedProvinces);
			request.setAttribute("selectedCities", selectedCities);
			request.setAttribute("selectedDeliverType", selectedDeliverType);
			request.setAttribute("timeText", timeText);
			request.setAttribute("cardName", cardName);
			request.setAttribute("checkSignList", checkSignList);
			request.setAttribute("provinces", PROVICES);
			if (PROVICES_CITY.get(province) != null) {
				request.setAttribute("cities", PROVICES_CITY.get(province));
			}

			request.setAttribute("dataset", resultMap == null ? "" : resultMap.get("dataset"));
			request.setAttribute("categories", resultMap == null ? "" : resultMap.get("categories"));
			request.setAttribute("actualData", resultMap == null ? "" : resultMap);
		} catch (Exception e) {
			logger.info("get userPayCompare chart failed, the error message is: " + e);
		}
		return "back/jsp/chart/user_pay";
	}

	/**
	 * 用户来源统计：比较
	 */
	@RequestMapping("/userSourceCompare")
	public String userSourceCompare(HttpServletRequest request, String compareType, String startDate, String endDate, String source, String sourceRemark, String deliverType, String province, String city, String dateType, String selectedProvinces, String selectedCities, String selectedDeliverType, String timeText, String cardName, String selectedSRemark, String selectedSRemarkName) {
		logger.info("userSourceCompare data chart");
		try {
			// 登陆验证
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}

			if (startDate == null && endDate == null) {
				/*
				 * 计算30天前的秒值
				 */
				// Date date = new Date(System.currentTimeMillis());
				// endDate = sdf.format(date);
				// long startTime = TimeUtil.addDay(endDate, -30).getTime();
				// date = new Date(startTime);
				// startDate = sdf.format(date);
				endDate = sdf.format(new Date());
				startDate = sdf.format(TimeUtil.addDay(endDate, -30));
			}
			compareType = compareType == null ? "oneLine" : compareType;
			dateType = dateType == null ? "day" : dateType;
			timeText = timeText == null ? endDate : timeText;

			request.setAttribute("compareType", compareType);
			request.setAttribute("deliverType", deliverType);
			request.setAttribute("sourceList", sourceList);
			request.setAttribute("curSource", source);
			request.setAttribute("curSourceRemark", sourceRemark);
			request.setAttribute("curProvince", province);
			request.setAttribute("curCity", city);
			request.setAttribute("startDate", startDate);
			request.setAttribute("endDate", endDate);
			request.setAttribute("dateType", dateType);
			request.setAttribute("selectedProvinces", selectedProvinces);
			request.setAttribute("selectedCities", selectedCities);
			request.setAttribute("selectedDeliverType", selectedDeliverType);
			request.setAttribute("timeText", timeText);
			request.setAttribute("cardName", cardName);
			request.setAttribute("selectedSRemark", selectedSRemark);
			request.setAttribute("selectedSRemarkName", selectedSRemarkName);
			request.setAttribute("provinces", PROVICES);
			request.setAttribute("checkSignList", checkSignList);
			if (PROVICES_CITY.get(province) != null) {
				request.setAttribute("cities", PROVICES_CITY.get(province));
			}
			if (source != null && !"0".equals(source)) {
				List<TytSource> sourceRemarkList = TytSourceUtil.getTytSourceSubSet(Long.valueOf(source));
				request.setAttribute("sourceRemarkList", sourceRemarkList);
			}
			/*
			 * 准备数据源
			 */
			Map<String, String> resultMap = new HashMap<String, String>();
			if ("oneLine".equals(compareType.trim())) {
				/*
				 * 单线报表
				 */
				resultMap = userChartService.getUserSourceMap(deliverType, province, city, source, sourceRemark, startDate, endDate, dateType);
				// 区分是单线报表
				request.setAttribute("chartType", "singleLine");
				request.setAttribute("chartobj", ChartUtil.getChartConfigureParam());

			} else if ("topArea".equals(compareType.trim())) {
				/*
				 * 地区前五
				 */
				resultMap = userChartService.getUserSourceMapByTopArea(deliverType, source, sourceRemark, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareProvince".equals(compareType.trim())) {
				/*
				 * 获取选择的省的数据
				 */
				resultMap = userChartService.getUserSourceMapByProvinces(deliverType, source, sourceRemark, startDate, endDate, dateType, selectedProvinces);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareCity".equals(compareType.trim())) {
				/*
				 * 市级区域比较
				 */
				resultMap = userChartService.getUserSourceMapByCities(deliverType, source, sourceRemark, startDate, endDate, dateType, selectedCities);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("topCardId".equals(compareType.trim())) {
				/*
				 * 身份前五
				 */
				resultMap = userChartService.getUserSourceMapByTopCardId(province, city, source, sourceRemark, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("IdCard".equals(compareType.trim())) {
				/*
				 * 身份比较
				 */
				resultMap = userChartService.getUserSourceMapByIdCard(selectedDeliverType, province, city, source, sourceRemark, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("userSource".equals(compareType.trim())) {
				/*
				 * 来源对比
				 */
				// resultMap =
				// userChartService.getUserSourceMapBySource(province, city,
				// startDate, endDate, dateType);
				// // 区分是单线报表还是多线报表
				// request.setAttribute("chartType", "multiLine");
				// request.setAttribute("chartobj",
				// ChartUtil.getMultiLineChartConfigureParam());
			} else if ("nearTime".equals(compareType.trim())) {
				/*
				 * 近期同比
				 */
				resultMap = userChartService.getUserSourceMapByNearTime(deliverType, province, city, source, sourceRemark, startDate, endDate, dateType);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			} else if ("compareTime".equals(compareType.trim())) {
				/*
				 * 自定义时段对比
				 */
				resultMap = userChartService.getUserSourceMapByTime(deliverType, province, city, source, sourceRemark, startDate, endDate, dateType, timeText);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			} else if ("topSourceRemark".equals(compareType.trim())) {
				/*
				 * 来源备注前五对比
				 */
				resultMap = userChartService.getUserSourceMapByTopSourceRemark(deliverType, province, city, startDate, endDate, dateType);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareSRemark".equals(compareType.trim())) {
				/*
				 * 来源备注对比
				 */
				resultMap = userChartService.getUserSourceMapBySourceRemark(deliverType, province, city, startDate, endDate, dateType, selectedSRemark);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			}

			// 准备报表的配置项
			request.setAttribute("dataset", resultMap == null ? "" : resultMap.get("dataset"));
			request.setAttribute("categories", resultMap == null ? "" : resultMap.get("categories"));
			request.setAttribute("actualData", resultMap == null ? "" : resultMap);
		} catch (Exception e) {
			logger.info("get userSourceCompare chart failed, the error message is: " + e);
		}
		return "back/jsp/chart/user_source";
	}

	/**
	 * 用户发货统计：比较
	 */
	@RequestMapping("/userSendGoods")
	public String userSendGoods(HttpServletRequest request, String compareType, String angle, String status, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String selectedProvinces, String selectedCities, String selectedDeliverType, String timeText, String cardName) {
		logger.info("userSendGoods data chart");
		try {
			// 登陆验证
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}

			if (startDate == null && endDate == null) {
				/*
				 * 计算30天前的秒值
				 */
				// Date date = new Date(System.currentTimeMillis());
				// endDate = sdf.format(date);
				// long startTime = TimeUtil.addDay(endDate, -30).getTime();
				// date = new Date(startTime);
				// startDate = sdf.format(date);
				endDate = sdf.format(new Date());
				startDate = sdf.format(TimeUtil.addDay(endDate, -30));
			}
			compareType = compareType == null ? "oneLine" : compareType;
			dateType = dateType == null ? "day" : dateType;
			angle = angle == null ? "1" : angle;
			status = status == null ? "100" : status;
			timeText = timeText == null ? endDate : timeText;
			// 获取坐标省市表中的省
			List<TytGeoDict> list = userChartService.findListTytGeoDictByPid("0");
			request.setAttribute("provinces", list);
			if (province != null && !"".equals(province) && !"allProvince".equals(province)) {
				List<TytGeoDict> cityList = userChartService.findListTytGeoDictByPid(province);
				request.setAttribute("cities", cityList);
			}
			request.setAttribute("compareType", compareType);
			request.setAttribute("angle", angle);
			request.setAttribute("status", status);
			request.setAttribute("deliverType", deliverType);
			request.setAttribute("curProvince", province);
			request.setAttribute("curCity", city);
			request.setAttribute("cTimeStart", cTimeStart);
			request.setAttribute("cTimeEnd", cTimeEnd);
			request.setAttribute("startDate", startDate);
			request.setAttribute("endDate", endDate);
			request.setAttribute("dateType", dateType);
			request.setAttribute("selectedProvinces", selectedProvinces);
			request.setAttribute("selectedCities", selectedCities);
			request.setAttribute("selectedDeliverType", selectedDeliverType);
			request.setAttribute("timeText", timeText);
			request.setAttribute("cardName", cardName);
			request.setAttribute("checkSignList", checkSignList);
			if (province != null && !"".equals(province) && !"allProvince".equals(province)) {
				List<TytGeoDict> curProvince = userChartService.findListTytGeoDictById(province);
				request.setAttribute("curProvince", curProvince == null ? "北京" : curProvince.get(0).getName());
			}
			request.setAttribute("curCity", city);
			/*
			 * 准备数据源
			 */
			Map<String, String> resultMap = new HashMap<String, String>();
			if ("oneLine".equals(compareType.trim())) {
				/*
				 * 单线报表
				 */
				resultMap = userChartService.getUserSendGoodsMap(angle, status, deliverType, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType);
				// 区分是单线报表
				request.setAttribute("chartType", "singleLine");
				request.setAttribute("chartobj", ChartUtil.getChartConfigureParam());

			} else if ("topArea".equals(compareType.trim())) {
				/*
				 * 地区前五
				 */
				resultMap = userChartService.getUserSendGoodsMapByTopArea(angle, status, deliverType, cTimeStart, cTimeEnd, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareProvince".equals(compareType.trim())) {
				/*
				 * 获取选择的省的数据
				 */
				resultMap = userChartService.getUserSendGoodsMapByProvinces(angle, status, deliverType, cTimeStart, cTimeEnd, startDate, endDate, dateType, selectedProvinces);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("compareCity".equals(compareType.trim())) {
				/*
				 * 市级区域比较
				 */
				resultMap = userChartService.getUserSendGoodsMapByCities(angle, status, deliverType, cTimeStart, cTimeEnd, startDate, endDate, dateType, selectedCities);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("topCardId".equals(compareType.trim())) {
				/*
				 * 身份前五
				 */
				resultMap = userChartService.getUserSendGoodsMapByTopCardId(angle, status, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("IdCard".equals(compareType.trim())) {
				/*
				 * 身份比较
				 */
				resultMap = userChartService.getUserSendGoodsMapByIdCard(selectedDeliverType, angle, status, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			} else if ("nearTime".equals(compareType.trim())) {
				/*
				 * 近期同比
				 */
				resultMap = userChartService.getUserSendGoodsMapByNearTime(angle, status, deliverType, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType);

				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			} else if ("compareTime".equals(compareType.trim())) {
				/*
				 * 自定义时段对比
				 */
				resultMap = userChartService.getUserSendGoodsMapByTime(angle, status, deliverType, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType, timeText);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigByTime());
			} else if ("compareStatus".equals(compareType.trim())) {
				/*
				 * 状态对比：人工有效，人工成交，人工总量
				 */
				resultMap = userChartService.getUserSendGoodsMapByStatus(angle, deliverType, province, city, cTimeStart, cTimeEnd, startDate, endDate, dateType);
				// 区分是单线报表还是多线报表
				request.setAttribute("chartType", "multiLine");
				request.setAttribute("chartobj", ChartUtil.getMultiLineChartConfigureParam());
			}

			// request.setAttribute("provinces", PROVICES);
			// if (PROVICES_CITY.get(province) != null) {
			// request.setAttribute("cities", PROVICES_CITY.get(province));
			// }
			// 准备报表的配置项
			request.setAttribute("dataset", resultMap == null ? "" : resultMap.get("dataset"));
			request.setAttribute("categories", resultMap == null ? "" : resultMap.get("categories"));
			request.setAttribute("actualData", resultMap == null ? "" : resultMap);
		} catch (Exception e) {
			logger.info("get userSendGoods chart failed, the error message is: " + e);
		}
		return "back/jsp/chart/user_send_goods";
	}

	/**
	 * 车货搜索分析
	 * 
	 * @return
	 */
	@RequestMapping("/carGoodsSearch")
	public String carGoodsSearch(HttpServletRequest request, String compareType, String angle, String status, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String selectedProvinces, String selectedCities, String selectedDeliverType, String timeText, String cardName) {
		logger.info("carGoodsSearch data chart");
		try {
			// 登陆验证
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}

			if (startDate == null && endDate == null) {
				/*
				 * 计算30天前的秒值
				 */
//				Date date = new Date(System.currentTimeMillis());
//				endDate = sdf.format(date);
//				long startTime = TimeUtil.addDay(endDate, -30).getTime();
//				date = new Date(startTime);
//				startDate = sdf.format(date);
				endDate = sdf.format(new Date());
				startDate = sdf.format(TimeUtil.addDay(endDate, -30));
			}
			compareType = compareType == null ? "oneLine" : compareType;
			dateType = dateType == null ? "day" : dateType;
			angle = angle == null ? "1" : angle;
			timeText = timeText == null ? endDate : timeText;
			Map<String, String> resultMap = new HashMap<String, String>();

			// 获取坐标省市表中的省
			List<TytGeoDict> list = userChartService.findListTytGeoDictByPid("0");
			request.setAttribute("provinces", list);
			if (province != null && !"".equals(province) && !"allProvince".equals(province)) {
				List<TytGeoDict> cityList = userChartService.findListTytGeoDictByPid(province);
				request.setAttribute("cities", cityList);
			}
			// for (TytGeoDict tytGeoDict : list) {
			// List<TytGeoDict> cities =
			// userChartService.findListTytGeoDictByPid(tytGeoDict.getId().toString());
			// for (TytGeoDict dict : cities) {
			// System.out.println(dict.getId() + "-----" + dict.getName());
			// }
			// }
			request.setAttribute("compareType", compareType);
			request.setAttribute("angle", angle);
			request.setAttribute("status", status);
			request.setAttribute("curProvince", province);
			request.setAttribute("curCity", city);
			request.setAttribute("startDate", startDate);
			request.setAttribute("endDate", endDate);
			request.setAttribute("selectedProvinces", selectedProvinces);
			request.setAttribute("selectedCities", selectedCities);
			request.setAttribute("selectedDeliverType", selectedDeliverType);
			request.setAttribute("timeText", timeText);
			request.setAttribute("cardName", cardName);

			if ("oneLine".equals(compareType.trim())) {
				/*
				 * 单线报表
				 */
				resultMap = userChartService.getCarGoodsSearchMap(status, province, city, startDate, endDate);

			} else if ("topArea".equals(compareType.trim())) {
				/*
				 * 地区前五
				 */
				resultMap = userChartService.getCarGoodsSearchMapByTop(status, province, city, startDate, endDate);
			} else if ("compareCity".equals(compareType.trim())) {
				/*
				 * 市级区域比较
				 */
				resultMap = userChartService.getCarGoodsSearchMapByCity(status, startDate, endDate, selectedCities);
			} else if ("nearTime".equals(compareType.trim())) {
				/*
				 * 近期同比
				 */
				resultMap = userChartService.getCarGoodsSearchMapByNearTime(status, province, city, startDate, endDate);
			} else if ("compareTime".equals(compareType.trim())) {
				/*
				 * 自定义时段对比
				 */
				resultMap = userChartService.getCarGoodsSearchMapByTime(status, province, city, startDate, endDate, timeText);
			}
			if (province != null && !"".equals(province) && !"allProvince".equals(province)) {
				List<TytGeoDict> curProvince = userChartService.findListTytGeoDictById(province);
				request.setAttribute("curProvince", curProvince == null ? "北京" : curProvince.get(0).getName());
			}
			request.setAttribute("curCity", city);
			// if (city != null && !"".equals(city)) {
			// List<TytGeoDict> curCity =
			// userChartService.findListTytGeoDictById(city);
			// request.setAttribute("curCity", curCity == null ? "北京市" :
			// curCity.get(0).getName());
			// }
			// List<MultiLineCategory> mlcList = new
			// ArrayList<MultiLineCategory>();// x轴json对象
			// MultiLineCategory mlc = new MultiLineCategory();
			// List<Category> cList = new ArrayList<Category>();
			// Category category = null;
			// category = new Category();
			// category.setLabel("Q1");
			// cList.add(category);
			// category.setLabel("Q2");
			// cList.add(category);
			// category.setLabel("Q3");
			// cList.add(category);
			// category.setLabel("Q4");
			// cList.add(category);
			//
			// mlc.setCategory(cList);
			// mlcList.add(mlc);
			// resultMap.put("categories", gson.toJson(mlcList));
			//
			// // 图表的y轴
			// List<MultiSerisChartData> mscdList = new
			// ArrayList<MultiSerisChartData>();// y轴json对象
			// MultiSerisChartData mscd = null;
			// List<StringData> lineDataList = null;
			// StringData lineData = null;
			//
			// lineDataList = new ArrayList<StringData>();
			// lineData = new StringData();
			// lineData.setValue("10000");
			// lineDataList.add(lineData);
			//
			// lineData.setValue("20000");
			// lineDataList.add(lineData);
			//
			// lineData.setValue("30000");
			// lineDataList.add(lineData);
			//
			// lineData.setValue("40000");
			// lineDataList.add(lineData);
			// mscd = new MultiSerisChartData();
			// mscd.setSeriesname("Previous Year");
			// mscd.setData(lineDataList);
			// mscdList.add(mscd);
			//
			// lineDataList = new ArrayList<StringData>();
			// lineData = new StringData();
			// lineData.setValue("5000");
			// lineDataList.add(lineData);
			//
			// lineData.setValue("6000");
			// lineDataList.add(lineData);
			//
			// lineData.setValue("7000");
			// lineDataList.add(lineData);
			//
			// lineData.setValue("8000");
			// lineDataList.add(lineData);
			// mscd = new MultiSerisChartData();
			// mscd.setSeriesname("Current");
			// mscd.setData(lineDataList);
			// mscdList.add(mscd);

			// resultMap.put("dataset", gson.toJson(mscdList));

			// request.setAttribute("provinces", PROVICES);
			// if (PROVICES_CITY.get(province) != null) {
			// request.setAttribute("cities", PROVICES_CITY.get(province));
			// }
			// 准备报表的配置项
			request.setAttribute("chartobj", ChartUtil.getMscolumn2dConfigureParam());
			request.setAttribute("categories", resultMap == null ? "" : resultMap.get("categories"));
			request.setAttribute("dataset", resultMap == null ? "" : resultMap.get("dataset"));
			request.setAttribute("actualData", resultMap == null ? "" : resultMap);

		} catch (Exception e) {
			logger.info("get carGoodsSearch chart failed, the error message is: " + e);
		}
		return "back/jsp/chart/car_goods_search";
		// return "back/jsp/chart/chart2d";
	}

	/**
	 * 获取所有的地区
	 * 
	 * @param request
	 * @param province
	 * @return
	 */
	@RequestMapping("/findChineseRegion")
	@ResponseBody
	public String findCity(HttpServletRequest request) {
		logger.info("get findChineseRegion ");
		List<TytGeoDict> list = null;
		try {
			list = userChartService.findListTytGeoDict();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return gson.toJson(list);
	}

	/**
	 * 根据省获取该省所有的市
	 * 
	 * @param request
	 * @param province
	 * @return
	 */
	@RequestMapping("/findCity")
	@ResponseBody
	public String findCity(HttpServletRequest request, String province) {
		logger.info("get city by province " + province);
		List<TytGeoDict> list = null;
		try {
			list = userChartService.findListTytGeoDictByPid(province);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return gson.toJson(list);
	}

	/**
	 * 根据省获取该省所有的市和县
	 * 
	 * @param request
	 * @param province
	 * @return
	 */
	@RequestMapping("/getcity")
	@ResponseBody
	public String getcity(HttpServletRequest request, String province) {
		try {
			province = java.net.URLDecoder.decode(province, "UTF-8").replace("省", "");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		logger.info("get city by province " + province);
		List<String> cities = PROVICES_CITY.get(province);
		return gson.toJson(cities);
	}

	/**
	 * 根据用户来源获取用户备注
	 * 
	 * @param request
	 * @param province
	 * @return
	 */
	@RequestMapping("/getSourceRemark")
	@ResponseBody
	public String getSourceRemark(HttpServletRequest request, Long source) {
		logger.info("get SourceRemark by source " + source);
		List<TytSource> sourceRemarkList = TytSourceUtil.getTytSourceSubSet(source);
		return gson.toJson(sourceRemarkList);
	}

}
