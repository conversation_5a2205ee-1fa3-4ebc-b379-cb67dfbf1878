package com.tyt.chart.bean;

/**
 * 封装市每天注册量的实体
 * 
 * <AUTHOR>
 * @date 2016-6-16下午3:33:04
 * @description
 */
public class MultiLineDataBean {
	/* 比较项的名称，可能是省或者市 */
	private String name;
	/* 注册量 */
	private String quality;
	/* 日期 */
	private String date;
	
	private String source;
	private String sourceRemark;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getQuality() {
		return quality;
	}

	public void setQuality(String quality) {
		this.quality = quality;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSourceRemark() {
		return sourceRemark;
	}

	public void setSourceRemark(String sourceRemark) {
		this.sourceRemark = sourceRemark;
	}

	@Override
	public String toString() {
		return "MultiLineDataBean [name=" + name + ", quality=" + quality + ", date=" + date + ", source=" + source + ", sourceRemark="
				+ sourceRemark + "]";
	}

//	@Override
//	public String toString() {
//		return "CityRegisterBean [name=" + name + ", qulity=" + quality + ", date=" + date + "]";
//	}
	
}
