package com.tyt.covergoods.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.tyt.covergoods.bean.req.CoverGoodsDialConfigSaveReq;
import com.tyt.covergoods.bean.req.CoverGoodsDialConfigUpdateReq;
import com.tyt.covergoods.bean.resp.CoverGoodsUserBeansVO;
import com.tyt.covergoods.service.CoverGoodsDialConfigService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytCoverGoodsBeansConfigUser;
import com.tyt.manager.entity.base.TytCoverGoodsDialConfig;
import com.tyt.manager.entity.base.TytCoverGoodsDialConfigUser;
import com.tyt.manager.entity.base.TytCoverGoodsWhiteListConfigUser;
import com.tyt.manager.mapper.base.TytCoverGoodsBeansConfigUserMapper;
import com.tyt.manager.mapper.base.TytCoverGoodsDialConfigMapper;
import com.tyt.manager.mapper.base.TytCoverGoodsDialConfigUserMapper;
import com.tyt.manager.mapper.base.TytCoverGoodsWhiteListConfigUserMapper;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.util.page.CustomPageHelper;
import com.tyt.util.page.PageData;
import com.tyt.util.page.PageParameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 捂货规则配置
 *
 * <AUTHOR>
 * @since 2024/01/12 11:26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoverGoodsDialConfigServiceImpl implements CoverGoodsDialConfigService {

    private static final String COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY = "CoverGoodsDialConfigNTimes";

    private final TytCoverGoodsDialConfigMapper dialConfigMapper;
    private final TytCoverGoodsDialConfigUserMapper dialConfigUserMapper;
    private final ThreadPoolTaskExecutor executor;
    private final TytCoverGoodsBeansConfigUserMapper beansConfigUserMapper;
    private final TytCoverGoodsWhiteListConfigUserMapper whiteListConfigUserMapper;

    /**
     * 捂货相关配置类型 1 捂货设置 2 抢单豆设置  3 捂货白名单设置
     */
    private static final Integer CONFIG_TYPE_COVER_GOODS_CONFIG = 1;
    private static final Integer CONFIG_TYPE_COVER_GOODS_BEANS_CONFIG = 2;
    private static final Integer CONFIG_TYPE_WHITE_LIST_CONFIG = 3;

    /**
     * 启用标识 0未启用，1启用
     */
    private static final Boolean ENABLE_CLOSE = false;
    private static final Boolean ENABLE_OPEN = true;

    @Override
    public PageData<TytCoverGoodsDialConfig> getDialConfigList(Integer configType, Integer pageNum, Integer pageSize) {
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));
        List<TytCoverGoodsDialConfig> goodsDialConfigs =
                dialConfigMapper.selectByConfigTypeAndDelFlagOrderByIdDesc(configType, false);
        return customPageHelper.endPage(goodsDialConfigs);
    }

    @Override
    public TytCoverGoodsDialConfig getDialConfigDetail(Long id) {
        return dialConfigMapper.selectByIdAndDelFlag(id, false);
    }

    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public TytCoverGoodsDialConfig saveDialConfig(CoverGoodsDialConfigSaveReq req, EmployeeQueryBean currentUser) {
        Date now = new Date();

        TytCoverGoodsDialConfig coverGoodsDialConfig = new TytCoverGoodsDialConfig();
        coverGoodsDialConfig.setName(req.getName());
        coverGoodsDialConfig.setEnable(ENABLE_CLOSE);
        coverGoodsDialConfig.setXTimeInSeconds(req.getXTimeInSeconds());
        coverGoodsDialConfig.setYTimeInSeconds(req.getYTimeInSeconds());
        coverGoodsDialConfig.setNTimes(0);   //todo 此处默认为0 6420版本不再使用
        coverGoodsDialConfig.setCreateTime(now);
        coverGoodsDialConfig.setUpdateTime(now);
        coverGoodsDialConfig.setDelFlag(false);
        coverGoodsDialConfig.setGrantNTimes(0); //todo 此处默认为0 6420版本不再使用
        coverGoodsDialConfig.setConfigType(req.getConfigType());
        coverGoodsDialConfig.setOperateUserName(currentUser.getUserName());
        coverGoodsDialConfig.setOperateUserId(currentUser.getId());

        dialConfigMapper.insert(coverGoodsDialConfig);
        return coverGoodsDialConfig;
    }

    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public Long updateDialConfig(CoverGoodsDialConfigUpdateReq req, EmployeeQueryBean requireCurrentUser) {
        TytCoverGoodsDialConfig coverGoodsDialConfig = dialConfigMapper.selectByPrimaryKey(req.getId());
        if (coverGoodsDialConfig == null) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "配置不存在"));
        }

        TytCoverGoodsDialConfig update = new TytCoverGoodsDialConfig();
        Integer configType = coverGoodsDialConfig.getConfigType();
        //捂货设置参数变更
        if (null != configType && configType == CONFIG_TYPE_COVER_GOODS_CONFIG) {
            Integer xTimeInSeconds = req.getXTimeInSeconds();
            if (null != xTimeInSeconds) {
                update.setXTimeInSeconds(xTimeInSeconds);
            }
            Integer yTimeInSeconds = req.getYTimeInSeconds();
            if (null != yTimeInSeconds) {
                update.setYTimeInSeconds(yTimeInSeconds);
            }
        }
        update.setId(req.getId());
        update.setName(req.getName());
        update.setOperateUserId(requireCurrentUser.getId());
        update.setOperateUserName(requireCurrentUser.getUserName());
        dialConfigMapper.updateByPrimaryKeySelective(update);

        return coverGoodsDialConfig.getId();
    }

    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public Long updateDialSwitchConfig(Long id, Boolean enable, EmployeeQueryBean requireCurrentUser) {
        TytCoverGoodsDialConfig coverGoodsDialConfig = dialConfigMapper.selectByPrimaryKey(id);
        if (coverGoodsDialConfig == null) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "配置不存在"));
        }
        TytCoverGoodsDialConfig update = new TytCoverGoodsDialConfig();
        update.setId(id);
        update.setEnable(enable);
        update.setOperateUserId(requireCurrentUser.getId());
        update.setOperateUserName(requireCurrentUser.getUserName());
        dialConfigMapper.updateByPrimaryKeySelective(update);
        //todo 当开启时抢单豆发放待完善
//        if (Boolean.TRUE.equals(enable)&&coverGoodsDialConfig.getConfigType()==CONFIG_TYPE_COVER_GOODS_BEANS_CONFIG
//        && grantBeans(coverGoodsDialConfig)) {
//            // 发送n
//            update.setGrantNTimes(coverGoodsDialConfig.getGrantNTimes() + 1);
//        }
        return coverGoodsDialConfig.getId();
    }


    /**
     * 发送免责卡
     */
    private boolean grantN(TytCoverGoodsDialConfig coverGoodsDialConfig) {
        Integer grantNTimes = coverGoodsDialConfig.getGrantNTimes();
        // 已经发送过, 不用发
        if (grantNTimes > 0) {
            return false;
        }

        Long configId = coverGoodsDialConfig.getId();
        // 每批1000条
        int pageSize = 1000;

        PageHelper.startPage(0, 1);
        List<TytCoverGoodsDialConfigUser> singleUser =
                dialConfigUserMapper.selectByConfigIdAndUserIdOrderByUpdateAsc(configId, null);
        if (!singleUser.isEmpty()) {
            saveNToRedis(coverGoodsDialConfig, singleUser);

            TytCoverGoodsDialConfigUser firstUser = singleUser.get(0);

            executor.submit(() -> {
                Long idStart = firstUser.getId();
                while (idStart > 0L) {
                    try {
                        List<TytCoverGoodsDialConfigUser> goodsDialConfigUsers =
                                dialConfigUserMapper.selectUserIdByConfigIdAndIdStart(configId, idStart, pageSize);

                        if (goodsDialConfigUsers.isEmpty()) {
                            idStart = -1L;
                        } else {
                            saveNToRedis(coverGoodsDialConfig, goodsDialConfigUsers);
                            idStart = goodsDialConfigUsers.get(goodsDialConfigUsers.size() - 1).getId();
                        }
                    } catch (Exception e) {
                        log.error("免责卡发送失败, configId: {}, idStart: {}", configId, idStart, e);
                        idStart = -1L;
                    }
                }
            });
        }
        return true;
    }

    private void saveNToRedis(TytCoverGoodsDialConfig coverGoodsDialConfig,
                              List<TytCoverGoodsDialConfigUser> goodsDialConfigUsers) {
        Map<String, String> configNMap =
                goodsDialConfigUsers.stream().collect(Collectors.toMap(it -> String.valueOf(it.getUserId()),
                        it -> String.valueOf(coverGoodsDialConfig.getNTimes())));
        RedisUtil.mapPut(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, configNMap);
    }

    /**
     * 当抢单豆配置启动时发放抢单豆
     *
     * @param coverGoodsDialConfig
     * @return boolean
     */
    private boolean grantBeans(TytCoverGoodsDialConfig coverGoodsDialConfig) {
        Integer grantNTimes = coverGoodsDialConfig.getGrantNTimes();
        // 已经发送过, 不用发
        if (grantNTimes > 0) {
            return false;
        }

        Long configId = coverGoodsDialConfig.getId();
        // 每批1000条
        int pageSize = 1000;

        PageHelper.startPage(0, 1);
        List<TytCoverGoodsBeansConfigUser> singleUser =
                beansConfigUserMapper.selectByConfigIdAndUserIdOrderByUpdateAsc(configId, null);
        if (!singleUser.isEmpty()) {
            TytCoverGoodsBeansConfigUser firstUser = singleUser.get(0);
            //更新 并 获取首个用户抢单豆个数
            beansConfigUserMapper.updateLeftNumByUserIdAndConfigId(configId, firstUser.getUserId());
            CoverGoodsUserBeansVO beansUserVO =
                    beansConfigUserMapper.selectTotalBeansLeftNumByUserId(firstUser.getUserId());
            RedisUtil.mapPut(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, beansUserVO.getUserId().toString(),
                    beansUserVO.getTotalLeftNum() == null ? "0" : beansUserVO.getTotalLeftNum().toString());

            executor.submit(() -> {
                Long idStart = firstUser.getId();
                while (idStart > 0L) {
                    try {
                        List<TytCoverGoodsBeansConfigUser> beansConfigUsers =
                                beansConfigUserMapper.selectUserIdByConfigIdAndIdStart(configId, idStart, pageSize);

                        if (beansConfigUsers.isEmpty()) {
                            idStart = -1L;
                        } else {
                            List<Long> userIds = new ArrayList<>();
                            for (TytCoverGoodsBeansConfigUser beansConfigUser : beansConfigUsers) {
                                userIds.add(beansConfigUser.getUserId());
                            }
                            beansConfigUserMapper.updateLeftNumByUserIdsAndConfigId(configId, userIds);
                            List<CoverGoodsUserBeansVO> beansUserVOS =
                                    beansConfigUserMapper.selectTotalBeansLeftNumByUserIds(userIds);
                            saveUsersBeansToRedis(beansUserVOS);
                            idStart = beansConfigUsers.get(beansConfigUsers.size() - 1).getId();
                        }
                    } catch (Exception e) {
                        log.error("免责卡发送失败, configId: {}, idStart: {}", configId, idStart, e);
                        idStart = -1L;
                    }
                }
            });
        }
        return true;
    }


    private void saveUsersBeansToRedis(List<CoverGoodsUserBeansVO> beansUserVOS) {
        Map<String, String> configNMap =
                beansUserVOS.stream().collect(Collectors.toMap(it -> String.valueOf(it.getUserId()),
                        it -> String.valueOf(it.getTotalLeftNum())));
        RedisUtil.mapPut(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, configNMap);
    }

    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void deleteDialConfigUsers(Long configId, List<Long> userIds, Integer configType) {
        if (CONFIG_TYPE_COVER_GOODS_CONFIG == configType) {
            dialConfigUserMapper.deleteByConfigIdAndUserIds(configId, userIds);
        }
        if (CONFIG_TYPE_COVER_GOODS_BEANS_CONFIG == configType) {
            beansConfigUserMapper.deleteByConfigIdAndUserIds(configId, userIds);
        }
        if (CONFIG_TYPE_WHITE_LIST_CONFIG == configType) {
            whiteListConfigUserMapper.deleteByConfigIdAndUserIds(configId, userIds);
        }
    }

    @Override
    public PageData<TytCoverGoodsDialConfigUser>
    getDialConfigUserList(Integer pageNum, Integer pageSize, Long configId, Long userId) {
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));

        List<TytCoverGoodsDialConfigUser> result =
                dialConfigUserMapper.selectByConfigIdAndUserIdOrderByUpdateDesc(configId, userId);
        return customPageHelper.endPage(result);
    }

    @Override
    public PageData<TytCoverGoodsWhiteListConfigUser>
    getDialConfigWhiteUserList(Integer pageNum, Integer pageSize, Long configId, Long userId) {
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));

        List<TytCoverGoodsWhiteListConfigUser> result =
                whiteListConfigUserMapper.selectByConfigIdAndUserIdOrderByUpdateDesc(configId, userId);
        return customPageHelper.endPage(result);
    }

    @Override
    public PageData<TytCoverGoodsBeansConfigUser>
    getDialConfigBeansUserList(Integer pageNum, Integer pageSize, Long configId, Long userId) {
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));

        List<TytCoverGoodsBeansConfigUser> result =
                beansConfigUserMapper.selectByConfigIdAndUserIdOrderByUpdateDesc(configId, userId);
        return customPageHelper.endPage(result);
    }


    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void deleteConfigUserById(Long id, Long userId, Long configId) {
        TytCoverGoodsDialConfig coverGoodsDialConfig = dialConfigMapper.selectByPrimaryKey(configId);
        if (null != coverGoodsDialConfig) {
            Integer configType = coverGoodsDialConfig.getConfigType();
            if (CONFIG_TYPE_COVER_GOODS_CONFIG == configType) {
                dialConfigUserMapper.deleteByPrimaryKey(id);
            }
            if (CONFIG_TYPE_COVER_GOODS_BEANS_CONFIG == configType) {
                beansConfigUserMapper.deleteByPrimaryKey(id);
            }
            if (CONFIG_TYPE_WHITE_LIST_CONFIG == configType) {
                whiteListConfigUserMapper.deleteByPrimaryKey(id);
            }
        }
    }

    /**
     * 抢单豆用户删除(不再存放redis 废弃)
     *
     * @param id
     * @param userId
     * @return void
     */
    private void deleteBeansUserById(Long id, Long userId) {
        TytCoverGoodsBeansConfigUser beansConfigUser = beansConfigUserMapper.selectByPrimaryKey(id);
        if (null != beansConfigUser) {
            CoverGoodsUserBeansVO userBeansVO =
                    beansConfigUserMapper.selectTotalBeansLeftNumByUserId(Long.valueOf(userId));
            Integer totalLeftNum = userBeansVO.getTotalLeftNum() == null ? 0 : userBeansVO.getTotalLeftNum();
            Integer totalNum = beansConfigUser.getTotalNum() == null ? 0 : beansConfigUser.getTotalNum();
            if (userBeansVO != null) {
                log.info("beansConfigUserMapper selectTotalBeansLeftNumByUserId userId:{} userBeansVO:{}", userId,
                        JSON.toJSONString(userBeansVO));
                RedisUtil.mapPut(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, userId.toString(),
                        Integer.valueOf(totalLeftNum - totalNum).toString());
            } else {
                log.info("beansConfigUserMapper selectTotalBeansLeftNumByUserId userId:{} ", userId);
                RedisUtil.mapPut(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, userId.toString(),
                        userBeansVO.getTotalLeftNum() == null ? "0" : userBeansVO.getTotalLeftNum().toString());
            }
            beansConfigUserMapper.deleteByPrimaryKey(id);
        }
    }
}
