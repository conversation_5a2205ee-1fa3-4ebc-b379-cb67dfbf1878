package com.tyt.covergoods.service;

import com.tyt.covergoods.bean.CoverGoodsConfigReq;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytCoverGoodsConfig;

import java.util.List;

/**
 * @author: helian
 * @since: 2024/01/11 15:59
 */
public interface CoverGoodsConfigService {
    /**
     * 获取配置列表
     *
     * @return
     */
    List<TytCoverGoodsConfig> getConfigList();

    /**
     * 更新捂货配置
     *
     * @param req
     * @param currentUser
     */
    void updateConfig(CoverGoodsConfigReq req, EmployeeQueryBean currentUser);
}
