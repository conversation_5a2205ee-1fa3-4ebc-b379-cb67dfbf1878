package com.tyt.covergoods.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tyt.covergoods.bean.req.CarGoodsDialTimesConfigReq;
import com.tyt.covergoods.bean.req.DriverGoodsDialTimesConfigReq;
import com.tyt.covergoods.bean.req.EnterpriseGoodsDialTimesConfigReq;
import com.tyt.covergoods.bean.req.OrderCoverGoodsDialTimesConfigReq;
import com.tyt.covergoods.bean.req.OwnerGoodsDialTimesConfigReq;
import com.tyt.covergoods.bean.req.UserCreditCoverGoodsDialTimesConfigReq;

/**
 * <AUTHOR>
 * @since 2024/03/06 14:30
 */
public interface CoverGoodsDialTimesConfigService {

    void updateDefaultUserCreditConfig(UserCreditCoverGoodsDialTimesConfigReq req) throws JsonProcessingException;

    void updateDefaultOrderConfig(OrderCoverGoodsDialTimesConfigReq req) throws JsonProcessingException;

    void updateDefaultCarConfig(CarGoodsDialTimesConfigReq req) throws JsonProcessingException;

    void updateDefaultDriverConfig(DriverGoodsDialTimesConfigReq req) throws JsonProcessingException;

    void updateDefaultEnterpriseConfig(EnterpriseGoodsDialTimesConfigReq req) throws JsonProcessingException;

    void updateDefaultOwnerConfig(OwnerGoodsDialTimesConfigReq req) throws JsonProcessingException;

    String getDetailDefaultConfig(Integer type);
}
