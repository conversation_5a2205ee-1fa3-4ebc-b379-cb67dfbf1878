package com.tyt.covergoods.controller;

import com.tyt.covergoods.bean.CoverGoodsConfigReq;
import com.tyt.covergoods.service.CoverGoodsConfigService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.web.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 捂货业务
 *
 * @author: helian
 * @since: 2024/01/11 15:55
 */
@RestController
@RequestMapping("/cover/goods")
public class CoverGoodsConfigController extends BaseController {
    @Autowired
    private CoverGoodsConfigService coverGoodsConfigService;

    /**
     * 获取捂货配置列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResultMsgBean getConfigList() {

        return ResultMsgBean.successResponse(coverGoodsConfigService.getConfigList());
    }

    /**
     * 获取捂货配置列表
     *
     * @returnxl
     */
    @PostMapping("/update")
    public ResultMsgBean updateConfig(@Validated @RequestBody CoverGoodsConfigReq req, HttpServletRequest request) {
        EmployeeQueryBean currentUser = getRequireCurrentUser(request);
        coverGoodsConfigService.updateConfig(req, currentUser);
        return ResultMsgBean.successResponse();
    }


}
