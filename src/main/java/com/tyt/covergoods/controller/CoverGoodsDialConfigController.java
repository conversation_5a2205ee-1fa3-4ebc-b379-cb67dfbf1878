package com.tyt.covergoods.controller;

import cn.hutool.core.lang.Pair;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.tyt.covergoods.bean.CoverGoodsDialConfigExcelUploadBean;
import com.tyt.covergoods.bean.req.CoverGoodsDialConfigSaveReq;
import com.tyt.covergoods.bean.req.CoverGoodsDialConfigUpdateReq;
import com.tyt.covergoods.bean.req.CoverGoodsDialConfigUserDeleteReq;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigDetailVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigListVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigSaveVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigTemplateUploadVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigUpdateVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigUserListVO;
import com.tyt.covergoods.service.CoverGoodsDialConfigService;
import com.tyt.manager.entity.base.TytCoverGoodsBeansConfigUser;
import com.tyt.manager.entity.base.TytCoverGoodsDialConfig;
import com.tyt.manager.entity.base.TytCoverGoodsDialConfigUser;
import com.tyt.manager.entity.base.TytCoverGoodsWhiteListConfigUser;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.model.TypedResultMsgBean;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.easyExcel.ConcurrentHandleReadListener;
import com.tyt.util.page.PageData;
import com.tyt.web.base.BaseController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.tyt.covergoods.bean.DialConfigCovert.BEANS_USER_LIST_MAPPING_FUNCTION;
import static com.tyt.covergoods.bean.DialConfigCovert.DETAIL_MAPPING_FUNCTION;
import static com.tyt.covergoods.bean.DialConfigCovert.LIST_MAPPING_FUNCTION;
import static com.tyt.covergoods.bean.DialConfigCovert.SAVE_MAPPING_FUNCTION;
import static com.tyt.covergoods.bean.DialConfigCovert.USER_LIST_MAPPING_FUNCTION;
import static com.tyt.covergoods.bean.DialConfigCovert.WHITE_USER_LIST_MAPPING_FUNCTION;

/**
 * 捂货规则配置 所有接口调用时添加 /manage_new 前缀
 *
 * <AUTHOR>
 * @since 2024/01/12 11:26
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/cover/goods/dial/config")
public class CoverGoodsDialConfigController extends BaseController {

    private final CoverGoodsDialConfigService coverGoodsDialConfigService;


    /**
     * 获取捂货配置列表
     *
     * @param pageNum    pageNum
     * @param pageSize   pageSize
     * @param configType 捂货相关配置类型 1 捂货设置 2 抢单豆设置  3 捂货白名单设置
     * @return TypedResultMsgBean<PageData < CoverGoodsDialConfigListVO>>
     */
    @GetMapping("/list")
    public TypedResultMsgBean<PageData<CoverGoodsDialConfigListVO>>
    getDialConfigList(@RequestParam(required = false) Integer pageNum,
                      @RequestParam(required = false) Integer pageSize,
                      @RequestParam(required = false) Integer configType) {
        PageData<TytCoverGoodsDialConfig> dialConfigList = coverGoodsDialConfigService.getDialConfigList(configType,
                pageNum,
                pageSize);
        return TypedResultMsgBean.success(dialConfigList.covert(LIST_MAPPING_FUNCTION));
    }


    /**
     * 根据Id获取捂货配置详情
     *
     * @param id 配置列表主键Id
     * @return TypedResultMsgBean<CoverGoodsDialConfigDetailVO>
     */
    @GetMapping("/detail")
    public TypedResultMsgBean<CoverGoodsDialConfigDetailVO> getDialConfigDetail(@RequestParam Long id) {
        TytCoverGoodsDialConfig coverGoodsDialConfig = coverGoodsDialConfigService.getDialConfigDetail(id);
        return TypedResultMsgBean.success(DETAIL_MAPPING_FUNCTION.apply(coverGoodsDialConfig));
    }

    /**
     * 添加捂货配置
     *
     * @param req           添加入参
     * @param bindingResult
     * @return TypedResultMsgBean<CoverGoodsDialConfigSaveVO>
     */
    @PostMapping("/save")
    public TypedResultMsgBean<CoverGoodsDialConfigSaveVO>
    saveDialConfig(@RequestBody @Validated CoverGoodsDialConfigSaveReq req, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage()));
        }
        TytCoverGoodsDialConfig result = coverGoodsDialConfigService.saveDialConfig(req, getRequireCurrentUser());
        return TypedResultMsgBean.success(SAVE_MAPPING_FUNCTION.apply(result));
    }


    /**
     * 编辑捂货配置
     *
     * @param req           编辑入参
     * @param bindingResult
     * @return TypedResultMsgBean<CoverGoodsDialConfigUpdateVO>
     */
    @PostMapping("/update")
    public TypedResultMsgBean<CoverGoodsDialConfigUpdateVO> updateDialConfig(@RequestBody @Validated CoverGoodsDialConfigUpdateReq req,
                                                                             BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage()));
        }
        Long id = coverGoodsDialConfigService.updateDialConfig(req, getRequireCurrentUser());

        CoverGoodsDialConfigUpdateVO coverGoodsDialConfigUpdateVO = new CoverGoodsDialConfigUpdateVO();
        coverGoodsDialConfigUpdateVO.setId(id);
        return TypedResultMsgBean.success(coverGoodsDialConfigUpdateVO);
    }

    /**
     * 捂货配置启用、禁用状态更新
     *
     * @param id     配置列表主键Id
     * @param enable
     * @return TypedResultMsgBean<CoverGoodsDialConfigUpdateVO>
     */
    @PostMapping("/updateSwitch")
    public TypedResultMsgBean<CoverGoodsDialConfigUpdateVO> updateSwitch(@RequestParam Long id,
                                                                         @RequestParam Boolean enable) {
        Long returnId = coverGoodsDialConfigService.updateDialSwitchConfig(id, enable, getRequireCurrentUser());
        CoverGoodsDialConfigUpdateVO coverGoodsDialConfigUpdateVO = new CoverGoodsDialConfigUpdateVO();
        coverGoodsDialConfigUpdateVO.setId(returnId);
        return TypedResultMsgBean.success(coverGoodsDialConfigUpdateVO);
    }

    /**
     * 捂货规则名单用户列表查询
     *
     * @param configId 配置id 必传
     * @param userId   用户id, 筛选条件，选传
     */
    @GetMapping("/user/list")
    public TypedResultMsgBean<PageData<CoverGoodsDialConfigUserListVO>>
    getDialConfigUserList(@RequestParam(required = false) Integer pageNum,
                          @RequestParam(required = false) Integer pageSize,
                          @RequestParam Long configId,
                          @RequestParam(required = false) Long userId) {
        PageData<TytCoverGoodsDialConfigUser>
                result = coverGoodsDialConfigService.getDialConfigUserList(pageNum, pageSize, configId, userId);
        return TypedResultMsgBean.success(result.covert(USER_LIST_MAPPING_FUNCTION));

    }

    /**
     * 捂货规则白名单用户列表查询
     *
     * @param configId 配置id 必传
     * @param userId   用户id, 筛选条件，选传
     */
    @GetMapping("/white/user/list")
    public TypedResultMsgBean<PageData<CoverGoodsDialConfigUserListVO>>
    getDialConfigWhiteUserList(@RequestParam(required = false) Integer pageNum,
                               @RequestParam(required = false) Integer pageSize,
                               @RequestParam Long configId,
                               @RequestParam(required = false) Long userId) {
        PageData<TytCoverGoodsWhiteListConfigUser>
                result = coverGoodsDialConfigService.getDialConfigWhiteUserList(pageNum, pageSize, configId, userId);
        return TypedResultMsgBean.success(result.covert(WHITE_USER_LIST_MAPPING_FUNCTION));
    }

    /**
     * 捂货规则抢单豆用户列表查询
     *
     * @param configId 配置id 必传
     * @param userId   用户id, 筛选条件，选传
     */
    @GetMapping("/beans/user/list")
    public TypedResultMsgBean<PageData<CoverGoodsDialConfigUserListVO>>
    getDialConfigBeansUserList(@RequestParam(required = false) Integer pageNum,
                               @RequestParam(required = false) Integer pageSize,
                               @RequestParam Long configId,
                               @RequestParam(required = false) Long userId) {
        PageData<TytCoverGoodsBeansConfigUser>
                result = coverGoodsDialConfigService.getDialConfigBeansUserList(pageNum, pageSize, configId, userId);
        return TypedResultMsgBean.success(result.covert(BEANS_USER_LIST_MAPPING_FUNCTION));
    }

    /**
     * 根据上传模板和配置Id 删除捂货配置导入用户
     *
     * @param configId     配置id
     * @param templateFile
     * @return TypedResultMsgBean<Object>
     */
    @PostMapping("/user/delete_all")
    public TypedResultMsgBean<Object>
    deleteAllDialConfigUser(@RequestParam Long configId,
                            MultipartFile templateFile) throws IOException {
        if (templateFile == null) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未选择上传文件"));
        }

        TytCoverGoodsDialConfig dialConfigDetail = coverGoodsDialConfigService.getDialConfigDetail(configId);
        if (dialConfigDetail == null) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "规则不存在"));
        }
        AtomicReference<ConcurrentHandleReadListener.HandleResult> reference = new AtomicReference<>();
        ReadListener<CoverGoodsDialConfigExcelUploadBean> listener =
                new ConcurrentHandleReadListener<CoverGoodsDialConfigExcelUploadBean>(1000, 20) {

                    @Override
                    protected HandleResult handleDataPairs(List<Pair<ReadRowHolder,
                            CoverGoodsDialConfigExcelUploadBean>> pairs, AnalysisContext context) {
                        List<ReadRowHolder> failureReadRowHolder = new ArrayList<>();

                        List<Long> userIds = pairs.stream()
                                .filter(Objects::nonNull)
                                .map(it -> {
                                    Optional<Long> optional = Optional.of(it)
                                            .map(Pair::getValue)
                                            .map(CoverGoodsDialConfigExcelUploadBean::getUserId);

                                    if (optional.isPresent()) {
                                        return optional.get();
                                    }
                                    failureReadRowHolder.add(it.getKey());
                                    return null;
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        try {
                            coverGoodsDialConfigService.deleteDialConfigUsers(configId, userIds,
                                    dialConfigDetail.getConfigType());
                            return new HandleResult(userIds.size(), failureReadRowHolder.size(),
                                    failureReadRowHolder);
                        } catch (Exception e) {
                            log.error("捂货规则配置导入失败", e);
                            return new HandleResult(0, pairs.size(),
                                    pairs.stream().map(Pair::getKey).collect(Collectors.toList()));
                        }
                    }

                    @Override
                    protected void doAfterAllFinished(HandleResult handleResult, AnalysisContext context) {
                        reference.set(handleResult);
                    }

                    @Override
                    protected HandleResult handleOnException(Exception exception, AnalysisContext context) {
                        return new HandleResult(0, 1, Collections.singletonList(context.readRowHolder()));
                    }
                };

        EasyExcelFactory.read(templateFile.getInputStream(),
                        CoverGoodsDialConfigExcelUploadBean.class,
                        listener)
                .sheet().doRead();

        ConcurrentHandleReadListener.HandleResult handleResult = reference.get();
        CoverGoodsDialConfigTemplateUploadVO templateUploadVO = new CoverGoodsDialConfigTemplateUploadVO();
        if (handleResult != null) {
            templateUploadVO.setSuccessCount(handleResult.getSuccessCount());
            templateUploadVO.setFailureCount(handleResult.getFailureCount());
            templateUploadVO.setTotalCount(handleResult.getFailureCount() + handleResult.getSuccessCount());
        }
        return TypedResultMsgBean.success(templateUploadVO);
    }


    /**
     * 捂货规则导入用户名单删除
     *
     * @param req           请求入参
     * @param bindingResult
     * @return TypedResultMsgBean<Object>
     */
    @PostMapping("/user/delete")
    public TypedResultMsgBean<Object> updateDialConfigUser(@RequestBody @Validated CoverGoodsDialConfigUserDeleteReq req,
                                                           BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage()));
        }
        coverGoodsDialConfigService.deleteConfigUserById(req.getId(), req.getUserId(), req.getConfigId());
        return TypedResultMsgBean.success(null);
    }
}
