package com.tyt.covergoods.bean.req;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @since 2024/03/04 14:29
 */
@Data
public class OrderCoverGoodsDialTimesConfigReq {

    /**
     * 抢单豆
     */
    @Min(0)
    private Integer num = 0;

    /**
     * 上限
     */
    @Min(0)
    private Integer maxNum = 0;

    /**
     * 有效期
     */
    @Min(0)
    private Integer expireDays = 0;
}
