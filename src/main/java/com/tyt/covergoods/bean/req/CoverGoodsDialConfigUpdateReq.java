package com.tyt.covergoods.bean.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/01/12 11:28
 */
@Data
public class CoverGoodsDialConfigUpdateReq {

    /**
     * id
     */
    private Long id;

    /**
     * 配置名
     */

    private String name;

    /**
     * 列表倒计时
     */
    @JsonProperty("xTimeInSeconds")
    private Integer xTimeInSeconds;

    /**
     * 详情倒计时
     */
    @JsonProperty("yTimeInSeconds")
    private Integer yTimeInSeconds;

}
