package com.tyt.covergoods.bean.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @since 2024/01/12 11:28
 */
@Data
@Slf4j
public class CoverGoodsDialConfigSaveReq {
    /**
     * 捂货相关配置类型 1 捂货设置 2 抢单豆设置  3 捂货白名单设置
     */
    @NotNull(message = "捂货相关配置类型不能为空")
    private Integer configType;

    /**
     * 配置名
     */
    @NotNull(message = "配置名不能为空")
    @Size(min = 1, max = 12, message = "配置名长度最长12字符")
    private String name;

    /**
     * 列表倒计时 当configType=1时不能为空
     */
    @JsonProperty("xTimeInSeconds")
    private Integer xTimeInSeconds;

    /**
     * 详情倒计时  当configType=1时不能为空
     */
    @JsonProperty("yTimeInSeconds")
    private Integer yTimeInSeconds;

}
