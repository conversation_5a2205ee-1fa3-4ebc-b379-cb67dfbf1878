package com.tyt.covergoods.bean.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/01/12 11:28
 */
@Data
public class CoverGoodsDialConfigUserDeleteReq {
    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * userId
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * configId
     */
    @NotNull(message = "configId不能为空")
    private Long configId;

}

