package com.tyt.covergoods.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
*  抢单豆名单上传实体
* <AUTHOR>
* @since 2024/2/20 16:44
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoverGoodsBeansConfigExcelUploadBean {

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("名称")
    private String beansName;

    @ExcelProperty("抢单豆")
    private Integer totalNum;

    @ExcelProperty("有效期")
    private Date validateTime;
}
