package com.tyt.covergoods.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/01/15 13:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoverGoodsDialTimesConfigExcelUploadBean {

    @ExcelProperty("用户ID")
    private Long userId;

    @ExcelProperty("名称")
    private String reason;

    @ExcelProperty("抢单豆")
    private Integer dialTimes;

    @ExcelProperty("有效期")
    private Integer expireDays;
}
