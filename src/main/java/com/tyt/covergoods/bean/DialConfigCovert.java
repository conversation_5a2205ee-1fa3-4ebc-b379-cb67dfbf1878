package com.tyt.covergoods.bean;

import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigDetailVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigListVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigSaveVO;
import com.tyt.covergoods.bean.resp.CoverGoodsDialConfigUserListVO;
import com.tyt.manager.entity.base.TytCoverGoodsBeansConfigUser;
import com.tyt.manager.entity.base.TytCoverGoodsDialConfig;
import com.tyt.manager.entity.base.TytCoverGoodsDialConfigUser;
import com.tyt.manager.entity.base.TytCoverGoodsWhiteListConfigUser;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/01/15 10:54
 */
public class DialConfigCovert {

    private DialConfigCovert() {
    }

    public static final Function<TytCoverGoodsDialConfig, CoverGoodsDialConfigListVO> LIST_MAPPING_FUNCTION =
            it -> {
                CoverGoodsDialConfigListVO result = new CoverGoodsDialConfigListVO();
                if (it == null) {
                    return result;
                }

                result.setId(it.getId());
                result.setName(it.getName());
                result.setConfigType(it.getConfigType());
                result.setXTimeInSeconds(it.getXTimeInSeconds());
                result.setYTimeInSeconds(it.getYTimeInSeconds());
                result.setNTimes(it.getNTimes());
                result.setEnable(it.getEnable());
                return result;
            };

    public static final Function<TytCoverGoodsDialConfig, CoverGoodsDialConfigDetailVO> DETAIL_MAPPING_FUNCTION =
            it -> {
                CoverGoodsDialConfigDetailVO result = new CoverGoodsDialConfigDetailVO();
                if (it == null) {
                    return result;
                }

                result.setId(it.getId());
                result.setName(it.getName());
                result.setConfigType(it.getConfigType());
                result.setXTimeInSeconds(it.getXTimeInSeconds());
                result.setYTimeInSeconds(it.getYTimeInSeconds());
                result.setNTimes(it.getNTimes());
                result.setEnable(it.getEnable());
                return result;
            };

    public static final Function<TytCoverGoodsDialConfig, CoverGoodsDialConfigSaveVO> SAVE_MAPPING_FUNCTION =
            it -> {
                CoverGoodsDialConfigSaveVO result = new CoverGoodsDialConfigSaveVO();
                if (it == null) {
                    return result;
                }

                result.setId(it.getId());
                return result;
            };

    public static final Function<TytCoverGoodsDialConfigUser, CoverGoodsDialConfigUserListVO> USER_LIST_MAPPING_FUNCTION =
            it -> {
                CoverGoodsDialConfigUserListVO result = new CoverGoodsDialConfigUserListVO();
                if (it == null) {
                    return result;
                }

                result.setId(it.getId());
                result.setUserId(it.getUserId());
                result.setOperateUserId(it.getOperateUserId());
                result.setOperateUserName(it.getOperateUserName());
                result.setTransportDialConfigId(it.getDialConfigId());
                return result;
            };

    public static final Function<TytCoverGoodsWhiteListConfigUser, CoverGoodsDialConfigUserListVO> WHITE_USER_LIST_MAPPING_FUNCTION =
            it -> {
                CoverGoodsDialConfigUserListVO result = new CoverGoodsDialConfigUserListVO();
                if (it == null) {
                    return result;
                }

                result.setId(it.getId());
                result.setUserId(it.getUserId());
                result.setOperateUserId(it.getOperateUserId());
                result.setOperateUserName(it.getOperateUserName());
                result.setTransportDialConfigId(it.getDialConfigId());
                return result;
            };

    public static final Function<TytCoverGoodsBeansConfigUser, CoverGoodsDialConfigUserListVO> BEANS_USER_LIST_MAPPING_FUNCTION =
            it -> {
                CoverGoodsDialConfigUserListVO result = new CoverGoodsDialConfigUserListVO();
                if (it == null) {
                    return result;
                }

                result.setId(it.getId());
                result.setUserId(it.getUserId());
                result.setOperateUserId(it.getOperateUserId());
                result.setOperateUserName(it.getOperateUserName());
                result.setTransportDialConfigId(it.getDialConfigId());
                return result;
            };
}
