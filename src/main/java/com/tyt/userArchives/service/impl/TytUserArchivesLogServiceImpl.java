package com.tyt.userArchives.service.impl;

import com.tyt.userArchives.service.TytUserArchivesLogService;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytUserArchivesLog;
import com.tyt.service.base.BaseServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("tytUserArchivesLogService")
public class TytUserArchivesLogServiceImpl extends BaseServiceImpl<TytUserArchivesLog, Long> implements TytUserArchivesLogService {

    private Logger logger = LoggerFactory.getLogger(TytUserArchivesLogServiceImpl.class);

    @Resource(name = "tytUserArchivesLogDao")
    public void setBaseDao(BaseDao<TytUserArchivesLog, Long> tytUserArchivesDao) {
        super.setBaseDao(tytUserArchivesDao);
    }
    
    /**
     * @Description  更新用户档案日志记录的状态  是否有效：0.无效 1.有效
     * <AUTHOR>
     * @Date  2019/10/16 14:14
     * @Param [userBuyGoodsId, isValid]
     * @return void
     **/
    @Override
    public void updateUserArchivesLogStatus(Long userBuyGoodsId, Integer isValid) {

        String sql = "update tyt_user_archives_log set is_valid = ? where user_buy_goods_id = ?";
        Object[] params = new Object[]{isValid,userBuyGoodsId};

        this.getBaseDao().executeUpdateSql(sql, params);
    }
}
