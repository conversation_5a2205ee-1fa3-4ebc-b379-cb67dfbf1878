package com.tyt.mybatis.mapper;

import com.tyt.model.TytUpgradeTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface TytUpgradeTemplateDao {
    /**
     * 模版列表
     * <AUTHOR>
     * @return
     * @date 2020-07-30 19:19:41
     */
    @Select(" select id, template_name AS templateName, template_pic AS templatePic, " +
        "cargo_station_android_url AS cargoStationAndroidUrl, cargo_station_ios_url AS cargoStationIosUrl, " +
        "car_owner_android_url AS carOwnerAndroidUrl, car_owner_ios_url AS carOwnerIosUrl, creator, " +
        "creator_id AS creatorId, operator, operator_id AS operatorId, ctime, mtime from tyt_upgrade_template ORDER BY id DESC")
    List<TytUpgradeTemplate> queryList();

    /**
     * 根据ID获取模版
     * @param id
     * <AUTHOR>
     * @return
     * @date 2020-07-30 19:20:11
     */
    @Select(" select id, template_name AS templateName, template_pic AS templatePic, " +
        "cargo_station_android_url AS cargoStationAndroidUrl, cargo_station_ios_url AS cargoStationIosUrl, " +
        "car_owner_android_url AS carOwnerAndroidUrl, car_owner_ios_url AS carOwnerIosUrl, creator, " +
        "creator_id AS creatorId, operator, operator_id AS operatorId, ctime, mtime from tyt_upgrade_template where id = #{id}")
    TytUpgradeTemplate findById(long id);

    /**
     * 修改模版
     * @param tytUpgradeTemplate 模版信息
     * <AUTHOR>
     * @return
     * @date 2020-07-30 19:20:20
     */
    @Update("<script> " +
        "UPDATE tyt_upgrade_template " +
        "SET template_name = #{templateName}," +
        " <if test='templatePic != null and templatePic!= \"\" '>" +
        "  template_pic = #{templatePic}, </if>" +
        "cargo_station_android_url = #{cargoStationAndroidUrl}," +
        "cargo_station_ios_url = #{cargoStationIosUrl}," +
        "car_owner_android_url = #{carOwnerAndroidUrl}," +
        "car_owner_ios_url = #{carOwnerIosUrl}," +
        "operator = #{operator}," +
        "operator_id = #{operatorId} " +
        " WHERE id = #{id};" +
        " </script>")
    int updateTemplate(TytUpgradeTemplate tytUpgradeTemplate);
}
