package com.tyt.mybatis.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

@Mapper
public interface TransportMtMapper {
    /**
     * 一个货物对应一个货主手机
     * @param msgID
     * @return
     */
    @Select("select shipper_phone from tyt_transport_mt where src_msg_id = #{msgID} limit 1")
    String getShipperPhoneByMsgID(@Param("msgID") String msgID);

    @Select("select company, busi_line as busiLine from tyt_transport_mt where shipper_phone = #{phone} order by ctime desc limit 1")
    Map<String,String> getLatestTransportInfo(@Param("phone") String phone);

    @Select("select status from tyt_transport_mt where src_msg_id = #{msgID} order by id desc limit 1")
    int getStatusBySrcMsgId(Long srcMsgId);
}
