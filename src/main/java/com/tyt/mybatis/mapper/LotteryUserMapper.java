package com.tyt.mybatis.mapper;

import com.tyt.probdraw.bean.CreditUserVO;
import com.tyt.probdraw.bean.LotteryUser;
import com.tyt.probdraw.bean.UserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: hp
 * Date: 2021/9/6
 */
@Mapper
@Repository
public interface LotteryUserMapper {

    List<LotteryUser> getActivityInfoList(LotteryUser lotteryUser);

    List<UserVO> listUser(@Param("importDataList") List<String> importDataList);

    List<UserVO> listUserGroupByPhone(@Param("importDataList") List<String> importDataList);

    List<UserVO> listUserGroupById(@Param("importDataList") List<String> importDataList);

    List<String> getUserIdsByIdList(@Param("importDataList") List<String> importDataList);

    List<UserVO> listUserIdGroupById(@Param("importDataList") List<String> importDataList);

    List<UserVO> getNumByUserIdAndActivityId(@Param("userPhones") List<String> userPhones, @Param("activityId") Long activityId);

    /**
     * 查询已存在活动的用户id
     * @param userIdList
     * @param activityId
     * @return
     */
    List<Long> getActivityExistUserList(@Param("userIdList") List<Long> userIdList, @Param("activityId") Long activityId);

    int insert(@Param("lotteryUsers") List<LotteryUser> lotteryUsers);

    int delete(@Param("id") Long id);

    LotteryUser selectOneById(@Param("id") Long id);

    List<LotteryUser> listUserByPage(@Param("id") Long id,@Param("page") Integer page,@Param("limit") Integer limit);

    int updateWhetherToSend(LotteryUser lotteryUser);

    List<CreditUserVO> listUserIdAndName(@Param("importDataList") List<String> importDataList);

    /**
     * @description 根据活动ID，批量用户ID 查询已参与活动的用户ID
     * <AUTHOR>
     * @date 2023/8/24 16:55
     * @version 1.0
     * @param drawActivityInfoId
     * @param userIds
     * @return java.util.List<java.lang.Integer>
     */
    List<Long> selectUserIdByTakeIn(@Param("drawActivityInfoId") Long drawActivityInfoId,@Param("userIds") List<Long> userIds);

    /**
     * @description 批量修改删除字段
     * <AUTHOR>
     * @date 2023/8/24 17:06
     * @version 1.0
     * @param drawActivityInfoId
     * @param userIds
     * @return void
     */
    void updateBatchUpdateIsDel(@Param("drawActivityInfoId") Long drawActivityInfoId,@Param("userIds") List<Long> userIds);

    List<UserVO> listUserById(@Param("ids") List<Long> ids);

}
