package com.tyt.mybatis.mapper;

import com.tyt.invoice.bean.AcceptForCarriageInfo;
import com.tyt.invoice.bean.ReqParam;
import com.tyt.mybatis.mapper.model.TytCar;
import org.apache.ibatis.annotations.Param;


public interface TytCarMapper {

    int updateHeadNoByPlateNoSelective(@Param("record") TytCar record, @Param("province") String province, @Param("plateNo") String plateNo);

    int updateTailNoByPlateNoSelective(@Param("record") TytCar record, @Param("province") String province, @Param("plateNo") String plateNo);

    String getDriverById(Long id);

    AcceptForCarriageInfo queryCarInfo(ReqParam reqParam);

    /**
     * 修改车头信息验真
     * @param id
     * @param headInfoTest 0未验真 1 已验真 2验真失败
     */
    void updateHeadInfoTest(@Param("id") Long id,@Param("headInfoTest") Integer headInfoTest);
    /**
     * 修改车头道路运输中验真
     * @param id
     * @param headRoadTransportTest 0未验真 1 已验真 2验真失败
     */
    void updateHeadRoadTransportTest(@Param("id") Long id,@Param("headRoadTransportTest") Integer headRoadTransportTest);
    /**
     * 修改挂车信息验真
     * @param id
     * @param tailInfoTest 0未验真 1 已验真 2验真失败
     */
    void updateTailInfoTest(@Param("id") Long id,@Param("tailInfoTest") Integer tailInfoTest);
    /**
     * 修改挂车道路运输中验真验真
     * @param id
     * @param tailRoadTransportTest 0未验真 1 已验真 2验真失败
     */
    void updateTailRoadTransportTest(@Param("id") Long id,@Param("tailRoadTransportTest") Integer tailRoadTransportTest);

}