package com.tyt.mybatis.mapper;


import com.tyt.model.RelTaskUser;
import com.tyt.upgrade.dto.TaskUserDTO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Mapper
@Repository
public interface RelTaskUserDao {

    @Insert("<script>" +
        " INSERT INTO rel_task_user ( template_id, task_id, task_title, user_id, template_name, " +
        "user_status, creator, creator_id, operator, operator_id ) " +
        "VALUES " +
        "   <foreach collection='taskUserList' item='taskUser' index='index' separator=','>" +
        "       (#{taskUser.templateId}, #{taskUser.taskId}, #{taskUser.taskTitle}, #{taskUser.userId}, #{taskUser.templateName} " +
        "       ,#{taskUser.userStatus}, #{taskUser.creator}, #{taskUser.creatorId}, #{taskUser.operator}, #{taskUser.operatorId} )" +
        "   </foreach >" +
        "</script>")
    int saveAll(@Param("taskUserList") List<RelTaskUser> taskUserList);

    @Delete("<script> " +
        "DELETE FROM rel_task_user WHERE user_id IN " +
        "   <foreach item='id' index='index' collection='userIds' open='(' separator=',' close=')'> " +
        "      #{id} " +
        "   </foreach> " +
        "</script>")
    int deleteByUserIds(@Param("userIds") Set<Long> userIds);

    @Delete("DELETE FROM rel_task_user")
    int deleteAll();

    @Delete("DELETE FROM rel_task_user WHERE id = #{id}")
    int deleteById(@Param("id") long id);

    @Update(" UPDATE rel_task_user " +
        "SET user_status = #{userStatus}," +
        "operator = #{operator}," +
        "operator_id = #{operatorId} " +
        " WHERE user_id = #{userId} AND user_status != #{userStatus};")
    int changeUserStatus(RelTaskUser relTaskUser);

    /**
     * 模版列表
     * <AUTHOR>
     * @return
     * @date 2020-07-30 19:19:41
     */
    @Select("<script>  " +
        " SELECT " +
        " rtu.id, " +
        " rtu.template_id AS templateId, " +
        " rtu.template_name AS templateName, " +
        " rtu.task_id AS taskId, " +
        " rtu.task_title AS taskTitle, " +
        " rtu.user_id AS userId, " +
        " rtu.user_status AS userStatus, " +
        " tur.real_name AS userName, " +
        " tur.car_owner_login_time AS carOwnerLoginTime, " +
        " tur.cargo_station_login_time AS cargoStationLoginTime, " +
        " tur.user_phone AS userPhone, " +
        " IFNULL(tur.car_owner_upgrade_status,0) AS carOwnerUpgradeStatus, " +
        " IFNULL(tur.cargo_station_upgrade_status,0) AS cargoStationUpgradeStatus " +
        "FROM " +
        " rel_task_user rtu " +
        " LEFT JOIN tyt_upgrade_record tur ON rtu.user_id = tur.user_id " +
        " WHERE rtu.task_id = #{taskId} " +
        "   <choose> " +
        "        <when test='cargoStationUpgradeStatus == 0'>" +
        "             AND (tur.cargo_station_upgrade_status = #{cargoStationUpgradeStatus} OR tur.cargo_station_upgrade_status is null )" +
        "        </when> " +
        "        <when test='cargoStationUpgradeStatus == 1'>" +
        "             AND tur.cargo_station_upgrade_status = #{cargoStationUpgradeStatus} " +
        "        </when> " +
        "    </choose>" +
        "   <choose> " +
        "        <when test='carOwnerUpgradeStatus == 0'>" +
        "             AND (tur.car_owner_upgrade_status = #{carOwnerUpgradeStatus} OR tur.car_owner_upgrade_status is null )" +
        "        </when> " +
        "        <when test='carOwnerUpgradeStatus == 1'>" +
        "             AND tur.car_owner_upgrade_status = #{carOwnerUpgradeStatus} " +
        "        </when> " +
        "    </choose>" +
        "<if test='userIds != null'>  " +
        " AND rtu.user_id IN " +
        "   <foreach item='id' index='index' collection='userIds' open='(' separator=',' close=')'> " +
        "      #{id} " +
        "   </foreach> " +
        "</if>" +
        " LIMIT #{offset}, #{pageSize} " +
        "</script> ")
    List<TaskUserDTO> queryPageList(@Param("offset") int offset,
                                    @Param("pageSize")  int pageSize,
                                    @Param("taskId") int taskId,
                                    @Param("carOwnerUpgradeStatus") Integer carOwnerUpgradeStatus,
                                    @Param("cargoStationUpgradeStatus") Integer cargoStationUpgradeStatus,
                                    @Param("userIds") List userIds);

    @Select("<script>  " +
        " SELECT " +
        " COUNT(*) " +
        "FROM " +
        " rel_task_user rtu " +
        " LEFT JOIN tyt_upgrade_record tur ON rtu.user_id = tur.user_id " +
        " WHERE rtu.task_id = #{taskId} " +
        "   <choose> " +
        "        <when test='cargoStationUpgradeStatus == 0'>" +
        "             AND (tur.cargo_station_upgrade_status = #{cargoStationUpgradeStatus} OR tur.cargo_station_upgrade_status is null )" +
        "        </when> " +
        "        <when test='cargoStationUpgradeStatus == 1'>" +
        "             AND tur.cargo_station_upgrade_status = #{cargoStationUpgradeStatus} " +
        "        </when> " +
        "    </choose>" +
        "   <choose> " +
        "        <when test='carOwnerUpgradeStatus == 0'>" +
        "             AND (tur.car_owner_upgrade_status = #{carOwnerUpgradeStatus} OR tur.car_owner_upgrade_status is null )" +
        "        </when> " +
        "        <when test='carOwnerUpgradeStatus == 1'>" +
        "             AND tur.car_owner_upgrade_status = #{carOwnerUpgradeStatus} " +
        "        </when> " +
        "    </choose>" +
        "<if test='userIds != null'>  " +
        " AND rtu.user_id IN " +
        "   <foreach item='id' index='index' collection='userIds' open='(' separator=',' close=')'> " +
        "      #{id} " +
        "   </foreach> " +
        "</if> " +
        "</script> ")
    Long queryRowCount(@Param("taskId") int taskId,
                       @Param("carOwnerUpgradeStatus") Integer carOwnerUpgradeStatus,
                       @Param("cargoStationUpgradeStatus") Integer cargoStationUpgradeStatus,
                       @Param("userIds") List userIds);
}
