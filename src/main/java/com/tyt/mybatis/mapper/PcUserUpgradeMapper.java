package com.tyt.mybatis.mapper;


import com.tyt.pcupgrade.bean.PcUpgradeTask;
import com.tyt.pcupgrade.bean.PcUserUpgrade;
import com.tyt.pcupgrade.dto.PcUpgradeUserInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PcUserUpgradeMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(PcUserUpgrade record);

    PcUserUpgrade selectByPrimaryKey(Integer id);

    /**
     * 根据升级任务id查询该任务下的定向升级用户
     * @param taskId
     * @return
     */
    List<PcUserUpgrade> selectByTaskId(@Param("taskId") Integer taskId);

    /**
     * 根据任务id查询该任务下的定向升级用户信息
     * @param taskId
     * @return
     */
    List<PcUpgradeUserInfo> selectUpgradeUserInfo(@Param("taskId") Integer taskId);

    /**
     * 根据任务id删除该任务下定向升级用户信息
     * @param taskId
     * @return
     */
    int deleteByTaskId(@Param("taskId") Integer taskId);

    int updateByPrimaryKeySelective(PcUserUpgrade record);

    /**
     * 批量插入
     * @param upgradeTaskId
     * @param userIds
     * @return
     */
    int batchInsert(@Param("upgradeTaskId") Integer upgradeTaskId, @Param("userIds") List<Long> userIds);
}
