package com.tyt.mybatis.mapper;

import com.tyt.mybatis.mapper.model.TytCustomServiceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface TytCustomServiceTypeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TytCustomServiceType record);

    int insertSelective(TytCustomServiceType record);

    TytCustomServiceType selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TytCustomServiceType record);

    int updateByPrimaryKey(TytCustomServiceType record);

    List<TytCustomServiceType> selectAll();

    TytCustomServiceType selectByTypeName(@Param("typeName") String typeName);
}
