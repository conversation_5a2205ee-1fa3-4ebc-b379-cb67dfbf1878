package com.tyt.mybatis.mapper.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 黑名单用户订单拉黑日志表实体类
 * blacklist_user_orders_log
 *
 * <AUTHOR>
 * @date 2023-10-9 10:07:35
 */
@Data
@Table(name = "`blacklist_user_orders_log`")
public class BlacklistUserOrdersLog implements Serializable{

	public static final int ACTIONROLE_GOODS = 1;

	public static final int ACTIONROLE_CAR = 2;

	/**
	 * （1-新增；2-减少；3-解除；4-永久；）
	 */
	public static final int ACTION_TYPE_1 = 1;

	public static final int ACTION_TYPE_2 = 2;

	public static final int ACTION_TYPE_3 = 3;

	public static final int ACTION_TYPE_4 = 4;

	/**
	 * id
	 */
	@Id
	@Column(name = "`id`")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	/**
	 * 用户id
	 */
	@Column(name = "`user_id`")
	private Long userId;
	/**
	 * 异常上报ID
	 */
	@Column(name = "`waybill_ex_id`")
	private Long waybillExId;
	/**
	 * 运单号
	 */
	@Column(name = "`ts_order_no`")
	private String tsOrderNo;
	/**
	 * 操作角色（1-货方；2-车方；）
	 */
	@Column(name = "`action_role`")
	private Integer actionRole;
	/**
	 * 拉黑操作类型（1-新增；2-增加；3-减少；4-解除；）
	 */
	@Column(name = "`restrict_action_type`")
	private Integer restrictActionType;
	/**
	 * 拉黑天数（-1为永久拉黑）
	 */
	@Column(name = "`restrict_num`")
	private Integer restrictNum;
	/**
	 * 限制找货操作类型（1-新增；2-增加；3-减少；4-解除；）
	 */
	@Column(name = "`car_restrict_action_type`")
	private Integer carRestrictActionType;
	/**
	 * 限制找货天数（-1为永久限制）
	 */
	@Column(name = "`car_restrict_num`")
	private Integer carRestrictNum;
	/**
	 * 限制发货操作类型（1-新增；2-增加；3-减少；4-解除；）
	 */
	@Column(name = "`goods_restrict_action_type`")
	private Integer goodsRestrictActionType;
	/**
	 * 限制找货天数（-1为永久限制）
	 */
	@Column(name = "`goods_restrict_num`")
	private Integer goodsRestrictNum;
	/**
	 * 限制找货/发货原因
	 */
	@Column(name = "`restrict_cause`")
	private String restrictCause;
	/**
	 * 备注
	 */
	@Column(name = "`remark`")
	private String remark;
	/**
	 * 操作人ID
	 */
	@Column(name = "`opera_user_id`")
	private Long operaUserId;
	/**
	 * 操作人名称
	 */
	@Column(name = "`opera_user_name`")
	private String operaUserName;
	/**
	 * 创建时间
	 */
	@Column(name = "`create_time`")
	private Date createTime;
}
