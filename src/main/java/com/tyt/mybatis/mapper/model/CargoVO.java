package com.tyt.mybatis.mapper.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CargoVO implements Serializable {

    private Long tsId;
    private String taskContent;
    private String weight;//吨位
    private String length;
    private Integer wide;
    private String high;
    private String size;//尺寸
    private String startPoint;
    private String destPoint;
    private Date ctime;

    private String infoFee;

    /**
     * 出发地经度
     */
    private Integer startLongitude;

    /**
     * 出发地纬度
     */
    private Integer startLatitude;

    /**
     * 目的地经度
     */
    private Integer destLongitude;

    /**
     * 目的地纬度
     */
    private Integer destLatitude;
    private Integer distance;
    private Date pubDate;
    private Integer excellentGoods;
    private Integer publishType;

    private Date loadingTime;
    private Date unloadTime;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    private String labelJson;


}