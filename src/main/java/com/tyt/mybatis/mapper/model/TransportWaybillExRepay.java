package com.tyt.mybatis.mapper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 异常上报追偿发起表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-02-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_waybill_ex_repay")
public class TransportWaybillExRepay {

    public static final int REPAY_STATUS_WAIT = 0;

    public static final int REPAY_STATUS_DOING = 1;

    public static final int REPAY_STATUS_ALREADY = 2;

    public static final int PAY_STATUS_NO = 0;

    public static final int PAY_STATUS_DOING = 1;

    public static final int PAY_STATUS_SUCCESS = 2;

    public static final int PAY_STATUS_FAIL = 3;

    public static final int TARGET_CAR = 1;

    public static final int TARGET_GOODS = 2;

    public static final int DELETE_NO = 0;

    public static final int DELETE_YES = 1;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 运单号
     */
    private String tsOrderNo;

    /**
     * 运单ID
     */
    private Long orderId;

    /**
     * 异常上报方：1-车主上报；2-货主上报；
     */
    private Integer exParty;

    /**
     * 货主用户ID
     */
    private Long goodsUserId;

    /**
     * 车主用户ID
     */
    private Long carUserId;

    /**
     * 补偿金额（元）
     */
    private BigDecimal compensateAmount;

    /**
     * 补偿类型：1.订金补偿 2.放空补偿 3.压车补偿 4.距离加价补偿 5.吨位加价补偿 6.拉跑货补偿 7.倒卖货补偿 8.倒送货补偿 9.防溢出补偿 10.其他
     */
    private Integer compensateType;

    /**
     * 补偿原因
     */
    private String compensateReason;

    /**
     * 补偿时间
     */
    private Date compensateTime;

    /**
     * 追偿对象：1-车方；2-货方；
     */
    private Integer repayTarget;

    /**
     * 追偿对象用户ID
     */
    private Long repayUserId;

    /**
     * 追偿金额（元）
     */
    private BigDecimal repayAmount;

    /**
     * 追偿状态：0-待追偿；1-追偿中；2-已还款；
     */
    private Integer repayStatus;

    /**
     * 追偿支付单号
     */
    private String repayTradeNo;

    /**
     * 追偿支付状态：0-未支付；1-支付中；2-支付成功；3-支付失败；
     */
    private Integer payStatus;

    /**
     * 是否删除：0-否；1-是；
     */
    private Integer deleteFlag;

    /**
     * 到账时间
     */
    private Date payTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}
