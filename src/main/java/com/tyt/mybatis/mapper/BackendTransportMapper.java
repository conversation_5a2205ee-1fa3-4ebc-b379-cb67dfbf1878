package com.tyt.mybatis.mapper;

import com.tyt.model.TransportBackend;
import com.tyt.transportnews.bean.BackendDealCarBean;
import com.tyt.transportnews.bean.BackendListPageBean;
import com.tyt.transportnews.bean.TransportBackendCarVO;
import com.tyt.transportnews.bean.TransportBackendListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/1 16:18
 */

public interface BackendTransportMapper {
    /**
     * 后台货源列表页
     * @param pageBean
     * @return
     */
    List<TransportBackendListVO> findTransportBackends(@Param("pageBean") BackendListPageBean pageBean);

    BackendDealCarBean selectBackendDealCarByTsId(Long id);

    List<TransportBackendCarVO> getTransportOrderList(Long tsId);

    /**
     * 根据货源id查询
     * @param srcMsgId
     * @return
     */
    List<TransportBackend> selectBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}
