package com.tyt.mybatis.mapper;


import com.tyt.pcupgrade.bean.PcUpgradeTask;
import com.tyt.pcupgrade.dto.QueryCondition;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PcUpgradeTaskMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(PcUpgradeTask record);

    PcUpgradeTask selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PcUpgradeTask record);

    /**
     * 根据任务id修改任务状态
     * @param taskId 任务id
     * @param taskStatus 0:任务未开始 1:进行中 2:已结束 3:已终止 时间区间有效性
     * @return
     */
    int updateTaskStatus(@Param("taskId") Integer taskId,@Param("taskStatus") Integer taskStatus);

    /**
     * 根据任务id查询任务有效性 为null 则无效 反之有效
     * @param taskId
     * @return
     */
    PcUpgradeTask selectTaskValidity(@Param("taskId") Integer taskId);

    /**
     * 查询升级列表信息
     * @return
     */
    List<PcUpgradeTask> selectUpgradeTaskList(QueryCondition queryCondition);

    /**
     * 根据任务名称查询
     * @param taskName
     * @return
     */
    PcUpgradeTask selectByTaskName(@Param("taskName") String taskName);

    /**
     * 根据升级时间区间查询
     * @param upgradeStartTime 升级开始时间
     * @param upgradeEndTime 升级结束时间
     * @return
     */
    PcUpgradeTask selectByDateInterval(@Param("upgradeStartTime") Date upgradeStartTime,@Param("upgradeEndTime") Date upgradeEndTime,@Param("upgradeType") Integer upgradeType,@Param("taskId") Integer taskId);

}