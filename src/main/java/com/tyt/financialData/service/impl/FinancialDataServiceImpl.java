package com.tyt.financialData.service.impl;

import com.tyt.dao.base.BaseDao;
import com.tyt.financialData.bean.FinancialDataQueryBean;
import com.tyt.financialData.service.FinancialDataService;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytFinancialData;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.AppConfig;
import com.tyt.util.CsvReader;
import com.tyt.util.JxlUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Field;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description  财务数据服务层实现类
 * <AUTHOR>
 * @Date  2019/11/21 10:24
 * @Param
 * @return
 **/
@Service("financialDataService")
public class FinancialDataServiceImpl extends BaseServiceImpl<TytFinancialData, Long> implements FinancialDataService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "financialDataDao")
    public void setBaseDao(BaseDao<TytFinancialData, Long> financialDataDao) {
        super.setBaseDao(financialDataDao);
    }

    /**
     * @Description  获取财务数据列表实现方法
     * <AUTHOR>
     * @Date  2019/11/21 10:44
     * @Param [financialData, pageBean]
     * @return java.util.List<com.tyt.model.TytFinancialData>
     **/
    @Override
    public List<TytFinancialData> getFinancialDataList(FinancialDataQueryBean financialData, PageBean pageBean) {
        //编号
        String no = financialData.getNo();
        //类型
        String type = financialData.getType();
        //开始时间
        String startTime = financialData.getStartTime();
        //结束时间
        String endTime = financialData.getEndTime();

        //拼接查询财务列表数据的sql语句
        StringBuffer sbSql = new StringBuffer("SELECT id, no, type, ctime,");
        for(int i = 1; i < 21; i++){
            sbSql.append(" field" + i + (i != 20 ?",":""));
        }
        sbSql.append(" FROM tyt_financial_data where 1 = 1 ");
        //拼接传入的查询参数
        List<Object> listObject = new ArrayList<Object>();
        //拼接查询条件的sql
        StringBuffer contionSql = new StringBuffer();
        if(StringUtils.isNotBlank(no)) {
            contionSql.append(" and no = ? ");
            listObject.add(no);
        }
        if(StringUtils.isNotBlank(type)){
            contionSql.append(" and type = ? ");
            listObject.add(type);
        }
        if(StringUtils.isNotBlank(startTime)){
            contionSql.append(" and ctime >= ? ");
            listObject.add(startTime);
        }
        if(StringUtils.isNotBlank(endTime)){
            contionSql.append(" and ctime <= ? ");
            listObject.add(endTime);
        }
        //根据ID降序排序
        contionSql.append(" order by id desc ");
        //查询语句拼接上面的查询条件
        sbSql.append(contionSql);

        //获取财务数据总数量的sql语句
        StringBuffer sbCount = new StringBuffer("SELECT count(*) FROM tyt_financial_data where 1 = 1  ");
        sbCount.append(contionSql);
        //查询总条数
        BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), listObject.toArray());
        pageBean.setRowCount(rowCount.intValue());

        //查询结果转换的类型
        Map<String, Type> map = new HashMap<String, Type>();
        map.put("id", Hibernate.LONG);
        map.put("no",Hibernate.STRING);
        map.put("type",Hibernate.STRING);
        map.put("ctime",Hibernate.DATE);
        for(int i = 1; i < 21; i++){
           map.put("field" + i, Hibernate.STRING);
        }

        //执行查询语句,转换成财务数据列表对象,并进行分页
        List <TytFinancialData> financialDataList = this.getBaseDao().search(sbSql.toString(), map,
                TytFinancialData.class, listObject.toArray(), pageBean.getCurrentPage(), pageBean.getPageSize());

        return financialDataList;
    }
    
    /**
     * @Description  将财务数据导入到数据库中
     * <AUTHOR>
     * @Date  2019/11/21 16:26
     * @Param [fileField]
     * @return com.tyt.model.ResultMsgBean
     **/
    @Override
    public ResultMsgBean saveFinancialData(MultipartFile fileField) throws Exception {
        ResultMsgBean msgBean = new ResultMsgBean();
        if (fileField != null && !fileField.isEmpty()) {
            String excelUrl = renameExcelBase(fileField, "excel");
            fileField.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + excelUrl));
            //将导入文件中的数据转换成集合
            List<List<String>> importDataList = CsvReader.csvToList(AppConfig.getProperty("picture.file.path.domain") + excelUrl);
            // 将Excel文件数据转换成List集合
            //List<List<String>> importDataList = JxlUtil.readExcel(fileField.getInputStream());
            //编号、类型、创建时间、field1...field30
            for (int i = 0; i < importDataList.size(); i++) {
                //如果为第一条记录则跳过
                if(i == 0){
                    continue;
                }
                List<String> lineDataList = importDataList.get(i);
                if (lineDataList != null && lineDataList.size() > 0) {
                    //财务数据对象
                    TytFinancialData financialData = new TytFinancialData();
                    //如果编号，类型，日期三个字段其中一个为空，则不进行导入直接跳过
                    if(StringUtils.isBlank(lineDataList.get(0)) ||
                            StringUtils.isBlank(lineDataList.get(1)) ||
                            StringUtils.isBlank(lineDataList.get(2))){
                        continue;
                    }
                    //编号
                    financialData.setNo(lineDataList.get(0));
                    //类型
                    financialData.setType(lineDataList.get(1));
                    //创建时间 格式:yyyy-MM-dd HH:mm:ss
                    financialData.setCtime(TimeUtil.parseDateString(lineDataList.get(2)));
                    //field1 -> field20
                    //通过反射技术给相似的字段进行赋值,字段名称下标为j+1,值的下标为j+3
                    for (int j = 0; j < lineDataList.size(); j++) {
                        if(StringUtils.isNotBlank(lineDataList.get(j))){
                            //字段名称
                            Field f = financialData.getClass().getDeclaredField("field" + (j + 1));
                            f.setAccessible(true);
                            f.set(financialData, lineDataList.get(j+3));
                        }
                    }
                    String bean = ToStringBuilder.reflectionToString(financialData, ToStringStyle.SHORT_PREFIX_STYLE);
                    logger.info("====赋值后的财务对象数据为：===="+bean);
                    this.getBaseDao().insert(financialData);
                }
            }
            msgBean.setCode(ResultMsgBean.OK);
            msgBean.setMsg("将财务数据导入到数据库中成功！");
            return msgBean;
        }
        return msgBean;
    }

    /**
     * @Description  删除导入的财务数据
     * <AUTHOR>
     * @Date  2019/11/22 9:51
     * @Param [type]
     * @return java.lang.Boolean
     **/
    @Override
    public Boolean deleteFinancialData(String type) throws Exception {
        String delSql = "";
        Map<String, Object> map = new HashMap<String, Object>();
        if(StringUtils.isNotBlank(type)){
            delSql = "delete from tyt_financial_data where type = :type";
            map.put("type",type);
        }else{
            delSql = "delete from tyt_financial_data";
        }
        int c = this.executeUpdateSql(delSql, map);
        return c > 0;
    }

    /**
     * @Description  判断字符串是否为空，为空则转换成""
     * <AUTHOR>
     * @Date  2019/11/25 16:50
     * @Param [field]
     * @return java.lang.String
     **/
    public String isBlank(String field){
        return StringUtils.isBlank(field) ? "" : field;
    }
}
