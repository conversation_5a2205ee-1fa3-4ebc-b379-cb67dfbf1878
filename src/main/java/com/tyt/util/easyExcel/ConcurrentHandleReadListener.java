package com.tyt.util.easyExcel;

import cn.hutool.core.lang.Pair;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;

/**
 * 多线程执行 Excel 读取操作的 ReadListener
 * <p>
 * 重要: 使用此 Listener 需要结合业务合理配置 dataSize 和 maxConcurrency
 * <p>
 * 具体的执行流程为:
 * 1. 主线程从 excel 中读取出配置的 dataSize 的行数
 * 2. 在线程池内执行 {@link #handleDataPairs(List, AnalysisContext)}, 并将读取到的数据作为参数传入
 * 3. excel读取结束，并且线程池内所有任务执行完后, 会调用{@link #doAfterAllFinished(HandleResult, AnalysisContext)} 方法
 * 4. 线程内的线程会在空闲10s后销毁
 * <p>
 * 此 Listener 的线程池, 核心线程数为0, 最大线程数为配置的 maxConcurrency, 阻塞队列为 SynchronousQueue, 拒绝策略为 BlockPolicy
 * <p>
 * <p>
 * maxConcurrency 只能控制最大并发数
 * 假设读取指定 dataSize 数据的时间为 t1, 执行 {@link #handleDataPairs(List, AnalysisContext)} 方法的时间为 t2
 * maxConcurrency、t1、t2和实际并发数有以下关系:
 * <p>
 * t1 > t2:
 * 主线程提交任务时不会创建新线程, 因为存在空闲线程. 此时无论 maxConcurrency 配置为多少, 线程池内线程数和并发数都为 1
 * <p>
 * t1 * k = t2, k 为正整数并且 k <= maxConcurrency:
 * 当线程池内线程数达到 k 时, 下次主线程提交任务一定会存在空闲线程, 此时并发数为 k
 * <p>
 * t1 * k = t2, k 为正整数并且 k > maxConcurrency:
 * 当线程池内线程数达到 maxConcurrency 时, 主线程提交任务时会阻塞, 直到有线程空闲, 此时并发数为maxConcurrency
 * <p>
 * <p>
 * 值得一提的是, 配置 maxConcurrency = 1 时, 最大并发数为 1, 此时业务上的表现与使用单线程没有差别
 * 但主线程花费的时间要比单线程内直接使用 {@link ReadListener} 短
 * 假设读取指定 dataSize 数据的时间为 t1, 执行 {@link #handleDataPairs(List, AnalysisContext)} 方法的时间为 t2, 读取、执行的次数为 n
 * 直接使用 {@link ReadListener}, 读取和处理数据由一个线程串行执行, 需要使用的总时间为 n * (t1 + t2)
 * 使用此 Listener, 并且配置 maxConcurrency = 1, 此时主线程读数据, Listener 内单线程执行逻辑, 需要使用的总时间为 n * max(t1, t2)
 *
 * <AUTHOR>
 * @since 2024/01/07 11:12
 */
@Slf4j
public abstract class ConcurrentHandleReadListener<T> extends AnalysisEventListener<T> {

    private static final Duration DEFAULT_RESULT_WAIT_TIMEOUT = Duration.ofSeconds(5);
    /**
     * 执行 Excel 业务的线程池
     */
    private final ExecutorService executor;
    /**
     * 每次处理数据的大小
     */
    private final int dataSize;
    /**
     * 等待汇总结果的超时时间
     */
    private final Duration resultWaitTimeout;
    /**
     * 读取出来的数据集合
     */
    private final List<Pair<ReadRowHolder, T>> pairList;
    /**
     * 结果汇总
     */
    private final HandleResult resultSum = new HandleResult();
    /**
     * 汇总结果时使用的锁
     */
    private final ReentrantLock resultSumLock = new ReentrantLock();

    /**
     * 汇总线程池内的 task
     */
    private CompletableFuture<Void> combine = CompletableFuture.completedFuture(null);

    protected ConcurrentHandleReadListener(int dataSizePerBatch, int maxConcurrency) {
        this(dataSizePerBatch, maxConcurrency, DEFAULT_RESULT_WAIT_TIMEOUT);
    }

    protected ConcurrentHandleReadListener(int dataSizePerBatch, int maxConcurrency,
                                           Duration resultWaitTimeout) {
        if (dataSizePerBatch < 1) {
            throw new IllegalArgumentException("dataSizePerBatch 不能小于 1");
        }
        this.pairList = new ArrayList<>(dataSizePerBatch);
        this.dataSize = dataSizePerBatch;
        this.resultWaitTimeout = resultWaitTimeout;
        this.executor = new ThreadPoolExecutor(0, maxConcurrency,
                10L, TimeUnit.SECONDS,
                new SynchronousQueue<>(), new BlockPolicy());
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        Pair<ReadRowHolder, T> pair = new Pair<>(context.readRowHolder(), data);
        pairList.add(pair);
        if (pairList.size() >= dataSize) {
            List<Pair<ReadRowHolder, T>> dataParis = Collections.unmodifiableList(new ArrayList<>(pairList));
            pairList.clear();

            submitDataPairs(dataParis, context);
        }
    }

    /**
     * 向线程池内提交读取的数据集
     *
     * @param context   读取到集合内最后一条数据时对应的 AnalysisContext
     * @param dataParis 数据集
     */
    private void submitDataPairs(List<Pair<ReadRowHolder, T>> dataParis, AnalysisContext context) {

        if (dataParis.isEmpty()) {
            return;
        }
        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            try {
                HandleResult handleResult = handleDataPairs(dataParis, context);
                appendResultSum(handleResult);
            } catch (Exception e) {
                log.error("处理 dataParis 出现异常", e);
            }
        }, executor);

        combine = CompletableFuture.allOf(combine, completableFuture);
    }

    /**
     * 实现此方法处理从 excel 内读取到的数据集
     *
     * @param pairList 数据集合, key 为 ReadRowHolder, value 为 数据体
     * @param context  读取到集合内最后一条数据时对应的 AnalysisContext
     * @return 此次程序处理的结果
     */
    protected abstract HandleResult handleDataPairs(List<Pair<ReadRowHolder, T>> pairList, AnalysisContext context);

    /**
     * 汇总处理结果
     *
     * @param handleResult 单次处理结果
     */
    private void appendResultSum(HandleResult handleResult) {
        if (handleResult == null) {
            return;
        }

        resultSumLock.lock();
        try {
            resultSum.setSuccessCount(resultSum.getSuccessCount() + handleResult.getSuccessCount());
            resultSum.setFailureCount(resultSum.getFailureCount() + handleResult.getFailureCount());
            List<ReadRowHolder> failureAnalysisContexts = handleResult.getFailureReadRowHolder();
            if (failureAnalysisContexts != null && !failureAnalysisContexts.isEmpty()) {
                resultSum.getFailureReadRowHolder().addAll(failureAnalysisContexts);
            }
        } finally {
            resultSumLock.unlock();
        }
    }

    private HandleResult waitAndGetResultSum() {
        long waitTimeMillis = resultWaitTimeout.toMillis();
        try {
            combine.get(waitTimeMillis, TimeUnit.MILLISECONDS);
        } catch (InterruptedException ignore) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("等待聚合结果时出现异常: ", e);
        }
        return getResultSum();
    }

    /**
     * 获取到目前为止的结果集
     *
     * @return 结果集
     */
    protected HandleResult getResultSum() {
        return new HandleResult(resultSum.successCount, resultSum.failureCount,
                Collections.unmodifiableList(resultSum.failureReadRowHolder));
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        submitDataPairs(pairList, context);
        doAfterAllFinished(waitAndGetResultSum(), context);
    }

    /**
     * 所有行数读完会执行此方法
     * <p>
     * 可以用来获取处理结果汇总
     *
     * @param handleResult 处理结果汇总
     * @param context      context
     */
    protected abstract void doAfterAllFinished(HandleResult handleResult, AnalysisContext context);

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        try {
            HandleResult handleResult = handleOnException(exception, context);
            appendResultSum(handleResult);
        } catch (Exception e) {
            log.error("handleOnException 执行异常 context: {}", context, e);
        }
    }

    /**
     * 发生异常时会调用此方法
     *
     * @param exception exception
     * @param context   context
     * @return resultSum 此次异常处理结果
     */
    protected abstract HandleResult handleOnException(Exception exception, AnalysisContext context);

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HandleResult {

        /**
         * 成功数
         */
        private int successCount;

        /**
         * 失败数
         */
        private int failureCount;

        /**
         * 失败数据集合，存储在内存中，存储时最好结合{@link #getResultSum()} 判断数据量，否则有产生 OOM 的风险
         */
        private List<ReadRowHolder> failureReadRowHolder = new ArrayList<>();
    }

    static class BlockPolicy implements RejectedExecutionHandler {

        /**
         * 线程池关闭时，为避免任务丢失，留下处理方法
         * 如果需要由调用方来运行，可以{@code new BlockPolicy(Runnable::run)}
         */
        private final Consumer<Runnable> handlerWhenShutdown;

        /**
         * 构造
         *
         * @param handlerWhenShutdown 线程池关闭后的执行策略
         */
        public BlockPolicy(final Consumer<Runnable> handlerWhenShutdown) {
            this.handlerWhenShutdown = handlerWhenShutdown;
        }

        /**
         * 构造
         */
        public BlockPolicy() {
            this(null);
        }

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            // 线程池未关闭时，阻塞等待
            if (!e.isShutdown()) {
                try {
                    e.getQueue().put(r);
                } catch (InterruptedException ignore) {
                    Thread.currentThread().interrupt();
                    throw new RejectedExecutionException("Task " + r + " rejected from " + e);
                }
            } else if (null != handlerWhenShutdown) {
                // 当设置了关闭时候的处理
                handlerWhenShutdown.accept(r);
            }
            // 线程池关闭后，丢弃任务
        }
    }
}
