package com.tyt.util;

import java.awt.Color;
import java.util.Random;

public class DrawVeryCodeUtil {
	    public static final int WIDTH = 100;
	    public static final int HEIGHT = 30;
	    public static Random random = new Random();
	    public static final char[] CHARS = { '2', '3', '4', '5', '6', '7', '8', '9',
	    	        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
	    	        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };

	 public  String getRandomStr() {
	    	
	     StringBuffer buffer = new StringBuffer();
	
	     for (int i = 0; i < 4; i++) {// 生成6个字符
	
	         buffer.append(CHARS[random.nextInt(CHARS.length)]);
	
	     }
	
	     return buffer.toString();
	
	 }
	
	
	
	 // 取随机颜色
	
	 public static Color getRandomColor() {
	
	     return new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255));
	
	 }
	
	
	
	 // 取随机颜色的反色
	
	 public static Color getReverseColor(Color c) {
	
	     return new Color(255 - c.getRed(), 255 - c.getGreen(), 255 - c.getBlue());
	
	 }
	
	 
	
}
