package com.tyt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**  
 * @Title: GenerateShortUrlUtil.java
 * @Package com.tyt.message.bean
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年12月5日
 */
public class  GenerateShortUrlUtil {  
	private static Logger logger = LoggerFactory.getLogger(GenerateShortUrlUtil.class);

    public static DefaultHttpClient httpclient;  
    static {  
       // httpclient = new DefaultHttpClient();  
        httpclient = new DefaultHttpClient(new ThreadSafeClientConnManager());
       // httpclient = (DefaultHttpClient) HttpClientConnectionManager.getSSLInstance(httpclient); // 接受任何证书的浏览器客户端  
    }
      
    /** 
     * 生成端连接信息 
     *  
     * @author-cp:wangfeng
     */  
    public static String  generateShortUrl(String url) {
    	HttpResponse response = null;
        try {  
            //HttpPost httpost = new HttpPost("http://dwz.cn/create.php"); 
            HttpPost httpost = new HttpPost("http://api.t.sina.com.cn/short_url/shorten.json"); 

            List<NameValuePair> params = new ArrayList<NameValuePair>();  
            //params.add(new BasicNameValuePair("url", url)); // 用户名称  
            params.add(new BasicNameValuePair("url_long", url)); // 用户名称  
            params.add(new BasicNameValuePair("source", "3271760578")); // 用户名称  
            
            httpost.setEntity(new UrlEncodedFormEntity(params,  "utf-8"));
            HttpEntity entity = null;
         //   synchronized(httpclient){
               response = httpclient.execute(httpost); 
               entity = response.getEntity();
             //  EntityUtils.consume(entity);
           // }
            String jsonStr = EntityUtils.toString(entity, "utf-8");  
             
            System.out.println(jsonStr);  
            logger.info("===shorturl==="+jsonStr);

            JSONArray jsonArray = JSON.parseObject(jsonStr, JSONArray.class);
            JSONObject object = jsonArray.getJSONObject(0);
            System.out.println(object.getString("url_short"));  
            return object.getString("url_short");  
            
        } catch (Exception e) {  
            e.printStackTrace();  
            logger.info("===shorturl==="+e.getMessage());
            return "Error";  
        }/* finally {
        	try{  
        		response.getEntity().getContent().close(); 
        	} catch (Exception ex){logger.info("===shorturl==="+ex.getMessage());  } 
        }*/
          
    }  
      
    /** 
     * 测试生成端连接 
     * @param args   
     * @author-cp:wangfeng
     */  
    public static void main(String []args){ 
/*    	System.out.println("+==="+System.currentTimeMillis());
        generateShortUrl("https://www.teyuntong.com/info_fee_sms.html");
        generateShortUrl("http://help.baidu.com/index?xx=456");*/  

    	 // 创建3个线程的线程池  
    	ExecutorService cachedThreadPool = Executors.newCachedThreadPool();
    	for (int i = 0; i < 200; i++) {
    /*		final int index = i;
    		try {
    			Thread.sleep(index * 1000);
    		} catch (InterruptedException e) {
    			e.printStackTrace();
    		}*/
    		cachedThreadPool.execute(new Runnable() {
    		    private volatile int i = 1;  
    			@Override
    			public void run() {
    			 	System.out.println("任务 " + (i++) + " 完成=====" +System.currentTimeMillis());
    		        generateShortUrl("https://www.teyuntong.com/info_fee_sms.html");
    		        generateShortUrl("http://help.baidu.com/index?xx=456");    			}
    		});
    	}
 

    }  
    
 


}  




