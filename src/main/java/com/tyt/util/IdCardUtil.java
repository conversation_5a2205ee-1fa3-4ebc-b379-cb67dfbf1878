package com.tyt.util;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 身份证号工具
 * <AUTHOR>
 *
 */
public class IdCardUtil {
	/**
	 * 获取性别
	 * @param idCard
	 * @return
	 */
	public static String getGender(String idCard) {  
        String sex="1";
        if(idCard!=null &&!"".equals(idCard)){
	        if (idCard.length()==15) { 
	        	sex=idCard.substring(13, 14);
	        }else{
	        	if(idCard.length()==18){
	        		sex=idCard.substring(16, 17);
	        	}
	        } 
	        return Integer.parseInt(sex) % 2==0?"0":"1"; 
        }else
        	return null;
    }  
	
	public static boolean checkIdCard(String idCard) {   
        String regex = "[1-9]\\d{13,16}[a-zA-Z0-9]{1}";   
        return Pattern.matches(regex,idCard);   
    }
	  
}	
