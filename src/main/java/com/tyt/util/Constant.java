package com.tyt.util;

import java.util.Date;

/**
 * User: Administrator Date: 13-12-22 Time: 下午5:26
 */
public class Constant {
	public static final String SESSION_USER = "session.user";
	public static final String LOGIN_USER_CACHE_KEY = "LOGIN_USER_CACHE_KEY_";
	public static final String LOGIN_USER_CACHE_IMIT_KEY = "LOGIN_USER_CACHE_IMIT_KEY_";
	public static final String SESSION_INFO_USER = "webuser";
	public static final String COOKIE_USER = "ssoInfo";
	public static final String COOKIE_DOMAIN = ".teyuntong.net";
	public static final String WEB_ACCESS_FAILURE = "访问出错......";
	public static final String WEB_DUPLICATE_INFO = "您今天已经发过此条信息，请重新填写!";
	public static final String PHONE_NOT_OPEN = "手机未开通";
	public static final String MSG_NOT_LOGIN = "未登录，亲！";
	public static final String MSG_LOGOUT_WELCOME = "亲！下班了，欢迎再来";
	public static final String MSG_PASSWORD_ERROR = "密码错误";
	public static final String MSG_USER_NOT_EXIST = "用户不存在";

	public static final String MSG_USER_SIGN_INVALID = "抱歉，您没有后台登陆权限!";

	public static final String MSG_LOGIN_OK = "上班了，亲！";

	public static final String MSG_USER_EDIT_OK = "修改成功";

	public static final String MSG_USER_EDIT_ERROR = "修改失败";

	public static final String MSG_EDIT_OK = "修改成功";

	public static final String MSG_EDIT_ERROR = "修改失败";

	public static final String MSG_SAVE_OK = "添加成功";

	public static final String MSG_SAVE_ERROR = "添加失败";

	public static final String MSG_CACHE_NOT_EXIST = "缓存键值不存在:";

	public static final String MSG_TRANSPORT_NOT_EXIST = "货源不存在";

	public static final Integer DEFAULT_PAGE_SIZE = 30;
	public static final Integer DEFAULT_PAGE_SIZE_INFO = 8;
	public static final Integer DEFAULT_PAGE_SIZE_INFO_BLOCK = 7;
	public static final Integer DEFAULT_PAGE_SIZE_PC = 8;
	public static Date TODAY = null;

	public static String SDF_TODAY = null;

	public static int INFO_QUERY_STOP = 0;

	public static int INFO_UPLOAD_STOP_ALL = 0;

	public static int INFO_UPLOAD_STOP_USER = 0;

	public static final String CACHE_INFO_STATUS_SETTING = "INFO_STATUS_";
	public static final String CACHE_INFO_QUERY_STOP_SETTING = CACHE_INFO_STATUS_SETTING + 3;
	public static final String CACHE_INFO_UPLOAD_STOP_ALL_SETTING = CACHE_INFO_STATUS_SETTING + 2;
	public static final String CACHE_INFO_UPLOAD_STOP_USER_SETTING = CACHE_INFO_STATUS_SETTING + 1;

	public static final String CACHE_USER_KEY = "USER_";

	public static final String CACHE_TICKET_KEY = "TICKET_";

	public static final String TICKET_CHANGE_KEY = "ticket:change:key";

	public static final String CACHE_CAR_TICKET_KEY = "login:clientSign:user:car:";
	public static final String CACHE_GOODS_TICKET_KEY = "login:clientSign:user:goods:";
	public static final String CACHE_OLD_TICKET_KEY = "old:";


	public static final String CACHE_INFO_CELLPHONE_KEY = "CELLPHONE_";

	public static final String CACHE_INFO_KEY = "INFO_";

	public static final String CACHE_INFO_ENABLE_LIST_KEY = "INFO_ENABLE_LIST";

	public static final String CACHE_INFO_DISABLE_LIST_KEY = "INFO_DISABLE_LIST";

	public static final String CACHE_USER_INFO_ENABLE_LIST_KEY = "UI_ENABLE_LIST_";

	public static final String CACHE_USER_INFO_DISABLE_LIST_KEY = "UI_DISABLE_LIST_";

	public static final String CACHE_BLOCK_KEY = "BLOCK_";

	public static final String CACHE_INFO_QUERY_KEY = "IQ_";
	public static final String CACHE_INFO_QUERY_XXTEA_KEY = "IQ_XT_";
	// PC 专用 query缓存KEY
	public static final String CACHE_INFO_QUERY_PC_KEY = "IQ_PC_";
	public static final String CACHE_INFO_QUERY_PC_XXTEA_KEY = "IQ_XT_PC_";

	public static final String CACHE_SESSION_KEY = "SESSION_";

	public static final String CACHE_CONFIG_KEY = "CONFIG_";

	public static final String CACHE_VERSION_KEY = "PLAT_VERSION_KEY_";
	public static final String CACHE_COORD_XML_KEY = "COORD_KEY_";
	// 发布条数
	public static final String CACHE_INFO_NUMBER_KEY = "INFO_NUMBER_";

	public static final long CACHE_EXPIRE_TIME_NEVER = 0;
	// public static final long CACHE_EXPIRE_TIME = 3600;//1h
	// public static final long CACHE_EXPIRE_TIME_3H = 10800;//3h
	public static final long CACHE_EXPIRE_TIME_12H = 43200;// 12h
	public static final long CACHE_EXPIRE_TIME_24H = 86400;// 24h
    public static final long CACHE_EXPIRE_TIME_30D = 86400*30;// 30天
	public static final long CACHE_EXPIRE_TIME_30M = 1800;// 30MIN
	public static final long CACHE_EXPIRE_TIME_5M = 300;// 5MIN
	public static final long CACHE_EXPIRE_TIME_3M = 180;// 3MIN
	public static final long CACHE_EXPIRE_TIME_60S = 60;// 60s
	public static final long CACHE_EXPIRE_TIME_30S = 30;// 30s
	public static final long CACHE_EXPIRE_TIME_15S = 15;// 15s
	public static final long CACHE_EXPIRE_TIME_10S = 10;// 10s
	public static final long CACHE_EXPIRE_TIME_5S = 5;// 10s
	public static final int CACHE_EXPIRE_LIMIT = 30 * 24 * 60 * 60;

	public static final int COOKIE_EXPIRE_TIME = 12 * 60 * 60;
	public static final int SESSION_EXPIRE_TIME = 12 * 60 * 60;

    public static final long IMPORT_THREAD_SLEEP_MILLISECOND = 1000;
    public static final int IMPORT_LIMIT = 100;
    public static final int REDIS_EXPIRE_TIME_5S = 5;

	public static final String SESSION_VALIDATE_CODE = "SESSION_VALIDATE_CODE";

	public static final String CACHE_PUBLIC_RESOURCE_KEY = "public_resource_";
	public static final String CACHE_PUBLIC_RESOURCE_TURNPICTURE_KEY = "public_resource_turnPicture";
	// 公共资源缓存key前缀，每条公共资源单独一个缓存，key是公共资源code，value是公共资源值
	public static final String CACHE_PUBLIC_RESOURCE_PREFIX = "public_resource:";

	public static final String CACHE_VERIFY_CODE_KEY = "verify_code_key_";

	public static final String CACHE_TYT_CONFIG_MAP_KEY = "TYT_CONFIG_MAP_";
    public static final String CACHE_TYT_CONFIG_MAP_FLAG_KEY = "TYT_CONFIG_MAP_FLAG";

	public static final String CACHE_TYT_CONFIG_TURNPICCTURE_MAP_KEY = "TYT_CONFIG_TURNPICTURE_MAP_";

	/* 货物信息去重键 */
	public static final String CACHE_HASHCODE_KEY = "HASHCODE_";
	/**
	 * 限制用户发货时间的缓存KEY
	 */
	public static final String CACHE_LIMIT_TRANSPORT_TIME = "limit:publish:transport:";

	/**
	 * 客户端标识1 PC 2 ANDROID 3 IOS 默认1
	 */
	public static final int PLAT_PC = 1;
	public static final int PLAT_ANDROID = 2;
	public static final int PLAT_IOS = 3;
	public static final int PLAT_APAD = 4;
	public static final int PLAT_IPAD = 5;
	public static final int PLAT_WEB = 6;
    public static final int PLAT_ANDROID_CAR = 21;
    public static final int PLAT_IOS_CAR = 31;
    public static final int PLAT_ANDROID_GOODS = 22;
    public static final int PLAT_IOS_GOODS = 32;
    public static final int PLAT_WEB_GOODS = 62;


	/* 有关Excel操作涉及到的字段 */
	// public static final Integer EXCELTOTALCOLUMNS = 38;//Excel导出操作的显示列数
	// public static final Integer EXCELTIMECOLUMNS = 30;//Excel导出操作的显示列数

	public static final String PRIVATE_KEY_ = "1345~opo-4%";
	// TODO PRIVATE_KEY 需要分开吗？
	public static final String PRIVATE_KEY_PC = "1345~opo-4%";
	public static final String PRIVATE_KEY_ANDROID = "1345~opo-4%";
	public static final String PRIVATE_KEY_IOS = "1345~opo-4%";

	// 招聘发布-职位信息选项
	/*
	 * public static final Integer POSITION0=0;//不限 public static final Integer
	 * POSITION1=1;//大板车司机 public static final Integer POSITION2=2;//17.5米平板车司机
	 * public static final Integer POSITION3=3;//三线六桥板车司机 public static final
	 * Integer POSITION4=4;//特种车司机 public static final Integer
	 * POSITION5=5;//临时司机 public static final Integer POSITION6=6;//其他车辆司机
	 */
	public static final Integer POSITION0 = 0;// 不限
	public static final Integer POSITION1 = 1;// 13.5米大板车司机
	public static final Integer POSITION2 = 2;// 17.5米大板车司机
	public static final Integer POSITION3 = 3;// 6.8米/9.6米单机板车司机
	public static final Integer POSITION4 = 4;// 特种车司机
	public static final Integer POSITION5 = 5;// 临时司机
	public static final Integer POSITION6 = 6;// 其他车辆司机

	// 招聘发布-职务
	public static final Integer DUTY0 = 0;// 不限
	public static final Integer DUTY1 = 1;// 主司机
	public static final Integer DUTY2 = 2;// 副司机

	// 招聘发布-教育信息选项
	public static final Integer EDUCATION0 = 0;// 无要求
	public static final Integer EDUCATION1 = 1;// 高中
	public static final Integer EDUCATION2 = 2;// 技校
	public static final Integer EDUCATION3 = 3;// 中专
	public static final Integer EDUCATION4 = 4;// 大专
	public static final Integer EDUCATION5 = 5;// 本科
	public static final Integer EDUCATION6 = 6;// 其它

	// 招聘发布-工资信息选项
	public static final Integer SALARY0 = 0;// 不限
	public static final Integer SALARY1 = 1;// 面议
	public static final Integer SALARY2 = 2;// 5000以下
	public static final Integer SALARY3 = 3;// 5000-6000
	public static final Integer SALARY4 = 4;// 6000-7000
	public static final Integer SALARY5 = 5;// 7000-8000
	public static final Integer SALARY6 = 6;// 8000以上
	// 招聘发布-驾龄信息选项
	/*
	 * public static final Integer YEARS0=0;//无要求 public static final Integer
	 * YEARS1=1;//2年以下 public static final Integer YEARS2=2;//2-5年 public static
	 * final Integer YEARS3=3;//5-7年 public static final Integer
	 * YEARS4=4;//7-10年 public static final Integer YEARS5=5;//10-15年 public
	 * static final Integer YEARS6=6;//15-20年 public static final Integer
	 * YEARS7=7;//20-25年 public static final Integer YEARS8=8;//25-30年 public
	 * static final Integer YEARS9=9;//30年以上
	 */
	// 招聘发布-驾龄信息选项
	public static final Integer YEARS0 = 0;// 无要求
	public static final Integer YEARS1 = 1;// 5年以下
	public static final Integer YEARS2 = 2;// 5-10年
	public static final Integer YEARS3 = 3;// 10年以上

	// 招聘发布-年龄信息选项
	public static final Integer AGE0 = 0;// 无要求
	public static final Integer AGE1 = 1;// 18-30岁
	public static final Integer AGE2 = 2;// 30-40岁
	public static final Integer AGE3 = 3;// 40-50岁
	public static final Integer AGE4 = 4;// 50岁以上

	// 二手车发布-车名品牌
	public static final Integer CARNAME0 = 0;// 牵引车头搜索-不限
	public static final Integer CARNAME1 = 1;// 牵引头品牌-德龙
	public static final Integer CARNAME2 = 2;// 牵引头品牌-欧曼
	public static final Integer CARNAME3 = 3;// 牵引头品牌-东风
	public static final Integer CARNAME4 = 4;// 牵引头品牌-北奔
	public static final Integer CARNAME5 = 5;// 牵引头品牌-解放
	public static final Integer CARNAME6 = 6;// 牵引头品牌-一汽重卡
	public static final Integer CARNAME7 = 7;// 牵引头品牌-其它

	// 二手车发布-价格区间
	public static final Integer PRICE0 = 0;// 不限
	public static final Integer PRICE1 = 1;// 面议
	public static final Integer PRICE2 = 2;// 2万以下
	public static final Integer PRICE3 = 3;// 2-5万
	public static final Integer PRICE4 = 4;// 5-15万
	public static final Integer PRICE5 = 5;// 15-25万
	public static final Integer PRICE6 = 6;// 25万以上

	// 二手车-是否分期付款
	public static final Integer SUBSECTION0 = 0;// 是否分期
	public static final Integer SUBSECTION1 = 1;// 是
	public static final Integer SUBSECTION2 = 2;// 否

	// 二手车发布-车龄
	public static final Integer CARAGE0 = 0;// 不限
	public static final Integer CARAGE1 = 1;// 1年以下
	public static final Integer CARAGE2 = 2;// 1-2年
	public static final Integer CARAGE3 = 3;// 2-3年
	public static final Integer CARAGE4 = 4;// 3-4年
	public static final Integer CARAGE5 = 5;// 4年以上
	// 信息的截取长度
	public static Integer INSURE_DESCRIBE_LENGTH = 25;

	// 司机招聘，二手车，新车资讯，保险广告信息发布状态
	public static Integer INFO_STATUS_DISABLE = 0;// 无效
	public static Integer INFO_STATUS_WAIT = 1;// 待审核
	public static Integer INFO_STATUS_PASS = 2;// 审核通过
	public static Integer INFO_STATUS_FAILURE = 3;// 审核未通过
	public static Integer INFO_STATUS_OUT = 4;// 无效过期信息
	public static Integer INFO_STATUS_NEVER = 5;// 回收站中删除

	// 身份
	public static Integer IDENTITY0 = 0;// 招聘身份
	public static Integer IDENTITY1 = 1;// 公司
	public static Integer IDENTITY2 = 2;// 个人

	// 性别
	public static Integer SEX0 = 0;// 性别
	public static Integer SEX1 = 1;// 男
	public static Integer SEX2 = 2;// 女

	// 事故历史
	public static Integer HISTORY0 = 0;// 事故历史
	public static Integer HISTORY1 = 1;// 无
	public static Integer HISTORY2 = 2;// 有

	public static Integer CATEGORY0 = 0;// 不限
	public static Integer CATEGORY1 = 1;// 在售车
	public static Integer CATEGORY2 = 2;// 积压车

	public static Integer DISTINGUISH0 = 0;// 不限
	public static Integer DISTINGUISH1 = 1;// 招聘者;代售
	public static Integer DISTINGUISH2 = 2;// 求职者;求购

	public static final Integer INSURECOMPANY0 = 0;// 不限
	public static final Integer INSURECOMPANY1 = 1;// 中国人寿
	public static final Integer INSURECOMPANY2 = 2;// 中国人保

	public static final String SUBSIDY0 = "不限";
	public static final String SUBSIDY1 = "五险一金";
	public static final String SUBSIDY2 = "包吃";
	public static final String SUBSIDY3 = "包住";
	public static final String SUBSIDY4 = "年底双薪";
	public static final String SUBSIDY5 = "周末双休";
	public static final String SUBSIDY6 = "交通补助";
	public static final String SUBSIDY7 = "加班补助";
	public static final String SUBSIDY8 = "餐补";
	public static final String SUBSIDY9 = "话补";
	public static final String SUBSIDY10 = "房补";

	public static final String KIND0 = "不限";
	public static final String KIND1 = "交强险";
	public static final String KIND2 = "第三者责任险";
	public static final String KIND3 = "车辆损失险";
	public static final String KIND4 = "不计免赔特约险";
	public static final String KIND5 = "盗抢险";
	public static final String KIND6 = "车上座位责任险";
	public static final String KIND7 = "玻璃单独破碎险";
	public static final String KIND8 = "自燃险";
	public static final String KIND9 = "新增设备损失险";

	public static final Integer ADVICETITLE0 = 0;// 不限
	public static final Integer ADVICETITLE1 = 1;// 诚信问题
	public static final Integer ADVICETITLE2 = 2;// 软件错误
	public static final Integer ADVICETITLE3 = 3;// 改进建议
	public static final Integer ADVICETITLE4 = 4;// 其它问题
	public static final Integer ADVICETITLE5 = 5;// 功能建议
	// 意见反馈审核状态
	public static final Integer ADVICE_STATUS_NO = 1;
	public static final Integer ADVICE_STATUS_YES = 2;
	// 黑名单信息审核状态
	public static final Integer BLOCKIFO_STATUS_NO = 1;
	public static final Integer BLOCKIFO_STATUS_YES = 2;

	// 新车类型
	public static final Integer MODEL0 = 0;// 不限
	public static final Integer MODEL1 = 1;// 牵引头
	public static final Integer MODEL2 = 2;// 挂车
	public static final Integer MODEL3 = 3;// 牵引头加挂车

	// 收集状态
	public static final Integer COLLECT_STATUS0 = 0;// 不限
	public static final Integer COLLECT_STATUS1 = 1;// 收藏
	public static final Integer COLLECT_STATUS2 = 2;// 浏览
	// 访问状态
	public static final Integer VISITE_STATUS0 = 0;// 不限
	public static final Integer VISITE_STATUS1 = 1;// 已浏览

	// 有效期
	public static final Integer SAVADAY1 = 1;// 剩余天数大于等于1；
	public static final String TYT_CAR_FORE_FORMTOCKEN_ = "tyt_car_fore_formtocken_";
	public static final String TYT_IDENTITY_FORE_FORMTOCKEN_ = "tyt_identity_fore_formtocken_";
	public static final String TYT_PAY_BACK_FORMTOCKEN_ = "tyt_pay_back_formtocken_";
	public static final String TYT_MESSAGE_SEND_FORMTOCKEN_ = "tyt_message_send_formtocken_";
	public static final String TYT_SAVE_LIST_NOTIFY_FORMTOCKEN_ = "tyt_save_list_notify_formtocken_";
	// 配置名称
	public static final String CONFIG_TRANSPORTCOUNTS = "transportPublishCounts";
	/* 资源key */
	public static final String CACHE_TYT_SOURCE_MAP_KEY = "TYT_SOURCE_MAP_KEY_";
	// 发货限制规则列表
	public static final String CACHE_STTLIMIT_KEY = "CACHE_STTLIMIT_KEY_";
	// 用户发货条数缓存key
	public static final String CACHE_USERSUB_KEY = "CACHE_USERSUB_KEY_";
	// 表名
	public static final String TABLE_BCAR_JOB_NAME = "tyt_bcar_job";
	public static final String TABLE_BCAR_RECRUIT_NAME = "tyt_bcar_recruit";
	public static final String TABLE_SCAR_JOB_NAME = "tyt_scar_job";
	public static final String TABLE_SCAR_RECRUIT_NAME = "tyt_scar_recruit";
	// public static void main(String[] args) {
	// System.out.println(citys.size());
	// }
	public static final String TYT_CAR_NEWS_FORMTOCKEN_ = "tyt_car_news_formtocken_";
	// 用户身份标签缓存键值
	public static final String CACHE_USER_IDENTITY_LABLES_BCAR = "CACHE_IDENTITY_LABLES_BCAR_";
	public static final String CACHE_USER_IDENTITY_LABLES_SCAR = "CACHE_IDENTITY_LABLES_SCAR_";
	public static final String TABLE_TRANSPORT_ORDERS_NAME = "tyt_transport_orders";// 接单表sort_id

	public static final String MERCHANT_TYPE_GROUP_CODE = "merchant_type";
	/* 图片地址前缀 */
	public static final String IMAGE_PREFIX = AppConfig.getProperty("picture.path.domain");

	// tyt_source父级-子级KEYs
	public static final String CACHE_TYT_SOURCE_PARENT_SUB_KEY_ = "CACHE_TYT_SOURCE_PARENT_SUB_KEY_";
	public static final String MAINTAINER_SKILL__GROUP_CODE = "merchant_service_kind";

	// 地区码
	public static final String CACHE_TYT_GEO_DICT = "CACHE_TYT_GEO_DICT_";
	public static final int CACHE_TYT_GEO_DICT_TIME = 30 * 24 * 60 * 60;
	public static final String CACHE_PAGE_GRADE = "CACHE_PAGE_GRADE_";// 分页级别
	public static final String CACHE_ROLE_MENU = "CACHE_ROLE_MENU_KEY_";
	public static final String CACHE_MENU = "CACHE_MENU_KEY_";
	/*
	 * 用户拨打货源电话相关常量
	 */
	public static final Short USER_CLASS_DEFUALT_VALUE = -1;
	public static final Short IDENTITY_TYPE_DEFAULT_VALUE = -1;
	public static final String CALL_PHONE_LIMIT_KEY = "call_phone_limit";
	public static final long CALL_PHONE_TIME_CACHE_TIME = 24 * 60 * 60;
	public static final String GROUP_CODE_CAR = "user_identity_type_2";
	public static final String GROUP_CODE_DELIEVER_GOOD = "user_identity_type_1";
	public static final String IDENTITY_LEVEL_ONE = "user_deliver_type_one";
	public static final String IDENTITY_LEVEL_TWO = "user_deliver_type_two";

	// 登录次数验证KEY
	public static final String CACHE_LOGIN_VERIFY_KEY = "MANAGE_LOGIN_VERIFY_";

	public static final int PICTURE_TYPE_LOOP = 2;
	public static final int PICTURE_TYPE_ADVERTISE = 1;
	public static final Short OPEN_CLOSE_STATUS_CLOSE = 0;
	public static final Short OPEN_CLOSE_STATUS_OPEN = 1;
	public static final Short LOOP_PICTURE_STATUS_OK = 0;
	public static final Short LOOP_PICTURE_STATUS_DELETE = 1;

	// 用户定向更新数据是否需要更新key 1:不需要更新 2：需要更新 更新完成后设置为1
	public static final String SPECIFIED_USER_UPGRADE_ISUPDATE_FLAG = "common_specified_user_upgrade_isupdate_flag";
	public static final String LOOP_PICTURE_207_DB_PATH = "loopPicture207DBPath";
	public static final String LOOP_PICTURE_207_FULL_PATH = "loopPicture207FullPath";
	public static final String TRANSPORT_NEWS_207_DB_PATH = "transportNews207DBPath";
	public static final String TRANSPORT_NEWS_207_FULL_PATH = "transportNews207FullPath";
	public static final String ADVERTISE_PICTURE_MAX_ENABLE_NUM = "advertisePictureMaxEnableNum";
	public static final String PEOPLE_ORDER_LIST_PAGE_SIZE_KEY = "peopleOrderListPageSize";
	public static final String REDIS_LOCK_TIMEOUT_KEY = "redis_lock_timeout";
	public static final String PEOPLE_ORDER_DEPARMENT_ID_KEY = "peopleOrderDeparmentId";
	public static final String PEOPLE_ORDER_ROLE_ID_KEY = "peopleOrderRoleId";
	public static final String PEOPLE_ORDER_ADD_TRACER_MSG_KEY = "people_order_add_tracer_msg";
	public static final String PEOPLE_ORDER_UPDATE_TRACER_MSG_KEY = "people_order_update_tracer_msg";
	public static final String TYT_PEOPLE_ORDER_LENGTH_SIZE_KEY = "tyt_people_order_length_size";
	public static final String TYT_PEOPLE_ORDER_WIDE_SIZE_KEY = "tyt_people_order_wide_size";
	public static final String TYT_PEOPLE_ORDER_HIGH_SIZE_KEY = "tyt_people_order_high_size";
	public static final String PEOPLE_ORDER_INTENTION_TRANSPORT_SMS_KEY = "people.order.intention.transport_sms";
	public static final String PEOPLE_ORDER_INTENTION_CAR_SMS_KEY = "people.order.intention.car_sms";
	public static final String STIMULATE_ORDER_CONFIRM_UPDATE_KEY = "stimulate_order_confirm_update_msg";
	public static final String STIMULATE_ORDER_RELIEVE_UPDATE_KEY = "stimulate_order_relieve_update_msg";
	public static final String MANAGE_PUBLIC_IP_USER = "manage.public.ip.user";
	public static final String MANAGE_PUBLIC_IP = "manage.public.ip";
	public static final String MAGAGE_NO_LIMIT_IP = "magage.no.limit.ip";
	public static final String SYNC_CAR_BI_PATH_KEY = "sync.car.bi.path";
	public static final String SYNC_CAR_BI_PRIVATEKEY_KEY = "sync.car.bi.privatekey";
	public static final String SYNC_CAR_BI_HTTP_BASE_KEY = "sync.car.bi.http.base";

	public static final String LOCK_CAR_PRE_BASE = "lock.car.preference_";
	public static final String STOP_FIND_CAR_GROUP_CODE = "stop_car_reasong";

	// 新地区码缓存KEY
	public static final String CACHE_TYT_GEO_DICT_NEW = "CACHE_TYT_GEO_DICT_NEW_";
	// 新地区码以坐标为key
	public static final String CACHE_TYT_GEO_DICT_PX_PY_NEW = "CACHE_TYT_GEO_DICT_PX_PY_NEW_";
	public static final String REQUEST_URL_LIMIT_PLAT_CACHE_KEY = "request.url.limit.plat.cache.key";
	public static final String REQUEST_URL_LIMIT_PC_CACHE_KEY = "request.url.limit.pc.cache.key";
	public static final String SINGLE_USER_CALL_URL_LIMIT_CACHE_KEY = "single.user.call.url.limit.cache";
	public static final String LOCATION_FILTER_CACHE_KEY = "location.filter.cache";

	//车辆认证失败发送站内信模板key
	public static final String CAR_AUTH_FAILE_TRACER_ACC_KEY = "car_auth_faile_for_accendant_msg";
	public static final String GOODS_EXPIRED_STARTTIME_KEY = "goods.expired.starttime";
	public static final String TRACER_END_TIME_KEY = "tracer.end.time";
	public static final int PICTURE_TYPE_LOOP_CAR_BUY = 3;
	public static final String WITHDRAW_VERIFY_MSG_KEY = "withdraw.verify.msg.key";
	public static final String WITHDRAW_VERIFY_CODE_REDIS_KEY = "withdraw.verify.code.redis.key";
	public static final String WITHDRAW_VALIDATE_SESSION_KEY = "withdraw.validate.session.key";
	public static final String WITHDRAW_VALIDATE_OK = "WITHDRAW_VALIDATE_OK";

	public static final String GOODS_REFUND_VERIFY_MSG_KEY = "goods.refund.verify.msg.key";
	public static final String GOODS_REFUND_VERIFY_CODE_REDIS_KEY = "goods.refund.verify.code.redis.key";


	/**
	 * 用户设备基本信息缓存的key
	 */
	public static final String MANAGE_USER_HEADER = "manage:user:header:";
	public static final int START_PICTURE_TYPE_LOOP = 5;
	public static final int PICTURE_TYPE_LOOP_TRANSPORT = 6;
	
	public static final int RIGHTS_EXPERIENCE = 1;
	public static final int RIGHTS_EXPERIENCE_TIMEOUT = 2;
	public static final int RIGHTS_NORMAL = 3;
	public static final int RIGHTS_NORMAL_TIMEOUT = 4;
	public static final int RIGHTS_VIP = 5;
	public static final int RIGHTS_VIP_TIMEOUT = 6;
	public static final String INDEX_NORMAL_IDENTITY_TYPE1 = "100";
	public static final String INDEX_NORMAL_IDENTITY_TYPE2 = "2";
	public static final String INDEX_NORMAL_IDENTITY_CONTENT = "恭喜您获得3次免费查看电话机会！可立即开始找货！";
	public static final String INDEX_NORMAL_CAR_AUTH_TYPE1 = "100";
	public static final String INDEX_NORMAL_CAR_AUTH_TYPE2 = "4";
	public static final String INDEX_NORMAL_CAR_AUTH_CONTENT = "恭喜您获得20天试用会员！可免费使用30次查看电话机会！";
	public static final String INDEX_PAY_VIP_TYPE1 = "100";
	public static final String INDEX_PAY_VIP_TYPE2 = "5";
	public static final String INDEX_PAY_VIP_CONTENT = "您的会员专属功能已开通，可享无限次拨打！有效期至${date}。";
	public static final String INDEX_PAY_VIP_NO_IDENTITY_TYPE1 = "100";
	public static final String INDEX_PAY_VIP_NO_IDENTITY_TYPE2 = "6";
	public static final String INDEX_PAY_VIP_NO_IDENTITY_CONTENT = "您的会员专属功能已开通，认证身份后可享无限次拨打！有效期至${date}。";

	public static final String REDIS_ORDER_IDS_KEY = "vip_pay_order_ids";

	// 支付测试账号，用于支付一分钱
	public static final String PAY_TEST_CELLPHONE = "11112222333";

	/**
	 * 5分钟
	 */
	public static final int MINUTES_5 = 5;
	/**
	 * 24小时换算为分钟
	 */
	public static final int MINUTES_24H = 1440;

	public static final String CACHE_DRAW_BILL_LINK = "cache_draw_bill_linke_";

    public static final String START_AD_PICTURE_207_FULL_PATH = "startAdPicture207FullPath";
    public static final String START_AD_PICTURE_207_DB_PATH = "startAdPicture207DBPath";

	public static final String BILLING_DRIVER_FULL_PATH = "billingDriverFullPath";
	public static final String BILLING_DRIVER_DB_PATH = "billingDriverDbPath";

	/**
	 * manage系统验证登录信息的缓存KEY
	 */
	public static final String BS_LOGIN_VERIFY_REDIS_KEY = "bs:login:verify:redis:key:";

	/**
	 * callcenter系统验证登录信息的缓存KEY
	 */
	public static final String CS_LOGIN_VERIFY_REDIS_KEY = "cs:login:verify:redis:key:";
    /**
     * 统计发布中货源查看记录和沟通数,hash结构，后缀今天的日期,eg: plat:count:transports:2019-01-01
     */
    public static final String PLAT_COUNT_TRANSPORT_KEY = "plat:count:transports:";
    /**
     * 货源今日查看记录数,hash内数据，后缀货源ID,eg: view:count:5732821
     */
    public static final String VIEW_COUNT_HASH_KEY = "view:count:";
	/**
	 * 后台添加车辆默认失败原因
	 */
	public static final String CAR_FAILURE_HEAD_REASON = "车头信息待完善，请完善后重新提交审核";
	public static final String CAR_FAILURE_TAIL_REASON = "挂车信息待完善，请完善后重新提交审核";

	public static final String CACHE_CAR_CURRENT_LOCATION = "car:current:location:";
	/**
	 * 车辆定位信息缓存时间s
	 */
	public static final int CACHE_EXPIRE_TIME_5MIN = 300;

	/**
	 * 数字常量 1
	 */
	public static final int NUM_ONE = 1;
	/**
	 * 数字常量 0
	 */
	public static final int NUM_ZERO = 0;

	/**
	 * 城市地图导入锁
	 */
	public static final String city_import_lock = "tyt:lock:city_import";

	// 用户反馈原因key
	public static final String QUESTION_TYPE_DICTIONARY = "tyt:manager:cache:question_type_dictionary";

	/**
	 * Excel导入最大行数限制
	 */
	public static final Integer IMPORT_EXCEL_ROW_MAXNUM = 100000;

	public static final int TRACTOR = 1; //牵引车
	public static final int SINGLE_CAR = 2; //单机板

	/**
	 * 批量推技术服务费
	 */
	public static final String TEC_BATCH_REFUND_CONSTANT = "tec:batch:refund:";

}
