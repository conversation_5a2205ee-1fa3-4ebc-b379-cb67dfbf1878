package com.tyt.util;

import cn.hutool.core.math.MathUtil;
import com.google.zxing.common.detector.MathUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/6/27
 */
public class IdUtil {

    /**
     * @return java.lang.String
     * @Description 以当前时间毫秒数做基础计数, 返回唯一有序增长ID
     * 20位 = 当前时间毫秒数17位+随机3位数
     * <AUTHOR>
     * @Date 2019/3/13 18:56
     * @Param []
     **/
    public static String getIncreaseIdByLocalTime() {
        return new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()) +   //当前时间毫秒数-17位
                ((int) (Math.random() * 900) + 100);                       //随机3位数
    }
}
