package com.tyt.util;

import com.tyt.model.CarInsuranceInquiry;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.*;

/**
 * @Description  比较两个实体对象属性值差别工具类
 * <AUTHOR>
 * @Date  2019/3/22 11:52
 * @Param
 * @return
 **/
public class ClassCompareUtil {
    /**
     * 比较两个实体属性值，返回一个boolean,true则表时两个对象中的属性值无差异
     * @param oldObject 进行属性比较的对象1
     * @param newObject 进行属性比较的对象2
     * @return 属性差异比较结果boolean
     */
    public static boolean compareObject(Object oldObject, Object newObject) {
        Map<String, Map<String,Object>> resultMap=compareFields(oldObject,newObject);

        if(resultMap.size()>0) {
            return true;
        }else {
            return false;
        }
    }

    /**
     * 比较两个实体属性值，返回一个map以有差异的属性名为key，value为一个Map分别存oldObject,newObject此属性名的值
     * @param oldObject 进行属性比较的对象1
     * @param newObject 进行属性比较的对象2
     * @return 属性差异比较结果map
     */
    @SuppressWarnings("rawtypes")
    public static Map<String, Map<String,Object>> compareFields(Object oldObject, Object newObject) {
        Map<String, Map<String, Object>> map = null;

        try{
            /**
             * 只有两个对象都是同一类型的才有可比性
             */
            if (oldObject.getClass() == newObject.getClass()) {
                map = new LinkedHashMap<String, Map<String,Object>>();

                Class clazz = oldObject.getClass();
                //获取object的所有属性
                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz,Object.class).getPropertyDescriptors();

                for (PropertyDescriptor pd : pds) {
                    //遍历获取属性名
                    String name = pd.getName();

                    //获取属性的get方法
                    Method readMethod = pd.getReadMethod();

                    // 在oldObject上调用get方法等同于获得oldObject的属性值
                    Object oldValue = readMethod.invoke(oldObject);
                    // 在newObject上调用get方法等同于获得newObject的属性值
                    Object newValue = readMethod.invoke(newObject);

                    if(oldValue instanceof List){
                        continue;
                    }

                    if(newValue instanceof List){
                        continue;
                    }

                    if(oldValue instanceof Timestamp){
                        oldValue = new Date(((Timestamp) oldValue).getTime());
                    }

                    if(newValue instanceof Timestamp){
                        newValue = new Date(((Timestamp) newValue).getTime());
                    }

//                    if(oldValue == null && newValue == null){
//                        continue;
//                    }else if(oldValue == null && newValue != null){
//                        Map<String,Object> valueMap = new HashMap<String,Object>();
//                        valueMap.put("oldValue",oldValue);
//                        valueMap.put("newValue",newValue);
//
//                        map.put(name, valueMap);
//
//                        continue;
//                    }
                    if(oldValue == null || newValue == null){
                        continue;
                    }
                    // 比较这两个值是否相等,不等就可以放入map了
                    if (oldValue != null && newValue != null && !oldValue.equals(newValue)) {
                        Map<String,Object> valueMap = new HashMap<String,Object>();
                        valueMap.put("oldValue",oldValue);
                        valueMap.put("newValue",newValue);

                        map.put(name, valueMap);
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }

        return map;
    }
    
    
    /**
     * @Description  两个实体对象属性值比较测试方法
     * <AUTHOR>
     * @Date  2019/3/22 12:03
     * @Param [args]
     * @return void
     **/
    public static void main(String[] args) {
        CarInsuranceInquiry inquiry1 = new CarInsuranceInquiry();

        inquiry1.setId(1l);

        inquiry1.setCarOwner(1);
        inquiry1.setInsuranceType(1);
        inquiry1.setServiceUserName("张三");

        inquiry1.setDealStatus(1);

        CarInsuranceInquiry inquiry2 = new CarInsuranceInquiry();
        //1.属性值相同
        inquiry2.setId(1l);
        //2.属性值不同
        inquiry2.setCarOwner(2);
        inquiry2.setInsuranceType(3);
        //3.新增属性值
        inquiry2.setLinkPhone1("18813129858");
        inquiry2.setLinkPhone2("18812126889");
        inquiry2.setRemark("test");
        //4.空值测试
        inquiry2.setDealStatus(null);
        inquiry2.setHeadDrivingUrl("");

        Map<String, Map<String,Object>> resultMap=compareFields(inquiry1,inquiry2);
        int size=resultMap.size();
        if(size>0) {
            System.out.println("对象1与对象2的属性值有差异,差异结果如下：");

            Iterator<String> it = resultMap.keySet().iterator();
            while(it.hasNext()) {
                String key=it.next();
                System.out.println("  "+key+"(oldValue:"+resultMap.get(key).get("oldValue")+",newValue:"+resultMap.get(key).get("newValue")+")");
            }
        }else {
            System.out.println("对象1与对象2的属性值无差异！");
        }

    }

}

