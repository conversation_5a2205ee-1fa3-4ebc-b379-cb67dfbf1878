package com.tyt.util;


import com.tyt.service.config.TytConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 图片链接兼容工具类
 *
 * <AUTHOR>
 * @since 2025-3-26 10:41:24
 */
@Component
@RequiredArgsConstructor
public class PictureUtil {

    private final TytConfigService tytConfigService;
    private static final String PREFIX_PICTURE = "prefix_picture";
    private static final String HTTP = "http";
    private static final String DEFAULT_URL = "http://newtest.teyuntong.net/rootdata";

    public String getPictureUrl(String pictureUrl) {
        if (StringUtils.isNotBlank(pictureUrl) && !pictureUrl.startsWith(HTTP)) {
            String prefixPicture = tytConfigService.getStringValue(PREFIX_PICTURE,DEFAULT_URL);
            return prefixPicture + pictureUrl;
        }
        return pictureUrl;
    }
}
