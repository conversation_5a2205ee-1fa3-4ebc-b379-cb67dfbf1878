package com.tyt.util;

import com.tyt.manager.utils.XmlReaderUtil;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class ParseCoordXmlUtil {
	
	public static Map<String, String> parseCoordXml() {
		Map<String, String> map = new HashMap<>();
		try {

			DocumentBuilder builder = XmlReaderUtil.getDocumentBuilder();

			Document document = builder.parse(ParseCoordXmlUtil.class.getResource("/").getPath()+"pcc.xml");
			NodeList provinces = document.getElementsByTagName("pro");

			for (int i = 0; i < provinces.getLength(); i++) {
				Node proNode = provinces.item(i);
				if (proNode.getNodeType() != Node.ELEMENT_NODE)continue;
				Element proElement = (Element) proNode;
				map.put(coord(proElement.getAttribute("px"))+","+coord(proElement.getAttribute("py")),
						coord(proElement.getAttribute("longitude"))+","+coord(proElement.getAttribute("latitude"))
						);
				NodeList citys = proElement.getChildNodes();
				for (int j = 0; j < citys.getLength(); j++) {
					Node cityNode = citys.item(j);
					if (cityNode.getNodeType() != Node.ELEMENT_NODE)continue;
					Element cityElement = (Element) cityNode;
					
					map.put(coord(cityElement.getAttribute("px"))+","+coord(cityElement.getAttribute("py")),
							coord(cityElement.getAttribute("longitude"))+","+coord(cityElement.getAttribute("latitude"))
							);
					NodeList counties = cityElement.getChildNodes();
					for (int c = 0; c < counties.getLength(); c++) {
						Node countyNode = counties.item(c);
						if (countyNode.getNodeType() != Node.ELEMENT_NODE)continue;
						Element countyElement = (Element) countyNode;
						map.put(coord(countyElement.getAttribute("px"))+","+coord(countyElement.getAttribute("py")),
								coord(countyElement.getAttribute("longitude"))+","+coord(countyElement.getAttribute("latitude"))
								);
						NodeList towns = countyElement.getChildNodes();
						for(int n = 0; n < towns.getLength(); n++){
							Node townNode = towns.item(n);
							if (townNode.getNodeType() != Node.ELEMENT_NODE)continue;
							Element townElement = (Element) townNode;
							map.put(coord(townElement.getAttribute("px"))+","+coord(townElement.getAttribute("py")),
									coord(townElement.getAttribute("longitude"))+","+coord(townElement.getAttribute("latitude"))
									);
							townElement=null;
							townNode=null;
						}
						towns=null;
						countyElement=null;
						countyNode=null;
					}
					counties=null;
					cityElement=null;
					cityNode=null;
				}
				citys=null;
				proElement=null;
				proNode=null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}
	/**
	 * 坐标乘以100
	 * @param coord
	 * @return
	 */
	private static int coord(String coord){
		if(coord==null||coord.equals(""))return 0;
		return new BigDecimal(coord).movePointRight(2).intValue();
	}

}
