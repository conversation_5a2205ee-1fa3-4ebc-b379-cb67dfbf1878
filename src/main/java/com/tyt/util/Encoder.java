package com.tyt.util;
import java.io.UnsupportedEncodingException;
public class Encoder {
    private final static char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',  'e', 'f'};
    public Encoder() {
    }
    public static String gbk2iso( String s )
    {
        try
        {
            return new String( s.getBytes( "GBK" ), "ISO-8859-1" );
        }
        catch (UnsupportedEncodingException e)
        {
            return s;
        }
        catch (NullPointerException e)
        {
            return "";
        }
    }

    public static String gbk2utf8( String s )
    {
        try
        {
            return new String( s.getBytes( "GBK" ), "UTF-8" );
        }
        catch (UnsupportedEncodingException e)
        {
            return s;
        }
        catch (NullPointerException e)
        {
            return "";
        }
    }

    public static String iso2gbk( String s )
    {
        try
        {
            return new String( s.getBytes( "ISO-8859-1" ), "GBK" );
        }
        catch (UnsupportedEncodingException e)
        {
            return s;
        }
        catch (NullPointerException e)
        {
            return "";
        }
    }
    
    public static String md5( String s )
    {
        byte[] source = s.getBytes();
        try
        {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance( "MD5" );
            md.update( source );
            byte tmp[] = md.digest();

            char str[] = new char[16 * 2];

            int k = 0;
            for (int i = 0; i < 16; i++) {

                byte byte0 = tmp[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];

                str[k++] = hexDigits[byte0 & 0xf];
            }
            s = new String(str);
            return s;
        }
        catch( Exception e )
        {
            e.printStackTrace();
        }
        return null;
    }
    
    public static void main(String[] args) {
//    	System.out.println(Encoder.md5("1261717161"+AppConfig.getProperty("tyt.private.key")));
    	/*System.out.println(Encoder.md5("15227233635-13731265357"+" "
                + "徐水--任丘200挖 马上装；" + " "
                +  "592396777 " 
               ss +  "1 "
                + "2014-9-2 00:00:00"));*/ 
//    	Long time=1419996904567L;
    	String phone="13811699235";
    	String token=Encoder.md5("123456");
    	
    	
//    	String ticket=Encoder.md5(""+time);
//    	String validToken = Encoder.md5(ticket+AppConfig.getProperty("tyt.private.key"));
    	
    	System.out.println(token);
//    	System.out.println(validToken);
    }
}