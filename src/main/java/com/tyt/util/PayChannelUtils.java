package com.tyt.util;

import com.tyt.payment.service.IPayChannel;

public class PayChannelUtils {
    public static IPayChannel getPayChannel(String payType) {
        try {
            //这个路径需要根据你来定义，属于一种规则，这样一来，将所有策略都写在该包下即可
            String path = "com.tyt.payment.service.impl." + payType.concat("PayChannel");
            //反射，类加载器
            Class<?> clazz = Class.forName(path);
            //调用instance()方法获取单例对象
            IPayChannel instance = (IPayChannel) clazz.getDeclaredMethod("getInstance").invoke(null, null);
            return instance;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Load [" + payType.concat("Strategy") + "] Error :", e);
        }
    }
}
