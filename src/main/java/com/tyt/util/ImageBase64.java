package com.tyt.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by duanwc on 2019/3/21.
 */
public class ImageBase64 {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 去掉base64图片前缀
     *
     * @param imgString
     * @return
     */
    public static String replacePre(String imgString) {
        //允许的图片格式（可配置）
        String imgType = "jpg,png,jpeg,gif";
        if (!StringUtils.isEmpty(imgType)) {
            String[] imgTypes = imgType.split(",");
            Pattern pattern;
            Matcher matcher;
            String regex;
            for (String v : imgTypes) {
                regex = MessageFormat.format("data:image/{0};base64,", v);
                pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                matcher = pattern.matcher(imgString);
                if (matcher.lookingAt()) {
                    return matcher.replaceFirst("");
                }
            }
        }
        return imgString;
    }

    /**
     * base64 字符串转换成 InputStream
     * @param encodeBase64String
     * @return
     */
    public static InputStream byteToInputStream(String encodeBase64String){
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] bytes = decoder.decode(encodeBase64String);
        for (int i = 0; i < bytes.length; ++i) {
            if (bytes[i] < 0) {// 调整异常数据
                bytes[i] += 256;
            }
        }
        return new ByteArrayInputStream(bytes);
    }

    /**
     * 校验文件大小
     *
     * @param
     * @return
     */
    private boolean checkSize(byte[] bytes) {
        //符合条件的照片大小（可配置） 单位：M
        double imgSize = 2.0;
        //图片转base64字符串一般会大，这个变量就是设置偏移量。可配置在文件中，随时修改。目前配的是0。后续看情况适当做修改
        double deviation = 0.0;
        int length = bytes.length;
        //原照片大小
        double size = (double) length / 1024 / 1024 * (1 - deviation);
        logger.info("照片大小为：" + size + "M");
        return size <= imgSize;
    }


    /**
     * 校验宽高
     *
     * @param
     * @return
     */
    private boolean checkWidthAndHeight(int width, int height) {
        //宽高最小值（可配置） 单位px
        int imgRangeLow = 8;
        //宽高最大值（可配置） 单位px
        int imgRangeHigh = 40;
        logger.info("照片宽为：" + width + "px");
        logger.info("照片高为：" + height + "px");

        if (width > imgRangeLow && width <= imgRangeHigh &&
                height > imgRangeLow && height <= imgRangeHigh) {
            return true;
        }

        return false;
    }


}
