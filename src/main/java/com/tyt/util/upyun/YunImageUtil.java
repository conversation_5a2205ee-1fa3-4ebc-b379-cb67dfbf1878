package com.tyt.util.upyun;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.multipart.MultipartFile;

import com.tyt.util.FtpUtil;
import com.tyt.util.ImageUtil;
import com.tyt.web.qbean.KmConfig;

public class YunImageUtil {
	
	    // 运行前先设置好以下三个参数
		private static final String BUCKET_NAME = "tyt-idcard-images";
		private static final String OPERATOR_NAME = "sarah";
		private static final String OPERATOR_PWD = "sarahzhang";
		/** 根目录 */
		private static final String DIR_ROOT = "/";
		private static UpYun upyun = new UpYun(BUCKET_NAME, OPERATOR_NAME, OPERATOR_PWD);

	    public static String  uploadImage(String perfix,MultipartFile file, HttpServletRequest request){
	    	String fileName=ImageUtil.renameFile(file.getOriginalFilename());
		    try {
			     boolean result = upyun.writeFile(fileName, file.getInputStream(), true);
		    } catch (IOException e) {
			     e.printStackTrace();
		}
		    return fileName;
	}
	    
	   public static void deleteImage(String fileName,HttpServletRequest request){
		   String filePath = DIR_ROOT + fileName;
			// 删除文件
		   upyun.deleteFile(filePath);
		}
}
