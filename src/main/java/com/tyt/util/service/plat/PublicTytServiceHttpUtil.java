package com.tyt.util.service.plat;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.TreeMap;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.tyt.util.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.tyt.model.User;
import com.tyt.service.cache.CacheService;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.user.UserService;
import com.tyt.util.httpclient.HttpClient;
import com.tyt.util.httpclient.ResponseStatus;

public class PublicTytServiceHttpUtil {
	public static HttpClient httpClient = new HttpClient();

	public static PropertiesUtil propertiesUtil = PropertiesUtil.init("server");

	public static String key = propertiesUtil.getString("tyt.private.key");
	public static Logger logger = LoggerFactory.getLogger(PublicTytServiceHttpUtil.class);
	public static String testUserId="147260";
	/**
	 * 
	 * @param phone
	 * @param md5Pwd md5一次的密码
	 * @return
	 * @throws IOException 
	 * @throws HttpException 
	 * @throws UnsupportedEncodingException
	 */
	public static boolean platLogin() throws Exception 
	{
		
			TytConfigService tytConfigService=(TytConfigService)ApplicationContextUtils.getBean("tytConfigService");
			String serverUrl=tytConfigService.getStringValue("tyt_company_plat_service_url", "http://182.92.229.222:8081/plat/");
			String url=serverUrl+"plat/user/login";
			TreeMap<String,String> tree=new TreeMap<String,String>();
			String companyUserId=tytConfigService.getStringValue("tyt_company_account_user_id", "");
			if(companyUserId==null||"".equals(companyUserId)){
				companyUserId=testUserId;
			}
			UserService userService=(UserService)ApplicationContextUtils.getBean("userService");
			User u=userService.getById(Long.parseLong(companyUserId));

			tree.put("cellPhone", u.getCellPhone());

			tree.put("password", Encoder.md5(u.getPassword()+u.getCellPhone()));
			tree.put("clientSign", "6");
			tree.put("osVersion", "manage_new");
			tree.put("clientVersion", "80000");
			tree.put("clientId", "80000");
			tree.put("cid", "************");

	    	String sign=SignUtil.sign(tree, key);
	    	tree.put("sign", sign);

	    	ResponseStatus responseStatus=httpClient.post(url, tree, null);
	    	String responS =responseStatus.getContent();
	    	PlatResultMsgBean resultMsgBean= JSON.parseObject(responS, PlatResultMsgBean.class);
	    	if(resultMsgBean!=null&&resultMsgBean.getCode()==200){
	    		/*PlatUserBean platUserBean=((JSON)resultMsgBean.getData()).toJavaObject(PlatUserBean.class);
	    		String ticket=platUserBean.getTicket();

	        	logger.info("ticket="+ticket);
	    		if(ticket!=null&&!"".equals(ticket)){
	    			//保持缓存
	    		}*/
	    		logger.info("公司账户登录成功");
	    		return true;
	    	}

     	return false;
	}


	/**
	 * 模拟登陆
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public static boolean platLogin(String userId) throws Exception
	{

		TytConfigService tytConfigService=(TytConfigService)ApplicationContextUtils.getBean("tytConfigService");
		String serverUrl=tytConfigService.getStringValue("tyt_company_plat_service_url", "http://182.92.229.222:8081/plat/");
		String url=serverUrl+"plat/user/login";
		TreeMap<String,String> tree=new TreeMap<String,String>();
		UserService userService=(UserService)ApplicationContextUtils.getBean("userService");
		User u=userService.getById(Long.parseLong(userId));

		tree.put("cellPhone", u.getCellPhone());

		tree.put("password", Encoder.md5(u.getPassword()+u.getCellPhone()));
		tree.put("clientSign", "6");
		tree.put("osVersion", "manage_new");
		tree.put("clientVersion", "80000");
		tree.put("clientId", "80000");
		tree.put("cid", "************");

		String sign=SignUtil.sign(tree, key);
		tree.put("sign", sign);

		ResponseStatus responseStatus=httpClient.post(url, tree, null);
		String responS =responseStatus.getContent();
		PlatResultMsgBean resultMsgBean= JSON.parseObject(responS, PlatResultMsgBean.class);
		if(resultMsgBean!=null&&resultMsgBean.getCode()==200){
	    		/*PlatUserBean platUserBean=((JSON)resultMsgBean.getData()).toJavaObject(PlatUserBean.class);
	    		String ticket=platUserBean.getTicket();

	        	logger.info("ticket="+ticket);
	    		if(ticket!=null&&!"".equals(ticket)){
	    			//保持缓存
	    		}*/
			logger.info("公司账户登录成功");
			return true;
		}

		return false;
	}
	/**
	 * 调用plat工程服务
	 * @param url 不包含 域名与工程名的服务地址，url前面不要加/号
	 * @param paramsTreeMap 参数
	 * @param c app接口文档中data对应的对象
	 * @return T
	 * @throws IOException
	 * @throws HttpException
	 */
	public static <T> T callPlatService(String url, TreeMap<String,String>  treeMap,  Class<T> c) throws Exception {
			TytConfigService tytConfigService=(TytConfigService)ApplicationContextUtils.getBean("tytConfigService");
			String companyUserId=tytConfigService.getStringValue("tyt_company_account_user_id", "");
			if(companyUserId==null||"".equals(companyUserId)){
				companyUserId=testUserId;
			}

			String serverUrl=tytConfigService.getStringValue("tyt_company_plat_service_url", "http://182.92.229.222:8081/plat/");
			CacheService cacheService=(CacheService)ApplicationContextUtils.getBean("cacheServiceMcImpl");
		String objTicketKey = UserTicketUtil.getObjTicketKey(companyUserId);
		Object obj=cacheService.getObject(objTicketKey);
			String ticket=null;
			if(obj!=null){
				ticket=(String)obj;
			}else{
				platLogin();
				ticket=(String)cacheService.getObject(objTicketKey);
			}
			TreeMap <String,String> paramsTreeMap=new TreeMap<String,String>();
			if(treeMap!=null){
				for(Map.Entry<String,String> entry :treeMap.entrySet()){
					if(entry.getKey()!=null&&!"".equals(entry.getKey())&& entry.getValue()!=null&&!"".equals(entry.getValue())){
						paramsTreeMap.put(entry.getKey(), entry.getValue());
					}
				}
			}
			paramsTreeMap.put("clientSign", "6");
			paramsTreeMap.put("osVersion", "manage_new");
			paramsTreeMap.put("clientVersion", "80000");
			paramsTreeMap.put("userId",companyUserId);
			paramsTreeMap.put("ticket", ticket);
			String sign=SignUtil.sign(paramsTreeMap, PublicTytServiceHttpUtil.key);
			paramsTreeMap.put("sign", sign);
			ResponseStatus responseStatus=httpClient.post(serverUrl+url, paramsTreeMap, null);
	    	String responS =responseStatus.getContent();
	    	logger.info("访问plat服务{}返回内容为:{}",serverUrl+url,responS);
	    	PlatResultMsgBean resultMsgBean= JSON.parseObject(responS, PlatResultMsgBean.class);
	    	if(resultMsgBean!=null&&resultMsgBean.getCode()==200){
	    		T t=((JSON)resultMsgBean.getData()).toJavaObject(c);
	    		return t;
	    	}
		return null;
	}
	/**
	 * 调用plat工程服务
	 * @param url 不包含 域名与工程名的服务地址,url前面不要加/号
	 * @param paramsTreeMap 参数
	 * @return ResultMsgBean 中data是com.alibaba.fastjson.JSON对象 可以转为app接口文档中data对应的对象
	 * @throws IOException
	 * @throws HttpException
	 */
	public static PlatResultMsgBean callPlatService(String url, TreeMap<String,String>  treeMap) throws Exception {

			TytConfigService tytConfigService=(TytConfigService)ApplicationContextUtils.getBean("tytConfigService");
			String companyUserId=tytConfigService.getStringValue("tyt_company_account_user_id", "");
			if(companyUserId==null||"".equals(companyUserId)){
				companyUserId=testUserId;
			}

			//人工派单用户ID
			String srcMsgId = treeMap.get("srcMsgId");
			if(StringUtils.isNotEmpty(srcMsgId)){
				UserService userService = ApplicationContextUtils.getBean("userService");
				Long mtUserId	= userService.getUserIdBySrcMsgId(srcMsgId);
				if(mtUserId != 0){
					companyUserId = String.valueOf(mtUserId);
				}

			}
			//如果是保存，优先使用参数传入
			if(treeMap.get("userId") != null){
				companyUserId = treeMap.get("userId");
			}
			String serverUrl=tytConfigService.getStringValue("tyt_company_plat_service_url", "http://182.92.229.222:8081/plat/");
//			String serverUrl="http://127.0.0.1:8080/plat/";

			CacheService cacheService=(CacheService)ApplicationContextUtils.getBean("cacheServiceMcImpl");
		String objTicketKey = UserTicketUtil.getObjTicketKey(companyUserId);
		Object obj=cacheService.getObject(objTicketKey);
			String ticket=null;
			if(obj!=null){
				ticket=(String)obj;
			}else{
				platLogin(companyUserId);
				ticket=(String)cacheService.getObject(objTicketKey);
			}
			TreeMap <String,String> paramsTreeMap=new TreeMap<String,String>();
			if(treeMap!=null){
				for(Map.Entry<String,String> entry :treeMap.entrySet()){
					if(entry.getKey()!=null&&!"".equals(entry.getKey())&& entry.getValue()!=null&&!"".equals(entry.getValue())){
						paramsTreeMap.put(entry.getKey(), entry.getValue());
					}
				}
			}
			paramsTreeMap.put("clientSign", "6");
			paramsTreeMap.put("osVersion", "manage_new");
			paramsTreeMap.put("clientVersion", "80000");
			paramsTreeMap.put("userId",companyUserId);
			paramsTreeMap.put("ticket", ticket);
			paramsTreeMap.put("isMt", "1");
			String sign=SignUtil.sign(paramsTreeMap, key);
			paramsTreeMap.put("sign", sign);
			ResponseStatus responseStatus=httpClient.post(serverUrl+url, paramsTreeMap, null);
	    	String responS =responseStatus.getContent();
	    	logger.info("访问plat服务{}返回内容为:{}",serverUrl+url,responS);
	    	PlatResultMsgBean resultMsgBean= JSON.parseObject(responS, PlatResultMsgBean.class);
	    	return resultMsgBean;
	}

	/**
	 * 调用plat工程服务
	 * @param url 不包含 域名与工程名的服务地址,url前面不要加/号
	 * @param paramsTreeMap 参数
	 * @return String ResultMsgBean对象的 json字符串
	 * @throws Exception
	 */
	public static String callPlatServiceToJson(String url, TreeMap<String,String>  treeMap) throws Exception {

			TytConfigService tytConfigService=(TytConfigService)ApplicationContextUtils.getBean("tytConfigService");
			String companyUserId=tytConfigService.getStringValue("tyt_company_account_user_id", "");
			if(companyUserId==null||"".equals(companyUserId)){
				companyUserId=testUserId;
			}
			String serverUrl=tytConfigService.getStringValue("tyt_company_plat_service_url", "http://182.92.229.222:8081/plat/");

			CacheService cacheService=(CacheService)ApplicationContextUtils.getBean("cacheServiceMcImpl");
		String objTicketKey = UserTicketUtil.getObjTicketKey(companyUserId);
		Object obj=cacheService.getObject(objTicketKey);
			String ticket=null;
			if(obj!=null){
				ticket=(String)obj;
			}else{
				platLogin();
				ticket=(String)cacheService.getObject(objTicketKey);
			}
			TreeMap <String,String> paramsTreeMap=new TreeMap<String,String>();
			if(treeMap!=null){
				for(Map.Entry<String,String> entry :treeMap.entrySet()){
					if(entry.getKey()!=null&&!"".equals(entry.getKey())&& entry.getValue()!=null&&!"".equals(entry.getValue())){
						paramsTreeMap.put(entry.getKey(), entry.getValue());
					}
				}
			}
			paramsTreeMap.put("clientSign", "6");
			paramsTreeMap.put("osVersion", "manage_new");
			paramsTreeMap.put("clientVersion", "80000");		
			paramsTreeMap.put("userId",companyUserId);		
			paramsTreeMap.put("ticket", ticket);		
			String sign=SignUtil.sign(paramsTreeMap, key);
			paramsTreeMap.put("sign", sign);
			ResponseStatus responseStatus=httpClient.post(serverUrl+url, paramsTreeMap, null);
	    	String responS =responseStatus.getContent();
	    	logger.info("访问plat服务{}返回内容为:{}",serverUrl+url,responS);
	    	return responS;
    
	}
	/**
	 * 获得公司账户对象
	 * @return
	 */
	public static User getCompanyUser(){
		try {
			TytConfigService tytConfigService=(TytConfigService)ApplicationContextUtils.getBean("tytConfigService");
			String companyUserId=tytConfigService.getStringValue("tyt_company_account_user_id", "");
			
			CacheService cacheService=(CacheService)ApplicationContextUtils.getBean("cacheServiceMcImpl");
			Object obj=cacheService.getObject(Constant.CACHE_USER_KEY +companyUserId);
			if(obj!=null){
				User user=(User)obj;
				return user;
			}else
			{
				boolean loginStatus=platLogin();
				if(loginStatus){
					UserService userService=(UserService)ApplicationContextUtils.getBean("userService");
					User user=userService.getById(Long.parseLong(companyUserId));
					return user;
				}
			}
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 查询plat项目标准化货物相关
	 * @param keyword
	 * @param type 1是查询货物 2是查参数
	 * @return
	 */
	public static PlatResultMsgBean matches(String keyword,String type) {
		TytConfigService tytConfigService=(TytConfigService)ApplicationContextUtils.getBean("tytConfigService");
		String url=tytConfigService.getStringValue("tyt_company_plat_service_url", "http://182.92.229.222:8081/plat/");
		if ("1".equals(type)){
			url += "/openapi/transport/search/matches/group/v6000";
		}else {
			url += "/openapi/transport/search/matches/v6000";
		}
		String parameter = "clientSign=1001&"
				+ "clientVersion=1000&"
				+ "keyword=" + keyword
				+ "&timestamp=" + System.currentTimeMillis();
		String sign =Encoder.md5(parameter +  "tecjdcx~b9-4@l");
		url += "?" + parameter + "&sign=" + sign;
		String result = HttpUtil.post(url, new JSONObject());
		PlatResultMsgBean resultMsgBean= JSON.parseObject(result, PlatResultMsgBean.class);
		return resultMsgBean;
	}

}
