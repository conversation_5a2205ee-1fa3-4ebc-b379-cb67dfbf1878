package com.tyt.util;

import com.tyt.service.common.util.PropertiesLoader;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/12/09 16:20
 */
public abstract class RedissonUtils {
    private final static Logger logger = LoggerFactory.getLogger(RedissonUtils.class);

    private static volatile RedissonClient client = null;

    /**
     * 属性文件加载对象
     */
    private final static PropertiesLoader loader = new PropertiesLoader("tyt-common.properties", "server.properties",
            "server_url.properties");


    static {
        if (Objects.isNull(client)) {
            initSentinels();
        }
    }

    private static synchronized void initSentinels() {
        if (Objects.nonNull(client)) {
            return;
        }

        String host = loader.getProperty("tyt.redis.host", "*************");
        Integer port = loader.getInteger("tyt.redis.port", 6378);
        String password = loader.getProperty("tyt.redis.password", "Tytwrite01");
        Integer database = loader.getInteger("tyt.redis.database", 3);

        logger.info("init redisson start host is 【:{}】 port is 【:{}】 database is 【:{}】", host, port, database);
        Config config = new Config();

        config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setPassword(password)
                .setDatabase(database)
                .setConnectionPoolSize(30)
                .setConnectionMinimumIdleSize(10)
                .setTimeout(3000);

        client = Redisson.create(config);


    }

    public static RedissonClient getClient() {
        if (Objects.isNull(client)) {
            initSentinels();
        }
        return client;
    }


    /**
     * 从缓存中获取
     *
     * @param key       key
     * @param callable  缓存未命中执行的逻辑
     * @param cacheTime 缓存时间
     * @param timeUnit  时间单位
     * @param cacheNull 是否缓存null
     * @return 缓存结果
     */
    public static <T> T getObjectFromCache(String key, Long cacheTime, TimeUnit timeUnit,
                                           boolean cacheNull, Callable<T> callable) {
        RBucket<T> bucket = client.getBucket(key);
        T value = bucket.get();
        if (value == null) {
            try {
                value = callable.call();
            } catch (Exception e) {
                logger.error("缓存计算异常", e);
                throw new RuntimeException(e);
            }

            // value不为空或缓存空时才set
            if (value != null || cacheNull) {
                bucket.set(value, cacheTime, timeUnit);
            }
        }
        return value;
    }

    /**
     * 从缓存中删除
     *
     * @param key key
     * @return 缓存结果
     */
    public static boolean removeObjectFromCache(String key) {
        RBucket<Object> bucket = client.getBucket(key);
        return bucket.delete();
    }
}
