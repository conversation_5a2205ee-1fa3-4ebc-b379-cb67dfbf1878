package com.tyt.invitefriends.service;

import java.util.List;
import com.tyt.model.InviteFriends;
import com.tyt.model.PageBean;
import com.tyt.service.base.BaseService;
import com.tyt.web.back.internal.bean.InviteFriendsBean;

public interface InviteFriendsService extends BaseService<InviteFriends, Long>{

	public void saveInviteFriends(Long userId,String friends,Integer platId) throws Exception;
	
	public InviteFriends getByUserIdAndFriendCell(Long userId,String friendCell) throws Exception;
	
	public void addCounts(Long userId,String friendCell) throws Exception;
	/**
	 * 邀请朋友接口
	 * @param friend
	 * @param pageBean
	 * @return
	 * @throws Exception
	 */
	public List<InviteFriendsBean> getFriends(InviteFriendsBean friend,
			PageBean pageBean)throws Exception;

	public List<InviteFriendsBean> getFriendById(Long id)throws Exception;
}
