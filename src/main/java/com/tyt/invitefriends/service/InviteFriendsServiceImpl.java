package com.tyt.invitefriends.service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;
import com.tyt.dao.base.BaseDao;
import com.tyt.invitefriends.controller.Friends;
import com.tyt.invitefriends.dao.InviteFriendsDao;
import com.tyt.model.InviteFriends;
import com.tyt.model.PageBean;
import com.tyt.model.User;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;
import com.tyt.util.SendMessage;
import com.tyt.util.TimeUtil;
import com.tyt.web.back.internal.bean.InviteFriendsBean;

@Service("inviteFriendsService")
public class InviteFriendsServiceImpl extends BaseServiceImpl<InviteFriends, Long> implements InviteFriendsService{

	@Resource(name="inviteFriendsDao")
	public void setBaseDao(BaseDao<InviteFriends, Long> inviteFriendsDao) {
	        super.setBaseDao(inviteFriendsDao);
	}
	
	@Resource(name = "userService")
	private UserService userService;
	

	@Override
	public void saveInviteFriends(Long userId,String friends,Integer platId) throws Exception{
		InviteFriends oldFriend=null;
		InviteFriends friend=new InviteFriends();
		User user=null;
		for(Friends frd:StringToList(friends)){
			oldFriend=getByUserIdAndFriendCell(userId,frd.getTel());
			if(oldFriend!=null){
			  addCounts(userId, frd.getTel());	
			}else{
				friend.setUserId(userId);
				friend.setFriendCell(frd.getTel());
				friend.setFriendName(frd.getName());
				friend.setCtime(TimeUtil.getTimeStamp());
				friend.setPlatId(platId);
				add(friend);
				user=userService.getById(userId);
				SendMessage.sendMessage(frd.getTel(), "您的好友"
				     +user!=null?(user.getTrueName()==null?"":user.getTrueName()):""
				    		 +"邀请您成为特运通VIP用户,详情请见www.teyuntong.com");
			}
		}
		
	}
   /**
    * 字符串到对象集合
    * @param friends
    * @return
    */
   private List<Friends> StringToList(String friends){
	   String[] friendsArr=friends.split(",");
	   List<Friends> friendsList=new ArrayList<Friends>();
	   for(String friend:friendsArr){
		   String[] friendArr=friend.split(":");
		   String[] telsArr=friendArr[1].split("<");
		   for(String tel:telsArr){
			   Friends frd=new Friends(filterString(friendArr[0]),filterString(tel));
			   friendsList.add(frd);
			   frd=null; 
		   }
	   }
	   return friendsList;
   }
   /**
    * 过滤字符串，只保留某些值
    * @param str
    * @return
    */
   private String filterString(String str){
	   str=str.replaceAll("[^0-9a-zA-Z\u4e00-\u9fa5，,+:：]+","");
	   if(str.startsWith("+86")){
		   str=str.substring(3);
	   }
	   return str;
   }

	@Override
	public InviteFriends getByUserIdAndFriendCell(Long userId,String friendCell) throws Exception{
			
		  StringBuffer sql = new StringBuffer();
	      sql.append(" entity.userId =").append(userId);
	      sql.append(" and entity.friendCell ='").append(friendCell.trim()).append("'");
	      sql.append(" and date_format(entity.ctime,'%Y-%m-%d')='").append(TimeUtil.formatDate(TimeUtil.today())).append("'");
	      try{
	    	  List<InviteFriends> friendList=this.getList(sql.toString(), null);
		      InviteFriends friend=null;
		      if(friendList.size()>0){
		    	  friend=friendList.get(0);
		      }
		      return friend;
	      }catch(Exception e){
	    	  e.printStackTrace();
	      }
	      return null;
	      
	}



	@Override
	public void addCounts(Long userId, String friendCell) throws Exception{
		((InviteFriendsDao) this.getBaseDao()).addCounts(userId, friendCell);
	}
	
	@Override
	public List<InviteFriendsBean> getFriends(InviteFriendsBean friend,PageBean page) throws Exception {
		List<Object> listObject = new ArrayList<Object>();

		StringBuffer sbCount = new StringBuffer(
				"SELECT count(*) FROM tyt_invite_friends f left join tyt_user u on f.user_id=u.id where 1=1 ");

		StringBuffer sb = new StringBuffer();

		if(!StringUtils.isBlank(friend.getCellPhone())){
			Long userId=userService.getUserIdByCellPhone(friend.getCellPhone());
			if(userId!=null) {
				sb.append(" AND u.id=? ");
				listObject.add(userId);
			}else return null;
		} 
		
		if(!StringUtils.isBlank(friend.getTrueName())){
			sb.append(" AND u.true_name=? ");
			listObject.add(friend.getTrueName());
		} 
		
		if(friend.getPlatId()!=null){
			sb.append(" AND f.plat_id=? ");
			listObject.add(friend.getPlatId());
		} 
		
		if(friend.getCtime()!=null){
			sb.append(" AND date_format(f.ctime,'%Y-%m-%d')>=? ");
			listObject.add(TimeUtil.formatDate(friend.getCtime()));
		} 


		sbCount.append(sb);

		BigInteger rowCount = this.getBaseDao().query(sbCount.toString(),
				listObject.toArray());
		if (rowCount != null && rowCount.longValue() > 0) {
			StringBuffer sbSql = new StringBuffer(
					"SELECT f.id id,f.user_id userId,f.friend_cell friendCell,f.friend_name friendName,f.ctime ctime"+
",f.plat_id platId,f.friend_type friendType,f.remark remark,u.true_name trueName"+
",u.cell_phone cellPhone FROM tyt_invite_friends f LEFT JOIN tyt_user u ON f.user_id=u.id where 1=1 ");
			Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();

			map.put("id", Hibernate.LONG);
			map.put("userId", Hibernate.LONG);
			map.put("friendCell", Hibernate.STRING);
			map.put("ctime", Hibernate.TIMESTAMP);
			map.put("platId", Hibernate.INTEGER);
			map.put("friendType", Hibernate.INTEGER);
			map.put("remark", Hibernate.STRING);
			map.put("friendName", Hibernate.STRING);
			map.put("trueName", Hibernate.STRING);
			map.put("cellPhone", Hibernate.STRING);
			page.setRowCount(rowCount.longValue());
			sbSql.append(sb);
			sbSql.append(" order by f.id desc ");
			
			List<InviteFriendsBean> list = this.getBaseDao().search(
					sbSql.toString(), map, InviteFriendsBean.class,
					listObject.toArray(), page.getCurrentPage(),
					page.getPageSize());

			return list;
		}

		return null;
	}

	@Override
	public List<InviteFriendsBean> getFriendById(Long id) throws Exception {
   		String sql = "SELECT f.friend_cell friendCell,u.cell_phone cellPhone FROM tyt_invite_friends f LEFT JOIN tyt_user u ON f.user_id=u.id where f.id = :id";
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("cellPhone", Hibernate.STRING);
		map.put("friendCell", Hibernate.STRING);
		Map<String,Object> paramMap = new HashMap<String,Object>();
		paramMap.put("id",id);
		List<InviteFriendsBean> search = this.getBaseDao().search(sql, map, InviteFriendsBean.class, paramMap);
		return search;
	}


}
