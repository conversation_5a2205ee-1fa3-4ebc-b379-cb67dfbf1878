package com.tyt.invitefriends.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.tyt.invitefriends.service.InviteFriendsService;
import com.tyt.model.ResultMsgBean;
import com.tyt.web.base.BaseController;

@Controller
@RequestMapping("/inviteFriends")
public class InviteFriendsController extends BaseController{
	
	@Resource(name="inviteFriendsService")
	InviteFriendsService inviteFriendsService;

	@RequestMapping(value="/save")  
    @ResponseBody  
	public void save(Integer platId,Long userId,String token,String friends,
			HttpServletRequest request,HttpServletResponse response){
		ResultMsgBean msgBean=null;
		StringBuffer  condition=new StringBuffer();
		condition.append("condition[");
		condition.append("userId:").append(userId);
		condition.append(" platId:").append(platId);
		condition.append(" token:").append(token);
		condition.append(" friends:").append(friends);
		condition.append("]");
		try {
			if(userId==null){
				logger.info("inviteFriends userId is null."+condition);
				msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"userId is null");
				printJSON(request, response, msgBean);
				return;
			}
			if(platId==null){
				logger.info("inviteFriends platId is null."+condition);
				msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"platId is null");
				printJSON(request, response, msgBean);
				return;
			}
			if (!validateToken(userId+"",token, request, response)) {
				logger.info("inviteFriends token error."+condition);
				return;
			}
			
			inviteFriendsService.saveInviteFriends(userId,friends,platId);
			logger.info("inviteFriends save ok.");
			msgBean = new ResultMsgBean(ResultMsgBean.OK,"1");
			printJSON(request, response, msgBean);
			return;
		} catch (Exception e) {
			logger.info("inviteFriends Exception."+e.toString()+condition);
			msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"Exception");
			printJSON(request, response, msgBean);
			return;
		}
	}
}
