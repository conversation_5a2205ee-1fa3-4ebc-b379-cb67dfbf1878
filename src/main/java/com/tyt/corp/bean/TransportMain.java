package com.tyt.corp.bean;

import java.util.Date;

public class TransportMain {

	 private Long id;
	    /**
	     * 货物内容
	     */
	    private String taskContent;

	    /**
	     * 采集时间
	     */
	    private Date ctime;

	    /**
	     * 重量单位吨
	     */
	    private String weight;

	    /**
	     * 货物长单位米
	     */
	    private String length;

	    /**
	     * 货物宽单位米
	     */
	    private String wide;

	    /**
	     * 货物高单位米
	     */
	    private String high;

	    /**
	     * 车辆类型-长度 后台配置，单位米
	     */
	    private String carTypeLength;

	    /**
	     * 车辆特殊要求，满地爬，爬梯板等
	     */
	    private String carSpecialRequire;

	    /**
	     * 装卸方式  一装一卸
	     */
	    private String handingMode;

	    /**
	     * 备注
	     */
	    private String remark;
	//以上历史货源查询字段

	    /**
	     * 出发地 包含省市县
	     */
	    private String startPoint;

	    /**
	     * 目的地 包含省市县
	     */
	    private String destPoint;

	    /**
	     * 出发地详细地址
	     */
	    private String startDetailAdd;

	    /**
	     * 目的地详细地址
	     */
	    private String destDetailAdd;

	    /**
	     * 装车时间
	     */
	    private Date loadingTime;

	    /**
	     * 卸车时间
	     */
	    private Date unloadTime;

	    /**
	     * 装车人姓名
	     */
	    private String loadUserName;

	    /**
	     * 装车人电话
	     */
	    private String loadUserPhone;

	    /**
	     * 卸车人姓名
	     */
	    private String unloadUserName;

	    /**
	     * 卸车人电话
	     */
	    private String unloadUserPhone;
	    public Long getId() {
	        return id;
	    }

	    public void setId(Long id) {
	        this.id = id;
	    }

	    public String getTaskContent() {
	        return taskContent;
	    }

	    public void setTaskContent(String taskContent) {
	        this.taskContent = taskContent;
	    }

	    public Date getCtime() {
	        return ctime;
	    }

	    public void setCtime(Date ctime) {
	        this.ctime = ctime;
	    }

	    public String getWeight() {
	        return weight;
	    }

	    public void setWeight(String weight) {
	        this.weight = weight;
	    }

	    public String getLength() {
	        return length;
	    }

	    public void setLength(String length) {
	        this.length = length;
	    }

	    public String getWide() {
	        return wide;
	    }

	    public void setWide(String wide) {
	        this.wide = wide;
	    }

	    public String getHigh() {
	        return high;
	    }

	    public void setHigh(String high) {
	        this.high = high;
	    }

	    public String getCarTypeLength() {
	        return carTypeLength;
	    }

	    public void setCarTypeLength(String carTypeLength) {
	        this.carTypeLength = carTypeLength;
	    }

	    public String getCarSpecialRequire() {
	        return carSpecialRequire;
	    }

	    public void setCarSpecialRequire(String carSpecialRequire) {
	        this.carSpecialRequire = carSpecialRequire;
	    }

	    public String getHandingMode() {
	        return handingMode;
	    }

	    public void setHandingMode(String handingMode) {
	        this.handingMode = handingMode;
	    }

	    public String getRemark() {
	        return remark;
	    }

	    public void setRemark(String remark) {
	        this.remark = remark;
	    }

	    public String getStartPoint() {
	        return startPoint;
	    }

	    public void setStartPoint(String startPoint) {
	        this.startPoint = startPoint;
	    }

	    public String getDestPoint() {
	        return destPoint;
	    }

	    public void setDestPoint(String destPoint) {
	        this.destPoint = destPoint;
	    }

	    public Date getLoadingTime() {
	        return loadingTime;
	    }

	    public void setLoadingTime(Date loadingTime) {
	        this.loadingTime = loadingTime;
	    }

	    public Date getUnloadTime() {
	        return unloadTime;
	    }

	    public void setUnloadTime(Date unloadTime) {
	        this.unloadTime = unloadTime;
	    }

	    public String getStartDetailAdd() {
	        return startDetailAdd;
	    }

	    public void setStartDetailAdd(String startDetailAdd) {
	        this.startDetailAdd = startDetailAdd;
	    }

	    public String getDestDetailAdd() {
	        return destDetailAdd;
	    }

	    public void setDestDetailAdd(String destDetailAdd) {
	        this.destDetailAdd = destDetailAdd;
	    }

	    public String getLoadUserName() {
	        return loadUserName;
	    }

	    public void setLoadUserName(String loadUserName) {
	        this.loadUserName = loadUserName;
	    }

	    public String getLoadUserPhone() {
	        return loadUserPhone;
	    }

	    public void setLoadUserPhone(String loadUserPhone) {
	        this.loadUserPhone = loadUserPhone;
	    }

	    public String getUnloadUserName() {
	        return unloadUserName;
	    }

	    public void setUnloadUserName(String unloadUserName) {
	        this.unloadUserName = unloadUserName;
	    }

	    public String getUnloadUserPhone() {
	        return unloadUserPhone;
	    }

	    public void setUnloadUserPhone(String unloadUserPhone) {
	        this.unloadUserPhone = unloadUserPhone;
	    }
}
