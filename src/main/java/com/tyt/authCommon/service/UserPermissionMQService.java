package com.tyt.authCommon.service;

import com.tyt.authCommon.bean.ExposureRecordInfo;
import com.tyt.model.TytUserBuyGoods;

import java.util.List;

/**
 * @Description  用户权益分配的MQ消息
 * <AUTHOR>
 * @Date  2019/7/11 14:29
 * @Param 
 * @return 
 **/
public interface UserPermissionMQService {

    /**
     * @Description  发送用户权益分配的MQ消息
     * <AUTHOR>
     * @Date  2019/7/11 14:30
     * @Param [userId, goodsId, changeType]
     * @return void
     **/
    public void sendUserPermission2MQ(Long userId, Long goodsId, Integer changeType, TytUserBuyGoods userBuyGoods, ExposureRecordInfo exposureRecordInfo);

     /**
      * @Description  改变用户权益发送的MQ消息(目前只用于商品订单退款,后续可以将上面的方法进行迁移改造)
      * <AUTHOR>
      * @Date  2019/10/16 14:39
      * @Param [userId, goodsId, changeType, messageType，userBuyGoods]
      * @return void
      **/
    public void changeUserPermission2MQ(Long userId, Long goodsId, Integer changeType,String ordNum,Integer messageType,
                                        TytUserBuyGoods userBuyGoods);

    /**
     * 改变用户权益发送的MQ消息
     * @param userId
     * @param changeType
     * @param messageType
     * @param userBuyGoods
     */
    void changeUserPermissionList2Mq(Long userId, Integer changeType, Integer messageType, List<TytUserBuyGoods> userBuyGoods);
}
