package com.tyt.authCommon.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.model.TytUserBuyGoods;
import lombok.Data;

import java.util.List;

/**
 * @Description  分配用户权益mq消息
 * <AUTHOR>
 * @Date  2019/7/2 15:34
 * @Param
 * @return
 **/
@Data
public class MqUserPermissionListMsg extends MqBaseMessageBean {

    //用户ID
    private Long userId;

    /**
     * 变更类型(1.购买 2.赠送 3.次卡到期 4.时间到期 5.扣减 )
     */
    private Integer changeType;

    /**
     * 商品订单信息
     */
    private List<TytUserBuyGoods> userBuyGoodsList;
}
