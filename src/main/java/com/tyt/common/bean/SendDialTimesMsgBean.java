package com.tyt.common.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

public class SendDialTimesMsgBean extends MqBaseMessageBean {

    /**
     * 接收用户id
     */
    @Getter
    @Setter
    private Long userId;
    /**
     * 接收用户车方等级
     */
    @Getter
    @Setter
    private Integer userLevel = -1;
    /**
     * 发送类型
     */
    @Getter
    @Setter
    private DialTimesSendType sendType;
    /**
     * 手动发放详情 sendType 为 MANUAL_SEND 时需要
     */
    @Getter
    @Setter
    private ManualSendDetail manualSendDetail;

    @Data
    public static class ManualSendDetail {
        /**
         * 发送数量
         */
        private Integer sendCount;
        /**
         * 发放描述
         */
        private String sendInfoDesc;
        /**
         * 有效期（天）
         */
        private Integer expireDays;
        /**
         * 操作人ID
         */
        private Long operateUserId;
        /**
         * 捂货配置Id
         */
        private Long dailConfigId;
        /**
         * 操作人姓名
         */
        private String operateUserName;
    }

    @Getter
    public enum DialTimesSendType {
        /**
         * 手动发放
         */
        MANUAL_SEND("手动发放"),
        /**
         * 用户等级发放
         */
        CAR_USER_LEVEL("车主等级"),
        /**
         * 履约发放
         */
        ORDER("履约"),
        /**
         * 认证车辆发放
         */
        CAR_VERIFY("认证车辆"),
        /**
         * 认证司机发放
         */
        DRIVER_VERIFY("认证司机"),
        /**
         * 认证企业发放
         */
        ENTERPRISE_VERIFY("认证企业"),
        /**
         * 认证车主发放
         */
        OWNER_VERIFY("认证车主"),
        ;

        public final String sendInfoDesc;

        DialTimesSendType(String sendInfoDesc) {
            this.sendInfoDesc = sendInfoDesc;
        }
    }
}
