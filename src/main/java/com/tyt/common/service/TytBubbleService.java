package com.tyt.common.service;


import com.tyt.model.TytBubble;
import com.tyt.service.base.BaseService;

public interface TytBubbleService extends BaseService<TytBubble,Long> {
	/**
	 * 修改用户指定气泡的值 number是为0时清0为-1时减1，1时加1
	 * @param userId
	 * @param type1,具体内容请查看《APP接口文档》-气泡数量类型说明
	 * @param type2
	 * @param number
	 * @return
	 */
	public int  updateBubbleNumber(Long userId,String type1,String type2,int number);
		
}
