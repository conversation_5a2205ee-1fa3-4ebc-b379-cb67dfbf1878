package com.tyt.common.service;

import java.util.List;

import com.tyt.model.PageBean;
import com.tyt.model.TransportBackend;
import com.tyt.model.TytCarDriverArchives;
import com.tyt.model.TytMqMessage;
import com.tyt.service.base.BaseService;
import com.tyt.web.back.internal.bean.ASRTaskMsg;
import com.tyt.web.back.internal.bean.MqMessageBean;

public interface TytMqMessageService extends BaseService<TytMqMessage, Integer> {
	/**
	 * 保存发送Mq
	 * 
	 * @param serialNum
	 *            消息序列号
	 * @param messageContent
	 *            消息内容
	 * @param messageTypeInfofee
	 *            消息类别
	 */
	public void addMqMessage(String serialNum, String messageContent, int messageTypeInfofee);

	public void sendMqMessage(String serialNum, String messageContent);

	public void sendMsgToGoodsSource(String serialNum, String messageContent);

    void sendMsgToMarketActivity(String serialNum, String messageContent, String tag);

    /**
	 * 查询mq列表
	 * @param pageBean
	 * @param mqMessage
	 * @return
	 */

	public List<MqMessageBean> getMqMessageList(PageBean pageBean,MqMessageBean mqMessage);

	/**
	 * 货站接单发送mq
	 * @param transportBackend
	 * @param userIds
	 */
	public void saveAndSendTransportNewsMq(TransportBackend transportBackend, List<Long> userIds) throws Exception;

	/**
	 * 发送短信的mq
	 * @param cellPhone
	 * @param archives
	 */
	void addSendSms(String cellPhone, TytCarDriverArchives archives, String loseStr,String platNumber);

	/**
	 * 发送退运费MQ
	 * @param orderId 订单ID
	 */
	void sendMsgToRefundFreight(Long orderId);

	ASRTaskMsg addAndSendASRTaskdMqMessageDistribute(String callId, Long srcMsgId, Long carUserId);
}
