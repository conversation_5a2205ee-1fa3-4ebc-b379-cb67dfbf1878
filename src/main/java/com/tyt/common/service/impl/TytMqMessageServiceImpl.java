package com.tyt.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.common.bean.ShortMsgBean;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.dao.base.BaseDao;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.invoice.bean.FreightOperateMessage;
import com.tyt.message.mqservice.IMqProducer;
import com.tyt.model.*;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.user.UserService;
import com.tyt.transportnews.bean.TransportNewsMessageBean;
import com.tyt.util.EmptyUtil;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import com.tyt.web.back.internal.bean.ASRTaskMsg;
import com.tyt.web.back.internal.bean.MqMessageBean;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

@Service("tytMqMessageService")
public class TytMqMessageServiceImpl extends BaseServiceImpl<TytMqMessage, Integer> implements TytMqMessageService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "tytMqMessageDao")
	public void setBaseDao(BaseDao<TytMqMessage, Integer> TytMqMessageDao) {
		super.setBaseDao(TytMqMessageDao);
	}

	@Autowired
	private IMqProducer producer;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytConfigService")
	private TytConfigService configService;

	@Override
	public void addMqMessage(String serialNum, String messageContent, int messageType) {
		String sql = "INSERT INTO `tyt`.`tyt_mq_message` ( `message_serial_num`, `message_content`, `create_time`, `message_type`) VALUES (?, ?, ?, ?)";
		Object[] params = new Object[] { serialNum, messageContent, new Date(), messageType };
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void sendMqMessage(String serialNum, String messageContent) {
		producer.sendMsg(messageContent, null, serialNum, null);
	}

	@Override
	public void sendMsgToGoodsSource(String serialNum, String messageContent) {
		producer.sendMsgToGoodsSource(messageContent,serialNum);
	}

	@Override
	public void sendMsgToMarketActivity(String serialNum, String messageContent, String tag) {
		producer.sendMarketActivityMsg(messageContent, serialNum, tag);
	}

	@Override
	public List<MqMessageBean> getMqMessageList(PageBean pageBean,
			MqMessageBean mqMessage) {
		
		StringBuffer countBuffer = new StringBuffer("SELECT count(*) FROM tyt.tyt_mq_message  f where 1=1");
		StringBuffer conditionBuffer = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		if (EmptyUtil.objectIsNotEmpty(mqMessage.getMessageSerialNum())) {
			conditionBuffer.append(" and f.message_serial_num=?");
			params.add(mqMessage.getMessageSerialNum());
		}
		if (EmptyUtil.objectIsNotEmpty(mqMessage.getDealStatus())) {
			conditionBuffer.append(" and f.deal_status=?");
			params.add(mqMessage.getDealStatus());
		}
		
		if (EmptyUtil.objectIsNotEmpty(mqMessage.getMessageType())) {
			conditionBuffer.append(" and f.message_type=?");
			params.add(mqMessage.getMessageType());
		}
		if (EmptyUtil.objectIsNotEmpty(mqMessage.getBeginTime())) {
			String beginTime = TimeUtil.formatDate(mqMessage.getBeginTime());
			conditionBuffer.append(" and f.create_time>=?");
			params.add(beginTime);
		}
		if (EmptyUtil.objectIsNotEmpty(mqMessage.getEndTime())) {
			String endTime = TimeUtil.formatDate(mqMessage.getEndTime());
			conditionBuffer.append(" and f.create_time<=?");
			params.add(endTime);
		}
		//默认查询处理三次的数据
		if (EmptyUtil.objectIsNotEmpty(mqMessage.getSendNbr())) {
			conditionBuffer.append(" and f.send_nbr=?");
			params.add(mqMessage.getSendNbr());
		}
//		else{
//			if(!(params.size()>0)){
//				conditionBuffer.append(" and f.send_nbr=?");
//				mqMessage.setSendNbr(3);
//				params.add(3);
//			}
//		}
		BigInteger count = this.getBaseDao().query(countBuffer.append(conditionBuffer).toString(), params.toArray());
		if (count == null || count.longValue() <= 0) {
			return null;// 没有记录，直接返回null
		}
		// 查询记录详情
		int currentPage = pageBean.getCurrentPage();// 当前页数
		int pageSize = pageBean.getPageSize();// 页面的大小,默认为一页显示5条数据
		if (currentPage <= 0) {
			currentPage = 1;
		}
		if (pageSize <= 0) {
			pageSize = 50;
		}
		pageBean.setRowCount(count.longValue());
		// 查询记录详情
		StringBuffer selectBuffer = new StringBuffer("select id,message_content messageContent,message_serial_num messageSerialNum,message_type messageType, deal_status dealStatus,create_time createTime,update_time updateTime,send_nbr sendNbr " 
				+" FROM tyt.tyt_mq_message f where 1=1");
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("messageContent", Hibernate.STRING);
		scalarMap.put("messageSerialNum", Hibernate.STRING);
		scalarMap.put("messageType", Hibernate.INTEGER);
		scalarMap.put("dealStatus", Hibernate.INTEGER);
		scalarMap.put("createTime", Hibernate.TIMESTAMP);
		scalarMap.put("updateTime", Hibernate.TIMESTAMP);
		scalarMap.put("sendNbr", Hibernate.INTEGER);
		return this.getBaseDao().search(selectBuffer.append(conditionBuffer).append(" order by f.id desc").toString(), scalarMap, MqMessageBean.class, params.toArray(), currentPage, pageSize);
	}

	/**
	 * 货站接单
	 * @param transportBackend
	 * @param userIds
	 */
	@Override
	public void saveAndSendTransportNewsMq(TransportBackend transportBackend,List<Long> userIds) throws Exception {
		if (userIds!=null&&userIds.size()>0){
			for (Long userId : userIds) {
				//缓存中获取user
				User user = userService.getByUserId(userId);
				TransportNewsMessageBean transportNewsMessageBean = new TransportNewsMessageBean();
				//目的地
				transportNewsMessageBean.setDestPoint(transportBackend.getDestPoint());
				//起始地
				transportNewsMessageBean.setStartPoint(transportBackend.getStartPoint());
				//接单id
				transportNewsMessageBean.setUserId(userId);
				//1待接单通知 2下架通知
				if (transportBackend.getStatus().equals(1)) {
					transportNewsMessageBean.setOpStatus(1);
				} else if (transportBackend.getStatus().equals(2)) {
					transportNewsMessageBean.setOpStatus(2);
				}
				//transportBackend表id
				if (transportBackend.getId()!=null){
					transportNewsMessageBean.setBackendId(transportBackend.getId());
				}
				//后太维护人id
				transportNewsMessageBean.setEmployeeUserId(transportBackend.getUserId());
				//货物内容
				transportNewsMessageBean.setTaskContent(transportBackend.getTaskContent());
				//接单人电话
				transportNewsMessageBean.setCellPhone(user.getCellPhone());
				//接单人真是姓名
				transportNewsMessageBean.setTrueName(user.getTrueName());
				transportNewsMessageBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
				transportNewsMessageBean.setMessageType(MqBaseMessageBean.TRANSPORT_NEWS_ORDER_RECEIVING);
				//保存发送的信息---tyt_mq_message
				addMqMessage(transportNewsMessageBean.getMessageSerailNum(), JSON.toJSONString(transportNewsMessageBean),transportNewsMessageBean.getMessageType());
				//发送mq
				sendMqMessage(transportNewsMessageBean.getMessageSerailNum(), JSON.toJSONString(transportNewsMessageBean));
				logger.info("transportNewsMessageBean======="+JSON.toJSONString(transportNewsMessageBean));
			}
		}

	}
	@Override
	public void addSendSms(String cellPhone, TytCarDriverArchives archives, String loseStr,String platNumber) {
		String content = null;
		String tmpl ="";
		if (cellPhone!=null) {
			if (archives!=null){
				if (archives.getExamineStatus() == 2) {//成功
					tmpl ="【特运通车主版】司机信息审核通过(${driverShowName} ${driverPhone})。";
					content = StringUtils.replaceEach(tmpl, new String[] { "${driverShowName}","${driverPhone}"}, new String[] {archives.getDriverShowName(),archives.getDriverPhone() });
				} else if (archives.getExamineStatus() == 3) {//失败
					tmpl ="【特运通车主版】司机信息审核失败(${driverShowName} ${driverPhone})。${loseStr}。请进入特运通车主版，在“我的-司机管理”中进行修改。";
					content = StringUtils.replaceEach(tmpl, new String[] { "${driverShowName}","${driverPhone}","${loseStr}"}, new String[] {archives.getDriverShowName(),archives.getDriverPhone(),loseStr});
				}
			}else {
				tmpl="尊敬用户您好，您车辆${numberPlates}已成功加入特运通优选车，平台将向您推送更多的优选车好货，您可以关注车方APP准备随时接单了~";
				content = StringUtils.replaceEach(tmpl, new String[] { "${numberPlates}"}, new String[] {platNumber});
			}
			ShortMsgBean shortMsgBean = new ShortMsgBean();
			shortMsgBean.setContent(content);
			shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			String messageSerailNum = SerialNumUtil.generateSeriaNum();
			shortMsgBean.setMessageSerailNum(messageSerailNum);
			shortMsgBean.setCell_phone(cellPhone);
			shortMsgBean.setRemark("");
			addMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			sendMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean));
		}
	}

	@Override
	public void sendMsgToRefundFreight(Long orderId) {
		//发送退还运费MQ
		try {
			FreightOperateMessage freightOperateMessage = new FreightOperateMessage();
			freightOperateMessage.setMessageType(com.tyt.infofee.bean.MqBaseMessageBean.ORDERS_FREIGHT_OPERATE_CODE);
			freightOperateMessage.setOrderId(orderId);
			freightOperateMessage.setOperateType(FreightOperateMessage.OPERATE_TYPE_REFUND_FREIGHT);
			freightOperateMessage.setRefundReason("转普通单");
			freightOperateMessage.setMessageSerailNum(UUID.randomUUID() + "-manage");

			addMqMessage(freightOperateMessage.getMessageSerailNum(),JSON.toJSONString(freightOperateMessage), com.tyt.infofee.bean.MqBaseMessageBean.ORDERS_FREIGHT_OPERATE_CODE);
			sendMqMessage(freightOperateMessage.getMessageSerailNum(), JSON.toJSONString(freightOperateMessage));
		} catch (Exception e) {
			logger.error("send refund freight error:", e);
		}
	}

	@Override
	public ASRTaskMsg addAndSendASRTaskdMqMessageDistribute(String callId, Long srcMsgId, Long carUserId) {
		ASRTaskMsg asrTaskMsg = new ASRTaskMsg();
		String serialNum = SerialNumUtil.generateSeriaNum();
		asrTaskMsg.setMessageSerailNum(serialNum);
		asrTaskMsg.setMessageType(MqBaseMessageBean.ASR_TASK_CREATE_MESSAGE_DISTRIBUTE);
		asrTaskMsg.setCallId(callId);
		asrTaskMsg.setSrcMsgId(srcMsgId);
		asrTaskMsg.setCarUserId(carUserId);
		addMqMessage(serialNum,JSON.toJSONString(asrTaskMsg), MqBaseMessageBean.ASR_TASK_CREATE_MESSAGE_DISTRIBUTE);
		sendMqMessage(serialNum, JSON.toJSONString(asrTaskMsg));
		return asrTaskMsg;
	}
}
