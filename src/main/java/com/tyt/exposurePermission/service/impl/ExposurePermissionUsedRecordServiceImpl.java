package com.tyt.exposurePermission.service.impl;

import com.tyt.exposurePermission.bean.ExposurePermissionQueryBean;
import com.tyt.exposurePermission.bean.ExposurePermissionRecordBean;
import com.tyt.exposurePermission.service.ExposurePermissionUsedRecordService;
import com.tyt.exposurePermission.vo.ExposurePermissionRecordVo;
import com.tyt.manager.entity.base.ExposurePermissionUsedRecord;
import com.tyt.manager.mapper.base.ExposurePermissionUsedRecordMapper;
import com.tyt.model.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/25 10:16
 * @Version 1.0
 **/
@Service
public class ExposurePermissionUsedRecordServiceImpl implements ExposurePermissionUsedRecordService {

    @Autowired
    ExposurePermissionUsedRecordMapper exposurePermissionUsedRecordMapper;

    @Override
    public List<ExposurePermissionUsedRecord> getExposurePermissionList(ExposurePermissionQueryBean exposurePermissionQueryBean, PageBean pageBean) {
        Example odExa = new Example(ExposurePermissionUsedRecord.class);
        odExa.and().andEqualTo("srcMsgId", exposurePermissionQueryBean.getSrcMsgId());
        int total = exposurePermissionUsedRecordMapper.selectCountByExample(odExa);
        pageBean.setRowCount(total);
        if(total>0){
            int pageNumber = (exposurePermissionQueryBean.getPageNumber()-1)* exposurePermissionQueryBean.getPageSize();
            List<ExposurePermissionUsedRecord> exposurePermissionUsedRecords =exposurePermissionUsedRecordMapper.getExposurePermissionList(
                    exposurePermissionQueryBean.getSrcMsgId(),pageNumber,exposurePermissionQueryBean.getPageSize());
            return exposurePermissionUsedRecords;
        }
        return new ArrayList<>();
    }

    @Override
    public List<ExposurePermissionRecordVo> getExposurePermissionUsedRecord(ExposurePermissionRecordBean exposurePermissionRecordBean, PageBean pageBean) {
        Example odExa = new Example(ExposurePermissionUsedRecord.class);
        odExa.and().andEqualTo("userId", exposurePermissionRecordBean.getUserId())
        .andGreaterThanOrEqualTo("ctime",exposurePermissionRecordBean.getStartTime())
        .andLessThanOrEqualTo("ctime",exposurePermissionRecordBean.getEndTime());
        int total = exposurePermissionUsedRecordMapper.selectCountByExample(odExa);
        pageBean.setRowCount(total);
        if(total>0){
            int pageNumber = (exposurePermissionRecordBean.getPageNumber()-1)* exposurePermissionRecordBean.getPageSize();
            List<ExposurePermissionRecordVo> exposurePermissionUsedRecords =exposurePermissionUsedRecordMapper.getExposurePermissionUsedRecord(
                    exposurePermissionRecordBean.getUserId(),exposurePermissionRecordBean.getStartTime(),exposurePermissionRecordBean.getEndTime(),pageNumber,exposurePermissionRecordBean.getPageSize());
            return exposurePermissionUsedRecords;
        }
        return new ArrayList<>();
    }
}
