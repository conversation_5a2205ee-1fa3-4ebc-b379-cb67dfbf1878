package com.tyt.exposurePermission.service;

import com.tyt.exposurePermission.bean.ExposurePermissionQueryBean;
import com.tyt.exposurePermission.bean.ExposurePermissionRecordBean;
import com.tyt.exposurePermission.vo.ExposurePermissionRecordVo;
import com.tyt.manager.entity.base.ExposurePermissionUsedRecord;
import com.tyt.model.PageBean;

import java.util.List;

public interface ExposurePermissionUsedRecordService {

    List<ExposurePermissionUsedRecord> getExposurePermissionList(ExposurePermissionQueryBean exposurePermissionQueryBean, PageBean pageBean);

    List<ExposurePermissionRecordVo> getExposurePermissionUsedRecord(ExposurePermissionRecordBean exposurePermissionRecordBean, PageBean pageBean);
}
