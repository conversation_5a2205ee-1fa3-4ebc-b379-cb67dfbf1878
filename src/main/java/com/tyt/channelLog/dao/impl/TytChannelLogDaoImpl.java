package com.tyt.channelLog.dao.impl;

import com.tyt.channelLog.dao.TytChannelLogDao;
import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytChannelLog;
import org.springframework.stereotype.Repository;


/**
 * @Description  用户登录信息数据层实现类
 * <AUTHOR>
 * @Date  2019/5/21 15:50
 * @Param
 * @return
 **/
@Repository("tytChannelLogDao")
public class TytChannelLogDaoImpl extends BaseDaoImpl<TytChannelLog, Long> implements TytChannelLogDao {

    public TytChannelLogDaoImpl() {
        this.setEntityClass(TytChannelLog.class);
    }

}
