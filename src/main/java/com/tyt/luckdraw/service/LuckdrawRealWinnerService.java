package com.tyt.luckdraw.service;

import com.tyt.luckdraw.bean.RealWinnerListBean;
import com.tyt.model.LuckdrawRealWinner;
import com.tyt.model.PageBean;
import com.tyt.service.base.BaseService;

import java.util.List;

public interface LuckdrawRealWinnerService extends BaseService<LuckdrawRealWinner,Long> {

    /**
     * @Description 获取真实中奖用户名单列表的方法
     * <AUTHOR>
     * @Date  2020/3/11 10:35
     * @Param [realWinnerListBean, pageBean]
     * @return java.util.List<com.tyt.model.LuckdrawRealWinner>
     **/
   public List<LuckdrawRealWinner> getRealWinnerList(RealWinnerListBean realWinnerListBean, PageBean pageBean);

   /**
    * @Description  csv文件头部数据与列表数据拼接的方法
    * <AUTHOR>
    * @Date  2020/3/11 11:22
    * @Param [content, realWinnerList]
    * @return void
    **/
   public void realWinnerListToCvsString(StringBuilder content,List<LuckdrawRealWinner> realWinnerList);
}
