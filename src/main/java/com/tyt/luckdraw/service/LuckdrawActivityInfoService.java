package com.tyt.luckdraw.service;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.luckdraw.bean.ActivityInfoListBean;
import com.tyt.model.LuckdrawActivityInfo;
import com.tyt.model.PageBean;
import com.tyt.service.base.BaseService;

import java.util.List;

public interface LuckdrawActivityInfoService extends BaseService<LuckdrawActivityInfo,Integer> {

    /**
     * 抽奖活动列表
     * @param activityName  活动名称（条件参数）
     * @param pageBean  分页
     * @return  list
     */
    List<ActivityInfoListBean> getActivityInfoList(String activityName, PageBean pageBean);

    /**
     * 保存活动信息
     * @param activity  保存内容
     * @param curUser  登录人信息
     */
    void saveActivityInfo(LuckdrawActivityInfo activity, EmployeeQueryBean curUser);
}
