package com.tyt.maintainer.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;
import com.tyt.dao.base.BaseDao;
import com.tyt.maintainer.bean.MaintainDetailBean;
import com.tyt.maintainer.bean.MaintainerQueryBean;
import com.tyt.maintainer.bean.MaintainerResultBean;
import com.tyt.model.TytSource;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;
import com.tyt.web.back.internal.bean.SourceBean;

@Service(value = "maintainerService")
public class MaintainerServiceImpl extends BaseServiceImpl<TytSource, Long> implements MaintainerService {
	@Resource(name = "maintainerDao")
	public void setBaseDao(BaseDao<TytSource, Long> maintainerDao) {
		super.setBaseDao(maintainerDao);
	}
	@Resource(name = "userService")
	private UserService userService;
	
	@SuppressWarnings("deprecation")
	@Override
	public List<MaintainerResultBean> getAllMaintainers(MaintainerQueryBean queryBean) throws Exception {
		List<Object> listArr = new ArrayList<Object>();
		StringBuffer sql = new StringBuffer("SELECT tms.`id`, tms.`cell_phone` AS 'cellPhone', tms.`status`, tms.`name`, tms.`last_online_time` AS 'lastOnlineTime', tms.`enabled` FROM tyt_mechant_service tms WHERE 1=1");
		if (queryBean.getCellPhone() != null && !"".equals(queryBean.getCellPhone())) {
			//通过手机号查询时改为该手机号对应的用户id查询
			Long userId = userService.getIdByCellPhone(queryBean.getCellPhone());
			if(userId!=null) {
				sql.append(" AND tms.user_id=? ");
				listArr.add(userId);
			}else {
				return null;
			}
		}
		if (queryBean.getStatus() != null && !"".equals(queryBean.getStatus()) && !"-1".equals(queryBean.getStatus())) {
			sql.append(" AND tms.`status`=?");
			listArr.add(queryBean.getStatus());
		}
		if (queryBean.getName() != null && !"".equals(queryBean.getName())) {
			sql.append(" AND tms.`name` LIKE ?");
			listArr.add("%" + queryBean.getName() + "%");
		}
		if (queryBean.getSkillValue() != null && !"".equals(queryBean.getSkillValue()) && !"allSkills".equals(queryBean.getSkillValue())) {
			sql.append(" AND EXISTS (SELECT id FROM tyt_merchant_service_technical WHERE service_id = tms.id  AND device_type = ?)");
			listArr.add(queryBean.getSkillValue());
		}
		if (queryBean.getEnabled() != null && !"".equals(queryBean.getEnabled()) && !"-1".equals(queryBean.getEnabled())) {
			sql.append(" AND tms.`enabled`=?");
			listArr.add(queryBean.getEnabled());
		}
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("cellPhone", Hibernate.STRING);
		scalarMap.put("status", Hibernate.INTEGER);
		scalarMap.put("name", Hibernate.STRING);
		scalarMap.put("lastOnlineTime", Hibernate.STRING);
		scalarMap.put("enabled", Hibernate.INTEGER);
		// 查询所有，保留分页相关逻辑，便于之后需要支持分页的时候使用
		List<MaintainerResultBean> maintainerResultBeans = this.getBaseDao().search(sql.toString(), scalarMap, MaintainerResultBean.class, listArr.toArray());
		/**
		 * 获取维修商的技能
		 */
		Iterator<MaintainerResultBean> iterator = maintainerResultBeans.iterator();
		MaintainerResultBean resultBean;
		int serviceId;
		String skillSql = "SELECT ts.`name` FROM tyt_source ts WHERE ts.`group_code` = 'merchant_service_kind' AND ts.`value` IN (SELECT tyst.`device_type` FROM tyt_merchant_service_technical tyst WHERE tyst.`service_id`=?)";
		scalarMap.clear();
		scalarMap.put("name", Hibernate.STRING);
		StringBuffer skills = new StringBuffer();
		while (iterator.hasNext()) {
			listArr.clear();
			resultBean = iterator.next();
			serviceId = resultBean.getId();
			listArr.add(serviceId);
			List<SourceBean> maintainSkills = this.getBaseDao().search(skillSql, scalarMap, SourceBean.class, listArr.toArray());
			if (maintainSkills != null && maintainSkills.size() > 0) {
				for (int i = 0; i < maintainSkills.size(); i++) {
					skills.append(maintainSkills.get(i).getName() + ",");
				}
				resultBean.setSkills(skills.toString().substring(0, skills.toString().length() - 1));
				skills.delete(0, skills.length());
			}
		}
		return maintainerResultBeans;
	}

	@SuppressWarnings("deprecation")
	@Override
	public MaintainDetailBean getMerchantById(String id) {
		MaintainDetailBean detailBean = null;
		String sql = "SELECT tms.`id`, tms.`remark`, tms.`cell_phone` AS 'cellPhone', tms.`status`, tms.`name`, tms.`last_online_time` AS 'lastOnlineTime', tms.`enabled`, tms.head_image_url AS 'avatar', tms.tele_phones AS 'telePhones', tms.read_count AS 'readCount', tms.call_count AS 'callCount', tms.longitude, tms.latitude FROM tyt_mechant_service tms WHERE tms.id=?";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("cellPhone", Hibernate.STRING);
		scalarMap.put("status", Hibernate.INTEGER);
		scalarMap.put("name", Hibernate.STRING);
		scalarMap.put("lastOnlineTime", Hibernate.STRING);
		scalarMap.put("enabled", Hibernate.INTEGER);
		scalarMap.put("avatar", Hibernate.STRING);
		scalarMap.put("telePhones", Hibernate.STRING);
		scalarMap.put("readCount", Hibernate.INTEGER);
		scalarMap.put("callCount", Hibernate.INTEGER);
		scalarMap.put("longitude", Hibernate.STRING);
		scalarMap.put("latitude", Hibernate.STRING);
		scalarMap.put("remark", Hibernate.STRING);
		List<MaintainDetailBean> detailBeans = this.getBaseDao().search(sql, scalarMap, MaintainDetailBean.class, new Object[] { id });
		if (detailBeans.size() != 1) {
			return new MaintainDetailBean();
		} else {
			detailBean = detailBeans.get(0);
			String skillSql = "SELECT ts.`name` FROM tyt_source ts WHERE ts.`group_code` = 'merchant_service_kind' AND ts.`value` IN (SELECT tyst.`device_type` FROM tyt_merchant_service_technical tyst WHERE tyst.`service_id`=?)";
			scalarMap.clear();
			scalarMap.put("name", Hibernate.STRING);
			StringBuffer skills = new StringBuffer();
			List<SourceBean> maintainSkills = this.getBaseDao().search(skillSql, scalarMap, SourceBean.class, new String[] { detailBean.getId() + "" });
			if (maintainSkills != null && maintainSkills.size() > 0) {
				for (int i = 0; i < maintainSkills.size(); i++) {
					skills.append(maintainSkills.get(i).getName() + ",");
				}
				detailBean.setSkills(skills.toString().substring(0, skills.toString().length() - 1));
			}
		}
		return detailBean;
	}

	@Override
	public boolean updateMaintaint(String remark, String id, String enabled) {
		String sql = "UPDATE tyt_mechant_service SET remark = ?, enabled=? WHERE id=?";
		int count = this.getBaseDao().executeUpdateSql(sql, new Object[] { remark, enabled, id });
		return count == 1;
	}
}
