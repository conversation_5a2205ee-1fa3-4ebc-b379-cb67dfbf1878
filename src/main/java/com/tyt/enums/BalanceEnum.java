package com.tyt.enums;

public enum BalanceEnum {
    //余额类型，1: 钱包余额，2：积分，3：冻结资金, 4: 公司入账 5：公司出账 6：手续费 其他待定
    钱包余额(1),
    积分(2),
    冻结资金(3),
    公司入账(4),
    公司出帐(5),
    手续费(6);

    private int id;

    BalanceEnum(int id) {
        this.id = id;
    }

    private static BalanceEnum getBalanceEnumById(int id){
        for(BalanceEnum balanceEnum: BalanceEnum.values()){
            if(balanceEnum.id == id){
                return balanceEnum;
            }
        }
        return null;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


}
