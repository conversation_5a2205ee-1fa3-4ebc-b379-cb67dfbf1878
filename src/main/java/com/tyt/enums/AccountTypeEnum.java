package com.tyt.enums;

/**
 * 出入帐方式枚举
 *             <c:if test="${inOutAccount.accountType==3 }">data-type="1"</c:if>>
 *             <c:if test="${inOutAccount.accountType==1 }">信息费支付</c:if>
 *             <c:if test="${inOutAccount.accountType==2 }">退回信息费</c:if>
 *             <c:if test="${inOutAccount.accountType==3 }">提现</c:if>
 *             <c:if test="${inOutAccount.accountType==4 }">信息费手续费支付</c:if>
 *             <c:if test="${inOutAccount.accountType==5 }">支付会员费</c:if>
 */
public enum AccountTypeEnum {
    信息费支付(1), 信息费手续费支付(4);

    private int id;

    AccountTypeEnum(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
