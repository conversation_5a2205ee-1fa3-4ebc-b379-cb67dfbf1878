package com.tyt.enums;

/**
 * 沟通记录状态
 */
public enum ContactStatusEnum {
    //记录状态 1：未完成  2：已完成 3升级处理
    ING(1,"未完成"),
    COMPLETE(2, "已完成"),
    UPGRADE(3, "升级处理");

    private int id;
    private String desc;

    ContactStatusEnum(int id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
