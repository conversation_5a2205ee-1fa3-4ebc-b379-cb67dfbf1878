package com.tyt.peopleorders.bean;



import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.tyt.model.PageBean;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:57
 */

public class CsQuestionTypeBean implements Serializable {

	/**** 正确结果 **/
    public static final int OK = 200;

    /**** 错误结果 **/
    public static final int ERROR = 500;
    private static final long serialVersionUID = -2426253401122322138L;

    private int code=OK;
    private String msg;
    private PageBean data;
    private List<CsDictionary> listData;

	public CsQuestionTypeBean(){
	}
    public CsQuestionTypeBean(int code, String msg) {
        super();
        this.code = code;
        this.msg = msg;
    }
    public CsQuestionTypeBean(int code, String msg, PageBean data) {
		super();
		this.code = code;
		this.msg = msg;
		this.data = data;
	}
	public Integer getCode() {
        return code;
    }
    public void setCode(int code) {
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }


    /**
     * @return the data
     */
    public PageBean getData() {
        return data;
    }
    /**
     * @param data the data to set
     */
    public void setData(PageBean data) {
        this.data = data;
    }

    public List<CsDictionary> getListData() {
        return listData;
    }

    public void setListData(List<CsDictionary> listData) {
        this.listData = listData;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
