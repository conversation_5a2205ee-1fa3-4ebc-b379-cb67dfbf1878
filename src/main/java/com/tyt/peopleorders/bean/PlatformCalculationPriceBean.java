package com.tyt.peopleorders.bean;

/**
 * 人工派单运价--平台参考价格
 */
public class PlatformCalculationPriceBean {
    private String referenceDaily;//备注文字
    private String includingVatGuidance;//货主含税指导价
    private String includingVatGuidanceSingle;//含税指导价 每公里每顿价格
    private String lowestHighest;//货主含税最低至最高区间价
    private String lowestHighestSingle;//含税区间价 每公里每吨价格
    private String includingVatSvgDealPrice;//含税平均成交价
    private String includingVatSvgDealPriceSingle;//含税平均成交价 每公里每吨价格

    //20190225陈红新加需求
    private String removeVatGuidance;//去税指导价
    private String removeVatGuidanceSingle;//去税指导单价
    private String removeVatLowestHighest;//去税最低至最高区间价
    private String removeVatLowestHighestSingle;//去税最低至最高单价
    private String removeVatSvgDealPrice;//去税平均成交价
    private String removeVatSvgDealPriceSingle;//去税平均单价


    private String taxRemovalGuidance;//车方去税指导价
    private String taxRemovalGuidanceSingle;//去税指导价 每公里每吨价格
    private String taxRemovalLowestHighest;//车方去税历史成交区间价

    private String maxCash;//全运价最高
    private String maxOil;//全油卡最高

    public String getReferenceDaily() {
        return referenceDaily;
    }

    public void setReferenceDaily(String referenceDaily) {
        this.referenceDaily = referenceDaily;
    }

    public String getIncludingVatGuidance() {
        return includingVatGuidance;
    }

    public void setIncludingVatGuidance(String includingVatGuidance) {
        this.includingVatGuidance = includingVatGuidance;
    }

    public String getIncludingVatGuidanceSingle() {
        return includingVatGuidanceSingle;
    }

    public void setIncludingVatGuidanceSingle(String includingVatGuidanceSingle) {
        this.includingVatGuidanceSingle = includingVatGuidanceSingle;
    }

    public String getLowestHighest() {
        return lowestHighest;
    }

    public void setLowestHighest(String lowestHighest) {
        this.lowestHighest = lowestHighest;
    }

    public String getLowestHighestSingle() {
        return lowestHighestSingle;
    }

    public void setLowestHighestSingle(String lowestHighestSingle) {
        this.lowestHighestSingle = lowestHighestSingle;
    }

    public String getIncludingVatSvgDealPrice() {
        return includingVatSvgDealPrice;
    }

    public void setIncludingVatSvgDealPrice(String includingVatSvgDealPrice) {
        this.includingVatSvgDealPrice = includingVatSvgDealPrice;
    }

    public String getIncludingVatSvgDealPriceSingle() {
        return includingVatSvgDealPriceSingle;
    }

    public void setIncludingVatSvgDealPriceSingle(String includingVatSvgDealPriceSingle) {
        this.includingVatSvgDealPriceSingle = includingVatSvgDealPriceSingle;
    }

    public String getTaxRemovalGuidance() {
        return taxRemovalGuidance;
    }

    public void setTaxRemovalGuidance(String taxRemovalGuidance) {
        this.taxRemovalGuidance = taxRemovalGuidance;
    }

    public String getTaxRemovalGuidanceSingle() {
        return taxRemovalGuidanceSingle;
    }

    public void setTaxRemovalGuidanceSingle(String taxRemovalGuidanceSingle) {
        this.taxRemovalGuidanceSingle = taxRemovalGuidanceSingle;
    }

    public String getTaxRemovalLowestHighest() {
        return taxRemovalLowestHighest;
    }

    public void setTaxRemovalLowestHighest(String taxRemovalLowestHighest) {
        this.taxRemovalLowestHighest = taxRemovalLowestHighest;
    }

    public String getMaxCash() {
        return maxCash;
    }

    public void setMaxCash(String maxCash) {
        this.maxCash = maxCash;
    }

    public String getMaxOil() {
        return maxOil;
    }

    public void setMaxOil(String maxOil) {
        this.maxOil = maxOil;
    }

    public String getRemoveVatGuidance() {
        return removeVatGuidance;
    }

    public void setRemoveVatGuidance(String removeVatGuidance) {
        this.removeVatGuidance = removeVatGuidance;
    }

    public String getRemoveVatGuidanceSingle() {
        return removeVatGuidanceSingle;
    }

    public void setRemoveVatGuidanceSingle(String removeVatGuidanceSingle) {
        this.removeVatGuidanceSingle = removeVatGuidanceSingle;
    }

    public String getRemoveVatLowestHighest() {
        return removeVatLowestHighest;
    }

    public void setRemoveVatLowestHighest(String removeVatLowestHighest) {
        this.removeVatLowestHighest = removeVatLowestHighest;
    }

    public String getRemoveVatLowestHighestSingle() {
        return removeVatLowestHighestSingle;
    }

    public void setRemoveVatLowestHighestSingle(String removeVatLowestHighestSingle) {
        this.removeVatLowestHighestSingle = removeVatLowestHighestSingle;
    }

    public String getRemoveVatSvgDealPrice() {
        return removeVatSvgDealPrice;
    }

    public void setRemoveVatSvgDealPrice(String removeVatSvgDealPrice) {
        this.removeVatSvgDealPrice = removeVatSvgDealPrice;
    }

    public String getRemoveVatSvgDealPriceSingle() {
        return removeVatSvgDealPriceSingle;
    }

    public void setRemoveVatSvgDealPriceSingle(String removeVatSvgDealPriceSingle) {
        this.removeVatSvgDealPriceSingle = removeVatSvgDealPriceSingle;
    }
}
