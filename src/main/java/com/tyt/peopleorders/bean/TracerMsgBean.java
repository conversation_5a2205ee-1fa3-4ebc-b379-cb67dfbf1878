package com.tyt.peopleorders.bean;

import java.io.Serializable;
import java.util.Date;

public class TracerMsgBean implements Serializable{

	private static final long serialVersionUID = -8597559126010751409L;
	private Long id;//主键
	private String tracerName;//跟单人姓名
	private Integer findingNum;//跟单人货源中找车中的数量
	private Integer payedNoIntentionNum;//已支付但是未达成意向的货源个数
	private Integer createOrderNoPayNum;//已创建订单但是无人支付货源个数
	private Integer liineDealNum;//线上成交个数货源个数
	private Integer offlineDealNum;//线下成交的货源数量
	private Integer stoppedNum;//已停止找车的货源数量
	private Integer expiredNum;//已过期的货源数量
	private Integer trancedTotalNum;//跟单货源的总数量
	private Date statisticDate;//统计日期
	private String startTime; //开始时间
	private String endTime; //结束时间
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTracerName() {
		return tracerName;
	}
	public void setTracerName(String tracerName) {
		this.tracerName = tracerName;
	}
	public Integer getFindingNum() {
		return findingNum;
	}
	public void setFindingNum(Integer findingNum) {
		this.findingNum = findingNum;
	}
	public Integer getPayedNoIntentionNum() {
		return payedNoIntentionNum;
	}
	public void setPayedNoIntentionNum(Integer payedNoIntentionNum) {
		this.payedNoIntentionNum = payedNoIntentionNum;
	}
	public Integer getCreateOrderNoPayNum() {
		return createOrderNoPayNum;
	}
	public void setCreateOrderNoPayNum(Integer createOrderNoPayNum) {
		this.createOrderNoPayNum = createOrderNoPayNum;
	}
	public Integer getLiineDealNum() {
		return liineDealNum;
	}
	public void setLiineDealNum(Integer liineDealNum) {
		this.liineDealNum = liineDealNum;
	}
	public Integer getOfflineDealNum() {
		return offlineDealNum;
	}
	public void setOfflineDealNum(Integer offlineDealNum) {
		this.offlineDealNum = offlineDealNum;
	}
	public Integer getStoppedNum() {
		return stoppedNum;
	}
	public void setStoppedNum(Integer stoppedNum) {
		this.stoppedNum = stoppedNum;
	}
	public Integer getExpiredNum() {
		return expiredNum;
	}
	public void setExpiredNum(Integer expiredNum) {
		this.expiredNum = expiredNum;
	}
	public Integer getTrancedTotalNum() {
		return trancedTotalNum;
	}
	public void setTrancedTotalNum(Integer trancedTotalNum) {
		this.trancedTotalNum = trancedTotalNum;
	}
	public Date getStatisticDate() {
		return statisticDate;
	}
	public void setStatisticDate(Date statisticDate) {
		this.statisticDate = statisticDate;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	@Override
	public String toString() {
		return "TracerMsgBean [id=" + id + ", tracerName=" + tracerName
				+ ", findingNum=" + findingNum + ", payedNoIntentionNum="
				+ payedNoIntentionNum + ", createOrderNoPayNum="
				+ createOrderNoPayNum + ", liineDealNum=" + liineDealNum
				+ ", offlineDealNum=" + offlineDealNum + ", stoppedNum="
				+ stoppedNum + ", expiredNum=" + expiredNum
				+ ", trancedTotalNum=" + trancedTotalNum + ", statisticDate="
				+ statisticDate + ", startTime=" + startTime + ", endTime="
				+ endTime + "]";
	}
	
	
	
}
