package com.tyt.peopleorders.bean;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.JSON;

public class FreightPriceDetailInfoBean implements Serializable {

	private static final long serialVersionUID = 5847507278772904307L;
	private Long id;
	private String startPoint; // 起点
	private String destPoint; // 终点
	private String taskContent;// 货物信息
	private Double distance;// 总里程
	private Integer daysNum;// 天数
	private Double cargoLength;// 货物长度
	private Double selfTonne;// 车辆自重
	private Double tonne;// 载重（不包含自重）
	private Double baseCost;// 固定成本
	private Double oilCost;// 燃油费
	private Double highwayCost;// 高速费
	private Double fixedCost; // 成本价格
	private Double minPrice;// 最低价格
	private Double minProfitRate; // 基本利润率
	private Double estimateProfitPrice;// 预估利润金额
	private Integer estimateProfitRate;// 预估利润率
	private Double guidingPrice;// 指导价格
	private Double guidingRate;// 指导利润率
	private Double minPerKmPrice; // 最低每公里价格
	private Double guidingPerKmPrice; // 指导每公里价格
	private Date ctime;
	private Date mtime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getStartPoint() {
		return startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	public String getDestPoint() {
		return destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}

	public String getTaskContent() {
		return taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	public Double getDistance() {
		return distance;
	}

	public void setDistance(Double distance) {
		this.distance = distance;
	}

	public Integer getDaysNum() {
		return daysNum;
	}

	public void setDaysNum(Integer daysNum) {
		this.daysNum = daysNum;
	}

	public Double getCargoLength() {
		return cargoLength;
	}

	public void setCargoLength(Double cargoLength) {
		this.cargoLength = cargoLength;
	}

	public Double getSelfTonne() {
		return selfTonne;
	}

	public void setSelfTonne(Double selfTonne) {
		this.selfTonne = selfTonne;
	}

	public Double getTonne() {
		return tonne;
	}

	public void setTonne(Double tonne) {
		this.tonne = tonne;
	}

	public Double getBaseCost() {
		return baseCost;
	}

	public void setBaseCost(Double baseCost) {
		this.baseCost = baseCost;
	}

	public Double getOilCost() {
		return oilCost;
	}

	public void setOilCost(Double oilCost) {
		this.oilCost = oilCost;
	}

	public Double getHighwayCost() {
		return highwayCost;
	}

	public void setHighwayCost(Double highwayCost) {
		this.highwayCost = highwayCost;
	}

	public Double getMinPrice() {
		return minPrice;
	}

	public void setMinPrice(Double minPrice) {
		this.minPrice = minPrice;
	}

	public Double getMinProfitRate() {
		return minProfitRate;
	}

	public void setMinProfitRate(Double minProfitRate) {
		this.minProfitRate = minProfitRate;
	}

	public Double getEstimateProfitPrice() {
		return estimateProfitPrice;
	}

	public void setEstimateProfitPrice(Double estimateProfitPrice) {
		this.estimateProfitPrice = estimateProfitPrice;
	}

	public Double getGuidingPrice() {
		return guidingPrice;
	}

	public void setGuidingPrice(Double guidingPrice) {
		this.guidingPrice = guidingPrice;
	}

	public Double getGuidingRate() {
		return guidingRate;
	}

	public void setGuidingRate(Double guidingRate) {
		this.guidingRate = guidingRate;
	}

	public Double getMinPerKmPrice() {
		return minPerKmPrice;
	}

	public void setMinPerKmPrice(Double minPerKmPrice) {
		this.minPerKmPrice = minPerKmPrice;
	}

	public Double getGuidingPerKmPrice() {
		return guidingPerKmPrice;
	}

	public void setGuidingPerKmPrice(Double guidingPerKmPrice) {
		this.guidingPerKmPrice = guidingPerKmPrice;
	}

	public Double getFixedCost() {
		return fixedCost;
	}

	public void setFixedCost(Double fixedCost) {
		this.fixedCost = fixedCost;
	}

	public Integer getEstimateProfitRate() {
		return estimateProfitRate;
	}

	public void setEstimateProfitRate(Integer estimateProfitRate) {
		this.estimateProfitRate = estimateProfitRate;
	}

	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
