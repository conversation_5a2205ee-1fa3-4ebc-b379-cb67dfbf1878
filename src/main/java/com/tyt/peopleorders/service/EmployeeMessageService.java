package com.tyt.peopleorders.service;

import java.util.List;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.EmployeeMessage;
import com.tyt.model.TransportMt;
import com.tyt.model.PageBean;
import com.tyt.peopleorders.bean.EmployeeMessageBean;
import com.tyt.peopleorders.bean.EmployeeMessageQueryBean;
import com.tyt.service.base.BaseService;

public interface EmployeeMessageService extends BaseService<EmployeeMessage, Long> {

	/**
	 * 获得未推送消息列表
	 * @param userId
	 * @param time
	 * @param id
	 * @return
	 */
	public List<EmployeeMessage> updateGetEmployeeMessageList(Long userId,Long time,Long id);
	/**
	 * 获得未读消息数
	 * @param userId
	 * @return
	 */
	public int getEmployeeMessageReadCount(Long userId);
	
	/**
	 * 获得消息列表
	 * @param userId
	 * @param emb
	 * @param page
	 * @return
	 */
	public List<EmployeeMessageBean> updateGetList(Long userId,EmployeeMessageQueryBean emb,PageBean page);

	public void saveChangeTrancerMessage(EmployeeQueryBean loginUser, String tsId, String newTrancerNameAndId, TransportMt transportMt);

	/**
	 * 删除消息
	 * @param id
	 */
	public void deleteMsg(Long id);
	/**
	 * 修改消息备注
	 * @param id
	 * @param remarks
	 */
	public void updateRemarks(Long id,String remarks);
}
