package com.tyt.peopleorders.service.impl;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Pattern;

import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytSequenceService;
import com.tyt.dao.base.BaseDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.EcaContactMapper;
import com.tyt.peopleorders.bean.EcaContractQueryBean;
import com.tyt.peopleorders.service.EcaContractService;
import com.tyt.peopleorders.service.EcaProgressService;
import com.tyt.peopleorders.service.EcaUserProgressService;
import com.tyt.peopleorders.service.TytCarOwnerIntentionService;
import com.tyt.peopleorders.service.UserIdentityCertService;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.car.CarService;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.transport.TransportMainService;
import com.tyt.service.user.TytUserIdentityAuthService;
import com.tyt.service.user.UserService;
import com.tyt.util.TimeUtil;
import com.tyt.util.service.plat.PlatResultMsgBean;
import com.tyt.util.service.plat.PublicTytServiceHttpUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Service("ecaContractService")
public class EcaContractServiceImpl extends BaseServiceImpl<EcaContract, Long> implements EcaContractService {

	public static Logger logger = LoggerFactory.getLogger(EcaContractServiceImpl.class);

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "userService")
	private UserService userService;
	
	@Resource(name = "tytSequenceService")
	private TytSequenceService tytSequenceService;

	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;
	
	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Resource(name = "tytUserIdentityAuthService")
	private TytUserIdentityAuthService userIdentityAuthService;
	
	@Resource(name = "tytCarOwnerIntentionService")
	private TytCarOwnerIntentionService tytCarOwnerIntentionService;
	
	@Resource(name = "carService")
	private CarService carService;
	
	@Resource(name = "ecaUserProgressService")
	private EcaUserProgressService ecaUserProgressService;
	
	@Resource(name = "ecaProgressService")
	private EcaProgressService ecaProgressService;
	
	@Resource(name = "userIdentityCertService")
	private UserIdentityCertService userIdentityCertService;

	@Autowired
	private EcaContactMapper ecaContactMapper;

	private static String ECA_SEQUENCE_NAME = "ecacontract";


	private static final String SETTLE_FORMAT_1 = "现金${cash},油卡${oil}";
	private static final String SETTLE_FORMAT_2 = "现金${cash}";
	private static final String SETTLE_FORMAT_3 = "油卡${oil}";

	@Resource(name = "ecaContractDao")
	public void setBaseDao(BaseDao<EcaContract, Long> ecaContractDao) {
		super.setBaseDao(ecaContractDao);
	}
	
	@SuppressWarnings("deprecation")
	@Override
	public List<EcaContract> getContractList(EcaContractQueryBean queryBean,PageBean pageBean){
		Map<String,Object> paramMap=new HashMap<String,Object>();
		StringBuffer conditionSQL=new StringBuffer();
		StringBuffer countSql=new StringBuffer("select count(1) from eca_contract eca where 1=1");
		//合同编号
		if (queryBean.getContractNum()!=null && !"".equals(queryBean.getContractNum())) {
			conditionSQL.append(" AND eca.contract_num=:contractNum");
			paramMap.put("contractNum", queryBean.getContractNum());
		}
		//托运方
		if (queryBean.getShipper()!=null && !"".equals(queryBean.getShipper())) {
			List ids = userIdentityAuthService.getIdsByTrueName(queryBean.getShipper());
			List ids2 = userIdentityCertService.getIdsByTrueName(queryBean.getShipper());
			if (ids!=null && ids2!=null) {
				ids.addAll(ids2);
				conditionSQL.append(" AND shipper_user_id IN(:ids)");
				paramMap.put("ids",ids.toArray());
			}else if (ids!=null && ids2==null) {
				conditionSQL.append(" AND shipper_user_id IN(:ids)");
				paramMap.put("ids",ids.toArray());
			}else if (ids==null && ids2!=null) {
				conditionSQL.append(" AND shipper_user_id IN(:ids)");
				paramMap.put("ids",ids2.toArray());
			}else{
				conditionSQL.append(" AND shipper_user_id IN(:ids)");
				paramMap.put("ids",0);
			}
		}
		//承运方
		if (queryBean.getCarryUserName()!=null && !"".equals(queryBean.getCarryUserName())) {
			List ids = userIdentityAuthService.getIdsByTrueName(queryBean.getCarryUserName());
			List ids2 = userIdentityCertService.getIdsByTrueName(queryBean.getCarryUserName());
			if (ids!=null && ids2!=null) {
				ids.addAll(ids2);
				conditionSQL.append(" AND carry_user_id IN(:ids)");
				paramMap.put("ids",ids.toArray());
			}else if (ids!=null && ids2==null) {
				conditionSQL.append(" AND carry_user_id IN(:ids)");
				paramMap.put("ids",ids.toArray());
			}else if (ids==null && ids2!=null) {
				conditionSQL.append(" AND carry_user_id IN(:ids)");
				paramMap.put("ids",ids2.toArray());
			}else{
				conditionSQL.append(" AND carry_user_id IN(:ids)");
				paramMap.put("ids",0);
			}
		}
		//创建人
		if (queryBean.getCreateUserName()!=null && !"".equals(queryBean.getCreateUserName())) {
			conditionSQL.append(" AND eca.create_user_name like:createUserName");
			paramMap.put("createUserName", "%"+ queryBean.getCreateUserName() +"%");
		}
		//创建开始时间
		if (queryBean.getStartCtime()!=null && !"".equals(queryBean.getStartCtime())) {
			conditionSQL.append(" AND eca.ctime >=:startCtime");
			paramMap.put("startCtime", queryBean.getStartCtime()+" 00:00:00");
		}
		//创建结束时间
		if (queryBean.getEndCtime()!=null && !"".equals(queryBean.getEndCtime())) {
			conditionSQL.append(" AND eca.ctime <=:endCtime");
			paramMap.put("endCtime", queryBean.getEndCtime()+" 23:59:59");
		}
		//合同状态
		if (queryBean.getStatus()!=null && queryBean.getStatus()>0) {
			conditionSQL.append(" AND eca.status=:status");
			paramMap.put("status", queryBean.getStatus());
		}
		countSql.append(conditionSQL);
		BigInteger rowCount=this.getBaseDao().queryByMap(countSql.toString(), paramMap);
		if (rowCount != null && rowCount.intValue()>0) {
			StringBuffer sbSql = new StringBuffer(
					"SELECT eca.`id` id,eca.`contract_num` contractNum,eca.`shipper_user_id` shipperUserId, eca.`shipper_name` shipperName, "
					+ "eca.`shipper_type` shipperType,eca.`shipper_user_name` shipperUserName, "
					+ "eca.`shipper_phone` shipperPhone, eca.`shipper_sign_status` shipperSignStatus,"
					+ "eca.`carry_user_id` carryUserId,eca.`carry_user_name` carryUserName, eca.`carry_phone` carryPhone, "
					+ "eca.`carry_sign_status` carrySignStatus,eca.`start_point` startPoint, "
					+ "eca.`dest_point` destPoint, eca.`task_content` taskContent, eca.`src_msg_id` srcMsgId, "
					+ "eca.`create_user_name` createUserName,eca.`status` status,eca.`mt_id` mtId, eca.`ctime` ctime "
					+ "FROM `eca_contract` eca WHERE 1=1 "
			);
			Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
			map.put("id", Hibernate.LONG);
			map.put("contractNum", Hibernate.STRING);
			map.put("shipperUserId", Hibernate.LONG);
			map.put("shipperName", Hibernate.STRING);
			map.put("shipperType", Hibernate.INTEGER);
			map.put("shipperUserName", Hibernate.STRING);
			map.put("shipperPhone", Hibernate.STRING);
			map.put("shipperSignStatus", Hibernate.INTEGER);
			map.put("carryUserId", Hibernate.LONG);
			map.put("carryUserName", Hibernate.STRING);
			map.put("carryPhone", Hibernate.STRING);
			map.put("carrySignStatus", Hibernate.INTEGER);
			map.put("startPoint", Hibernate.STRING);
			map.put("destPoint", Hibernate.STRING);
			map.put("taskContent", Hibernate.STRING);
			map.put("srcMsgId", Hibernate.LONG);
			map.put("createUserName", Hibernate.STRING);
			map.put("status", Hibernate.INTEGER);
			map.put("mtId", Hibernate.LONG);
			map.put("ctime", Hibernate.TIMESTAMP);
			pageBean.setRowCount(rowCount.longValue());
			String sql=" order by ctime desc";
			sbSql.append(conditionSQL);
			sbSql.append(sql);
			List<EcaContract> resultList=new ArrayList<EcaContract>();
			resultList=this.getBaseDao().searchByName(sbSql.toString(), map, EcaContract.class, paramMap, pageBean.getCurrentPage(), pageBean.getPageSize());
			return resultList;
		}
		return null;
	}


/**
 * c初始化合同
 * @throws Exception 
 */
	@Override
	public void initEcaInfo(TransportMt transportMtNew, EmployeeQueryBean user) throws Exception {

		Long ecaContractId = insertEcaConTract(transportMtNew,user);
		insertEcaProgress(transportMtNew,user,ecaContractId);
		insertEcaUserProgress(transportMtNew,ecaContractId);
		
	}

	@Override
	public void insertEcaUserProgress(TransportMt transportMtNew, Long ecaContractId) {
		EcaUserProgress ecaUserProgress = new EcaUserProgress();
		ecaUserProgress.setContractId(ecaContractId);
		ecaUserProgress.setCtime(new Date());
		ecaUserProgress.setSrcMsgId(transportMtNew.getSrcMsgId());
		ecaUserProgress.setType(1);
		ecaUserProgressService.add(ecaUserProgress);
		
		
	}


	@Override
	public void insertEcaProgress(TransportMt transportMtNew,
			EmployeeQueryBean user, Long ecaContractId) {
		EcaProgress ecaProgress = new EcaProgress();
		ecaProgress.setContractId(ecaContractId);
		ecaProgress.setOptType(1);
		ecaProgress.setOptHandler(1);
		ecaProgress.setUserId(user.getId());
		ecaProgress.setUserName(user.getName());
		ecaProgress.setStatus(1);
		ecaProgress.setDetail("创建合同");
		ecaProgress.setCtime(new Date());
		ecaProgressService.add(ecaProgress);
		
	}

	@Override
	public ResultMsgBean getContractDetail(String contractId,String userId,String userName,String ip) throws Exception{
		TreeMap<String, String> paramsMap = new TreeMap<String, String>();
		paramsMap.put("contractId", contractId); // 正常发布tsId=0, 重发tsId =货物信息 -　必填
		paramsMap.put("currUserId", userId);
		paramsMap.put("userName", userName);
		paramsMap.put("ip", ip);

		ResultMsgBean resultMsgBean = null;
		logger.info("transport mt service get getContractDetail paramsMap is: " + paramsMap);
		try {
			PlatResultMsgBean transportBean = PublicTytServiceHttpUtil.callPlatService("plat/contract/getContractDetail.action", paramsMap);
			resultMsgBean = new ResultMsgBean();
			BeanUtils.copyProperties(transportBean, resultMsgBean);
			if (transportBean.getCode() != 200) {
				logger.info("plat接口getContractDetail服务调用异常:{}", transportBean.getMsg());
				resultMsgBean.setCode(transportBean.getCode());
			}
			return resultMsgBean;
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean = new ResultMsgBean();
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			return resultMsgBean;
		}

	}

	@Override
	public ResultMsgBean cancelContract(String contractId, String shipperCancelReason, String carryCancelReason,String userName,String userId,String optIp) throws Exception{
		TreeMap<String, String> paramsMap = new TreeMap<String, String>();
		paramsMap.put("contractId", contractId);
		paramsMap.put("shipperCancelReason", shipperCancelReason);
		paramsMap.put("carryCancelReason", carryCancelReason);
		paramsMap.put("userName", userName);
		paramsMap.put("currUserId", userId);
		paramsMap.put("optIp", optIp);

		ResultMsgBean resultMsgBean = null;
		logger.info("transport mt service get cancelContract paramsMap is: " + paramsMap);
		try {
			PlatResultMsgBean transportBean = PublicTytServiceHttpUtil.callPlatService("plat/mt/transport/cancelContract", paramsMap);
			resultMsgBean = new ResultMsgBean();
			BeanUtils.copyProperties(transportBean, resultMsgBean);
			if (transportBean.getCode() != 200) {
				logger.info("cancelContract:{}", transportBean.getMsg());
				resultMsgBean.setCode(transportBean.getCode());
			}
			return resultMsgBean;
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean = new ResultMsgBean();
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			return resultMsgBean;
		}
	}

	@Override
	public ResultMsgBean editContract(String contractId, String startPoint, String destPoint,String startDetailAdd,String destDetailAdd, String taskContent, String headLicensePlate, String tailLicensePlate, String price,String ip,String userId,String userName) throws Exception{
		TreeMap<String, String> paramsMap = new TreeMap<String, String>();
		paramsMap.put("contractId", contractId);
		paramsMap.put("startPoint", startPoint);
		paramsMap.put("startDetailAdd", startDetailAdd);
		paramsMap.put("destDetailAdd", destDetailAdd);
		paramsMap.put("destPoint", destPoint);
		paramsMap.put("taskContent", taskContent);
		paramsMap.put("headLicensePlate", headLicensePlate);
		paramsMap.put("tailLicensePlate", tailLicensePlate);
		paramsMap.put("price", price);
		paramsMap.put("ip",ip);
		paramsMap.put("currUserId",userId);
		paramsMap.put("userName", userName);

		ResultMsgBean resultMsgBean = null;
		logger.info("transport mt service get editContract paramsMap is: " + paramsMap);
		try {
			PlatResultMsgBean transportBean = PublicTytServiceHttpUtil.callPlatService("plat/mt/transport/editContract", paramsMap);
			resultMsgBean = new ResultMsgBean();
			BeanUtils.copyProperties(transportBean, resultMsgBean);
			if (transportBean.getCode() != 200) {
				logger.info("editContract:{}", transportBean.getMsg());
				resultMsgBean.setCode(transportBean.getCode());
			}
			return resultMsgBean;
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean = new ResultMsgBean();
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			return resultMsgBean;
		}
	}

	@Override
	public ResultMsgBean editContractWithOilCard(String contractId, String startPoint, String destPoint,String startDetailAdd,String destDetailAdd, String taskContent, String headLicensePlate, String tailLicensePlate, String price, String cashPrice, String oilCard, String ip,String userId,String userName) throws Exception{
		TreeMap<String, String> paramsMap = new TreeMap<String, String>();
		paramsMap.put("contractId", contractId);
		paramsMap.put("startPoint", startPoint);
		paramsMap.put("startDetailAdd", startDetailAdd);
		paramsMap.put("destDetailAdd", destDetailAdd);
		paramsMap.put("destPoint", destPoint);
		paramsMap.put("taskContent", taskContent);
		paramsMap.put("headLicensePlate", headLicensePlate);
		paramsMap.put("tailLicensePlate", tailLicensePlate);
		paramsMap.put("cashPrice", cashPrice);
		paramsMap.put("oilCard", oilCard);
		paramsMap.put("ip",ip);
		paramsMap.put("currUserId",userId);
		paramsMap.put("userName", userName);
		cashPrice = StringUtils.isEmpty(cashPrice) ? "0":cashPrice;
		oilCard = StringUtils.isEmpty(oilCard) ? "0":oilCard;
		price = String.valueOf(Integer.parseInt(cashPrice) + Integer.parseInt(oilCard));
		paramsMap.put("price", price);
		String settleAccountsType = ecaContactMapper.getSettleAccountsType(contractId);
		if(settleAccountsTypeEditable(settleAccountsType)){
			paramsMap.put("settleAccountsType", composeSettleTypeInfo(Float.parseFloat(cashPrice), Float.parseFloat(oilCard)));
		}

		ResultMsgBean resultMsgBean = null;
		logger.info("transport mt service get editContract paramsMap is: " + paramsMap);
		try {
			PlatResultMsgBean transportBean = PublicTytServiceHttpUtil.callPlatService("plat/mt/transport/editContract", paramsMap);
			resultMsgBean = new ResultMsgBean();
			BeanUtils.copyProperties(transportBean, resultMsgBean);
			if (transportBean.getCode() != 200) {
				logger.info("editContract:{}", transportBean.getMsg());
				resultMsgBean.setCode(transportBean.getCode());
			}
			return resultMsgBean;
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean = new ResultMsgBean();
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			return resultMsgBean;
		}
	}

	private boolean settleAccountsTypeEditable(String settleAccountsType) {
        Pattern pattern1 = Pattern.compile("现金[0-9\\.]+,油卡[0-9\\.]+");
        Pattern pattern2 = Pattern.compile("现金[0-9\\.]+");
        Pattern pattern3 = Pattern.compile("油卡[0-9\\.]+");
        return pattern1.matcher(settleAccountsType).matches() ||
                pattern2.matcher(settleAccountsType).matches() ||
                pattern3.matcher(settleAccountsType).matches();
	}

	@Override
	public EcaContract getEcaContractBySrcMsgId(Long SrcMsgId) {
		List<EcaContract> ecaContracts = this.getBaseDao().find("from EcaContract t where t.srcMsgId = ? and t.status < 4 ",SrcMsgId);
		return (ecaContracts!=null&&ecaContracts.size()>0)?ecaContracts.get(0):null;
	}


	@Override
	public Long  insertEcaConTract(TransportMt transportMtNew,
			EmployeeQueryBean user) throws Exception {
		EcaContract ecaContract = new EcaContract();
		ecaContract.setSrcMsgId(transportMtNew.getSrcMsgId());
		//生成合同编号
		ecaContract.setContractNum(tytSequenceService.updateGetNumberForDate(ECA_SEQUENCE_NAME));
		//初始化托运方信息
		if(transportMtNew.getUserId()!=null && transportMtNew.getUserId()>1){
			User userInfo = userService.getUserByCellphone(transportMtNew.getShipperPhone());
			ecaContract.setShipperUserId(userInfo != null ? userInfo.getId() : null);
			}
		ecaContract.setShipperPhone(transportMtNew.getShipperPhone());
		//初始化承运方信息
		if(transportMtNew.getFirstPayUserId()!=null && transportMtNew.getFirstPayUserId()>1){
			ecaContract.setCarryUserId(transportMtNew.getFirstPayUserId());
			User userInfo = userService.getById(transportMtNew.getFirstPayUserId());
			ecaContract.setCarryPhone(userInfo != null ? userInfo.getCellPhone() : null);
		}
		//加载main标的tsOrderNo
		TransportMain main = transportMainService.getTransportMainForId(transportMtNew.getSrcMsgId());
		if(main!=null){
			ecaContract.setTsOrderNo(main.getTsOrderNo());	
		}else{
			ecaContract.setTsOrderNo(transportMtNew.getTsOrderNo());
			}
		/**
		 * 原合同价格 现金 + 油卡， 备注中保存拼接现金+油卡字符串
 		 */
		ecaContract.setCashPrice(transportMtNew.getStrikePrice());
		ecaContract.setOilCard(transportMtNew.getOilPrice());
		ecaContract.setPrice(transportMtNew.getStrikePrice() + Math.round(transportMtNew.getOilPrice()));
		String settleInfo = composeSettleTypeInfo(ecaContract.getCashPrice(), ecaContract.getOilCard());
		ecaContract.setSettleAccountsType(settleInfo);
		ecaContract.setStartPoint(transportMtNew.getStartPoint());
		ecaContract.setStartDetailAdd(transportMtNew.getStartDetailAdd());
		ecaContract.setDestDetailAdd(transportMtNew.getDestDetailAdd());
		ecaContract.setDestPoint(transportMtNew.getDestPoint());
		ecaContract.setTaskContent(transportMtNew.getTaskContent());
		ecaContract.setStatus(1);
		ecaContract.setCreateUserId(user.getId());
		ecaContract.setCreateUserName(user.getName());
		ecaContract.setMtId(transportMtNew.getId());
		ecaContract.setCtime(new Date());
		ecaContract.setMtime(new Date());
		//TODO 加载运输牌照
		TytCarOwnerIntention  tytCarOwnerIntention = tytCarOwnerIntentionService.getBySrcMsgIdAndCaruserId(transportMtNew.getSrcMsgId(),transportMtNew.getFirstPayUserId());
		if(tytCarOwnerIntention!=null && tytCarOwnerIntention.getCarId()!=null){
			Car car = carService.getById(tytCarOwnerIntention.getCarId());
			ecaContract.setHeadLicensePlate(car.getHeadCity()+car.getHeadNo());
			ecaContract.setTailLicensePlate(car.getTailCity()+car.getTailNo());
			
		}
		ecaContract.setShipperSignStatus(1);
		ecaContract.setCarrySignStatus(1);
		Long ecaContractId = (Long) this.getBaseDao().insertWithReturnId(ecaContract);
		return ecaContractId;
	}

	private String composeSettleTypeInfo(float cashPrice, float oilCard) {
		String settleInfo = "";
		if(cashPrice > 0 && oilCard > 0){
			Map<String, Integer> valuesMap = new HashMap();
			valuesMap.put("cash", Math.round(cashPrice));
			valuesMap.put("oil", Math.round(oilCard));
			StrSubstitutor strSub = new StrSubstitutor(valuesMap);
			settleInfo = strSub.replace(SETTLE_FORMAT_1);
		}else if(cashPrice > 0 && oilCard == 0){
			Map<String, Integer> valuesMap = new HashMap();
			valuesMap.put("cash", Math.round(cashPrice));
			StrSubstitutor strSub = new StrSubstitutor(valuesMap);
			settleInfo = strSub.replace(SETTLE_FORMAT_2);
		}else if(cashPrice == 0 && oilCard > 0){
			Map<String, Integer> valuesMap = new HashMap();
			valuesMap.put("oil", Math.round(oilCard));
			StrSubstitutor strSub = new StrSubstitutor(valuesMap);
			settleInfo = strSub.replace(SETTLE_FORMAT_3);
		}
		return settleInfo;
	}

}
