package com.tyt.peopleorders.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.EmployeeMessageTmpl;
import com.tyt.peopleorders.service.EmployeeMessageTmplService;
import com.tyt.service.base.BaseServiceImpl;

@Service(value = "employeeMessageTmplService")
public class EmployeeMessageTmplServiceImpl extends BaseServiceImpl<EmployeeMessageTmpl, Long> implements
		EmployeeMessageTmplService {
	public Logger logger = LoggerFactory.getLogger("EmployeeMessageTmplServiceImpl");

	@Resource(name = "employeeMessageTmplDao")
	public void setBaseDao(BaseDao<EmployeeMessageTmpl, Long> EmployeeMessageTmplDao) {
		super.setBaseDao(EmployeeMessageTmplDao);
	}
/*
	@Resource(name = "userService")
	public UserService userService;
	@Resource(name = "tytConfigService")
	TytConfigService tytConfigService;
	
	@Resource(name = "transportMainService")
	public TransportMainService transportMainService;*/

	public EmployeeMessageTmpl getEmployeeMessageTmpl(String key){
		String hql="from EmployeeMessageTmpl where tmplKey=?";
		List<EmployeeMessageTmpl> list=this.getBaseDao().find(hql, key);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}
}
