package com.tyt.peopleorders.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import com.tyt.model.TytAccurateQueue;
import com.tyt.peopleorders.bean.CarMsgBean;
import com.tyt.service.base.BaseService;


public interface TytAccurateQueueService extends BaseService<TytAccurateQueue, Long>{
	/**
	 * 更新精准货源的推送状态
	 * 
	 * @param srcMsgId
	 *            货源的源ID
	 * @param carId
	 *            推送的车辆ID
	 * @param pushStatus
	 *            推送状态 0 推送 1 未推送
	 */
	void updatePushStatus(Long srcMsgId, Long carId, int pushStatus);
	
	public List<TytAccurateQueue> getList(Long tsId,Integer limit);

	boolean updateCarInfo(Long carId);
}
