package com.tyt.peopleorders.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.EmployeeMessageTmpl;
import com.tyt.peopleorders.dao.EmployeeMessageTmplDao;

@Repository("employeeMessageTmplDao")
public class EmployeeMessageTmplDaoImpl extends BaseDaoImpl<EmployeeMessageTmpl, Long> implements EmployeeMessageTmplDao {
	public EmployeeMessageTmplDaoImpl() {
		this.setEntityClass(EmployeeMessageTmpl.class);
	}
}
