package com.tyt.file.service.impl;

import com.tyt.file.bean.FileInfo;
import com.tyt.file.service.FileInfoService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.mybatis.mapper.FileInfoMapper;
import com.tyt.util.AppConfig;
import com.tyt.util.CreateFileUtil;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName FileInfoServiceImpl
 * @Description 文件信息服务层接口实现类
 * <AUTHOR>
 * @Date 2019-01-17 14:58
 * @Version 1.0
 */
@Service("fileInfoService")
public class FileInfoServiceImpl implements FileInfoService {

    //文件的根路径
    private final String FILE_PATH_DOMAIN = AppConfig.getProperty("picture.file.path.domain");
    //文件相对路径
    private final String FILE_PATH_REAL = "/data/file/";
    //文件路径分割符
    private final String FILE_PATH_SEPARATION = "/";

    //原图压缩宽
    private final String PICTURE_ORIGINAL_WIDTH = AppConfig.getProperty("picture.original.width");
    //原图压缩高
    private final String PICTURE_ORIGINAL_HEIGHT = AppConfig.getProperty("picture.original.height");
    //缩略图压缩宽
    private final String PICTURE_COMPRESS_WIDTH = AppConfig.getProperty("picture.compress.width");
    //缩略图压缩高
    private final String PICTURE_COMPRESS_HEIGHT = AppConfig.getProperty("picture.compress.height");


    @Autowired
    private FileInfoMapper fileInfoMapper;

    /**
     * @Description  上传文件实现方法
     * <AUTHOR>
     * @Date  2020/3/24 11:52
     * @Param [curUser, businessId, fileType, uploadFile, sortId, typeName]
     * @return java.util.Map
     **/
    @Override
    public Map uploadFile(EmployeeQueryBean curUser, Long businessId, Integer fileType,
                          MultipartFile uploadFile,Integer sortId,String typeName) throws Exception{

        //1.保存原图片文件
        //先将所有图片转换成.jpg格式的文件，Thumbnails压缩png图片有bug，有时会越压越大
        //文件名称
        String originalFilename = uploadFile.getOriginalFilename().replace(".png", ".jpg");
        //文件大小
        Long fileSize = uploadFile.getSize();
        //文件后缀
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".")).replace(".png", ".jpg");
        //重命名后的文件相对路径
        String fileRealUrl = renameFileBase(uploadFile, typeName).replace(".png", ".jpg");
        //重命名后的文件名称
        String fileName = fileRealUrl.substring(fileRealUrl.lastIndexOf(FILE_PATH_SEPARATION)+1).replace(".png", ".jpg");
        //重命名后的文件全路径
        String fileAllPath = FILE_PATH_DOMAIN + fileRealUrl;
        //生成本地文件
        uploadFile.transferTo(new File(fileAllPath));
        //原图压缩成 600*600
        if(fileSize>=1024000){
            Thumbnails.of(fileAllPath)
//				.scale(0.1f)  //按比例压缩
//				.sourceRegion(Positions.CENTER, 300,300)  //截取中心点300范围
                    .size(Integer.parseInt(PICTURE_ORIGINAL_WIDTH), Integer.parseInt(PICTURE_ORIGINAL_HEIGHT))   //按照尺寸比例压缩
                    .toFile(fileAllPath);
        }

        //2.生成缩略图,保存缩略图文件
        String prefix = fileRealUrl.substring(0,fileRealUrl.lastIndexOf("."));
        //缩略图文件相对路径
        String smallFileUrl = prefix + "_" + PICTURE_COMPRESS_WIDTH
                                     + "_" + PICTURE_COMPRESS_HEIGHT + suffix;
        //缩略图文件名称
        String smallFileName = smallFileUrl.substring(smallFileUrl.lastIndexOf(FILE_PATH_SEPARATION)+1);
        //缩略图压缩成 200*200
        Thumbnails.of(fileAllPath)
//				.scale(0.1f)  //按比例压缩
//				.sourceRegion(Positions.CENTER, 300,300)  //截取中心点300范围
                .size(Integer.parseInt(PICTURE_COMPRESS_WIDTH), Integer.parseInt(PICTURE_COMPRESS_HEIGHT))   //按照尺寸比例压缩
                .toFile(FILE_PATH_DOMAIN + smallFileUrl);
        //3.将文件信息保存到数据库中
        FileInfo fileInfo = new FileInfo();
        //业务Id
        fileInfo.setBusinessId(businessId);
        //文件类型:1.异常上报凭证图片,
        fileInfo.setFileType(fileType);
        //文件原名称
        fileInfo.setOriginalFileName(originalFilename);
        //文件名称
        fileInfo.setFileName(fileName);
        //文件相对路径
        fileInfo.setFilePath(fileRealUrl);
        //文件大小
        fileInfo.setFileSize(fileSize);
        //文件后缀名称
        fileInfo.setFileSuffix(suffix);
        //缩略图文件名称
        fileInfo.setSmallFileName(smallFileName);
        //缩略图文件路径
        fileInfo.setSmallFilePath(smallFileUrl);
        fileInfo.setCreateUserId(curUser.getId());
        fileInfo.setCreateUserName(curUser.getUserName());
        fileInfo.setCtime(new Date());
        fileInfo.setUpdateUserId(curUser.getId());
        fileInfo.setUpdateUserName(curUser.getUserName());
        fileInfo.setUtime(new Date());
        //文件状态: 1-正常,2-删除
        fileInfo.setStatus(1);
        //排序ID
        fileInfo.setSortId(sortId);
        fileInfoMapper.insertFileInfo(fileInfo);
        //4.将文件信息返回
        Map<String,Object> fileInfoMap = new HashMap<String,Object>();
        fileInfoMap.put("fileInfo",fileInfo);
        fileInfoMap.put("fileRealUrl",fileRealUrl);
        fileInfoMap.put("smallFileUrl",smallFileUrl);
        return fileInfoMap;
    }


    /**
     * @Description  上传文件实现方法 -- 新
     * <AUTHOR>
     * @Date  2023/4/14 11:52
     * @Param [curUser, businessId, fileType, uploadFile, sortId, typeName]
     * @return java.util.Map
     **/
    @Override
    public Map uploadFileList(EmployeeQueryBean curUser, List<Long> businessId, Integer fileType,
                          MultipartFile uploadFile,Integer sortId,String typeName) throws Exception{

        //1.保存原图片文件
        //先将所有图片转换成.jpg格式的文件，Thumbnails压缩png图片有bug，有时会越压越大
        //文件名称
        String originalFilename = uploadFile.getOriginalFilename().replace(".png", ".jpg");
        //文件大小
        Long fileSize = uploadFile.getSize();
        //文件后缀
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".")).replace(".png", ".jpg");
        //重命名后的文件相对路径
        String fileRealUrl = renameFileBase(uploadFile, typeName).replace(".png", ".jpg");
        //重命名后的文件名称
        String fileName = fileRealUrl.substring(fileRealUrl.lastIndexOf(FILE_PATH_SEPARATION)+1).replace(".png", ".jpg");
        //重命名后的文件全路径
        String fileAllPath = FILE_PATH_DOMAIN + fileRealUrl;
        //生成本地文件
        uploadFile.transferTo(new File(fileAllPath));
        //原图压缩成 600*600
        Thumbnails.of(fileAllPath)
//				.scale(0.1f)  //按比例压缩
//				.sourceRegion(Positions.CENTER, 300,300)  //截取中心点300范围
                .size(Integer.parseInt(PICTURE_ORIGINAL_WIDTH), Integer.parseInt(PICTURE_ORIGINAL_HEIGHT))   //按照尺寸比例压缩
                .toFile(fileAllPath);

        //2.生成缩略图,保存缩略图文件
        String prefix = fileRealUrl.substring(0,fileRealUrl.lastIndexOf("."));
        //缩略图文件相对路径
        String smallFileUrl = prefix + "_" + PICTURE_COMPRESS_WIDTH
                + "_" + PICTURE_COMPRESS_HEIGHT + suffix;
        //缩略图文件名称
        String smallFileName = smallFileUrl.substring(smallFileUrl.lastIndexOf(FILE_PATH_SEPARATION)+1);
        //缩略图压缩成 200*200
        Thumbnails.of(fileAllPath)
//				.scale(0.1f)  //按比例压缩
//				.sourceRegion(Positions.CENTER, 300,300)  //截取中心点300范围
                .size(Integer.parseInt(PICTURE_COMPRESS_WIDTH), Integer.parseInt(PICTURE_COMPRESS_HEIGHT))   //按照尺寸比例压缩
                .toFile(FILE_PATH_DOMAIN + smallFileUrl);

        FileInfo fileInfo = new FileInfo();

        for (Long id : businessId) {
            //3.将文件信息保存到数据库中
            //业务Id
            fileInfo.setBusinessId(id);
            //文件类型:1.异常上报凭证图片,
            fileInfo.setFileType(fileType);
            //文件原名称
            fileInfo.setOriginalFileName(originalFilename);
            //文件名称
            fileInfo.setFileName(fileName);
            //文件相对路径
            fileInfo.setFilePath(fileRealUrl);
            //文件大小
            fileInfo.setFileSize(fileSize);
            //文件后缀名称
            fileInfo.setFileSuffix(suffix);
            //缩略图文件名称
            fileInfo.setSmallFileName(smallFileName);
            //缩略图文件路径
            fileInfo.setSmallFilePath(smallFileUrl);
            fileInfo.setCreateUserId(curUser.getId());
            fileInfo.setCreateUserName(curUser.getUserName());
            fileInfo.setCtime(new Date());
            fileInfo.setUpdateUserId(curUser.getId());
            fileInfo.setUpdateUserName(curUser.getUserName());
            fileInfo.setUtime(new Date());
            //文件状态: 1-正常,2-删除
            fileInfo.setStatus(1);
            //排序ID
            fileInfo.setSortId(sortId);
            fileInfoMapper.insertFileInfo(fileInfo);
        }

        //4.将文件信息返回
        Map<String,Object> fileInfoMap = new HashMap<String,Object>();
        fileInfoMap.put("fileInfo",fileInfo);
        fileInfoMap.put("fileRealUrl",fileRealUrl);
        fileInfoMap.put("smallFileUrl",smallFileUrl);
        return fileInfoMap;
    }
    
    /**
     * @Description 获取文件列表实现方法
     * <AUTHOR>
     * @Date  2019/1/18 11:37
     * @Param [businessId, fileType]
     * @return java.util.List<com.tyt.file.bean.FileInfo>
     **/
    @Override
    public List<FileInfo> getFileInfoList(Long businessId, Integer fileType) {

        List<FileInfo> fileList = fileInfoMapper.getFileInfoList(businessId, fileType);
        return fileList;
    }

    /**
     * @Description 获取文件列表实现方法
     * <AUTHOR>
     * @Date  2019/1/18 11:37
     * @Param [businessId, fileType]
     * @return java.util.List<com.tyt.file.bean.FileInfo>
     **/
    @Override
    public List<FileInfo> getSingleFileInfoId(Long businessId, Integer fileType, Integer type) {

        List<FileInfo> fileList = fileInfoMapper.getFileInfoListId(businessId, fileType,type);
        return fileList;
    }

    /**
     * 获取文件列表方法
     * @param businessId
     * @param secondBusinessId
     * @param fileType
     * @param type
     * @return
     */
    @Override
    public List<FileInfo> getFileInfoListByCondition(Long businessId, Long secondBusinessId, Integer fileType, Integer type) {
        List<FileInfo> fileInfoList = fileInfoMapper.getFileInfoListByCondition(businessId, secondBusinessId, fileType, type);
        return fileInfoList;
    }

    /**
     * @Description  获取单个文件信息的实现方法
     * <AUTHOR>
     * @Date  2019/1/18 12:30
     * @Param [id]
     * @return com.tyt.file.bean.FileInfo
     **/
    @Override
    public FileInfo getSingleFileInfo(Long id) {

        FileInfo singleFileInfo = fileInfoMapper.getSingleFileInfo(id);
        return singleFileInfo;
    }
     
    /**
     * @Description  更新文件信息状态的实现方法
     * <AUTHOR>
     * @Date  2019/1/18 12:30
     * @Param [curUser, id, status]
     * @return int
     **/
    @Override
    public int updateFileInfoStatus(EmployeeQueryBean curUser, Long id, Integer status) {
        //文件信息对象
        FileInfo fileInfo = new FileInfo();
        //文件Id
        fileInfo.setId(id);
        fileInfo.setUpdateUserId(curUser.getId());
        fileInfo.setUpdateUserName(curUser.getUserName());
        fileInfo.setUtime(new Date());
        //文件状态
        fileInfo.setStatus(status);
        //更新结果
        int result = fileInfoMapper.updateFileInfo(fileInfo);
        return result;
    }

    /**
     * @Description  根据业务ID修改文件状态的方法
     * <AUTHOR>
     * @Date  2020/1/9 10:55
     * @Param [user, businessId, status]
     * @return int
     **/
    @Override
    public int updateStatusByBusinessId(EmployeeQueryBean curUser, Long businessId, Integer status) {
        //文件信息对象
        FileInfo fileInfo = new FileInfo();
        //文件Id
        fileInfo.setBusinessId(businessId);
        fileInfo.setUpdateUserId(curUser.getId());
        fileInfo.setUpdateUserName(curUser.getUserName());
        fileInfo.setUtime(new Date());
        //文件状态
        fileInfo.setStatus(status);
        //更新结果
        int result = fileInfoMapper.updateStatusByBusinessId(fileInfo);
        return result;
    }

    /**
     * @Description  创建上传文件保存目录，返回重命名后的文件路径
     * <AUTHOR>
     * @Date  2018/12/13 16:25
     * @Param [multipartFile, typeName]
     * @return java.lang.String
     **/
    public String renameFileBase(MultipartFile multipartFile, String typeName) {
        //文件相对路径文件
        String domainurl = FILE_PATH_REAL + typeName + FILE_PATH_SEPARATION;
        //创建文件相对路径文件夹
        CreateFileUtil.createDir(FILE_PATH_DOMAIN + domainurl);
        //文件相对全路径，包含重命名后的文件名称
        String realUrl = domainurl + renameFile(multipartFile.getOriginalFilename());
        return realUrl;
    }
    /**
     * @Description  对上传的文件重命名
     * <AUTHOR>
     * @Date  2018/12/13 16:18
     * @Param [fileName]
     * @return java.lang.String
     **/
    public static String renameFile(String fileName) {
        DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String formatDate = format.format(new Date());
        int random = new Random().nextInt(10000);
        int position = fileName.lastIndexOf(".");
        String extension = fileName.substring(position);
        return formatDate + random + extension;
    }

    /**
     * @Description  根据文件路径更新文件信息的排序ID
     * <AUTHOR>
     * @Date  2020/3/17 11:07
     * @Param [curUser, filePath, sortId]
     * @return void
     **/
    @Override
    public int updateSortIdByFilePath(EmployeeQueryBean curUser, String filePath, Integer sortId) {
        //文件信息对象
        FileInfo fileInfo = new FileInfo();
        //文件路径
        fileInfo.setFilePath(filePath);
        //排序ID
        fileInfo.setSortId(sortId);
        fileInfo.setUpdateUserId(curUser.getId());
        fileInfo.setUpdateUserName(curUser.getUserName());
        fileInfo.setUtime(new Date());
        //更新结果
        int result = fileInfoMapper.updateSortIdByFilePath(fileInfo);
        return result;
    }
}
