package com.tyt.log.controller;

import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.bean.UserInviteAwardInfoBean;
import com.tyt.acvitity.service.PlatUserInviteAwardInfoService;
import com.tyt.carnews.service.CarNewsService;
import com.tyt.carteam.service.CarteamService;
import com.tyt.competePhone.service.CompeteCellphoneService;
import com.tyt.driver.service.TytCarDriverArchivesService;
import com.tyt.enums.MenuEnum;
import com.tyt.insurance.bean.InsuranceBean;
import com.tyt.insurance.service.InsuranceService;
import com.tyt.invitefriends.service.InviteFriendsService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.log.qbean.CustomInformation;
import com.tyt.log.qbean.TytManageQueryPhoneLogQbean;
import com.tyt.log.service.TytManageQueryPhoneLogService;
import com.tyt.manager.entity.base.OwnerCompanyRelation;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.CustomInfomationMapper;
import com.tyt.promotion.service.PromotionService;
import com.tyt.service.advice.AdviceService;
import com.tyt.service.blockinfo.BlockInfoService;
import com.tyt.service.dispatch.TransportDispatchService;
import com.tyt.service.newFriend.TytRecFriendLogService;
import com.tyt.service.newcar.NewCarBackService;
import com.tyt.service.secondcar.SecondCarBackService;
import com.tyt.service.takecar.TakeCarService;
import com.tyt.service.transaction.TransactionStatusRecordService;
import com.tyt.service.user.AccountUpdateLogService;
import com.tyt.service.user.TytRegPhoneService;
import com.tyt.service.user.UserService;
import com.tyt.service.user.UserTelService;
import com.tyt.transportnews.service.OwnerCompanyRelationService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.usedCarSale.service.TytUsedCarSaleService;
import com.tyt.util.Constant;
import com.tyt.web.back.internal.bean.InviteFriendsBean;
import com.tyt.web.back.internal.bean.TytRecFriendLogBean;
import com.tyt.web.back.internal.bean.UserTelBean;
import com.tyt.web.base.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Base64;
import java.util.List;

import static com.tyt.enums.MenuEnum.getMenuEnumByName;


@Controller
@RequestMapping("/manage/queryPhonelog")
public class TytManageQueryPhoneLogController extends BaseController {

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "EmployeeService")
    private com.tyt.jurisdiction.service.EmployeeService employeeService;

    @Resource(name = "tytManageQueryPhoneLogService")
    TytManageQueryPhoneLogService tytManageQueryPhoneLogService;

    @Resource(name = "tytCarDriverArchivesService")
    private TytCarDriverArchivesService tytCarDriverArchivesService;

    @Resource(name = "carteamService")
    private CarteamService carteamService;

    @Resource(name = "insuranceService")
    private InsuranceService insuranceService;

    @Resource(name = "adviceService")
    private AdviceService adviceBackService;

    @Resource(name = "accountUpdateLogService")
    private AccountUpdateLogService accountUpdateLogService;

    @Resource(name = "tytRegPhoneService")
    private TytRegPhoneService tytRegPhoneService;
    @Resource(name="tsInsuranceService")
    private TsInsuranceService tsInsuranceService;


    @Resource(name = "inviteFriendsService")
    InviteFriendsService inviteFriendsService;

    @Resource(name="userTelService")
    UserTelService userTelService;

    @Resource(name = "competeCellphoneService")
    private CompeteCellphoneService competeCellphoneService;

    @Autowired
    private OwnerCompanyRelationService ownerCompanyRelationService;

    @Resource(name = "transportDispatchService")
    private TransportDispatchService transportDispatchService;

    @Resource
    private TransactionStatusRecordService transactionStatusRecordService;

    @Resource(name = "blockInfoService")
    private BlockInfoService blockInfoService;

    @Resource(name = "newCarBackService")
    private NewCarBackService newCarBackService;

    @Resource(name = "secondCarBackService")
    private SecondCarBackService secondCarBackService;

    @Resource(name = "takeCarService")
    TakeCarService takeCarService;

    @Resource(name = "carNewsService")
    CarNewsService carNewsService;

    @Autowired
    private TytUsedCarSaleService usedCarSaleService;

    @Resource(name="tytRecFriendLogService")
    private TytRecFriendLogService tytRecFriendLogService;

    @Autowired
    private PromotionService promotionService;

    @Resource(name = "platUserInviteAwardInfoService")
    private PlatUserInviteAwardInfoService platUserInviteAwardInfoService;

    @Autowired
    private CustomInfomationMapper customInfomationMapper;


    @RequestMapping(value = "/getPhoneByUserId")
    @ResponseBody
    public ResultMsgBean getList(HttpServletRequest request, TytManageQueryPhoneLogQbean queryPhoneLogQbean) {
        ResultMsgBean resultMsgBean=new ResultMsgBean();
		EmployeeQueryBean curUser = getCurrentUser(request);
		if (curUser == null) {
			resultMsgBean.setCode(500);
			resultMsgBean.setMsg(Constant.MSG_NOT_LOGIN);
			return resultMsgBean;
		}
		try {
			queryPhoneLogQbean.setUserId(new String(Base64.getDecoder().decode(queryPhoneLogQbean.getUserId())));
			if(queryPhoneLogQbean.getUserId() == null || !StringUtils.isNumeric(queryPhoneLogQbean.getUserId())) {
                resultMsgBean.setCode(600);
                resultMsgBean.setMsg("用户不存在");
                return resultMsgBean;
            }
            User user = userService.getByIdForLock(Long.valueOf(queryPhoneLogQbean.getUserId()));
            tytManageQueryPhoneLogService.saveLog(queryPhoneLogQbean, curUser);
            if(null==user){
                resultMsgBean.setCode(500);
                resultMsgBean.setMsg("用户不存在");
                return resultMsgBean;
            }
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(user.getCellPhone());
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg(e.getMessage());
            return resultMsgBean;
        }
    }

    @RequestMapping(value = "/getEmployeePhoneByUserId")
    @ResponseBody
    public ResultMsgBean getEmployeePhoneByUserId(HttpServletRequest request, TytManageQueryPhoneLogQbean queryPhoneLogQbean) {
        ResultMsgBean resultMsgBean=new ResultMsgBean();
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg(Constant.MSG_NOT_LOGIN);
            return resultMsgBean;
        }
        try {
            queryPhoneLogQbean.setUserId(new String(Base64.getDecoder().decode(queryPhoneLogQbean.getUserId())));
            if(queryPhoneLogQbean.getUserId() == null || !StringUtils.isNumeric(queryPhoneLogQbean.getUserId())) {
                resultMsgBean.setCode(600);
                resultMsgBean.setMsg("用户不存在");
                return resultMsgBean;
            }
            TytEmployee employee = employeeService.getByIdForLock(Long.valueOf(queryPhoneLogQbean.getUserId()));
            tytManageQueryPhoneLogService.saveLog(queryPhoneLogQbean, curUser);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(employee.getLoginPhoneNo());
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg(e.getMessage());
            return resultMsgBean;
        }
    }


    @RequestMapping(value = "/getPhoneById")
    @ResponseBody
    public ResultMsgBean getPhoneById(HttpServletRequest request, TytManageQueryPhoneLogQbean queryPhoneLogQbean) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg(Constant.MSG_NOT_LOGIN);
            return resultMsgBean;
        }
        try {
            if (queryPhoneLogQbean.getId() == null || queryPhoneLogQbean.getId() <= 0L) {
                resultMsgBean.setCode(600);
                resultMsgBean.setMsg("参数非法");
                return resultMsgBean;
            }
            //传递过来的主键Id
            Long id = queryPhoneLogQbean.getId();
            //返回对应的手机号
            JSONObject dataJson = new JSONObject();
            MenuEnum menuEnum = getMenuEnumByName(queryPhoneLogQbean.getMenuName());
            switch (menuEnum) {
                case 未注册手机号收集:
                    TytRegPhone tytRegPhone = tytRegPhoneService.getById(id);
                    if(tytRegPhone!=null){
                        dataJson.put("phone",tytRegPhone.getPhone());
                    }
                    break;
                case 意见反馈记录:
                    Advice advice = adviceBackService.getById(id);
                    if(advice!=null){
                        dataJson.put("cellPhone",Strings.isNotEmpty(advice.getCellPhone())?advice.getCellPhone():"");
                        dataJson.put("telPhone", Strings.isNotEmpty(advice.getTelPhone())?advice.getTelPhone():"");
                    }
                    break;
                case 推荐好友列表:
                    List<InviteFriendsBean> inviteFriends = inviteFriendsService.getFriendById(id);
                    if(!inviteFriends.isEmpty()){
                        dataJson.put("cellPhone",inviteFriends.get(0).getCellPhone());
                        dataJson.put("friendCell",inviteFriends.get(0).getFriendCell());
                    }
                    break;
                case 发货联系人列表:
                    List<UserTelBean> userTelList = userTelService.getUserTelById(id);
                    if(!userTelList.isEmpty()){
                        dataJson.put("tell",userTelList.get(0).getTell());
                        dataJson.put("cellPhone",userTelList.get(0).getCellPhone());
                    }
                    break;
                case 在线预约:
                    InsuranceBean inSuranceBean = insuranceService.getInsuranceById(id);
                    if(inSuranceBean!=null){
                        dataJson.put("registerTel",Strings.isNotEmpty(inSuranceBean.getRegisterTel())?inSuranceBean.getRegisterTel():"");
                        dataJson.put("tel1",Strings.isNotEmpty(inSuranceBean.getTel1())?inSuranceBean.getTel1():"");
                        dataJson.put("tel2",Strings.isNotEmpty(inSuranceBean.getTel2())?inSuranceBean.getTel2():"");
                        dataJson.put("tel3",Strings.isNotEmpty(inSuranceBean.getTel3())?inSuranceBean.getTel3():"");
                    }
                    break;
                case 在线订单管理:
                case 收单管理:
                case 保险流水:
                    TransportInsurance insurance = tsInsuranceService.getById(id);
                    if(insurance!=null){
                        dataJson.put("cellPhone",Strings.isNotEmpty(insurance.getCellPhone())?insurance.getCellPhone():"");
                        dataJson.put("applicantPhone",Strings.isNotEmpty(insurance.getApplicantPhone())?insurance.getApplicantPhone():"");
                        dataJson.put("insuredPhone",Strings.isNotEmpty(insurance.getInsuredPhone())?insurance.getInsuredPhone():"");
                    }
                    break;
                case 车队维护管理:
                    List<TytCarteamPhone> carTeamPhones = carteamService.getCarTeamPhones(id);
                    if(carTeamPhones!=null&&carTeamPhones.size()>0){
                        TytCarteamPhone tytCarteamPhone = carTeamPhones.get(0);
                        dataJson.put("phone",Strings.isNotEmpty( tytCarteamPhone.getPhone())?tytCarteamPhone.getPhone():"");
                    }
                    break;
                case 手机号发货屏蔽控制:
                    CompeteProductCellphone competeProductCellphone = competeCellphoneService.getById(id);
                    if (competeProductCellphone!=null) {
                        dataJson.put("cellPhone",competeProductCellphone.getCellPhone());
                    }
                    break;
                case 用户账号变更记录:
                    AccountUpdateLog accountUpdateLog = accountUpdateLogService.getById(id);
                    if (accountUpdateLog!=null) {
                        dataJson.put("cellPhone",accountUpdateLog.getCellPhone());
                        dataJson.put("newPhone",accountUpdateLog.getNewPhone());
                    }
                    break;
                case 司机审核管理:
                    TytCarDriverArchives carDriverArchive = tytCarDriverArchivesService.getById(id);
                    if(carDriverArchive!=null){
                        dataJson.put("userPhone",carDriverArchive.getUserPhone());
                        dataJson.put("driverPhone",carDriverArchive.getDriverPhone());
                    }
                    break;
                case 货源管理:
                    List<TransportManageVO> dispatchById = transportDispatchService.getDispatchById(id);
                    if(!dispatchById.isEmpty()){
                        dataJson.put("uploadCellPhone",dispatchById.get(0).getUploadCellPhone());
                        dataJson.put("giveGoodsPhone",Strings.isNotEmpty(dispatchById.get(0).getGiveGoodsPhone())?dispatchById.get(0).getGiveGoodsPhone():"");
                    }
                    break;
                case 订单管理:
                    List<TransportOrdersVO> dispatchOrderById = transportDispatchService.getDispatchOrderById(id);
                    if(!dispatchOrderById.isEmpty()){
                        dataJson.put("uploadCellPhone",dispatchOrderById.get(0).getUploadCellphone());
                        dataJson.put("giveGoodsPhone",Strings.isNotEmpty(dispatchOrderById.get(0).getGiveGoodsPhone())?dispatchOrderById.get(0).getGiveGoodsPhone():"");
                        dataJson.put("payCellPhone",dispatchOrderById.get(0).getPayCellPhone());
                    }
                    break;
                case 绑定关系配置:
                    OwnerCompanyRelation ownerCompanyRelation = ownerCompanyRelationService.getById(id);
                    if(ownerCompanyRelation!=null){
                        dataJson.put("userPhone",ownerCompanyRelation.getUserPhone());
                    }
                    break;
                case 交易状态记列录:
                    TransactionStatusRecord transactionStatusRecord = transactionStatusRecordService.getTransactionById(id);
                    if(transactionStatusRecord!=null){
                        dataJson.put("telephone",transactionStatusRecord.getTelephone());
                    }
                    break;
                case 货源诈骗列表:
                    BlockInfo blockInfo = blockInfoService.getById(id);
                    if(blockInfo!=null){
                        dataJson.put("blockerTel",Strings.isNotEmpty(blockInfo.getBlockerTel())?blockInfo.getBlockerTel():"");
                        dataJson.put("cellPhone",Strings.isNotEmpty(blockInfo.getCellPhone())?blockInfo.getCellPhone():"");
                        dataJson.put("informerTel",Strings.isNotEmpty(blockInfo.getInformerTel())?blockInfo.getInformerTel():"");
                    }
                    break;
                case 新车资讯管理_PC:
                    NewCar newCar = newCarBackService.getById(id);
                    if(newCar!=null){
                        dataJson.put("telephone",Strings.isNotEmpty(newCar.getTelephone())?newCar.getTelephone():"");
                        dataJson.put("cellPhone",Strings.isNotEmpty(newCar.getCellPhone())?newCar.getCellPhone():"");
                    }
                    break;
                case 二手车信息管理_PC:
                    SecondCar secondCar = secondCarBackService.getById(id);
                    if(secondCar!=null){
                        dataJson.put("telephone",Strings.isNotEmpty(secondCar.getTelephone())?secondCar.getTelephone():"");
                        dataJson.put("cellPhone",Strings.isNotEmpty(secondCar.getCellPhone())?secondCar.getCellPhone():"");
                    }
                    break;
                case 带车信息管理_PC:
                    TakeCar takeCar = takeCarService.getById(id);
                    if(takeCar!=null){
                        dataJson.put("telephone",Strings.isNotEmpty(takeCar.getTelephone())?takeCar.getTelephone():"");
                        dataJson.put("cellPhone",Strings.isNotEmpty(takeCar.getCellPhone())?takeCar.getCellPhone():"");
                    }
                    break;
                case 新车资讯管理:
                    TytCarNews carNews = carNewsService.getById(id);
                    if(carNews!=null){
                        dataJson.put("factoryPhone1",Strings.isNotEmpty(carNews.getFactoryPhone1())?carNews.getFactoryPhone1():"");
                        dataJson.put("factoryPhone2",Strings.isNotEmpty(carNews.getFactoryPhone2())?carNews.getFactoryPhone2():"");
                    }
                    break;
                case 二手车审核列表:
                    TytUsedCarSale usedCarSale = usedCarSaleService.getById(id);
                    if(usedCarSale!=null){
                        dataJson.put("saleCellPhone",Strings.isNotEmpty(usedCarSale.getSaleCellPhone())?usedCarSale.getSaleCellPhone():"");
                    }
                    break;
                case 推广日志:
                    TytRecFriendLogBean recFriendLog = tytRecFriendLogService.getFriendLogById(id);
                    if(recFriendLog!=null){
                        dataJson.put("cellPhone",Strings.isNotEmpty(recFriendLog.getCellPhone())?recFriendLog.getCellPhone():"");
                        dataJson.put("recCellPhone",Strings.isNotEmpty(recFriendLog.getRecCellPhone())?recFriendLog.getRecCellPhone():"");
                    }
                    break;
                case 有奖用户管理:
                    TytPromotionWinner promotionWinner = promotionService.getById(id);
                    if(promotionWinner!=null){
                        dataJson.put("cellphone",Strings.isNotEmpty(promotionWinner.getCellphone())?promotionWinner.getCellphone():"");
                    }
                    break;
                case 活动查询:
                    UserInviteAwardInfoBean inviteAwardInfo = platUserInviteAwardInfoService.getInviteAwardInfoById(id);
                    if(inviteAwardInfo!=null){
                        dataJson.put("cellPhone",Strings.isNotEmpty(inviteAwardInfo.getCellPhone())?inviteAwardInfo.getCellPhone():"");
                        dataJson.put("inviteCellPhone",Strings.isNotEmpty(inviteAwardInfo.getInviteCellPhone())?inviteAwardInfo.getInviteCellPhone():"");
                    }
                    break;
                case 客户资料:
                    CustomInformation customInformation = customInfomationMapper.getCustomInformationById(id);
                    if(customInformation != null){
                        dataJson.put("goodsPhone", Strings.isNotEmpty(customInformation.getGoodsPhone()) ? customInformation.getGoodsPhone() : "");
                    }
                    break;
                default:
                    logger.info("getPhoneById【id:{}】【menuName:{}】不符合要求",id, menuEnum);
                    break;
            }
            tytManageQueryPhoneLogService.saveLog(queryPhoneLogQbean, curUser);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(dataJson);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg(e.getMessage());
            return resultMsgBean;
        }
    }
    @RequestMapping(value = "/saveCheckPhoneLogs")
    @ResponseBody
    public ResultMsgBean saveCheckPhoneLogs(HttpServletRequest request, TytManageQueryPhoneLogQbean queryPhoneLogQbean) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg(Constant.MSG_NOT_LOGIN);
            return resultMsgBean;
        }
        tytManageQueryPhoneLogService.saveLog(queryPhoneLogQbean, curUser);
        resultMsgBean.setCode(200);
        resultMsgBean.setMsg("查询成功");
        return resultMsgBean;

    }
}
