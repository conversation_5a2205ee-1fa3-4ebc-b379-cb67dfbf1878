package com.tyt.log.controller;

import java.util.Base64;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.tyt.util.TimeUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.log.qbean.TytManageCudLogQbean;
import com.tyt.log.service.TytManageCudLogService;
import com.tyt.model.PageBean;
import com.tyt.model.TytManageCudLog;
import com.tyt.model.TytSource;
import com.tyt.util.Constant;
import com.tyt.util.TytSourceUtil;
import com.tyt.web.base.BaseController;
/**
 * 操作记录管理
 * <AUTHOR>
 * @date 2017年8月11日
 */
@Controller
@RequestMapping("/boss/manage/opLog")
public class TytManageCudLogController extends BaseController{
	
	@Resource(name="tytManageCudLogService")
	TytManageCudLogService tytManageCudLogService;
	
	@RequestMapping(value = "/get")
	public String getList(HttpServletRequest request,HttpServletResponse response,
			TytManageCudLogQbean tytManageCudLog,Integer pageNo,Integer pageSize){
		// 登陆验证
		EmployeeQueryBean curUser = getCurrentUser(request);
		if (curUser==null) {
			request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
			return "back/jsp/admin_login";
		}	
		//分页处理
		if (pageNo == null || pageNo.intValue() <= 0)pageNo = 1;
		PageBean pageBean = new PageBean();
		pageBean.setCurrentPage(pageNo);
		if (pageSize == null || pageSize.intValue() <= 0)
			pageSize = Constant.DEFAULT_PAGE_SIZE;
		pageBean.setPageSize(pageSize);

		if(tytManageCudLog.getStartTime() == null){
			try {
				tytManageCudLog.setStartTime(TimeUtil.addDay(TimeUtil.today(),-3));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		List<TytManageCudLog> logs = tytManageCudLogService.getList(tytManageCudLog, pageBean);
		//遍历循环 列表数据原手机号字段置空，跟随该手机号配套对应的Base64加密的userId同时返回
		for (TytManageCudLog manageCudLog:logs){
			if(manageCudLog.getOpUserId()!=null){
				manageCudLog.setOpUserPhone(Base64.getEncoder().encodeToString(manageCudLog.getOpUserId().toString().getBytes()));
			}else{
				manageCudLog.setOpUserPhone(null);
			}
		}
		List<TytSource> opTypeList=TytSourceUtil.getSourceList("tyt_manage_log_optype");//销售核准身份Map
		request.setAttribute("opTypeList",opTypeList);
		request.setAttribute("logs",logs);
		request.setAttribute("log",tytManageCudLog);
		request.setAttribute("pageNo", pageNo);
		request.setAttribute("pageSize", pageSize);
		request.setAttribute("rowCount", pageBean.getRowCount());
		request.setAttribute("maxPage", pageBean.getMaxPage());
		return "back/jsp/tytManageCudLog";
		
	}
	

}
