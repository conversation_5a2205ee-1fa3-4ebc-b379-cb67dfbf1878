package com.tyt.log.qbean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomInformation {

    private Long id;

    /*
    * 货主手机号
    */
    private String goodsPhone;

    /*
     * 姓名
     */
    private String name;

    /*
     * 来源渠道枚举 0-自拓 1-市场 2-KA 3-其他
     */
    private Integer source;

    /*
     * 身份类型
     */
    private String indentityType;

    /*
     * 主要发货类型
     */
    private String publishGoodType;

    /*
     * 客户等级
     */
    private String customLevels;

    /*
     * 市场维护人员
     */
    private String marketMaintenancePersonnel;

    /*
     * 市场维护人员ID
     */
    private Long marketMaintenancePersonnelUserId;

    /*
     * 调度维护人员
     */
    private String dispatchMaintenancePersonnel;

    /*
     * 调度维护人员ID
     */
    private Long dispatchMaintenancePersonnelId;

    /*
     * 代调合作意向登记
     */
    private String dispatchIntentionLevels;

    /*
     * 详细地址
     */
    private String detailAddress;

    /*
     * 货主城市
     */
    private String goodsCity;

    /*
     * 公司名称
     */
    private String companyName;

    /*
     * 公司凭证url
     */
    private String companyVoucher;

    /*
     * 职位
     */
    private String position;

    /*
     * 手机号码归属地
     */
    private String mobileBelongingPlace;

    /*
     * 状态
     */
    private Integer status;

    /*
     * 创建时间
     */
    private Date createTime;

    /*
     * 创建人ID
     */
    private Long createUserId;

    /*
     * 创建人用户名
     */
    private String createUserName;

    /*
     * 更新时间
     */
    private Date updateTime;
}
