package com.tyt.log.service;


import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.log.qbean.TytManageCudLogQbean;
import com.tyt.model.PageBean;
import com.tyt.model.TytManageCudLog;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.StringUtil;


@Service("tytManageCudLogService")
public class TytManageCudLogServiceImpl extends BaseServiceImpl<TytManageCudLog,Long> implements TytManageCudLogService {
	 
	@Resource(name="tytManageCudLogDao")
	 public void setBaseDao(BaseDao<TytManageCudLog, Long> opLogDao) {
	     super.setBaseDao(opLogDao);
	 }

	@Override
	public List<TytManageCudLog> getList(TytManageCudLogQbean tytManageLog,
			PageBean pageBean) {
		StringBuffer countSQL=new StringBuffer("SELECT COUNT(*) FROM tyt_manage_cud_log entity where 1=1");
		StringBuffer conditionSQL=new StringBuffer();
		List<Object> params=new ArrayList<Object>();
		if(tytManageLog!=null){
			if(tytManageLog.getOpUserId()!=null){
				conditionSQL.append(" and entity.op_user_id=?");
				params.add(tytManageLog.getOpUserId());
			}
			if(StringUtil.isNotEmpty(tytManageLog.getOpUserPhone())){
				conditionSQL.append(" and entity.op_user_phone=?");
				params.add(tytManageLog.getOpUserPhone());
			}
			if(StringUtil.isNotEmpty(tytManageLog.getOpUrl())){
				conditionSQL.append(" and entity.op_url=?");
				params.add(tytManageLog.getOpUrl());
			}
			if(tytManageLog.getStartTime()!=null){
				conditionSQL.append(" and entity.record_time>=?");
				params.add(tytManageLog.getStartTime());
			}
			if(tytManageLog.getEndTime()!=null){
				conditionSQL.append(" and entity.record_time<=?");
				params.add(tytManageLog.getEndTime());
			}
			if(StringUtil.isNotEmpty(tytManageLog.getIp())){
				conditionSQL.append(" and entity.ip=?");
				params.add(tytManageLog.getIp());
			}
			if(tytManageLog.getOpType() != null){
				conditionSQL.append(" and entity.op_type=?");
				params.add(tytManageLog.getOpType());
			}
		}
		countSQL.append(conditionSQL);
		BigInteger count=this.getBaseDao().query(countSQL.toString(), params.toArray());
		if(count!=null&&count.longValue()>0){
			StringBuffer selectSQL=new StringBuffer(" SELECT * FROM tyt_manage_cud_log entity where 1=1 ");
			selectSQL.append(conditionSQL);
			selectSQL.append(" order by entity.id desc");
			pageBean.setRowCount(count.longValue());
			return this.getBaseDao().search(selectSQL.toString(), params.toArray(), 
					pageBean.getCurrentPage(), pageBean.getPageSize());
		}
		return null;
	}
	
}
