package com.tyt.looppicture.controller;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.tyt.service.user.TytSourceService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.looppicture.bean.LoopPictureBean;
import com.tyt.looppicture.service.HpLoopPictureService;
import com.tyt.model.HpLoopPicture;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.service.config.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.FtpUtil;
import com.tyt.util.RandomUtil;
import com.tyt.util.TimeUtil;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.KmConfig;

/**
 *
 * <AUTHOR>
 * @date 2017年10月11日上午11:27:37
 * @description
 */
@Controller
@RequestMapping("/hpLoopPicture")
public class HpLoopPictureController extends BaseController {
	@Resource(name = "hpLoopPictureService")
	private HpLoopPictureService hpLoopPictureService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name="tytSourceService")
	TytSourceService sourceService;
	/**
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/hpLoopPicturelist")
	public String hpLoopPicturelist(HttpServletRequest request) {
		logger.info("get hpLoopPicture list message");
		try {
			List<HpLoopPicture> hpLoopPicturesList = hpLoopPictureService.findPictureList(Constant.PICTURE_TYPE_LOOP+","+Constant.PICTURE_TYPE_LOOP_CAR_BUY+","+Constant.START_PICTURE_TYPE_LOOP+","+Constant.PICTURE_TYPE_LOOP_TRANSPORT, request);
			logger.info("get hpLoopPicture list message result is: " + hpLoopPicturesList);
			request.setAttribute("hpLoopPicturesList", hpLoopPicturesList);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("get hpLoopPicture list message failed, the error message is: " + e);
		}
		String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "https://www.teyuntong.com/");
		request.setAttribute("tytServerPictureUrl", tytServerPictureUrl);

		return "back/jsp/looppicture/looppicture_list";
	}

	@RequestMapping("/addHpLoopPicture")
	public String addHpLoopPicture() {
		return "back/jsp/looppicture/looppicture_add";
	}

	@RequestMapping("/addAdvertisePicture")
	public String addAdvertisePicture() {
		return "back/jsp/looppicture/advertise_add";
	}

	/**
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/hpAdvertisePicturelist")
	public String hpAdvertisePicturelist(HttpServletRequest request) {
		logger.info("get hpAdvertisePicture list message");
		try {
			List<HpLoopPicture> hpLoopPicturesList = hpLoopPictureService.findPictureList(Constant.PICTURE_TYPE_ADVERTISE+"", request);
			logger.info("get hpAdvertisePicture list message result is: " + hpLoopPicturesList);
			request.setAttribute("hpLoopPicturesList", hpLoopPicturesList);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("get hpAdvertisePicture list message failed, the error message is: " + e);
		}
		String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "https://www.teyuntong.com/");
		request.setAttribute("tytServerPictureUrl", tytServerPictureUrl);

		return "back/jsp/looppicture/advertise_list";
	}

	/**
	 * 保存轮播图
	 *
	 * @param loopPictureBean
	 * @param request
	 * @param response
	 * @param loopPicturePic
	 */
	@RequestMapping(value = "/saveLoopPicture")
	@ResponseBody
	public void saveLoopPicture(LoopPictureBean loopPictureBean, HttpServletRequest request, HttpServletResponse response, @RequestParam(required = false) MultipartFile loopPicturePic) {
		logger.info("loop picture save, request message is [ loopPictureBean: " + loopPictureBean + " ]");
		response.setContentType("text/html;charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		try {
			Short type;
			if(loopPictureBean.getDisplaySite()==3||loopPictureBean.getDisplaySite()==4) {
				type = Constant.PICTURE_TYPE_LOOP_CAR_BUY;
			}else if(loopPictureBean.getDisplaySite()==5){
				type = Constant.START_PICTURE_TYPE_LOOP;
			}else if(loopPictureBean.getDisplaySite()==8 ||loopPictureBean.getDisplaySite()==9 ||loopPictureBean.getDisplaySite()==10){
				type = Constant.PICTURE_TYPE_LOOP_TRANSPORT;
			}else {
				type = Constant.PICTURE_TYPE_LOOP;
			}
			EmployeeQueryBean employeeQueryBean = getCurrentUser(request);
			HpLoopPicture loopPicture = createLoopPicture(true, loopPictureBean, loopPicturePic, employeeQueryBean.getId(), type);
			hpLoopPictureService.add(loopPicture);
			deleteCacheKey();
			//刷新APP配置版本号
			//修改遗留bug,如果是type=3板车购买类型 刷新板车购买版本号
			if(type==3){
				sourceService.updateRefreshSourceVersion("carNewsResource");
			}
			sourceService.updateRefreshSourceVersion("globalResource");
			response.getWriter().write("<script>alert('保存成功，稍后将关闭此窗口');window.opener.location.reload();window.close();</script>");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			try {
				response.getWriter().write("<script>alert('保存失败，请重试');window.close();</script>");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 保存首页宣传图
	 *
	 * @param loopPictureBean
	 * @param request
	 * @param response
	 * @param advertisePicturePic
	 */
	@RequestMapping(value = "/saveAdvertisePicture")
	@ResponseBody
	public void saveAdvertisePicture(LoopPictureBean advertisePictureBean, HttpServletRequest request, HttpServletResponse response, @RequestParam(required = false) MultipartFile advertisePicturePic) {
		logger.info("advertise picture save, request message is [ advertisePictureBean: " + advertisePictureBean + " ]");
		response.setContentType("text/html;charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		try {
			EmployeeQueryBean employeeQueryBean = getCurrentUser(request);
			HpLoopPicture loopPicture = createLoopPicture(true, advertisePictureBean, advertisePicturePic, employeeQueryBean.getId(), (short) Constant.PICTURE_TYPE_ADVERTISE);
			hpLoopPictureService.add(loopPicture);
			deleteCacheKey();
			//刷新APP配置版本号
			sourceService.updateRefreshSourceVersion("globalResource");
			response.getWriter().write("<script>alert('保存成功，稍后将关闭此窗口');window.opener.location.reload();window.close();</script>");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			try {
				response.getWriter().write("<script>alert('保存失败，请重试');window.close();</script>");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	@RequestMapping("/changeStatus")
	@ResponseBody
	public ResultMsgBean changeStatus(long id, int status, Integer type) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			boolean isAllowChange = true;
			if (type != null && type.intValue() == 1 && status == 1) {
				// 获取首页轮播图当前启用的数量
				int enabledNum = hpLoopPictureService.getEnabledNum(type);
				// 获取朔首页轮播图最大允许的启用数量
				int maxAllowEndableNum = tytConfigService.getIntValue(Constant.ADVERTISE_PICTURE_MAX_ENABLE_NUM, 10);
				if (enabledNum >= maxAllowEndableNum) {
					isAllowChange = false;
				}
			}
			if (!isAllowChange) {
				result.setCode(300);
			} else {
				hpLoopPictureService.updateOpenClose(id, status);
				deleteCacheKey();
				//刷新APP配置版本号
				//修改遗留bug,如果是type=3板车购买类型 刷新板车购买版本号
				sourceService.updateRefreshSourceVersion("carNewsResource");
				sourceService.updateRefreshSourceVersion("globalResource");
				// 获取结果
				result.setCode(200);
				result.setMsg("操作成功");
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(400);
			result.setMsg("操作失败");
		}
		return result;
	}

	/**
	 * 编辑轮播图
	 *
	 * @param request
	 * @param id
	 * @return
	 */
	@RequestMapping("/findLoopPicture")
	public String findLoopPicture(HttpServletRequest request, Long id) {
		logger.info("find loopPicture id is: " + id);
		try {
			HpLoopPicture loopPicture = hpLoopPictureService.getById(id);
			logger.info("find loopPicture id is: " + id + ", result is: " + loopPicture);
			request.setAttribute("loopPicture", loopPicture);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("get hpAdvertisePicture list message failed, the error message is: " + e);
		}
		String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "https://www.teyuntong.com/");
		request.setAttribute("tytServerPictureUrl", tytServerPictureUrl);

		return "back/jsp/looppicture/looppicture_edit";
	}

	/**
	 * 查找品宣图
	 *
	 * @param request
	 * @param id
	 * @return
	 */
	@RequestMapping("/findAdvertise")
	public String findAdvertise(HttpServletRequest request, Long id) {
		logger.info("find findAdvertise id is: " + id);
		try {
			HpLoopPicture findAdvertise = hpLoopPictureService.getById(id);
			logger.info("find findAdvertise id is: " + id + ", result is: " + findAdvertise);
			request.setAttribute("findAdvertise", findAdvertise);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("get hpAdvertisePicture list message failed, the error message is: " + e);
		}
		String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "https://www.teyuntong.com/");
		request.setAttribute("tytServerPictureUrl", tytServerPictureUrl);

		return "back/jsp/looppicture/advertise_edit";
	}

	/**
	 * 更新轮播图
	 *
	 * @param loopPictureBean
	 * @param request
	 * @param response
	 * @param loopPicturePic
	 */
	@RequestMapping(value = "/updateLoopPicture")
	@ResponseBody
	public void updateLoopPicture(LoopPictureBean loopPictureBean, HttpServletRequest request, HttpServletResponse response, @RequestParam(required = false) MultipartFile loopPicturePic) {
		logger.info("loop picture update, request message is [ loopPictureBean: " + loopPictureBean + " ]");
		response.setContentType("text/html;charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "https://www.teyuntong.com/");
		request.setAttribute("tytServerPictureUrl", tytServerPictureUrl);
		try {
			EmployeeQueryBean employeeQueryBean = getCurrentUser(request);
			Short type;
			if(loopPictureBean.getDisplaySite()==3||loopPictureBean.getDisplaySite()==4) {
				type = Constant.PICTURE_TYPE_LOOP_CAR_BUY;
			}else if(loopPictureBean.getDisplaySite()==5){
				type = Constant.START_PICTURE_TYPE_LOOP;
			}else if(loopPictureBean.getDisplaySite()==8 ||loopPictureBean.getDisplaySite()==9 ||loopPictureBean.getDisplaySite()==10){
				type = Constant.PICTURE_TYPE_LOOP_TRANSPORT;
			}else {
				type = Constant.PICTURE_TYPE_LOOP;
			}
			HpLoopPicture loopPicture = createLoopPicture(false, loopPictureBean, loopPicturePic, employeeQueryBean.getId(),type);
			hpLoopPictureService.update(loopPicture);
			deleteCacheKey();
			//刷新APP配置版本号
			//修改遗留bug,如果是type=3板车购买类型 刷新板车购买版本号
			if(type==3){
				sourceService.updateRefreshSourceVersion("carNewsResource");
			}
			sourceService.updateRefreshSourceVersion("globalResource");
			response.getWriter().write("<script>alert('更新成功，稍后将关闭此窗口');window.opener.location.reload();window.close();</script>");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			try {
				response.getWriter().write("<script>alert('更新失败，请重试');window.close();</script>");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 更新首页广告图
	 *
	 * @param loopPictureBean
	 * @param request
	 * @param response
	 * @param loopPicturePic
	 */
	@RequestMapping(value = "/updateAdvertisePicture")
	@ResponseBody
	public void updateAdvertisePicture(LoopPictureBean loopPictureBean, HttpServletRequest request, HttpServletResponse response, @RequestParam(required = false) MultipartFile advertisePicturePic) {
		logger.info("advertise picture update, request message is [ loopPictureBean: " + loopPictureBean + " ]");
		response.setContentType("text/html;charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "https://www.teyuntong.com/");
		request.setAttribute("tytServerPictureUrl", tytServerPictureUrl);
		try {
			EmployeeQueryBean employeeQueryBean = getCurrentUser(request);
			HpLoopPicture loopPicture = createLoopPicture(false, loopPictureBean, advertisePicturePic, employeeQueryBean.getId(), (short) Constant.PICTURE_TYPE_ADVERTISE);
			hpLoopPictureService.update(loopPicture);
			deleteCacheKey();
			//刷新APP配置版本号
			sourceService.updateRefreshSourceVersion("globalResource");
			response.getWriter().write("<script>alert('更新成功，稍后将关闭此窗口');window.opener.location.reload();window.close();</script>");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			try {
				response.getWriter().write("<script>alert('更新失败，请重试');window.close();</script>");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 删除轮播图
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping("/deleteLoopPicture")
	@ResponseBody
	public ResultMsgBean deleteLoopPicture(Long id) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			hpLoopPictureService.deleteLoopPicture(id);
			deleteCacheKey();
			//刷新APP配置版本号
			//修改遗留bug,刷新板车购买版本号
			sourceService.updateRefreshSourceVersion("carNewsResource");
			sourceService.updateRefreshSourceVersion("globalResource");
			// 获取结果
			result.setCode(200);
			result.setMsg("删除成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(400);
			result.setMsg("删除失败");
		}
		return result;
	}

	private HpLoopPicture createLoopPicture(boolean isSave, LoopPictureBean loopPictureBean, MultipartFile loopPicturePic, Long loginUserId, short pictureTypep) throws IllegalStateException, IOException {
		HpLoopPicture loopPicture = new HpLoopPicture();
		BeanUtils.copyProperties(loopPictureBean, loopPicture);
		// 开启状态默认关闭 开启状态0是关闭1是开启
		loopPicture.setOpenClose(Constant.OPEN_CLOSE_STATUS_CLOSE);
		// 设置状态为正常 状态0正常1是删除
		loopPicture.setStatus(Constant.LOOP_PICTURE_STATUS_OK);
		// 设置操作用户id
		loopPicture.setUserId(loginUserId);
		loopPicture.setType(pictureTypep);
		loopPicture.setUtime(new Date());
		if (isSave)
			loopPicture.setCtime(new Date());
		// 上传图片并设置图片地址
		if (loopPicturePic != null && !loopPicturePic.isEmpty()) {
			String originalFileName = loopPicturePic.getOriginalFilename();
			String fileSuffix = originalFileName.substring(originalFileName.lastIndexOf("."));
			// 生成文件名
			String fileName = System.currentTimeMillis() + "_" + RandomUtil.getSixRandom() + fileSuffix;
			// 生成图片数据库地址
			String dbPath = tytConfigService.getStringValue(Constant.LOOP_PICTURE_207_DB_PATH, "/uploadimg/loopPicture/");
			loopPicture.setPictureUrl(dbPath + fileName);
			// 获取上传到207的目录
			String uploadPath = tytConfigService.getStringValue(Constant.LOOP_PICTURE_207_FULL_PATH, "/loopPicture/");
			FtpUtil.upLoadFileFtp2(new KmConfig(), loopPicturePic.getInputStream(), uploadPath + fileName);
		} else {
			if (!isSave) {
				loopPicture.setPictureUrl(loopPictureBean.getPictureUrlOld());
			}
		}
		if (!isSave) {
			HpLoopPicture hpLoopPictureOld = hpLoopPictureService.getById(loopPictureBean.getId());
			loopPicture.setCtime(hpLoopPictureOld.getCtime());
			BeanUtils.copyProperties(loopPicture, hpLoopPictureOld);
			loopPicture = hpLoopPictureOld;
		}
		return loopPicture;
	}

	public void deleteCacheKey() {
		String value = tytConfigService.getStringValue("appHomePageLoopPicture", "app_home_page_loop_picture_{date}");
		String key = StringUtils.replaceEach(value, new String[] { "{date}" }, new String[] { TimeUtil.formatDate_(new Date()) });
		logger.info("loop picture controller delete from redis key is: " + key);
		RedisUtil.del(key);
	}
}
