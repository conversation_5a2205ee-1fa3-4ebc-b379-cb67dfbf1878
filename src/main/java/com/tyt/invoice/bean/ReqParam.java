package com.tyt.invoice.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReqParam implements Serializable {

    private Long tsOrderNo;

    private String orderNewStatus;

    private Integer costStatus;

    private Long orderId;

    private Long payUserId;

    private Long carId;
    private Long tsId;
    /**
     *  发货人姓名
     */
    private String publishGoodsName;

    private String pubUserName;

    private String headNo;

    /**
     * 开票主体编码
     */
    private String  invoiceIssuerCode;
    /**
     *  超限证是否上传
     */
    private String OverLimitVoucher;
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;//接单时间
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startUpdateTime;
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endUpdateTime;
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startPayTime;
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endPayTime;

    private int pageNum = 1;
    private int pageSize = 20 ;

    private Long businessId;
    private int fileType;
    private String  enterpriseName;
    private String urlTypeStr;

    private String[] orderNewStatusTemp;

    private Integer invoiceTransport;//是否网络货运 0：非开票；1:专票

    private Integer riskControlStatus;//风控状态 0未检测、1正常单、2待申诉、3待审核、4申诉通过、5申诉不通过、6超过申诉期 7 虚假单
    private String driverName;// 司机姓名

    private int  urlType; //1 装车图片 2.卸车图片 3.回单图片 4.超限图片 5.补充凭证

    private int innerSwitch;// 1 - 协议未签署不需要查询托运协议 2 - 协议签需要查询托运协议

    private int operate;//1,通过 2.驳回

    private String headNum;
    private String headStr;

    private Integer searchType; // 1 正常列表 2 申诉列表

    private String Reason;//转普通单等 原因 用于发短不进行落库操作。

    private Integer opType;

    private String refundReason;
    private Long refundAmount;

    private String cellPhone;//登陆人用户手机号

    private int operateType;

    private String remark;
   private String headCity;
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date beginTime;
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    private Integer freightStatus;

    private Integer invoiceStatus;
    private Integer dataType;

    private Integer isAssignOrder;

}
