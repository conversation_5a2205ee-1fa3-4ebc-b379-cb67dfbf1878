package com.tyt.tpay.bean;

import com.tyt.goods.bean.FinancialData;
import com.tyt.goods.bean.UserBuyGoodsListBean;
import com.tyt.model.OrdersPay;
import lombok.Data;

import java.util.List;

/**
 * @ClassName OrdersCollectBean
 * @Description 支付订单和财务数据集合
 * <AUTHOR>
 * @Date 2022-07-07 11:12
 * @Version 1.0
 */
@Data
public class OrdersCollectBean {

    private List<OrdersPay> list;

    private FinancialData financialData;
}
