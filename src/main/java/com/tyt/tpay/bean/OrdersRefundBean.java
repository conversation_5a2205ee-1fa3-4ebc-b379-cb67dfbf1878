package com.tyt.tpay.bean;

import lombok.Data;

import java.util.Date;

@Data
public class OrdersRefundBean {

    /**
     * 退款订单号x
     */
    private String refundOrderNo;

    /**
     * 业务订单ID
     */
    private String businessNo;

    /**
     * 订单类型 会员费 信息费 XXX
     */
    private String orderType;

    /**
     * 商品名称或主题
     */
    private String subject;

    /**
     * 支付状态(1-待处理,2-退款成功,3-退款失败)
     */
    private Integer status;

    /**
     * 支付开始时间
     */
    private Date startRefundTime;

    /**
     * 支付结束时间
     */
    private Date endRefundTime;
}
