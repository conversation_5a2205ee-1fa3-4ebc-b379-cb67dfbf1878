package com.tyt.tpay.service.impl;

import com.tyt.dao.base.BaseDao;
import com.tyt.goods.bean.FinancialData;
import com.tyt.model.OrdersPay;
import com.tyt.model.PageBean;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.tpay.bean.OrdersCollectBean;
import com.tyt.tpay.bean.OrdersDict;
import com.tyt.tpay.bean.OrdersPayBean;
import com.tyt.tpay.service.OrdersPayService;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrdersPayServiceImpl
 * @description 支付订单服务层实现类
 * @date 2022-07-06 17:25
 */
@Service(value = "ordersPayService")
public class OrdersPayServiceImpl extends BaseServiceImpl <OrdersPay, Long> implements OrdersPayService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    @Resource(name = "ordersPayDao")
    public void setBaseDao(BaseDao <OrdersPay, Long> ordersPayDao) {
        super.setBaseDao(ordersPayDao);
    }

    /**
     * @description 获取支付订单集合
     * <AUTHOR>
     * @date 2022/7/7 11:16
     * @param ordersPayBean
     * @param pageBean
     * @return com.tyt.tpay.bean.OrdersCollectBean
     */
    @Override
    public OrdersCollectBean getOrdersPayList(OrdersPayBean ordersPayBean, PageBean pageBean) throws Exception {
        OrdersCollectBean collectBean = new OrdersCollectBean();

        String orderNo = ordersPayBean.getOrderNo();
        String businessNo = ordersPayBean.getBusinessNo();
        String orderType = ordersPayBean.getOrderType();
        String subject = ordersPayBean.getSubject();
        Integer status = ordersPayBean.getStatus();
        Date startPayTime = ordersPayBean.getStartPayTime();
        Date endPayTime = ordersPayBean.getEndPayTime();

        //拼接查询支付订单列表信息的sql语句
        StringBuffer sbSql = new StringBuffer("SELECT " +
                " o.id id, " +
                " o.order_no orderNo, " +
                " o.total_amount totalAmount, " +
                " o.merchant_order_no  merchantOrderNo, " +
                " o.business_no  businessNo, " +
                " o.third_trade_no thirdTradeNo,  " +
                " o.user_id userId, " +
                " o.status status, " +
                " o.pay_time payTime, " +
                " o.trade_time tradeTime, " +
                " o.subject subject, " +
                " o.order_type orderType, " +
                " o.ctime ctime, " +
                " o.mtime mtime " +
                "FROM " +
                " tpay_pay.orders_pay o  " +
                "WHERE " +
                " 1 =1 ");
        //拼接传入的查询参数
        List<Object> listObject = new ArrayList <Object>();
        //拼接查询条件的sql
        StringBuffer contionSql = new StringBuffer();
        if(StringUtils.isNotBlank(orderNo)) {
            contionSql.append(" and o.order_no = ? ");
            listObject.add(orderNo);
        }
        if(StringUtils.isNotBlank(businessNo)) {
            contionSql.append(" and o.business_no = ? ");
            listObject.add(businessNo);
        }
        if(StringUtils.isNotBlank(orderType)) {
            contionSql.append(" and o.order_type = ? ");
            listObject.add(orderType);
        }
        if(StringUtils.isNotBlank(subject)) {
            contionSql.append(" and o.subject = ? ");
            listObject.add(subject);
        }
        if(status != null){
            contionSql.append(" and o.status = ? ");
            listObject.add(status);
        }
        if(startPayTime != null){
            contionSql.append(" and o.pay_time >= ? ");
            listObject.add(TimeUtil.weeHours(startPayTime,0));
        }
        if (endPayTime != null){
            contionSql.append(" and o.pay_time < ? ");
            try {
                listObject.add(TimeUtil.weeHours(endPayTime,1));
            } catch (Exception e) {}
        }
        //根据支付订单id降序排序
        contionSql.append(" order by o.id desc ");
        //查询语句拼接上面的查询条件
        sbSql.append(contionSql);

        //获取商品订单总数量的sql语句
        StringBuffer sbCount = new StringBuffer("select count(*) from tpay_pay.orders_pay o where 1 = 1 ");
        sbCount.append(contionSql);
        //查询总条数
        BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), listObject.toArray());
        pageBean.setRowCount(rowCount.intValue());

        //查询财务数据的sql语句
        StringBuffer financialSql = new StringBuffer("select count(*) paySuccessCount,sum(o.total_amount) paySuccessAmount " +
                " from tpay_pay.orders_pay o where 1 = 1 ");
        financialSql.append(contionSql);
        Map <String, Type> scalarMap = new HashMap <String, Type>();
        scalarMap.put("paySuccessCount",Hibernate.INTEGER);
        scalarMap.put("paySuccessAmount",Hibernate.BIG_DECIMAL);
        List<FinancialData> financialDataList = this.getBaseDao().search(financialSql.toString(), scalarMap, FinancialData.class, listObject.toArray());
        if(financialDataList != null && financialDataList.size()>0){
            FinancialData financialData = financialDataList.get(0);
            collectBean.setFinancialData(financialData);
        }
        //查询结果转换的类型
        Map<String, Type> map = new HashMap<String, Type>();
        map.put("id", Hibernate.LONG);
        map.put("orderNo", Hibernate.STRING);
        map.put("totalAmount",Hibernate.BIG_DECIMAL);
        map.put("merchantOrderNo",Hibernate.STRING);
        map.put("businessNo",Hibernate.STRING);
        map.put("thirdTradeNo",Hibernate.STRING);
        map.put("userId",Hibernate.STRING);
        map.put("status",Hibernate.INTEGER);
        map.put("payTime",Hibernate.TIMESTAMP);
        map.put("tradeTime",Hibernate.TIMESTAMP);
        map.put("subject",Hibernate.STRING);
        map.put("orderType",Hibernate.STRING);
        map.put("ctime",Hibernate.TIMESTAMP);
        map.put("mtime",Hibernate.TIMESTAMP);

        //执行查询语句,转换成支付订单记录查询对象,进行分页
        List<OrdersPay> list = this.getBaseDao().search(sbSql.toString(), map,
                OrdersPay.class, listObject.toArray(),pageBean.getCurrentPage(),pageBean.getPageSize());
        collectBean.setList(list);
        return collectBean;
    }

    /**
     * @description csv文件头部数据与列表数据拼接的方法
     * <AUTHOR>
     * @date 2022/7/7 14:28
     * @param content
     * @param ordersPayList
     * @return void
     */
    @Override
    public void ordersPayToCvsString(StringBuilder content, List <OrdersPay> ordersPayList) {
        // 行分隔符
        String CSV_RN = "\r\n";
        for (OrdersPay ordersPay : ordersPayList) {
            //ID,业务类型,业务订单ID,用户ID,商户订单ID,商品名称,支付金额,支付状态,订单创建时间,支付时间
            //ID
            content.append(ordersPay.getId() == null?"":ordersPay.getId()).append(",");
            //业务类型
            content.append(ordersPay.getOrderType() == null?"":ordersPay.getOrderType()).append(",");
            //业务订单ID
            content.append(ordersPay.getBusinessNo() == null?"":ordersPay.getBusinessNo()+"\t").append(",");
            //用户ID
            content.append(ordersPay.getUserId() == null?"":ordersPay.getUserId()).append(",");
            //商户订单ID
            content.append(ordersPay.getOrderNo() == null?"":ordersPay.getOrderNo()+"\t").append(",");
            //商品名称
            content.append(ordersPay.getSubject() == null?"":ordersPay.getSubject()).append(",");
            //支付金额
            content.append(ordersPay.getTotalAmount() == null?"0.00":ordersPay.getTotalAmount()).append(",");
            //支付状态
            String payStatus = "";
            if(ordersPay.getStatus() != null){
                 if(1 == ordersPay.getStatus()){
                     payStatus = "待支付";
                 }else if(2 == ordersPay.getStatus()){
                     payStatus = "支付成功";
                 }else if(3 == ordersPay.getStatus()){
                     payStatus = "支付失败";
                 }
            }
            content.append(payStatus).append(",");
            //订单创建时间
            Date ctime = ordersPay.getCtime();
            appendTimeStamp(content, ctime, true);
            //支付时间
            Date tradeTime = ordersPay.getTradeTime();
            appendTimeStamp(content, tradeTime, false);
            content.append(CSV_RN);
        }
    }


    /**
     * @Description  导出内容拼接时间戳日期格式
     * <AUTHOR>
     * @Date  2022/7/7 15:18
     * @Param [content, timeStamp, commaFlag]
     *         commaFlag: 字符串后面是否拼接逗号 true:是 false:否
     * @return void
     **/
    private void appendTimeStamp(StringBuilder content, Date date,Boolean commaFlag) {
        if (date != null) {
            if(commaFlag){
                content.append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date)).append(",");
            }else{
                content.append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date));
            }
        } else {
            if(commaFlag){
                content.append(",");
            }else{
            }
        }
    }

    /**
     * @description 获取订单字典集合
     * <AUTHOR>
     * @date 2022/7/7 15:59
     * @param
     * @return com.tyt.tpay.bean.OrdersDict
     */
    @Override
    public OrdersDict getOrderDict() throws Exception{
        //订单字典对象
        OrdersDict ordersDict = new OrdersDict();
        String sql = "select order_type from tpay_pay.orders_pay group by order_type";
        List<String> orderTypeList = this.getBaseDao().findAll(sql, new Object[]{});
//
//        sql = "select subject from tpay_pay.orders_pay group by subject";
//        List<String> subjectList = this.getBaseDao().findAll(sql, new Object[]{});

        ordersDict.setOrderTypeList(orderTypeList);
        ordersDict.setSubjectList(null);
        return ordersDict;
    }
}
