package com.tyt.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.common.service.TytNoticeRemindTmplService;
import com.tyt.common.service.TytSequenceService;
import com.tyt.dao.base.BaseDao;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.TransportOrderResultBean;
import com.tyt.infofee.service.TradeInfoService;
import com.tyt.message.mqservice.IMqProducer;
import com.tyt.model.*;
import com.tyt.payment.bean.FinancialData;
import com.tyt.payment.service.OrderService;
import com.tyt.payment.util.MakeOrderNum;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.service.user.TytUserAccountService;
import com.tyt.service.user.UserService;
import com.tyt.util.*;
import com.tyt.web.back.internal.bean.GoodBean;
import com.tyt.web.back.internal.bean.MqMoneyRefundMsg;
import com.tyt.web.back.internal.bean.OrderQueryBean;
import com.tyt.web.back.internal.bean.TransportOrderBean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("orderService")
public class OrderServiceImpl extends BaseServiceImpl<Order, Long> implements OrderService {

	private final Logger payLog = LoggerFactory.getLogger("payLog");

	private static final String TABLE_TRANSPORT_ORDERS_NAME = "tyt_transport_orders";

	@Resource(name = "tytSequenceService")
	private TytSequenceService sequenceService;
	@Resource(name = "tytNoticeRemindService")
	private TytNoticeRemindService tytNoticeRemindService;
	@Resource(name = "tytNoticeRemindTmplService")
	private TytNoticeRemindTmplService tytNoticeRemindTmplService;
	@Autowired
	private IMqProducer mqProducer;
	@Resource(name = "userService")
	private UserService userService;
	@Resource(name = "tytUserAccountService")
	private TytUserAccountService userAccountService;
	@Resource(name = "tradeInfoService")
	private TradeInfoService tradeInfoService;

	private static final String TABLE_TRANSPORT_WAYBILL_NAME = "tyt_transport_waybill";
	private static final String VIRTUAL_USER_ACCOUNT = "***********";

	@Resource(name = "orderDao")
	public void setBaseDao(BaseDao<Order, Long> orderDao) {
		super.setBaseDao(orderDao);
	}

	@Override
	public Order getOrder(Price price, User user, String payChannel, Integer platId) throws Exception {

		/* 判断数据库是否有该条记录 */
		if (price == null) {
			throw new Exception("mysql priceId无有效对应记录.");
		}
		if (user == null) {
			throw new Exception("mysql cellPhone无效.");
		}
		/* 生成Order对象 */
		Order order = new Order();
		order.setOrderId(MakeOrderNum.getOrderNo());
		order.setCellPhone(user.getCellPhone());
		order.setPayStatus(user.getUserType() == User.USER_TYPE_VIP ? User.PAY_STATUS_RENEWAL : User.PAY_STATUS_FIRST);
		order.setPayMethod(payChannel);
		order.setPlatId(platId);
		order.setStatus(Order.ORDER_STATUS_NO_PAY);
		order.setCtime(TimeUtil.getTimeStamp());
		order.setMtime(TimeUtil.getTimeStamp());
		order.setRenewalYears(price.getYears());
		order.setTotalFee(price.getPrice());
		return order;
	}

	@Override
	public Order getByIdAndAmount(String orderId, Integer amount) {
		StringBuffer sql = new StringBuffer();
		sql.append(" entity.orderId='").append(orderId).append("'");
		sql.append(" and entity.totalFee=").append(amount);
		List<Order> orders = this.getList(sql.toString(), null);
		if (orders.size() > 0)
			return orders.get(0);
		return null;
	}

	@Override
	public Order getByOrderId(String orderId) throws Exception {
		StringBuffer sql = new StringBuffer();
		sql.append(" entity.orderId ='").append(orderId.trim()).append("'");
		List<Order> orderList = getList(sql.toString(), null);

		Order order = null;
		if (orderList != null && orderList.size() > 0) {
			order = orderList.get(0);
		}
		return order;
	}

	@Override
	public Order updateOrderById(Integer status, Long id, String opType, String note) {
		/* 更新sql语句 */
		StringBuffer sbSql = new StringBuffer("update tyt_old_order set status=?," + "op_type=?,note=?,mtime=? " + "where id=? ");
		/* 占位符参数 */
		List<Object> params = new ArrayList<Object>();
		params.add(status);
		params.add(opType);
		params.add(note);
		params.add(TimeUtil.getTimeStamp());
		params.add(id);
		try {
			this.executeUpdateSql(sbSql.toString(), params.toArray());
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			// 提醒垃圾回收
			if (params != null)
				params = null;
			if (sbSql != null)
				sbSql = null;
		}
		return this.getById(id);
	}

	@Override
	public Order updateOrderById(Integer status, Long id) {
		payLog.info("更新订单开始【id:{}】", id);
		/* 更新sql语句 */
		StringBuffer sbSql = new StringBuffer("update tyt_old_order set status=?,mtime=? " + "where id=? and status=0");
		/* 占位符参数 */
		List<Object> params = new ArrayList<Object>();
		params.add(status);
		params.add(TimeUtil.getTimeStamp());
		params.add(id);
		/* 如果已经没有可以修改的东西，即条数为0，则就不要继续下一步了 */
		int c = 0;
		try {

			c = this.executeUpdateSql(sbSql.toString(), params.toArray());
			if (c <= 0)
				return null;
		} catch (Exception e) {
			e.printStackTrace();
			payLog.info("更新订单【id:{}】,【msg:{}】", id, "异常:" + e.toString());
			return null;
		} finally {
			// 提醒垃圾回收
			if (params != null)
				params = null;
			if (sbSql != null)
				sbSql = null;
		}
		payLog.info("更新订单【id:{}】条数【{}】", id, c);
		if (c <= 0)
			return null;
		return this.getById(id);
	}

	@SuppressWarnings("deprecation")
	@Override
	public TransportOrderBean getOrderById(String goodPayId) {
		String sql = "SELECT tto.id, tto.pay_amount AS 'payAmount', tto.ts_id AS 'transportId', tto.ts_order_no AS 'transportOrderNum', tto.`pay_cell_phone` AS 'payCellPhone', tto.`pay_link_phone` AS 'payLinkPhone', tto.`pay_user_id` AS 'payUserId', tto.`pay_end_time` AS 'payEndTime', tto.`pay_type` AS 'payType', tto.`pay_status` AS 'payStatus', tto.`pay_fee_amount` AS 'payFeeAmount', tto.`user_id` AS 'userId' FROM tyt_transport_orders tto WHERE tto.id=:orderId";
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("orderId", goodPayId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.STRING);
		scalarMap.put("payAmount", Hibernate.INTEGER);
		scalarMap.put("transportOrderNum", Hibernate.STRING);
		scalarMap.put("payCellPhone", Hibernate.STRING);
		scalarMap.put("payLinkPhone", Hibernate.STRING);
		scalarMap.put("payUserId", Hibernate.STRING);
		scalarMap.put("payStatus", Hibernate.INTEGER);
		scalarMap.put("payType", Hibernate.INTEGER);
		scalarMap.put("payEndTime", Hibernate.STRING);
		scalarMap.put("payFeeAmount", Hibernate.STRING);
		scalarMap.put("userId", Hibernate.INTEGER);
		scalarMap.put("transportId", Hibernate.INTEGER);
		TransportOrderBean orderBean = this.getBaseDao().search(sql, scalarMap, TransportOrderBean.class, paramMap).get(0);
		return orderBean;
	}

	private static final int TRADE_TYPE_PAY_INFOFEE = 1;
	private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@Override
	public int addTradeInfo(Integer payAmount, Order order, String infoFeeOrderId) {
		String sql = "INSERT INTO `tyt`.`tyt_trade_info` (`trade_type`, `trade_time`, `update_time`, `trade_account`, `allow_pay_back_refund`, `refunding_account`, `good_info_fee_id`, `payer_user_id`, `order_id`, `pay_receiver_user_id`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		Object[] params = new Object[] { TRADE_TYPE_PAY_INFOFEE, sdf.format(new Date()), sdf.format(new Date()), payAmount, payAmount, 0, infoFeeOrderId, order.getUserId(), order.getOrderId(), order.getUserId() };
		this.getBaseDao().executeUpdateSql(sql, params);
		sql = "SELECT MAX(id) FROM tyt_trade_info";
		Integer maxId = this.getBaseDao().query(sql, new Object[] {});
		return maxId.intValue() + 1;
	}

	@Override
	public void updateGoodOrderStatus(String infoFeeOrderId, int infofeeOrderPayStatus, int infofeeOrderRobStatus, String orderNumber, Integer payType, Date payEndTime) {
		/*
		 * 获取最大的sort_id
		 */
		Long sortId = updateGetNextSequenceNbr(TABLE_TRANSPORT_ORDERS_NAME);
		String sql = "UPDATE tyt_transport_orders SET sort_id=?, pay_status=?, rob_status=?, pay_no=?, mtime=NOW(), pay_end_time=?, pay_type=? WHERE id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { sortId, infofeeOrderPayStatus, infofeeOrderRobStatus, orderNumber, payEndTime, payType, infoFeeOrderId });
	}

	@Override
	public Long updateGetNextSequenceNbr(String sequenceName) {
		String sql = "update tyt_sequence set number=number+1 where name=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { sequenceName });
		TytSequence tytSequence = sequenceService.getById(sequenceName);
		return tytSequence.getNumber();
	}

	@Override
	public void updateOrderStatus(Long id, int status, String thirdpartyOrderSerialNum) {
		String sql = "UPDATE tyt_old_order SET STATUS=?, thirdparty_order_serial_num=?, op_type=2 WHERE id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { status, thirdpartyOrderSerialNum, id });
	}
    
	/**
	 * @Description  修改订单状态
	 * <AUTHOR>
	 * @Date  2019/7/17 16:53
	 * @Param [id, status]
	 * @return void
	 **/
	@Override
	public void updateOrderStatus(Long id, int status) {
		String sql = "UPDATE tyt_old_order SET STATUS=? WHERE id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { status, id });
	}
	
	@SuppressWarnings("deprecation")
	@Override
	public GoodBean getGoodById(Long transportId) {
		String sql = "SELECT ttm.`id` AS 'goodId', ttm.`status` AS 'status', LEFT(ttm.`ctime`,10) AS 'ctime' FROM tyt_transport_main ttm WHERE ttm.`id`=:id";
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("id", transportId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("goodId", Hibernate.STRING);
		scalarMap.put("status", Hibernate.INTEGER);
		scalarMap.put("ctime", Hibernate.STRING);
		List<GoodBean> gooBeans = this.getBaseDao().search(sql, scalarMap, GoodBean.class, paramMap);
		return gooBeans.get(0);
	}

	@Override
	public void updateCarOwnerRefuseBubbleNumber(Long id, String type1, String type2) {
		String sql = "update tyt_bubble set number=number+1 ,utime=now() where user_id=? and type1=? and type2=?";
		Object[] params = new Object[] { id, type1, type2 };
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void saveTopNotice(String type1, String type2, String msgId, String userId, Integer payUserId) {
		// 发弹窗通知
		TytNoticeRemindTmpl tytNoticeRemindTmpl = tytNoticeRemindTmplService.getTytNoticeRemindTmpl(type1, type2);
		if (tytNoticeRemindTmpl != null) {
			TytNoticeRemind tytNoticeRemind = new TytNoticeRemind();
			tytNoticeRemind.setType1(tytNoticeRemindTmpl.getType1());
			tytNoticeRemind.setType2(tytNoticeRemindTmpl.getType2());
			tytNoticeRemind.setPriority(tytNoticeRemindTmpl.getPriority());
			tytNoticeRemind.setIsPopup(tytNoticeRemindTmpl.getIsPopup());
			tytNoticeRemind.setContent(tytNoticeRemindTmpl.getContent());
			tytNoticeRemind.setMsgId(msgId);
			tytNoticeRemind.setProductionId(Long.valueOf(userId));
			tytNoticeRemind.setReceiveId(Long.valueOf(payUserId));
			tytNoticeRemind.setCtime(new Date());
			tytNoticeRemind.setReceiveStatus("0");
			tytNoticeRemind.setStatus("0");
			tytNoticeRemindService.add(tytNoticeRemind);
		}
	}

	@Override
	public void updateOrderRefuse(String infoFeeOrderId) {
		String sql = "UPDATE tyt_transport_orders SET rob_status=2, refuse_time=NOW(), mtime=NOW() WHERE id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { infoFeeOrderId });
	}

	@Override
	public void refund(String infoFeeOrderId, Long userId, String transportOrderNum) throws Exception {
		MqMoneyRefundMsg moneyRefundMsg = new MqMoneyRefundMsg();
		String serialNum = SerialNumUtil.generateSeriaNum();
		moneyRefundMsg.setMessageSerailNum(serialNum);
		moneyRefundMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_RERUND);
		List<String> orderIds = new ArrayList<String>();
		orderIds.add(infoFeeOrderId);
		moneyRefundMsg.setOrderIds(orderIds);
		List<String> userIds = new ArrayList<String>();
		userIds.add(String.valueOf(userId));
		moneyRefundMsg.setUserId(userIds);
		String messageContent = JSON.toJSONString(moneyRefundMsg);
		addMqMessage(serialNum, messageContent, MqBaseMessageBean.MESSAGETYPE_RERUND);
		mqProducer.sendMsg(messageContent, null, serialNum, null);
	}

	@Override
	public void addMqMessage(String serialNum, String messageContent, int messageTypeInfofee) {
		String sql = "INSERT INTO `tyt`.`tyt_mq_message` ( `message_serial_num`, `message_content`, `create_time`, `message_type`) VALUES (?, ?, ?, ?)";
		Object[] params = new Object[] { serialNum, messageContent, new Date(), messageTypeInfofee };
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void updateCarOwnerBubbleNumber(String payUserId, String type1, String type2) {
		String sql = "update tyt_bubble set number=number-1 ,utime=now() where user_id=? and type1=? and type2=? and number>0";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { payUserId, type1, type2 });
	}

	@Override
	public void updateTransport(Long srcMsgId, int infoStatus) {
		// tyt_manage_new_transport_optimize2017112 货源信息优化
		String sql = "UPDATE tyt_transport SET info_status=? WHERE src_msg_id=? and status=?";
		Object[] params = new Object[] { infoStatus, srcMsgId, 1 };
		this.getBaseDao().executeUpdateSql(sql, params);
		sql = "UPDATE tyt_transport_main SET info_status=? WHERE id=?";
		params = new Object[] { infoStatus, srcMsgId };
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void updateBubbleNumber(Integer userId, String type1, String type2, String transportOrderNum) {
		String sql = "update tyt_bubble set number=number+1 ,utime=now() where NOT EXISTS (SELECT id FROM tyt_transport_waybill WHERE ts_order_no=? AND pay_number>=1) AND user_id=? and type1=? and type2=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { transportOrderNum, userId, type1, type2 });
	}

	@Override
	public void updateWayBillBytransportOrderNum(TransportOrderBean orderBean) {
		/*
		 * 获取最大的sort_id
		 */
		Long sortId = updateGetNextSequenceNbr(TABLE_TRANSPORT_WAYBILL_NAME);
		String sql = "UPDATE tyt_transport_waybill SET sort_id=?, pay_user_id=?, pay_link_phone=?, pay_cell_phone=?,info_status=1,pay_time=NOW(),mtime=NOW(),pay_number=pay_number+1,pay_amount=? WHERE ts_order_no=?";
		Object[] params = new Object[] { sortId, orderBean.getPayUserId(), orderBean.getPayLinkPhone(), orderBean.getPayCellPhone(), orderBean.getPayAmount(), orderBean.getTransportOrderNum() };
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void updateGoodOrderStatusInfofee(String infoFeeOrderId, int infofeeOrderPayStatus,
				int infofeeOrderRobStatus, String orderId, int payType, Date payEndTime, int infofeeOrderAgreeLoad) {
		/*
		 * 获取最大的sort_id
		 */
		Long sortId = updateGetNextSequenceNbr(TABLE_TRANSPORT_ORDERS_NAME);
		String sql = "UPDATE tyt_transport_orders SET sort_id=?, pay_status=?, rob_status=?, pay_no=?, mtime=NOW(), pay_end_time=?, pay_type=?,cost_status=? WHERE id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { sortId, infofeeOrderPayStatus, infofeeOrderRobStatus, orderId, payEndTime, payType, infofeeOrderAgreeLoad, infoFeeOrderId });
	}

	@Override
	public void updateWayBillBytransportOrderNumInfofee(TransportOrderBean orderBean) {
		/*
		 * 获取最大的sort_id
		 */
		Long sortId = updateGetNextSequenceNbr(TABLE_TRANSPORT_WAYBILL_NAME);
		String sql = "UPDATE tyt_transport_waybill SET sort_id=?, pay_user_id=?, pay_link_phone=?, pay_cell_phone=?,info_status=2,pay_time=NOW(),mtime=NOW(),pay_number=pay_number+1,pay_amount=? WHERE ts_order_no=?";
		Object[] params = new Object[] { sortId, orderBean.getPayUserId(), orderBean.getPayLinkPhone(), orderBean.getPayCellPhone(), orderBean.getPayAmount(), orderBean.getTransportOrderNum() };
		this.getBaseDao().executeUpdateSql(sql, params);
	}
	
	@Override
	public long addTradeInfo(String infoFeeOrderId, Order order, TransportOrderBean orderBean, int accountType, int tradeType, String amount)
				throws Exception {
		TytTradeinfo topUp = new TytTradeinfo();
		Date now = new Date();
		topUp.setAllow_pay_back_refund("0");
		topUp.setGood_info_fee_id(Long.valueOf(infoFeeOrderId));
		topUp.setOrder_id(order.getOrderId());
		// 查询平台用户信息
		Long platUserId = userService.getIdByCellPhone(VIRTUAL_USER_ACCOUNT);
		topUp.setPay_receiver_user_id(platUserId);
		// 查询平台余额账户信息
		TytUserAccount platRemainAccount = userAccountService.queryAccount(platUserId, accountType);
		if (platRemainAccount != null) {
			topUp.setPay_receiver_account_id(platRemainAccount.getId());
		}
		// 查询付款人账户信息
		TytUserAccount payAccount = userAccountService.queryAccount(order.getUserId(), 1);
		if (payAccount != null) {
			topUp.setPayer_account_id(payAccount.getId());
		}
		topUp.setPayer_user_id(order.getUserId());
		topUp.setRefunding_account("0");
		topUp.setTrade_account(amount);
		topUp.setTrade_time(now);
		topUp.setTrade_type(tradeType);
		topUp.setUpdate_time(now);
		tradeInfoService.add(topUp);
		return topUp.getId();
	}


	/**
	 * @Description  添加用户购买商品订单
	 * <AUTHOR>
	 * @Date  2019/6/27 16:03
	 * @Param [userId, cellPhone, goods, payStatus, clientSign, clientVersion]
	 * @return com.tyt.model.Order
	 **/
	@Override
	public Order addGoodsOrder(Long userId, String cellPhone, String orderId, TytGoods goods, Integer payStatus) throws Exception {

		// 商品价格(单位：元)
		BigDecimal goodsPrice = goods.getPrice();
		// 将商品价格单位转换为 分
		int price = goodsPrice.movePointRight(2).intValue();
		// 开通年限(单位：day,month,year)
		Integer effectiveTime = goods.getEffectiveTime();
		// 单位(day:天; month:月;year:年;boolean:布尔)
		String unit = goods.getUnit();
		//订单表对象
		Order order = new Order();
		order.setOrderId(orderId);
		order.setUserId(userId);
		order.setCellPhone(cellPhone);
		order.setPayStatus(payStatus);
		order.setRenewalYears(effectiveTime);
		order.setUnit(unit);
		//终端标识 4：代表manage后台
		order.setPlatId(4);
		order.setStatus(Order.ORDER_STATUS_NO_PAY);
		order.setCtime(TimeUtil.getTimeStamp());
		order.setMtime(TimeUtil.getTimeStamp());
		order.setTotalFee(price);
		//关联订单表id(order_type=3则为商品ID)
		order.setGoodPayId(goods.getId().intValue());
		//货物的运单号，货物的唯一标示(order_type=3则为用户购买商品的订单ID-order_id)
		order.setOrderNum(orderId);
		//订单类型(1.会员费 2.信息费 3.购买商品)
		order.setOrderType(3);
		//商品名称(order_type=3该值不为空)
		order.setGoodsName(goods.getName());
		//是否启用tpay收银台版本(true:是,false:否)
		boolean isTpayVersion = TytSwitchUtil.isTpayVersion();
		if(isTpayVersion){
			/* 微信开放平台审核通过的应用APPID */
			String appid = AppConfig.getProperty("car.weixin.appid");
			order.setAppid(appid); //应用APPID
			order.setUserType("USER"); //付款方类型,用户：USER  平台商户：MERCHANT
			order.setPayMethod("tpay支付"); //付款方式
			order.setPayChannel("4"); //支付渠道 1支付宝 2银行卡 3.微信 4.tpay支付
		}
		this.add(order);
		return order;
	}


	@Override
	public void addOrderId(Long orderId) {
		String orderIdsKey = Constant.REDIS_ORDER_IDS_KEY;
		// 判断是否存在当前key,存在直接追加只，否则创建
		if (RedisUtil.exists(orderIdsKey)) {
			RedisUtil.listAdd(orderIdsKey, String.valueOf(orderId));
		} else {
			List<String> orderIdsList = new ArrayList<String>();
			orderIdsList.add(String.valueOf(orderId));
			RedisUtil.setList(orderIdsKey, orderIdsList, 0);
		}
	}

	/**
	 * @Description  获取财务统计数据(支付成功的笔数、金额)
	 * <AUTHOR>
	 * @Date  2019/12/10 12:01
	 * @Param [order, pageNo, pageSize]
	 * @return com.tyt.payment.bean.FinancialData
	 **/
	@Override
	public FinancialData getFinancialData(OrderQueryBean order) {
		String cellPhone = order.getCellPhone();
		String payMethod = order.getPayMethod();
		String payChannel = order.getPayChannel();
		String opType = order.getOpType();
		String note = order.getNote();
		String orderId = order.getOrderId();

		Integer payStatus = order.getPayStatus();
		Integer status = order.getStatus();
		Integer totalFee = order.getTotalFee();
		Integer platId = order.getPlatId();
		Integer orderType = order.getOrderType();

		Date startCtime = order.getStartCtime();
		Date endCtime = order.getEndCtime();

		//拼接传入的查询参数
		List<Object> listObject = new ArrayList<Object>();
		//拼接查询条件的sql
		StringBuffer contionSql = new StringBuffer();
		if(StringUtils.isNotBlank(cellPhone)) {
			contionSql.append(" and o.cell_phone = ? ");
			listObject.add(cellPhone);
		}
		if(StringUtils.isNotBlank(payMethod)) {
			contionSql.append(" and o.paymethod = ? ");
			listObject.add(payMethod);
		}
		if(StringUtils.isNotBlank(payChannel)) {
			contionSql.append(" and o.pay_channel = ? ");
			listObject.add(payChannel);
		}
		if(StringUtils.isNotBlank(opType)) {
			contionSql.append(" and o.op_type = ? ");
			listObject.add(opType);
		}
		if(StringUtils.isNotBlank(note)) {
			contionSql.append(" and o.note = ? ");
			listObject.add(note);
		}
		if(StringUtils.isNotBlank(orderId)) {
			contionSql.append(" and o.order_id = ? ");
			listObject.add(orderId);
		}

		if(payStatus != null){
			contionSql.append(" and o.pay_status = ? ");
			listObject.add(payStatus);
		}
		if(status != null){
			contionSql.append(" and o.status = ? ");
			listObject.add(status);
		}
		if(totalFee != null){
			contionSql.append(" and o.total_fee = ? ");
			listObject.add(totalFee);
		}
		if(platId != null){
			contionSql.append(" and o.plat_id = ? ");
			listObject.add(platId);
		}
		if(orderType != null && orderType.intValue() != 0){
			switch (order.getOrderType()) {
				case 1: //信息费
					contionSql.append(" and o.order_type = 2 ");
					break;
				case 2: //会员费
					contionSql.append(" and o.order_type in (1,3) ");
					break;
			}
		}
		if(startCtime != null){
			contionSql.append(" and o.ctime > ? ");
			listObject.add(startCtime);
		}
		if(endCtime != null){
			contionSql.append(" and o.ctime < ? ");
			try {
				listObject.add(TimeUtil.addDay(endCtime, 1));
			} catch (Exception e) {}
		}
		contionSql.append(" order by o.id desc ");

		//查询财务数据的sql语句
		StringBuffer financialSql = new StringBuffer("select count(*) paySuccessCount,sum(total_fee) paySuccessAmount from tyt_old_order o where 1=1 ");
		financialSql.append(contionSql);
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		//支付订单笔数,单位：笔
		scalarMap.put("paySuccessCount",Hibernate.INTEGER);
		//支付订单金额,单位：分
		scalarMap.put("paySuccessAmount",Hibernate.BIG_DECIMAL);
		List<FinancialData> financialDataList = this.getBaseDao().search(financialSql.toString(), scalarMap, FinancialData.class, listObject.toArray());

		//财务数据对象(支付成功的笔数、金额)
		FinancialData financialData = new FinancialData();
		if(financialDataList != null && financialDataList.size()>0){
			financialData = financialDataList.get(0);
		}
		return financialData;
	}

    @Override
    public List<Order> getOrderList(OrderQueryBean order, PageBean pageBean) {
        StringBuffer sql = new StringBuffer();
        boolean hasfirst = false;

        if (org.springframework.util.StringUtils.hasLength(order.getCellPhone())) {
            // sql.append(" entity.cellPhone='").append(order.getCellPhone()).append("'");
            // 改为通过用户id查询
            try {
                Long userId = userService.getIdByCellPhone(order.getCellPhone());
                sql.append(" entity.userId='").append(userId).append("'");
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!hasfirst)
                hasfirst = true;
        }
        if (org.springframework.util.StringUtils.hasLength(order.getOrderId())) {
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.orderId='").append(order.getOrderId()).append("'");
            if (!hasfirst)
                hasfirst = true;
        }
        if(order.getStartCtime() != null){
            Date startCtime = order.getStartCtime();
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.ctime > '").append(TimeUtil.formatDateTime(startCtime)).append("'");
            if (!hasfirst)
                hasfirst = true;
        }
        if(order.getEndCtime() != null){
            Date endCtime = order.getEndCtime();
            try {
                Date endDate = TimeUtil.addDay(endCtime, 1);
                if (hasfirst)
                    sql.append(" and");
                sql.append(" entity.ctime < '").append(TimeUtil.formatDateTime(endDate)).append("'");
                if (!hasfirst)
                    hasfirst = true;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (order.getStatus() != null) {
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.status=").append(order.getStatus());
            if (!hasfirst)
                hasfirst = true;
        }
        if (order.getPayStatus() != null) {
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.payStatus=").append(order.getPayStatus());
            if (!hasfirst)
                hasfirst = true;
        }
        if (order.getPlatId() != null && order.getPlatId().intValue() > 0) {
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.platId=").append(order.getPlatId());
            if (!hasfirst)
                hasfirst = true;
        }
        if (org.springframework.util.StringUtils.hasLength(order.getPayChannel())) {
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.payChannel='").append(order.getPayChannel()).append("'");
            if (!hasfirst)
                hasfirst = true;
        }
        if (org.springframework.util.StringUtils.hasLength(order.getOpType())) {
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.opType='").append(order.getOpType()).append("'");
            if (!hasfirst)
                hasfirst = true;
        }
        if (org.springframework.util.StringUtils.hasLength(order.getNote())) {
            if (hasfirst)
                sql.append(" and");
            sql.append(" entity.note='").append(order.getNote()).append("'");
            if (!hasfirst)
                hasfirst = true;
        }
        if (order.getOrderType() != 0) {
            if (hasfirst)
                sql.append(" and");
            switch (order.getOrderType()) {
                case 1: //信息费
                    sql.append(" entity.orderType = 2 ");
                    break;
                case 2: //会员费
                    sql.append(" entity.orderType in (1,3) ");
                    break;
            }
            if (!hasfirst)
                hasfirst = true;
        }
        if (!hasfirst)
            sql.append(" 1=1 ");
        sql.append(" order by entity.id desc ");

        return this.getList(sql.toString(), pageBean);
    }

    @Override
    public void getOrderListCsvString(List<Order> orders,StringBuilder content) {
        for (Order order : orders) {
            //手机号
//            content.append(StringUtils.isBlank(order.getCellPhone())?"":order.getCellPhone()+"\t").append(",");
            //订单编号
            content.append(StringUtils.isBlank(order.getOrderId())?"":order.getOrderId()+"\t").append(",");
            //支付方式
            content.append(StringUtils.isBlank(order.getPayMethod())?"":order.getPayMethod()).append(",");
            //付费类型
            content.append(StringUtils.isBlank(order.getGoodsName())?"":order.getGoodsName()).append(",");
            //缴费/续费
            String payStatus = "";
            if (order.getPayStatus()!=null){
                if (order.getPayStatus() == 1){
                    payStatus = "缴费";
                }else if (order.getPayStatus() == 2){
                    payStatus = "续费";
                }
            }
//			order.setCellPhone("111***111");
            content.append(payStatus).append(",");
            //开通时长
            String times = "";
            if (order.getRenewalYears()!=null){
                if ("day".equals(order.getUnit())){
                    times = order.getRenewalYears()+"天";
                }else if("month".equals(order.getUnit())){
                    times = order.getRenewalYears()+"月";
                }else if("year".equals(order.getUnit())){
                    times = order.getRenewalYears()+"年";
                }
            }
            content.append(times).append(",");
            //总金额
            if (order.getTotalFee()!=null){
                BigDecimal totalFee = new BigDecimal(order.getTotalFee()).movePointLeft(2);
                content.append(totalFee).append(",");
            }else{
                content.append(",");
            }
            //终端
            String platSign = "";
            if (order.getPlatId()!=null){
                if (order.getPlatId() == 1){
                    platSign = "PC";
                }else if (order.getPlatId() == 2){
                    platSign = "Android";
                }else if (order.getPlatId() == 3){
                    platSign = "IOS";
                }else if (order.getPlatId() == 4){
                    platSign = "APad";
                }else if (order.getPlatId() == 5){
                    platSign = "IPAD";
                }else if (order.getPlatId() == 6){
                    platSign = "WEB";
                }else if (order.getPlatId() == 21){
					platSign = "Android-车";
				}else if (order.getPlatId() == 22){
					platSign = "Android-货";
				}else if (order.getPlatId() == 31){
					platSign = "IOS-车";
				}else if (order.getPlatId() == 32){
					platSign = "IOS-货";
				}else if (order.getPlatId() == 62){
					platSign = "Web-货";
				}
            }
            content.append(platSign).append(",");
            //付费时间
            content.append(TimeUtil.formatDateTime(order.getCtime())).append(",");
            //状态
            String status = "";
            if (order.getStatus() == 0){
                status = "未付款";
            }else if (order.getStatus() == 2){
                status = "付款成功";
            }
            content.append(status).append(",");
            //确认类型
            String opType = "";
            if ("1".equals(order.getOpType())){
                opType = "自动";
            }else if ("2".equals(order.getOpType())){
                opType = "手动";
            }
            content.append(opType).append(",");
            content.append("\r\n");
        }


    }

	@Override
	public void updateThirdpartyOrderNo(String orderNum,String payNo,Long userId,String thirdpartyOrderSerialNum) {
		String sql = "UPDATE tyt_old_order SET thirdparty_order_serial_num=?,pay_channel=4,paymethod='tpay支付' WHERE order_num=? and order_id=? and status =2 and user_id=? ORDER BY id desc limit 1";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { thirdpartyOrderSerialNum, orderNum,payNo,userId });
	}


	/**
	 * @description 根据关联订单号和手机号查询支付订单信息
	 * <AUTHOR>
	 * @date 2020/10/28 17:58
	 * @param payOrderNo
	 * @param cellPhone
	 * @return com.tyt.model.Order
	 */
	@Override
	public Order getByPayOrderAndCellPhone(String payOrderNo, String cellPhone) {
		String sql = "select * from tyt_old_order where pay_order_no = ? and cell_phone = ? order by mtime desc limit 1";
		List<Order> list = this.getBaseDao().queryForList(sql, new Object[] {payOrderNo,cellPhone});
		if(list!=null && list.size()>0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public Order listOrderId(String leadOrderId) {
		List<Order> ordersList = this.getBaseDao().find("from tyt_old_order where status = 2 and order_id=? limit 1", leadOrderId);
		return ordersList.get(0);
	}

	@Override
	public List<Order> listOrderByPayOrderAndCellPhone(List<TransportOrderResultBean> transportOrderList) {
		if(CollectionUtils.isEmpty(transportOrderList)){
			return new ArrayList<>();
		}
		//通过java8 获取transportOrderList的payOrderNo集合

		StringBuilder conditionsBuilder = new StringBuilder("(");
		List<Object> parameters = new ArrayList<>();

		for (TransportOrderResultBean bean : transportOrderList) {
			if (!conditionsBuilder.toString().equals("(")) {
				conditionsBuilder.append(" OR ");
			}
			conditionsBuilder.append("(pay_order_no = ? AND cell_phone = ?)");
			parameters.add(bean.getPayOrderNo());
			parameters.add(bean.getPayCellPhone());
		}
		conditionsBuilder.append(")");

		String sql = "SELECT * FROM tyt_old_order WHERE " + conditionsBuilder.toString() + " ORDER BY mtime DESC";
		payLog.info("listOrderByPayOrderAndCellPhone sql: {}", sql);
		List<Order> orderList = this.getBaseDao().queryForList(sql, parameters.toArray());
		if(CollectionUtils.isEmpty(orderList)){
			return new ArrayList<>();
		}

		List<Order> resultList = new ArrayList<>();
		//使用java 8 stream 遍历,按照pay_order_no、cell_phone 分组，按照mtime降序 每个组取第一个
		orderList.stream()
				.collect(Collectors.groupingBy(order -> order.getPayOrderNo() + "," + order.getCellPhone()))
				.forEach((key, value) -> {
					value.sort((o1, o2) -> o2.getMtime().compareTo(o1.getMtime()));
					Order order = value.get(0);
					resultList.add(order);
				});
		return resultList;
	}

	/**
	 * 根据业务单号查询支付订单信息
	 *
	 * @param orderNum
	 * @param status
	 * @return
	 */
	@Override
	public Order getOrderByOrderNumAndStatus(String orderNum, Long payUserId, Integer status) {
		String sql = "select * from tyt_old_order where order_num = ? and user_id = ? and status = ? order by mtime desc limit 1";
		List<Order> list = this.getBaseDao().queryForList(sql, new Object[] {orderNum, payUserId, status});
		if(CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}
}
