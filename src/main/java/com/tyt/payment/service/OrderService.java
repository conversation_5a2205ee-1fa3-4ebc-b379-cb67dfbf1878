package com.tyt.payment.service;

import com.tyt.infofee.bean.TransportOrderResultBean;
import com.tyt.model.*;
import com.tyt.payment.bean.FinancialData;
import com.tyt.service.base.BaseService;
import com.tyt.web.back.internal.bean.GoodBean;
import com.tyt.web.back.internal.bean.OrderQueryBean;
import com.tyt.web.back.internal.bean.TransportOrderBean;

import java.util.Date;
import java.util.List;

public interface OrderService extends BaseService<Order, Long> {
	/**
	 * 保存订单
	 * 
	 * @param price
	 * @param cellPhone
	 * @param userType
	 * @param payChannel
	 * @param platId
	 */
	Order getOrder(Price price, User user, String payChannel, Integer platId) throws Exception;

	/**
	 * 根据订单id和金额查询订单，以验证金额是否被改
	 * 
	 * @param orderId
	 * @param amount
	 * @return
	 */
	Order getByIdAndAmount(String orderId, Integer amount) throws Exception;

	/**
	 * 根据商户订单号查询订单
	 * 
	 * @param orderId
	 * @return
	 * @throws Exception
	 */
	Order getByOrderId(String orderId) throws Exception;

	/**
	 * 更新订单状态
	 * 
	 * @param status
	 * @param opType
	 *            1自动2手动
	 * @param note
	 *            备注
	 * @return
	 */
	Order updateOrderById(Integer status, Long id, String opType, String note);

	/**
	 * 更新订单状态
	 *
	 * @param status
	 * @return
	 */
	Order updateOrderById(Integer status, Long id);
	/**
	 * 
	 * @param goodPayId
	 *            货物信息费支付id
	 * @return
	 */
	TransportOrderBean getOrderById(String goodPayId);

	int addTradeInfo(Integer payAmount, Order order, String infoFeeOrderId);

	void updateGoodOrderStatus(String infoFeeOrderId, int infofeeOrderPayStatus, int infofeeOrderRobStatus, String orderNumber, Integer payType, Date payEndTime);

	/**
	 * 或得下一个序列的值
	 * 
	 * @param sequenceName
	 *            虚列的名字
	 * @return
	 */
	Long updateGetNextSequenceNbr(String sequenceName);

	void updateOrderStatus(Long id, int status, String thirdpartyOrderSerialNum);

	/**
	 * @Description  修改订单状态
	 * <AUTHOR>
	 * @Date  2019/7/17 16:53
	 * @Param [id, status]
	 * @return void
	 **/
	void updateOrderStatus(Long id, int status);

	GoodBean getGoodById(Long transportId);

	void updateCarOwnerRefuseBubbleNumber(Long id, String type1, String type2);

	void saveTopNotice(String type1, String type2, String id, String userId, Integer payUserId);

	void updateOrderRefuse(String infoFeeOrderId);

	void refund(String infoFeeOrderId, Long userId, String transportOrderNum) throws Exception;

	void addMqMessage(String serialNum, String messageContent, int messageTypeInfofee);

	void updateCarOwnerBubbleNumber(String payUserId, String type1, String type2);

	void updateTransport(Long srcMsgId, int infoStatus);

	void updateBubbleNumber(Integer userId, String type1, String type2, String transportOrderNum);

	void updateWayBillBytransportOrderNum(TransportOrderBean orderBean);

	void updateGoodOrderStatusInfofee(String goodPayId, int infofeeOrderPaySuccess,
									  int infofeeOrderAgreeLoad, String orderId, int payType, Date date, int i);

	void updateWayBillBytransportOrderNumInfofee(TransportOrderBean orderBean);

	long addTradeInfo(String infoFeeOrderId, Order order, TransportOrderBean orderBean, int accountType, int tradeType, String amount) throws Exception;

	/**
	 * @Description  添加用户购买商品订单
	 * <AUTHOR>
	 * @Date  2019/6/27 16:02
	 * @Param [userId, cellPhone, goods, payStatus, clientSign, clientVersion]
	 * @return com.tyt.model.Order
	 **/
	Order addGoodsOrder(Long userId, String cellPhone, String orderId, TytGoods goods, Integer payStatus) throws Exception;

	/**
	 * 保存订单id到redis中，用于在展示用户缴费失败列表处使用
	 *
	 * @param orderId
	 */
	void addOrderId(Long orderId);

	/**
	 * @Description  获取财务统计数据(支付成功的笔数、金额)
	 * <AUTHOR>
	 * @Date  2019/12/10 11:59
	 * @Param [order, pageNo, pageSize]
	 * @return com.tyt.payment.bean.FinancialData
	 **/
	FinancialData getFinancialData(OrderQueryBean order);

    List<Order> getOrderList(OrderQueryBean order, PageBean pageBean);

    void getOrderListCsvString(List<Order> orders,StringBuilder content);

	/**
	 * 更新第三方单号
	 * @param orderId
	 * @param userId
	 * @param thirdpartyOrderSerialNum
	 */
	void updateThirdpartyOrderNo(String orderId,String payNo,Long userId, String thirdpartyOrderSerialNum);

	/**
	 * @description 根据关联订单号和手机号查询支付订单信息
	 * <AUTHOR>
	 * @date 2020/10/28 17:58
	 * @param payOrderNo
	 * @param cellPhone
	 * @return com.tyt.model.Order
	 */
	Order getByPayOrderAndCellPhone(String payOrderNo, String cellPhone);

	/**
	 * 根据订单编号查询订单
	 * @param leadOrderId
	 * @return
	 */
	Order listOrderId(String leadOrderId);

	/**
	 * @description 根据关联订单号和手机号查询支付订单信息
	 * <AUTHOR>
	 * @date 2020/10/28 17:58
	 * @param transportOrderList
	 * @return List<Order> 订单列表
	 */
	List<Order> listOrderByPayOrderAndCellPhone(List<TransportOrderResultBean> transportOrderList);

	/**
	 * 根据业务单号查询支付订单信息
	 *
	 * @param orderNum
	 * @param status
	 * @return
	 */
	Order getOrderByOrderNumAndStatus(String orderNum, Long payUserId, Integer status);
}
