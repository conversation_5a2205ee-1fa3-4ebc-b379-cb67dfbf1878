package com.tyt.weixinpay.bean;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 封装调用微信下订单接口响应的信息的实体
 * 
 * <AUTHOR>
 * @date 2016-6-24下午2:00:47
 * @description
 */
@XmlRootElement(name = "xml")
public class PlaceOrderResQRBean extends PlaceOrderResBean {

	private String codeUrl;

	@XmlElement(name = "code_url")
	public String getCodeUrl() {
		return codeUrl;
	}

	public void setCodeUrl(String codeUrl) {
		this.codeUrl = codeUrl;
	}

}
