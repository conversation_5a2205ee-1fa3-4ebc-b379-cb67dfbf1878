package com.tyt.refresh.req;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 配置内容
 *
 * <AUTHOR>
 * @since 2024年4月16日 16点03分
 */
@Getter
@Setter
public class GoodsRefreshConfigContentReq {

    // 刷新配置参数
    /**
     * 刷新次数
     */
    private String frequency;
    /**
     * 刷新间隔（分钟）
     */
    private String interval;
    /**
     * 生效时间段，[1,9] 代表 1点到9点
     */
    private List<Integer> period;

    // 曝光次数限制参数
    /**
     * 拨打次数配置
     */
    private Integer dialTimes;
    /**
     * 点击次数配置
     */
    private Integer viewTimes;

}
