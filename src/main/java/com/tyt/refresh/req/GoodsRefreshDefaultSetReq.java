package com.tyt.refresh.req;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: helian
 * @since: 2023/10/13 13:24
 */
@Getter
@Setter
public class GoodsRefreshDefaultSetReq {
    // /**
    //  * 刷新次数
    //  */
    // private String frequency;

    /**
     * 刷新间隔（分钟）
     */
    private Integer interval;

    /**
     * 生效时间段，[1,9] 代表 1点到9点
     */
    private List<Integer> period;

}
