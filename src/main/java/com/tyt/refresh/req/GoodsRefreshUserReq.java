package com.tyt.refresh.req;

import com.tyt.model.PageBean;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @author: helian
 * @since: 2023/10/12 09:56
 */
@Data
public class GoodsRefreshUserReq extends PageBean {
    /**
     * 刷新code
     */
    @NotBlank(message = "刷新编码不能为空")
    private String refreshCode;

    /**
     * 要筛选的用户id
     */
    private Long queryUserId;
}
