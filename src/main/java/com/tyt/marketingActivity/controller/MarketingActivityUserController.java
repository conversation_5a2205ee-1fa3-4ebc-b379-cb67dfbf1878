package com.tyt.marketingActivity.controller;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.marketingActivity.service.MarketingActivityUserService;
import com.tyt.model.*;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.Constant;
import com.tyt.util.CsvWriter;
import com.tyt.util.FileUtil;
import com.tyt.web.base.BaseController;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping("/marketing/activity/user")
public class MarketingActivityUserController extends BaseController {

    @Resource(name = "marketingActivityUserService")
    private MarketingActivityUserService marketingActivityUserService;
    @Autowired
    private MarketingActivityService marketingActivityService;

    /**
     * 下载模板
     * @param request  request
     * @param response response
     */
    /*@RequestMapping(value = "/excelExportModel", method = RequestMethod.POST)
    public void excelExportModel(HttpServletRequest request, HttpServletResponse response) {
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser==null) {
            request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
            return ;
        }
        try {
            String header = "用户ID,用户活动等级" + "\r\n";
            CsvWriter.exportCsv("导入活动名单模板", header.toString(), response);
            header = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/



    /**
     * 导入用户
     * @param request
     * @param response
     * @param fileField
     */
    @RequestMapping(value = "/importList", method = RequestMethod.POST)
    public void importList(HttpServletRequest request, HttpServletResponse response,
                                    @RequestParam(value = "fileField", required = true) MultipartFile fileField,
                                    @RequestParam(value = "activityId", required = true) Long activityId) {
        EmployeeQueryBean curUser = getCurrentUser(request);
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,"导入成功！");
        if (curUser == null) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("导入失败,该用户没有登录");
            printJSON(request, response, msgBean);
        }
        try {
            //根据产品需求 将原来根据用户手机号导入参加活动用户 改为根据用户Id
            //msgBean = marketingActivityUserService.saveExcel(fileField, curUser, activityId,msgBean);
            //csv导入
            //msgBean = marketingActivityUserService.saveExcelByUserId(fileField, curUser, activityId,msgBean);
            //2023-08-24 excel导入
            msgBean = marketingActivityUserService.saveExcelUserId(marketingActivityUserService, fileField, curUser, activityId,msgBean);
        } catch (Exception e) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("导入失败,请重新导入");
            e.printStackTrace();
        }
        printJSON(request, response, msgBean);
    }

    /**
     * 查看名单
     * @param userId
     * @param cellPhone
     * @param pageBean
     * @param request
     * @return
     */
    @RequestMapping("/list")
    @ResponseBody
    public ResultMsgBean getList(
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "cellPhone", required = false) String cellPhone,
            @RequestParam(value = "activityId", required = true) Long activityId,
            PageBean pageBean, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            if (pageBean.getPageSize() > 50) {
                pageBean.setPageSize(50);
            }
            // List<MarketingActivityUser> list = marketingActivityUserService.getUserList(cellPhone,activityId, pageBean);
            List<MarketingActivityUser> list = marketingActivityUserService.getUserListByUserId(userId,activityId, pageBean);
            HashMap<Object, Object> map=new HashMap<>();
            map.put("list", list);
            map.put("pageNo", pageBean.getCurrentPage());
            map.put("maxPage", pageBean.getMaxPage());
            map.put("pageSize", pageBean.getPageSize());
            map.put("rowCount", pageBean.getRowCount());
            map.put("cellPhone", cellPhone);
            map.put("userId", userId);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 删除人员
     * @param id
     * @param request
     * @return
     */
    @RequestMapping("/delete")
    @ResponseBody
    public ResultMsgBean updateStatus(
            @RequestParam(value = "id", required = true) Long id,HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"操作成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            marketingActivityUserService.updateStatus(id,curUser.getRealName());
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }
    
    /**
     * @description 批量删除活动用户
     * <AUTHOR>
     * @date 2023/8/24 14:30
     * @version 1.0
     * @param request
     * @param activityId
     * @param fileField
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = "batchDel")
    @ResponseBody
    public ResultMsgBean batchDel(HttpServletRequest request,
                                  @RequestParam("activityId") Long activityId,
                                  @RequestParam(value = "fileField") MultipartFile fileField) {
        // 获得当前用户的身份
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            return ResultMsgBean.failResponse(500, "未登录");
        }
        MarketingActivity marketingActivity = marketingActivityService.getActivityById(activityId);
        if (Objects.isNull(marketingActivity)){
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,"无效活动");
        }
        return marketingActivityUserService.updateBatchDel(marketingActivityUserService,activityId, fileField, curUser.getRealName());
    }

    /**
     * 导入活动用户名单--excel
     * @return
     */
    @RequestMapping("/excelExportModel")
    public ResponseEntity<byte[]> excelExportModel() {
        File file = null;
        try {
            String filename = "活动用户导入模板.xlsx";
            file = new File(this.getClass().getResource("/" + filename).toURI());
            return FileUtil.buildResponseEntity(file);
        } catch (Exception e) {
            logger.error("download familiar template error, the error message is: ", e);
        }
        return null;
    }
}
