package com.tyt.marketingActivity.service.impl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.tyt.exception.ExcelImportException;
import com.tyt.marketingActivity.bean.DelActivityImportBean;
import com.tyt.marketingActivity.service.MarketingActivityUserService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 活动弹窗用户根据导入excel批量删除
 * @date 2023/08/24 14:34
 */
@Slf4j
public class MarketingUserDelExcelListener extends AnalysisEventListener<DelActivityImportBean> {
    /**
     * 每隔500条存储数据库，然后清理list，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 临时存储
     */
    private List<Long> userIdsCachedData = new ArrayList<>(BATCH_COUNT);
    private MarketingActivityUserService marketingActivityUserService;

    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 操作人姓名
     */
    private String operater;

    /**
     * 成功条数
     */
    private Integer successfulNum = 0;
    private ResultMsgBean resultMsgBean;
    private Integer flag = 0;

    public MarketingUserDelExcelListener(MarketingActivityUserService marketingActivityUserService, Long activityId, String operater,
                                         ResultMsgBean resultMsgBean) {
        this.marketingActivityUserService = marketingActivityUserService;
        this.activityId = activityId;
        this.operater = operater;
        this.resultMsgBean = resultMsgBean;
    }

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        //总行数
        Integer rowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
        log.info("弹窗活动管理,批量删除名单,上传的名单总行数为{}", rowNumber);
        if (rowNumber - 1 <= 0) {
            throw new ExcelImportException(ReturnCodeConstant.ERROR, "excel中没有数据");
        }
        if (rowNumber - 1 > Constant.IMPORT_EXCEL_ROW_MAXNUM) {
            throw new ExcelImportException(ReturnCodeConstant.EXCEL_ROW_EXCEED_MAXIMUM_LIMIT, "导入超过限制,限制行数10W行,实际行数 " + rowNumber);
        }
        super.invokeHead(headMap, context);
    }

    @Override
    public void invoke(DelActivityImportBean delActivityImportBean, AnalysisContext analysisContext) {
        userIdsCachedData.add(delActivityImportBean.getUserId());
        if (userIdsCachedData.size() >= BATCH_COUNT) {
            updateBatchDel(activityId, operater);
            userIdsCachedData.clear();
            flag ++;
            if (flag>100){
                flag = 0;
                try {
                    Thread.sleep(Constant.IMPORT_THREAD_SLEEP_MILLISECOND);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //这里也要保存数据，确保最后遗留的数据也存储到数据库 大量
        updateBatchDel(activityId, operater);
        resultMsgBean.setData(successfulNum);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelImportException) {
            if (Objects.equals(((ExcelImportException) exception).getCode(), ReturnCodeConstant.EXCEL_ROW_EXCEED_MAXIMUM_LIMIT)) {
                resultMsgBean.setCode(ReturnCodeConstant.EXCEL_ROW_EXCEED_MAXIMUM_LIMIT);
                resultMsgBean.setMsg(exception.getMessage());
                throw new ExcelImportException(500, "导入超过限制");
            }
        }
        resultMsgBean.setData(successfulNum);
        log.error("弹窗活动管理,批量删除名单,名单id从{}之后的名单删除失败", userIdsCachedData.get(0));
        super.onException(exception, context);
    }


    public void updateBatchDel(Long activityId, String operater) {
        if (CollectionUtils.isNotEmpty(userIdsCachedData)) {
            String userIds = userIdsCachedData.stream().map(String::valueOf).collect(Collectors.joining(","));
            marketingActivityUserService.updateBatchDel(activityId, userIds, operater);
            successfulNum += userIdsCachedData.size();
        }
    }
}
