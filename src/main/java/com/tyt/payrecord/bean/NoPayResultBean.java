package com.tyt.payrecord.bean;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import com.alibaba.fastjson.JSON;

import java.math.BigDecimal;

/**
 * 封装查询未支付列表结果的实体
 * 
 * <AUTHOR>
 * @date 2018年6月11日上午10:41:53
 * @description
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class NoPayResultBean {
	private String id;
	private String payTime;
	private String payEndTime;
	private String payResult;
	private String payType;
	private String phoneNum;
	private String years;
	private String terminal;
	private String payChannel;
	private String dealStatus;
	private String commResult;
	private String remark;
	private String dealer;
	private String dealTime;
	private String userId;

	@Getter
	@Setter
	private String carMaintainer;

	@Getter
	@Setter
	private String goodsMaintainer;

	@Getter
	@Setter
	private String goodsName;

	@Getter
	@Setter
	private BigDecimal totalFee;

	@Getter
	@Setter
	private Long orderId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getPayTime() {
		return (payTime != null && payTime.length() == 21) ? payTime.substring(0, payTime.length() - 2) : payTime;
	}

	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}

	public String getPayEndTime() {
		if (StringUtils.isNotEmpty(payEndTime) && payEndTime.compareTo(payTime) < 0) {
			return "";
		} else {
			return (payEndTime != null && payEndTime.length() == 21) ? payEndTime.substring(0, payEndTime.length() - 2) : payEndTime;
		}
	}

	public void setPayEndTime(String payEndTime) {
		this.payEndTime = payEndTime;
	}

	public String getPayResult() {
		String payResultName = "";
		if (StringUtils.isEmpty(payResult)) {
			if (StringUtils.isEmpty(payEndTime)) {
				payResultName = "缴费未完成";
			} else {
				if (payEndTime.compareTo(payTime) > 0) {
					payResultName = "缴费完成";
				} else {
					payResultName = "缴费未完成";
				}
			}
		} else {
			if ("2".equals(payResult)) {
				payResultName = "缴费完成";
			} else {
				payResultName = "缴费未完成";
			}
		}
		return payResultName;
	}

	public void setPayResult(String payResult) {
		this.payResult = payResult;
	}

	public String getPayType() {
		String payTypeName = "未知";
		if ("1".equals(payType)) {
			payTypeName = "缴费";
		} else if ("2".equals(payType)) {
			payTypeName = "续费";
		}
		return payTypeName;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getPhoneNum() {
		return phoneNum;
	}

	public void setPhoneNum(String phoneNum) {
		this.phoneNum = phoneNum;
	}

	public String getYears() {
		return years;
	}

	public void setYears(String years) {
		this.years = years;
	}

	public String getTerminal() {
		String terminalName = "未知";
		if (StringUtils.isEmpty(terminal)) {
			terminal = "100";
		}
		// 终端类型 1: pc 2: android 3:IOS 4:APAD 5: IPAD 6:WEB
		switch (terminal) {
		case "1":
			terminalName = "PC";
			break;
		case "2":
			terminalName = "ANDROID";
			break;
		case "3":
			terminalName = "IOS";
			break;
		case "4":
			terminalName = "APAD";
			break;
		case "5":
			terminalName = "IPAD";
			break;
		case "6":
			terminalName = "WEB";
			break;
		}
		return terminalName;
	}

	public void setTerminal(String terminal) {
		this.terminal = terminal;
	}

	public String getPayChannel() {
		String payChannelName = "未选择支付方式";
		if (StringUtils.isEmpty(payChannel)) {
			payChannel = "100";
		}
		// 支付渠道 1：支付宝 2：银行卡 3：微信
		switch (payChannel) {
		case "1":
			payChannelName = "支付宝";
			break;
		case "2":
			payChannelName = "银行卡";
			break;
		case "3":
			payChannelName = "微信";
			break;
		}
		return payChannelName;
	}

	public void setPayChannel(String payChannel) {
		this.payChannel = payChannel;
	}

	public String getDealStatus() {
		return dealStatus;
	}

	public void setDealStatus(String dealStatus) {
		this.dealStatus = dealStatus;
	}

	public String getCommResult() {
		return commResult;
	}

	public void setCommResult(String commResult) {
		this.commResult = commResult;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getDealer() {
		return dealer;
	}

	public void setDealer(String dealer) {
		this.dealer = dealer;
	}

	public String getDealTime() {
		return (dealTime != null && dealTime.length() == 21) ? dealTime.substring(0, dealTime.length() - 2) : dealTime;
	}

	public void setDealTime(String dealTime) {
		this.dealTime = dealTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
