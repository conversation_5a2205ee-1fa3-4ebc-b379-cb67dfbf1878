package com.tyt.price.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.Price;
import com.tyt.price.dao.PriceDao;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.cache.CacheService;
import com.tyt.util.StringUtil;
import com.tyt.web.fore.querybean.PriceCenterBean;
import com.tyt.web.qbean.CarQueryBean;
@Service("priceService")
public class PriceServiceImpl extends BaseServiceImpl<Price, Long> implements PriceService {

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	@Resource(name="priceDao")
	public void setBaseDao(BaseDao<Price, Long> priceDao) {
	        super.setBaseDao(priceDao);
	}
	
	@Override
	public List<Price> getEnabledList() {
		return ((PriceDao)(this.getBaseDao())).search(" entity.status=1", null);
	}

	@Override
	public List<PriceCenterBean> getListByUserSign(String userSign) {
		List<Object> listObject = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer();
		
		sb.append(" p.status=? ");
		listObject.add(Price.PRICE_STATUS_ENABLED);
		sb.append(" and p.user_sign=? ");
		listObject.add(userSign);
		
		StringBuffer sbSql = new StringBuffer(
				"SELECT p.id id,p.remark remark,p.price price,p.years years from tyt_price p where ");
						
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();

		map.put("id", Hibernate.LONG);
		map.put("remark", Hibernate.STRING);
		map.put("price", Hibernate.STRING);
		map.put("years", Hibernate.INTEGER);

		sbSql.append(sb);
		
		List<PriceCenterBean> list = this.getBaseDao().search(
				sbSql.toString(), map, PriceCenterBean.class,
				listObject.toArray(),1,
				10);
		//价格除以100
		for(int i=0;i<list.size();i++){
//			list.get(i).setPrice(
//					StringUtil.fmtMoney(new BigDecimal(list.get(i).getPrice()).movePointLeft(2).toString())
//					);
			list.get(i).setPrice(
					StringUtil.changeF2Y(list.get(i).getPrice()));
		      }
		return list;
	}
		
}