package com.tyt.carteam.service.impl;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.tyt.carteam.bean.CarteamClewBean;
import com.tyt.carteam.bean.MissonAlloIdReqBean;
import com.tyt.carteam.bean.MissonNoAlloIdBean;
import com.tyt.carteam.service.CarteamClewLogService;
import com.tyt.carteam.service.CarteamClewService;
import com.tyt.dao.base.BaseDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.jurisdiction.service.EmployeeService;
import com.tyt.model.PageBean;
import com.tyt.model.TytCarteamClew;
import com.tyt.model.TytCarteamClewLog;
import com.tyt.model.TytEmployee;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.TytSourceService;
import com.tyt.util.JxlUtil;
import com.tyt.util.TimeUtil;

@SuppressWarnings("deprecation")
@Service("carteamClewService")
public class CarteamClewServiceImpl extends BaseServiceImpl<TytCarteamClew, Long> implements CarteamClewService {
	@Resource(name = "EmployeeService")
	private EmployeeService employeeService;

	@Resource(name = "carteamClewDao")
	public void setBaseDao(BaseDao<TytCarteamClew, Long> carteamClewDao) {
		super.setBaseDao(carteamClewDao);
	}

	@Resource(name = "carteamClewLogService")
	private CarteamClewLogService carteamClewLogService;
	@Resource(name = "tytSourceService")
	private TytSourceService tytSourceService;
	@Override
	public List<CarteamClewBean> getClewList(CarteamClewBean queryBean, PageBean pageBean,Long curId) {
		Map<String,Object> paramMap = new HashMap<String,Object>();
		StringBuffer conditionSQL=new StringBuffer(" where 1=1 ");
		//如果角色为组员。只看分配给自己的线索
		if(queryBean.getType()!=null && queryBean.getType()==2){
			conditionSQL.append(" AND c.sale_id=:saleId");
			paramMap.put("saleId", curId);
		}
		//处理状态
		if(queryBean.getStatus()!=null && queryBean.getStatus().intValue()>0){
			conditionSQL.append(" AND c.status=:status");
			paramMap.put("status", queryBean.getStatus());
		}
		//任务分配
		if(queryBean.getIsAllot()!=null && queryBean.getIsAllot().intValue()>0){
			conditionSQL.append(" AND c.is_allot=:isAllot");
			paramMap.put("isAllot", queryBean.getIsAllot());
		}
		//业务员姓名
		if(StringUtils.isNotBlank(queryBean.getSaleName())){
			conditionSQL.append(" AND c.sale_name=:saleName");
			paramMap.put("saleName", queryBean.getSaleName());
		}
		//业务员账号
		if(StringUtils.isNotBlank(queryBean.getSalePhone())) {
			conditionSQL.append(" AND c.sale_phone=:salePhone");
			paramMap.put("salePhone", queryBean.getSalePhone());
		}
		//联系结果
		if(queryBean.getRelResult()!=null) {
			conditionSQL.append(" AND c.rel_result=:relResult");
			paramMap.put("relResult", queryBean.getRelResult());
		}
		//省
		if(StringUtils.isNotBlank(queryBean.getProvince())) {
			conditionSQL.append(" AND c.province=:province");
			paramMap.put("province", queryBean.getProvince());
		}
		//市
		if(StringUtils.isNotBlank(queryBean.getCity())) {
			conditionSQL.append(" AND c.city=:city");
			paramMap.put("city", queryBean.getCity());
		}
		StringBuffer countSQL = new StringBuffer("SELECT COUNT(*) FROM tyt_carteam_clew c");
		countSQL.append(conditionSQL);
		BigInteger rowCount = this.getBaseDao().queryByMap(countSQL.toString(), paramMap);
		if(rowCount==null||rowCount.longValue()<=0l){
			return null;
		}
		pageBean.setRowCount(rowCount.longValue());
		StringBuffer querySQL = new StringBuffer("SELECT c.id id,c.user_id userId,c.link_phone linkPhone,c.gather_name gatherName,c.user_name userName," +
				"c.gather_identity gatherIdentity,c.user_identity userIdentity,c.sale_name saleName,c.sale_phone salePhone,c.dial_num dialNum," +
				"c.rel_result relResult,c.mtime mtime,c.province province,c.city city FROM tyt_carteam_clew c");
		querySQL.append(conditionSQL);
		querySQL.append(" ORDER BY c.mtime DESC ");
		Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
		map.put("id", Hibernate.LONG);
		map.put("userId", Hibernate.LONG);
		map.put("linkPhone", Hibernate.STRING);
		map.put("gatherName", Hibernate.STRING);
		map.put("userName", Hibernate.STRING);
		map.put("gatherIdentity", Hibernate.STRING);
		map.put("userIdentity", Hibernate.STRING);
		map.put("saleName", Hibernate.STRING);
		map.put("salePhone", Hibernate.STRING);
		map.put("dialNum", Hibernate.INTEGER);
		map.put("relResult", Hibernate.INTEGER);
		map.put("mtime", Hibernate.TIMESTAMP);
		map.put("province", Hibernate.STRING);
		map.put("city", Hibernate.STRING);
		return this.getBaseDao().searchByName(querySQL.toString(), map, CarteamClewBean.class, paramMap, pageBean.getCurrentPage(), pageBean.getPageSize());

	}

	@Override
	public void saveRelResult(Long clewId, Integer relRsult, String relRsultName, Integer type, EmployeeQueryBean curUser) {
		try {
			//1.更新线索表拨打次数+1 ，rel_result ,
			TytCarteamClew bean = this.getById(clewId);
			bean.setRelResult(relRsult);
			bean.setDialNum(bean.getDialNum()+1);
			bean.setMtime(new Date());
			bean.setOperaUserId(curUser.getId());
			bean.setOperaUserName(curUser.getUserName());
			this.update(bean);
			//2.新增log表

			TytCarteamClewLog clewLog = new TytCarteamClewLog();
			BeanUtils.copyProperties(clewLog,bean);
			clewLog.setClewId(clewId);
			clewLog.setType(type);
			clewLog.setRelResult(relRsultName);
			clewLog.setCtime(new Date());
			carteamClewLogService.add(clewLog);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}

	}

	@Override
	public int geNoAlloNum() {
		String sql = "SELECT COUNT(*) FROM tyt_carteam_clew tcc WHERE tcc.`is_allot`=? AND tcc.`status`=?";
		BigInteger noAlloNum = this.getBaseDao().query(sql, new Object[] { 1, 1 });
		return noAlloNum.intValue();
	}

	@Override
	public void saveMissionAllo(List<MissonAlloIdReqBean> alloIdReqList, EmployeeQueryBean curUser) {
		// 查询所有未分配的任务id
		String queryNoAlloIdSql = "SELECT id FROM tyt_carteam_clew tcc WHERE tcc.`is_allot`=? AND tcc.`status`=? order by id asc ";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		List<MissonNoAlloIdBean> noAlloIdList = this.getBaseDao().search(queryNoAlloIdSql,
					scalarMap, MissonNoAlloIdBean.class, new Object[] { 1, 1 });
		MissonAlloIdReqBean curReqBean;
		String updateAlloSql = "UPDATE tyt_carteam_clew tcc SET tcc.`allo_time`=NOW(), tcc.`is_allot`=?, sale_id=?, sale_name=?, sale_phone=(SELECT tie.`login_phone_no` FROM tyt_internal_employee tie WHERE tie.`id`=?), opera_user_id=?,opera_user_name=? WHERE tcc.`id`>=? AND tcc.`id`<=? and tcc.`is_allot`=?";
		List<Object[]> batchParams = new ArrayList<>();
		// 当前未分配任务id的list索引
		int curIdIndex = -1;
		// 当前分配任务的最小id
		long curMinId;
		// 当前分配任务的最大id
		long curMaxId;
		Object[] curParam;
		Long curUerId = (curUser == null ? null : curUser.getId());
		String curUsername = (curUser == null ? null : curUser.getRealName());
		for (int i = 0; i < alloIdReqList.size() && curIdIndex < noAlloIdList.size(); i++) {
			curReqBean = alloIdReqList.get(i);
			int curAllNum = curReqBean.getAlloNum();
			curMinId = noAlloIdList.get(curIdIndex + 1).getId();
			curMaxId = noAlloIdList.get(curIdIndex + curAllNum).getId();
			curIdIndex = curIdIndex + curAllNum;
			curParam = new Object[] { 2, curReqBean.getUserId(), curReqBean.getName(),
					curReqBean.getUserId(), curUerId, curUsername, curMinId, curMaxId, 1 };
			batchParams.add(curParam);
		}
		if (batchParams.size() > 0) {
			this.getBaseDao().addOrUpdateBatch(updateAlloSql, batchParams);
		}
	}

	@Override
	public TytCarteamClew getByCellPhone(String cellPhone) {
		String sql="SELECT * FROM `tyt_carteam_clew` WHERE link_phone=? ";
		List<TytCarteamClew> clews = this.getBaseDao().queryForList(sql, new Object[]{cellPhone});
		if(clews!=null && clews.size()>0){
			return clews.get(0);
		}
		return null;
	}

	@Override
	public Integer getDisposeCount(Integer status,Long curId) {
		BigInteger count =null;
		if(status!=null && status==1){
			String sql = "SELECT COUNT(*) FROM tyt_carteam_clew tcc WHERE tcc.`status`=?  AND tcc.`sale_id`=?";
			count = this.getBaseDao().query(sql, new Object[] { status,curId});
		}else{
			String sql = "SELECT COUNT(*) FROM tyt_carteam_clew tcc WHERE tcc.`status`=? AND tcc.`mtime`>=? AND tcc.`sale_id`=?";
			count = this.getBaseDao().query(sql, new Object[] { status, TimeUtil.formatDate(new Date()) ,curId});
		}

		return count.intValue();
	}


    @Override
    public void updateClewForDeal(Long clewId, EmployeeQueryBean curUser) {
        //更新线索状态，操作人和时间
        TytCarteamClew clew = this.getById(clewId);
        clew.setStatus(2);
        //clew.setRelResult();
		clew.setMtime(new Date());
        clew.setOperaUserId(curUser.getId());
        clew.setOperaUserName(curUser.getName());
        this.update(clew);
        //插入线索日志
        try {
            TytCarteamClewLog log = new TytCarteamClewLog();
            BeanUtils.copyProperties(log, clew);
            log.setRelResult(null);
            log.setClewId(clewId);
            log.setOperaUserId(curUser.getId());
            log.setOperaUserName(curUser.getName());
			log.setCtime(new Date());
            carteamClewLogService.add(log);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    private PageBean pageBean = new PageBean(1, 1);
	@Override
	public void addAndImportCarteamClew(MultipartFile familiarExcel, EmployeeQueryBean curUser) throws IOException, Exception {
		List<List<String>> carteamClewDataList = JxlUtil.readExcel(familiarExcel.getInputStream());
		if (carteamClewDataList != null && carteamClewDataList.size() > 0) {
			// 去除重复电话
			removeDuplication(carteamClewDataList);
			List<String> curCarteamClewData;
			String sql = "SELECT COUNT(*) FROM tyt_carteam_clew tcc WHERE tcc.`link_phone`=? ";
			BigInteger count;
			String linkPhone;
			String salePhone;
			List<TytEmployee> employeeList;
			TytEmployee curEmployee;
			TytCarteamClew newCarteamClew;
			Date nowDate = new Date();
			for (int i = 0; i < carteamClewDataList.size(); i++) {
				curCarteamClewData = carteamClewDataList.get(i);
				linkPhone = curCarteamClewData.get(1);
				// 判断该线索是否存在，不存在才保存
				count = this.getBaseDao().query(sql, new Object[] { linkPhone });
				if (count.intValue() == 0) {
					newCarteamClew = new TytCarteamClew();
					newCarteamClew.setCtime(nowDate);
					newCarteamClew.setMtime(nowDate);
					newCarteamClew.setLinkPhone(linkPhone);
					newCarteamClew.setStatus(1);
					newCarteamClew.setSource(2);
					newCarteamClew.setImportSource(curCarteamClewData.get(0));
					try {
						newCarteamClew.setDialNum(Integer.valueOf(curCarteamClewData.get(5)));
					} catch(NumberFormatException e) {
						newCarteamClew.setDialNum(0);
					}
					newCarteamClew.setGatherIdentity(curCarteamClewData.get(3));
					newCarteamClew.setGatherName(curCarteamClewData.get(2));
					if (curUser != null) {
						newCarteamClew.setOperaUserId(curUser.getId());
						newCarteamClew.setOperaUserName(curUser.getRealName());
					}
					// 根据业务员手机号查询业务员信息
					salePhone = curCarteamClewData.get(4);
					employeeList = employeeService.search(" loginPhoneNo=? and isValid=? ", new Object[] {salePhone,1}, pageBean);
					if (employeeList != null && employeeList.size() > 0) {
						curEmployee = employeeList.get(0);
						newCarteamClew.setSaleId(curEmployee.getId());
						newCarteamClew.setSalePhone(salePhone);
						newCarteamClew.setSaleName(curEmployee.getRealName());
						newCarteamClew.setIsAllot(2);
					} else {
						newCarteamClew.setIsAllot(1);
					}
					add(newCarteamClew);
				}
			}
		}
	}

	private void removeDuplication(List<List<String>> carteamClewDataList) {
		Iterator<List<String>> iterator = carteamClewDataList.iterator();
		List<String> curCarteamClewData;
		Map<String, String> containedPhoneMap = new HashMap<>();
		String linkPhone;
		while (iterator.hasNext()) {
			curCarteamClewData = iterator.next();
			linkPhone = curCarteamClewData.get(1);
			if (containedPhoneMap.containsKey(linkPhone)) {
				iterator.remove();
			} else {
				containedPhoneMap.put(linkPhone, null);
			}
		}
	}
}
