package com.tyt.carteam.service.impl;

import javax.annotation.Resource;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import org.springframework.stereotype.Service;

import com.tyt.carteam.service.CarteamPhoneService;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytCarteamPhone;
import com.tyt.service.base.BaseServiceImpl;

import java.util.Date;
import java.util.List;

@Service("carteamPhoneService")
public class CarteamPhoneServiceImpl extends BaseServiceImpl<TytCarteamPhone, Long> implements CarteamPhoneService {

	@Resource(name = "carteamPhoneDao")
	public void setBaseDao(BaseDao<TytCarteamPhone, Long> carteamPhoneDao) {
		super.setBaseDao(carteamPhoneDao);
	}

    @Override
    public void savePhone(List<TytCarteamPhone> phoneList, Long carteamId, EmployeeQueryBean curUser) {
	    //删除该车队下的所有联系人号码
        deletePhone(carteamId);
        //添加到车队联系人
        if (phoneList != null && phoneList.size()>0){
            for (TytCarteamPhone phone : phoneList) {
                phone.setCarteamId(carteamId);
                phone.setIsDel((byte)1);
                phone.setCreateId(curUser.getId());
                phone.setCreateName(curUser.getName());
                phone.setModifyId(curUser.getId());
                phone.setModifyName(curUser.getName());
                phone.setCtime(new Date());
                phone.setUtime(new Date());
                this.getBaseDao().insert(phone);
            }
        }
    }

    @Override
    public void deletePhone(Long carteamId, String cellPhone) {
        String sql = "UPDATE `tyt_carteam_phone` SET is_del=2 WHERE carteam_id=? AND phone=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[] { carteamId, cellPhone });
    }

    private void deletePhone(Long carteamId){
	    String sql = "UPDATE `tyt_carteam_phone` SET is_del=2 WHERE carteam_id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[] { carteamId });
    }

    /**
     * @Description  同步车队线索联系人信息到车队联系人表中
     * <AUTHOR>
     * @Date  2018/11/20 18:26
     * @Param [tytCarteamPhone]
     * @return void
     **/
    @Override
    public void saveCarTeamClewPhone(TytCarteamPhone tytCarteamPhone) {
        //将车队线索联系人插入到联系人表中
        this.getBaseDao().insert(tytCarteamPhone);
    }
}
