package com.tyt.equipment.service.impl;

import com.tyt.dao.base.BaseDao;
import com.tyt.equipment.service.TytTransportWaybillExService;
import com.tyt.model.TytTransportWaybillEx;
import com.tyt.service.base.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("tytTransportWaybillExService")
public class TytTransportWaybillExServiceImpl extends BaseServiceImpl<TytTransportWaybillEx, Long> implements TytTransportWaybillExService {

    @Override
    @Resource(name = "transportWayBillExDao")
    public void setBaseDao(BaseDao<TytTransportWaybillEx, Long> transportWayBillExDao) {
        super.setBaseDao(transportWayBillExDao);
    }

    /**
     * 根据用户ID获取异常上报订单和投诉订单
     * @param userId
     * @return
     */
    @Override
    public List<TytTransportWaybillEx> getTransportWaybillExListByUserId(Long userId) {
        String sql = "SELECT twe.*  FROM tyt_transport_waybill_ex twe " +
                " left join tyt_transport_main ttm ON twe.ts_id = ttm.id " +
                "WHERE " +
                " twe.user_id = ? " +
                " AND twe.ex_status IN (0,1) " +
                " AND ttm.excellent_goods = 1 " +
                "ORDER BY " +
                " twe.id DESC";
        List<TytTransportWaybillEx> transportWaybillExList = this.getBaseDao().queryForList(sql, new Object[]{userId});
        return transportWaybillExList;
    }

    @Override
    public TytTransportWaybillEx getWayBillExById(Long id) {
        return this.getBaseDao().findById(id);
    }

    /**
     * 根据订单ID和订单类型查询异常上报信息
     *
     * @param orderId
     * @param orderType
     * @return
     */
    @Override
    public TytTransportWaybillEx getTransportWaybillExByOrderIdAndOrderType(Long orderId, Integer orderType) {
        String sql = "select * from tyt_transport_waybill_ex where 1=1 and order_id = ? and order_type = ? order by id desc";

        List<TytTransportWaybillEx> transportWaybillExList = this.getBaseDao().queryForList(sql, new Object[]{orderId, orderType});
        if(CollectionUtils.isNotEmpty(transportWaybillExList)){
            return transportWaybillExList.get(0);
        }
        return null;
    }

    @Override
    public  List<TytTransportWaybillEx> getTransportWaybillExByOrderId(Long orderId,Integer orderType) {
        String sql = "select * from tyt_transport_waybill_ex where 1=1 and order_id = ? and order_type = ? order by id desc";
        return this.getBaseDao().queryForList(sql, new Object[]{orderId,orderType});
    }

}
