package com.tyt.equipment.dao.base;

import java.io.Serializable;
import java.util.List;

import org.hibernate.HibernateException;

/**  
 * @Title: IBaseDao.java
 * @Package com.tyt.dao.equipment.base
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年6月17日
 */
public interface IBaseDao {

	
	public void deleteObject(Object obj) throws HibernateException;
	public void deleteObject(Class class1,Serializable serializable) throws HibernateException;
	public Object deleteObject(String hsql,Object obj);
	public void updateObject(Object obj) throws HibernateException;
	public Object updateObject(String hsql, Object obj);
	public Object saveOrUpdateObject(Object obj);
	public Object saveOrUpdateObject(String hsql, Object obj);
	public Object saveObject(Object obj) throws HibernateException;
	public Object getObject(Class class1,Serializable serializable) throws HibernateException;
	public List queryHql(String hsql) throws HibernateException;
	public List queryHql(String hsql, Object obj) throws HibernateException;	
	public List queryHql(String sql,Object obj,Integer pageNo,Integer maxRow) throws HibernateException;
	public List query(final String sql, final Object obj,final Integer pageNo,final Integer maxRow) throws HibernateException;
	public Object update(final String hsql, final Object obj) throws HibernateException;
	
}
