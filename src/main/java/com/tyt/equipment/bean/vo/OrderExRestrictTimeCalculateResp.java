package com.tyt.equipment.bean.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 异常上报处理处罚计算最终限制时间响应类
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11 15:28
 */
@Data
public class OrderExRestrictTimeCalculateResp implements Serializable {

    /**
     * 车方拉黑最终时间
     */
    private Date carBlackListTime;
    /**
     * 货方拉黑最终时间
     */
    private Date goodsBlackListTime;
    /**
     * 车方限制找货最终时间
     */
    private Date carRestrictTime;
    /**
     * 货方限制发货最终时间
     */
    private Date goodsRestrictTime;
}
