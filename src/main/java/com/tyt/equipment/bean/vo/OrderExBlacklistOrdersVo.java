package com.tyt.equipment.bean.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 异常上报待处理详情处罚记录VO类
 * <AUTHOR>
 * @version 1.0
 * @date 2023-10-12 10:08:08
 */
@Data
@Builder
public class OrderExBlacklistOrdersVo implements Serializable {

    /**
     * 类型量值（拉黑）
     */
    public static final int TYPE_BLACK = 1;
    /**
     * 类型量值（发货限制）
     */
    public static final int TYPE_LIMIT_GOODS = 2;
    /**
     * 类型量值（找货限制）
     */
    public static final int TYPE_LIMIT_CAR = 3;

    /**
     * 当前状态-永久处罚
     */
    public static final int STATUS_PERPETUAL = 1;
    /**
     * 当前状态-解除
     */
    public static final int STATUS_RELIEVE = 2;
    /**
     * 当前状态-天数处罚
     */
    public static final int STATUS_DAYS = 3;

    /**
     * 1-拉黑；2-发货限制；3-找货限制；
     */
    private Integer type;
    /**
     * 当前处罚状态（1-永久处罚；2-解除；3-天数处罚）
     */
    private Integer status;
    /**
     * 可增加或减少的天数
     */
    private Integer days;
    /**
     * 运单号
     */
    private String orderNo;
    /**
     * 关联异常上报ID
     */
    private List<OrderExVo> exIdList;
    /**
     * 限制天数（-1表示永久）
     */
    private Integer restrictNum;
    /**
     * 处罚时间列表
     */
    private List<OrderExRestrictTimeVo> restrictTimeList;
}
