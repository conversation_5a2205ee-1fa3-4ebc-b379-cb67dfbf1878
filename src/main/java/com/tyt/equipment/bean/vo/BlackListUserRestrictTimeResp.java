package com.tyt.equipment.bean.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 拉黑处罚计算最终限制时间请求类
 * <AUTHOR>
 * @version 1.0
 * @date 2023-10-11 17:38:35
 */
@Data
public class BlackListUserRestrictTimeResp implements Serializable {

    /**
     * 拉黑开始时间
     */
    private Date restrictStartTime;
    /**
     * 拉黑结束时间
     */
    private Date restrictEndTime;
    /**
     * 找货限制开始时间
     */
    private Date carRestrictStartTime;
    /**
     * 找货限制结束时间
     */
    private Date carRestrictEndTime;
    /**
     * 发货限制开始时间
     */
    private Date goodsRestrictStartTime;
    /**
     * 发货限制结束时间
     */
    private Date goodsRestrictEndTime;
}
