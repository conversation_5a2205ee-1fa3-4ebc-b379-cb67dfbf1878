package com.tyt.company.service.impl;

import com.tyt.company.bean.BusiLine;
import com.tyt.company.bean.Company;
import com.tyt.company.service.ICompanyService;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.PageBean;
import com.tyt.mybatis.mapper.CompanyMapper;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.web.qbean.CompanyQueryBean;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("companyService")
public class CompanyServiceImpl extends BaseServiceImpl<CompanyQueryBean,Long>  implements ICompanyService {
    private static final Logger logger = LoggerFactory.getLogger(CompanyServiceImpl.class);

    @Autowired
    private CompanyMapper companyMapper;


    @Override
    public List<Company> queryCompanyList() {
        return companyMapper.queryCompanyList();
    }

    @Override
    public List<BusiLine> queryBusiLineByCompanyName(String companyName, int isValid) {
        if(StringUtils.isNotEmpty(companyName)) {
            return companyMapper.queryBusiLineByCompanyName(companyName, isValid);
        }else{
            return companyMapper.queryBusiLine(isValid);
        }
    }

    @Override
    @Resource(name="companyDao")
    public void setBaseDao(BaseDao<CompanyQueryBean,Long> companyDao) {
        super.setBaseDao(companyDao);
    }
    /**
     * 查询公司及业务线列表数据
     * @param company  公司对象
     * @param page
     * @return
     * @throws Exception
     */
    @Override
    public List<CompanyQueryBean> queryCompanyAndBusinessLine(CompanyQueryBean company, PageBean page) throws Exception {

        List<Object> listObject = new ArrayList<Object>();

        //查询公司业务线数据的sql语句
        StringBuffer sbCount = new StringBuffer(
                "select count(*) from  tyt_company as a left join tyt_company_busi_line as b on a.id=b.company_id where 1=1 ");

        //拼接查询条件的sql
        StringBuffer sb = new StringBuffer();

        //合作公司ID
        Integer userId = company.getId();
        if(userId != null)
        {
            sb.append(" AND a.id = ? ");
            listObject.add(company.getId());
        }
        //业务线ID
        Integer  busiLineId = company.getBusiLineId();
        if(busiLineId != null)
        {
            sb.append(" AND b.id = ? ");
            listObject.add(company.getBusiLineId());
        }
        //业务线状态 1:正常 0：停用
        Integer isValid = company.getIsValid();
        if(isValid != null)
        {
            sb.append(" AND b.is_valid = ? ");
            listObject.add(company.getIsValid());
        }
        sbCount.append(sb);

        //查询总条数
        BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), listObject.toArray());
        if (rowCount != null && rowCount.longValue() > 0) {

            StringBuffer sbSql = new StringBuffer(
                    "select a.id as id,a.company_name as companyName,b.id as busiLineId, b.busi_line_name as busiLineName,b.is_valid as isValid"
                            +  " from  tyt_company as a left join tyt_company_busi_line as b on a.id=b.company_id where 1=1 ");
            Map<String, Type> map = new HashMap<String, Type>();
            //公司Id
            map.put("id", Hibernate.INTEGER);
            //公司名称
            map.put("companyName", Hibernate.STRING);
            //业务线Id
            map.put("busiLineId", Hibernate.INTEGER);
            //业务线名称
            map.put("busiLineName", Hibernate.STRING);
            //业务线状态
            map.put("isValid", Hibernate.INTEGER);
            //拼接上面的查询条件
            sbSql.append(sb);
            sbSql.append(" order by b.id asc ");
            page.setRowCount(rowCount.longValue());

            List<CompanyQueryBean> list = this.getBaseDao().search(
                    sbSql.toString(), map, CompanyQueryBean.class,
                    listObject.toArray(), page.getCurrentPage(),
                    page.getPageSize());
            return list;
        }
        return null;
    }

    /**
     * 根据公司的名称获取公司信息的方法
     * @param companyName
     * @return
     */
    @Override
    public Company getCompanyInfoByCompanyName(String companyName) {
        String sql = "select a.id as id,a.company_name as companyName,a.is_valid as isValid,a.remark as remark from tyt_company a where 1=1 and a.company_name = ?";

        //映射的实体类属性
        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
        map.put("id",Hibernate.INTEGER);
        map.put("companyName",Hibernate.STRING);
        map.put("isValid",Hibernate.INTEGER);
        map.put("remark",Hibernate.STRING);

        //查询条件数组
        Object[] params = new Object[]{companyName};
        List<Company> companys = this.getBaseDao().search(sql,map,Company.class,params);
        if (companys == null || companys.size() <= 0){
            return null;
        }else{
            Company company = companys.get(0);
            return company;
        }
    }

    /**
     * 查询公司下拉列表数据的方法
     * @return
     * @throws Exception
     */
    @Override
    public List<Company> queryCompanyLists() throws Exception {
        //查询合作公司sql语句
        String sql = "select * from tyt_company where 1=1";

        Object[] params = null;
        List<Company> companys = this.getBaseDao().findAll(sql,params);
        return companys;
    }

    /**
     * 根据公司Id查询业务线下拉列表数据的方法
     * @return
     * @throws Exception
     */
    @Override
    public List<BusiLine> queryBusLineLists(int companyId) throws Exception {
        //查询业务线sql语句
        String sql = "select * from tyt_company_busi_line where 1=1 and company_id = ?";

        Object[] params = new Object[]{companyId};
        List<BusiLine> busiLines = this.getBaseDao().findAll(sql,params);
        return busiLines;
    }

    /**
     * 新增合作公司的方法
     * @param companyName 公司名称
     * @throws Exception
     */
    @Override
    public void addCompany(String companyName) throws Exception {

        //新增合作企业
        String sql = "insert into tyt_company(company_name) values (:companyName)";
        Map<String, Object> map = new HashMap<String,Object>();
        map.put("companyName",companyName);

        this.getBaseDao().executeUpdateSql(sql,map);
    }

    /**
     * 修改公司名称的方法
     *
     * @param companyId   公司Id
     * @param companyName 公司新名称
     * @return
     * @throws Exception
     */
    @Override
    public int updateCompany(int companyId, String companyName) throws Exception {

        //修改公司名称的sql
        String sql = "update tyt_company set company_name = :companyName where id= :companyId";
        Map<String, Object> map = new HashMap<String,Object>();
        map.put("companyName",companyName);
        map.put("companyId",companyId);

        int result = this.getBaseDao().executeUpdateSql(sql,map);
        return result;
    }

    /**
     * 新增业务线的方法
     *
     * @param companyId
     * @param busiLineName
     * @throws Exception
     */
    @Override
    public void addBusiLine(int companyId, String busiLineName) throws Exception {

        //新增业务线sql
        String sql = "insert into tyt_company_busi_line(company_id,busi_line_name) values (:companyId, :busiLineName)";
        Map<String, Object> map = new HashMap<String,Object>();
        map.put("companyId", companyId);
        map.put("busiLineName", busiLineName);
        this.getBaseDao().executeUpdateSql(sql,map);

    }

    /**
     * 修改业务线名称的方法
     *
     * @param busiLineId   业务线Id
     * @param busiLineName 业务线名称
     * @return
     * @throws Exception
     */
    @Override
    public int updateBusiLine(int busiLineId, String busiLineName) throws Exception {

        //修改业务线名称的sql
        String sql = "update tyt_company_busi_line set busi_line_name = :busiLineName where id = :busiLineId";
        Map<String, Object> map = new HashMap<String,Object>();
        map.put("busiLineName",busiLineName);
        map.put("busiLineId",busiLineId);

        int result = this.getBaseDao().executeUpdateSql(sql,map);
        return result;
    }

    /**
     * 更新业务线状态的方法
     *
     * @param busiLineId 业务线Id
     * @param isValid    业务线状态 1:正常  0:停用
     * @return
     * @throws Exception
     */
    @Override
    public int updateBusiLineStatus(int busiLineId, int isValid) throws Exception {
        //更新业务线状态sql
        String sql = "update tyt_company_busi_line set is_valid = :isValid where id = :busiLineId";
        Map<String, Object> map = new HashMap<String,Object>();
        map.put("isValid",isValid);
        map.put("busiLineId",busiLineId);

        int result = this.getBaseDao().executeUpdateSql(sql,map);
        return result;
    }

    /**
     * 根据公司Id和业务线名称判断该业务线是否存在的方法
     *
     * @param companyId    公司Id
     * @param busiLineName 业务线名称
     * @throws Exception
     */
    @Override
    public BusiLine getBusiLineNameByIdAndName(int companyId, String busiLineName) throws Exception {
        //查询企业名称是否存在的sql
        String sql = "select a.id as id,a.company_id as companyId,a.busi_line_name as busiLineName,a.is_valid as isValid,a.remark as remark from tyt_company_busi_line a where 1=1 and a.company_id = ? and a.busi_line_name = ?";

        //映射的实体类属性
        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
        map.put("id", Hibernate.INTEGER);
        map.put("companyId",Hibernate.INTEGER);
        map.put("busiLineName",Hibernate.STRING);
        map.put("isValid",Hibernate.INTEGER);
        map.put("remark",Hibernate.STRING);

        //查询条件数组
        Object[] params = new Object[]{companyId,busiLineName};

        List<BusiLine> busiLines = this.getBaseDao().search(sql,map,BusiLine.class,params);
        if (busiLines == null || busiLines.size() <= 0){
            return null;
        }else{
            BusiLine busiLine = busiLines.get(0);
            return busiLine;
        }
    }

    /**
     * 根据公司的Id获取公司信息的方法
     *
     * @param companyId
     * @return
     */
    @Override
    public Company getCompanyById(int companyId) {
        String sql = "select a.id as id,a.company_name as companyName,a.is_valid as isValid,a.remark as remark from tyt_company a where 1=1 and a.id = ?";

        //映射的实体类属性
        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
        map.put("id",Hibernate.INTEGER);
        map.put("companyName",Hibernate.STRING);
        map.put("isValid",Hibernate.INTEGER);
        map.put("remark",Hibernate.STRING);

        //查询条件数组
        Object[] params = new Object[]{companyId};
        List<Company> companys = this.getBaseDao().search(sql,map,Company.class,params);
        if (companys == null || companys.size() <= 0){
            return null;
        }else{
            Company company = companys.get(0);
            return company;
        }
    }
}
