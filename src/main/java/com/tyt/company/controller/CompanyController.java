package com.tyt.company.controller;

import com.tyt.company.bean.BusiLine;
import com.tyt.company.bean.Company;
import com.tyt.company.service.ICompanyService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.Constant;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.CompanyQueryBean;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@RequestMapping("/company")
public class CompanyController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(CompanyController.class);

    @Autowired
    private ICompanyService companyService;

    @RequestMapping("/companyList")
    @ResponseBody
    public ResultMsgBean companyList(){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        List<Company> companyList = companyService.queryCompanyList();
        resultMsgBean.setData(companyList);
        return resultMsgBean;
    }

    @RequestMapping("/busiLineList")
    @ResponseBody
    public ResultMsgBean busiLineList(@RequestParam String companyName){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        List<BusiLine> busiLineList = companyService.queryBusiLineByCompanyName(companyName, 1);
        resultMsgBean.setData(busiLineList);
        return resultMsgBean;
    }

    @RequestMapping("/list")
    @ResponseBody
    public ResultMsgBean busiLineList(@RequestParam(defaultValue = "") String companyName,
                                      @RequestParam(defaultValue = "1") int isValid){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        List<BusiLine> busiLineList = companyService.queryBusiLineByCompanyName(companyName, isValid);
        resultMsgBean.setData(busiLineList);
        return resultMsgBean;
    }
    //----------------------------------------------------------------------------------------------------
    /**
     * 合作公司管理-合作公司及其业务线列表查询
     * @param company  公司对象
     * @param pageNo   当前页数
     * @param pageSize 每页显示的条数
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/companylist")
    public String companylist(CompanyQueryBean company, Integer pageNo, Integer pageSize,
                              HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
                return "back/jsp/admin_login";
            }
            if (pageNo == null || pageNo.intValue() <= 0)
            {
                pageNo = 1;
            }
            PageBean pageBean = new PageBean();
            pageBean.setCurrentPage(pageNo);
            if (pageSize == null || pageSize.intValue() <= 0)
            {
                pageSize = Constant.DEFAULT_PAGE_SIZE;
            }
            pageBean.setPageSize(pageSize);
            //查询合作公司及业务线数据
            List<CompanyQueryBean> companys = companyService.queryCompanyAndBusinessLine(company,pageBean);
            request.setAttribute("rowCount", pageBean.getRowCount());
            request.setAttribute("pageNo", pageNo);
            request.setAttribute("pageSize", pageSize);
            request.setAttribute("maxPage", pageBean.getMaxPage());
            //查询出的公司和业务线数据
            request.setAttribute("companys",companys);

            //原公司和业务线数据对象
            request.setAttribute("company",company);

            //合作公司下拉框数据,弹出层使用
            List<Company> companysData = companyService.queryCompanyLists();
            request.setAttribute("companyData",companysData);
        } catch (Exception e) {
            logger.info("合作公司管理-合作公司及其业务线列表查询发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return "company/jsp/company_list";
    }

    /**
     * 获取公司下拉列表数据的方法
     * @return
     */
    @RequestMapping(value = "/companylists")
    @ResponseBody
    public ResultMsgBean  companylists() {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        try {
            //合作公司下拉框数据
            List<Company> companysData = companyService.queryCompanyLists();
            resultMsgBean.setData(companysData);

            resultMsgBean.setCode(ResultMsgBean.OK);
            resultMsgBean.setMsg("查询成功");
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("获取公司下拉列表数据发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }

    /**
     * 根据公司Id获取业务线列表信息
     * @param companyId   公司Id
     * @return
     */
    @RequestMapping(value = "/busiLinelists")
    @ResponseBody
    public ResultMsgBean  busiLinelists(@RequestParam int companyId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        try {
            //业务线下拉框数据，根据公司Id进行查询
            List<BusiLine> busiLinesData = companyService.queryBusLineLists(companyId);
            resultMsgBean.setData(busiLinesData);

            resultMsgBean.setCode(ResultMsgBean.OK);
            resultMsgBean.setMsg("查询成功");
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("根据公司Id获取业务线列表数据发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }


    /**
     * 新增合作企业
     * @param request
     * @param response
     * @param companyName 公司名称
     * @return
     */
    @RequestMapping("/addCompany")
    @ResponseBody
    public ResultMsgBean addCompany(HttpServletRequest request, HttpServletResponse response,String companyName) {

        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "新增数据");
        try {
            //判断企业名称是否为空
            if(StringUtils.isBlank(companyName)){
                msgBean.setCode(101);
                msgBean.setMsg("企业名称不能为空");
                return msgBean;
            }
            //判断企业名称是否存在
            Company company = companyService.getCompanyInfoByCompanyName(companyName);
            if(company!=null){
                msgBean.setCode(103);
                msgBean.setMsg("该企业已经存在，不能重复添加");
                return msgBean;
            }
            companyService.addCompany(companyName);
            msgBean.setCode(ResultMsgBean.OK);
            msgBean.setMsg("添加成功");
        } catch (Exception e) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("服务器错误");
            logger.info(" 新增合作企业发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return msgBean;
    }

    /**
     * 修改合作企业名称
     * @param request
     * @param response
     * @param companyId   企业Id
     * @param companyName 企业名称
     * @return
     */
    @RequestMapping("/editCompany")
    @ResponseBody
    public ResultMsgBean editCompany(HttpServletRequest request, HttpServletResponse response,int companyId,String companyName) {

        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "修改数据");
        try {
            //判断企业名称是否为空
            if(StringUtils.isBlank(companyName)){
                msgBean.setCode(101);
                msgBean.setMsg("企业名称不能为空");
                return msgBean;
            }
            //判断企业名称是否存在
            Company company = companyService.getCompanyInfoByCompanyName(companyName);
            if(company!=null){
                msgBean.setCode(103);
                msgBean.setMsg("新企业名称已经存在，不能重复添加");
                return msgBean;
            }
            int result = companyService.updateCompany(companyId,companyName);
            if(result > 0)
            {
                msgBean.setCode(ResultMsgBean.OK);
                msgBean.setMsg("修改成功");
            }else{
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("修改失败");
                return msgBean;
            }
        } catch (Exception e) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("服务器错误");
            logger.info(" 修改合作企业名称发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return msgBean;
    }

    /**
     * 新增业务线
     * @param request
     * @param response
     * @param companyId    企业Id
     * @param busiLineName 业务线名称
     * @return
     */
    @RequestMapping("/addBusiLine")
    @ResponseBody
    public ResultMsgBean addBusiLine(HttpServletRequest request, HttpServletResponse response,int companyId,String busiLineName) {

        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "修改数据");
        try {
            //判断业务线名称是否为空
            if(StringUtils.isBlank(busiLineName)){
                msgBean.setCode(101);
                msgBean.setMsg("未输入业务线名称");
                return msgBean;
            }
            //判断业务线名称是否存在
            BusiLine  busiLine = companyService.getBusiLineNameByIdAndName(companyId, busiLineName);
            if(busiLine !=null){
                msgBean.setCode(103);
                msgBean.setMsg("业务线名称已经存在，不能重复添加");
                return msgBean;
            }
            companyService.addBusiLine(companyId, busiLineName);
            msgBean.setCode(ResultMsgBean.OK);
            msgBean.setMsg("添加成功");
        } catch (Exception e) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("服务器错误");
            logger.info(" 新增业务线发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return msgBean;
    }

    /**
     * 修改业务线名称
     * @param request
     * @param response
     * @param companyId       企业Id
     * @param busiLineId      业务线Id
     * @param newBusiLineName 业务线新名称
     * @return
     */
    @RequestMapping("/editBusiLine")
    @ResponseBody
    public ResultMsgBean editBusiLine(HttpServletRequest request, HttpServletResponse response,int companyId,int busiLineId,String newBusiLineName) {

        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "修改数据");
        try {
            //判断业务线新名称是否为空
            if(StringUtils.isBlank(newBusiLineName)){
                msgBean.setCode(101);
                msgBean.setMsg("企业名称不能为空");
                return msgBean;
            }
            //判断业务线名称是否存在
            BusiLine  busiLine = companyService.getBusiLineNameByIdAndName(companyId, newBusiLineName);
            if(busiLine !=null){
                msgBean.setCode(103);
                msgBean.setMsg("业务线名称已经存在，不能重复添加");
                return msgBean;
            }
            int result = companyService.updateBusiLine(busiLineId,newBusiLineName);
            if(result > 0)
            {
                msgBean.setCode(ResultMsgBean.OK);
                msgBean.setMsg("修改成功");
            }else{
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("修改失败");
            }
        } catch (Exception e) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("服务器错误");
            logger.info(" 修改业务线名称发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return msgBean;
    }

    /**
     * 更新业务线状态
     * @param request
     * @param response
     * @param busiLineId  业务线Id
     * @param isValid     业务线要更新的状态 1:正常 0:停用
     * @return
     */
    @RequestMapping("/updateBusiLineStatus")
    @ResponseBody
    public ResultMsgBean updateBusiLineStatus(HttpServletRequest request, HttpServletResponse response, int busiLineId, int isValid) {

        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "修改数据状态");
        try {
            int result = companyService.updateBusiLineStatus(busiLineId,isValid);
            if(result > 0) {
                msgBean.setCode(ResultMsgBean.OK);
                msgBean.setMsg("修改数据状态成功");
            }else{
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("修改数据状态失败");
            }
        } catch (Exception e) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("服务器错误");
            logger.info(" 更新业务线状态发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return msgBean;
    }
}
