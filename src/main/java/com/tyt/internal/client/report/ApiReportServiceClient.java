package com.tyt.internal.client.report;

import com.tyt.internal.config.InternalServiceConstant;
import com.tyt.internal.client.report.export.ExportTaskDTO;
import com.tyt.internal.client.report.export.ExportTaskVO;
import com.tyt.internal.config.WebResult;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;


/**
 * 数据报表服务远程接口
 *
 * <AUTHOR>
 * @date 2024/5/30 17:16
 */
public interface ApiReportServiceClient {

    String BASE_URL = InternalServiceConstant.REPORT_SERVICE_URL + "/rule";

    String EXPORT_BASE_URL = InternalServiceConstant.REPORT_SERVICE_URL + "/export";


    /**
     * 添加导出报表任务
     *
     * @param dto 导出报表参数
     * @return
     */
    @POST(EXPORT_BASE_URL + "/handleTask")
    Call<WebResult<ExportTaskVO>> handleTask(@Body ExportTaskDTO dto);


}
