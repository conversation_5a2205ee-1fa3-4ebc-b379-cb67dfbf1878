package com.tyt.internal.client.report.export;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 报表导出任务返回视图
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportTaskVO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 处理的service
     */
    private String handleService;

    /**
     * 请求参数
     */
    private String inputParams;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 导出次数
     */
    private Integer exportCount;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String modifyTime;


    /**
     * 进度
     */
    private Integer progress;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 状态：0 保存任务成功、1 写入文件成功、2 文件处理成功、3 文件处理失败
     */
    private Integer status;
}
