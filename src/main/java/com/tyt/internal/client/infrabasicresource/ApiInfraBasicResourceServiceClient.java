package com.tyt.internal.client.infrabasicresource;

import com.tyt.internal.client.infrabasicresource.vo.ModifyResourceOrConfigVO;
import com.tyt.internal.config.InternalServiceConstant;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 修改开关公共资源AB测试
 *
 * <AUTHOR>
 * @since 2024/7/18 15:45
 */
public interface ApiInfraBasicResourceServiceClient {

    String BASE_URL = InternalServiceConstant.OUTER_SERVICE_URL;


    @POST(BASE_URL + "/rpc/modifyResourceOrConfig/dingMessage")
    Call<Void> dingMessage(@Body ModifyResourceOrConfigVO modifyResourceOrConfigVO);

}
