package com.tyt.internal.config;

import com.tyt.service.common.exception.TytException;
import com.tyt.util.PlatCommonUtil;
import lombok.extern.slf4j.Slf4j;
import retrofit2.Response;
import com.tyt.service.common.enums.ResponseEnum;

/**
 * 内部调用服务util
 *
 * <AUTHOR>
 * @date 2024/4/24 13:40
 */
@Slf4j
public class InternalClientUtil {

    /**
     * 获取数据，如果有错误则抛出异常
     * @param response response
     * @param <T> response
     * @return T
     */
    public static <T> T getDataDetail(Response<InternalWebResult<T>> response) {
        log.info("getDataDetail_response:{}", response);
        if (!response.isSuccessful()) {
            log.info("internal_client_response_error:{}", response);
            throw TytException.createException(ResponseEnum.rest_error.info("请求服务失败！"));
        }

        InternalWebResult<T> bodyResult = response.body();

        PlatCommonUtil.printFixLog("getDataDetail", bodyResult);

        T resultData = InternalWebResult.getResultData(bodyResult);
        return resultData;
    }

}
