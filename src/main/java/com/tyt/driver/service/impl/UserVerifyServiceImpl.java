package com.tyt.driver.service.impl;

import com.tyt.driver.bean.GlobalStatusEnum;
import com.tyt.driver.bean.IdCardTwoElementVerifyDTO;
import com.tyt.driver.bean.IdCardTwoElementVerifyVO;
import com.tyt.driver.service.UserVerifyService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.ocr.service.CommonApiService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * 人身核验相关.
 * <AUTHOR>
 *
 * @date 2023-9-6 13:37:40
 */
@Slf4j
@Service
public class UserVerifyServiceImpl implements UserVerifyService {

    /**
     * 每天最多验证次数5.
     */
    public static final int DAY_VERIFY_MAX = 5;



    public static final int DAY_SECOND = 24 * 60 * 60;


    @Autowired
    private CommonApiService commonApiService;


    private String getDayErrorMsg() {
        String msg = "今天提交已超过" + DAY_VERIFY_MAX + "次，请明天再试";
        return msg;
    }

    public static String getUserRealVerifyKey(String idCaard) {
        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String key = "tyt:manage:cache:userRealVerify";
        String verifyKey = CommonUtil.joinRedisKey(key, dayShort, idCaard + "");
        return verifyKey;
    }


    @Override
    public int getVerifyDayCount(String idCard, boolean check) {
        String verifyKey = getUserRealVerifyKey(idCard);

        String countValue = RedisUtil.get(verifyKey);

        int verifyCount = 0;

        if (StringUtils.isNotBlank(countValue)) {
            verifyCount = Integer.parseInt(countValue);
        }

        if (check && verifyCount >= DAY_VERIFY_MAX) {
            throw TytException.createException(ResponseEnum.request_error.info(this.getDayErrorMsg()));
        }

        return verifyCount;
    }

    @Override
    public int incrVerifyDayCount(String idCard) {
        int verifyCount = this.getVerifyDayCount(idCard, false);
        verifyCount = verifyCount + 1;

        String verifyKey = getUserRealVerifyKey(idCard);

        RedisUtil.set(verifyKey, verifyCount + "", DAY_SECOND);
        return verifyCount;
    }

    @Override
    public Integer realVerify(String idCard, String userName) {

        IdCardTwoElementVerifyDTO req = new IdCardTwoElementVerifyDTO();
        req.setIdCardNo(idCard);
        req.setIdCardName(userName);
        IdCardTwoElementVerifyVO idCardTwoElementVerifyVO = commonApiService.twoElementVerify(req);


        Integer result = idCardTwoElementVerifyVO.isSuccess() ? GlobalStatusEnum.YES.getCode() : GlobalStatusEnum.NO.getCode();

        return result;
    }



    /**
     * 三网验证

     * @param idCard
     * @param userName
     * @return Integer
     */
    @Override
    public Integer checkAndRealVerify(String idCard, String userName) {
        //判断验证次数
        this.getVerifyDayCount(idCard, true);
        //调取验证
        Integer result = this.realVerify(idCard, userName);
        //验证次数+1
        this.incrVerifyDayCount(idCard);
        //返回验证结果
        return result;
    }

}
