package com.tyt.driver.service;


import com.tyt.driver.bean.CarDriverOptionReq;
import com.tyt.driver.bean.CarDriverReq;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCarDriverArchives;
import com.tyt.service.base.BaseService;

import java.util.List;

public interface TytCarDriverArchivesService extends BaseService<TytCarDriverArchives, Long> {

    List<TytCarDriverArchives> searchList(CarDriverReq carDriverReq, PageBean pageBean);

    ResultMsgBean getDriverlicenseData(String pic,String type);

    ResultMsgBean updateArchivers(CarDriverOptionReq driverOptionReq, EmployeeQueryBean queryBean );
}
