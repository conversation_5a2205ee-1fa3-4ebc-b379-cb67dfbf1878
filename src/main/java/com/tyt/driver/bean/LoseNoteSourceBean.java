package com.tyt.driver.bean;

import com.tyt.web.back.internal.bean.SourceBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/01/25 17:16
 */
public class LoseNoteSourceBean {
    
    private List<SourceBean> dicfList;
    private List<SourceBean> dicbList;
    private List<SourceBean> dlList;
    private List<SourceBean> dcList;

    public List<SourceBean> getDicfList() {
        return dicfList;
    }

    public void setDicfList(List<SourceBean> dicfList) {
        this.dicfList = dicfList;
    }

    public List<SourceBean> getDicbList() {
        return dicbList;
    }

    public void setDicbList(List<SourceBean> dicbList) {
        this.dicbList = dicbList;
    }

    public List<SourceBean> getDlList() {
        return dlList;
    }

    public void setDlList(List<SourceBean> dlList) {
        this.dlList = dlList;
    }

    public List<SourceBean> getDcList() {
        return dcList;
    }

    public void setDcList(List<SourceBean> dcList) {
        this.dcList = dcList;
    }
}
