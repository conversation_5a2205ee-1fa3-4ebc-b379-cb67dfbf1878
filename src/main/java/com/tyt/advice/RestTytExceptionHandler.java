package com.tyt.advice;

import com.tyt.manager.enums.ManageResponseEnum;
import com.tyt.model.TypedResultMsgBean;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Optional;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/09/07 15:50
 **/

@Slf4j
@RestControllerAdvice
public class RestTytExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    TypedResultMsgBean<?> onBindException(MethodArgumentNotValidException bindException) {
        log.warn("参数校验异常:{}", bindException.getMessage());
        String defaultMessage = Optional.of(bindException.getBindingResult())
                .map(BindingResult::getFieldError)
                .map(FieldError::getDefaultMessage)
                .orElse(null);

        return new TypedResultMsgBean<>(TypedResultMsgBean.ERROR, defaultMessage);
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    TypedResultMsgBean<?> onBindException(BindException bindException) {
        log.warn("参数校验异常:{}", bindException.getMessage());
        String defaultMessage = Optional.of(bindException.getBindingResult())
                .map(BindingResult::getFieldError)
                .map(FieldError::getDefaultMessage)
                .orElse(null);

        return new TypedResultMsgBean<>(ManageResponseEnum.request_error.getCode(), defaultMessage);
    }

    @ExceptionHandler(TytException.class)
    @ResponseStatus(HttpStatus.OK)
    TypedResultMsgBean<String> onFailureResultException(TytException tytException) {
        StackTraceElement[] stackTraces = tytException.getStackTrace();

        Object[] subarray = ArrayUtils.subarray(stackTraces, 0, 5);

        StringJoiner sj = new StringJoiner(System.lineSeparator());

        sj.add(tytException.getErrorMsg());

        for (Object stackTrace : subarray) {
            sj.add("\tat " + stackTrace);
        }

        log.warn("业务异常 :{}", sj);

        return new TypedResultMsgBean<>(TypedResultMsgBean.ERROR, tytException.getErrorMsg());
    }
}
