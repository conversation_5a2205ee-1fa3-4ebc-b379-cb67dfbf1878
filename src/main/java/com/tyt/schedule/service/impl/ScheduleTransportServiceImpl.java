package com.tyt.schedule.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.common.exception.BusinessException;
import com.tyt.common.service.TytGeoDictService;
import com.tyt.common.service.TytMapDictService;
import com.tyt.corp.CorpRestClient;
import com.tyt.corp.bean.CorpResultBean;
import com.tyt.dao.base.BaseDao;
import com.tyt.infofee.bean.*;
import com.tyt.model.*;
import com.tyt.model.MqBaseMessageBean;
import com.tyt.peopleorders.bean.MqSyncCommunicateMsg;
import com.tyt.peopleorders.service.TransportMtService;
import com.tyt.schedule.bean.RegionPointBean;
import com.tyt.schedule.bean.ScheduleTransportBean;
import com.tyt.schedule.bean.ScheduleUserBean;
import com.tyt.schedule.service.ScheduleTransportService;
import com.tyt.schedule.service.ScheduleUserService;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.common.amap.service.Route;
import com.tyt.service.user.UserService;
import com.tyt.util.ArithUtil;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import com.tyt.util.service.plat.PlatResultMsgBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

@Service("scheduleTransportService")
public class ScheduleTransportServiceImpl extends BaseServiceImpl<TransportMt, Long> implements ScheduleTransportService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userService")
    private UserService userService;

    /**
     * 地理服务
     */
    @Resource(name = "tytGeoDictService")
    private TytGeoDictService geoDictService;

    @Resource(name = "tytMapDictService")
    TytMapDictService tytMapDictService;

    @Resource(name = "transportMtService")
    TransportMtService transportMtService;

    @Resource(name = "transportMtDao")
    public void setBaseDao(BaseDao<TransportMt, Long> transportMtDao) {
        super.setBaseDao(transportMtDao);
    }

    @Override
    public TransportMt saveTransportMt(TransportMt mt) {
        try {
            TytGeoDict startGeo = geoDictService.getTytGeoDict(mt.getStartProvinc(), mt.getStartCity(), mt.getStartArea());
            TytGeoDict destGeo = geoDictService.getTytGeoDict(mt.getDestProvinc(), mt.getDestCity(), mt.getDestArea());

            // 数据库必填字段
            mt.setRouteDistance(-100); // 公里数必填，否则派单后台出错
            mt.setBusiLine("");
            mt.setCompany(StringUtils.defaultString(mt.getCompany()));

            // 时间
            Timestamp timestamp = new Timestamp(new Date().getTime());
            mt.setUserId(mt.getUserId());
            mt.setTsId(0l);

            String startPoint = this.handleRegion(mt.getStartProvinc(), mt.getStartCity(), mt.getStartArea());
            String destPoint = this.handleRegion(mt.getDestProvinc(), mt.getDestCity(), mt.getDestArea());

            mt.setStartPoint(startPoint); // 出发地 -　必填
            mt.setDestPoint(destPoint); // 目的地 -　必填
            mt.setStartCoordXValue(startGeo.getPx().intValue()); // 出发地x坐标（小数点后最多两位）
            // - 必填
            mt.setStartCoordYValue(startGeo.getPy().intValue()); // 出发地y坐标（小数点后最多两位）
            // - 必填
            mt.setDestCoordXValue(destGeo.getPx().intValue()); // 目的地x坐标（小数点后最多两位）
            // - 必填
            mt.setDestCoordYValue(destGeo.getPy().intValue()); // 目的地y坐标（小数点后最多两位）
            // - 必填
            mt.setStartLongitudeValue(startGeo.getLongitude().intValue()); // 出发地经度
            // - 必填
            mt.setStartLatitudeValue(startGeo.getLatitude().intValue()); // 出发地纬度
            // - 必填
            mt.setDestLongitudeValue(destGeo.getLongitude().intValue()); // 目的地经度
            // - 必填
            mt.setDestLatitudeValue(destGeo.getLatitude().intValue()); // 目的地纬度
            // - 必填
            mt.setIsInfoFee("0"); // 0是不需要、1是需要 - 选填
            mt.setStatus(1); // 状态  1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
            mt.setAndroidDistance(mt.getDistanceValue());
            mt.setIosDistance(mt.getDistanceValue());
            mt.setBrand(mt.getBrand()); // 品牌 - 选填
            mt.setType(mt.getType()); // 机器型号 - 选填
            mt.setGoodNumber(mt.getGoodNumber()); // 台数 - 选填
            mt.setGoodTypeName(mt.getGoodTypeName()); // 货物类型，如”挖掘机” - 选填
            mt.setIsStandard(mt.getIsStandard());// 是否为标准化数据0是，1不是 - 选填
            mt.setMatchItemId(mt.getMatchItemId() == null ? -1 : mt.getMatchItemId());// 如果没有该值则传-1
            mt.setShipperType(mt.getShipperType());
            mt.setRouteDistance(mt.getRouteDistance());
            mt.setCtime(timestamp);
            mt.setMtime(timestamp);
            mt.setOwnerType(1); // 0 小B货源
            mt.setOperatorId(mt.getUserId());
            // 保存本地货源
            this.add(mt);
            return mt;
        } catch (Exception e) {
            logger.error("自有货源发布失败", e);
        }
        return null;
    }

    @Override
    public ScheduleTransportBean searchList(TransportMt mt, Long[] userIds, Long lastId, Integer limit) {
        if (lastId == null) { // 如果lastId 不传参数，则默认为0
            lastId = 0L;
        }
        if (limit == null || limit > 30) { // 当limit 不传或者大于最大值时，limit等于设置的最大值
            limit = 30;
        }
        String today = TimeUtil.formatDate(new Date()); // 当前天时间，处理筛选派单内无效货源使用
        // 组装sql
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        sql.append("select * from tyt_transport_mt where 1=1 ");
        if (userIds != null && userIds.length > 0) {
            String idsQue = parseQuestionmark(userIds);
            sql.append(" and user_id in ("+ idsQue +") ");
            for(Long userId : userIds) {
                params.add(userId);
            }
        }
        //货源状态,我的货源 1.发布中 4.已成交 5.已撤销,多个参数值用逗号分隔
        String goodsStatus = mt.getGoodsStatus();
        if(StringUtils.isNotBlank(goodsStatus)){
            sql.append(" and status in ("+ goodsStatus +") ");
        }
        if (StringUtils.isNotBlank(mt.getStartProvinc())) {
            sql.append(" and start_point = ?");
            String startPoint = this.handleRegion(mt.getStartProvinc(), mt.getStartCity(), mt.getStartArea());
            params.add(startPoint);
        }
        if (StringUtils.isNotBlank(mt.getDestProvinc())) {
            sql.append(" and dest_point = ?");
            String startPoint = this.handleRegion(mt.getDestProvinc(), mt.getDestCity(), mt.getDestArea());
            params.add(startPoint);
        }
        if (mt.getOwnerType() == null) {
            sql.append(" and (ctime > ? or owner_type = 1)");
            params.add(today);
        } else {
            if (mt.getOwnerType() == 0) { // 如果只查询内部派单，则通过时间处理
                sql.append(" and ctime > ? and owner_type = 0 ");
                params.add(today);
            } else { // 如只查询自有货源，则直接获取类型
                sql.append(" and owner_type = 1");
            }
        }
        if (lastId == 0) {
            sql.append(" and id > ? ");
        } else {
            sql.append(" and id < ? ");
        }
        params.add(lastId);
        sql.append(" order by status asc,ctime desc");
        List<TransportMt> mtList = this.getBaseDao().search(sql.toString(), params.toArray(), 0, limit);
        if (CollectionUtils.isNotEmpty(mtList)) {
            lastId = mtList.get(mtList.size() - 1).getId();
        }
        ScheduleTransportBean tsBean = new ScheduleTransportBean();
        tsBean.setLastId(lastId);
        tsBean.setTransportList(mtList);
        return tsBean;
    }

    @Override
    public ScheduleTransportBean dealList(Long userId, Long lastId, Integer limit) {
        if (lastId == null) { // 如果lastId 不传参数，则默认为0
            lastId = 0L;
        }
        if (limit == null || limit > 30) { // 当limit 不传或者大于最大值时，limit等于设置的最大值
            limit = 30;
        }
        String today = TimeUtil.formatDate(new Date()); // 当前天时间，处理筛选派单内无效货源使用
        // 组装sql
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        sql.append("select * from tyt_transport_mt where status in (4,5) and owner_type = 1 and user_id = ? ");
        params.add(userId);
        if (lastId == 0) {
            sql.append(" and id > ? ");
        } else {
            sql.append(" and id < ? ");
        }
        params.add(lastId);
        sql.append(" order by ctime desc");
        List<TransportMt> mtList = this.getBaseDao().search(sql.toString(), params.toArray(), 0, limit);
        if (CollectionUtils.isNotEmpty(mtList)) {
            lastId = mtList.get(mtList.size() - 1).getId();
        }
        ScheduleTransportBean tsBean = new ScheduleTransportBean();
        tsBean.setLastId(lastId);
        tsBean.setTransportList(mtList);
        return tsBean;
    }

    @Override
    public TransportMt detail(Long id) {
        return this.getById(id);
    }

    @Override
    public int updateSetting(Long id, Long userId, int status, String carryName, String carryPhone) {
        String sql = "UPDATE tyt_transport_mt SET status=?, carry_name=?, carry_phone=?, mtime=now() where id=? and user_id=? ";
        int i = this.getBaseDao().executeUpdateSql(sql, new Object[]{status, carryName, carryPhone, id, userId});
        return i;
    }

    /**
     * 处理省市区县的连接
     *
     * @param provinc 省
     * @param city    市
     * @param area    区县
     * @return 完整的地址
     */
    private String handleRegion(String provinc, String city, String area) {
        String point = "";
        switch (provinc) {
            case "北京":
            case "天津":
            case "上海":
            case "重庆": {
                if(StringUtils.equals(city, area)) {
                    point = city;
                } else {
                    point = city + StringUtils.defaultString(area);
                }
                break;
            }
            default: {
                if(StringUtils.equals(city, area)) {
                    point = provinc + city;
                } else {
                    point = provinc + city + StringUtils.defaultString(area);
                }
                break;
            }
        }
        return point;
    }

    @Override
    public List<RegionPointBean> getStartPointList(Long userId, int limit) {
        String sql = "SELECT DISTINCT start_point startPoint, start_provinc startProvinc, start_city startCity, start_area startArea FROM `tyt_transport_mt` " +
                " where status = 1 and user_id = ? order by ctime desc limit ?";
        if(limit > 30) {
            limit = 30;
        }
        // 查询数据集
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("startProvinc", Hibernate.STRING);
        scalarMap.put("startCity", Hibernate.STRING);
        scalarMap.put("startArea", Hibernate.STRING);

        final Object[] params = {
                userId,
                limit
        };
        List<RegionPointBean> queryList = this.getBaseDao().search(sql, scalarMap, RegionPointBean.class, params);
        return queryList;
    }

    /**
     * 解析sql问号个数
     *
     * @param objects
     * @return
     */
    private String parseQuestionmark(Object[] objects) {
        String ques = "";
        for (int i = 0; i < objects.length; i++) {
            if (i == 0) {
                ques = "?";
            } else {
                ques += ",?";
            }
        }
        return ques;
    }
}
