package com.tyt.schedule.bean;

import java.io.Serializable;

/**
 * Created by duanwc on 2019/5/30.
 */
public class ScheduleUserBean implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户密码
     */
    private String password;
    /**
     * 用户手机号
     */
    private String cellPhone;
    /**
     * 用户真实姓名
     */
    private String trueName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getTrueName() {
        return trueName;
    }

    public void setTrueName(String trueName) {
        this.trueName = trueName;
    }

    @Override
    public String toString() {
        return "ScheduleUserBean{" +
                "id=" + id +
                ", password='" + password + '\'' +
                ", cellPhone='" + cellPhone + '\'' +
                ", trueName='" + trueName + '\'' +
                '}';
    }
}
