package com.tyt.schedule.bean;

import com.tyt.model.TransportMt;

import java.io.Serializable;
import java.util.List;

/**
 * Created by duanwc on 2019/5/30.
 */
public class ScheduleTransportBean implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 人工派单货源列表
     */
    private List<TransportMt> transportList;

    /**
     * 最后记录ID
     */
    private Long lastId;

    public List<TransportMt> getTransportList() {
        return transportList;
    }

    public void setTransportList(List<TransportMt> transportList) {
        this.transportList = transportList;
    }

    public Long getLastId() {
        return lastId;
    }

    public void setLastId(Long lastId) {
        this.lastId = lastId;
    }

    @Override
    public String toString() {
        return "ScheduleMtBean{" +
                "transportList=" + transportList +
                ", lastId=" + lastId +
                '}';
    }
}
