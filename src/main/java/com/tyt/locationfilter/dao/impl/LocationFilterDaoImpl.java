package com.tyt.locationfilter.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.locationfilter.dao.LocationFilterDao;
import com.tyt.model.TytLocationFilter;

@Repository("locationFilterDao")
public class LocationFilterDaoImpl extends BaseDaoImpl<TytLocationFilter, Long> implements LocationFilterDao {

	public LocationFilterDaoImpl() {
		this.setEntityClass(TytLocationFilter.class);
	}
}
