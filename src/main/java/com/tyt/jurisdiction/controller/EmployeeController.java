package com.tyt.jurisdiction.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.jurisdiction.bean.EmployeeBean;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.jurisdiction.bean.RoleQueryBean;
import com.tyt.jurisdiction.service.EmployeeService;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytEmployee;
import com.tyt.util.Constant;
import com.tyt.web.base.BaseController;

/**
 * Created by douge on 16-7-12.
 */

@Controller
@RequestMapping("/jurisdiction/employee")
public class EmployeeController extends BaseController {
	@Resource(name = "EmployeeService")
	EmployeeService employeeService;

	@RequestMapping("/list")
	public String getList(EmployeeBean bean, Integer pageNo, Integer pageSize, HttpServletRequest request,
						  HttpServletResponse response) {
		long t1=System.currentTimeMillis();
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
//				return "<script>top.location.href=\"/back/jsp/admin_login\"</script>";
				return "back/jsp/admin_login";
			}
			//分页设置
			if (pageNo == null || pageNo.intValue() <= 0)
				pageNo = 1;
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(pageNo);
			if (pageSize == null || pageSize.intValue() <= 0)
				pageSize = Constant.DEFAULT_PAGE_SIZE;
			pageBean.setPageSize(pageSize);
			List<EmployeeQueryBean> employeeList = employeeService.queryEmployee(bean, pageBean);
			request.setAttribute("employeeList", employeeList);

			request.setAttribute("employee", bean);
			request.setAttribute("pageNo", pageNo);
			request.setAttribute("pageSize", pageSize);
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("maxPage", pageBean.getMaxPage());
			return "back/jurisdiction/jsp/employee";
			
		} catch (Exception e) {
			e.printStackTrace();
			return "back/jurisdiction/jsp/employee";
		}finally{
			long t2=System.currentTimeMillis();
			logger.info("后台查询内部员工列表时间【{}ms】",t2-t1);
		}
		
	}

	@RequestMapping("/add")
	@ResponseBody
	public ResultMsgBean addEmployee(TytEmployee bean, HttpServletRequest request,
									 HttpServletResponse response) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				result.setCode(100);
				result.setMsg("请重新登陆!");
				response.sendRedirect(request.getContextPath() + "/back/jsp/admin_login.jsp");
				return result;
			}
			if (bean.getDataInstitutionType() == null || "".equals(bean.getDataInstitutionType().trim())) {
				bean.setDataInstitutionType("2");
			}
			employeeService.insertEmployee(bean);
			//获取结果
			result.setCode(200);
			result.setMsg("添加成功,将关闭当前页面!");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(400);
			result.setMsg("添加失败,是否关闭当前页面？");
			return result;
		}

	}

	@RequestMapping("/getById")
	public String getById(Long employeeId, HttpServletRequest request, HttpServletResponse response) {
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}

			if (employeeId.longValue() <= 0l) {
				request.setAttribute("errorMsg", "数据错误！");
				return "back/bcar/jsp/employee_edit";
			}
			TytEmployee employee = employeeService.getEmployeeById(employeeId);
			request.setAttribute("employee", employee);
			return "back/jurisdiction/jsp/employee_edit";
		} catch (Exception e) {
			e.printStackTrace();
			request.setAttribute("errorMsg", e);
			return "back/jurisdiction/jsp/employee_edit";
		}
	}

	@RequestMapping(value = "/getEmployeeById")
	@ResponseBody
	public ResultMsgBean getUser(Long employeeId){
		ResultMsgBean resultMsgBean=new ResultMsgBean();
		try {
			if(employeeId==null||employeeId.longValue()<=0l){
				resultMsgBean.setCode(600);
				resultMsgBean.setMsg(employeeId+"用户不存在");
				return resultMsgBean;
			}
			TytEmployee employee=employeeService.getEmployeeById(employeeId);
			resultMsgBean.setCode(200);
			resultMsgBean.setMsg("查询成功");
			resultMsgBean.setData(employee);
			return resultMsgBean;
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean.setCode(500);
			resultMsgBean.setMsg(e.getMessage());
			return resultMsgBean;
		}
	}

	@RequestMapping("/update")
	@ResponseBody
	public ResultMsgBean updateEmployee(TytEmployee employeeBean, HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				result.setCode(100);
				result.setMsg("请重新登陆!");
				response.sendRedirect(request.getContextPath() + "/back/jsp/admin_login.jsp");
				return result;
			}

			if (employeeBean.getId() == null || Long.valueOf(employeeBean.getId()) <= 0l) {
				result.setCode(300);
				result.setMsg("非法信息编辑!");
				return result;
			}

			// 老数据
			TytEmployee oldEmployee = employeeService.getEmployeeById(employeeBean.getId());
			if (oldEmployee == null) {
				result.setCode(300);
				result.setMsg("非法信息编辑!");
				return result;
			}

			employeeService.updateEmployee(oldEmployee, employeeBean, curUser.getId());
			result.setCode(200);
			result.setMsg("修改成功,将关闭当前页面!");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg(e.toString());
			return result;
		}

	}

	@RequestMapping("/deleteById")
	@ResponseBody
	public ResultMsgBean deleteById(String employeeId, HttpServletRequest request, HttpServletResponse response) {

		ResultMsgBean result = new ResultMsgBean();
		try {

			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				result.setCode(1003);
				result.setMsg("请重新登陆!");
				response.sendRedirect(request.getContextPath() + "/back/jsp/admin_login.jsp");
				return result;
			}
			String[] employeeIdArr = employeeId.split(",");
			/*employeeService.deleteById(employeeIdArr,curUser.getId());
			//查看是否存在下级员工
			Long count=employeeService.getSystemNameCounts(employeeIdArr);
			if(count.longValue()>0){
				result.setCode(600);
				result.setMsg("员工存在下级员工，请转移后再删除!");
				return result;
			}
			for (String id : employeeIdArr) {
				employeeService.deleteById(Long.valueOf(id), 2, curUser.getId());
			}*/
			employeeService.deleteById(employeeIdArr, curUser.getId());
			result.setCode(200);
			result.setMsg("删除成功.");
			return result;
		} catch (Exception e) {
			if(!e.getMessage().contains("下级员工")){//这个异常时自定义的
				e.printStackTrace();
			}
			result.setCode(500);
			result.setMsg(e.getMessage());
			return result;
		}
	}

	@RequestMapping("/getRolesById")
	public String getRolesById(String employeeId, HttpServletRequest request, HttpServletResponse response) {
		try {

			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}

			if (employeeId.toString().indexOf(",") == -1) {
				List<RoleQueryBean> roles = employeeService.getRolesById(Long.valueOf(employeeId));
				request.setAttribute("roles", roles);
				List<RoleQueryBean> otherRoles = employeeService.getOtherRolesById(Long.valueOf(employeeId));
				request.setAttribute("otherRoles", otherRoles);
				// 获取当前员工的信息
				TytEmployee employee = employeeService.getById(Long.valueOf(employeeId));
				request.setAttribute("employee", employee);
			} else {
				employeeId = employeeId.substring(0, employeeId.lastIndexOf(","));
				List<RoleQueryBean> otherRoles = employeeService.getOtherRolesById(-1L);
				request.setAttribute("otherRoles", otherRoles);
				request.setAttribute("roles", null);
			}

			request.setAttribute("employeeId", employeeId);

			return "back/jurisdiction/jsp/set_roles";
		} catch (Exception e) {
			e.printStackTrace();
			return "back/jurisdiction/jsp/set_roles";
		}
	}

	@RequestMapping("/setRolesById")
	@ResponseBody
	public ResultMsgBean setRolesById(String employeeId, String roleIds, String dataInstitutionVal, HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				result.setCode(100);
				result.setMsg("请重新登陆!");
				response.sendRedirect(request.getContextPath() + "/back/jsp/admin_login.jsp");
				return result;
			}
			if (employeeId == null || "" == employeeId.replaceAll(",", "").trim()) {
				result.setCode(400);
				result.setMsg("操作失败！");
				return result;
			}
			int resultCode = employeeService.saveEmployeeRoleCorrelation(employeeId, roleIds, dataInstitutionVal);
			if (resultCode == 200) {
				result.setCode(200);
				result.setMsg("设置成功,将关闭当前页面!");
			} else if (resultCode == 400) {
				result.setCode(400);
				result.setMsg("该条信息已经存在,是否关闭当前页面？");
			} else if (resultCode == 600) {
				result.setCode(600);
				result.setMsg("该条信息没做任何改变,是否关闭当前页面？");
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg(e.toString());
			return result;
		}
	}
	
	@RequestMapping("/toUpdatePassword")
	public String toUpdatePassword(String employeeId, HttpServletRequest request, HttpServletResponse response) {
		try {

			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			request.setAttribute("userId", curUser.getId());
			return "back/jsp/updatePassword";
		}catch(Exception e){
			e.printStackTrace();
			request.setAttribute("msg", e.toString());
			return "back/jsp/admin_login";
		}
		}

	@RequestMapping("/updateEmployeePassword")
	@ResponseBody
	public int updateEmployeePassword(String id, String newPassword,HttpServletRequest request) {
		EmployeeQueryBean curUser = getCurrentUser(request);
		if (StringUtils.isBlank(newPassword)){
		    return 501;
        }
		return employeeService.updateEmployeePassword(id, newPassword,curUser.getUserName());
	}

	@RequestMapping("/ajx_queryEmployeeByLoginPhoneNo")
	@ResponseBody
	public List<EmployeeQueryBean> queryEmployeeByLoginPhoneNo(String phoneNo) {
		return employeeService.queryEmployeeByLoginPhoneNo(phoneNo);
	}

	@RequestMapping("/ajx_queryEmployeeByName")
	@ResponseBody
	public List<EmployeeQueryBean> queryEmployeeByName(String name) {
		try {
			return employeeService.queryEmployeeByName(name);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}
	}

	@RequestMapping("/ajx_validateEmployeePassword")
	@ResponseBody
	public int validateEmployeePassword(String id, String password) {
		if (employeeService.validateEmployeePassword(id, password))
			return 200;
		else
			return 500;
	}

}
