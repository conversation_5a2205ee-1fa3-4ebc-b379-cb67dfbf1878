package com.tyt.jurisdiction.service.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.jurisdiction.bean.InstitutionBean;
import com.tyt.jurisdiction.bean.InstitutionQueryBean;
import com.tyt.jurisdiction.service.DepartmentService;
import com.tyt.jurisdiction.service.InstitutionService;
import com.tyt.jurisdiction.util.JurisdictionUtil;
import com.tyt.model.PageBean;
import com.tyt.model.TytInstitution;
import com.tyt.service.base.BaseServiceImpl;

/**
 * Created by douge on 16-7-12.
 */

@Service("InstitutionService")
public class InstitutionServiceImpl extends BaseServiceImpl<TytInstitution, Long> implements InstitutionService {

	@Resource(name = "InstitutionDao")
	public void setBaseDao(BaseDao<TytInstitution, Long> institutionDao) {
		super.setBaseDao(institutionDao);
	}

	@Resource(name = "DepartmentService")
	DepartmentService departmentService;

	@Override
	public List<InstitutionQueryBean> queryInstitution(InstitutionBean bean, PageBean pageBean) {
		BigInteger count = null;// 定义出查询Count

		StringBuffer countSQL = new StringBuffer("select count(*) from tyt_institution where 1=1");

		List<Object> list = new ArrayList<Object>();
		StringBuffer conditionSQL = new StringBuffer();
		boolean hasCondition = false;

		conditionSQL.append(" and is_valid=? ");
		list.add(1);

		if (null != bean.getId() && !"".equals(bean.getId().trim())) {
			conditionSQL.append(" and id=? ");
			list.add(bean.getId().trim());
			hasCondition = true;
		}

		if (null != bean.getInstitutionName() && !"".equals(bean.getInstitutionName().trim())) {
			conditionSQL.append(" and institution_name=?");
			list.add(bean.getInstitutionName().trim());
			hasCondition = true;
		}

		if (null != bean.getInstitutionNo() && !"".equals(bean.getInstitutionNo().trim())) {
			conditionSQL.append(" and institution_no=?");
			list.add(bean.getInstitutionNo().trim());
			hasCondition = true;
		}
		// 新找出满足条件的pid
		Long pid = 0l;
		if (hasCondition) {
			StringBuffer selectOneSQL = new StringBuffer("select * from tyt_institution where 1=1");
			List<TytInstitution> oneList = this.getBaseDao().search(selectOneSQL.append(conditionSQL).toString(), list.toArray(), 1, 1);
			if (oneList != null && oneList.size() > 0) {
				pid = oneList.get(0).getPid();
			}
		}
		conditionSQL.append(" AND pid=?");
		list.add(pid);

		count = this.getBaseDao().query(countSQL.append(conditionSQL).toString(), list.toArray());

		if (count.longValue() > 0l) {
			pageBean.setRowCount(count.longValue());
			StringBuffer selectSQL = new StringBuffer("SELECT i.id, (SELECT COUNT(*) FROM tyt_institution ti WHERE ti.pid = i.id AND ti.is_valid = 1) childrenNum, i.institution_name institutionName, ").append("i.institution_no institutionNo,(SELECT COUNT(*) FROM tyt_internal_employee ie WHERE ie.is_valid!=2 AND ie.department_id IN (SELECT d.id FROM tyt_department d WHERE d.institution_id = i.id)) employeeNum ").append("FROM tyt_institution i WHERE 1=1");
			Map<String, Type> scalarMap = new HashMap<String, Type>();
			scalarMap.put("id", Hibernate.LONG);
			scalarMap.put("childrenNum", Hibernate.INTEGER);
			scalarMap.put("institutionName", Hibernate.STRING);
			scalarMap.put("institutionNo", Hibernate.STRING);
			scalarMap.put("employeeNum", Hibernate.INTEGER);
			selectSQL.append(conditionSQL);

			List<InstitutionQueryBean> queryList = this.getBaseDao().search(selectSQL.toString(), scalarMap, InstitutionQueryBean.class, list.toArray(), pageBean.getCurrentPage(), pageBean.getPageSize());
			return queryList;
		}

		return null;
	}

	@Override
	public List<InstitutionQueryBean> queryALLInstitution() {
		String sql = "SELECT id, institution_name FROM tyt_institution WHERE is_valid=1 ";
		List<InstitutionQueryBean> queryList = this.getBaseDao().findAll(sql, null);
		return queryList;

	}

	@Override
	public List<Object[]> queryChildInstitutionsByPid(String pid) {
		String sql = "SELECT i.id, (SELECT COUNT(*) FROM tyt_institution ti WHERE ti.pid = i.id AND ti.is_valid = 1) childrenNum, " + "i.institution_name institutionName, i.institution_no institutionNo, " + "(SELECT COUNT(*) FROM tyt_internal_employee ie WHERE ie.is_valid !=2 and ie.department_id IN " + "(SELECT d.id FROM tyt_department d WHERE d.institution_id = i.id)) employeeNum, i.pid " + "FROM tyt_institution i WHERE i.is_valid=1 AND i.pid=" + pid;

		List<Object[]> queryList = this.getBaseDao().findAll(sql, null);
		return queryList;
	}

	@Override
	public List queryInstitutionByInstitutionName(String institutionName) {
		String sql = "SELECT id FROM tyt_institution WHERE institution_name='" + institutionName + "' and is_valid=1";
		return this.getBaseDao().findAll(sql, null);
	}

	@Override
	public List ajx_queryInstitutionByInstitutionNo(String institutionNo) {
		String sql = "SELECT id FROM tyt_institution WHERE institution_no='" + institutionNo + "' AND is_valid=1";
		return this.getBaseDao().findAll(sql, null);
	}

	@Override
	public InstitutionQueryBean getInstitutionById(Long institutionId) {
		String sql = "SELECT i.id, i.institution_name institutionName, i.institution_no institutionNo, i.pid FROM tyt_institution i WHERE i.id=" + institutionId;

		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("institutionName", Hibernate.STRING);
		scalarMap.put("institutionNo", Hibernate.STRING);
		scalarMap.put("pid", Hibernate.LONG);

		List<InstitutionQueryBean> queryList = this.getBaseDao().search(sql, scalarMap, InstitutionQueryBean.class, null, 0, 1);

		return queryList.get(0);

	}

	@Override
	public int updateInstitution(InstitutionQueryBean institutionOld, InstitutionBean bean, Long userId) {
		boolean b = JurisdictionUtil.equalsInstitution(institutionOld, bean);

		if (b) {
			String s = bean.getId();
			long l = Long.valueOf(s);

			TytInstitution institution = this.getById(l);
			institution.setInstitutionName(bean.getInstitutionName());
			institution.setInstitutionNo(bean.getInstitutionNo());
			institution.setPid(Long.valueOf(bean.getPid()));
			institution.setUpdateTime(new Date());
			institution.setLastUpdater(userId);

			this.update(institution);
			return 200;
		} else {
			return 600;
		}
	}

	@Override
	public int delInstitutionById(Long institutionId) {
		if (departmentService.queryDepartmentByInstitutionId(institutionId).size() > 0) {
			return 600;
		}
		if (queryChildInstitutionsByPid(institutionId.toString()).size() > 0) {
			return 500;
		}
		try {
			String sql = "UPDATE tyt_institution d SET d.is_valid = 0 WHERE id=" + institutionId;
			getBaseDao().executeUpdateSql(sql.toString(), new Object[0]);

		} catch (Exception e) {
			e.printStackTrace();
			return 400;
		}
		return 200;
	}

	@Override
	public List<InstitutionQueryBean> queryOtherInstitution(Long institutionId) {
		String sql = "SELECT id, institution_name FROM tyt_institution WHERE is_valid=1 and id!=" + institutionId;
		List<InstitutionQueryBean> queryList = this.getBaseDao().findAll(sql, null);
		return queryList;
	}

	@Override
	public List<InstitutionQueryBean> queryOtherInstitutionWithNoChildren(Long institutionId) {
		String sql = "SELECT id, institution_name FROM tyt_institution WHERE is_valid=1 and id!= ? AND pid != ?";
		List<InstitutionQueryBean> queryList = this.getBaseDao().findAll(sql, new Object[] { institutionId, institutionId });
		return queryList;
	}
}
