package com.tyt.acvitity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.acvitity.bean.DTO.StimulateActivityDTO;
import com.tyt.acvitity.bean.DTO.StimulateOrderDTO;
import com.tyt.acvitity.bean.MqRiskMsg;
import com.tyt.acvitity.bean.UserInviteAwardInfoBean;
import com.tyt.acvitity.bean.VO.StimulateActivityVO;
import com.tyt.acvitity.bean.VO.StimulateOrderVO;
import com.tyt.acvitity.dao.StimulateActivityDao;
import com.tyt.acvitity.dao.TytConventionActivityDao;
import com.tyt.acvitity.service.PlatUserInviteAwardInfoService;
import com.tyt.acvitity.service.StimulateActivityService;
import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.authCommon.bean.MqPushMessageBean;
import com.tyt.authCommon.service.UserPermissionMQService;
import com.tyt.cargo.dataqueue.client.topic.ts.UserBusinessTopic;
import com.tyt.common.bean.ShortMsgBean;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.dao.base.BaseDao;
import com.tyt.dao.user.UserDao;
import com.tyt.file.bean.FileInfo;
import com.tyt.file.service.FileInfoService;
import com.tyt.goods.dao.UserBuyGoodsDao;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.jurisdiction.service.EmployeeService;
import com.tyt.manager.commons.constant.TransactionConstant;
import com.tyt.manager.service.producer.BusinessMqProducerService;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.*;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.common.common.HttpClientFactory;
import com.tyt.service.config.TytConfigService;
import com.tyt.userFlag.service.ActivityUserFlagService;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("platUserInviteAwardInfoService")
public class PlatUserInviteAwardInfoServiceImpl extends BaseServiceImpl<PlatUserInviteAwardInfo, Long> implements PlatUserInviteAwardInfoService {




	@Override
	@Resource(name = "platUserInviteAwardInfoDao")
	public void setBaseDao(BaseDao<PlatUserInviteAwardInfo, Long> platUserInviteAwardInfoDao) {
		super.setBaseDao(platUserInviteAwardInfoDao);
	}

	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;
	@Resource
	private UserBuyGoodsDao userBuyGoodsDao;
	@Resource
	private StimulateActivityDao stimulateActivityDao;
	@Resource
	private TytConventionActivityDao tytConventionActivityDao;
	@Resource
	private UserBuyGoodsService userBuyGoodsService;
	@Resource
	private UserDao userDao;
	@Resource
	private UserPermissionMQService userPermissionMQService;
	@Resource
	private UserPermissionService userPermissionService;
	@Resource(name = "EmployeeService")
	private EmployeeService employeeService;
	@Resource
	private PlatUserInviteAwardInfoService platUserInviteAwardInfoService;
	@Resource(name = "transportOrdersService")
	TransportOrdersService transportOrdersService;
	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;
	@Autowired
	private FileInfoService fileInfoService;
	@Autowired
	private ActivityUserFlagService activityUserFlagService;
	@Autowired
	private StimulateActivityService stimulateActivityService;
	@Autowired
	private TransportOrdersRiskService transportOrdersRiskService;
	@Autowired
	private MarketingActivityService marketingActivityService;

	@Autowired
	private TytConfigService tytConfigService;

	@Autowired
	private TytMqMessageService mqMessageService;

	@Autowired
	private RestTemplate restTemplate;



	private static CloseableHttpClient httpClient = getHttpclientInstance();

	private static CloseableHttpClient getHttpclientInstance() {
		if (httpClient == null) {
			httpClient = HttpClientFactory.getHttpClientWithRetry();
		}
		return httpClient;
	}

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	public List<UserInviteAwardInfoBean> getList(UserInviteAwardInfoBean queryBean, PageBean pageBean) {
		if(StringUtils.isBlank(queryBean.getCellPhone()) && StringUtils.isBlank(queryBean.getInviteCellPhone())) {
			return null;
		}
		Map<String,Object> paramMap = new HashMap<String,Object>();
		StringBuffer conditionSQL=new StringBuffer();
		//推荐人手机号
		if(StringUtils.isNotBlank(queryBean.getCellPhone())){
			conditionSQL.append(" AND u.cell_phone=:cellPhone");
			paramMap.put("cellPhone", queryBean.getCellPhone());
		}
		//被推荐人手机号
		if(StringUtils.isNotBlank(queryBean.getInviteCellPhone())){
			conditionSQL.append(" AND t.invite_cell_phone=:inviteCellPhone");
			paramMap.put("inviteCellPhone", queryBean.getInviteCellPhone());
		}
		StringBuffer countSQL = new StringBuffer("SELECT COUNT(*) FROM plat_user_invite_award_info t ,tyt_user u WHERE t.user_id=u.id ");
		countSQL.append(conditionSQL);
		BigInteger rowCount = this.getBaseDao().queryByMap(countSQL.toString(), paramMap);
		if(rowCount==null||rowCount.longValue()<=0L){
	 		return null;
	 	}
		pageBean.setRowCount(rowCount.longValue());
		StringBuffer querySQL = new StringBuffer("SELECT t.id id,u.cell_phone cellPhone,t.award_days awardDays,"
				+ "t.invite_cell_phone inviteCellPhone,t.user_state userState "
				+ "FROM plat_user_invite_award_info t ,tyt_user u WHERE t.user_id=u.id ");
		querySQL.append(conditionSQL);
		querySQL.append(" ORDER BY t.ctime");
		Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
		 map.put("id", Hibernate.LONG);
		 map.put("cellPhone", Hibernate.STRING);
		 map.put("awardDays", Hibernate.INTEGER);
		 map.put("inviteCellPhone", Hibernate.STRING);
		 map.put("userState", Hibernate.INTEGER);
		 return this.getBaseDao().searchByName(querySQL.toString(), map, UserInviteAwardInfoBean.class, paramMap, pageBean.getCurrentPage(), pageBean.getPageSize());
	}

	@Override
	public UserInviteAwardInfoBean getInviteInfo(String cellPhone) {
		String sql="SELECT t.id id,u.cell_phone cellPhone,t.award_days awardDays,t.invite_cell_phone inviteCellPhone,t.user_state userState FROM `plat_user_invite_award_info` t ,tyt_user u WHERE t.`user_id`=u.id AND t.invite_cell_phone=:inviteCellPhone ";
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("inviteCellPhone", cellPhone);
		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
		mapType.put("id", Hibernate.LONG);
		mapType.put("cellPhone", Hibernate.STRING);
		mapType.put("awardDays", Hibernate.INTEGER);
		mapType.put("inviteCellPhone", Hibernate.STRING);
		mapType.put("userState", Hibernate.INTEGER);
		List<UserInviteAwardInfoBean> list = this.getBaseDao().search(sql, mapType, UserInviteAwardInfoBean.class, map);
		if(list!=null && list.size()>0) {
			return list.get(0);
		}
		return null;
	}

    @Override
    public ResultMsgBean stimulateActivityList(StimulateActivityDTO stimulateActivityDTO) {
		StringBuffer SQL = new StringBuffer("select id as id, concat(activity_name, '（', marketing_activity_id, '）') as activityName, " +
				" activity_type activityType,user_id as userId, user_account as userAccount, order_number as orderNumber, ts_order_no as tsOrderNo, " +
				" order_status as orderStatus, activity_status as activityStatus, if(activity_status=2 or activity_status = 1, suspect_type, null) suspectType, " +
				" case when activity_status = 2 or activity_status = 5 then concat('已撤回', repeal_days,'天权益')" +
				" else concat('已赠送（', donate_days, '天）') end benefitGift , marketing_activity_id as activityId" +
				" from tyt_stimulate_activity where 1 = 1 ");
		StringBuffer COUNT_SQL = new StringBuffer("select count(*) from tyt_stimulate_activity where 1 = 1 ");
		HashMap<String, Object> params = new HashMap<>();
		if(StringUtils.isNotBlank(stimulateActivityDTO.getActivityName())){
			SQL.append(" and activity_name = :activityName ");
			COUNT_SQL.append(" and activity_name = :activityName ");
			params.put("activityName", stimulateActivityDTO.getActivityName());
		}
		if(null != stimulateActivityDTO.getUserId()){
			SQL.append(" and user_id = :userId ");
			COUNT_SQL.append(" and user_id = :userId ");
			params.put("userId", stimulateActivityDTO.getUserId());
		}
		if(StringUtils.isNotBlank(stimulateActivityDTO.getUserAccount())){
			SQL.append(" and user_account = :userAccount ");
			COUNT_SQL.append(" and user_account = :userAccount ");
			params.put("userAccount", stimulateActivityDTO.getUserAccount());
		}
		if(null != stimulateActivityDTO.getActivityStatus()){
			SQL.append(" and activity_status = :activityStatus ");
			COUNT_SQL.append(" and activity_status = :activityStatus ");
			params.put("activityStatus", stimulateActivityDTO.getActivityStatus());
		}
		if(null != stimulateActivityDTO.getOrderStatus()){
			SQL.append(" and order_status = :orderStatus ");
			COUNT_SQL.append(" and order_status = :orderStatus ");
			params.put("orderStatus", stimulateActivityDTO.getOrderStatus());
		}
		SQL.append("order by create_time desc");
		HashMap<String, Type> fieldTypeMap = new HashMap<>();
		fieldTypeMap.put("id", Hibernate.INTEGER);
		fieldTypeMap.put("activityName", Hibernate.STRING);
		fieldTypeMap.put("activityType", Hibernate.INTEGER);
		fieldTypeMap.put("userId", Hibernate.LONG);
		fieldTypeMap.put("userAccount", Hibernate.STRING);
		fieldTypeMap.put("orderNumber", Hibernate.LONG);
		fieldTypeMap.put("tsOrderNo", Hibernate.STRING);
		fieldTypeMap.put("orderStatus", Hibernate.INTEGER);
		fieldTypeMap.put("activityStatus", Hibernate.INTEGER);
		fieldTypeMap.put("benefitGift", Hibernate.STRING);
		fieldTypeMap.put("suspectType", Hibernate.STRING);
		fieldTypeMap.put("activityId", Hibernate.LONG);
		BigInteger rowCount = this.getBaseDao().queryByMap(COUNT_SQL.toString(), params);
		List<StimulateActivityVO> stimulateActivityVOS = new ArrayList<>();
		if(rowCount.intValue() > 0){
			stimulateActivityVOS = this.getBaseDao().searchByName(SQL.toString(), fieldTypeMap, StimulateActivityVO.class, params, stimulateActivityDTO.getPageNumber(), stimulateActivityDTO.getPageSize());
			// 获取当前用户刷单次数
			stimulateActivityVOS.stream().forEach(e->{
				e.setBrushesCount(brushesCount(e.getUserId(), e.getActivityId().intValue()));
			});
		}
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		HashMap<String, Object> resultMap = new HashMap<>();
		resultMap.put("totalSize", rowCount.intValue());
		resultMap.put("data", stimulateActivityVOS);
		resultMsgBean.setData(resultMap);
		return resultMsgBean;
    }

    @Override
    public ResultMsgBean updateBrushing(Long id, String reason, Integer type, Long operatorId) throws ParseException {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		// 获取操作人姓名
		TytEmployee employee = employeeService.getEmployeeById(operatorId);
		if(null == employee){
			throw new RuntimeException("获取操作用户信息异常");
		}
		if(3 == type){
			StringBuffer sql = new StringBuffer("update tyt_stimulate_activity set activity_status=?, update_time=now(), remark=?, update_user_name=? where id=? and activity_status = 1");
			int count = stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{type, reason,employee.getRealName(), id});
			return resultMsgBean;
		}
		// 如果是确认刷单的话需要撤回权益并且计算撤回的天数
		List<TytUserBuyGoods> tytUserBuyGoodsList  = new ArrayList<>();
		// 获取用户参与记录
		List<TytStimulateActivityEntity> tytStimulateActivityEntities = stimulateActivityDao.find("from TytStimulateActivityEntity where id = ?", id.intValue());
		if(null == tytStimulateActivityEntities || tytStimulateActivityEntities.isEmpty()){
			throw new RuntimeException("获取用户参与记录失败");
		}
		TytStimulateActivityEntity stimulateActivityEntity = tytStimulateActivityEntities.get(0);
		Long userId = stimulateActivityEntity.getUserId();
		// 获取用户购买商品记录对象
//		TytUserBuyGoods tytUserBuyGoods = new TytUserBuyGoods();
//		tytUserBuyGoods.setOrderId(stimulateActivityEntity.getGoodsOrderId());
//		tytUserBuyGoodsList = userBuyGoodsService.findList(tytUserBuyGoods);
		tytUserBuyGoodsList = userBuyGoodsService.queryByOrderId(stimulateActivityEntity.getGoodsOrderId(),userId);
		if(null == tytUserBuyGoodsList || tytUserBuyGoodsList.isEmpty()){
			throw new RuntimeException("获取用户赠送商品记录异常");
		}
		// 获取用户对象
		User user = userDao.findById(userId);
		if(null == user){
			throw new RuntimeException("获取用户信息异常");
		}
		// 获取用户车方权益，计算具体能够扣除多少天的权益
		int remainingDays = 0;
		List<UserPermission> permissionByUserIds = userPermissionService.getAllPermissionByUserIds(100101, Arrays.asList(user.getId()));
		if(null != permissionByUserIds && !permissionByUserIds.isEmpty()){
			// 计算剩余的会员天数
			remainingDays = TimeUtil.daysBetween(new Date(), permissionByUserIds.get(0).getEndTime());
		}
		// 计算实际扣除的天数
		int realRepealDays = countRepealDays(remainingDays, stimulateActivityEntity.getDonateDays());
		// 修改用户参与记录表，刷单结果和扣除天数
		StringBuffer sql = new StringBuffer("update tyt_stimulate_activity set activity_status=?, update_time=now(), remark=?, repeal_days = ?, update_user_name=? where id=? and activity_status = 1");
		int count = stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{type, reason, realRepealDays, employee.getRealName(), id});
		Integer updateResult = platUserInviteAwardInfoService.updateUserBuyGoods(user, tytUserBuyGoodsList.get(0));


		// 如果当前用户刷单超过两次需要将发放的权益全部扣除并屏蔽当前用户参与该活动
		if(platUserInviteAwardInfoService.brushesCount(userId, stimulateActivityEntity.getMarketingActivityId()) >= 2){
			// 获取当前用户所有未被扣除权益的参与记录
			List<TytStimulateActivityEntity> entities = stimulateActivityDao.find("from TytStimulateActivityEntity where userId = ? and activityStatus in (0,1,3) and id != ?", userId, id.intValue());
			if(null == entities || entities.isEmpty()){
				return resultMsgBean;
			}
			List<String> goodsIdList = entities.stream().map(e -> {
				return e.getGoodsOrderId();
			}).collect(Collectors.toList());
			tytUserBuyGoodsList.addAll(userBuyGoodsDao.find("from TytUserBuyGoods where orderId in ('" + StringUtils.replace(StringUtils.join(goodsIdList, ","), ",", "','") + "')"));
			if(null == tytUserBuyGoodsList || tytUserBuyGoodsList.isEmpty()){
				throw new RuntimeException("获取用户购买商品记录异常");
			}
			// 遍历修改用户商品表
			tytUserBuyGoodsList.forEach(e ->{
				platUserInviteAwardInfoService.updateUserBuyGoods(user, e);
			});
			entities.forEach(e ->{
				StringBuffer SQL = new StringBuffer("update tyt_stimulate_activity set activity_status=5, update_time = now(), remark=?, repeal_days = ?, update_user_name=? where id=?");
				stimulateActivityDao.executeUpdateSql(SQL.toString(), new Object[]{"确认二次刷单清空已赠送权益", e.getDonateDays(), "系统", e.getId()});
			});
			// 将用户从当前活动中剔除
			String sql2ActivityUser = "UPDATE `marketing_activity_user` SET is_delete = 2, operater = '系统',mtime = NOW() where activity_id = ? and user_id = ?";
			final Object[] params = {stimulateActivityEntity.getMarketingActivityId(),userId};
			int i = this.getBaseDao().executeUpdateSql(sql2ActivityUser, params);
		}else{
			try {
				this.sendMessage(user, stimulateActivityEntity.getTsOrderNo());
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		tytUserBuyGoodsList.forEach(e->{
			// 用户权益和权益记录变更
			userPermissionMQService.changeUserPermission2MQ(userId, e.getGoodsId(), 5, e.getOrderId(), MqBaseMessageBean.USER_PERMISSION_MINUS_MESSAGE, e);

		});
		return resultMsgBean;
    }

	/**
	 * 发送站内信
	 * @param user 用户信息
	 * @throws Exception
	 */
	public void sendMessage(User user, String orderId) throws Exception {
		String seriaNum = SerialNumUtil.generateSeriaNum();
		MqPushMessageBean messageBean = new MqPushMessageBean();
		messageBean.setUserId(user.getId());
		messageBean.setCellPhone(user.getCellPhone());
		messageBean.setTrueName(user.getTrueName());
		messageBean.setTitle("确认刷单权益撤回提醒");
		messageBean.setSummary("刷单警告提醒，将扣除该单获赠权益！");
		messageBean.setContent("尊敬的用户，您好！经平台核验，您的信息费订单（订单号：" + orderId + "）存在违规行为，已扣除该单所赠权益。如若再次发现违规，将取消活动参与资格，并扣除所有获得权益");
		messageBean.setSendType(1L);
		messageBean.setOpenType("0");
		messageBean.setSendCode(2);
		messageBean.setMessageType(MqBaseMessageBean.PUSH_MQ_MESSAGE);
		messageBean.setMessageSerailNum(seriaNum);
		String jsonString = JSON.toJSONString(messageBean);
		tytMqMessageService.addMqMessage(seriaNum, jsonString, MqBaseMessageBean.PUSH_MQ_MESSAGE);
		tytMqMessageService.sendMqMessage(seriaNum, jsonString);
	}

	@Override
	public Integer updateUserBuyGoods(User user, TytUserBuyGoods userBuyGoods){
		// 修改用户商品表
		String sqlUserBuyGoods = "update tyt_user_buy_goods set orders_status=3,refund_user_id=:refundUserId,refund_user_name=:refundUserName,refund_time=:refundTime " +
				"where orders_status = 2 and id = :id";
		HashMap<String, Object> map = new HashMap<String, Object>();
		map.put("refundUserId",user.getId());
		map.put("refundUserName", user.getUserName());
		map.put("refundTime",new Date());
		map.put("id", userBuyGoods.getId());
		int updateResult = this.getBaseDao().executeUpdateSql(sqlUserBuyGoods,map);
		return updateResult;
	}

	@Override
	public HashMap<String, Object> stimulateOrderList(StimulateOrderDTO stimulateOrderDTO) {

		StringBuffer SQL = new StringBuffer("select say.id as id, concat(say.activity_name, '（', say.marketing_activity_id, '）') as activityName, " +
				" say.user_id as userId, say.user_account as userAccount, say.car_user_id as carUserId, say.car_user_account as carUserAccount, say.order_number as orderNumber, say.ts_order_no as tsOrderNo, " +
				" say.order_status as orderStatus, say.activity_status as activityStatus, say.update_time as updateTime, say.user_flag as userFlag, say.update_user_name as updateUserName, " +
				" say.marketing_activity_id as activityId, say.suspect_type as suspectType ,say.appeal_status as appealStatus,say.appeal_evidence as appealEvidence," +
				" tor.head_city as headCity ,tor.head_no as headNo,tor.tail_city as tailCity,tor.tail_no as tailNo ," +
				" tor.start_point as startPoint, tor.dest_point as destPoint, tor.task_content as taskContent ,lo.type as type" +
				" from tyt_stimulate_activity say JOIN tyt_transport_orders tor ON say.order_number = tor.id LEFT JOIN (SELECT l.stimulate_activity_id activityId, l.type, l.ctime FROM tyt_stimulate_car_location l LEFT JOIN tyt_stimulate_activity a ON l.stimulate_activity_id = a.id WHERE l.type = 1  GROUP BY l.stimulate_activity_id ) lo ON say.id = lo.activityId LEFT JOIN marketing_activity ma ON say.marketing_activity_id = ma.id " +
				" where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 AND ma.activity_type <> 8 ");
		StringBuffer COUNT_SQL = new StringBuffer("select count(*) from tyt_stimulate_activity say JOIN tyt_transport_orders tor ON say.order_number = tor.id LEFT JOIN (SELECT l.stimulate_activity_id activityId, l.type, l.ctime FROM tyt_stimulate_car_location l LEFT JOIN tyt_stimulate_activity a ON l.stimulate_activity_id = a.id WHERE l.type = 1  GROUP BY l.stimulate_activity_id) lo ON say.id = lo.activityId LEFT JOIN marketing_activity ma ON say.marketing_activity_id = ma.id where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 AND ma.activity_type <> 8 ");
		HashMap<String, Object> params = new HashMap<>();
		if(null != stimulateOrderDTO.getActivityId()){
			SQL.append(" and say.marketing_activity_id = :activityId ");
			COUNT_SQL.append(" and say.marketing_activity_id = :activityId ");
			params.put("activityId", stimulateOrderDTO.getActivityId());
		}
		if(null != stimulateOrderDTO.getUserId()){
			Long userId = stimulateOrderDTO.getUserId();
			SQL.append(" and say.user_id like '%"+userId+"%' ");
			COUNT_SQL.append(" and say.user_id like '%"+userId+"%' ");
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getUserFlag())){
			String userFlag = stimulateOrderDTO.getUserFlag();
			SQL.append(" and say.user_flag like '%"+userFlag+"%' ");
			COUNT_SQL.append(" and say.user_flag like '%"+userFlag+"%' ");
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getUserAccount())){
			SQL.append(" and say.user_account = :userAccount ");
			COUNT_SQL.append(" and say.user_account = :userAccount ");
			params.put("userAccount", stimulateOrderDTO.getUserAccount());
		}
		if(null != stimulateOrderDTO.getCarUserId()){
			Long carUserId = stimulateOrderDTO.getCarUserId();
			SQL.append(" and say.car_user_id like '%"+carUserId+"%' ");
			COUNT_SQL.append(" and say.car_user_id like '%"+carUserId+"%' ");
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getCarUserAccount())){
			SQL.append(" and say.car_user_account = :carUserAccount ");
			COUNT_SQL.append(" and say.car_user_account = :carUserAccount ");
			params.put("carUserAccount", stimulateOrderDTO.getCarUserAccount());
		}
		if(null != stimulateOrderDTO.getActivityStatus()){
			SQL.append(" and say.activity_status = :activityStatus ");
			COUNT_SQL.append(" and say.activity_status = :activityStatus ");
			params.put("activityStatus", stimulateOrderDTO.getActivityStatus());
		}
		if(null != stimulateOrderDTO.getOrderStatus()){
			SQL.append(" and say.order_status = :orderStatus ");
			COUNT_SQL.append(" and say.order_status = :orderStatus ");
			params.put("orderStatus", stimulateOrderDTO.getOrderStatus());
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getUpdateUserName())){
			SQL.append(" and say.update_user_name = :updateUserName ");
			COUNT_SQL.append(" and say.update_user_name = :updateUserName ");
			params.put("updateUserName", stimulateOrderDTO.getUpdateUserName());
		}
		if(null != stimulateOrderDTO.getStartTime()){
			SQL.append(" and say.update_time >=:startTime ");
			COUNT_SQL.append(" and say.update_time >=:startTime ");
			params.put("startTime", new Date(stimulateOrderDTO.getStartTime()));
		}
		if(null != stimulateOrderDTO.getEndTime()){
			SQL.append(" and say.update_time <=:endTime ");
			COUNT_SQL.append(" and say.update_time <=:endTime ");
			params.put("endTime", new Date(stimulateOrderDTO.getEndTime()));
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getTsOrderNo())){
			SQL.append(" and say.ts_order_no = :tsOrderNo ");
			COUNT_SQL.append(" and say.ts_order_no = :tsOrderNo ");
			params.put("tsOrderNo",stimulateOrderDTO.getTsOrderNo());
		}
		//用户申诉状态 0-未申诉、1-待审核、2-审核完成
		if(null != stimulateOrderDTO.getAppealStatus()){
			if (stimulateOrderDTO.getAppealStatus() == 0 || stimulateOrderDTO.getAppealStatus() == 1){
				SQL.append(" and say.appeal_status = :appealStatus ");
				COUNT_SQL.append(" and say.appeal_status = :appealStatus ");
				params.put("appealStatus", stimulateOrderDTO.getAppealStatus());
			}else {
				SQL.append(" and say.appeal_status in (2,3) ");
				COUNT_SQL.append(" and say.appeal_status in (2,3) ");
			}
		}
		//接单车牌状态 0-未填写、1-已填写
		if(null != stimulateOrderDTO.getPlateNumber()){
			if (stimulateOrderDTO.getPlateNumber() == 0){
				SQL.append(" and tor.head_city is null ");
				COUNT_SQL.append(" and tor.head_city is null ");
			}else {
				SQL.append(" and tor.head_city is not null ");
				COUNT_SQL.append(" and tor.head_city is not null ");
			}
		}
		//申诉证据：（0-未上传、1-已上传 2、待上传）
		if (null != stimulateOrderDTO.getAppealEvidence()){
			SQL.append(" and say.appeal_evidence = :appealEvidence ");
			COUNT_SQL.append(" and say.appeal_evidence = :appealEvidence ");
			params.put("appealEvidence", stimulateOrderDTO.getAppealEvidence());
		}
		//用户标签：（0-无标签、1-有标签）
		if(null != stimulateOrderDTO.getActivityUserFlag()){
			if (stimulateOrderDTO.getActivityUserFlag() == 0){
				SQL.append(" and say.user_flag is null ");
				COUNT_SQL.append(" and say.user_flag is null ");
			}else {
				SQL.append(" and say.user_flag is not null and say.user_flag !='' ");
				COUNT_SQL.append(" and say.user_flag is not null and say.user_flag !='' ");
			}
		}

		SQL.append("ORDER BY CASE say.appeal_status WHEN 1 THEN 1 END DESC,say.id DESC");
		HashMap<String, Type> fieldTypeMap = new HashMap<>();
		fieldTypeMap.put("id", Hibernate.INTEGER);
		fieldTypeMap.put("activityName", Hibernate.STRING);
		fieldTypeMap.put("userId", Hibernate.LONG);
		fieldTypeMap.put("userAccount", Hibernate.STRING);
		fieldTypeMap.put("carUserId", Hibernate.LONG);
		fieldTypeMap.put("carUserAccount", Hibernate.STRING);
		fieldTypeMap.put("orderNumber", Hibernate.LONG);
		fieldTypeMap.put("tsOrderNo", Hibernate.STRING);
		fieldTypeMap.put("orderStatus", Hibernate.INTEGER);
		fieldTypeMap.put("activityStatus", Hibernate.INTEGER);
		fieldTypeMap.put("updateTime", Hibernate.TIMESTAMP);
		fieldTypeMap.put("userFlag", Hibernate.STRING);
		fieldTypeMap.put("updateUserName", Hibernate.STRING);
		fieldTypeMap.put("suspectType", Hibernate.STRING);
		fieldTypeMap.put("activityId", Hibernate.LONG);
		fieldTypeMap.put("headCity",Hibernate.STRING);
		fieldTypeMap.put("headNo",Hibernate.STRING);
		fieldTypeMap.put("tailCity",Hibernate.STRING);
		fieldTypeMap.put("tailNo",Hibernate.STRING);
		fieldTypeMap.put("startPoint",Hibernate.STRING);
		fieldTypeMap.put("destPoint",Hibernate.STRING);
		fieldTypeMap.put("taskContent",Hibernate.STRING);
		fieldTypeMap.put("type",Hibernate.INTEGER);
		fieldTypeMap.put("appealStatus",Hibernate.INTEGER);
		fieldTypeMap.put("appealEvidence",Hibernate.INTEGER);
		BigInteger rowCount = this.getBaseDao().queryByMap(COUNT_SQL.toString(), params);
		List<StimulateOrderVO> stimulateOrderVOS = new ArrayList<>();
		if(rowCount.intValue() > 0){
			stimulateOrderVOS = this.getBaseDao().searchByName(SQL.toString(), fieldTypeMap, StimulateOrderVO.class, params, stimulateOrderDTO.getPageNumber(), stimulateOrderDTO.getPageSize());
			// 获取当前用户刷单次数
			stimulateOrderVOS.stream().forEach(e->{
				e.setBrushesCount(brushesCount(e.getUserId(), e.getActivityId().intValue()));
			});
		}
		HashMap<String, Object> resultMap = new HashMap<>();
		resultMap.put("totalSize", rowCount.intValue());
		resultMap.put("data", stimulateOrderVOS);
		return resultMap;
	}

	@Override
	public ResultMsgBean updateStimulateOrderStatus(EmployeeQueryBean curUser, List<Long> idList, String reason, Integer type, Long userId, Integer fileType, List<MultipartFile> fileList) throws Exception{
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		// 获取操作人姓名
		TytEmployee employee = employeeService.getEmployeeById(userId);
		if(null == employee){
			throw new RuntimeException("获取操作用户信息异常");
		}


		//解除嫌疑
		if(3 == type){

			if (0 < fileList.size()) {
				for (Long id : idList) {
					//查询已上传文件数量
					List<FileInfo> fileInfoList = fileInfoService.getSingleFileInfoId(id, fileType,1);
					if(fileInfoList != null && fileInfoList.size()>0){
						//已上传文件的数量不能超过5张
						int uploadFileNum = fileInfoList.size();
						if(uploadFileNum >= 5){
							resultMsgBean.setCode(500);
							resultMsgBean.setMsg("上传图片数量不能超过5张");
							return resultMsgBean;
						}
					}
				}
				//上传图片
				Map map = addStimulateUploadFile(curUser, idList, fileList, fileType);
				String flag = map.get("flag")+"";
				if (flag.equals("false")) {
					resultMsgBean.setCode(500);
					resultMsgBean.setMsg("上传凭证图片失败！");
					return resultMsgBean;
				}
				resultMsgBean.setData(map);
			}

			for (Long id : idList) {
				//更新tyt_stimulate_activity表
				StringBuffer sql = new StringBuffer("update tyt_stimulate_activity set activity_status=?, update_time=now(), remark=?, update_user_name=?, appeal_status = 3 where id=? and activity_status = 1");
				int count = stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{type, reason, employee.getRealName(), id});
				//获取tyt_stimulate_activity表用户参与记录
				List<TytStimulateActivityEntity> tytStimulateActivityEntities = stimulateActivityDao.find("from TytStimulateActivityEntity where id = ?", id.intValue());
				if (null == tytStimulateActivityEntities || tytStimulateActivityEntities.isEmpty()) {
					throw new RuntimeException("获取用户参与记录失败");
				}
				TytStimulateActivityEntity stimulateActivityEntity = tytStimulateActivityEntities.get(0);

				int activityId = stimulateActivityEntity.getMarketingActivityId();
				Long conventionUserId = stimulateActivityEntity.getUserId();
				List<TytConventionActivity> list = tytConventionActivityDao.find("from TytConventionActivity where activity_id = ? and user_id = ?", activityId, conventionUserId.intValue());
				if (null == list || list.isEmpty()) {
					throw new RuntimeException("获取用户tyt_convention_activity表记录失败");
				} else {
					TytConventionActivity tytConventionActivity = list.get(0);
					Long conventionId = tytConventionActivity.getId();
					int orderNum = tytConventionActivity.getOrderNum()+1;
					//更新tyt_convention_activity表 订单数
					StringBuffer conventionsql = new StringBuffer("update tyt_convention_activity set order_num=?, update_time=now() where id=?");
					int counts = stimulateActivityDao.executeUpdateSql(conventionsql.toString(), new Object[]{orderNum,conventionId.intValue()});
				}

//				//上传图片
//				if (0 < fileList.size()) {
//					//查询已上传文件数量
//					List<FileInfo> fileInfoList = fileInfoService.getSingleFileInfoId(id, fileType,1);
//					if(fileInfoList != null && fileInfoList.size()>0)
//					{
//						//已上传文件的数量不能超过10张
//						int uploadFileNum = fileInfoList.size();
//						if(uploadFileNum >= 5){
//							resultMsgBean.setCode(500);
//							resultMsgBean.setMsg("上传图片数量不能超过5张");
//							return resultMsgBean;
//						}
//					}
//					Map map = addUploadFile(curUser, id, fileList, fileType);
//					String flag = map.get("flag")+"";
//					if (flag.equals("false")) {
//						resultMsgBean.setCode(500);
//						resultMsgBean.setMsg("id为"+ id +"上传凭证图片失败！");
//						return resultMsgBean;
//					}
//					resultMsgBean.setData(map);
//				}

			}
			this.syncBi(idList);
		} else {
			for (Long id : idList) {
				String sql = new String();
				Object[] params = {};
				if (reason.equals("无法查询轨迹，请补充证明") ){
					sql = "update tyt_stimulate_activity set activity_status=?, update_time=now(), remark=?, update_user_name=?,appeal_status = 2 ,appeal_evidence = ? where id=? and activity_status = 1";
					params = new Object[]{1, reason, employee.getRealName(), 2, id};
				}else {
					sql = "update tyt_stimulate_activity set activity_status=?, update_time=now(), remark=?, update_user_name=?,appeal_status = 2 where id=? and activity_status = 1";
					params = new Object[]{2, reason, employee.getRealName(), id};
				}
				int count = stimulateActivityDao.executeUpdateSql(sql, params);
//				if (0 < fileList.size()) {
//					//查询已上传文件数量
//					List<FileInfo> fileInfoList = fileInfoService.getFileInfoList(id, fileType);
//					if(fileInfoList != null && fileInfoList.size()>0)
//					{
//						//已上传文件的数量不能超过10张
//						int uploadFileNum = fileInfoList.size();
//						if(uploadFileNum >= 5){
//							resultMsgBean.setCode(500);
//							resultMsgBean.setMsg("上传图片数量不能超过5张");
//							return resultMsgBean;
//						}
//					}
//					Map map = addUploadFile(curUser, id, fileList, fileType);
//					String flag = map.get("flag")+"";
//					if (flag.equals("false")) {
//						resultMsgBean.setCode(500);
//						resultMsgBean.setMsg("id为"+ id +"上传凭证图片失败！");
//						return resultMsgBean;
//					}
//					resultMsgBean.setData(map);
//				}
			}
		}
		return resultMsgBean;
	}

	private Map addStimulateUploadFile(EmployeeQueryBean curUser, List<Long> idList, List<MultipartFile> fileList, Integer fileType) throws Exception {

		//支持多级文件目录，文件目录格式 x/y/z
		String typeName = "shuandan/image";
		String flag = "true";
		Map<String,Object> map = new HashMap<String,Object>();
		int i = 1;
		for (MultipartFile file : fileList) {
			Map fileInfoMap = fileInfoService.uploadFileList(curUser, idList, fileType, file, i++, typeName);
			if(fileInfoMap == null){
				flag = "false";
			}
			if(fileList.size() == i-1){
				map = fileInfoMap;
			}

		}
		map.put("flag",flag);
		return map;
	}


	private Map addUploadFile(EmployeeQueryBean curUser, Long id, List<MultipartFile> fileList, Integer fileType) throws Exception {

		//支持多级文件目录，文件目录格式 x/y/z
		String typeName = "shuandan/image";
		String flag = "true";
		Map<String,Object> map = new HashMap<String,Object>();
		int i = 1;
		for (MultipartFile file : fileList) {
			Map fileInfoMap = fileInfoService.uploadFile(curUser, id, fileType, file, i++, typeName);
			if(fileInfoMap == null){
				flag = "false";
			}
			if(fileList.size() == i-1){
				map = fileInfoMap;
			}

		}
		map.put("flag",flag);
		return map;
	}


	@Override
	public ResultMsgBean updateStimulateOrderUserFlag(Integer id, String userFlag,EmployeeQueryBean curUser) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		StringBuffer sql = new StringBuffer("update tyt_stimulate_activity set user_flag=? where id=? and activity_status = 1");
		int count = stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{ userFlag, id});
		TytStimulateActivityEntity tytStimulateActivity = stimulateActivityService.getById(id);
		activityUserFlagService.saveUserFlag(tytStimulateActivity,userFlag,curUser);
		return resultMsgBean;
	}

	@Override
	public UserInviteAwardInfoBean getInviteAwardInfoById(Long id) {
		String querySQL = "SELECT u.cell_phone cellPhone,t.award_days awardDays,"
				+ "t.invite_cell_phone inviteCellPhone,t.user_state userState "
				+ "FROM plat_user_invite_award_info t left join tyt_user u on t.user_id = u.id where t.id=:id";
		Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
		map.put("cellPhone", Hibernate.STRING);
		map.put("awardDays", Hibernate.INTEGER);
		map.put("inviteCellPhone", Hibernate.STRING);
		map.put("userState", Hibernate.INTEGER);
		Map<String,Object> paramMap = new HashMap<>();
		paramMap.put("id",id);
		return this.getBaseDao().queryByMap(querySQL, paramMap, UserInviteAwardInfoBean.class, map);
	}

	/**
	  * <AUTHOR> Lion
	  * @Description 刷单判定短信发送
	  * @Param [type, stimulateActivityEntity, tytTransportOrders]
	  * @return void
	  * @Date 2022/8/24 14:07
	  */
	private void addSendSms(Integer type, TytStimulateActivityEntity stimulateActivityEntity, TytTransportOrders tytTransportOrders) {
		if(!StringUtils.isBlank(stimulateActivityEntity.getUserAccount())||!StringUtils.isBlank(stimulateActivityEntity.getCarUserAccount())){
			String cellPhone = StringUtils.isBlank(stimulateActivityEntity.getUserAccount())?stimulateActivityEntity.getCarUserAccount():stimulateActivityEntity.getUserAccount();
			String tmpl ="";
			if (2 == type) {
				tmpl = messageTmplService.getSmsTmpl(Constant.STIMULATE_ORDER_CONFIRM_UPDATE_KEY, "您的运单（${orderNum}）${startPoint}/${destPoint}/${content}，经核实存在刷单行为，本单将不进行统计，请您严格遵守规则，否则将取消您的参与资格。");
			} else {
				tmpl = messageTmplService.getSmsTmpl(Constant.STIMULATE_ORDER_RELIEVE_UPDATE_KEY, "您的运单（${orderNum}）${startPoint}/${destPoint}/${content}，已解除刷单嫌疑，正常进行订单统计，请到线上查看。");
			}
			String orderNum = stimulateActivityEntity.getOrderNumber()+"";
			String startPoint = tytTransportOrders.getStartPoint();
			String destPoint = tytTransportOrders.getDestPoint();
			String taskContent = tytTransportOrders.getTaskContent();
			String content = StringUtils.replaceEach(tmpl, new String[] { "${orderNum}", "${startPoint}", "${destPoint}", "${content}" }, new String[] { orderNum, startPoint, destPoint, taskContent });
			ShortMsgBean shortMsgBean = new ShortMsgBean();
			shortMsgBean.setContent(content);
			shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			String messageSerailNum = SerialNumUtil.generateSeriaNum();
			shortMsgBean.setMessageSerailNum(messageSerailNum);
			shortMsgBean.setCell_phone(cellPhone);
			shortMsgBean.setRemark("");
			tytMqMessageService.addMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			tytMqMessageService.sendMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean));

		}

	}

	@Override
	public ResultMsgBean stimulateActivityDetail(Long id) {
		List<TytStimulateActivityEntity> tytStimulateActivityEntities = stimulateActivityDao.find("from TytStimulateActivityEntity where id = ?", id.intValue());
		if(null == tytStimulateActivityEntities || tytStimulateActivityEntities.isEmpty()){
			throw new RuntimeException("获取用户参与记录失败");
		}
		// 获取用户刷单的次数
		TytStimulateActivityEntity tytStimulateActivityEntity = tytStimulateActivityEntities.get(0);
		tytStimulateActivityEntity.setBrushingCount(platUserInviteAwardInfoService.brushesCount(tytStimulateActivityEntity.getUserId(), tytStimulateActivityEntity.getMarketingActivityId()));
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		resultMsgBean.setData(tytStimulateActivityEntity);
		return resultMsgBean;
	}

	@Override
	public Integer brushesCount(Long userId, Integer activityId){
		List<TytStimulateActivityEntity> tytStimulateActivityEntities = stimulateActivityDao.find("from TytStimulateActivityEntity where userId = ? and activityStatus = 2 and marketing_activity_id = ?", userId, activityId);
		return null == tytStimulateActivityEntities ? 0 : tytStimulateActivityEntities.size();
	}

	/**
	 * 计算要扣除的会员天数
	 * @param remainingDays 剩余的会员天数
	 * @param repealDays 需要扣除的会员天数
	 * @return 真正扣除的会员天数
	 */
	private int countRepealDays(int remainingDays, int repealDays){
		if(remainingDays >= repealDays){
			// 当剩余的会员天数大于要扣减的会员天数直接扣除既可
			return repealDays;
		}else if(remainingDays <= repealDays && remainingDays > 0){
			// 如果剩余的会员天数大于 0 并且小于要扣减的天数，以剩余的会员天数为准
			return remainingDays;
		}else{
			return 0;
		}
	}


	/**
	 * 解除刷单嫌疑同步BI
	 * @param ids
	 * @throws Exception
	 */
	public void syncBi(List<Long> ids) throws Exception {
		//根据刷单表id查询订单id
		List<Long> orderIds = new ArrayList<>();
		if(null != ids && ids.size() > 0){
			for(Long id : ids){
				TytStimulateActivityEntity tytStimulateActivityEntity = stimulateActivityService.getById(id.intValue());
				orderIds.add(tytStimulateActivityEntity.getOrderNumber());
			}
		}

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		MultiValueMap<String,Object> map = new LinkedMultiValueMap<>();
		map.add("order_id",orderIds);
		map.add("risk_type","2");
		map.add("api_sign","8d60c1fdf478100169faf26408504b4b");
		String nowTimeStr = DateUtil.dateToString(new Date(), DateUtil.date_time_format);
		map.add("api_time",nowTimeStr);

		String biBase = tytConfigService.getStringValue(Constant.SYNC_CAR_BI_HTTP_BASE_KEY, "http://*************");
		String biPath = "/idc/index.php/apibi/riskOrder/doProcess";


		String url = biBase + biPath;
		logger.info("bi service request getCurrentLocation url is: " + url);
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(map, headers);
		try {

			ResponseEntity<JSONObject> response = restTemplate.postForEntity(url, request, JSONObject.class);

			if (response.getStatusCode().is2xxSuccessful()) {
				JSONObject respBody = response.getBody();
				logger.info("调用bi解除风控单状态【{}】", respBody);
				Integer respCode = respBody.getInteger("code");
				logger.info("调用bi解除风控单【{}】", respCode);
			}
		} catch (Exception e) {
			logger.error("BI服务器-同步解除刷单嫌疑出错：", e);
			throw new Exception();
		}
	}


	/**
	 * 解除刷单嫌疑同步BI -- 新 2023/3/29
	 * @param ids
	 * @throws Exception
	 */
	public void syncBiRisk(List<Long> ids) throws Exception {
		//根据刷单表id查询订单id
		List<Long> orderIds = new ArrayList<>();
		if(null != ids && ids.size() > 0){
			for(Long id : ids){
				TytTransportOrdersRisk tytStimulateActivityEntity = transportOrdersRiskService.getById(id.intValue());
				orderIds.add(tytStimulateActivityEntity.getOrderNumber());
			}
		}

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		MultiValueMap<String,Object> map = new LinkedMultiValueMap<>();
		map.add("order_id",orderIds);
		map.add("risk_type","2");
		map.add("api_sign","8d60c1fdf478100169faf26408504b4b");
		String nowTimeStr = DateUtil.dateToString(new Date(), DateUtil.date_time_format);
		map.add("api_time",nowTimeStr);

		String biBase = tytConfigService.getStringValue(Constant.SYNC_CAR_BI_HTTP_BASE_KEY, "http://*************");
		String biPath = "/idc/index.php/apibi/riskOrder/doProcess";


		String url = biBase + biPath;
		logger.info("bi service request getCurrentLocation url is: " + url);
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(map, headers);
		try {

			ResponseEntity<JSONObject> response = restTemplate.postForEntity(url, request, JSONObject.class);

			if (response.getStatusCode().is2xxSuccessful()) {
				JSONObject respBody = response.getBody();
				logger.info("调用bi解除风控单状态【{}】", respBody);
				Integer respCode = respBody.getInteger("code");
				logger.info("调用bi解除风控单【{}】", respCode);
			}
		} catch (Exception e) {
			logger.error("BI服务器-同步解除刷单嫌疑出错：", e);
			throw new Exception();
		}
	}

	/**
	 * @Description 刷单订单列表 -新 2023/3/29
	 * @Param [stimulateOrderDTO]
	 */
	@Override
	public HashMap<String, Object> ordersRiskList(StimulateOrderDTO stimulateOrderDTO) throws ParseException {
		MarketingActivity marketingActivity = null;

		//修正开始、结束时间请求参数
		correctionGetOrdersRiskListParamStartTimeAndEndTime(stimulateOrderDTO);

		//活动结束推后三天时间
		Date threeEndTime = null;
		if(null != stimulateOrderDTO.getActivityId()) {
			marketingActivity = marketingActivityService.getActivityById(stimulateOrderDTO.getActivityId());
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(marketingActivity.getEndTime());
			calendar.add(Calendar.DATE,3);
			threeEndTime = calendar.getTime();
		}
		StringBuffer SQL = new StringBuffer("select say.id as id, " +
				" say.user_id as userId,say.track_decide trackDecide, say.user_account as userAccount, say.car_user_id as carUserId, say.car_user_account as carUserAccount, say.order_number as orderNumber, say.ts_order_no as tsOrderNo, " +
				" say.order_status as orderStatus, say.activity_status as activityStatus, say.update_time as updateTime, say.user_flag as userFlag, say.update_user_name as updateUserName, " +
				" say.marketing_activity_id as activityId, say.suspect_type as suspectType ,say.appeal_status as appealStatus,say.appeal_evidence as appealEvidence,say.before_type as beforeType ,say.create_time as createTime," +
				" tor.head_city as headCity ,tor.head_no as headNo,tor.tail_city as tailCity,tor.tail_no as tailNo ,tor.pay_end_time as payEndTime ,tor.load_time as loadTime," +
				" tor.start_point as startPoint, tor.dest_point as destPoint,say.user_flag_remark as userFlagRemark, tor.task_content as taskContent,tor.refund_flag refundFlag " +
				" from tyt_transport_orders_risk say JOIN tyt_transport_orders tor ON say.order_number = tor.id JOIN tyt_transport_order_snapshot ttos ON tor.id = ttos.order_id " );
		StringBuffer COUNT_SQL = new StringBuffer("select count(*) from tyt_transport_orders_risk say JOIN tyt_transport_orders tor ON say.order_number = tor.id JOIN tyt_transport_order_snapshot ttos ON tor.id = ttos.order_id ");
		HashMap<String, Object> params = new HashMap<>();
		//活动筛选1:区分车/货方，是否连表
		if (marketingActivity != null && marketingActivity.getActivityPart() == 1 && marketingActivity.getActivityScope() == 2){//车方活动(定向)
			SQL.append(" JOIN marketing_activity_user mau ON mau.user_id = say.car_user_id where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 ");
			COUNT_SQL.append(" JOIN marketing_activity_user mau ON mau.user_id = say.car_user_id where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 ");
		}else if (marketingActivity != null && marketingActivity.getActivityPart() == 2 && marketingActivity.getActivityScope() == 2){ //货方活动(定向)
			SQL.append(" JOIN marketing_activity_user mau ON mau.user_id = say.user_id where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 ");
			COUNT_SQL.append(" JOIN marketing_activity_user mau ON mau.user_id = say.user_id where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 ");
		}else if (stimulateOrderDTO.getActivityId() == null || marketingActivity == null || marketingActivity.getActivityScope() == 1){//没有活动筛选/全员活动
			SQL.append(" where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 ");
			COUNT_SQL.append(" where 1 = 1 and say.activity_type = 2 and say.activity_status <> 0 ");
		}
		// 好差货过滤锁定中的订单
		SQL.append(" AND (ttos.seckill_goods in (0,2) OR (ttos.seckill_goods = 1 AND ttos.order_allocate_state = 1  AND tor.order_new_status IN (10, 15, 20, 25, 30, 35))) ");
		COUNT_SQL.append(" AND (ttos.seckill_goods in (0,2) OR (ttos.seckill_goods = 1 AND ttos.order_allocate_state = 1  AND tor.order_new_status IN (10, 15, 20, 25, 30, 35))) ");
		//活动筛选2:活动id、活动开始时间、结束时间
		if (null != marketingActivity){
			if (marketingActivity.getActivityType() == 4 || marketingActivity.getActivityType() == 7){//履约活动（车/货）结束时间往后推3天
				SQL.append(" and mau.activity_id = :activityId and say.create_time >= :startTime and say.pay_end_time <= :endTime and say.create_time <= :threeEndTime ");
				COUNT_SQL.append(" and mau.activity_id = :activityId and say.create_time >= :startTime and say.pay_end_time <= :endTime and say.create_time <= :threeEndTime ");
				params.put("activityId", stimulateOrderDTO.getActivityId());
				params.put("startTime", marketingActivity.getStartTime());
				params.put("endTime",marketingActivity.getEndTime());
				params.put("threeEndTime",threeEndTime);
			}else {
				SQL.append(" and mau.activity_id = :activityId and say.create_time >= :startTime and say.create_time <= :endTime ");
				COUNT_SQL.append(" and mau.activity_id = :activityId and say.create_time >= :startTime and say.create_time <= :endTime ");
				params.put("activityId", stimulateOrderDTO.getActivityId());
				params.put("startTime", marketingActivity.getStartTime());
				params.put("endTime",marketingActivity.getEndTime());
			}
		}
		if(null != stimulateOrderDTO.getUserId()){
			SQL.append(" and say.user_id = :userId  ");
			COUNT_SQL.append(" and say.user_id = :userId  ");
			params.put("userId",stimulateOrderDTO.getUserId());
		}
		// 6480 新增
		if(null != stimulateOrderDTO.getOrderNumber()){
			SQL.append(" and tor.id = :orderNumber  ");
			COUNT_SQL.append(" and tor.id = :orderNumber ");
			params.put("orderNumber",stimulateOrderDTO.getOrderNumber());
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getUserFlag())){
			if("无标签".equals(stimulateOrderDTO.getUserFlag())){
				SQL.append(" and  say.user_flag is null  ");
				COUNT_SQL.append(" and say.user_flag is null  ");
			}else{
				SQL.append(" and say.user_flag = :userFlag ");
				COUNT_SQL.append(" and say.user_flag = :userFlag ");
				params.put("userFlag",stimulateOrderDTO.getUserFlag());
			}
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getUserAccount())){
			SQL.append(" and say.user_account = :userAccount ");
			COUNT_SQL.append(" and say.user_account = :userAccount ");
			params.put("userAccount", stimulateOrderDTO.getUserAccount());
		}
		if(null != stimulateOrderDTO.getCarUserId()){
			SQL.append(" and say.car_user_id = :carUserId ");
			COUNT_SQL.append(" and say.car_user_id = :carUserId ");
			params.put("carUserId",stimulateOrderDTO.getCarUserId());
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getCarUserAccount())){
			SQL.append(" and say.car_user_account = :carUserAccount ");
			COUNT_SQL.append(" and say.car_user_account = :carUserAccount ");
			params.put("carUserAccount", stimulateOrderDTO.getCarUserAccount());
		}
		if(null != stimulateOrderDTO.getActivityStatus()){
			SQL.append(" and say.activity_status = :activityStatus ");
			COUNT_SQL.append(" and say.activity_status = :activityStatus ");
			params.put("activityStatus", stimulateOrderDTO.getActivityStatus());
		}
		if(null != stimulateOrderDTO.getOrderStatus()){
			SQL.append(" and say.order_status = :orderStatus ");
			COUNT_SQL.append(" and say.order_status = :orderStatus ");
			params.put("orderStatus", stimulateOrderDTO.getOrderStatus());
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getUpdateUserName())){
			SQL.append(" and say.update_user_name = :updateUserName ");
			COUNT_SQL.append(" and say.update_user_name = :updateUserName ");
			params.put("updateUserName", stimulateOrderDTO.getUpdateUserName());
		}
		if(null != stimulateOrderDTO.getStartTime()){
			SQL.append(" and say.update_time >=:startTime ");
			COUNT_SQL.append(" and say.update_time >=:startTime ");
			params.put("startTime", new Date(stimulateOrderDTO.getStartTime()));
		}
		if(null != stimulateOrderDTO.getEndTime()){
			SQL.append(" and say.update_time <=:endTime ");
			COUNT_SQL.append(" and say.update_time <=:endTime ");
			params.put("endTime", new Date(stimulateOrderDTO.getEndTime()));
		}
		if(StringUtils.isNotBlank(stimulateOrderDTO.getTsOrderNo())){
			SQL.append(" and say.ts_order_no = :tsOrderNo ");
			COUNT_SQL.append(" and say.ts_order_no = :tsOrderNo ");
			params.put("tsOrderNo",stimulateOrderDTO.getTsOrderNo());
		}
		//用户申诉状态 0-未申诉、1-待审核、2-审核完成 4-已驳回
		if(null != stimulateOrderDTO.getAppealStatus()){
			if (stimulateOrderDTO.getAppealStatus() == 0 || stimulateOrderDTO.getAppealStatus() == 1
			|| stimulateOrderDTO.getAppealStatus() == 4){
				SQL.append(" and say.appeal_status = :appealStatus ");
				COUNT_SQL.append(" and say.appeal_status = :appealStatus ");
				params.put("appealStatus", stimulateOrderDTO.getAppealStatus());
			}else {
				SQL.append(" and say.appeal_status in (2,3) ");
				COUNT_SQL.append(" and say.appeal_status in (2,3) ");
			}
		}
		//接单车牌状态 0-未填写、1-已填写
		if(null != stimulateOrderDTO.getPlateNumber()){
			if (stimulateOrderDTO.getPlateNumber() == 0){
				SQL.append(" and tor.head_city is null ");
				COUNT_SQL.append(" and tor.head_city is null ");
			}else {
				SQL.append(" and tor.head_city is not null ");
				COUNT_SQL.append(" and tor.head_city is not null ");
			}
		}
		//申诉证据：（0-未上传、1-已上传 2、待上传）
		if (null != stimulateOrderDTO.getAppealEvidence()){
			SQL.append(" and say.appeal_evidence = :appealEvidence ");
			COUNT_SQL.append(" and say.appeal_evidence = :appealEvidence ");
			params.put("appealEvidence", stimulateOrderDTO.getAppealEvidence());
		}

		//用户标签：（0-无标签、1-有标签）
		if(null != stimulateOrderDTO.getActivityUserFlag()){
			if (stimulateOrderDTO.getActivityUserFlag() == 0){
				SQL.append(" and say.user_flag is null ");
				COUNT_SQL.append(" and say.user_flag is null ");
			}else {
				SQL.append(" and say.user_flag is not null and say.user_flag !='' ");
				COUNT_SQL.append(" and say.user_flag is not null and say.user_flag !='' ");
			}
		}
		//风控时间
		if(null != stimulateOrderDTO.getCreateTimeStart()){
			SQL.append(" and say.create_time >=:createTimeStart ");
			COUNT_SQL.append(" and say.create_time >=:createTimeStart ");
			params.put("createTimeStart", stimulateOrderDTO.getCreateTimeStart());
		}
		if(null != stimulateOrderDTO.getCreateTimeEnd()){
			//时间处理
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date endTime = format.parse(formatter.format(stimulateOrderDTO.getCreateTimeEnd())+" 23:59:59");
			SQL.append(" and say.create_time <=:createTimeEnd ");
			COUNT_SQL.append(" and say.create_time <=:createTimeEnd ");
			params.put("createTimeEnd", endTime);
		}
		//申述人
		if (null != stimulateOrderDTO.getBeforeType()){
			SQL.append(" and say.before_type =:beforeType ");
			COUNT_SQL.append(" and say.before_type =:beforeType ");
			params.put("beforeType", stimulateOrderDTO.getBeforeType());
		}

		//轨迹判定结果
		if (null != stimulateOrderDTO.getTrackDecide()){
			SQL.append(" and say.track_decide =:trackDecide ");
			COUNT_SQL.append(" and say.track_decide =:trackDecide ");
			params.put("trackDecide", stimulateOrderDTO.getTrackDecide());
		}



		SQL.append("ORDER BY field(say.appeal_status,1,4,0,2,3),say.update_time DESC");
		HashMap<String, Type> fieldTypeMap = new HashMap<>();
		fieldTypeMap.put("id", Hibernate.INTEGER);
		fieldTypeMap.put("userId", Hibernate.LONG);
		fieldTypeMap.put("trackDecide", Hibernate.INTEGER);
		fieldTypeMap.put("userAccount", Hibernate.STRING);
		fieldTypeMap.put("carUserId", Hibernate.LONG);
		fieldTypeMap.put("carUserAccount", Hibernate.STRING);
		fieldTypeMap.put("orderNumber", Hibernate.LONG);
		fieldTypeMap.put("tsOrderNo", Hibernate.STRING);
		fieldTypeMap.put("orderStatus", Hibernate.INTEGER);
		fieldTypeMap.put("activityStatus", Hibernate.INTEGER);
		fieldTypeMap.put("updateTime", Hibernate.TIMESTAMP);
		fieldTypeMap.put("userFlag", Hibernate.STRING);
		fieldTypeMap.put("updateUserName", Hibernate.STRING);
		fieldTypeMap.put("suspectType", Hibernate.STRING);
		fieldTypeMap.put("activityId", Hibernate.LONG);
		fieldTypeMap.put("headCity",Hibernate.STRING);
		fieldTypeMap.put("headNo",Hibernate.STRING);
		fieldTypeMap.put("tailCity",Hibernate.STRING);
		fieldTypeMap.put("tailNo",Hibernate.STRING);
		fieldTypeMap.put("startPoint",Hibernate.STRING);
		fieldTypeMap.put("destPoint",Hibernate.STRING);
		fieldTypeMap.put("taskContent",Hibernate.STRING);
		fieldTypeMap.put("appealStatus",Hibernate.INTEGER);
		fieldTypeMap.put("appealEvidence",Hibernate.INTEGER);
		fieldTypeMap.put("createTime",Hibernate.TIMESTAMP);
		fieldTypeMap.put("beforeType",Hibernate.INTEGER);
		fieldTypeMap.put("payEndTime",Hibernate.TIMESTAMP);
		fieldTypeMap.put("loadTime",Hibernate.TIMESTAMP);
		fieldTypeMap.put("refundFlag",Hibernate.INTEGER);
		fieldTypeMap.put("userFlagRemark",Hibernate.STRING);

		BigInteger rowCount = this.getBaseDao().queryByMap(COUNT_SQL.toString(), params);
		List<StimulateOrderVO> stimulateOrderVOS = new ArrayList<>();
		if(rowCount.intValue() > 0){
			stimulateOrderVOS = this.getBaseDao().searchByName(SQL.toString(), fieldTypeMap, StimulateOrderVO.class, params, stimulateOrderDTO.getPageNumber(), stimulateOrderDTO.getPageSize());
			// 获取当前用户刷单次数
			stimulateOrderVOS.stream().forEach(e->{
				e.setBrushesCount(brushesCount(e.getUserId(), e.getActivityId().intValue()));
			});
		}
		HashMap<String, Object> resultMap = new HashMap<>();
		resultMap.put("totalSize", rowCount.intValue());
		resultMap.put("data", stimulateOrderVOS);
		return resultMap;
	}

	/**
	 * 修正新刷单列表接口 开始、结束时间请求参数8点问题
	 * stimulateOrderDTO stimulateOrderDTO
	 */
	private void correctionGetOrdersRiskListParamStartTimeAndEndTime(StimulateOrderDTO stimulateOrderDTO) {
		Long startTime = stimulateOrderDTO.getStartTime();
		Long endTime = stimulateOrderDTO.getEndTime();
		if (startTime != null) {
			LocalDateTime startLocalDateTime = new LocalDateTime(startTime);
			LocalDateTime newStartLocalDateTime = startLocalDateTime.withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
			stimulateOrderDTO.setStartTime(newStartLocalDateTime.toDateTime().getMillis());

		}
		if (endTime != null) {
			LocalDateTime endLocalDateTime = new LocalDateTime(endTime);
			LocalDateTime newEndLocalDateTime = endLocalDateTime.withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withMillisOfSecond(999);
			stimulateOrderDTO.setEndTime(newEndLocalDateTime.toDateTime().getMillis());
		}
	}


	/**
	 * @Description 确认刷单/解除嫌疑接口 -新 2023/3/29
	 * @Param [stimulateOrderDTO]
	 */
	@Override
	public ResultMsgBean updateOrdersRiskStatus(EmployeeQueryBean curUser, List<Long> idList, String reason, Integer type, Long userId, Integer fileType, List<MultipartFile> fileList) throws Exception{
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		// 获取操作人姓名
		TytEmployee employee = employeeService.getEmployeeById(userId);
		logger.info("获取操作人：{}",employee);
		if(null == employee){
			throw new RuntimeException("获取操作用户信息异常");
		}

		if(3 == type){

			if (0 < fileList.size()) {
				for (Long id : idList) {
					//查询已上传文件数量
					List<FileInfo> fileInfoList = fileInfoService.getSingleFileInfoId(id, fileType,1);
					if(fileInfoList != null && fileInfoList.size()>0){
						//已上传文件的数量不能超过5张
						int uploadFileNum = fileInfoList.size();
						if(uploadFileNum >= 5){
							resultMsgBean.setCode(500);
							resultMsgBean.setMsg("上传图片数量不能超过5张");
							return resultMsgBean;
						}
					}
				}
				//上传图片
				Map map = addStimulateUploadFile(curUser, idList, fileList, fileType);
				String flag = map.get("flag")+"";
				if (flag.equals("false")) {
					resultMsgBean.setCode(500);
					resultMsgBean.setMsg("上传凭证图片失败！");
					return resultMsgBean;
				}
				resultMsgBean.setData(map);
			}

			for (Long id : idList) {
				//更新tyt_stimulate_activity表
				StringBuffer sql = new StringBuffer("update tyt_transport_orders_risk set activity_status=?, update_time=now(), remark=?, update_user_name=?, appeal_status = 3 where id=? and activity_status = 1");
				int count = stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{type, reason, employee.getRealName(), id});

				TytTransportOrdersRisk transportOrdersRisk = transportOrdersRiskService.getById(id.intValue());

				MqRiskMsg mqRiskMsg = new MqRiskMsg();

				mqRiskMsg.setId(id.intValue());
				mqRiskMsg.setUserId(transportOrdersRisk.getUserId());
				mqRiskMsg.setCarUserId(transportOrdersRisk.getCarUserId());
				mqRiskMsg.setOrderNumber(transportOrdersRisk.getOrderNumber());
				mqRiskMsg.setTsOrderNo(transportOrdersRisk.getTsOrderNo());
				mqRiskMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
				mqRiskMsg.setMessageType(MqBaseMessageBean.RELIEVE_RISK);

				mqMessageService.addMqMessage(mqRiskMsg.getMessageSerailNum(), JSON.toJSONString(mqRiskMsg), MqBaseMessageBean.RELIEVE_RISK);
				mqMessageService.sendMqMessage(mqRiskMsg.getMessageSerailNum(), JSON.toJSONString(mqRiskMsg));

				//发送新mq处理(目前处理冲单、货履约)
//				transportOrdersRiskService.sendMqMessage(id);

			}

			this.syncBiRisk(idList);

		} else {
			for (Long id : idList) {
				String sql = new String();
				Object[] params = {};
				TytTransportOrdersRisk ordersRisk = transportOrdersRiskService.getById(id.intValue());
				if (ordersRisk != null){
					//如果双方都已提交
					if (ordersRisk.getGoodStatus() == 1 && ordersRisk.getCarStatus() == 1){
						sql = "update tyt_transport_orders_risk set activity_status=?, update_time=now(), remark=?, update_user_name=?,appeal_status = 2 where id=? and activity_status = 1";
						params = new Object[]{2, reason, employee.getRealName(), id};
					}else {
						sql = "update tyt_transport_orders_risk set activity_status=?, update_time=now(), remark=?, update_user_name=?,appeal_status = 2  where id=? and activity_status = 1";
						params = new Object[]{1, reason, employee.getRealName(), id};
					}
				}
				int count = stimulateActivityDao.executeUpdateSql(sql, params);
			}
		}
		return resultMsgBean;
	}


	/**
	 * @Description 修改用户标签 - 新 2023/3/29
	 */
	@Override
	public ResultMsgBean updateRiskOrderUserFlag(Integer id, String userFlag,EmployeeQueryBean curUser,String userFlagRemark) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		String name = curUser.getName();
		StringBuffer sql = new StringBuffer("update tyt_transport_orders_risk set user_flag=?,user_flag_remark=?,update_user_name= ? where id=? and activity_status = 1");
		stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{ userFlag, userFlagRemark,name,id});
		TytTransportOrdersRisk tytTransportOrdersRisk = transportOrdersRiskService.getById(id);
		activityUserFlagService.saveRiskUserFlag(tytTransportOrdersRisk,userFlag,curUser,userFlagRemark);
		return resultMsgBean;
	}

	@Override
	public void operateUser(Integer id, String name) {
		StringBuffer sql = new StringBuffer("update tyt_transport_orders_risk set update_user_name= ? where id=? and activity_status = 1");
		stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{name,id});
	}


}
