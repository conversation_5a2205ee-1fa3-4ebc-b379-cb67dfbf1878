package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.VO.StimulateOrderVO;
import com.tyt.model.*;
import com.tyt.service.base.BaseService;
import java.util.List;


/**
 * @description 刷单记录服务层
 * <AUTHOR>
 * @date 2022/8/30 9:47
 */
public interface StimulateActivityService extends BaseService<TytStimulateActivityEntity, Integer>{

    StringBuffer exportStimulateOrderList(List<StimulateOrderVO> data);

    TytStimulateActivityEntity getCarPlateNumber(Integer activityId);

    /**
     * @Description 导出刷单订单列表 -新 2023/3/29
     */
    StringBuffer exportOrdersRiskList(List<StimulateOrderVO> data);

}
