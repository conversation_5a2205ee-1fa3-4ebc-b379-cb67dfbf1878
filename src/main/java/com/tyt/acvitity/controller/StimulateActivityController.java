package com.tyt.acvitity.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tyt.acvitity.bean.CarLocation;
import com.tyt.acvitity.bean.DTO.StimulateOrderDTO;
import com.tyt.acvitity.bean.StimulateActivityDetailBean;
import com.tyt.acvitity.bean.StimulateFileInfoBean;
import com.tyt.acvitity.bean.VO.StimulateOrderVO;
import com.tyt.acvitity.service.PlatUserInviteAwardInfoService;
import com.tyt.acvitity.service.StimulateActivityService;
import com.tyt.acvitity.service.StimulateCarLocationService;
import com.tyt.file.bean.FileInfo;
import com.tyt.file.service.FileInfoService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.model.*;
import com.tyt.service.car.CarService;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.transport.TransportMainService;
import com.tyt.service.waybill.TytTransportWaybillService;
import com.tyt.userFlag.service.ActivityUserFlagService;
import com.tyt.util.CsvWriter;
import com.tyt.util.httpclient.CarLocationUtils;
import com.tyt.web.base.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

/**
 * @ClassName StimulateActivityController
 * @Description 履约活动刷单管理
 * <AUTHOR> Lion
 * @Date 2022/8/17 16:24
 * @Verdion 1.0
 **/
@Controller
@RequestMapping("/stimulate")
public class StimulateActivityController extends BaseController {

    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;

    @Resource(name = "platUserInviteAwardInfoService")
    private PlatUserInviteAwardInfoService platUserInviteAwardInfoService;

    @Autowired
    private CarService carService;

    @Autowired
    private StimulateActivityService stimulateActivityService;

    @Autowired
    private TytTransportWaybillService waybillService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private FileInfoService fileInfoService;

    @Autowired
    private StimulateCarLocationService stimulateCarLocationService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private ActivityUserFlagService activityUserFlagService;

    //服务器图片路径(旧规则)
    public static final String TYT_SERVER_PICTURE_URL_OLD = "tyt_server_picture_url_old";

    public static final String PREFIX_PICTURE = "prefix_picture";
    /**
      * <AUTHOR> Lion
      * @Description 活动筛选框列表
      * @Param [flag, request]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/8/17 17:01
      */
    @RequestMapping("/getActivityList")
    @ResponseBody
    public ResultMsgBean getActivity(
            @RequestParam(name = "flag",required = true) Integer flag, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            List<MarketingActivity> activityForRule = marketingActivityService.getActivityList(flag);
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(activityForRule);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
      * <AUTHOR> Lion
      * @Description 刷单订单列表
      * @Param [stimulateOrderDTO]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/8/23 13:44
      */
    @ResponseBody
    @RequestMapping("/stimulateOrderList")
    public ResultMsgBean stimulateActivityList(StimulateOrderDTO stimulateOrderDTO){
        ResultMsgBean rm = new ResultMsgBean();
        try {
            HashMap<String, Object> map = platUserInviteAwardInfoService.stimulateOrderList(stimulateOrderDTO);
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * <AUTHOR>
     * @Description 导出刷单订单列表
     * @param stimulateOrderDTO
     * @return
     * @Date 2022/09/22 13:44
     */
    @ResponseBody
    @RequestMapping("/export/stimulateOrderList")
    public void exportStimulateActivityList(HttpServletResponse response,StimulateOrderDTO stimulateOrderDTO){
        try {
            if(stimulateOrderDTO.getPageNumber()==null){
                stimulateOrderDTO.setPageNumber(1);
            }
            if(stimulateOrderDTO.getPageSize()==null){
                stimulateOrderDTO.setPageSize(10000);
            }
            HashMap<String, Object> map = platUserInviteAwardInfoService.stimulateOrderList(stimulateOrderDTO);
            List<StimulateOrderVO> stimulateOrderVOSList = JSON.parseObject(JSON.toJSONString(map.get("data")),
                    new TypeReference<List<StimulateOrderVO>>() {});
            StringBuffer csvStr = stimulateActivityService.exportStimulateOrderList(stimulateOrderVOSList);
            CsvWriter.exportCsv("刷单订单列表导出_", csvStr.toString(), response);
        } catch (Exception e) {
            logger.error("导出刷单订单列表异常 the error msg is {}", e.getMessage());
            e.printStackTrace();
        }
    }


    /**
      * <AUTHOR> Lion
      * @Description 确认刷单/解除嫌疑接口
      * @Param [idList, reason, type, fileType, uploadFile1, uploadFile2, uploadFile3, uploadFile4, uploadFile5, request, userId]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/8/26 14:01
      */
    @ResponseBody
    @RequestMapping("/updateStimulateOrderStatus")
    public ResultMsgBean updateStimulateOrderStatus(@RequestParam(name = "idList") List<Long> idList,
                                                    @RequestParam(name = "reason") String reason,
                                                    @RequestParam(name = "type") Integer type,
                                                    @RequestParam(name = "fileType",required = false) Integer fileType,
                                                    @RequestParam(name = "uploadFile1",required = false) MultipartFile uploadFile1,
                                                    @RequestParam(name = "uploadFile2",required = false) MultipartFile uploadFile2,
                                                    @RequestParam(name = "uploadFile3",required = false) MultipartFile uploadFile3,
                                                    @RequestParam(name = "uploadFile4",required = false) MultipartFile uploadFile4,
                                                    @RequestParam(name = "uploadFile5",required = false) MultipartFile uploadFile5,
                                                    HttpServletRequest request,
                                                    @RequestParam(name = "userId") Long userId) throws ParseException {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 登陆验证
            EmployeeQueryBean curUser = getCurrentUser(request);
            if(curUser == null){
                rm.setCode(500);
                rm.setMsg("用户未登录");
                return rm;
            }
            if (null == idList || 0 > idList.size() || StringUtils.isBlank(reason) || reason.length() > 200 || null == type || null == userId) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("参数异常");
                return rm;
            }
            List<MultipartFile> fileList = new ArrayList<>();
            if (null != uploadFile1) {
                Long size = uploadFile1.getSize();
                if (size.longValue() > 5 * 1024 * 1024) {
                    rm.setCode(500);
                    rm.setMsg("上传文件1大小不能超过5M");
                    return rm;
                }
                fileList.add(uploadFile1);
            }
            if (null != uploadFile2) {
                Long size = uploadFile2.getSize();
                if (size.longValue() > 5 * 1024 * 1024) {
                    rm.setCode(500);
                    rm.setMsg("上传文件2大小不能超过5M");
                    return rm;
                }
                fileList.add(uploadFile2);
            }
            if (null != uploadFile3) {
                Long size = uploadFile3.getSize();
                if (size.longValue() > 5 * 1024 * 1024) {
                    rm.setCode(500);
                    rm.setMsg("上传文件3大小不能超过5M");
                    return rm;
                }
                fileList.add(uploadFile3);
            }
            if (null != uploadFile4) {
                Long size = uploadFile4.getSize();
                if (size.longValue() > 5 * 1024 * 1024) {
                    rm.setCode(500);
                    rm.setMsg("上传文件4大小不能超过5M");
                    return rm;
                }
                fileList.add(uploadFile4);
            }
            if (null != uploadFile5) {
                Long size = uploadFile5.getSize();
                if (size.longValue() > 5 * 1024 * 1024) {
                    rm.setCode(500);
                    rm.setMsg("上传文件5大小不能超过5M");
                    return rm;
                }
                fileList.add(uploadFile5);
            }
            return platUserInviteAwardInfoService.updateStimulateOrderStatus(curUser, idList, reason, type, userId, fileType, fileList);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }


    /**
     * 用户标签列表
     */
    @ResponseBody
    @RequestMapping("/getUserFlag")
    public ResultMsgBean getUserFlag(@RequestParam(name = "id") Long id,HttpServletRequest request){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        // 登陆验证
        EmployeeQueryBean curUser = getCurrentUser(request);
        if(curUser == null){
            rm.setCode(500);
            rm.setMsg("用户未登录");
            return rm;
        }
        //参数验证
        if (id == null){
            rm.setCode(500);
            rm.setMsg("id参数缺失");
            return rm;
        }
        List<TytActivityUserFlag> activityUserFlagList = activityUserFlagService.getUserFlag(id);
        if(CollectionUtils.isNotEmpty(activityUserFlagList)){
            for(TytActivityUserFlag uf : activityUserFlagList){
                if(StringUtils.isNotBlank(uf.getUserFlagRemark())){
                    uf.setUserFlag(uf.getUserFlag()+"-"+uf.getUserFlagRemark());
                }
            }
        }
        rm.setData(activityUserFlagList);
        return rm;
    }

    /**
      * <AUTHOR> Lion
      * @Description 修改用户标签
      * @Param [id, userFlag]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/8/26 10:32
      */
    @ResponseBody
    @RequestMapping("/updateUserFlag")
    public ResultMsgBean updateUserFlag(@RequestParam(name = "id") Integer id,
                                        @RequestParam(name = "userFlag") String userFlag,
                                        HttpServletRequest request){
        if(null == id || StringUtils.isBlank(userFlag) || userFlag.length() > 50 ){
            return new ResultMsgBean(ResultMsgBean.ERROR, "参数异常");
        }
        EmployeeQueryBean curUser = getCurrentUser(request);
        try {
            return platUserInviteAwardInfoService.updateStimulateOrderUserFlag(id, userFlag,curUser);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            ResultMsgBean rm = new ResultMsgBean();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }

    /**
     * 获取用户证据
     * @param id
     * @param stimulateActivityId
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/getUserFileInfo")
    public ResultMsgBean getUserFileInfo(@RequestParam(name = "id") Integer id,HttpServletRequest request) throws Exception {
        ResultMsgBean rm = new ResultMsgBean();
        // 登陆验证
        EmployeeQueryBean curUser = getCurrentUser(request);
        if(curUser == null){
            rm.setCode(500);
            rm.setMsg("用户未登录");
            return rm;
        }
        //服务器图片路径(旧规则)
        String tytServerPictureUrlOld = tytConfigService.getStringValue(PREFIX_PICTURE);
        //获取文件
        List<FileInfo> fileInfoList = fileInfoService.getSingleFileInfoId(id.longValue(),3,2);
        if (fileInfoList!=null && fileInfoList.size()>0){
            for (FileInfo fileInfo : fileInfoList) {
                fileInfo.setFilePath(tytServerPictureUrlOld + fileInfo.getFilePath());
                fileInfo.setSmallFilePath(tytServerPictureUrlOld + fileInfo.getSmallFilePath());
            }
        }
        //获取文字描述
        TytStimulateActivityEntity stimulateActivity = stimulateActivityService.getById(id);
        //获取车牌号
        TytStimulateCarLocation tytStimulateCarLocation = stimulateCarLocationService.getCarLocationComplaint(id);

        StimulateFileInfoBean stimulateFileInfoBean = new StimulateFileInfoBean();
        stimulateFileInfoBean.setFileInfoList(fileInfoList);

        rm.setCode(ResultMsgBean.OK);
        rm.setMsg(ResultMsgBean.OK_MSG);
        rm.setData(stimulateFileInfoBean);
        return rm;
    }


    /**
     * @description 获取车辆列表信息接口
     * <AUTHOR>
     * @date 2022/8/26 17:43
     * @param carUserId 车方用户ID
     * @param request
     * @param response
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = "getCarList", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getCarList(Long carUserId,Integer activityId, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                resultMsgBean.setMsg("未登录,请先登录");
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                return resultMsgBean;
            }
            //登录用户Id
            Long userId = curUser.getId();
            if(userId == null) {
                resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                resultMsgBean.setMsg("用户Id不能为空！");
                return resultMsgBean;
            }
            if(carUserId == null) {
                resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                resultMsgBean.setMsg("车方用户ID不能为空！");
                return resultMsgBean;
            }
            if(activityId == null) {
                resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                resultMsgBean.setMsg("活动表ID不能为空！");
                return resultMsgBean;
            }
            StimulateActivityDetailBean stimulatelBean = new StimulateActivityDetailBean();
            List <Car> carList = carService.getAuthCountByUserId(carUserId.toString());
            stimulatelBean.setCarList(carList);
            //用户申述车牌
            TytStimulateCarLocation tytStimulateCarLocationList = stimulateCarLocationService.getCarLocationComplaint(activityId);
            if (tytStimulateCarLocationList != null){
                stimulatelBean.setStateHeadCity(tytStimulateCarLocationList.getHeadCity());
                stimulatelBean.setStateHeadNo(tytStimulateCarLocationList.getHeadNo());
                stimulatelBean.setStateTailCity(tytStimulateCarLocationList.getTailCity());
                stimulatelBean.setStateTailNo(tytStimulateCarLocationList.getTailNo());
            }
            //用户接单车牌
            TytStimulateActivityEntity tytStimulateActivity = stimulateActivityService.getCarPlateNumber(activityId);
            if (tytStimulateActivity != null){
                TytTransportOrders transportOrders = transportOrdersService.getDetail(tytStimulateActivity.getOrderNumber());
                if (transportOrders != null){
                    stimulatelBean.setSubmitHeadCity(transportOrders.getHeadCity());
                    stimulatelBean.setSubmitHeadNo(transportOrders.getHeadNo());
                    stimulatelBean.setSubmitTailCity(transportOrders.getTailCity());
                    stimulatelBean.setSubmitTailNo(transportOrders.getTailNo());
                }
            }
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("获取车辆列表信息成功！");
            resultMsgBean.setData(stimulatelBean);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("获取车辆列表信息发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }

    /**
     * @description 查询刷单记录详情(查询车辆轨迹)接口
     * <AUTHOR>
     * @date 2022/8/30 13:07
     * @param queryType	查询类型：1.详情 2.车辆轨迹
     * @param stimulateActivityId 刷单记录id
     * @param headCity 车头牌照头字母
     * @param headNo 车头牌照号码
     * @param tailCity 挂车牌照头字母
     * @param tailNo 挂车牌照号码
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = "getStimulateDetail", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getStimulateDetail(Integer queryType, Integer stimulateActivityId,
                                            String headCity, String headNo,
                                            String tailCity, String tailNo,
                                            Date beginTime, Date endTime) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        try {
            if(queryType == null){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("查询类型不能为空！");
                return resultMsgBean;
            }
            if(stimulateActivityId == null){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("刷单记录ID不能为空！");
                return resultMsgBean;
            }
            //刷单记录详情对象
            StimulateActivityDetailBean detailBean = new StimulateActivityDetailBean();

            TytStimulateActivityEntity stimulateActivity = stimulateActivityService.getById(stimulateActivityId);
            if(stimulateActivity != null){
                detailBean.setStimulateActivity(stimulateActivity);

                //服务器图片路径(旧规则)
                String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);
                List <FileInfo> fileInfoList = fileInfoService.getSingleFileInfoId(stimulateActivityId.longValue(), 3,1);
                if (fileInfoList!=null && fileInfoList.size()>0){
                    for (FileInfo fileInfo : fileInfoList) {
                        fileInfo.setFilePath(tytServerPictureUrlOld + fileInfo.getFilePath());
                        fileInfo.setSmallFilePath(tytServerPictureUrlOld + fileInfo.getSmallFilePath());
                    }
                }
                detailBean.setFileInfoList(fileInfoList);

                //运单信息\货源信息\接单信息
                String tsOrderNo = stimulateActivity.getTsOrderNo();
                TytTransportWaybill transportWaybill = waybillService.getWaybillDetails(tsOrderNo);
                detailBean.setTransportWaybill(transportWaybill);

                //货源信息(出发地、目的地经纬度信息)
                TransportMain transportMain = transportMainService.getTransportMainForId(transportWaybill.getTsId());
                detailBean.setTransportMain(transportMain);

                //获取车辆位置信息
                List<CarLocation> carLocationList = new ArrayList <>();
                if(queryType == 1){ //详情
                    //车辆轨迹信息
                    TytStimulateCarLocation carLocation = stimulateCarLocationService.getCarLocation(stimulateActivityId);
                    detailBean.setCarLocation(carLocation);

                    if(carLocation != null){
                        headCity = carLocation.getHeadCity();
                        headNo = carLocation.getHeadNo();
                        beginTime = carLocation.getBeginTime();
                        endTime = carLocation.getEndTime();
                        if (beginTime !=null && endTime != null ){
                            carLocationList = CarLocationUtils.getCarLocation(headCity, headNo, beginTime, endTime);
                            detailBean.setCarLocationList(carLocationList);
                        }
                    }
                }else if(queryType == 2){ //车辆轨迹
                    if(StringUtils.isBlank(headCity) || StringUtils.isBlank(headNo)){
                        resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                        resultMsgBean.setMsg("请完整填写车头车牌号！");
                        return resultMsgBean;
                    }
                    if(StringUtils.isBlank(tailCity) || StringUtils.isBlank(tailNo)){
                        resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                        resultMsgBean.setMsg("请完整填写挂车车牌号！");
                        return resultMsgBean;
                    }
                    if(beginTime == null){
                        resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                        resultMsgBean.setMsg("请选择开始时间！");
                        return resultMsgBean;
                    }
                    if(endTime == null){
                        resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                        resultMsgBean.setMsg("请选择结束时间！");
                        return resultMsgBean;
                    }
                    if(endTime.before(beginTime)){
                        resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                        resultMsgBean.setMsg("开始时间需小于结束时间！");
                        return resultMsgBean;
                    }

                    carLocationList = CarLocationUtils.getCarLocation(headCity, headNo, beginTime, endTime);
                    detailBean.setCarLocationList(carLocationList);
                    if(carLocationList != null && carLocationList.size() > 0){
                        TytStimulateCarLocation carLocation = new TytStimulateCarLocation();
                        carLocation.setStimulateActivityId(stimulateActivityId);
                        carLocation.setHeadCity(headCity);
                        carLocation.setHeadNo(headNo);
                        carLocation.setTailCity(tailCity);
                        carLocation.setTailNo(tailNo);
                        carLocation.setBeginTime(beginTime);
                        carLocation.setEndTime(endTime);
                        carLocation.setCtime(new Date());
                        stimulateCarLocationService.addSave(carLocation);
                        detailBean.setCarLocation(carLocation);
                    }
                }
            }
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("查询刷单记录详情信息成功！");
            resultMsgBean.setData(detailBean);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("查询刷单记录详情信息发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }

    /**
     * @description 查询车辆轨迹接口
     * <AUTHOR>
     * @date 2023/03/16 11:40
     * @param tsOrderNo 运单号
     * @param headCity 车头牌照头字母
     * @param headNo 车头牌照号码
     * @param tailCity 挂车牌照头字母
     * @param tailNo 挂车牌照号码
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = "getCarLocationDetail", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getCarLocationDetail(String tsOrderNo,
                                              String headCity, String headNo,
                                              String tailCity, String tailNo,
                                              Date beginTime, Date endTime) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        try {
            if(StringUtils.isBlank(tsOrderNo)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("运单号不能为空！");
                return resultMsgBean;
            }
            if(StringUtils.isBlank(headCity) || StringUtils.isBlank(headNo)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("请完整填写车头车牌号！");
                return resultMsgBean;
            }
            if(StringUtils.isBlank(tailCity) || StringUtils.isBlank(tailNo)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("请完整填写挂车车牌号！");
                return resultMsgBean;
            }
            if(beginTime == null){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("请选择开始时间！");
                return resultMsgBean;
            }
            if(endTime == null){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("请选择结束时间！");
                return resultMsgBean;
            }
            if(endTime.before(beginTime)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("开始时间需小于结束时间！");
                return resultMsgBean;
            }
            //刷单记录详情对象
            StimulateActivityDetailBean detailBean = new StimulateActivityDetailBean();

            //运单信息\货源信息\接单信息
            TytTransportWaybill transportWaybill = waybillService.getWaybillDetails(tsOrderNo);
            detailBean.setTransportWaybill(transportWaybill);

            //货源信息(出发地、目的地经纬度信息)
            TransportMain transportMain = transportMainService.getTransportMainForId(transportWaybill.getTsId());
            detailBean.setTransportMain(transportMain);

            //获取车辆位置信息
            List<CarLocation> carLocationList = CarLocationUtils.getCarLocation(headCity, headNo, beginTime, endTime);
            detailBean.setCarLocationList(carLocationList);
            if(carLocationList != null && carLocationList.size() > 0){
                //平均速度，单位：km/h
                OptionalDouble avgSpdOpt = carLocationList.stream()
                        .filter(carLocation -> carLocation.getSpd() > 0)
                        .mapToDouble(CarLocation::getSpd)
                        .average();

                BigDecimal avgSpd = avgSpdOpt.isPresent()
                        ? BigDecimal.valueOf(avgSpdOpt.getAsDouble()).movePointLeft(1)
                        : BigDecimal.ZERO;

                //行程开始位置
                CarLocation startLocation = carLocationList.get(0);
                //行程结束位置
                CarLocation endLocation = carLocationList.get(carLocationList.size()-1);
                //行驶里程数，单位：km
                BigDecimal totalMlg = null;
                if(endLocation.getMlg()!=null && startLocation.getMlg()!=null){
                    totalMlg = BigDecimal.valueOf(endLocation.getMlg() - startLocation.getMlg());
                }
                
                TytStimulateCarLocation carLocation = new TytStimulateCarLocation();
                carLocation.setStimulateActivityId(0);
                carLocation.setHeadCity(headCity);
                carLocation.setHeadNo(headNo);
                carLocation.setTailCity(tailCity);
                carLocation.setTailNo(tailNo);
                carLocation.setBeginTime(beginTime);
                carLocation.setEndTime(endTime);
                carLocation.setCtime(new Date());
                carLocation.setAvgSpd(avgSpd);
                carLocation.setTotalMlg(totalMlg);
                stimulateCarLocationService.addSave(carLocation);
                detailBean.setCarLocation(carLocation);
            }

            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("查询车辆轨迹信息成功！");
            resultMsgBean.setData(detailBean);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("查询车辆轨迹信息发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }


    /**
     * 轨迹查询默认时间
     */

    @RequestMapping("/getCarListTime")
    public ResultMsgBean getCarListTime(Long id){
        ResultMsgBean rm = new ResultMsgBean();
        if (id == null){
            rm.setCode(ResultMsgBean.ERROR);
            rm.setMsg("缺少必要的参数");
        }
        TytStimulateActivityEntity stimulateActivity = stimulateActivityService.getById(id.intValue());
        if (null != stimulateActivity && stimulateActivity.getOrderNumber() != null){
            TytTransportOrders orders = transportOrdersService.getById(stimulateActivity.getOrderNumber());
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(orders);
            return rm;
        }
        rm.setCode(ResultMsgBean.ERROR);
        rm.setMsg("失败");
        return rm;
    }
}
