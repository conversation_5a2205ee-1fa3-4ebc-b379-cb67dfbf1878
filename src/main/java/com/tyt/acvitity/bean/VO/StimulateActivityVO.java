package com.tyt.acvitity.bean.VO;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-02-16 09:33:24
 */
@Data
public class StimulateActivityVO{
	/**
	 *  运营活动表id
	 */
	private Integer id;
	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 活动类型
	 */
	private Integer activityType;

	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 用户账户
	 */
	private String userAccount;
	/**
	 * 订单状态 0 运输中 1 已完成
	 */
	private Integer orderStatus;
	/**
	 * 订单编号
	 */
	private Long orderNumber;
	/**
	 * 活动状态 0:正常 1：刷单嫌疑 2：已确认刷单 3：已解除刷单
	 */
	private Integer activityStatus;
	/**
	 * 权益
	 */
	private String benefitGift;
	/**
	 * 操作人
	 */
	private String updateUserName;
	/**
	 * 0:接单人与发货人为同一认证人 1:接单人与发货人认证同一企业 2:接单人与发货人认证同一辆车 3:1人同一天，接3单及以上，接单的车辆为同一车牌号 4:发货时间与接单时间间隔小于1分钟且确认付款时间间隔小于5分钟
	 */
	private String suspectType;
	/**
	 * 赠送商品支付订单号
	 */
	private String tsOrderNo;

	/**
	 * 活动 id
	 */
	private Long activityId;

	/**
	 * 刷单次数
	 */
	private Integer brushesCount;
}
