package com.tyt.acvitity.dao.impl;

import com.tyt.acvitity.dao.TransportOrdersRiskDao;
import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytStimulateActivityEntity;
import com.tyt.model.TytTransportOrdersRisk;
import org.springframework.stereotype.Repository;

@Repository("transportOrdersRiskDao")
public class TransportOrdersRiskDaoImpl extends BaseDaoImpl<TytTransportOrdersRisk, Integer> implements TransportOrdersRiskDao {

    public TransportOrdersRiskDaoImpl() {
        this.setEntityClass(TytTransportOrdersRisk.class);
    }
}
