package com.tyt.scar.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytScarJob;
import com.tyt.scar.bean.ScarJobBean;
import com.tyt.scar.bean.ScarJobCollectionQueryBean;
import com.tyt.scar.bean.ScarJobQueryBean;
import com.tyt.scar.service.ScarJobService;
import com.tyt.util.Constant;
import com.tyt.web.base.BaseController;

/**
 * 设备
 * 
 * <AUTHOR>
 * @date 2016年4月12日下午6:00:57
 * 
 */
@Controller
@RequestMapping("/manage/scar/job")
public class ScarJobController extends BaseController {

	@Resource(name = "sCarJobService")
	ScarJobService sCarJobService;
	
	
	
	@RequestMapping("/list")
	public String getList(ScarJobBean sCar,Integer pageNo,Integer pageSize,HttpServletRequest request,
			HttpServletResponse response){
		
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser==null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			//分页设置
			if (pageNo == null || pageNo.intValue() <= 0)
				pageNo = 1;
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(pageNo);
			if (pageSize == null || pageSize.intValue() <= 0)
				pageSize = Constant.DEFAULT_PAGE_SIZE;
			pageBean.setPageSize(pageSize);
			
			List<ScarJobQueryBean> sCarList=sCarJobService.query(sCar,pageBean);
			//遍历循环 将phone设置为null
			for (ScarJobQueryBean scarJob:sCarList){
				scarJob.setCellPhone(null);
				scarJob.setTelephone(null);
			}
			request.setAttribute("sCarList", sCarList);
			request.setAttribute("scar", sCar);
			request.setAttribute("pageNo", pageNo);
			request.setAttribute("pageSize", pageSize);
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("maxPage", pageBean.getMaxPage());
			return "back/scar/jsp/job";
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return "back/scar/jsp/job";
		}
	}
	
	@RequestMapping("/getById")
	public String getById(Long id,HttpServletRequest request,
			HttpServletResponse response){
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser==null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			
			if(id.longValue()<=0l){
				request.setAttribute("errorMsg", "数据错误！");
				return "back/scar/jsp/job_edit";
			}
			TytScarJob sCar=sCarJobService.getById(id);
			request.setAttribute("scar", sCar);
			return "back/scar/jsp/job_edit";
		} catch (Exception e) {
			e.printStackTrace();
			request.setAttribute("errorMsg", e);
			return "back/scar/jsp/job_edit";
		}
	}
	
	@RequestMapping("/update")
	@ResponseBody
	public ResultMsgBean updateJob(TytScarJob sCar,HttpServletRequest request,
			HttpServletResponse response){
		ResultMsgBean result=new ResultMsgBean();
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser==null) {
				result.setCode(100);
				result.setMsg("请重新登陆!");
				response.sendRedirect(request.getContextPath()+"/back/jsp/admin_login.jsp");
				return result;
			}
			if(sCar.getId()==null||sCar.getId().longValue()<=0l){
				result.setCode(300);
				result.setMsg("非法信息编辑!");
				return result;
			}
			// 老数据
			TytScarJob sCarOld = sCarJobService.getById(sCar.getId());
			if (sCarOld == null) {
				result.setCode(300);
				result.setMsg("非法信息编辑!");
				return result;
			}
			int resultCode=sCarJobService.updateBcarJob(sCarOld, sCar);
			//获取结果
			if(resultCode==200){
				result.setCode(200);
				result.setMsg("修改成功,将关闭当前页面!");
			}else if(resultCode==400){
				result.setCode(400);
				result.setMsg("该条信息已经存在,是否关闭当前页面？");
			}else if(resultCode==600){
				result.setCode(600);
				result.setMsg("该条信息没做任何改变,是否关闭当前页面？");
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg(e.toString());
			return result;
		}
	}


	@RequestMapping("/getCollectionById")
	public String getCollectionById(Long id, Integer pageNo, Integer pageSize, HttpServletRequest request, HttpServletResponse response) {
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}

			// 分页设置
			if (pageNo == null || pageNo.intValue() <= 0)
				pageNo = 1;
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(pageNo);
			if (pageSize == null || pageSize.intValue() <= 0)
				pageSize = Constant.DEFAULT_PAGE_SIZE;
			pageBean.setPageSize(pageSize);


			if (id.longValue() <= 0l) {
				request.setAttribute("errorMsg", "数据错误！");
				return "back/scar/jsp/job_collection";
			}
			List<ScarJobCollectionQueryBean> recruitCollectionList = sCarJobService.getCollectionById(id,pageBean);
			request.setAttribute("recruitCollectionList", recruitCollectionList);
			request.setAttribute("pageNo", pageNo);
			request.setAttribute("pageSize", pageSize);
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("maxPage", pageBean.getMaxPage());
			return "back/scar/jsp/job_collection";
		} catch (Exception e) {
			e.printStackTrace();
			request.setAttribute("errorMsg", e);
			return "back/scar/jsp/job_collection";
		}
	}


}
