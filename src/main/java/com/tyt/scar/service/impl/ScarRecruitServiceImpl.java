package com.tyt.scar.service.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.common.service.TytSequenceService;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.PageBean;
import com.tyt.model.TytScarRecruit;
import com.tyt.model.TytSource;
import com.tyt.scar.bean.ScarRecruitBean;
import com.tyt.scar.bean.ScarRecruitCollectionQueryBean;
import com.tyt.scar.bean.ScarRecruitQueryBean;
import com.tyt.scar.service.ScarRecruitService;
import com.tyt.scar.util.ScarUtil;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import com.tyt.util.TytSourceUtil;

@Service("sCarRecruitService")
public class ScarRecruitServiceImpl extends
		BaseServiceImpl<TytScarRecruit, Long> implements ScarRecruitService {

	@Resource(name = "tytSequenceService")
	TytSequenceService tytSequenceService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "sCarRecruitDao")
	@Override
	public void setBaseDao(BaseDao<TytScarRecruit, Long> sCarRecruitDao) {
		super.setBaseDao(sCarRecruitDao);
	}

	@Override
	public List<ScarRecruitQueryBean> query(ScarRecruitBean sCar,
											PageBean page) throws Exception {
		StringBuffer countSQL = new StringBuffer(
				"select count(*) from tyt_scar_recruit where 1=1 ");

		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer();
		if (null != sCar.getProvince() && !"".equals(sCar.getProvince().trim())) {
			sb.append(" and province=? ");
			list.add(sCar.getProvince().trim());
		}
		if (null != sCar.getCity() && !"".equals(sCar.getCity().trim())) {
			sb.append(" and city=? ");
			list.add(sCar.getCity().trim());
		}
		if (null != sCar.getCounty() && !"".equals(sCar.getCounty().trim())) {
			sb.append(" and county=? ");
			list.add(sCar.getCounty().trim());
		}
		if (null != sCar.getTitle() && !"".equals(sCar.getTitle())) {
			sb.append(" and title=? ");
			list.add(sCar.getTitle().trim());
		}
		if (null != sCar.getTelName() && !"".equals(sCar.getTelName().trim())) {
			sb.append(" and tel_name=? ");
			list.add(sCar.getTelName().trim());
		}
		if (null != sCar.getTelephone()
				&& !"".equals(sCar.getTelephone().trim())) {
			sb.append(" and telephone=? ");
			list.add(sCar.getTelephone().trim());
		}
		if (null != sCar.getCellPhone()
				&& !"".equals(sCar.getCellPhone().trim())) {
			//通过手机号查询时改为该手机号对应的用户id查询
			Long sCarUserId = userService.getIdByCellPhone(sCar.getCellPhone().trim());
			if(sCarUserId != null){
				sb.append(" and user_id=? ");
				list.add(sCarUserId);
			}else {
				return null;
			}
		}

		if (StringUtil.isNumeric(sCar.getDeviceTypeOneCode())) {
			sb.append(" and device_type_one_code=? ");
			list.add(sCar.getDeviceTypeOneCode());
		}
		if (StringUtil.isNumeric(sCar.getDeviceTypeTwoCode())) {
			sb.append(" and device_type_two_code=? ");
			list.add(sCar.getDeviceTypeTwoCode());
		}
		if (StringUtil.isNumeric(sCar.getFormatCode())) {
			sb.append(" and format_code=? ");
			list.add(sCar.getFormatCode());
		}

		if (StringUtil.isNumeric(sCar.getSalaryCode())) {
			sb.append(" and salary_code=? ");
			list.add(sCar.getSalaryCode());
		}
		if (StringUtil.isNumeric(sCar.getCarTypeCode())) {
			sb.append(" and car_type_code=? ");
			list.add(sCar.getCarTypeCode());
		}
		if (StringUtil.isNumeric(sCar.getStatus())) {
			sb.append(" and status=? ");
			list.add(sCar.getStatus());
		}
		if (StringUtil.isNumeric(sCar.getId())) {
			sb.append(" and id=? ");
			list.add(sCar.getId());
		}
		if (StringUtil.isNumeric(sCar.getReadNbr())) {
			sb.append(" and read_nbr=? ");
			list.add(sCar.getReadNbr());
		}
		if (sCar.getPublishTimeStart() != null) {
			sb.append(" and publish_time>=? ");
			list.add(sCar.getPublishTimeStart());
		}
		if (sCar.getPublishTimeEnd() != null) {
			sb.append(" and publish_time<=? ");
			list.add(sCar.getPublishTimeEnd());
		}
		if (null != sCar.getDisplayStatus()) {
			sb.append(" and display_status=? ");
			list.add(sCar.getDisplayStatus());
		}
		if (null != sCar.getDelStatus()) {
			sb.append(" and del_status=? ");
			list.add(sCar.getDelStatus());
		}
		BigInteger count = this.getBaseDao().query(
				countSQL.append(sb).toString(), list.toArray());
		if (count.longValue() > 0l) {
			page.setRowCount(count.longValue());
			StringBuffer selectSQL = new StringBuffer(
					" SELECT  id,title,"
							+ "salary,"
							+ "device_type_one deviceTypeOne,"
							+ "device_type_two deviceTypeTwo,format,"
							+ "province,city,county,"
							+ "publish_time publishTime,cell_phone cellPhone,telephone,status,"
							+ "read_nbr readNbr,remark,tel_name telName ,(SELECT COUNT(1) FROM tyt_collect_info WHERE ms_id=tyt_scar_recruit.`id` AND `status`=0 AND `type`=5) AS collectionNbr ,"
							+ "display_status displayStatus ,del_status delStatus "
							+ "FROM tyt_scar_recruit " + " WHERE 1=1 ");
			// 查询数据集

			Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
			scalarMap.put("id", Hibernate.LONG);
			scalarMap.put("title", Hibernate.STRING);
			scalarMap.put("salary", Hibernate.STRING);
			scalarMap.put("deviceTypeOne", Hibernate.STRING);
			scalarMap.put("deviceTypeTwo", Hibernate.STRING);
			scalarMap.put("format", Hibernate.STRING);
			scalarMap.put("province", Hibernate.STRING);
			scalarMap.put("city", Hibernate.STRING);
			scalarMap.put("county", Hibernate.STRING);
			scalarMap.put("publishTime", Hibernate.TIMESTAMP);
			scalarMap.put("telephone", Hibernate.STRING);
			scalarMap.put("cellPhone", Hibernate.STRING);
			scalarMap.put("telName", Hibernate.STRING);
			scalarMap.put("readNbr", Hibernate.INTEGER);
			scalarMap.put("status", Hibernate.INTEGER);
			scalarMap.put("remark", Hibernate.STRING);
			scalarMap.put("collectionNbr", Hibernate.INTEGER);
			scalarMap.put("displayStatus", Hibernate.INTEGER);
			scalarMap.put("delStatus", Hibernate.INTEGER);
			selectSQL.append(sb).append(" order by id desc");
			List<ScarRecruitQueryBean> queryList = this.getBaseDao().search(
					selectSQL.toString(), scalarMap,
					ScarRecruitQueryBean.class, list.toArray(),
					page.getCurrentPage(), page.getPageSize());
			return queryList;
		} else {
			return null;
		}
	}

	@Override
	public int updateBcarRecruit(TytScarRecruit sCarOld, TytScarRecruit sCar)
			throws Exception {
		// 生成md5
		String sCarMd5 = ScarUtil.createMd5(null, sCar.getCity(),
				sCar.getCounty(), sCar.getDeviceTypeOneCode(),
				sCar.getDeviceTypeTwoCode(), sCar.getFormatCode(),
				sCar.getProvince(), sCar.getRemark(), sCar.getUserId(),
				sCar.getSalaryCode(), sCar.getTelephone(), sCar.getTelName(),
				sCar.getTitle(), null);
		//有没有被修改？
		if (sCarMd5.equals(sCarOld.getMd5())
				&& sCarOld.getStatus().intValue() == sCar.getStatus().intValue()
				&& sCarOld.getDisplayStatus().intValue() == sCar.getDisplayStatus().intValue()
				&& sCarOld.getDelStatus().intValue() == sCar.getDelStatus().intValue()) {
			return 600;
		}
		// 滤重
		Long has = this.getByMd5(sCarMd5, sCar.getUserId());
		if (has != null && has.longValue() == sCar.getId().longValue() || has == null) {
			sCarOld.setTitle(sCar.getTitle());
			sCarOld.setTelName(sCar.getTelName());
			sCarOld.setProvince(sCar.getProvince());
			sCarOld.setCity(sCar.getCity());
			sCarOld.setCounty(sCar.getCounty());
			sCarOld.setTelephone(sCar.getTelephone());
			sCarOld.setRemark(sCar.getRemark());
			sCarOld.setMd5(sCarMd5);
			sCarOld.setPublishTime(new Date());
			sCarOld.setUtime(new Date());
			sCarOld.setStatus(sCar.getStatus());
			sCarOld.setSortId(tytSequenceService
					.updateGetNextSequenceNbr(Constant.TABLE_SCAR_RECRUIT_NAME));
			// 一级设备
			sCarOld.setDeviceTypeOneCode(sCar.getDeviceTypeOneCode());
			TytSource deviceTypeOneCodeSource = TytSourceUtil.getSourceName(
					"deviceType", sCar.getDeviceTypeOneCode() + "");
			sCarOld.setDeviceTypeOne(deviceTypeOneCodeSource == null ? null
					: deviceTypeOneCodeSource.getName());

			// 获取二级设备
			sCarOld.setDeviceTypeTwoCode(sCar.getDeviceTypeTwoCode());
			TytSource deviceTypeTwoCodeSource = TytSourceUtil.getSourceNameByParentId(
					deviceTypeOneCodeSource.getId(),
					sCar.getDeviceTypeTwoCode() + "");
			sCarOld.setDeviceTypeTwo(deviceTypeTwoCodeSource == null ? null
					: deviceTypeTwoCodeSource.getName());
			// 获取规格
			sCarOld.setFormatCode(sCar.getFormatCode());
			TytSource formatCodeSource = TytSourceUtil.getSourceNameByParentId(
					deviceTypeTwoCodeSource.getId(), sCar.getFormatCode() + "");
			sCarOld.setFormat(formatCodeSource == null ? null
					: formatCodeSource.getName());
			// 薪资待遇
			sCarOld.setSalaryCode(sCar.getSalaryCode());
			TytSource salarySource = TytSourceUtil.getSourceName("salaryMonS",
					sCar.getSalaryCode() + "");
			sCarOld.setSalary(salarySource == null ? null : salarySource
					.getName());
			sCarOld.setDisplayStatus(sCar.getDisplayStatus());
			sCarOld.setDelStatus(sCar.getDelStatus());
			//状态、开放关闭、删除标识三个状态之间没有级联关系
//			if (sCarOld.getStatus() == 1) {
//				sCarOld.setDisplayStatus(1);
//			}
			this.update(sCarOld);
			return 200;
		} else {
			return 400;
		}
	}

	private Long getByMd5(String sCarMd5, Long userId) {
		String selectSQL = "SELECT id FROM tyt_scar_recruit where md5=? and status=? and user_id=?";
		Object[] params = new Object[]{sCarMd5, Constant.INFO_STATUS_PASS,
				userId};
		BigInteger obj = this.getBaseDao().query(selectSQL, params);
		if (obj == null) {
			return null;
		} else {
			return obj.longValue();
		}

	}

	@Override
	public List<ScarRecruitCollectionQueryBean> getCollectionById(Long id, PageBean page) {
		String countSql2 = "select count(*) from tyt_collect_info where ms_id=" + id + " and status=0 and type=5";
		List<Object> list = new ArrayList<Object>();

		BigInteger count = this.getBaseDao().query(countSql2, list.toArray());
		if (count.longValue() > 0l) {
			page.setRowCount(count.longValue());
			StringBuffer selectSQL = new StringBuffer(
					"SELECT c.id, c.ms_id msId, u.true_name trueName, u.user_name userName,"
							+ " u.cell_phone cellPhone, c.ctime from tyt_collect_info c "
							+ "left join tyt_user u on c.user_id = u.id where c.ms_id ="
							+ id + " and status=0 and type=5");
			// 查询数据集


			Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
			scalarMap.put("id", Hibernate.LONG);
			scalarMap.put("msId", Hibernate.LONG);
			scalarMap.put("trueName", Hibernate.STRING);
			scalarMap.put("userName", Hibernate.STRING);
			scalarMap.put("cellPhone", Hibernate.STRING);
			scalarMap.put("cTime", Hibernate.TIMESTAMP);
			selectSQL.append(" order by c.ctime desc");
			List<ScarRecruitCollectionQueryBean> queryList = this.getBaseDao().search(
					selectSQL.toString(), scalarMap,
					ScarRecruitCollectionQueryBean.class, list.toArray(),
					page.getCurrentPage(), page.getPageSize());
			return queryList;
		} else {
			return null;
		}
	}

}
