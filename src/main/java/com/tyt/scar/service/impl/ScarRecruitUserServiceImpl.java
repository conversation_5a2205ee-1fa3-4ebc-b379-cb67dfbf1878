package com.tyt.scar.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytScarRecruitUser;
import com.tyt.scar.service.ScarRecruitUserService;
import com.tyt.service.base.BaseServiceImpl;

@Service("sCarRecruitUserService")
public class ScarRecruitUserServiceImpl extends BaseServiceImpl<TytScarRecruitUser, Long> implements
		ScarRecruitUserService {

	@Resource(name="sCarRecruitUserDao")
	public void setBaseDao(BaseDao<TytScarRecruitUser, Long> sCarRecruitUserDao) {
		super.setBaseDao(sCarRecruitUserDao);
	}

}
