package com.tyt.feedback.label.service.impl;

import com.tyt.feedback.label.bean.FeedbackLabelLog;
import com.tyt.feedback.label.service.FeedbackLabelLogService;
import com.tyt.mybatis.mapper.FeedbackLabelLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FeedbackLabelLogServiceImpl implements FeedbackLabelLogService {

    @Autowired
    private FeedbackLabelLogMapper feedbackLabelLogMapper;

    @Override
    public int addLog(FeedbackLabelLog feedbackLabelLog) {
        if (feedbackLabelLog != null) {
            return feedbackLabelLogMapper.addLog(feedbackLabelLog);
        }
        return 0;
    }

}
