package com.tyt.feedback.userNegativeLabelConfig.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tyt.abtest.bean.PageData;
import com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigInfo;
import com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigInfoVO;
import com.tyt.feedback.userNegativeLabelConfig.bean.UserNegativeLabelConfigReq;
import com.tyt.feedback.userNegativeLabelConfig.service.UserNegativeLabelConfigService;
import com.tyt.mybatis.mapper.UserNegativeLabelConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserNegativeLabelConfigServiceImpl implements UserNegativeLabelConfigService {

    @Autowired
    private UserNegativeLabelConfigMapper userNegativeLabelConfigMapper;

    @Override
    public PageData<UserNegativeLabelConfigInfoVO> getUserNegativeLabelConfigInfoVOList(UserNegativeLabelConfigReq req) {
        req.setStartRowNum((req.getPageNum() - 1) * req.getPageSize());
        PageData<UserNegativeLabelConfigInfoVO> result = new PageData<>();

        int count = userNegativeLabelConfigMapper.getUserNegativeLabelConfigInfoVOListCount(req);
        if (count != 0) {
            List<UserNegativeLabelConfigInfo> userNegativeLabelConfigInfoVOGroupByUserIdList = userNegativeLabelConfigMapper.getUserNegativeLabelConfigInfoVOGroupByUserIdList(req);
            if (userNegativeLabelConfigInfoVOGroupByUserIdList != null && userNegativeLabelConfigInfoVOGroupByUserIdList.size() > 0) {
                List<Long> userIdList = userNegativeLabelConfigInfoVOGroupByUserIdList.stream().map(UserNegativeLabelConfigInfo::getUserId).collect(Collectors.toList());
                req.setUserIdList(userIdList);
                List<UserNegativeLabelConfigInfoVO> userNegativeLabelConfigInfoVOList = userNegativeLabelConfigMapper.getUserNegativeLabelConfigInfoVOList(req);
                for (UserNegativeLabelConfigInfoVO userNegativeLabelConfigInfoVO : userNegativeLabelConfigInfoVOList) {
                    userNegativeLabelConfigInfoVO.setUserPhone(userPhoneProtect(userNegativeLabelConfigInfoVO.getUserPhone()));
                }
                result.setList(userNegativeLabelConfigInfoVOList);
            }
        }
        if(CollectionUtil.isEmpty(result.getList())){
            result.setList(new ArrayList<>());
        }
        result.setPageNum(req.getPageNum());
        result.setPageSize(req.getPageSize());
        result.setTotal(count);
        result.setPages((count / req.getPageSize()) + (count % req.getPageSize() > 0 ? 1 : 0));
        return result;
    }

    @Override
    public UserNegativeLabelConfigInfo getUserNegativeLabelConfigInfoById(Long id) {
        return userNegativeLabelConfigMapper.getUserNegativeLabelConfigInfoById(id);
    }

    @Override
    public int updateUserNegativeLabelConfigIsShowById(UserNegativeLabelConfigInfo userNegativeLabelConfigInfo) {
        if (userNegativeLabelConfigInfo != null) {
            return userNegativeLabelConfigMapper.updateUserNegativeLabelConfigIsShowById(userNegativeLabelConfigInfo.getId(), userNegativeLabelConfigInfo.getIsShow());
        }
        return 0;
    }

    @Override
    public int updateUserNegativeLabelConfigShowFeedbackLabelIdById(UserNegativeLabelConfigInfo userNegativeLabelConfigInfo) {
        if (userNegativeLabelConfigInfo != null) {
            return userNegativeLabelConfigMapper.updateUserNegativeLabelConfigShowFeedbackLabelIdById(userNegativeLabelConfigInfo.getId(), userNegativeLabelConfigInfo.getShowFeedbackLabelId());
        }
        return 0;
    }

    @Override
    public int updateUserNegativeLabelConfigBatch(UserNegativeLabelConfigInfo userNegativeLabelConfigInfo) {
        if (userNegativeLabelConfigInfo != null) {
            return userNegativeLabelConfigMapper.updateUserNegativeLabelConfigBatch(userNegativeLabelConfigInfo.getId(), userNegativeLabelConfigInfo.getIsShow(), userNegativeLabelConfigInfo.getShowFeedbackLabelId());
        }
        return 0;
    }

    @Override
    public String getPhoneNumById(long id) {
        return userNegativeLabelConfigMapper.getPhoneNumById(id);
    }

    private String userPhoneProtect(String userPhone) {
        String result = userPhone;
        if (userPhone != null && userPhone.length() > 7) {
            String phoneNumPre = userPhone.substring(0, 3);
            String phoneNumFix = userPhone.substring(7);
            StringBuilder userPhoneAfterProtect = new StringBuilder();
            result = userPhoneAfterProtect.append(phoneNumPre).append("****").append(phoneNumFix).toString();
        }
        return result;
    }
}
