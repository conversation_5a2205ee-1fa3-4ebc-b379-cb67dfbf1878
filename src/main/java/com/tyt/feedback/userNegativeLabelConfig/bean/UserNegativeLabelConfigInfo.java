package com.tyt.feedback.userNegativeLabelConfig.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserNegativeLabelConfigInfo {

    private Long id;

    private Long userId;

    private String userPhone;

    private Long showFeedbackLabelId;

    private Integer isShow;

    private Long modifyUserId;

    private String modifyUserName;

    private Date createTime;

    private Date updateTime;

    /**
     * 用户类型 1车 2货
     */
    private Integer userType;
}
