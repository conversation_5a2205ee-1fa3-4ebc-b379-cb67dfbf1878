package com.tyt.feedback.appeal.bean;

import com.tyt.feedback.appeal.entity.FeedbackUserAppeal;
import com.tyt.feedback.appeal.entity.FeedbackUserAppealExamine;
import com.tyt.feedback.appeal.entity.FeedbackUserAppealInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class FeedbackAppealRes implements Serializable {

    FeedbackAppealListVO listFeedbackUserAppeals = new FeedbackAppealListVO();
    private transient  List<FeedbackUserAppealInfo> listFeedbackUserAppealInfos = new ArrayList<>();
    private transient List<FeedbackUserAppealExamine> listFeedbackUserAppealExamines = new ArrayList<>();

}
