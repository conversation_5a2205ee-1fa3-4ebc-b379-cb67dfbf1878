package com.tyt.feedback.appeal.constants;

public enum RejectReasonEnum {
    VARCHER_DISAGREE(1, "凭证不符"),
    VARCHER_INFO_INCOMPELETE(2, "凭证信息不全"),
    VARCHER_INFO_VAGUE(3, "凭证信息模糊"),
    VARCHER_NOT_AGREE(4, "凭证显示不完整"),
    PLEASE_CALLER(5, "致电客服了解驳回原因");

    private Integer code;
    private String msg;

    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    private RejectReasonEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}

