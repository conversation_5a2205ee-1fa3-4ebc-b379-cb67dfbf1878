package com.tyt.usedCarBrand.dao.impl;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytUsedCarBrand;
import com.tyt.usedCarBrand.dao.TytUsedCarBrandDao;
import org.springframework.stereotype.Repository;

/**
 * @Description  二手车品牌数据层实现类
 * <AUTHOR>
 * @Date  2020/1/21 11:55
 * @Param
 * @return
 **/
@Repository("tytUsedCarBrandDao")
public class TytUsedCarBrandDaoImpl extends BaseDaoImpl<TytUsedCarBrand,Long> implements TytUsedCarBrandDao {
    public TytUsedCarBrandDaoImpl() {
        this.setEntityClass(TytUsedCarBrand.class);
    }
}