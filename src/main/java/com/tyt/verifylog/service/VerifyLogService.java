package com.tyt.verifylog.service;

public interface VerifyLogService {

	/**
	 *  发送短信/语音验证码
	 * @param userId 用户ID 可以为空
	 * @param phone  电话
	 * @param verifyCode 验证码 
	 * @param type  0短信1语音
	 * @return boolean
	 */
	boolean saveSendVerifyCode(String userId,String phone,String verifyCode,String type);

	/**
	 * 验证短信/语音验证码是否成功
	 * @param phone phone
	 * @param verifyCode verifyCode
	 * @return true 0成功，1失败
	 */ 
	boolean verify(String phone,String verifyCode);

}
