package com.tyt.coupon.bean;

/**
 * <AUTHOR>
 * @since 2023/11/23 16:14
 */
public class CouponConstant {
    /**
     * 代金券状态 1 有效 2 无效
     */
    public static final Integer COUPON_STATUS_EFFECTIVE = 1;
    public static final Integer COUPON_STATUS_INVALID = 2;

    /**
     * 优惠券数量 默认1000万
     */
    public static final Integer COUPON_QTY_DEFAULT=10000000;

    /**
     * 优惠券类型 1：代金券 2：满减券
     */
    public static final Integer COUPON_TYPE_VOUCHER = 1;
    public static final Integer COUPON_TYPE_FULL_REDUCTION  = 2;

    /**
     * 使用范围 1 购买会员 2 购买保险 3支付信息费  4车会员 5货会员 6车货组合会员 7发货此卡 8所有商品 9.广告运营类商品
     */
    public static final Integer USER_SCOPE_TYPE_MEMBER = 8;
    public static final Integer USER_SCOPE_TYPE_INFO_FEE= 3;

    /**
     * 发放类型 1：单券 2：礼包
     */
    public static final Integer  ACTIVITY_GRANT_TYPE_COUPON = 1;
    public static final Integer  ACTIVITY_GRANT_TYPE_GIFT = 2;


    /**
     * 优惠券活动结束时间
     */
    public static final String  ACTIVITY_DATE_END_VALUE = "2050-12-31 23:59:59";

    /**
     *优惠券链接跳转类型 local 本地
     */
    public static final String  LINK_TYPE_LOCAL = "local";

    /**
     * 优惠券链接跳转  会员 carVip
     */
    public static final String  LINK_TARGET_MEMBER="carVip";
    public static final String  LINK_TARGET_GOOD="callFindGoodHall";


    /**
     * 优惠券使用状态（promo_user_coupon中coupon_status） 1 未使用 2已使用 3无效 4已过期
     */
    public static final Integer  USER_COUPON_STATUS_NOT_USED = 1;
    public static final Integer  USER_COUPON_STATUS_USED  = 2;
    public static final Integer  USER_COUPON_STATUS_INVALID  = 3;
    public static final Integer  USER_COUPON_STATUS_EXPIRE  = 4;



}
