package com.tyt.coupon.service.impl;

import com.tyt.coupon.bean.CouponConstant;
import com.tyt.coupon.bean.CouponQueryBean;
import com.tyt.coupon.bean.CouponResponseBean;
import com.tyt.coupon.service.PromoCouponService;
import com.tyt.dao.base.BaseDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.PromoCoupon;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("promoCouponService")
public class PromoCouponServiceImpl extends BaseServiceImpl<PromoCoupon,Integer> implements PromoCouponService {



    @Resource(name = "promoCouponDao")
    public void setBaseDao(BaseDao<PromoCoupon, Integer> promoCouponDao) {
        super.setBaseDao(promoCouponDao);
    }

    @Override
    public List<CouponResponseBean> getCouponList(CouponQueryBean queryBean, PageBean pageBean) {
        Map<String,Object> paramMap = new HashMap<String,Object>();
        StringBuffer conditionSQL=new StringBuffer(" where 1=1 ");
        if(queryBean.getCouponTypeId()!=null && queryBean.getCouponTypeId()>0){
            conditionSQL.append(" AND cou.coupon_type_id=:couponTypeId");
            paramMap.put("couponTypeId", queryBean.getCouponTypeId());
        }
        if(StringUtils.isNotBlank(queryBean.getCouponName())){
            conditionSQL.append(" AND cou.coupon_name like :couponName");
            paramMap.put("couponName","%"+ queryBean.getCouponName()+"%");
        }
        if(queryBean.getCouponType()!=null&& queryBean.getCouponType()>0){
            conditionSQL.append(" AND cou.coupon_type=:couponType");
            paramMap.put("couponType",queryBean.getCouponType());
        }
        if(queryBean.getUseType()!=null){
            conditionSQL.append(" AND cou.use_type=:useType");
            paramMap.put("useType",queryBean.getUseType());
        }
        if(queryBean.getUseGoodsMark()!=null){
            conditionSQL.append(" AND cou.use_goods_mark=:useGoodsMark");
            paramMap.put("useGoodsMark",queryBean.getUseGoodsMark());
        }
        if (queryBean.getMinCouponAmount()!=null) {
            conditionSQL.append("  AND cou.coupon_amount >=:minCouponAmount");
            paramMap.put("minCouponAmount", queryBean.getMinCouponAmount());
        }
        if (queryBean.getMaxCouponAmount()!=null) {
            conditionSQL.append("  AND cou.coupon_amount<=:maxCouponAmount");
            paramMap.put("maxCouponAmount", queryBean.getMaxCouponAmount());
        }
        if (queryBean.getUseScopeType()!=null&& queryBean.getUseScopeType()>0) {
            conditionSQL.append("  AND cou.use_scope_type =:useScopeType");
            paramMap.put("useScopeType", queryBean.getUseScopeType());
        }
        if (StringUtils.isNotBlank(queryBean.getUseScopeDetail())) {
            conditionSQL.append("  AND cou.use_scope_detail like :useScopeDetail");
            paramMap.put("useScopeDetail", "%"+ queryBean.getUseScopeDetail()+"%");
        }
        if(queryBean.getValidType()!=null && queryBean.getValidType()>0){
            conditionSQL.append(" AND cou.valid_type=:validType");
            paramMap.put("validType", queryBean.getValidType());
        }
        if (StringUtils.isNotBlank(queryBean.getOperateName())) {
            conditionSQL.append("  AND cou.operate_name like:operateName");
            paramMap.put("operateName", "%"+ queryBean.getOperateName()+"%");
        }
        if (StringUtils.isNotBlank(queryBean.getCreateUserName())) {
            conditionSQL.append("  AND cou.create_user_name like:createUserName");
            paramMap.put("createUserName",  "%"+ queryBean.getCreateUserName()+"%");
        }
        if(queryBean.getCouponStatus()!=null && queryBean.getCouponStatus()>0){
            conditionSQL.append(" AND cou.coupon_status=:couponStatus");
            paramMap.put("couponStatus", queryBean.getCouponStatus());
        }
        StringBuffer countSQL = new StringBuffer("SELECT COUNT(*) FROM promo_coupon cou ");
        countSQL.append(conditionSQL);
        BigInteger rowCount = this.getBaseDao().queryByMap(countSQL.toString(), paramMap);
        if(rowCount==null||rowCount.longValue()<=0l){
            return null;
        }
        pageBean.setRowCount(rowCount.longValue());
        StringBuffer querySQL = new StringBuffer("SELECT cou.`coupon_type_id` couponTypeId, " +
                "cou.`coupon_name` couponName,cou.`coupon_type` couponType, cou.`use_type` useType, cou.`use_goods_mark` useGoodsMark," +
                "cou.`use_condition` useCondition, cou.`coupon_amount` couponAmount, cou.`coupon_desc` couponDesc,cou.`use_scope_type` useScopeType, cou.`use_scope_detail` useScopeDetail, cou.`valid_type` validType, " +
                "cou.`valid_days` validDays,cou.`valid_date_begin` validDateBegin,cou.`valid_date_end` validDateEnd,cou.`create_user_name` createUserName, cou.`coupon_status` couponStatus, " +
                "cou.`coupon_qty` couponQty,cou.`remain_qty` remainQty,(cou.`coupon_qty`-cou.`remain_qty`) assignedcount, cou.`operate_name` operateName, cou.`ctime` ctime, cou.`mtime` mtime FROM `promo_coupon` cou");
        querySQL.append(conditionSQL);
        querySQL.append(" ORDER BY cou.ctime DESC ");
        Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
        map.put("couponTypeId", Hibernate.INTEGER);
        map.put("couponName", Hibernate.STRING);
        map.put("couponType", Hibernate.INTEGER);
        map.put("useType", Hibernate.INTEGER);
        map.put("useGoodsMark", Hibernate.INTEGER);
        map.put("useCondition", Hibernate.INTEGER);
        map.put("couponAmount", Hibernate.BIG_DECIMAL);
        map.put("couponDesc", Hibernate.STRING);
        map.put("useScopeType", Hibernate.INTEGER);
        map.put("useScopeDetail", Hibernate.STRING);
        map.put("validType", Hibernate.INTEGER);
        map.put("validDays", Hibernate.INTEGER);
        map.put("validDateBegin", Hibernate.TIMESTAMP);
        map.put("validDateEnd", Hibernate.TIMESTAMP);
        map.put("createUserName", Hibernate.STRING);
        map.put("couponStatus", Hibernate.INTEGER);
        map.put("couponQty", Hibernate.INTEGER);//代金券数量
        map.put("remainQty", Hibernate.INTEGER);//剩余代金券数量
        map.put("assignedCount", Hibernate.INTEGER);//发放的代金券数量
        map.put("operateName", Hibernate.STRING);
        map.put("ctime", Hibernate.TIMESTAMP);
        map.put("mtime", Hibernate.TIMESTAMP);

        return this.getBaseDao().searchByName(querySQL.toString(), map, CouponResponseBean.class, paramMap, pageBean.getCurrentPage(), pageBean.getPageSize());
    }

    @Override
    public List<PromoCoupon> getCouponListByParams(String couponName,Integer couponTypeId) {
        StringBuffer querySQL = new StringBuffer("SELECT cou.`coupon_type_id` couponTypeId, " +
                "cou.`coupon_name` couponName,cou.`coupon_type` couponType, cou.`use_type` useType, cou.`use_goods_mark` useGoodsMark," +
                "cou.`use_condition` useCondition, cou.`coupon_amount` couponAmount, cou.`coupon_desc` couponDesc,cou.`use_scope_type` useScopeType, cou.`use_scope_detail` useScopeDetail, cou.`valid_type` validType, " +
                "cou.`valid_days` validDays,cou.`valid_date_begin` validDateBegin,cou.`valid_date_end` validDateEnd FROM `promo_coupon` cou  where cou.coupon_status=1 ");
        Map<String,Object> paramMap = new HashMap<String,Object>();
        if(StringUtils.isNotBlank(couponName)){
            querySQL.append(" AND cou.coupon_name like :couponName");
            paramMap.put("couponName","%"+couponName+"%");
        }
        if(couponTypeId!=null&& couponTypeId>0){
            querySQL.append(" AND cou.coupon_type_id=:couponTypeId");
            paramMap.put("couponTypeId",couponTypeId);
        }
        querySQL.append(" ORDER BY cou.ctime DESC ");
        Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
        map.put("couponTypeId", Hibernate.INTEGER);
        map.put("couponName", Hibernate.STRING);
        map.put("couponType", Hibernate.INTEGER);
        map.put("useType", Hibernate.INTEGER);
        map.put("useGoodsMark", Hibernate.INTEGER);
        map.put("useCondition", Hibernate.INTEGER);
        map.put("couponAmount", Hibernate.BIG_DECIMAL);
        map.put("couponDesc", Hibernate.STRING);
        map.put("useScopeType", Hibernate.INTEGER);
        map.put("useScopeDetail", Hibernate.STRING);
        map.put("validType", Hibernate.INTEGER);
        map.put("validDays", Hibernate.INTEGER);
        map.put("validDateBegin", Hibernate.TIMESTAMP);
        map.put("validDateEnd", Hibernate.TIMESTAMP);
        return this.getBaseDao().searchByName(querySQL.toString(), map, PromoCoupon.class, paramMap);
    }

    @Override
    public void saveCoupon(PromoCoupon coupon,EmployeeQueryBean curUser) {
        coupon.setCouponQty(coupon.getCouponQty()==null?0:coupon.getCouponQty());
        coupon.setRemainQty(coupon.getCouponQty()==null?0:coupon.getCouponQty());
        coupon.setWalletType(coupon.getWalletType());
        coupon.setCouponStatus(CouponConstant.COUPON_STATUS_EFFECTIVE);
        coupon.setCouponDesc(coupon.getCouponDesc()==null?"":coupon.getCouponDesc());
        coupon.setUseScopeDetail(coupon.getUseScopeDetail()==null?"":coupon.getUseScopeDetail());
        coupon.setValidDays(coupon.getValidDays()==null?0:coupon.getValidDays());
        coupon.setTopicType(coupon.getTopicType()==null?0:coupon.getTopicType());
        coupon.setCreateUserId(curUser.getId());
        coupon.setCreateUserName(curUser.getUserName());
        coupon.setLinkType(CouponConstant.LINK_TYPE_LOCAL);//默认为本地
        if(coupon.getUseScopeType()==CouponConstant.USER_SCOPE_TYPE_MEMBER){
            coupon.setLinkTarget(CouponConstant.LINK_TARGET_MEMBER); //购买会员
        }else{
            coupon.setLinkTarget(CouponConstant.LINK_TARGET_GOOD);//找货大厅
        }
        //设置开始时间结束时间
        if(null!=coupon.getValidDateBegin()){
            coupon.setValidDateBegin(TimeUtil.weeHours(coupon.getValidDateBegin(),0));
        }
        if(null!=coupon.getValidDateEnd()){
            coupon.setValidDateEnd(TimeUtil.weeHours(coupon.getValidDateEnd(),1));
        }
        coupon.setTypeName("无金额门槛");
        coupon.setCtime(new Date());
        coupon.setMtime(new Date());
        this.getBaseDao().insert(coupon);
    }

    @Override
    public void updateIncreCoupon(Integer increCount,Integer couponTypeId) {
        if (increCount !=null && increCount>0){
            String sql = "UPDATE `promo_coupon` SET `coupon_qty`= `coupon_qty`+?,`remain_qty`= `remain_qty`+?,mtime=? WHERE `coupon_type_id`=?";
            this.getBaseDao().executeUpdateSql(sql, new Object[] { increCount,increCount,new Date(), couponTypeId });
        }
    }

    @Override
    public void updateCouploseEffic(EmployeeQueryBean curUser,Integer couponTypeId) {
        //第一步 更新promo_coupon表状态为已失效
        String sql = "UPDATE `promo_coupon` SET `coupon_status`=2,operate_id=?,operate_name=?, mtime=?  WHERE `coupon_type_id`=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[] { curUser.getId(),curUser.getName(), new Date(),couponTypeId });
        //第二步 更新promo_user_coupon表中未使用状态的数据未已失效
        String sqlUser = "UPDATE `promo_user_coupon` SET `coupon_status`=3, mtime=?  WHERE `coupon_type_id`=? and coupon_status=1";
        this.getBaseDao().executeUpdateSql(sqlUser, new Object[] { new Date(), couponTypeId });
    }

    @Override
    public PromoCoupon getCouponDetail(Integer couponTypeId) {
        return this.getBaseDao().findById(couponTypeId);
    }

    @Override
    public StringBuffer getStringCsv(List<CouponResponseBean> list) {
        StringBuffer content = new StringBuffer();
//        // 标题栏
        String header = "优惠券ID,优惠券名称,优惠券类型,端限制,货源范围,券面额,使用范围,效期类型,效期,优惠券描述,优惠券状态,创建数量,发放数量,剩余可发放量,创建人,创建时间,更新人,更新时间" + "\r\n";
        content.append(header);
        if (list == null || list.size() <= 0)
            return content;
        for (CouponResponseBean bean : list) {
            StringBuffer row = new StringBuffer();
            //优惠券ID
            row.append("\t"+bean.getCouponTypeId()).append(",");
            //优惠券名称
            row.append(bean.getCouponName()).append(",");
            //优惠券类型
            String couponType="";
            if (bean.getCouponType()!= null) {
                switch (bean.getCouponType()) {
                    case 1:
                        couponType = "代金券";
                        break;
                    case 2:
                        couponType = "减免券";
                        break;
                    default:
                        couponType="";
                }
            }
            row.append(couponType).append(",");
            //端限制
            String useType="";
            if (bean.getUseType()!= null) {
                switch (bean.getUseType()) {
                    case 0:
                        useType = "通用";
                        break;
                    case 1:
                        useType = "车主端使用";
                        break;
                    case 2:
                        useType = "货主端使用";
                        break;
                    default:
                        useType="";

                }
            }
            row.append(useType).append(",");
            //货源范围 可用货源标识 (0:普通 1：优车  2:优车)
            String useGoodsMark="";
            if (bean.getUseGoodsMark()!= null) {
                switch (bean.getUseGoodsMark()) {
                    case 0:
                        useGoodsMark = "普通";
                        break;
                    case 1:
                        useGoodsMark = "优车";
                        break;
                    case 2:
                        useGoodsMark = "通用";
                        break;
                    default:
                        useGoodsMark="";
                }
            }
            row.append(useGoodsMark).append(",");
            //代金券金额
            row.append(bean.getCouponAmount() == null ? "": bean.getCouponAmount()).append(",");
            //使用范围
            String useScopeType="";
            if (bean.getUseScopeType()!= null) {
                switch (bean.getUseScopeType()) {
                    case 1:
                        useScopeType = "购买会员";
                        break;
                    case 2:
                        useScopeType = "购买保险";
                        break;
                    case 3:
                        useScopeType = "支付订金";
                        break;
                    case 8:
                        useScopeType = "所有会员";
                        break;
                    default:
                        useScopeType=bean.getUseScopeType().toString();
                }
            }
            row.append(useScopeType).append(",");
            //效期类型
            String validType="";
            if (bean.getValidType()!= null) {
                switch (bean.getValidType()) {
                    case 1:
                        validType = "领取后天数";
                        break;
                    case 2:
                        validType = "时间范围";
                        break;
                    case 3:
                        validType = "永久有效";
                        break;
                    case 4:
                        validType = "会员到期后延期n天";
                        break;
                    default:
                        validType="";
                }
            }
            row.append(validType).append(",");
            //效期
            String validDaysDetail="";
            if (bean.getValidType()!= null) {
                switch (bean.getValidType()) {
                    case 1:
                    case 4:
                        validDaysDetail = bean.getValidDays()==null?"":bean.getValidDays().toString();
                        break;
                    case 2:
                        String validDateBegin=bean.getValidDateBegin()==null?"":TimeUtil.formatDate(bean.getValidDateBegin());
                        String validDateEnd=bean.getValidDateEnd()==null?"":TimeUtil.formatDate(bean.getValidDateEnd());
                        validDaysDetail = validDateBegin+"至"+validDateEnd;
                        break;
                    case 3:
                        validDaysDetail = "-";
                        break;
                    default:
                        validDaysDetail="";
                }
            }
            row.append(validDaysDetail).append(",");
            //优惠券描述
            row.append(bean.getCouponDesc()).append(",");

            //优惠券状态
            String couponStatus="";
            if (bean.getCouponStatus()!= null) {
                switch (bean.getCouponStatus()) {
                    case 1:
                        couponStatus = "有效";
                        break;
                    case 2:
                        couponStatus = "无效";
                        break;
                    default:
                        couponStatus="";
                }
            }
            row.append(couponStatus).append(",");
            //创建数量
            row.append(bean.getCouponQty() == null ? "":bean.getCouponQty()).append(",");
            //发放数量
            row.append(bean.getAssignedCount() == null ? "":bean.getAssignedCount()).append(",");
            //剩余可发放量
            row.append(bean.getRemainQty() == null ? "":bean.getRemainQty()).append(",");
            //创建人
            row.append(bean.getCreateUserName()).append(",");
            //创建时间
            row.append(bean.getCtime()==null?"":TimeUtil.formatDateTime(bean.getCtime())).append(",");
            //更新人
            row.append(bean.getOperateName()).append(",");
            //更新时间
            row.append(bean.getMtime()==null?"":TimeUtil.formatDateTime(bean.getMtime()));
            content.append(row).append("\r\n");
        }
        return content;
    }
}
