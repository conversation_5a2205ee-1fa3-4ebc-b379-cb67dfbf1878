package com.tyt.coupon.service;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PromoActivity;
import com.tyt.service.base.BaseService;

import java.math.BigDecimal;
import java.text.ParseException;

/**
 * @Description  优惠券活动服务层
 * <AUTHOR>
 * @Date  2020/3/12 11:51
 * @Param
 * @return
 **/
public interface PromoActivityService extends BaseService <PromoActivity,Integer> {

    /**
     * @description 根据活动分组id和优惠券金额查询活动信息
     * <AUTHOR>
     * @date 2022/11/23 12:28
     * @param groupId
     * @param couponAmount
     * @return com.tyt.model.PromoActivity
     */
    PromoActivity getByGroupIdAndAmount(Integer groupId, BigDecimal couponAmount) ;

    Long savePromoActivity(String name, Long grantTypeId, String grantTypeName, EmployeeQueryBean curUser ) throws Exception ;
}
