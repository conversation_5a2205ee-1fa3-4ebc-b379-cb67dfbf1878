package com.tyt.requestlimit.dao.impl;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytSingleUserCallLimit;
import com.tyt.requestlimit.dao.SingleUserCallLimitDao;
import org.springframework.stereotype.Repository;

@Repository("singleUserCallLimitDao")
public class SingleUserCallLimitDaoImpl extends BaseDaoImpl<TytSingleUserCallLimit, Long> implements SingleUserCallLimitDao {

	public SingleUserCallLimitDaoImpl() {
		this.setEntityClass(TytSingleUserCallLimit.class);
	}
}
