package com.tyt.requestlimit.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSingleUserCallLimit;
import com.tyt.requestlimit.service.SingleUserCallLimitService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.service.config.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.web.base.BaseController;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/singleUserCalllimit")
public class SingleUserCallLimitController extends BaseController {

	@Resource(name = "singleUserCallLimitService")
	private SingleUserCallLimitService singleUserCallLimitService;

	@Resource(name = "tytConfigService")
	private TytConfigService configService;

	/**
	 * 查询列表
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping("/getList")
	public String getList(HttpServletRequest request,String userIds) {
		// 登陆验证
		EmployeeQueryBean curUser = getCurrentUser(request);
		try {
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			TytSingleUserCallLimit tytSingleUserCallLimit = new TytSingleUserCallLimit();
			if(StringUtils.isNotEmpty(userIds)){
				tytSingleUserCallLimit.setUserId(Long.valueOf(userIds));
				request.setAttribute("userIds",userIds);
			}
			List<TytSingleUserCallLimit> urlLimitList = singleUserCallLimitService.getLimit(tytSingleUserCallLimit);
			if (urlLimitList != null && urlLimitList.size() > 0) {
				request.setAttribute("urlLimitBeanList", urlLimitList);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/requestLimit/single_user_call_limit_list";
	}

	/**
	 * 
	 * @param urlLimitBean
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveSingleUserCallLimit")
	@ResponseBody
	public void saveSingleUserCallLimit(TytSingleUserCallLimit urlLimitBean, HttpServletRequest request, HttpServletResponse response) {
		logger.info("request limit save, request message is [ urlLimitBean: " + urlLimitBean + " ]");
		response.setContentType("text/html;charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		try {
			TytSingleUserCallLimit urlLimit = new TytSingleUserCallLimit();
			BeanUtils.copyProperties(urlLimit, urlLimitBean);
			urlLimit.setCtime(new Date());
			singleUserCallLimitService.add(urlLimit);
			updateRedisCache();
			response.getWriter().write("<script>alert('保存成功，稍后将关闭此窗口');window.opener.location.reload();window.close();</script>");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			try {
				response.getWriter().write("<script>alert('保存失败，请重试');window.close();</script>");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping("/deleteSingleUserCallLimit")
	@ResponseBody
	public ResultMsgBean deleteSingleUserCallLimit(Long id) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			singleUserCallLimitService.delete(id);
			updateRedisCache();
			// 获取结果
			result.setCode(200);
			result.setMsg("删除成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(400);
			result.setMsg("删除失败");
		}
		return result;
	}

	@RequestMapping("goUpdateSingleUserCallLimit")
	public String goUpdateSingleUserCallLimit(HttpServletRequest request, Long id) {
		// 登陆验证
		EmployeeQueryBean curUser = getCurrentUser(request);
		try {
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			TytSingleUserCallLimit urlLimit = singleUserCallLimitService.getById(id);
			request.setAttribute("urlLimitBean", urlLimit);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/requestLimit/single_user_call_limit_update";
	}

	private void updateRedisCache() {
		// 获取最新数据数据更新到redis中
		List<TytSingleUserCallLimit> urlLimitList = singleUserCallLimitService.getLimit(null);
		RedisUtil.set(Constant.SINGLE_USER_CALL_URL_LIMIT_CACHE_KEY, JSON.toJSONString(urlLimitList), 0);
	}

	/**
	 *
	 * @param request
	 * @param response
	 * @param fileField
	 */
	@RequestMapping(value = "/importList", method = RequestMethod.POST)
	public void importList(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "fileField", required = true) MultipartFile fileField) {
		EmployeeQueryBean curUser = getCurrentUser(request);
		ResultMsgBean msgBean = new ResultMsgBean();
		if (curUser == null) {
			msgBean.setCode(ResultMsgBean.ERROR);
			msgBean.setMsg("导入失败,该用户没有登录");
			printJSON(request, response, msgBean);
			return;
		}
		try {
			msgBean = singleUserCallLimitService.saveExcel(fileField, curUser);
			updateRedisCache();
		} catch (Exception e) {
			msgBean.setCode(ResultMsgBean.ERROR);
			msgBean.setMsg("导入失败,请重新导入");
			e.printStackTrace();
		}
		printJSON(request, response, msgBean);
		return;
	}
}