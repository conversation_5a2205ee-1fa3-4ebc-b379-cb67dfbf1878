package com.tyt.requestlimit.service.impl;

import com.tyt.dao.base.BaseDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSingleUserCallLimit;
import com.tyt.requestlimit.service.SingleUserCallLimitService;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.AppConfig;
import com.tyt.util.CsvReader;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("singleUserCallLimitService")
public class SingleUserCallLimitServiceImpl extends BaseServiceImpl<TytSingleUserCallLimit, Long> implements SingleUserCallLimitService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "singleUserCallLimitDao")
	public void setBaseDao(BaseDao<TytSingleUserCallLimit, Long> requestLimitDao) {
		super.setBaseDao(requestLimitDao);
	}

	@Override
	public List<TytSingleUserCallLimit> getLimit(TytSingleUserCallLimit tytSingleUserCallLimit) {
		return this.findList(tytSingleUserCallLimit);
	}

	@Override
	public ResultMsgBean saveExcel(MultipartFile fileField, EmployeeQueryBean curUser) throws Exception {
		ResultMsgBean msgBean = new ResultMsgBean();
		String str = "";
		if (fileField != null && !fileField.isEmpty()) {
			String excelUrl = renameExcelBase(fileField, "excel");
			fileField.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + excelUrl));
			List<List<String>> importDataList = CsvReader.csvToList(AppConfig.getProperty("picture.file.path.domain") + excelUrl);

			//  用户ID  限制次数
			for (List<String> lineDataList : importDataList) {
				if (lineDataList != null && lineDataList.size() > 0) {
					TytSingleUserCallLimit tytSingleUserCallLimit = new TytSingleUserCallLimit();
					tytSingleUserCallLimit.setCtime(new Date());
					tytSingleUserCallLimit.setUserId(Long.valueOf(lineDataList.get(0)));
					tytSingleUserCallLimit.setCallTime(Integer.valueOf(lineDataList.get(1)));
					this.getBaseDao().insert(tytSingleUserCallLimit);
				}
			}
			str += "导入成功";
			msgBean.setCode(200);
			msgBean.setData(str);
			return msgBean;
		}
		return msgBean;
	}

}
