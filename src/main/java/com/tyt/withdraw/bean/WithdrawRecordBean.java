package com.tyt.withdraw.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/8/25 10:32
 * @Version 1.0
 **/
@Data
public class WithdrawRecordBean {
    private Long id;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 用户Id
     */
    private Long userId;


    /**
     * 真实姓名
     */
    private String userName;

    /**
     * 银行卡号
     */
    private String cardNumber;

    /**
     * 持卡人姓名
     */
    private String cardOwner;

    /**
     * 提现订单号
     */
    private String orderNo;

    /**
     * 流水号
     */
    private String serialNumber;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 完成时间
     */
    private Date transferTime;

    /**
     * 提现金额
     */
    private BigDecimal withdrawAmount;

    /**
     * 手续费
     */
    private BigDecimal serviceAmount;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 状态 1.申请成功 2.申请失败 3 已转账 4 转账失败
     */
    private Integer status;


}
