package com.tyt.withdraw.controller;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.util.CsvWriter;
import com.tyt.web.base.BaseController;
import com.tyt.withdraw.bean.WithdrawBalanceRemindConfigBean;
import com.tyt.withdraw.bean.WithdrawRecordBean;
import com.tyt.withdraw.bean.WithdrawRecordQueryBean;
import com.tyt.withdraw.service.TytWithdrawService;
import com.tyt.withdraw.service.WithdrawBalanceRemindConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/8/25 10:18
 * @Version 1.0
 **/
@Controller
@RequestMapping("/withdraw")
public class TytWithdrawController  extends BaseController {
    @Resource(name = "withdrawService")
    private TytWithdrawService tytWithdrawService;

    @Resource(name = "withdrawBalanceRemindConfigService")
    private WithdrawBalanceRemindConfigService withdrawBalanceRemindConfigService;


    @GetMapping("/record/list")
    @ResponseBody
    public ResultMsgBean list(WithdrawRecordQueryBean queryBean , PageBean pageBean, Integer currentPage,
                              HttpServletRequest request, HttpServletResponse response) {
        {
            ResultMsgBean rm = new ResultMsgBean();
            try {
                // 获得当前用户的身份
                EmployeeQueryBean curUser = getCurrentUser(request);
                if (curUser == null) {
                    rm.setCode(ReturnCodeConstant.NOT_LOGGED_IN_CODE);
                    rm.setMsg(ReturnCodeConstant.NOT_LOGGED_IN_MSG);
                    return rm;
                }
                pageBean.setCurrentPage(currentPage == null ? 1 : currentPage);
                List<WithdrawRecordBean> list = tytWithdrawService.getRecordList(queryBean, pageBean);
                //遍历循环 列表数据原手机号字段置空，跟随该手机号配套对应的Base64加密的userId同时返回
                for(WithdrawRecordBean withdrawRecordBean:list){
                    withdrawRecordBean.setUserPhone(Base64.getEncoder().encodeToString(withdrawRecordBean.getUserId().toString().getBytes()));
                }

                BigDecimal totalAmount = tytWithdrawService.getTotalWithdrawAmount(queryBean);
                HashMap<Object, Object> map = new HashMap<>();
                map.put("list", list==null?new ArrayList<WithdrawRecordBean>():list);
                map.put("searchBean", queryBean);
                map.put("pageBean", pageBean);
                map.put("totalAmount", totalAmount==null?new BigDecimal(0):totalAmount);
                rm.setData(map);
                return rm;

            } catch (Exception e) {
                logger.error("服务器异常", e);
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("服务器错误");
            }
            return rm;
        }
    }

    @RequestMapping(value = "/record/export", method = RequestMethod.POST)
    public void export(WithdrawRecordQueryBean queryBean, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                logger.info("admin:refuse export withdraw record.");
                return;
            }
            /*获得查询结果总集*/
            PageBean pageBean = new PageBean();
            pageBean.setCurrentPage(1);
            pageBean.setPageSize(Integer.MAX_VALUE);
            List<WithdrawRecordBean> list = tytWithdrawService.getRecordList(queryBean, pageBean);
            //导出操作
            String CSV_RN = "\r\n";//列分隔符

            StringBuffer csvStr = tytWithdrawService.getStringCsv(list);

            CsvWriter.exportCsv("提现记录_", csvStr.toString(), response);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/updateBalanceRemaindConfig", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean updateBalanceRemaindConfig(WithdrawBalanceRemindConfigBean bean, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setCode(ReturnCodeConstant.NOT_LOGGED_IN_CODE);
                rm.setMsg(ReturnCodeConstant.NOT_LOGGED_IN_MSG);
                return rm;
            }
            if(StringUtils.isBlank(bean.getNotifyPhone())) {
                rm.setCode(ReturnCodeConstant.OTHER_ERROR);
                rm.setMsg("通知人手机号不可为空");
                return rm;
            }
            if(bean.getLimitQuota() == null) {
                rm.setCode(ReturnCodeConstant.OTHER_ERROR);
                rm.setMsg("限制额度不可为空");
                return rm;
            }
            withdrawBalanceRemindConfigService.updateBalanceRemaindConfig(bean,curUser.getUserName());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return rm;
    }

    @RequestMapping(value = "/getBalanceRemaindConfig", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean getBalanceRemaindConfig(HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setCode(ReturnCodeConstant.NOT_LOGGED_IN_CODE);
                rm.setMsg(ReturnCodeConstant.NOT_LOGGED_IN_MSG);
                return rm;
            }
            WithdrawBalanceRemindConfigBean data = withdrawBalanceRemindConfigService.getConfig();
            rm.setData(data);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return rm;
    }


}
