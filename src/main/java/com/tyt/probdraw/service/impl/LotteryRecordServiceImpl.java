package com.tyt.probdraw.service.impl;

import com.tyt.luckdraw.bean.ActivityInfoListBean;
import com.tyt.mybatis.mapper.LotteryRecordMapper;
import com.tyt.probdraw.bean.LotteryRecord;
import com.tyt.probdraw.service.LotteryRecordService;
import com.tyt.service.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: hp
 * Date: 2021/9/7
 */
@Service("lotteryRecordService")
public class LotteryRecordServiceImpl extends BaseServiceImpl<LotteryRecord,Integer> implements LotteryRecordService {
    @Autowired
    private LotteryRecordMapper lotteryRecordMapper;
    @Override
    public List<ActivityInfoListBean> list(LotteryRecord lotteryRecord) {
        return lotteryRecordMapper.list(lotteryRecord);
    }
}
