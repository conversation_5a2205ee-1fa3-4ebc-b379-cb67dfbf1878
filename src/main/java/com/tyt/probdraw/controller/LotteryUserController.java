package com.tyt.probdraw.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.probdraw.bean.LotteryUser;
import com.tyt.probdraw.service.LotteryUserService;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.CsvWriter;
import com.tyt.web.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: hp
 * Date: 2021/9/6
 */
@RestController
@RequestMapping("/probdraw/preset/")
public class LotteryUserController extends BaseController {
    @Autowired
    private LotteryUserService lotteryUserService;

    @GetMapping("list")
    public ResultMsgBean activityInfoList(LotteryUser lotteryUser, HttpServletRequest request,PageBean pageBean) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            if (pageBean.getPageSize() > 100) {
                pageBean.setPageSize(100);
            }
            Page<Object> result = PageHelper.startPage(pageBean.getCurrentPage(), pageBean.getPageSize(), true);
            List<LotteryUser> list = lotteryUserService.getActivityInfoList(lotteryUser);
            HashMap<Object, Object> map = new HashMap<>();
            map.put("list", list);
            pageBean.setRowCount(result.getTotal());
            map.put("pageBean",pageBean);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @return void
     * @Description 2.导出真实中奖用户名单列表文件
     * <AUTHOR>
     * @Date 2020/3/11 11:43
     * @Param [realWinnerListBean, request, response]
     * see /template/drawUser/抽奖用户模板.xlsx
     **/
    @Deprecated
    @RequestMapping(value = "exportRealWinnerList")
    public void exportRealWinnerList(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                //直接返回
                return;
            }
            PageBean pageBean = new PageBean();
            pageBean.setPageSize(Integer.MAX_VALUE);
            // 行分隔符
            String CSV_RN = "\r\n";
            // 头部数据
            StringBuilder content = new StringBuilder("用户id");

            // 导出真实中奖用户名单列表数据
            CsvWriter.exportCsv("抽奖用户模板", content.toString(), response, "GBK", "");
            content = null;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("export realwinners file error, the error message is: ", e.getMessage());
        }
    }

    /**
     * @param request
     * @param response
     * @param fileField
     */
    @RequestMapping(value = "/importList", method = RequestMethod.POST)
    public ResultMsgBean importList(HttpServletRequest request, HttpServletResponse response,
                           @RequestParam(value = "fileField", required = true) MultipartFile fileField,
                           @RequestParam(value = "id", required = true) Long activityId) {
        EmployeeQueryBean curUser = super.getCurrentUser(request);

        if (curUser == null) {
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "导入失败,该用户没有登录");
        }
        ResultMsgBean resultMsgBean = null;
        try {

            String importResult = lotteryUserService.saveExcel(lotteryUserService, fileField, curUser, activityId);

            resultMsgBean = ResultMsgBean.successResponse(importResult);

        } catch (TytException e) {
            logger.warn("", e);
            resultMsgBean = ResultMsgBean.failResponse(e);
        } catch (Exception e) {
            logger.error("", e);
            resultMsgBean = ResultMsgBean.failResponse(ResultMsgBean.ERROR, "导入失败,请重新导入");
        }

        return resultMsgBean;
    }
    @DeleteMapping("delete")
    public ResultMsgBean deleteUser(HttpServletRequest request,@RequestBody LotteryUser lotteryUser){
        EmployeeQueryBean curUser = getCurrentUser(request);
        ResultMsgBean msgBean = new ResultMsgBean();
        if (curUser == null) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("删除失败,该用户没有登录");
            return msgBean;
        }
        lotteryUserService.deleteUser(lotteryUser.getId());
        return msgBean;
    }
    @GetMapping("sendEmailAndPush")
    public ResultMsgBean sendEmailAndPush(Long id,HttpServletRequest request){
        EmployeeQueryBean curUser = getCurrentUser(request);
        ResultMsgBean msgBean = new ResultMsgBean();
        if (curUser == null) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("发送失败,该用户没有登录");
            return msgBean;
        }
        lotteryUserService.sendEmailAndPush(id);
        return new ResultMsgBean(200,"操作成功");
    }

    /**
     * @description 批量删除
     * <AUTHOR>
     * @date 2023/8/24 16:30
     * @version 1.0
     * @param request
     * @param activityId
     * @param fileField
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = "batchDel")
    public ResultMsgBean batchDel(HttpServletRequest request,
                                  @RequestParam("activityId") Long activityId,
                                  @RequestParam(value = "fileField") MultipartFile fileField){
        // 获得当前用户的身份
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            return ResultMsgBean.failResponse(500, "未登录");
        }
        return lotteryUserService.updateBatchDel(activityId,fileField);
    }
}
