package com.tyt.probdraw.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.luckdraw.bean.ActivityInfoListBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.probdraw.bean.LotteryRecord;
import com.tyt.probdraw.bean.LotteryUser;
import com.tyt.probdraw.service.LotteryRecordService;
import com.tyt.web.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: hp
 * Date: 2021/9/7
 */
@RestController
@RequestMapping("/lottery/record")
public class LotteryRecordController extends BaseController {
    @Autowired
    private LotteryRecordService lotteryRecordService;
    @GetMapping("list")
    public ResultMsgBean list(LotteryRecord lotteryRecord, HttpServletRequest request, PageBean pageBean) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            if (pageBean.getPageSize() > 100) {
                pageBean.setPageSize(100);
            }
            Page<Object> result = PageHelper.startPage(pageBean.getCurrentPage(),pageBean.getPageSize(), true);
            List<ActivityInfoListBean> list = lotteryRecordService.list(lotteryRecord);
            HashMap<Object, Object> map = new HashMap<>();
            map.put("list", list);
            pageBean.setRowCount(result.getTotal());
            map.put("pageBean",pageBean);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }
}
