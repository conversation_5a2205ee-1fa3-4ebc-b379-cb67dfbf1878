package com.tyt.stick.service;

import java.util.List;

import com.tyt.model.TytStickRule;
import com.tyt.service.base.BaseService;
/**
 * 
 * <AUTHOR>
 *
 */
public interface StickRuleService extends BaseService<TytStickRule,Long> {
	/**
	 * 修改
	 * @param rule
	 */
	void updateRule(Long id);
	/**
	 * 按状态获取值
	 * @param status
	 * @return
	 */
	List<TytStickRule> getRules(Integer status);
	/**
	 * 保存OBJECT
	 * @param rule
	 */
	void addRule(TytStickRule rule);
	/**
	 * 判断是不是已经存在的区间
	 * @param startHour
	 * @param endHour
	 * @return
	 */
	boolean getByHour(Integer startHour,Integer endHour);

}
