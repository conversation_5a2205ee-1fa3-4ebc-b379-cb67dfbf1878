package com.tyt.universalWordConfig.service.impl;

import com.tyt.mybatis.mapper.UniversalWordConfigLogMapper;
import com.tyt.universalWordConfig.bean.UniversalWordConfigLog;
import com.tyt.universalWordConfig.service.UniversalWordConfigLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UniversalWordConfigLogServiceImpl implements UniversalWordConfigLogService {

    @Autowired
    private UniversalWordConfigLogMapper universalWordConfigLogMapper;

    @Override
    public int addLog(UniversalWordConfigLog universalWordConfigLog) {
        return universalWordConfigLogMapper.addLog(universalWordConfigLog);
    }
}
