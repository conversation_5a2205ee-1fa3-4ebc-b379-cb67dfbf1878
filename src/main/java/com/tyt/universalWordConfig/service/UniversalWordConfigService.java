package com.tyt.universalWordConfig.service;

import com.tyt.abtest.bean.PageData;
import com.tyt.universalWordConfig.bean.UniversalWordConfigInfo;
import com.tyt.universalWordConfig.bean.UniversalWordConfigInfoReq;

public interface UniversalWordConfigService {


    PageData<UniversalWordConfigInfo> getUniversalWordConfigInfoList(UniversalWordConfigInfoReq req);

    UniversalWordConfigInfo getUniversalWordConfigInfoByid(Long universalWordConfigId);

    int updateUniversalWordConfigById(UniversalWordConfigInfo universalWordConfigInfo);
}
