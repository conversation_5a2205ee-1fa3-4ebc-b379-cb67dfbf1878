package com.tyt.web.qbean.account;

import lombok.Data;

import java.util.Date;

/**
 * @description: 担保账户查询bean
 * @author: liz<PERSON>
 * @date: 2020-09-09 15:29:47
 */
@Data
public class GuaranteeAccountReqBean {
    /**
     * 运单号
     */
    private String businessNo;
    /**
     * 出入账日期-开始日期
     */
    private Date startTime;
    /**
     * 出账日期-结束日期
     */
    private Date endTime;
    /**
     * 流水类型
     */
    private Integer flowType;
    /**
     * 订单号
     */
    private String businessOrderNo;
    /**
     * 流水号
     */
    private String flowNumber;
    /**
     * 手机号
     */
    private String userPhone;
    /**
     * 当前页
     */
    private Integer currentPage;
    /**
     * 每页数量
     */
    private Integer pageSize;
    /**
     * 流水类型
     */
    private Integer businessType;
}
