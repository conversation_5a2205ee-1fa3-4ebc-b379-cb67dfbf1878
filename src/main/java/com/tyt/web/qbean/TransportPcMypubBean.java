package com.tyt.web.qbean;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


public class TransportPcMypubBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6548721805995855175L;
	private String price;//运费
	private Long id;
    private String startPoint;
    private String destPoint;
   // private String taskContent;
    private String tel;
    private Long   pubQQ;
    private Integer status;
    private Integer source;
    private String pubTime;
    private Timestamp ctime;
    private Timestamp mtime;
    private String nickName;
    private String uploadCellPhone;
    private Integer resend;
    private String startCoord;
    private String destCoord;
    private Integer  platId;
    private Integer verifyFlag;
    
    /*20150716新增字段*/
    Long userId;//'发布人usreID 关联tyt_user表id',
  //  String priceCode;//'运费代码',
    String  startDetailAdd;//'出发地详细地址',
    String  destDetailAdd;//'目的地详细地址',
    Date pubDate;//'发布日期',
   // String goodsCode;//'货物代码 与 货物对应 task_content 字段是一对',
    //String weightCode;//'重量代码',
    String weight;// '重量单位吨',
    String length;// '货物长单位米',
    String wide;// '货物宽单位米',
    String high;//'货物高单位米',
    String isSuperelevation;// '是否三超 0未超1超',
    String linkman;//'联系人',
    String remark;// '备注',
    Date pubGoodsTime;//'发货日期',
    String tel3;//联系人2
    String tel4;//联系人3
    
    /*辅助字段*/
    String startCoordX;//'出发地坐标x',
    String startCoordY;//'出发地坐标y',
    String destCoordX;//'目的地坐标x',
    String destCoordY;//'目的地坐标y',
    String startLatitude;//'出发地纬度',
    String startLongitude;//'出发地经度',
    String destLongitude;//'目的地经度',
    String destLatitude;//'目的地纬度',
    String distance;//'出发地目的地之间距离',
    
//    
//    Integer startCoordXValue;//'出发地坐标x',
//    Integer startCoordYValue;//'出发地坐标y',
//    Integer destCoordXValue;//'目的地坐标x',
//    Integer destCoordYValue;//'目的地坐标y',
//    Integer startLatitudeValue;//'出发地纬度',
//    Integer startLongitudeValue;//'出发地经度',
//    Integer destLongitudeValue;//'目的地经度',
//    Integer destLatitudeValue;//'目的地纬度',
//    Integer distanceValue;//'出发地目的地之间距离',
    
    /*2015-08-21为解决pc问题新增*/
  //  String displayType;
  //  String hashCode;
    
    /*2015-08-24*/
    String isCar;
    Integer userType;
    /*2015-09-12*/
    String pcOldContent;
    
    public static final  int STATUS_ENABLE = 1;
    public static final  int STATUS_DISABLE = 0;
    public static final  int STATUS_UNKNOWN = 2;
    public static final  int STATUS_BLOCKED = 3;
    public static final  int STATUS_DEAl = 4;
    
    public static final int SOURCE_AUTO = 1;
    public static final int SOURCE_MANUAL = 0;
    
    public static final String NO_REGISTER="未注册";
    public static final String REGISTER_PAY="付费使用";
    public static final String REGISTER_NO_PAY="已注册试用";
    public static final String PAY_OTHER="付费用户，未用注册手机发布";
    public static final String REGISTER_OTHER_NO_PAY="已注册试用";
    public static final String PAY_NO_USE="付费,未使用";
    public static final String NO_PAY_NO_USE="已注册试用";

   
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }
    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

 
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public Long getPubQQ() {
        return pubQQ;
    }

    public void setPubQQ(Long pubQQ) {
        this.pubQQ = pubQQ;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getPubTime() {
        return pubTime;
    }

    public void setPubTime(String pubTime) {
        this.pubTime = pubTime;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
    
    
    public String getUploadCellPhone() {
		return uploadCellPhone;
	}

	public void setUploadCellPhone(String uploadCellPhone) {
		this.uploadCellPhone = uploadCellPhone;
	}

	public Integer getResend() {
		return resend;
	}

	public void setResend(Integer resend) {
		this.resend = resend;
	}
	
	public String getStartCoord() {
		return startCoord;
	}

	public void setStartCoord(String startCoord) {
		this.startCoord = startCoord;
	}
	public String getDestCoord() {
		return destCoord;
	}

	public void setDestCoord(String destCoord) {
		this.destCoord = destCoord;
	}
	
	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}

	public Integer getVerifyFlag() {
		return verifyFlag;
	}

	public void setVerifyFlag(Integer verifyFlag) {
		this.verifyFlag = verifyFlag;
	}

    public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}
    
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public String getStartDetailAdd() {
		return startDetailAdd;
	}

	public void setStartDetailAdd(String startDetailAdd) {
		this.startDetailAdd = startDetailAdd;
	}
	
	
	public String getDestDetailAdd() {
		return destDetailAdd;
	}
	public void setDestDetailAdd(String destDetailAdd) {
		this.destDetailAdd = destDetailAdd;
	}

	public Date getPubDate() {
		return pubDate;
	}

	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}
	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}
	public String getWide() {
		return wide;
	}

	public void setWide(String wide) {
		this.wide = wide;
	}
	public String getHigh() {
		return high;
	}

	public void setHigh(String high) {
		this.high = high;
	}
	public String getIsSuperelevation() {
		return isSuperelevation;
	}

	public void setIsSuperelevation(String isSuperelevation) {
		this.isSuperelevation = isSuperelevation;
	}
	public String getLinkman() {
		return linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getPubGoodsTime() {
		return pubGoodsTime;
	}

	public void setPubGoodsTime(Date pubGoodsTime) {
		this.pubGoodsTime = pubGoodsTime;
	}
	public String getTel3() {
		return tel3;
	}
	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}
	public String getTel4() {
		return tel4;
	}
	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}
	
	public void setStartCoordX(String startCoordX) {
		this.startCoordX = startCoordX;
	}


	public void setStartCoordY(String startCoordY) {
		this.startCoordY = startCoordY;
	}
	

	public void setDestCoordX(String destCoordX) {
		this.destCoordX = destCoordX;
	}
	
	public void setDestCoordY(String destCoordY) {
		this.destCoordY = destCoordY;
	}
	

	public void setStartLatitude(String startLatitude) {
		this.startLatitude = startLatitude;
	}
	

	public void setStartLongitude(String startLongitude) {
		this.startLongitude = startLongitude;
	}
	

	public void setDestLongitude(String destLongitude) {
		this.destLongitude = destLongitude;
	}
	

	public void setDestLatitude(String destLatitude) {
		this.destLatitude = destLatitude;
	}
	

	public void setDistance(String distance) {
		this.distance = distance;
	}
	
    

	
	@Column(name="is_car")
	public String getIsCar() {
		return isCar;
	}
	public void setIsCar(String isCar) {
		this.isCar = isCar;
	}
	@Column(name="user_type")
	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	
	@Column(name="pc_old_content")
	public String getPcOldContent() {
		return pcOldContent;
	}

	public void setPcOldContent(String pcOldContent) {
		this.pcOldContent = pcOldContent;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	public String getStartCoordX() {
		return startCoordX;
	}

	public String getStartCoordY() {
		return startCoordY;
	}

	public String getDestCoordX() {
		return destCoordX;
	}

	public String getDestCoordY() {
		return destCoordY;
	}

	public String getStartLatitude() {
		return startLatitude;
	}

	public String getStartLongitude() {
		return startLongitude;
	}

	public String getDestLongitude() {
		return destLongitude;
	}

	public String getDestLatitude() {
		return destLatitude;
	}

	public String getDistance() {
		return distance;
	}

}
