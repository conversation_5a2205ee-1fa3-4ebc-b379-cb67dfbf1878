package com.tyt.web.qbean;

import java.util.Date;
import com.tyt.util.TimeUtil;

public class InfoStatQueryBean {

	private String tel = null;
	private Long pubQQ = null;
	private String sales = null;
	String startTime = TimeUtil.formatDate(TimeUtil.dateDiff(-1));// 开始时间,
	String endTime = TimeUtil.formatDate(new Date());// 结束时间
	Integer serveDaysBegin = null;// 剩余天数
	Integer serveDaysEnd = null;
	Integer userType = null;// 用户类型
	private String note;// 备注
	private String maintainMan;// 备注
	Integer userSign;// 用户身份
	/*
	 * 销售审核一级身份
	 */
	private Integer identityLevelOne;
	/*
	 * 销售审核二级身份
	 */
	private Integer identityLevelTwo;

	public Integer getIdentityLevelOne() {
		return identityLevelOne;
	}

	public void setIdentityLevelOne(Integer identityLevelOne) {
		this.identityLevelOne = identityLevelOne;
	}

	public Integer getIdentityLevelTwo() {
		return identityLevelTwo;
	}

	public void setIdentityLevelTwo(Integer identityLevelTwo) {
		this.identityLevelTwo = identityLevelTwo;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public Long getPubQQ() {
		return pubQQ;
	}

	public void setPubQQ(Long pubQQ) {
		this.pubQQ = pubQQ;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getSales() {
		return sales;
	}

	public void setSales(String sales) {
		this.sales = sales;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public Integer getServeDaysBegin() {
		return serveDaysBegin;
	}

	public void setServeDaysBegin(Integer serveDaysBegin) {
		this.serveDaysBegin = serveDaysBegin;
	}

	public Integer getServeDaysEnd() {
		return serveDaysEnd;
	}

	public void setServeDaysEnd(Integer serveDaysEnd) {
		this.serveDaysEnd = serveDaysEnd;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getUserSign() {
		return userSign;
	}

	public void setUserSign(Integer userSign) {
		this.userSign = userSign;
	}

	public String getMaintainMan() {
		return maintainMan;
	}

	public void setMaintainMan(String maintainMan) {
		this.maintainMan = maintainMan;
	}

}
