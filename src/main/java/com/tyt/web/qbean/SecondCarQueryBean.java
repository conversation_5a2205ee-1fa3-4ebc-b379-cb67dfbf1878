package com.tyt.web.qbean;

import com.tyt.util.Constant;

public class SecondCarQueryBean {
    
	private String title;//标题
	private Integer model=0;//类型
	private Integer  carName=Constant.CARNAME0;//二手车品牌
	private Integer subsection=Constant.SUBSECTION0;//是否分期
	private String  province;//省
    private String  city;//市
    private String  county;//县
    private Integer  price=Constant.PRICE0;//二手车价格
    private Integer carAge=Constant.CARAGE0;//二手车车龄
    private Integer distinguish=Constant.DISTINGUISH1;//区分待售与求购
    private String  search;
    private Integer history=Constant.HISTORY0;
    
    public Integer getHistory() {
		return history;
	}
	public void setHistory(Integer history) {
		this.history = history;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getSearch() {
		return search;
	}
	public void setSearch(String search) {
		this.search = search;
	}
	String message;
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCounty() {
		return county;
	}
	public void setCounty(String county) {
		this.county = county;
	}
	
	public Integer getDistinguish() {
		return distinguish;
	}
	public void setDistinguish(Integer distinguish) {
		this.distinguish = distinguish;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public Integer getModel() {
		return model;
	}
	public void setModel(Integer model) {
		this.model = model;
	}
	public Integer getCarName() {
		return carName;
	}
	public void setCarName(Integer carName) {
		this.carName = carName;
	}
	public Integer getPrice() {
		return price;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}
	public Integer getCarAge() {
		return carAge;
	}
	public void setCarAge(Integer carAge) {
		this.carAge = carAge;
	}
	 public Integer getSubsection() {
			return subsection;
		}
		public void setSubsection(Integer subsection) {
			this.subsection = subsection;
	 }
	
	
    
    
}
