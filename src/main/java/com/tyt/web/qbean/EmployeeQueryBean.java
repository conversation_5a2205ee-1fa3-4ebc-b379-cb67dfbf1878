package com.tyt.web.qbean;

public class EmployeeQueryBean {
    
	private String  title;//招聘信息标题
	private Integer position=0;//招聘职位
	private Integer duty=0;//职务
	private String  province;//省
    private String  city;//市
    private String  county;//县
    //private String  subsidy="";//福利
    private Integer  years;//驾龄
    private String  search;//搜索
	private Integer  salary=0;//工资
    private Integer distinguish=1;//区分招聘者与求职者
	
    private Integer age;// 年龄
    private Integer education;// 学历
    private Integer sex;// 性别
    private Integer identity;// 身份
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public Integer getEducation() {
		return education;
	}
	public void setEducation(Integer education) {
		this.education = education;
	}
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public Integer getIdentity() {
		return identity;
	}
	public void setIdentity(Integer identity) {
		this.identity = identity;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCounty() {
		return county;
	}
	public void setCounty(String county) {
		this.county = county;
	}
	/*public String getSubsidy() {
		return subsidy;
	}
	public void setSubsidy(String subsidy) {
		this.subsidy = subsidy;
	}*/
	
	public Integer getDistinguish() {
		return distinguish;
	}
	public void setDistinguish(Integer distinguish) {
		this.distinguish = distinguish;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getSearch() {
		return search;
	}
	public void setSearch(String search) {
		this.search = search;
	}
	public Integer getPosition() {
		return position;
	}
	public void setPosition(Integer position) {
		this.position = position;
	}
	
	public Integer getDuty() {
		return duty;
	}
	public void setDuty(Integer duty) {
		this.duty = duty;
	}
	public Integer getYears() {
		return years;
	}
	public void setYears(Integer years) {
		this.years = years;
	}
	public Integer getSalary() {
		return salary;
	}
	public void setSalary(Integer salary) {
		this.salary = salary;
	}
    
    
}
