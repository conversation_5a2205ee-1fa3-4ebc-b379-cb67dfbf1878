package com.tyt.web.qbean;

import com.alibaba.fastjson.JSON;

/**
 * 显示到info_stat_list页面的实体类
 * 
 * <AUTHOR>
 * 
 */
public class InfoStatBean {

	private String id;
	private String cellPhone;
	private String tel;
	private String uploadCellPhone;
	private String qq;
	private String nickName;
	private String trueName;
	private String userType;
	private String alertInfomation;
	private String sales;
	private String note;// 备注
	private String userSign;// 备注
	private String maintainMan;// 销售审核身份
	/*
	 * 销售审核一级身份value值
	 */
	private Integer identityLevelOne;
	/*
	 * 销售审核二级身份value值
	 */
	private Integer identityLevelTwo;
	/*
	 * 销售审核一级身份name值
	 */
	private String identityLevelOneName;
	/*
	 * 销售审核二级身份name值
	 */
	private String identityLevelTwoName;

	public String getIdentityLevelOneName() {
		return identityLevelOneName;
	}

	public void setIdentityLevelOneName(String identityLevelOneName) {
		this.identityLevelOneName = identityLevelOneName;
	}

	public String getIdentityLevelTwoName() {
		return identityLevelTwoName;
	}

	public void setIdentityLevelTwoName(String identityLevelTwoName) {
		this.identityLevelTwoName = identityLevelTwoName;
	}

	public Integer getIdentityLevelOne() {
		return identityLevelOne;
	}

	public void setIdentityLevelOne(Integer identityLevelOne) {
		this.identityLevelOne = identityLevelOne;
	}

	public Integer getIdentityLevelTwo() {
		return identityLevelTwo;
	}

	public void setIdentityLevelTwo(Integer identityLevelTwo) {
		this.identityLevelTwo = identityLevelTwo;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	private Integer autoEnable = 0;// 自动有效
	private Integer mnualEnable = 0;// 人工有效
	private Integer manualDeal = 0;// 人工成交
	private Integer manualTotal = 0;// 人工总数(有效+成交)

	private Integer serveDays;// 剩余天数

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getSales() {
		return sales;
	}

	public void setSales(String sales) {
		this.sales = sales;
	}

	public String getAlertInfomation() {
		return alertInfomation;
	}

	public void setAlertInfomation(String alertInfomation) {
		this.alertInfomation = alertInfomation;
	}

	public String getUploadCellPhone() {
		return uploadCellPhone;
	}

	public void setUploadCellPhone(String uploadCellPhone) {
		this.uploadCellPhone = uploadCellPhone;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getTrueName() {
		return trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public Integer getAutoEnable() {
		return autoEnable;
	}

	public void setAutoEnable(Integer autoEnable) {
		this.autoEnable = autoEnable;
	}

	public Integer getMnualEnable() {
		return mnualEnable;
	}

	public void setMnualEnable(Integer mnualEnable) {
		this.mnualEnable = mnualEnable;
	}

	public Integer getManualDeal() {
		return manualDeal;
	}

	public void setManualDeal(Integer manualDeal) {
		this.manualDeal = manualDeal;

	}

	public Integer getManualTotal() {
		return manualTotal;
	}

	public void setManualTotal(Integer manualTotal) {
		this.manualTotal = manualTotal;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public Integer getServeDays() {
		return serveDays;
	}

	public void setServeDays(Integer serveDays) {
		this.serveDays = serveDays;
	}

	public String getUserSign() {
		return userSign;
	}

	public void setUserSign(String userSign) {
		this.userSign = userSign;
	}

	public String getMaintainMan() {
		return maintainMan;
	}

	public void setMaintainMan(String maintainMan) {
		this.maintainMan = maintainMan;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}