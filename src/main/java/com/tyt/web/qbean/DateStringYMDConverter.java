package com.tyt.web.qbean;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.tyt.web.enums.BusinessTypeEnum;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description: 日期转换 yyyy-MM-dd
 * @author: lizhao
 * @date: 2020-09-09 15:29:47
 */
@Data
public class DateStringYMDConverter implements Converter<Date> {

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Date convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(Date date, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String dateString = null;
        if(date != null){
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            dateString = formatter.format(date);
        }
        return new CellData(dateString);
    }
}
