package com.tyt.web.fore;

import com.tyt.model.*;
import com.tyt.payment.queryBean.ChannelBankQueryBean;
import com.tyt.payment.service.ChannelBankService;
import com.tyt.price.service.PriceService;
import com.tyt.service.advice.AdviceService;
import com.tyt.service.blockinfo.BlockInfoService;
import com.tyt.service.cache.CacheService;
import com.tyt.service.car.CarService;
import com.tyt.service.driver.EmployeeService;
import com.tyt.service.mobile.MobileService;
import com.tyt.service.newcar.NewCarService;
import com.tyt.service.pc.PcService;
import com.tyt.service.secondcar.SecondCarService;
import com.tyt.service.takecar.TakeCarService;
import com.tyt.service.transport.TransportCollectService;
import com.tyt.service.transport.TransportMainService;
import com.tyt.service.transport.TransportService;
import com.tyt.service.user.UserService;
import com.tyt.service.user.UserTelService;
import com.tyt.util.*;
import com.tyt.verifylog.service.VerifyLogService;
import com.tyt.web.base.BaseController;
import com.tyt.web.fore.querybean.*;
import com.tyt.web.qbean.BlockQueryBean;
import com.tyt.web.qbean.QueryUserTel;
import com.tyt.web.qbean.TransportBean;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
@Controller
@RequestMapping("/pc")
public class PrivateCenterController extends BaseController {
	@Resource(name = "userService")
	private UserService userService;
	
	@Resource(name = "carService")
	private CarService carService;
	
	@Resource(name = "userTelService")
	private UserTelService userTelService;
	
	@Resource(name = "pcService")
	private PcService pcService;

	@Resource(name = "employeeService")
	private EmployeeService employeeService;

	@Resource(name = "secondCarService")
	private SecondCarService secondCarService;

	@Resource(name = "newCarService")
	private NewCarService newCarService;
	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	
	@Resource(name="adviceService")
	 AdviceService adviceService;
	
//	@Resource(name = "insureService")
//	private InsureService insureService;
	
	@Resource(name = "takeCarService")
	private TakeCarService takeCarService;
	
	@Resource(name="blockInfoService")
	private BlockInfoService blockInfoService;
	
	@Resource(name="transportCollectService")
	private TransportCollectService transportCollectService;
	
	@Resource(name="mobileService")
	private MobileService mobileService;
	
	@Resource(name="transportService")
	private TransportService transportService;
	
	@Resource(name="transportMainService")
	private TransportMainService transportMainService;
	
	@Resource(name = "verifyLogService")
	private VerifyLogService verifyLogService;
	
	@Resource(name = "priceService")
	private PriceService priceService;
	
	@Resource(name = "channelBankService")
	private ChannelBankService channelBankService;

    //已收藏招聘信息
    @RequestMapping("employee/list")
    private String EmpList1(@RequestParam(value = "token", defaultValue = "") String token,
			@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
			Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
    	   try {
			if(StringUtils.hasLength(cellPhone)){
				if (!validateToken(cellPhone, token, request, response)) {
					logger.info(" validateToken " + cellPhone + " token= " + token
							+ " token Error!");
					return  "fore/jsp/failure";
				}
			    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
			    logger.info(cellPhone+" save to memcache "+b);
			    }
			    if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
			    if (pageNo == null || pageNo.intValue() <= 0)
					pageNo = 1;
				PageBean pageBean = new PageBean();
				pageBean.setCurrentPage(pageNo);
				if (pageSize == null || pageSize.intValue() <= 0)
					pageSize = Constant.DEFAULT_PAGE_SIZE_PC;
				pageBean.setPageSize(pageSize);
				User nowUser=userService.getUserByCellphone(getMemcache(request));
				Long userId=nowUser.getId();
				List<Collect>   collects1=pcService.getList(
						" entity.userId="+userId+" and entity.collectStatus="
				+Constant.COLLECT_STATUS1
				+" and (entity.employeeId>0 or entity.seekId>0)"+"order by entity.id desc"
				, pageBean);
				List<Employee> e3=new ArrayList<Employee>();
			    for(int i=0;i<collects1.size();i++){
			    	if(collects1.get(i).getEmployeeId()!=null){
			    		Employee e1=employeeService.getById(collects1.get(i).getEmployeeId());
			    	    e3.add(e1);
			        }else{
			    		if(collects1.get(i).getSeekId()!=null){
			    		Employee e2=employeeService.getById(collects1.get(i).getSeekId());
			    		e3.add(e2);
			    	}
			     }
			    }
			    request.setAttribute("e3", e3);
			    request.setAttribute("page", pageBean);
				request.setAttribute("pageNo", pageNo);
				collects1=null;
				e3=null;
			    return "fore/jsp/pcEmp";
		} catch (Exception e) {
			e.printStackTrace();
		}  
    	return null;
    }
 
    //已收藏二手车信息
    @RequestMapping("secondcar/list")
    private String SecondCarList1(@RequestParam(value = "token", defaultValue = "") String token,
			@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
			Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
    	    try {
				if(StringUtils.hasLength(cellPhone)){
				if (!validateToken(cellPhone, token, request, response)) {
					logger.info(" validateToken " + cellPhone + " token= " + token
							+ " token Error!");
					return  "fore/jsp/failure";
				}
				boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
				logger.info(cellPhone+" save to memcache "+b);
				}
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				if (pageNo == null || pageNo.intValue() <= 0)
					pageNo = 1;
				PageBean pageBean = new PageBean();
				pageBean.setCurrentPage(pageNo);
				if (pageSize == null || pageSize.intValue() <= 0)
					pageSize = Constant.DEFAULT_PAGE_SIZE_PC;
				pageBean.setPageSize(pageSize);
				User nowUser=userService.getUserByCellphone(getMemcache(request));
				Long userId=nowUser.getId();
				List<Collect>   collects=pcService.getList(" entity.userId="+userId+" and entity.collectStatus="+Constant.COLLECT_STATUS1+" and (entity.secondCarId>0 or entity.forSecondId>0)"+"order by ctime desc", pageBean);
				List<SecondCar> s3=new ArrayList<SecondCar>();
				
				for(int i=0;i<collects.size();i++){
						if(collects.get(i).getSecondCarId()!=null){
							SecondCar s1=secondCarService.getById(collects.get(i).getSecondCarId());
						    s3.add(s1);	
						}else{
								if(collects.get(i).getForSecondId()!=null){
							SecondCar s2=secondCarService.getById(collects.get(i).getForSecondId());
						    s3.add(s2);
								}
					    }
				}
				request.setAttribute("s3", s3);
				request.setAttribute("page", pageBean);
				request.setAttribute("pageNo", pageNo);
				collects=null;
				return "fore/jsp/pcSecond";
			} catch (Exception e) {
				e.printStackTrace();
			} 
    	    return null;
    }
   
  
    //已收藏新车资讯信息
    @RequestMapping("newcar/list")
    private String NewCarList1(@RequestParam(value = "token", defaultValue = "") String token,
			@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
			Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
    	    try {
				if(StringUtils.hasLength(cellPhone)){
				if (!validateToken(cellPhone, token, request, response)) {
					logger.info(" validateToken " + cellPhone + " token= " + token
							+ " token Error!");
					return  "fore/jsp/failure";
				}
				boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
				logger.info(cellPhone+" save to memcache "+b);
				}
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				if (pageNo == null || pageNo.intValue() <= 0)
					pageNo = 1;
				PageBean pageBean = new PageBean();
				pageBean.setCurrentPage(pageNo);
				if (pageSize == null || pageSize.intValue() <= 0)
					pageSize = Constant.DEFAULT_PAGE_SIZE_PC;
				pageBean.setPageSize(pageSize);
				User nowUser=userService.getUserByCellphone(getMemcache(request));
				Long userId=nowUser.getId();
				List<Collect>   collects=pcService.getList(" entity.userId="+userId+" and entity.collectStatus="+Constant.COLLECT_STATUS1+" and (entity.newCarId>0)"+"order by ctime desc", pageBean);
				List<NewCar> pcNewCar=new ArrayList<NewCar>();
				for(int i=0;i<collects.size();i++){
						if(collects.get(i).getNewCarId()!=null){
							pcNewCar.add(newCarService.getById(collects.get(i).getNewCarId()));
						}
				}
				request.setAttribute("pcNewCar",pcNewCar);
				request.setAttribute("page", pageBean);
				request.setAttribute("pageNo", pageNo);
				collects=null;
				pcNewCar=null;
				return "fore/jsp/pcNewCar";
			} catch (Exception e) {
				e.printStackTrace();
			} 
    	    return null;
     }
   
  //已收藏新车资讯信息
    /*@RequestMapping("insure/list")
    private String insureList1(@RequestParam(value = "token", defaultValue = "") String token,
			@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
			Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
    	    try {
				if(StringUtils.hasLength(cellPhone)){
				if (!validateToken(cellPhone, token, request, response)) {
					logger.info(" validateToken " + cellPhone + " token= " + token
							+ " token Error!");
					return  "fore/jsp/failure";
				}
				boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
				logger.info(cellPhone+" save to memcache "+b);
				}
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				if (pageNo == null || pageNo.intValue() <= 0)
					pageNo = 1;
				PageBean pageBean = new PageBean();
				pageBean.setCurrentPage(pageNo);
				if (pageSize == null || pageSize.intValue() <= 0)
					pageSize = Constant.DEFAULT_PAGE_SIZE_PC;
				pageBean.setPageSize(pageSize);
				User nowUser=userService.getUserByCellphone(getMemcache(request));
				Long userId=nowUser.getId();
				List<Collect>   collects=pcService.getList(" entity.userId="+userId+" and entity.collectStatus="+Constant.COLLECT_STATUS1+" and (entity.insureId>0)"+"order by ctime desc", pageBean);
				List<Insure> pcInsure=new ArrayList<Insure>();
				for(int i=0;i<collects.size();i++){
						if(collects.get(i).getInsureId()!=null){
							pcInsure.add(insureService.getById(collects.get(i).getInsureId()));
						}
				}
				request.setAttribute("pcInsure",pcInsure);
				request.setAttribute("page", pageBean);
				request.setAttribute("pageNo", pageNo);
				collects=null;
				return "fore/jsp/pcInsure";
			} catch (Exception e) {
				e.printStackTrace();
			} 
    	    return null;
     }
   */
    //已审核带车广告信息
    @RequestMapping("takecar/list")
    private String takeCarList(@RequestParam(value = "token", defaultValue = "") String token,
			@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
			Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
    	    try {
				if(StringUtils.hasLength(cellPhone)){
				if (!validateToken(cellPhone, token, request, response)) {
					logger.info(" validateToken " + cellPhone + " token= " + token
							+ " token Error!");
					return  "fore/jsp/failure";
				}
				boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
				logger.info(cellPhone+" save to memcache "+b);
				}
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				if (pageNo == null || pageNo.intValue() <= 0)
					pageNo = 1;
				PageBean pageBean = new PageBean();
				pageBean.setCurrentPage(pageNo);
				if (pageSize == null || pageSize.intValue() <= 0)
					pageSize = Constant.DEFAULT_PAGE_SIZE_PC;
				pageBean.setPageSize(pageSize);
				User nowUser=userService.getUserByCellphone(getMemcache(request));
				Long userId=nowUser.getId();
				List<Collect>   collects=pcService.getList(" entity.userId="+userId+" and entity.collectStatus="+Constant.COLLECT_STATUS1+" and entity.takeCarId>0"+"order by ctime desc", pageBean);
				List<TakeCar> pcTakeCar=new ArrayList<TakeCar>();
				for(int i=0;i<collects.size();i++){
						if(collects.get(i).getTakeCarId()!=null){
							pcTakeCar.add(takeCarService.getById(collects.get(i).getTakeCarId()));
						}
				}
				request.setAttribute("pcTakeCar",pcTakeCar);
				request.setAttribute("page", pageBean);
				request.setAttribute("pageNo", pageNo);
				collects=null;
				pcTakeCar=null;
				return "fore/jsp/pcTakeCar";
			} catch (Exception e) {
				e.printStackTrace();
			} 
    	    return null;
    }
    
      //招聘根据id删除已审核信息
	   @RequestMapping(value="employee/delete")
	   public String deleteEmp1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     Employee emp=employeeService.getById(id);
		     int status=emp.getStatus();
		     emp.setStatus(Constant.INFO_STATUS_DISABLE);
		     employeeService.update(emp);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	  //招聘根据id删除回收站信息
	   @RequestMapping(value="employee/never")
	   public String NeverEmp1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     Employee emp=employeeService.getById(id);
		     int status=emp.getStatus();
		     emp.setStatus(Constant.INFO_STATUS_NEVER);
		     employeeService.update(emp);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	   
	   
	   //求职根据id删除已审核信息
	   @RequestMapping(value="seek/delete")
	   public String deleteSeek1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     Employee seek=employeeService.getById(id);
		     int status=seek.getStatus();
		     seek.setStatus(Constant.INFO_STATUS_DISABLE);
		     employeeService.update(seek);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	 //求职根据id删除回收站信息
	   @RequestMapping(value="seek/never")
	   public String neverSeek1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     Employee seek=employeeService.getById(id);
		     int status=seek.getStatus();
		     seek.setStatus(Constant.INFO_STATUS_NEVER);
		     employeeService.update(seek);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	   
	   //待售根据id删除已审核信息
	   @RequestMapping(value="secondcar/delete")
	   public String deleteSecondCar1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     SecondCar secondCar=secondCarService.getById(id);
		     int status=secondCar.getStatus();
		     secondCar.setStatus(Constant.INFO_STATUS_DISABLE);
		     secondCarService.update(secondCar);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	   //待售根据id删除回收站信息
	   @RequestMapping(value="secondcar/never")
	   public String neverSecondCar1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     SecondCar secondCar=secondCarService.getById(id);
		     int status=secondCar.getStatus();
		     secondCar.setStatus(Constant.INFO_STATUS_NEVER);
		     secondCarService.update(secondCar);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	   //求购根据id删除已审核信息
	   @RequestMapping(value="forsecond/delete")
	   public String deleteForSecond1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     SecondCar forSecond=secondCarService.getById(id);
		     int status=forSecond.getStatus();
		     forSecond.setStatus(Constant.INFO_STATUS_DISABLE);
		     secondCarService.update(forSecond);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	 //求购根据id删除已审核信息
	   @RequestMapping(value="forsecond/never")
	   public String neverForSecond1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     SecondCar forSecond=secondCarService.getById(id);
		     int status=forSecond.getStatus();
		     forSecond.setStatus(Constant.INFO_STATUS_NEVER);
		     secondCarService.update(forSecond);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	   
	  //新车资讯根据id删除已审核信息
	   @RequestMapping(value="newcar/delete")
	   public String deleteNewCar1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     NewCar newCar=newCarService.getById(id);
		     int status=newCar.getStatus();
		     newCar.setStatus(Constant.INFO_STATUS_DISABLE);
		     newCarService.update(newCar);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	 //新车资讯根据id删除回收站信息
	   @RequestMapping(value="newcar/never")
	   public String neverNewCar1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     NewCar newCar=newCarService.getById(id);
		     int status=newCar.getStatus();
		     newCar.setStatus(Constant.INFO_STATUS_NEVER);
		     newCarService.update(newCar);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	   //保险广告根据id删除已审核信息
//	   @RequestMapping(value="insure/delete")
//	   public String deleteInsure1ById(Long id,HttpServletRequest request){
//	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
//		     Insure insure=insureService.getById(id);
//		     int status=insure.getStatus();
//		     insure.setStatus(Constant.INFO_STATUS_DISABLE);
//		     insureService.update(insure);
//			 return "redirect:/pc/allInfo/list?status="+status;
//	   }
	   
	 //保险广告根据id删除回收站信息
//	   @RequestMapping(value="insure/never")
//	   public String neverInsure1ById(Long id,HttpServletRequest request){
//	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
//		     Insure insure=insureService.getById(id);
//		     int status=insure.getStatus();
//		     insure.setStatus(Constant.INFO_STATUS_NEVER);
//		     insureService.update(insure);
//			 return "redirect:/pc/allInfo/list?status="+status;
//	   }
	   
	   //带车广告根据id删除已审核信息
	   @RequestMapping(value="takecar/delete")
	   public String deleteTakeCar1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     TakeCar takecar=takeCarService.getById(id);
		     int status=takecar.getStatus();
		     takecar.setStatus(Constant.INFO_STATUS_DISABLE);
		     takeCarService.update(takecar);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	  //带车广告根据id删除已审核信息
	   @RequestMapping(value="takecar/never")
	   public String neverTakeCar1ById(Long id,HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		     TakeCar takecar=takeCarService.getById(id);
		     int status=takecar.getStatus();
		     takecar.setStatus(Constant.INFO_STATUS_NEVER);
		     takeCarService.update(takecar);
			 return "redirect:/pc/allInfo/list?status="+status;
	   }
	   //删除新车图片
	   @RequestMapping(value = "newcarimage/delete")
		public String deleteNewCarImage(Long id, String image,
				HttpServletRequest request) {
	       if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
		   NewCar newCar = newCarService.getById(id);
			String images = newCar.getImages();
			if (images.length() == images.indexOf(image) + image.length()) {
				if (images.indexOf(image) == 0)
					newCar.setImages("");
				else
					newCar.setImages(images.substring(0, images.indexOf(image) - 1));
			} else {
				newCar.setImages(images.substring(0, images.indexOf(image))
						+ ""
						+ images.substring(images.indexOf(image) + image.length()
								+ 1));
			}
			newCarService.update(newCar);
			ImageUtil.deleteImage(image, request);
			request.setAttribute("newcar", newCar);
			request.setAttribute("images", newCar.getImages().split(";"));
			request.setAttribute("delete", "删除图片成功!");
			return "redirect:/pc/allInfo/list2";
		}
	   //保险广告图片删除
//	   @RequestMapping(value = "insureimage/delete")
//		public String deleteInsureImage(Long id, String image,
//				HttpServletRequest request) {
//	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
//	 		
//		   Insure insure = insureService.getById(id);
//			String images = insure.getImages();
//			if (images.length() == images.indexOf(image) + image.length()) {
//				if (images.indexOf(image) == 0)
//					insure.setImages("");
//				else
//					insure.setImages(images.substring(0, images.indexOf(image) - 1));
//			} else {
//				    insure.setImages(images.substring(0, images.indexOf(image))
//						+ ""
//						+ images.substring(images.indexOf(image) + image.length()
//								+ 1));
//			}
//			insureService.update(insure);
//			ImageUtil.deleteImage(image, request);
//			request.setAttribute("insure", insure);
//			request.setAttribute("images", insure.getImages().split(";"));
//			request.setAttribute("delete", "删除图片成功!");
//			return "redirect:/pc/allInfo/list2";
//        } 
	   /*     @RequestMapping(value = "employee/findById")
		public String findEmployeeById(Long id, HttpServletRequest request) {
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
	    	Employee employee = employeeService.getById(id);
			request.setAttribute("employee", employee);
			return "fore/jsp/emp_edite";
		}
	    @RequestMapping(value = "seek/findById")
		public String findSeekById(Long id, HttpServletRequest request) {
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";

	    	Employee seek = employeeService.getById(id);
			request.setAttribute("seek", seek);
			return "fore/jsp/seek_edite";
		}
	    @RequestMapping(value = "secondcar/findById")
		public String findSecondCarById(Long id, HttpServletRequest request) {
	     	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
	    	SecondCar secondcar = secondCarService.getById(id);
			request.setAttribute("secondcar", secondcar);
			return "fore/jsp/secondCar_edite";
		}
	    @RequestMapping(value = "forsecond/findById")
		public String findforSecondById(Long id, HttpServletRequest request) {
	     	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";

	    	SecondCar forsecond = secondCarService.getById(id);
			request.setAttribute("forsecond", forsecond);
			return "fore/jsp/forSecond_edite";
		}
	    
	  //招聘信息修改
	    @RequestMapping(value = "employee/update")
	    public String updateEmployee(Employee employee,@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
				 HttpServletRequest request,HttpServletResponse response) {
		    if(StringUtils.hasLength(cellPhone)){
			if (!validateToken(cellPhone, token, request, response)) {
				logger.info(" validateToken " + cellPhone + " token= " + token
						+ " token Error!");
				return  "fore/jsp/failure";
			}
		    employee.setCtime(employeeService.getById(employee.getId()).getCtime());
		    employeeService.update(employee);
		    request.setAttribute("employee", employee);
		}
		 return "redirect:/pc/employee/list2";
	}

	 //求职信息修改
	      @RequestMapping(value = "seek/update")
		public String updateSeek(Employee seek,@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
				 HttpServletRequest request,HttpServletResponse response) {
		    if(StringUtils.hasLength(cellPhone)){
			if (!validateToken(cellPhone, token, request, response)) {
				logger.info(" validateToken " + cellPhone + " token= " + token
						+ " token Error!");
				return  "fore/jsp/failure";
			}
		    seek.setCtime(employeeService.getById(seek.getId()).getCtime());
		    employeeService.update(seek);
		    request.setAttribute("seek", seek);
		}
			return "redirect:/pc/seek/list2";
		}
	       //待售二手车信息修改
	    @RequestMapping(value = "secondcar/update")
	    public String updateSecondCar(SecondCar secondCar,@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
				 HttpServletRequest request,HttpServletResponse response,MultipartFile images2){
	    	if(StringUtils.hasLength(cellPhone)){
				if (!validateToken(cellPhone, token, request, response)) {
					logger.info(" validateToken " + cellPhone + " token= " + token
							+ " token Error!");
					return  "fore/jsp/failure";
				}
	    	}	
			secondCar.setCtime(secondCarService.getById(secondCar.getId()).getCtime());
			StringBuffer buffer = new StringBuffer(secondCar.getImages());
			if (!images2.isEmpty()) {
				if (StringUtils.hasLength(secondCar.getImages()))
					buffer.append(";");
				buffer.append(ImageUtil.uploadImage("uploadSecondCarImages", images2,request));
			}
			secondCar.setImages(buffer.toString());
			secondCarService.update(secondCar);
			request.setAttribute("secondCar", secondCar);
			if (secondCar.getImages() != null){
			request.setAttribute("images", secondCar.getImages().split(";"));
			}
			return "redirect:/pc/secondcar/list2";
	    }
			
	
		 //求购二手车信息修改
	    @RequestMapping(value = "forsecond/update")
		public String updateForSecond(SecondCar forsecond,@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
				 HttpServletRequest request,HttpServletResponse response,MultipartFile images2){
	    	if(StringUtils.hasLength(cellPhone)){
				if (!validateToken(cellPhone, token, request, response)) {
					logger.info(" validateToken " + cellPhone + " token= " + token
							+ " token Error!");
					return  "fore/jsp/failure";
				}
	    	}
		forsecond.setCtime(secondCarService.getById(forsecond.getId()).getCtime());
		StringBuffer buffer = new StringBuffer(forsecond.getImages());
		if (!images2.isEmpty()) {
			if (StringUtils.hasLength(forsecond.getImages()))
				buffer.append(";");
			buffer.append(ImageUtil.uploadImage("uploadSecondCarImages", images2,request));
		}
			forsecond.setImages(buffer.toString());
			secondCarService.update(forsecond);
			request.setAttribute("forsecond", forsecond);
		if (forsecond.getImages() != null){
		request.setAttribute("images", forsecond.getImages().split(";"));
		}
			return "redirect:/pc/forsecond/list2";
		}*/
	    
	   /*public  String encodeStr(String str) {  
	        try {  
	            return new String(str.getBytes("ISO-8859-1"), "UTF-8");  
	        } catch (UnsupportedEncodingException e) {  
	            e.printStackTrace();  
	            return null;  
	        }  
	    } */ 

	    @RequestMapping(value="user/list")
	    public String UserList(
	    		@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
				@RequestParam(value = "msg", defaultValue = "") String msg,
	    		HttpServletRequest request,HttpServletResponse response){
	    	   try {
				if(StringUtils.hasLength(cellPhone)){
					if (!validateToken(cellPhone, token, request, response)) {
						logger.info(" validateToken " + cellPhone + " token= " + token
								+ " token Error!");
						return  "fore/jsp/failure";
					}
				    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
				    logger.info(cellPhone+" save to memcache "+b);
				     }
				    
				  if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    PersonalCenterUser pUser=new PersonalCenterUser();
					User user=userService.getUserByCellphone(getMemcache(request));
					Long userId=user.getId();
					List<PersonalCenterCar> carList=carService.getQueryCarList(userId,"1");
//					List<QueryUserTel> telList=userTelService.getTelsById(userId);
					BeanUtils.copyProperties(user, pUser);
					PersonalCenterBean center=new PersonalCenterBean(pUser, carList);
					request.setAttribute("center", center);
					request.setAttribute("user", user);
				    return "fore/jsp/pcUser";
			} catch (Exception e) {
				e.printStackTrace();
			}
	    	   return null;
	       }
	   /*@RequestMapping(value="user/list")
	    public String UserList(
	    		@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
				@RequestParam(value = "msg", defaultValue = "") String msg,
	    		HttpServletRequest request,HttpServletResponse response){
	    	   try {
				if(StringUtils.hasLength(cellPhone)){
					if (!validateToken(cellPhone, token, request, response)) {
						logger.info(" validateToken " + cellPhone + " token= " + token
								+ " token Error!");
						return  "fore/jsp/failure";
					}
				    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
				    logger.info(cellPhone+" save to memcache "+b);
				     }
				    
				  if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					User nowUser=userService.getUserByCellphone(getMemcache(request));
					String trueName=nowUser.getTrueName();
					if(nowUser!=null&&StringUtils.hasLength(trueName)){
						int length=trueName.length();
						if(length==2){
							trueName=trueName.substring(0,1)+"*";
						}else if(length>=3){
							StringBuffer buffer=new StringBuffer();
							for(int i=0;i<length-2;i++){
								buffer.append("*");
							}
							trueName=trueName.substring(0,1)+buffer.toString()+trueName.substring(length-1, length);
						}
						
					}
					
					nowUser.setTrueName(trueName);
					request.setAttribute("nowUser", nowUser);
					request.setAttribute("msg", msg);
				    return "fore/jsp/pcUser";
			} catch (Exception e) {
				logger.error("pc旧版个人中心异常");
				e.printStackTrace();
			}
			return null;
	       }*/
	   
	    @RequestMapping(value="user/telbook/list")
	    public String getTelBookList(
	    		@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
	    		HttpServletRequest request,HttpServletResponse response){
	    	 try {
	    		 if(StringUtils.hasLength(cellPhone)){
						if (!validateToken(cellPhone, token, request, response)) {
							logger.info(" validateToken " + cellPhone + " token= " + token
									+ " token Error!");
							return  "fore/jsp/failure";
						}
					    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
					    logger.info(cellPhone+" save to memcache "+b);
					     }
				cellPhone=getMemcache(request);    
				if(!StringUtils.hasLength(cellPhone))return  "fore/jsp/failure";
				User user=userService.getUserByCellphone(cellPhone);
				request.setAttribute("cellPhone", cellPhone);
				List<QueryUserTel> tels=userTelService.getTelsById(user.getId());
				request.setAttribute("tels", tels);
				return "fore/jsp/pc_telbook";
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
	    }
	    
	    @RequestMapping(value="user/telbook/delete")
	    public String deleteTelBook(Long[] idList,
	    		HttpServletRequest request,HttpServletResponse response){
	    	try {
				if(idList!=null&&idList.length>0){
					for(Long id:idList){
						userTelService.updateStatus(id, "2");
					}
				}
				return "redirect:/pc/user/telbook/list";
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
	    }
	    
	   /* @RequestMapping(value="user/telbook/add")
	    public String saveTelBook(
	    		String cellPhone,String homePhone,
	    		String verifyCode,String verifyCodeVoice,
	    		HttpServletRequest request,HttpServletResponse response){
	    	try {
	    		 String cell=getMemcache(request);
			     if(!StringUtils.hasLength(cell))return  "fore/jsp/failure";
				 if(StringUtils.hasLength(cellPhone)){
					if(readCookie("PC_WEB_VERIFY_CODE", request).equals(Encoder.md5(verifyCode))){
						添加
						User user=userService.getUserByCellphone(cell);
						if(cellPhone.equals(user.getCellPhone())){
							logger.info("pc_web联系电话添加自己");
							request.setAttribute("msg", "您不能添加自己");
						}
						检查是否已添加？
						if(userTelService.get(user.getId(),cellPhone)){
							logger.info("pc_web联系人已经存在");
							request.setAttribute("msg", "pc_web联系人已经存在");
						}
						添加到数据库
						UserTel usertel=new UserTel(user.getId(), cellPhone,"1");
						userTelService.add(usertel);
						//添加成功
					}else{
						//验证码错误
					}
				}
				if(StringUtils.hasLength(homePhone)){
					if(readCookie("PC_WEB_VERIFY_CODE_VOICE", request).equals(Encoder.md5(verifyCodeVoice))){
						添加
						//添加成功
					}else{
						//验证码错误
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }*/

	    		
	    @RequestMapping(value = "advice/publish")
		public String advicePublish(Advice a,@RequestParam(value="version",defaultValue="0000")String version,
			@RequestParam(value="platId",defaultValue="1")Integer platId,HttpServletRequest request){
					a.setPlatId(platId);
			a.setVersion(version);
			a.setCtime(TimeUtil.getTimeStamp());
			a.setStatus(Constant.ADVICE_STATUS_NO);
			String mem_cellPhone=(String) cacheService.getObject(Constant.CACHE_INFO_CELLPHONE_KEY);
		    if(StringUtils.hasLength(mem_cellPhone))a.setCellPhone(mem_cellPhone);
			adviceService.add(a);
			request.setAttribute("a", a);
			return "fore/jsp/thanks";
		}
	    
	    @RequestMapping(value="/toadvice")
		public String toAdvice(HttpServletRequest request){
	    	if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					request.setAttribute("cellPhone", getMemcache(request));
				return "fore/jsp/advice";
		}
	    
	    @RequestMapping(value="collect/list")
        public String collectList(
        		@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
				Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
        	    try {
					if(StringUtils.hasLength(cellPhone)){
					if (!validateToken(cellPhone, token, request, response)) {
						logger.info(" validateToken " + cellPhone + " token= " + token
								+ " token Error!");
						return  "fore/jsp/failure";
					}
					boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
					logger.info(cellPhone+" save to memcache "+b);
					}
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					if (pageNo == null || pageNo.intValue() <= 0)
						pageNo = 1;
					PageBean pageBean = new PageBean();
					pageBean.setCurrentPage(pageNo);
					if (pageSize == null || pageSize.intValue() <= 0)
						pageSize = Constant.DEFAULT_PAGE_SIZE_PC;
					pageBean.setPageSize(pageSize);
					User nowUser=userService.getUserByCellphone(getMemcache(request));
					Long userId=nowUser.getId();
					/*
					 * 运输信息收藏
					 * StringBuffer sql =new StringBuffer();
					sql.append(" entity.cellPhone="+nowUser.getCellPhone());
					sql.append(" and entity.status=1 ");
					sql.append(" and entity.ctime>='"+TimeUtil.formatDate(TimeUtil.dateDiff(-6))+"'");
					
					List<Transport> transports=new ArrayList<Transport>();
					List<TransportCollect> trcollects=transportCollectService.getList(sql.toString(),null);
					for(TransportCollect collect:trcollects){
						transports.add(infoCacheService.get(collect.getInfoId()));
					}*/
					List<Collect>   collects=pcService.getList(" entity.userId="+userId+" and entity.collectStatus="+Constant.COLLECT_STATUS1,pageBean);
					List<Employee>  employees=new ArrayList<Employee>();
					List<Employee>  seeks=new ArrayList<Employee>();
					List<SecondCar> secondcars=new ArrayList<SecondCar>();
					List<SecondCar> forseconds=new ArrayList<SecondCar>();
					List<NewCar>    newcars=new ArrayList<NewCar>();
//					List<Insure>    insures=new ArrayList<Insure>();
					List<TakeCar>   takecars=new ArrayList<TakeCar>();
					
					for(int i=0;i<collects.size();i++){
						if(collects.get(i).getEmployeeId()!=null){
						employees.add(employeeService.getById(collects.get(i).getEmployeeId()));
						}else{
							if(collects.get(i).getSeekId()!=null){
								seeks.add(employeeService.getById(collects.get(i).getSeekId()));	
							}else{
								if(collects.get(i).getSecondCarId()!=null){
									secondcars.add(secondCarService.getById(collects.get(i).getSecondCarId()));
								}else{
									if(collects.get(i).getForSecondId()!=null){
										forseconds.add(secondCarService.getById(collects.get(i).getForSecondId()));
									}else{
										if(collects.get(i).getNewCarId()!=null){
											newcars.add(newCarService.getById(collects.get(i).getNewCarId()));
										}else{
//											if(collects.get(i).getInsureId()!=null){
//						             	    	insures.add(insureService.getById(collects.get(i).getInsureId()));
//											}else{
//												
//											}
											if(collects.get(i).getTakeCarId()!=null){
							        	    	 takecars.add(takeCarService.getById(collects.get(i).getTakeCarId()));	
											}
										}
									}
								}
							}	
						}
					}
  //      	    request.setAttribute("transports",transports);
//					request.setAttribute("insures", insures);
					request.setAttribute("employees", employees);
					request.setAttribute("seeks", seeks);
					request.setAttribute("secondcars", secondcars);
					request.setAttribute("forseconds", forseconds);
					request.setAttribute("newcars", newcars);
					request.setAttribute("takecars", takecars);
					request.setAttribute("page", pageBean);
					request.setAttribute("pageNo", pageNo);
//					insures=null;
					employees=null;
					seeks=null;
					secondcars=null;
					newcars=null;
					takecars=null;
					return "fore/jsp/pcCollect";
				} catch (Exception e) {
					e.printStackTrace();
				}  
        	    return null;
	    }
	    @RequestMapping(value="transport/list")
        public String transportList(@RequestParam(value = "token", defaultValue = "") String token,
    			@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
    			Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
        	    try {
					if(StringUtils.hasLength(cellPhone)){
					if (!validateToken(cellPhone, token, request, response)) {
						logger.info(" validateToken " + cellPhone + " token= " + token
								+ " token Error!");
						return  "fore/jsp/failure";
					}
					boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
					logger.info(cellPhone+" save to memcache "+b);
					}
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					if (pageNo == null || pageNo.intValue() <= 0)
						pageNo = 1;
					PageBean pageBean = new PageBean();
					pageBean.setCurrentPage(pageNo);
					if (pageSize == null || pageSize.intValue() <= 0)
						pageSize = Constant.DEFAULT_PAGE_SIZE_PC;
					pageBean.setPageSize(pageSize);
					User nowUser=userService.getUserByCellphone(getMemcache(request));
					StringBuffer sql=new StringBuffer();
					sql.append(" entity.cellPhone='"+nowUser.getCellPhone()+"'");
					sql.append(" and entity.status=1");
					sql.append(" and entity.ctime>='"+TimeUtil.formatDate(TimeUtil.dateDiff(-6))+"'");
					sql.append(" order by entity.id desc");
					List<TransportCollect> collects=transportCollectService.getList(sql.toString(), pageBean);
					/*运输信息收藏*/
					List<Long> idList=new ArrayList<Long>();
					if(collects!=null)
						for(TransportCollect c:collects){
							idList.add(c.getInfoId());
						}
					/*获得Transport集合*/
					List<TransportMain> infoList=transportMainService.getListByIds(idList);
					logger.info("pc收藏信息idList:"+idList+"_infoListSize:"+infoList.size());
					//infoList=taskContent(infoList);
					List<TransportBean> transports=myPubContent(infoList);
					String beginTime = TimeUtil.formatDate(TimeUtil.today());
					String ctime=null;
					if(transports!=null)
					for(int i=0;i<transports.size();i++){
						    ctime=TimeUtil.formatDate(transports.get(i).getCtime());
							StringBuffer sql2=new StringBuffer();
							sql2.append(" entity.id >"+transports.get(i).getId());
							sql2.append(" and entity.taskContent like '%").append(StringUtil.subStr(transports.get(i).getTaskContent())).append("%'");
							sql2.append(" and entity.status=1");
							sql2.append(" order by id desc");
							List<Transport> transports2=transportService.getList(sql2.toString(),null);
					        if(transports2!=null&&transports2.size()>0){
					        	transportCollectService.updateId(transports.get(i).getId(), transports2.get(0).getId());
					        	transports.get(i).setStatus(transports2.get(0).getStatus());
					        	transports.get(i).setCtime(transports2.get(0).getCtime());
					        	transports.get(i).setId(transports2.get(0).getId());
					        }else{
					        	if(!ctime.equals(beginTime)){
					        		transports.get(i).setStatus(-1);
					        	}
					        }
					        transports.get(i).setTaskContent(StringUtil.subStr(transports.get(i).getTaskContent()));
					        transports2=null;
					        sql2=null;
					}
					/*for(int i=0;i<transports.size();i++){
						String taskContent=transports.get(i).getTaskContent();
						String  tel=transports.get(i).getTel();
						StringBuffer sql2=new StringBuffer();
						sql2.append(" entity.tel="+tel);
						sql2.append(" entity.taskContent="+taskContent);
						sql2.append(" order by id desc");
						List<Transport> transports2=transportService.getList(sql2.toString(),pageBean);
						for(int j=0;j<2;j++){
							Transport newestTransport=transports2.get(j);
						}
						
					}*/
					request.setAttribute("transports",transports);
					request.setAttribute("page", pageBean);
					request.setAttribute("pageNo", pageNo);
					idList=null;
					infoList=null;
					transports=null;
					return "fore/jsp/pcTransport";
				} catch (Exception e) {
					e.printStackTrace();
				}
        	    return null;
	    }
	    @RequestMapping(value="transport/detail")
	    public String transportGetById(
	    		Long id,HttpServletRequest request,HttpServletResponse response){
	    	String cellPhone=getMemcache(request);
	    	if(!StringUtils.hasLength(cellPhone))return  "fore/jsp/failure";
	    	TransportMain transport=transportMainService.getById(id);
	    	transport.setTaskContent(StringUtil.subStr(transport.getTaskContent()));
	    	request.setAttribute("transport",transport);
	    	User user=null;
	    	try{
		    		user=userService.getUserByCellphone(cellPhone);
		    		if(user!=null)request.setAttribute("serveDays",user.getServeDays());
		    		transport=null;
			    	user=null;
	    	}catch(Exception e){
	    		logger.info("pc transport detail Exception."+e.toString());
	    	}finally{
	    		
	    	}
	    	return "fore/jsp/pcTransportDetail";
	    }
	    
	    @RequestMapping(value="allInfo/list")
	    public String AllInformationList(@RequestParam(value = "token", defaultValue = "") String token,
				@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,@RequestParam(value = "status", defaultValue = "2")int status,
				Integer pageNo,Integer pageSize,HttpServletRequest request,HttpServletResponse response){
	    	    	if(StringUtils.hasLength(cellPhone)){
					if (!validateToken(cellPhone, token, request, response)) {
						logger.info(" validateToken " + cellPhone + " token= " + token
								+ " token Error!");
						return  "fore/jsp/failure";
					}
				    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
				    logger.info(cellPhone+" save to memcache "+b);
				    }
				    if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    if (pageNo == null || pageNo.intValue() <= 0)
		    			pageNo = 1;
		    		StringBuffer sql = new StringBuffer();
		    		sql.append(" entity.cellPhone="+getMemcache(request)+" and entity.status="+status);
					String sqlTo=sql.toString();
		    		List<Employee> employees=employeeService.getList(sqlTo+" and entity.distinguish="+1,null);
					List<Employee> seeks=employeeService.getList(sqlTo+" and entity.distinguish="+2,null);
	        	    List<SecondCar> secondcars=secondCarService.getList(sqlTo+" and entity.distinguish="+1,null);
	        	    List<SecondCar> forseconds=secondCarService.getList(sqlTo+" and entity.distinguish="+2,null);
	        	    List<NewCar>    newcars=newCarService.getList(sqlTo,null);
//	        	    List<Insure>    insures=insureService.getList(sqlTo,null);
//	        	    List<TakeCar>   takecars=takeCarService.getList(sqlTo,null);
	        	    List<TempInfo> infos=new ArrayList<TempInfo>();
	        	    
	        	    for(int i=0;i<employees.size();i++){
	        	    	TempInfo info=new TempInfo(employees.get(i).getId(), 1, employees.get(i).getTitle(), employees.get(i).getCtime(),employees.get(i).getStatus());
	        	    	infos.add(info);
	        	    	info=null;
	        	    }
	        	    employees=null;
	        	    for(int i=0;i<seeks.size();i++){
	        	    	TempInfo info=new TempInfo(seeks.get(i).getId(), 2, seeks.get(i).getTitle(), seeks.get(i).getCtime(),seeks.get(i).getStatus());
	        	    	infos.add(info);
	        	    	info=null;
	        	    }
	        	    seeks=null;
	        	    for(int i=0;i<secondcars.size();i++){
	        	    	TempInfo info=new TempInfo(secondcars.get(i).getId(), 3, secondcars.get(i).getTitle(), secondcars.get(i).getCtime(),secondcars.get(i).getStatus());
	        	    	infos.add(info);
	        	    	info=null;
	        	    }
	        	    secondcars=null;
	        	    for(int i=0;i<forseconds.size();i++){
	        	    	TempInfo info=new TempInfo(forseconds.get(i).getId(), 4, forseconds.get(i).getTitle(), forseconds.get(i).getCtime(),forseconds.get(i).getStatus());
	        	    	infos.add(info);
	        	    	info=null;
	        	    }
	        	    forseconds=null;
	        	    for(int i=0;i<newcars.size();i++){
	        	    	TempInfo info=new TempInfo(newcars.get(i).getId(), 5, newcars.get(i).getTitle(), newcars.get(i).getCtime(),newcars.get(i).getStatus());
	        	    	infos.add(info);
	        	    	info=null;
	        	    }
	        	    newcars=null;
//	        	    for(int i=0;i<insures.size();i++){
//	        	    	TempInfo info=new TempInfo(insures.get(i).getId(), 6, insures.get(i).getTitle(), insures.get(i).getCtime(),insures.get(i).getStatus());
//	        	    	infos.add(info);
//	        	    	info=null;
//	        	    }
//	        	    insures=null;
//	        	    for(int i=0;i<takecars.size();i++){
//	        	    	TempInfo info=new TempInfo(takecars.get(i).getId(), 7, takecars.get(i).getTitle(), takecars.get(i).getCtime(),takecars.get(i).getStatus());
//	        	    	infos.add(info);
//	        	    	info=null;
//	        	    }
//	        	    takecars=null;
	        	    /*查询指定页的集合employees*/
					Page page=new Page(infos);
					List<TempInfo> allInfos=page.getPage(pageNo);
	        	    request.setAttribute("status", status);
//	        	    request.setAttribute("employees", employees);
//	        	    request.setAttribute("seeks", seeks);
//	        	    request.setAttribute("secondcars",secondcars);
//	        	    request.setAttribute("forseconds",forseconds);
//	        	    request.setAttribute("newcars",newcars);
//	        	    request.setAttribute("insures",insures);
//	        	    request.setAttribute("takecars",takecars);
	        	    request.setAttribute("allInfos",allInfos);
	        	    request.setAttribute("page", page);
	    			request.setAttribute("pageNo", pageNo);
	    			allInfos=null;
	    	        return "fore/jsp/pcAllInfo";
	    }
	    
	    
	       //取消已收藏招聘信息
		   @RequestMapping(value="employee/cancel")
		   public String cancelEmp(Long id,Integer collectFlag,HttpServletRequest request){
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					User nowUser=userService.getUserByCellphone(getMemcache(request));
					Long userId=nowUser.getId();
					pcService.deleteCollectByEmpId(id, userId);
					if(collectFlag==1){
						return "redirect:/pc/employee/list";	
					}
					return "redirect:/pc/collect/list";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
		   }
		  //取消已收藏招聘信息
		   @RequestMapping(value="seek/cancel")
		   public String cancelSeek(Long id,Integer collectFlag,HttpServletRequest request){
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					User nowUser=userService.getUserByCellphone(getMemcache(request));
					pcService.deleteCollectBySeekId(id, nowUser.getId());
					if(collectFlag==1){
						return "redirect:/pc/employee/list";	
					}
					return "redirect:/pc/collect/list";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
		   }
		   
		   //取消已收藏待售二手车信息
		   @RequestMapping(value="secondcar/cancel")
		   public String cancelSecondCar(Long id,Integer collectFlag,HttpServletRequest request){
			   try {
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    User nowUser=userService.getUserByCellphone(getMemcache(request));
				    pcService.deleteCollectBySecondCarId(id, nowUser.getId());
				    if(collectFlag==1){
				    	return "redirect:/pc/secondcar/list";	
				    }
				   	return "redirect:/pc/collect/list";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		   }
		   
		 //取消已收藏求购二手车信息
		   @RequestMapping(value="forsecond/cancel")
		   public String cancelForSecond(Long id,Integer collectFlag,HttpServletRequest request){
			   try {
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    User nowUser=userService.getUserByCellphone(getMemcache(request));
				    pcService.deleteCollectByForSecondId(id, nowUser.getId());
				    if(collectFlag==1){
				    	return "redirect:/pc/secondcar/list";	
				    }
				   	return "redirect:/pc/collect/list";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		   }
		   
		 //取消已收藏新车信息
		   @RequestMapping(value="newcar/cancel")
		   public String cancelNewCar(Long id,Integer collectFlag,HttpServletRequest request){
			   try {
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    User nowUser=userService.getUserByCellphone(getMemcache(request));
				    pcService.deleteCollectByNewCarId(id, nowUser.getId());
				    if(collectFlag==1){
				    	return "redirect:/pc/newcar/list";	
				    }
				   	return "redirect:/pc/collect/list";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		   }
		   
		   
		 //取消已收藏保险广告信息
		   @RequestMapping(value="insure/cancel")
		   public String cancelInsure(Long id,Integer collectFlag,HttpServletRequest request){
			   try {
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    User nowUser=userService.getUserByCellphone(getMemcache(request));
				    pcService.deleteCollectByInsureId(id, nowUser.getId());
				    if(collectFlag==1){
				    	return "redirect:/pc/newcar/list";	
				    }
				   	return "redirect:/pc/collect/list";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		   }
		   
		 //取消已收藏带车信息
		   @RequestMapping(value="takecar/cancel")
		   public String cancelTakeCar(Long id,Integer collectFlag,HttpServletRequest request){
			   try {
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    User nowUser=userService.getUserByCellphone(getMemcache(request));
				    pcService.deleteCollectByTakeCarId(id, nowUser.getId());
				    if(collectFlag==1){
				    	return "redirect:/pc/takecar/list";	
				    }
				   	return "redirect:/pc/collect/list";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		   }
		   //取消已收藏货物信息
		   @RequestMapping(value="transport/cancel")
		   public String cancelTransport(Long id,HttpServletRequest request){
			   try {
				if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
				    User nowUser=userService.getUserByCellphone(getMemcache(request));
				    pcService.deleteTransportByTransportId(id,nowUser.getCellPhone());
				   	return "redirect:/pc/transport/list";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		   }
		   

		   @RequestMapping(value="/toList")
			public String toInsert(String to,HttpServletRequest request){
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					User user=userService.getUserByCellphone(getMemcache(request));
					request.setAttribute("blockFlag",2);
					request.setAttribute("cellPhone", user.getCellPhone());
					request.setAttribute("maxPage", 0);
					return "fore/jsp/"+to+"_List";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
		   
		   @RequestMapping(value="blockInfo/insert")
		   public String addBlockInfo(BlockInfo blockInfo,@RequestParam(value = "token", defaultValue = "") String token,
					@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,HttpServletRequest request,HttpServletResponse response){
			   try {
				if(StringUtils.hasLength(cellPhone)){
						if (!validateToken(cellPhone, token, request, response)) {
							logger.info(" validateToken " + cellPhone + " token= " + token
									+ " token Error!");
							return  "fore/jsp/failure";
						}
					    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
					    logger.info(cellPhone+" save to memcache "+b);
					     }
					  if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
						User nowUser=userService.getUserByCellphone(getMemcache(request));
						String informerTel=nowUser.getCellPhone();
						StringBuffer sql1= new StringBuffer();
						List<BlockInfo> myInfo=blockInfoService.getList(sql1.append(" entity.informerTel='"+informerTel+"'"+" and entity.blockerTel='"+FormatString.getNum(blockInfo.getBlockerTel()))+"'",null);
					    if(myInfo.size()>0){
					    	    request.setAttribute("error","您只能投诉同一个号码一次!");
					    }else{	
					    	    String tel=blockInfo.getBlockerTel().replaceAll("\\s*","");
					    	    if(tel.contains("-")){
					    	    	blockInfo.setAddress(mobileService.getByArea(tel.substring(0, tel.indexOf("-"))));
					    	    }else{
					    	    	blockInfo.setAddress(MobileUtil.getMobileAddress(tel));
					    	    }
					    	    tel=null;
								blockInfo.setBlockerTel(blockInfo.getBlockerTel().replaceAll("\\s*",""));
								blockInfo.setInformerTel(nowUser.getCellPhone());
								blockInfo.setQq(blockInfo.getQq());
								if(blockInfo.getBlockerName()!=null&&!blockInfo.getBlockerName().equals(""))blockInfo.setBlockerName(blockInfo.getBlockerName() .replaceAll("\\s*",""));
								blockInfo.setReason(FormatString.specialStringFilter(blockInfo.getReason()));
								blockInfo.setCtime(TimeUtil.getTimeStamp());
								blockInfo.setStatus(Constant.BLOCKIFO_STATUS_NO);
								request.setAttribute("success","感谢您提交宝贵信息，我们会尽快处理，打击骗子是我们共同的愿望。");
						        blockInfoService.add(blockInfo);
					    }
					    myInfo=null;
						request.setAttribute("blockFlag",2);
						request.setAttribute("maxPage", 0);
					    return "fore/jsp/pcBlock_List";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		   }
		   @RequestMapping("blockInfo/list")
		    private String blockInfoList(String cellPhone,BlockQueryBean blockQueryBean,Integer pageNo,Integer pageSize,
		    		@RequestParam(value = "token", defaultValue = "") String token,
		    		HttpServletRequest request,HttpServletResponse response
		    		){
			   try {
				if(StringUtils.hasLength(cellPhone)){
						if (!validateToken(cellPhone, token, request, response)) {
							logger.info(" validateToken " + cellPhone + " token= " + token
									+ " token Error!");
							return  "fore/jsp/failure";
						}
					    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
					    logger.info(cellPhone+" save to memcache "+b);
					     }
					    
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					StringBuffer sql = new StringBuffer();
					boolean hasfirst = false;
					if (StringUtils.hasLength(blockQueryBean.getBlockerTel())) {
						sql.append(" entity.blockerTel=")
								.append(blockQueryBean.getBlockerTel());
						if(!hasfirst)hasfirst = true;
					}
					if (StringUtils.hasLength(blockQueryBean.getAddress())) {
						if(hasfirst)sql.append(" and");
						sql.append(" entity.address like '%")
				        .append(blockQueryBean.getAddress()).append("%'");
						if(!hasfirst)hasfirst = true;
					}
					if (StringUtils.hasLength(blockQueryBean.getQq())) {
						if(hasfirst)sql.append(" and");
						sql.append(" entity.qq=")
				        .append(blockQueryBean.getQq());
						if(!hasfirst)hasfirst = true;
					}
					if(hasfirst)sql.append(" and");
					sql.append(" entity.status="+Constant.BLOCKIFO_STATUS_YES);
					sql.append(" order by entity.id desc ");
					if (pageNo == null || pageNo.intValue() <= 0)pageNo = 1;
				   		PageBean pageBean = new PageBean();
				   		pageBean.setCurrentPage(pageNo);
				   		if (pageSize == null || pageSize.intValue() <= 0)pageSize = Constant.DEFAULT_PAGE_SIZE_INFO_BLOCK;
				   		pageBean.setPageSize(pageSize);
						List<BlockInfo> blockInfos=blockInfoService.getList(sql.toString(),pageBean);
						logger.info("pc blocker_list:condition["+blockQueryBean+"]"+",RESULT:["+blockInfos+"]");
						request.setAttribute("blockinfos",blockInfos);
						request.setAttribute("blockFlag",1);
						request.setAttribute("page", pageBean);
						request.setAttribute("pageNo", pageNo);
						request.setAttribute("maxPage", pageBean.getMaxPage());
						blockInfos=null;
				        return "fore/jsp/pcBlock_List";
			} catch (Exception e) {
				e.printStackTrace();
			}
			   return null;
		    }
		   
		// 从招聘列表页面查询招聘详细信息，emp_detail.jsp页面调用
			@RequestMapping(value = "employee/detail")
			public String detailEmployee(@RequestParam(value = "id", defaultValue = "0") Integer id,
					@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
					HttpServletRequest request,HttpServletResponse response) {
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					Employee e=employeeService.findById(id);
					String province=e.getProvince();
					String city=e.getCity();
					if(city!=null&&province!=null&&city.equals(province))
					e.setCity(null);
					else e.setCity(city);
					request.setAttribute("employee",e);
					e=null;
					return "fore/jsp/pcEmpDetail";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
			//从求职列表查看求职信息
			@RequestMapping(value = "seek/detail")
			public String detailSeek(@RequestParam(value = "id", defaultValue = "0") Integer id,@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
					HttpServletRequest request,HttpServletResponse response) {
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					Employee e=employeeService.findById(id);
					String province=e.getProvince();
					String city=e.getCity();
					if(city!=null&&province!=null&&city.equals(province))
					e.setCity(null);
					else e.setCity(city);
					String birthProvince=e.getBirthProvince();
					String birthCity=e.getBirthCity();
					if(birthCity!=null&&birthProvince!=null&&birthCity.equals(birthProvince))
					e.setBirthCity(null);
					else e.setBirthCity(birthCity);
					request.setAttribute("seek",e);
					return "fore/jsp/pcSeekDetail";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
			// 从待售二手车列表页面查询二手车详细信息
			@RequestMapping(value = "secondcar/detail")
			public String detailSecondCar(Integer id,@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
					HttpServletRequest request,HttpServletResponse response) {
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					SecondCar s3=secondCarService.findById(id);
					String province=s3.getProvince();
					String city=s3.getCity();
					if(city!=null&&province!=null&&city.equals(province))
						s3.setCity(null);
					else s3.setCity(city);
					if(s3!=null&&s3.getImages()!=null&&StringUtils.hasLength(s3.getImages()))
						request.setAttribute("images", s3.getImages().split(";"));
					request.setAttribute("secondCar", s3);
					return "fore/jsp/pcSecondDetail";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
			// 从待售二手车列表页面查询二手车详细信息
				@RequestMapping(value = "forsecond/detail")
				public String detailForSecond(
						Integer id,@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
						HttpServletRequest request,HttpServletResponse response) {
					try {
						if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
						SecondCar s4=secondCarService.findById(id);
						String province=s4.getProvince();
						String city=s4.getCity();
						if(city!=null&&province!=null&&city.equals(province))
							s4.setCity(null);
						else s4.setCity(city);
						request.setAttribute("forSecond",s4);
						return "fore/jsp/pcForSecondDetail" ;
					} catch (Exception e) {
						e.printStackTrace();
					}
					return null;
				}

			
			// 查询新车资讯详细信息,newCar_detail.jsp页面调用
			/*@RequestMapping(value = "insure/detail")
			public String detailInsure(Long id,Boolean collectFlag,@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
					HttpServletRequest request,HttpServletResponse response) {
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					Insure i=insureService.getById(id);
					String province=i.getProvince();
					String city=i.getCity();
					if(city!=null&&province!=null&&city.equals(province))
					i.setCity(null);
					else i.setCity(city);
					if (i!=null&&i.getImages()!=null&&StringUtils.hasLength(i.getImages()))request.setAttribute("images", i.getImages().split(";"));
					request.setAttribute("insure",i);
					return "fore/jsp/pcInsureDetail";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}*/
			// 查询新车资讯详细信息,newCar_detail.jsp页面调用
				@RequestMapping(value = "newcar/detail")
				public String detailNewCar(Long id,Boolean collectFlag,@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
						HttpServletRequest request,HttpServletResponse response) {
					try {
						if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
						NewCar n=newCarService.getById(id);
						String province=n.getProvince();
						String city=n.getCity();
						if(city!=null&&province!=null&&city.equals(province))
						n.setCity(null);
						else n.setCity(city);
						if(n!=null&&n.getImages()!=null&&StringUtils.hasLength(n.getImages()))
							request.setAttribute("images", n.getImages().split(";"));
						request.setAttribute("newCar",n);
						return "fore/jsp/pcNewDetail";
					} catch (Exception e) {
						e.printStackTrace();
					}
					return null;
				}
			// 查询保险详细信息,insure_list.jsp页面调用
			@RequestMapping(value = "takecar/detail")
			public String detailTakeCar(Long id,Boolean collectFlag,@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
					HttpServletRequest request,HttpServletResponse response) {
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					TakeCar t=takeCarService.getById(id);
					String province=t.getProvince();
					String city=t.getCity();
					if(city!=null&&province!=null&&city.equals(province))
					t.setCity(null);
					else t.setCity(city);
					request.setAttribute("takeCar", t);
					return "fore/jsp/pcTakeDetail";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}

			@RequestMapping(value = "/isOline")
			public String isOline(RedirectAttributes attributes,
					Long priceId,String cellPhone,
					Integer userType ,
					Long payMethodId,
					HttpServletRequest request,HttpServletResponse response){
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
//					String token=Encoder.md5(cellPhone+AppConfig.getProperty("tyt.private.key"));
					attributes.addAttribute("cellPhone", cellPhone);
//					attributes.addAttribute("token", token);
					attributes.addAttribute("priceId", priceId);
					attributes.addAttribute("payMethodId", payMethodId);
					return "redirect:/alipay/access";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
			
			@RequestMapping(value = "/toGetResource")
			public String toGetResource(RedirectAttributes attributes,
					String cellPhone,Integer type ,
					HttpServletRequest request,HttpServletResponse response){
				try {
					if(!StringUtils.hasLength(getMemcache(request)))return  "fore/jsp/failure";
					String token=Encoder.md5(cellPhone+AppConfig.getProperty("tyt.private.key"));
					attributes.addAttribute("cellPhone", cellPhone);
					attributes.addAttribute("token", token);
					attributes.addAttribute("type", type);
					return "redirect:/resource/get";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
			/**
			 * 用户联系人电话本添加接口
			 * @param request
			 * @param response
			 */
			@RequestMapping(value = "/user/telbook/save",method=RequestMethod.POST)
			public String saveUserTel(
					String tel,String homephoneQuhao,
		    		String homephoneDianhua,String type,
					String verifyCode,String verifyCodeHome,
					HttpServletRequest request, HttpServletResponse response){
				try{
					String telPhone=tel;
					String very=verifyCode;
					if(type.equals("1")){
						telPhone=homephoneQuhao+homephoneDianhua;
						very=verifyCodeHome;
					}
					/*参数解析*/
					String cellPhone=getMemcache(request);
					if(!StringUtils.hasLength(cellPhone))return  "fore/jsp/failure";
					tel=tel.replaceAll(",", "");
					User user=userService.getUserByCellphone(cellPhone);
					String msg="添加成功";
					if(!StringUtils.hasLength(cellPhone)||!StringUtils.hasLength(very)){
						msg="电话号码或验证码不能为空";
					}
					boolean isSuccess=verifyLogService.verify(telPhone, very);
					if(!isSuccess){
						msg="验证码错误";
					}
					if(telPhone.equals(user.getCellPhone())){
						msg="联系人已经存在";
					}
					/*检查是否已添加？*/
					if(userTelService.get(user.getId(),telPhone)){
						logger.info("pc_web添加联系人已经存在");
						msg="联系人已经存在";
					}
										/*添加到数据库*/
					if(msg.equals("添加成功")){
						UserTel usertel=new UserTel(user.getId(), telPhone,"1",type);
						userTelService.add(usertel);
						request.setAttribute("msg", msg);
						request.setAttribute("tel", null);
						request.setAttribute("verifyCode", null);
						request.setAttribute("verifyCodeHome", null);
						request.setAttribute("homephoneQuhao", null);
						request.setAttribute("homephoneDianhua", null);
					}else{
						request.setAttribute("msg", msg);
						request.setAttribute("tel", tel);
						request.setAttribute("verifyCode", verifyCode);
						request.setAttribute("verifyCodeHome", verifyCodeHome);
						request.setAttribute("homephoneQuhao", homephoneQuhao);
						request.setAttribute("homephoneDianhua", homephoneDianhua);
					}
					request.setAttribute("type", type);
					return "fore/jsp/pc_tel_add";
				}catch(Exception e){
					request.setAttribute("msg", "请稍后重试");
					return "fore/jsp/pc_tel_add";
				}
			}
			
			@RequestMapping(value = "/myinfo/list")
			public String list(PersonalCenterTransportConditionBean info, Integer pageNo,
					Integer pageSize, HttpServletRequest request){
				try {
					String cellPhone=getMemcache(request);
					if(!StringUtils.hasLength(cellPhone))return  "fore/jsp/failure";
					User user=userService.getUserByCellphone(cellPhone);
					if (pageNo == null || pageNo.intValue() <= 0)
						pageNo = 1;
					PageBean pageBean = new PageBean();
					pageBean.setCurrentPage(pageNo);
					if (pageSize == null || pageSize.intValue() <= 0)
						pageSize = Constant.DEFAULT_PAGE_SIZE_INFO;
					pageBean.setPageSize(pageSize);
					List<PersonalCenterTransportBean> infos = transportMainService.getPersonalCenterTransportList(info,user.getId(), pageBean);
					
					request.setAttribute("info", info);
					request.setAttribute("infos", infos);
					request.setAttribute("rowCount", pageBean.getRowCount());
					request.setAttribute("page", pageBean);
					request.setAttribute("verifyFlag", user.getVerifyFlag());
					return "fore/jsp/pc_transport_list";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
			
			/**
			 * 个人中心货物管理详情
			 * @param id
			 * @param response
			 * @param request
			 * @return
			 */
			@RequestMapping(value = "/myinfo/detail")
			public String getTransportDetail(
					Long id,Integer pageNo,
					HttpServletResponse response,HttpServletRequest request){
				try {
					String cellPhone=getMemcache(request);
					if(!StringUtils.hasLength(cellPhone))return  "fore/jsp/failure";
					TransportMain info = transportMainService.getById(id);
					request.setAttribute("info", info);
					request.setAttribute("pageNo", pageNo);
					return "fore/jsp/pc_transport_detail";
				} catch (Exception e) {
					e.printStackTrace();
				}
				return null;
			}
			
			/**
			 * 返回信息的taskContent处理，为兼容老数据
			 * @param infos
			 * @return
			 */
			private List<TransportBean> myPubContent(List<TransportMain> infos){
				List<TransportBean> transportBeanList=new ArrayList<TransportBean>();
				if(infos!=null)
					for(TransportMain info:infos){
					info.setTaskContent(info.getPcOldContent());
					TransportBean transportBean=new TransportBean();
					BeanUtils.copyProperties(info, transportBean);
					transportBeanList.add(transportBean);
				}
				return transportBeanList.size()>0?transportBeanList:null;
			}
			
			@RequestMapping(value="/user/alipay")
		    public String getUser(
		    		@RequestParam(value = "token", defaultValue = "") String token,
					@RequestParam(value = "cellPhone", defaultValue = "") String cellPhone,
					@RequestParam(value = "clientSign", defaultValue = "1") Integer clientSign,
					@RequestParam(value = "channelId", defaultValue = "1") String channelId,
		    		HttpServletRequest request,HttpServletResponse response){
		    	 try {
		    		 if(StringUtils.hasLength(cellPhone)){
							if (!validateToken(cellPhone, token, request, response)) {
								logger.info(" validateToken " + cellPhone + " token= " + token
										+ " token Error!");
								return  "fore/jsp/failure";
							}
						    boolean b=cacheService.setObject(request.getSession().getId(), cellPhone,Constant.CACHE_EXPIRE_TIME_24H);
						    logger.info(cellPhone+" save to memcache "+b);
						     }
					cellPhone=getMemcache(request);    
					if(!StringUtils.hasLength(cellPhone))return  "fore/jsp/failure";
					User user=userService.getUserByCellphone(cellPhone);
					request.setAttribute("user", user);
					String userSign=Price.PRICE_CAR;
					if(user.getUserSign()==User.USER_SIGN_GOODS_OWNER||
							user.getUserSign()==User.USER_SIGN_EXCHANGE_STATION)
						userSign=Price.PRICE_GOODS;
					List<PriceCenterBean> priceList=priceService.getListByUserSign(userSign);
					request.setAttribute("priceList", priceList);
					//设置page对象
					PageBean page=new PageBean(1, 30);
					//获取 PC网站 有效的 支付银行列表
					List<ChannelBankQueryBean> channelBankList
					         =channelBankService.getChannelBank(
							         channelId, clientSign,ChannelBank.STATUS_ENABLE, page);
					request.setAttribute("channelBankList", channelBankList);
					return "fore/jsp/alipay";
				} catch (Exception e) {
					e.printStackTrace();
					return  "fore/jsp/failure";
				}
				
			}
			
}