package com.tyt.web.enums;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/11/9 14:11
 * @Version 1.0
 **/
public enum UserAcctStatusEnum {
    //用户状态（ACTIVATE_PENDING：待激活； CHECK_PENDING：待审核； REMITTANCE_VALID_PENDING：审核通过，待打款验证-企业用户使用； NORMAL：正常； CANCEL：销户）
    ACTIVATE_PENDING("待激活"),
    NORMAL("正常");

    private final String text;

    UserAcctStatusEnum(String text) {
        this.text = text;
    }

    public String getText() {
        return text;
    }
}
