package com.tyt.web.back.internal;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.CsBusinessUserBind;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.model.TytSource;
import com.tyt.service.advice.AdviceService;
import com.tyt.service.complaint.ComplaintRecordService;
import com.tyt.util.Constant;
import com.tyt.util.TytSourceUtil;
import com.tyt.web.back.internal.bean.ComplaintRecordCheckPhoneVO;
import com.tyt.web.back.internal.bean.ComplaintRecordSaveVO;
import com.tyt.web.back.internal.bean.ComplaintRecordTransportInfoVO;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.complaint.ComplaintHandleListBean;
import com.tyt.web.qbean.complaint.ComplaintRecordDetailsBean;
import com.tyt.web.qbean.complaint.ComplaintRecordQueryBean;
import com.tyt.web.qbean.complaint.SourceComplaintListBean;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 货源投诉controller
 */
@Controller
@RequestMapping("/complaint")
public class ComplaintController extends BaseController {

    @Autowired
    private ComplaintRecordService complaintRecordService;
    @Resource(name = "adviceService")
    private AdviceService adviceService;


    /**
     * 获取投诉列表
     */
    @RequestMapping("/queryRecordList")
    @ResponseBody
    public ResultMsgBean queryRecordList(@RequestParam(defaultValue = "1") Integer pageNo, int pageSize, ComplaintRecordQueryBean complaintRecordQueryBean) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            Map<String, Object> resultMap = complaintRecordService.queryRecordList(pageNo, pageSize, complaintRecordQueryBean);
            rm.setData(resultMap);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 获取投诉详情
     */
    @RequestMapping("/details")
    @ResponseBody
    public ResultMsgBean findDetails(Long id) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            ComplaintRecordDetailsBean details = complaintRecordService.findDetails(id);
            rm.setData(details);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping("/handleSave")
    @ResponseBody
    public ResultMsgBean handleSave(Long id, String result, Integer isEffective, Integer isFinal, HttpServletRequest request,
                                    Long quesOne, String quesOneName, Long quesTwo, String quesTwoName, Long quesThree, String quesThreeName,
                                    Long quesFour, String quesFourName, String replenishPictureUrl) {
        // 登陆验证
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            return new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN);
        }
        if (id == null || StringUtils.isBlank(result) || isEffective == null || isFinal == null) {
            return new ResultMsgBean(ResultMsgBean.ERROR, "参数有误");
        }
        try {

            complaintRecordService.addComplaintHandle(id, result, isEffective, isFinal, curUser, quesOne, quesOneName, quesTwo, quesTwoName, quesThree, quesThreeName, quesFour, quesFourName, replenishPictureUrl);
            return new ResultMsgBean(ResultMsgBean.OK, Constant.MSG_SAVE_OK);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("服务器异常", e);
            return new ResultMsgBean(ResultMsgBean.ERROR, "服务器错误");
        }
    }

    @RequestMapping("/queryComplainedRecordListBySourceId")
    @ResponseBody
    public ResultMsgBean queryComplainedRecordListBySourceId(Long msgId) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            List<SourceComplaintListBean> sourceComplaintListBeans = complaintRecordService.queryComplainedRecordListBySourceId(msgId);
            rm.setData(sourceComplaintListBeans);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping("/queryHandleListById")
    @ResponseBody
    public ResultMsgBean queryHandleListById(Long id) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            List<ComplaintHandleListBean> complaintHandleListBeans = complaintRecordService.queryHandleListById(id);
            rm.setData(complaintHandleListBeans);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping("/handlerList")
    @ResponseBody
    public ResultMsgBean queryHandlerList() {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            List<HashMap<String, Object>> handlerList = complaintRecordService.queryHandlerList();
            rm.setData(handlerList);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 生成工单
     *
     * @param id
     * @param complaintSource 1货源投诉 2意见反馈
     * @param request
     * @return
     */
    @RequestMapping("/createWordOrder")
    @ResponseBody
    public ResultMsgBean createWordOrder(Long id, Integer complaintSource,Long opUserId, String opUser, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            if (complaintSource == 1) {
                rm = complaintRecordService.createWordOrder(id, opUserId,opUser, curUser, rm);
                return rm;
            }
            if (complaintSource == 2) {
                rm = adviceService.createWordOrder(id,opUserId,opUser, curUser, rm);
            }
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 新增货源投诉单提交接口
     *
     * @param recordSaveVO
     * @param httpRequest
     * @return
     */
    @RequestMapping("/saveRecord/post")
    @ResponseBody
    public ResultMsgBean saveRecord(@RequestBody @Validated ComplaintRecordSaveVO recordSaveVO, HttpServletRequest httpRequest) {
        // 登陆验证
        EmployeeQueryBean curUser = getCurrentUser(httpRequest);
        if (curUser == null) {
            return new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN);
        }
        try {
            complaintRecordService.saveRecord(recordSaveVO);
            return new ResultMsgBean(ResultMsgBean.OK, Constant.MSG_SAVE_OK);
        } catch (Exception e) {
            logger.error("保存投诉异常", e);
            return new ResultMsgBean(ResultMsgBean.ERROR, "保存投诉异常");
        }
    }

    /**
     * 根据手机号查询用户是否存在（存在返回货方会员权益）
     *
     * @param phone 手机号
     * @return
     */
    @RequestMapping("/checkPhone/get")
    @ResponseBody
    public ResultMsgBean checkPhone(@RequestParam("phone") String phone, HttpServletRequest httpRequest) {
        EmployeeQueryBean curUser = getCurrentUser(httpRequest);
        if (curUser == null) {
            return new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN);
        }
        try {
            ComplaintRecordCheckPhoneVO result = complaintRecordService.checkPhone(phone);
            if (result == null) {
                return new ResultMsgBean(ResultMsgBean.USER_NOT_EXIST_ERROR_CODE, Constant.MSG_USER_NOT_EXIST);
            } else {
                return new ResultMsgBean(ResultMsgBean.OK, Constant.MSG_SAVE_OK, result);
            }
        } catch (Exception e) {
            logger.error("据手机号查询用户是否存在异常", e);
            return new ResultMsgBean(ResultMsgBean.ERROR, "据手机号查询用户是否存在异常");
        }
    }

    /**
     * 根据货源ID查询货源详情
     *
     * @param phone 手机号
     * @return
     */
    @RequestMapping("/transportInfo/get")
    @ResponseBody
    public ResultMsgBean transportInfo(@RequestParam("msgId") Long msgId, HttpServletRequest httpRequest) {
        EmployeeQueryBean curUser = getCurrentUser(httpRequest);
        if (curUser == null) {
            return new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN);
        }
        try {
            ComplaintRecordTransportInfoVO result = complaintRecordService.transportInfo(msgId);
            if (result == null) {
                return new ResultMsgBean(ResultMsgBean.TRANSPORT_NOT_EXIST_ERROR_CODE, Constant.MSG_TRANSPORT_NOT_EXIST);
            } else {
                return new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG, result);
            }
        } catch (Exception e) {
            logger.error("根据货源ID查询货源详情异常", e);
            return new ResultMsgBean(ResultMsgBean.ERROR, "根据货源ID查询货源详情异常");
        }
    }

    /**
     * 获取投诉分类字典列表
     *
     * @return
     */
    @RequestMapping("/typeDescSource/get")
    @ResponseBody
    public ResultMsgBean getTypeDescSource() {
        try {
            List<TytSource> typeDescSourceList = TytSourceUtil.getSourceList("complaint_record_type");
            return new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG, typeDescSourceList);
        } catch (Exception e) {
            logger.error("获取投诉分类字典列表异常", e);
            return new ResultMsgBean(ResultMsgBean.ERROR, "获取投诉分类字典列表异常");
        }
    }

    /**
     * 获取接单人列表
     *
     * @return ResultMsgBean
     */
    @RequestMapping("/opUserList")
    @ResponseBody
    public ResultMsgBean opUserList() {
        try {
            List<CsBusinessUserBind> opUserList = complaintRecordService.getOpUserList();
            return ResultMsgBean.successResponse(opUserList);
        } catch (Exception e) {
            logger.error("获取接单人列表异常", e);
            return ResultMsgBean.failResponse(e);
        }
    }
}
