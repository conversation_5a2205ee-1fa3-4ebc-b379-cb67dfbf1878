package com.tyt.web.back.internal.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/29 15:05
 */
public class TransportListBean implements Serializable {

    private static final long serialVersionUID = -130295681375625315L;
    /**
     * id
     */
    private Long id;

    /**
     * 信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
     */
    private String infoStatus;

    /**
     * 发布人姓名
     */
    private String nickName;
    /**
     * 发布时间
     */
    private Date pubTime;
    /**
     * 是否是标准化数据：0是，1不是
     */
    private String isStandard;
    /**
     * 货物名称
     */
    private String taskContent;
    /**
     * 出发地
     */
    private Long startPoint;
    /**
     * 目的地
     */
    private Long destPoint;
    /**
     * 更新时间
     */
    private Date mtime;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 手机号
     */
    private String tel;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 出发地详细地址
     */
    private String startAdd;
    /**
     * 目的地详细地址
     */
    private String destAdd;
    /**
     * 货物类型名称
     */
    private String goodTypeName;
    /**
     * 重量 单位吨
     */
    private String weight;
    /**
     * 货物长单位米
     */
    private String length;
    /**
     * 货物宽单位米
     */
    private String wide;
    /**
     * 货物高单位米
     */
    private String high;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 车辆最低长度
     */
    private BigDecimal carMinLength;
    /**
     * 车辆最大长度
     */
    private BigDecimal getCarMaxLength;
    /**
     * 运费
     */
    private String price;
    /**
     * 车辆类型
     */
    private String carType;
    /**
     * 挂车样式
     */
    private String carStyle;
}