package com.tyt.web.back.internal.bean;

import java.util.Date;

public class OrderQueryBean {

	String cellPhone;
	Integer payStatus;
	String payMethod;
	Integer status;
	Integer totalFee;
	Integer platId;
	String payChannel;
	String opType;// 操作类型 1自动 2手动
	String note;// 备注
	/*
	 * 订单类型 0 所有 1 信息费 2 会员费
	 */
	private Integer orderType = 0;

	private String orderId;
	private Date startCtime;
	private Date endCtime;

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public Integer getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(Integer payStatus) {
		this.payStatus = payStatus;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(Integer totalFee) {
		this.totalFee = totalFee;
	}

	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}

	public String getPayChannel() {
		return payChannel;
	}

	public void setPayChannel(String payChannel) {
		this.payChannel = payChannel;
	}

	public String getOpType() {
		return opType;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public Date getStartCtime() {
		return startCtime;
	}

	public void setStartCtime(Date startCtime) {
		this.startCtime = startCtime;
	}

	public Date getEndCtime() {
		return endCtime;
	}

	public void setEndCtime(Date endCtime) {
		this.endCtime = endCtime;
	}
}
