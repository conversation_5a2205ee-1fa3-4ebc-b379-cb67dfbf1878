package com.tyt.web.back.internal;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSource;
import com.tyt.mybatis.mapper.model.*;
import com.tyt.service.user.UserV6000Service;
import com.tyt.util.TytSourceUtil;
import com.tyt.web.back.internal.bean.UserCarQueryReqVO;
import com.tyt.web.back.internal.bean.UserGoodsQueryReqVO;
import com.tyt.web.back.internal.bean.UserQueryReqVO;
import com.tyt.web.back.internal.bean.UserVisitorQueryReqVO;
import com.tyt.web.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/admin/v6000")
public class UserV6000Controller extends BaseController {

    @Autowired
    private UserV6000Service userV6000Service;

    @RequestMapping(value = "/source/list", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean source(HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            List<TytSource> sourceList = TytSourceUtil.getSourceList("source");
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(sourceList);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询用户来源异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("查询出错");
            return resultMsgBean;
        }
    }

    @RequestMapping(value = "/user/list", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean userList(UserQueryReqVO vo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            QueryResultModel<UserInfoListModel> queryReuslt = userV6000Service.listAllUser(vo);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(queryReuslt);

            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询用户资料异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("查询出错");
            return resultMsgBean;
        }
    }


    @RequestMapping(value = "/user/info", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean userInfo(int userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            Map<String, Object> detailInfo = userV6000Service.getUserDetailInfo(userId);
            resultMsgBean.setData(detailInfo);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询用户资料异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("查询出错");
            return resultMsgBean;
        }
    }

    @RequestMapping(value = "/user/pay/info", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean userPayInfo(int userId, int goodsType) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            List<UserPayInfoModel> payInfo = userV6000Service.listUserPayInfo(userId, goodsType);
            resultMsgBean.setData(payInfo);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询用户资料异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("查询出错");
            return resultMsgBean;
        }
    }


    @RequestMapping(value = "/user/car/list", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean userCarList(UserCarQueryReqVO vo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            QueryResultModel<UserCarInfoListModel> queryResult = userV6000Service.listCarUser(vo);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(queryResult);

            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询用户资料异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("查询出错");
            return resultMsgBean;
        }
    }

    @RequestMapping(value = "/user/goods/list", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean userGoodsList(UserGoodsQueryReqVO vo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            QueryResultModel<UserGoodsInfoListModel> queryResult = userV6000Service.listGoodsUser(vo);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(queryResult);

            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询用户资料异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("查询出错");
            return resultMsgBean;
        }
    }


    @RequestMapping(value = "/user/visitor/list", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean userVisitorList(UserVisitorQueryReqVO vo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            QueryResultModel<UserVisitorInfoListModel> queryResult = userV6000Service.listVisitorUser(vo);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("查询成功");
            resultMsgBean.setData(queryResult);

            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询用户资料异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("查询出错");
            return resultMsgBean;
        }
    }


}
