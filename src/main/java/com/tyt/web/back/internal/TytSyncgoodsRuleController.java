package com.tyt.web.back.internal;

import com.github.pagehelper.PageInfo;
import com.tyt.manager.entity.base.TytSyncgoodsRuleGroup;
import com.tyt.manager.service.dictionary.TytSyncgoodsRuleService;
import com.tyt.manager.service.dictionary.TytSyncgoodsUserService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.mybatis.mapper.model.QueryResultModel;
import com.tyt.web.back.internal.bean.SyncgoodsRuleList;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 货源同步规则管理
 */
@RequestMapping(value = "syncgoodsRule")
@RestController
public class TytSyncgoodsRuleController {

    @Autowired
    private TytSyncgoodsRuleService tytSyncgoodsRuleService;
    @Autowired
    private TytSyncgoodsUserService tytSyncgoodsUserService;

    /**
     * 新增规则
     * @param ruleName
     * @return
     */
    @RequestMapping(value = "add/syncGoodsRule")
    public ResultMsgBean addSyncGoodsRule(String ruleName) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        if (StringUtils.isEmpty(ruleName)) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("缺少必要参数");
            return resultMsgBean;
        }
        TytSyncgoodsRuleGroup tytSyncgoodsRule = tytSyncgoodsRuleService.getByRuleName(ruleName);
        if (Objects.nonNull(tytSyncgoodsRule)) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("此分组名称已经存在");
            return resultMsgBean;
        }
        int countRule = tytSyncgoodsRuleService.countRule();
        if (countRule >= 100){
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("分组已达到上限");
            return resultMsgBean;
        }
        resultMsgBean.setCode(ReturnCodeConstant.OK);
        resultMsgBean.setMsg("成功");
        tytSyncgoodsRuleService.addSyncGoodsRule(ruleName);
        return resultMsgBean;
    }

    /**
     * 查询同步规则列表
     * @param pageSize
     * @param pageNum
     * @return
     */
    @RequestMapping(value = "get/syncgoods/list")
    public ResultMsgBean getSyncgoodsList(@RequestParam(value = "pageSize", defaultValue = "20", required = false) Integer pageSize,
                                          @RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        List<SyncgoodsRuleList> syncgoodsList = tytSyncgoodsRuleService.getSyncgoodsList(pageNum, pageSize);
        PageInfo<SyncgoodsRuleList> pageInfo = new PageInfo<>(syncgoodsList);
        QueryResultModel<SyncgoodsRuleList> queryReuslt = new QueryResultModel<>();
        queryReuslt.setTotal(pageInfo.getTotal());
        queryReuslt.setList(syncgoodsList);
        resultMsgBean.setCode(ReturnCodeConstant.OK);
        resultMsgBean.setMsg("查询成功");
        resultMsgBean.setData(queryReuslt);
        return resultMsgBean;
    }

    /**
     * 启用、停用
     * @param id
     * @param ruleStatus 状态(0停用；1 启用)
     * @return
     */
    @RequestMapping(value = "update/rulestatus")
    public ResultMsgBean updateRuleStatus(Long id, Integer ruleStatus) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        if (Objects.isNull(id) || Objects.isNull(ruleStatus)) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("缺少必要参数");
            return resultMsgBean;
        }
        TytSyncgoodsRuleGroup syncGoodsRule = tytSyncgoodsRuleService.findSyncGoodsRuleGroup(id);
        if (Objects.isNull(syncGoodsRule)) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("规则不存在");
            return resultMsgBean;
        }
        Integer countGroupUser = tytSyncgoodsUserService.countGroupUser(id);
        if (Objects.isNull(countGroupUser) || countGroupUser == 0) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("请先导入用户");
            return resultMsgBean;
        }
        tytSyncgoodsRuleService.updateRuleStatus(id, ruleStatus);
        resultMsgBean.setCode(ReturnCodeConstant.OK);
        resultMsgBean.setMsg("成功");
        return resultMsgBean;
    }

    /**
     * 删除规则
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "delete/rule")
    public ResultMsgBean deleteRule(Long id) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        if (Objects.isNull(id)) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("缺少必要参数");
            return resultMsgBean;
        }
        TytSyncgoodsRuleGroup syncGoodsRule = tytSyncgoodsRuleService.findSyncGoodsRuleGroup(id);
        if (Objects.isNull(syncGoodsRule)) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("规则不存在");
            return resultMsgBean;
        }
        if (syncGoodsRule.getStatus() == 1) {
            resultMsgBean.setCode(ReturnCodeConstant.OTHER_ERROR);
            resultMsgBean.setMsg("启用状态不允许删除");
            return resultMsgBean;
        }
        tytSyncgoodsRuleService.deleteRule(id);
        resultMsgBean.setCode(ReturnCodeConstant.OK);
        resultMsgBean.setMsg("成功");
        return resultMsgBean;
    }

}
