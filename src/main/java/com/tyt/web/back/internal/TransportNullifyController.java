package com.tyt.web.back.internal;

import com.tyt.model.PageBean;
import com.tyt.service.transport.TransportNullifyService;
import com.tyt.service.transport.TransportService;
import com.tyt.service.user.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import com.tyt.model.TransportNullifyBean;
import com.tyt.web.base.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

@Controller
@RequestMapping("/back/transport/nullify")
public class TransportNullifyController extends BaseController{

	@Resource(name = "transportNullifyService")
	private TransportNullifyService transportNullifyService;

	@Resource(name= "transportService")
	private TransportService transportService;

	@Resource(name = "userService")
	private UserService userService;
	/**
	 * 查询无效货源列表
	 * @param condition 查询条件
	 * @param pageNo     页码
	 * @param pageSize   每页大小
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/list")
	public String get(TransportNullifyBean condition,Integer pageNo,
			Integer pageSize,HttpServletRequest request,HttpServletResponse response){
		//检查登录状态
		if (!checkLogin(request)) {
			request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
			return "back/jsp/admin_login";
		}

		StringBuffer sql = new StringBuffer(" 1=1 ");
		boolean hasfirst = false;
		if (StringUtils.hasLength(condition.getStartProvinc())) {
			sql.append(" and entity.startProvinc='").append(condition.getStartProvinc().trim()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if (StringUtils.hasLength(condition.getStartCity())) {
			sql.append(" and entity.startCity='").append(condition.getStartCity().trim()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if (StringUtils.hasLength(condition.getStartArea()) && !condition.getStartArea().equals(condition.getStartCity())) {
			sql.append(" and entity.startArea='").append(condition.getStartArea().trim()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if (StringUtils.hasLength(condition.getDestProvinc())) {
			sql.append(" and entity.destProvinc='").append(condition.getDestProvinc().trim()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if (StringUtils.hasLength(condition.getDestCity())) {
			sql.append(" and entity.destCity='").append(condition.getDestCity().trim()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if (StringUtils.hasLength(condition.getDestArea()) && !condition.getDestArea().equals(condition.getDestCity())) {
			sql.append(" and entity.destArea='").append(condition.getDestArea().trim()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		//上传手机号
//		if (StringUtils.hasLength(condition.getUploadCellPhone())) {
//			sql.append(" and  entity.uploadCellPhone='").append(condition.getUploadCellPhone()).append("'");
//			if (!hasfirst)hasfirst = true;
//		}
		//关键词
		if (StringUtils.hasLength(condition.getMatchingKeyword())) {
			condition.setMatchingKeyword(condition.getMatchingKeyword().replace("'",""));
			sql.append(" and  entity.matchingKeyword like '%").append(condition.getMatchingKeyword()).append("%' ");
			if (!hasfirst)hasfirst = true;
		}
		//采集时间
		if(condition.getCtime()!=null){
			sql.append(" and entity.ctime>='").append(TimeUtil.formatDate(condition.getCtime())).append("'");
			try {
				sql.append(" and entity.ctime<'").append(TimeUtil.formatDate(TimeUtil.addDay(condition.getCtime(), 1))).append("'");
			} catch (Exception e) {
				e.printStackTrace();
			}
			if (!hasfirst)hasfirst = true;
		}else{
			sql.append(" and entity.ctime>='").append(TimeUtil.formatDate(new Date())).append("'");
			if (!hasfirst)hasfirst = true;
		}
		//用户ID
		if (condition.getUserId()!=null) {
			sql.append(" and  entity.userId=").append(condition.getUserId());
			if (!hasfirst)hasfirst = true;
		}else {
			if (StringUtils.hasLength(condition.getUploadCellPhone())) {
				Long userId=userService.getUserIdByCellPhone(condition.getUploadCellPhone());
				if(userId==null) {
					userId=Long.MAX_VALUE;
				}
				sql.append(" and  entity.userId=").append(userId);
				if (!hasfirst)hasfirst = true;
				
			}
		}
		//平台类型
		if (condition.getPlatId()!=null) {
			sql.append(" and entity.platId=").append(condition.getPlatId());
			if (!hasfirst)hasfirst = true;
		}
		//默认只查询状态正常的
		sql.append("and state<=0 order by entity.id desc ");
		
		if (pageNo == null || pageNo.intValue() <= 0)
			pageNo = 1;
		PageBean pageBean = new PageBean();
		pageBean.setCurrentPage(pageNo);
		if (pageSize == null || pageSize.intValue() <= 0)
			pageSize = Constant.DEFAULT_PAGE_SIZE;
		pageBean.setPageSize(pageSize);
		//获取列表和分页数据
		List<TransportNullifyBean> conditions=transportNullifyService.getList(sql.toString(), pageBean);
		if (!conditions.isEmpty()) {
			conditions.stream().forEach(o -> {
				o.setUploadCellPhone(Base64.getEncoder().encodeToString(o.getUserId().toString().getBytes()));
			});
		}
		request.setAttribute("conditions", conditions);
		request.setAttribute("condition", condition);
		request.setAttribute("rowCount", pageBean.getRowCount());
		request.setAttribute("pageNo", pageNo);
		request.setAttribute("pageSize", pageSize);
		request.setAttribute("maxPage", pageBean.getMaxPage());
		if (pageBean.getMaxPage() > 0L) {
			List<Integer> pageNoList = new ArrayList<Integer>();
			for (int i = 1; i <= pageBean.getMaxPage(); i++) {
				pageNoList.add(i);
			}
			request.setAttribute("pageNoList", pageNoList);
			pageNoList=null;
		}
		return "back/jsp/transport_nullify_list";
	}

	/**
	 * 	根据ID列表更改无效货源为有效状态
	 * @param idList    ID列表
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/update_valid")
	@ResponseBody
	public void updateValidByIds(Long[] idList, HttpServletRequest request, HttpServletResponse response) {
		response.setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = response.getWriter();
			if (!checkLogin(request)) {
				out.write("用户未登录");
				logger.info("自动无效货源--批量有效：用户未登录");
				return;
			}

			if(idList == null || idList.length <= 0){
				out.write("修改失败,货物ID为空");
				logger.info("自动无效货源--批量有效：货物ID为空");
				return;
			}
			//批量更新
			boolean result = transportNullifyService.updateStatus2Valid(Arrays.asList(idList));
			if (result) {
				out.write("200");
				logger.info("自动无效货源--批量有效：修改成功");
			} else {
				logger.info("自动无效货源--批量有效：修改失败");
				out.write("修改失败!");
			}
			out.close();
		} catch (IOException e) {
			out.write("修改失败!");
			logger.info("自动无效货源--批量有效：修改失败，"+e);
//			e.printStackTrace();
		} catch (Exception e) {
			logger.info("自动无效货源--批量有效：修改失败，"+e);
			out.write("修改失败!");
//			e.printStackTrace();
		} finally {
			if (out != null) {
				out.close();
			}
		}
	}

}
