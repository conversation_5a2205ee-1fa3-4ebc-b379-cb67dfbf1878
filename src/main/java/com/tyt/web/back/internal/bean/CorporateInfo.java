package com.tyt.web.back.internal.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorporateInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 法人
     */
    private String legalPersonName;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 企业联系方式
     */
    private String phoneNumber;

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Override
    public String toString() {
        return "CorporateInfo{" +
                "legalPersonName='" + legalPersonName + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                '}';
    }
}
