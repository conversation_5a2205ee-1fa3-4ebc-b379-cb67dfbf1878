package com.tyt.web.back.internal;

import java.io.PrintWriter;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.OpLog;
import com.tyt.model.TytSttLimit;
import com.tyt.service.transport.SttLimitService;
import com.tyt.util.Constant;
import com.tyt.web.base.BaseController;

@Controller
@RequestMapping("/boss/sttLimit")
public class SttLimitController extends BaseController {

	@Resource(name = "sttLimitService")
	SttLimitService sttLimitService;

	/**
	 * 获取列表
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/list")
	public String getList(HttpServletRequest request,
			HttpServletResponse response) {
		try {
			// 获得当前用户的身份
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			List<TytSttLimit> sttLimitList=sttLimitService.getSttLimitList();
			request.setAttribute("sttLimitList", sttLimitList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "back/jsp/tyt_stt_limit";
	}
	/**
	 * 到更新页面
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/toupdate")
	public String updateTo(Long id,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			// 获得当前用户的身份
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			TytSttLimit sttLimit=sttLimitService.getById(id);
			request.setAttribute("sttLimit", sttLimit);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return "back/jsp/tyt_stt_limit_update";
	}
	/**
	 * 更新
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/update")
	public void updateById(Long id, Integer value, Integer type,
			HttpServletRequest request, HttpServletResponse response) {
		PrintWriter writer=null;
		try {

			writer = response.getWriter();
			// 获得当前用户的身份
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				writer.print("-1");
				return;
			}
			if(id==null||id.longValue()<=0||value==null||type==null||type.intValue()<0||type.intValue()>3){
				writer.print("0");
				return;
			}
			//List<TytSttLimit> list=sttLimitService.getSttLimitList();
			TytSttLimit limit=sttLimitService.getById(id);//list.get(id.intValue()-1);
			if(limit.getType().intValue()!=type.intValue()||limit.getValue().intValue()
					!=value.intValue()){
				sttLimitService.update(id,value,type,limit.getUserSign(),limit.getCode());
				//TODO modify by tianjw on 20180205 所有后台日志均记入tyt_manage_cud_log表中，不往log中记录
//				String op=id+":"+limit.getType()+"-"+type+","+limit.getValue()+"-"+value;
//				createOpLog(curUser.getId(),
//								 OpLog.UPDATE_STT_LIMIT,
//								 curUser.getLoginPhoneNo(), "无", "无", "无", 6,
//							request,op);
			}
			writer.print("1");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			writer.print("5");
		}finally{
			if(writer!=null){
				writer.close();
			}
		}

	}
}
