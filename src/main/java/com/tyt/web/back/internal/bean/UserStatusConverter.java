package com.tyt.web.back.internal.bean;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/10/19 19:27
 * @Version 1.0
 **/
public class UserStatusConverter implements Converter<String> {
    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(String value, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        switch(value){
            case "CHECK_PENDING":
                return new CellData("待审核");
            case "REMITTANCE_VALID_PENDING":
                return new CellData("审核通过，待打款验证");
            case "ACTIVATE_PENDING":
                return new CellData("待激活");
            case "NORMAL":
                return new CellData("正常");
            default:
                return new CellData("");
            }
    }
}
