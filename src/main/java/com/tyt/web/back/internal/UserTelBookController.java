package com.tyt.web.back.internal;

import java.io.PrintWriter;
import java.util.Base64;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.User;
import com.tyt.model.UserTel;
import com.tyt.service.user.UserService;
import com.tyt.service.user.UserTelService;
import com.tyt.util.Constant;
import com.tyt.web.back.internal.bean.UserTelBean;
import com.tyt.web.base.BaseController;
@Controller
@RequestMapping("/boss/user/telbook")
public class UserTelBookController extends BaseController {
	
	@Resource(name="userTelService")
	UserTelService userTelService;
	
	@Resource(name = "userService")
	private UserService userService;
	/**
	 * 查询我的电话本
	 * @param userId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/my")
	public String myTelbook(@RequestParam(value = "userId", defaultValue = "1") Long userId,
			HttpServletRequest request, HttpServletResponse response){
		try{
		   if (!checkLogin(request)) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
		   List<UserTel> tels=userTelService.getByUserId(userId);
		   request.setAttribute("tels", tels);
		}catch(Exception e){
			e.printStackTrace();
		}
		return "back/jsp/my_usertel";
	}
	/**
	 * 更新
	 * @param id
	 * @param status
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/update")
	public void update(@RequestParam(value = "id", defaultValue = "") Long id,
			@RequestParam(value = "status", defaultValue = "") String status,
			HttpServletRequest request, HttpServletResponse response){
		try{
			if (!checkLogin(request)) {
				throw new Exception("后台用户没登录");
			}
		   if(!status.equals("0")&&!status.equals("1")){
			   throw new Exception("后台电话本更新状态类型错误");
		   }
		   userTelService.updateStatus(id, status);
		   PrintWriter out=response.getWriter();
		   out.print(1);
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	@RequestMapping("/list")
	public String myTelbook(UserTelBean tel,Integer pageNo,Integer pageSize,
			HttpServletRequest request, HttpServletResponse response){
		try {
			if (!checkLogin(request)) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			if (pageNo == null || pageNo.intValue() <= 0)
				pageNo = 1;
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(pageNo);
			if (pageSize == null || pageSize.intValue() <= 0)
				pageSize = Constant.DEFAULT_PAGE_SIZE;
			pageBean.setPageSize(pageSize);
			List<UserTelBean> tels=userTelService.getUserTels(tel,pageBean);
			if (tels!=null) {
				tels.stream().forEach(o -> {
					o.setCellPhone("");
					o.setTell("");
				});
			}
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("pageNo", pageNo);
			request.setAttribute("pageSize", pageSize);
			request.setAttribute("tels", tels);
			request.setAttribute("tel", tel);
			request.setAttribute("maxPage", pageBean.getMaxPage());
//			if (pageBean.getMaxPage() > 0L) {
//				List<Integer> pageNoList = new ArrayList<Integer>();
//				for (int i = 1; i <= pageBean.getMaxPage(); i++) {
//					pageNoList.add(i);
//				}
//				request.setAttribute("pageNoList", pageNoList);
//				pageNoList=null;
//			}
//
//			User opUser = getCurrentUser(request);
//			if (opUser != null)
//				request.setAttribute("userSign", opUser.getUserSign());
			return "back/jsp/usertel_list";
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/*@RequestMapping("/add")
	public void addTelbook(@RequestParam(value = "userId", defaultValue = "1") Long userId,
			@RequestParam(value = "tel", defaultValue = "") String tel,
			@RequestParam(value = "type", defaultValue = "0") String type,
			HttpServletRequest request, HttpServletResponse response){
		    PrintWriter out=null;
		    try {
		    	out=response.getWriter();
		    	String message="";
		    	if (!checkLogin(request)) {
					request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
					message="not login";
				}
		    	tel=tel.replaceAll(",", "");
				userTelService.add(new UserTel(userId, tel, "1", type));
				message="1";
				List<UserTel> tels=userTelService.getByUserId(userId);
				request.setAttribute("tels",tels);
				out.print(message);
			} catch (Exception e) {
				e.printStackTrace();
			}finally{
				if(out!=null)out.close();
			}
            		
	}*/
	
	@RequestMapping("/add")
	public String addTelbook(@RequestParam(value = "userId", defaultValue = "1") Long userId,
			@RequestParam(value = "tel", defaultValue = "") String tel,
			@RequestParam(value = "type", defaultValue = "0") String type,
			@RequestParam(value = "first", defaultValue = "0") Integer first,
			HttpServletRequest request, HttpServletResponse response){
		    try {
		    	EmployeeQueryBean currentUser=getCurrentUser(request);
		    	if (currentUser==null) {
					request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
					return "back/jsp/admin_login";
				}
		    	User user=userService.getByUserId(userId);
		    	if(first!=1){
		    		tel=tel.replaceAll(",", "");
		    		if(tel.equals(user.getCellPhone())||userTelService.get(userId,tel)){
						logger.info("后台添加联系人已经存在");
						request.setAttribute("msg","联系人已经存在");
					}else{
						userTelService.add(new UserTel(userId, tel, "1", type));
						request.setAttribute("msg","添加成功");
					}
					
		    	}
				List<UserTel> tels=userTelService.getByUserId(userId);
				request.setAttribute("currentUser", user);
				request.setAttribute("tels",tels);
				return "back/jsp/user_tel_add";
			} catch (Exception e) {
				e.printStackTrace();
				request.setAttribute("msg","服务器错误，请联系开发人员！");
				return "back/jsp/user_tel_add";
			}finally{
			}
            		
	}
}
