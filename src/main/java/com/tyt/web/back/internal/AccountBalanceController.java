package com.tyt.web.back.internal;

import java.util.Base64;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserAccount;
import com.tyt.service.user.TytUserAccountService;
import com.tyt.util.Constant;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.UserAccountQuery;



@Controller
@RequestMapping("/manage/account/balance")
public class AccountBalanceController extends BaseController {
	
	@Resource(name = "tytUserAccountService")
	private TytUserAccountService tytUserAccountService;
	
	/**
	 * 用户账户列表
	 * 
	 * @param pageBean
	 * @param pageNo
	 * @param conditionBean
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("getList")
	public String getList(PageBean pageBean, Integer pageNo, UserAccountQuery conditionBean, HttpServletRequest request, HttpServletResponse response) {
		try {

			if (!checkLogin(request)) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			pageBean.setCurrentPage(pageNo == null ? 1 : pageNo);
			long t1 = System.currentTimeMillis();
			List<UserAccountQuery> financialFlowList = tytUserAccountService.getUserAccountList(pageBean, conditionBean);
			//遍历循环 列表数据原手机号字段置空，跟随该手机号配套对应的Base64加密的userId同时返回
			for(UserAccountQuery userAccount:financialFlowList){
				userAccount.setPhone(Base64.getEncoder().encodeToString(userAccount.getUserId().toString().getBytes()));
			}
			long t2 = System.currentTimeMillis();
			logger.info("后台查询账户记录时间【{}】参数【{}】", t2 - t1, conditionBean);
			request.setAttribute("financialFlowList", financialFlowList);
			request.setAttribute("pageNo", pageBean.getCurrentPage());
			request.setAttribute("pageSize", pageBean.getPageSize());
			request.setAttribute("maxPage", pageBean.getMaxPage());
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("accountBean", conditionBean);
			return "back/jsp/user_account_list";
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			request.setAttribute("msg", e);
			return "back/jsp/admin_login";
		}
	}
	
	/**
	 * 到更改账户余额页面
	 * @param userId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/toUpdate")
	public String toUpdateBalance(Long id,
			HttpServletRequest request,HttpServletResponse response){
		        try {
					//登陆验证
					EmployeeQueryBean curUser = getCurrentUser(request);
					if (curUser==null) {
						request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
						return "back/jsp/admin_login";
					}
					if(id==null||id.longValue()<=0){
						request.setAttribute("msg", "数据错误，请重新登陆");
						return "back/jsp/admin_login";
					}
					TytUserAccount account=tytUserAccountService.getUserAccountById(id);
					if(account==null)throw new Exception("用户账户信息不存在");
					request.setAttribute("account", account);
					return "back/jsp/user_balance_edit";
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					request.setAttribute("msg", e);
					return "back/jsp/user_account_list";
				}
	}
	
	/**
	 * 更改账户
	 * @param userId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/update")
	@ResponseBody
	public ResultMsgBean updateBalance(Long id,String add,String moneyAmount,Long tradeId,Long userId,String remark,
			HttpServletRequest request,HttpServletResponse response){
			ResultMsgBean result = new ResultMsgBean();
	        try {
				//登陆验证
				EmployeeQueryBean curUser = getCurrentUser(request);
				if (curUser==null) {
					result.setCode(100);
					result.setMsg("请重新登陆!");
					response.sendRedirect(request.getContextPath() + "/back/jsp/admin_login.jsp");
					return result;
				}
				if(id==null||id.longValue()<=0){
					result.setCode(400);
					result.setMsg("数据错误，请重新填写!");
					return result;
				}
				//更新数据并记录更改
				boolean isUpdate = tytUserAccountService.updateBalanceValue(curUser.getId(),id,add,tradeId,moneyAmount,userId,remark);
				if(isUpdate){
					result.setCode(200);
					result.setMsg("更改成功！");
					return result;
				}else{
					result.setCode(400);
					result.setMsg("更改失败！");
					return result;
				}
			} catch (Exception e) {
				result.setCode(400);
				result.setMsg("数据处理异常更改失败！");
				return result;
			}
	        
	}
	
}
