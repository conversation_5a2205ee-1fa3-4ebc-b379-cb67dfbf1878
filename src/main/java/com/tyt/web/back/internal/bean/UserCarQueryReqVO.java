package com.tyt.web.back.internal.bean;

import java.util.Date;

public class UserCarQueryReqVO {

    private Integer userId;
    private String trueName;
    private String carUserName;
    private String cellPhone;
    private String province;
    private String city;
    private String county;
    private String source;
    private Date registDateStart;
    private Date registDateEnd;

    private Date carDateStart;
    private Date carDateEnd;

    private Date goodsDateStart;
    private Date goodsDateEnd;

    private String maintainMan;
    private Integer page;
    private Integer size;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getTrueName() {
        return trueName;
    }

    public void setTrueName(String trueName) {
        this.trueName = trueName;
    }

    public String getCarUserName() {
        return carUserName;
    }

    public void setCarUserName(String carUserName) {
        this.carUserName = carUserName;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Date getRegistDateStart() {
        return registDateStart;
    }

    public void setRegistDateStart(Date registDateStart) {
        this.registDateStart = registDateStart;
    }

    public Date getRegistDateEnd() {
        return registDateEnd;
    }

    public void setRegistDateEnd(Date registDateEnd) {
        this.registDateEnd = registDateEnd;
    }

    public Date getCarDateStart() {
        return carDateStart;
    }

    public void setCarDateStart(Date carDateStart) {
        this.carDateStart = carDateStart;
    }

    public Date getCarDateEnd() {
        return carDateEnd;
    }

    public void setCarDateEnd(Date carDateEnd) {
        this.carDateEnd = carDateEnd;
    }

    public Date getGoodsDateStart() {
        return goodsDateStart;
    }

    public void setGoodsDateStart(Date goodsDateStart) {
        this.goodsDateStart = goodsDateStart;
    }

    public Date getGoodsDateEnd() {
        return goodsDateEnd;
    }

    public void setGoodsDateEnd(Date goodsDateEnd) {
        this.goodsDateEnd = goodsDateEnd;
    }

    public String getMaintainMan() {
        return maintainMan;
    }

    public void setMaintainMan(String maintainMan) {
        this.maintainMan = maintainMan;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
