package com.tyt.web.back.internal;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.service.transport.TransportMainService;
import com.tyt.web.back.internal.bean.TytTransportInfoFeeBean;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.payment.service.OrderService;
import com.tyt.service.waybill.TytTransportWaybillService;
import com.tyt.util.Constant;
import com.tyt.web.back.internal.bean.TytTransportWaybillBean;
import com.tyt.web.base.BaseController;

/**
 * 货主运单查询
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/manage/wayBill")
public class OwnerWaybillController extends BaseController{

	@Resource(name = "tytTransportWaybillService")
	private TytTransportWaybillService waybillService;
	@Resource(name = "orderService")
	private OrderService orderService;
	@Resource(name ="transportOrdersService")
	private TransportOrdersService transportOrdersService;

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@RequestMapping(value = "/list")
	public String getWayList(TytTransportWaybillBean queryBean,PageBean pageBean, Integer pageNo, 
			HttpServletRequest request,HttpServletResponse response) {
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			pageBean.setCurrentPage(pageNo == null ? 1 : pageNo);
			List<TytTransportWaybillBean> list=waybillService.getList(queryBean,pageBean);
			if (list!=null) {
				list.stream().forEach(o -> {
					TransportMain transportMain = transportMainService.getById(Long.valueOf(o.getTsId()));
					o.setTecServiceFee(transportMain.getTecServiceFee());
					o.setUploadCellphone(Base64.getEncoder().encodeToString(o.getUserId().toString().getBytes()));
				});
			}
			request.setAttribute("list", list);
			request.setAttribute("pageNo", pageBean.getCurrentPage());
			request.setAttribute("pageSize", pageBean.getPageSize());
			request.setAttribute("maxPage", pageBean.getMaxPage());
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("queryBean", queryBean);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/waybillOrderQuery/waybill_list";
		
	}
	
	@RequestMapping(value="/getWaybillDetails")
	@ResponseBody
	public ResultMsgBean getWaybillDetails(
			@RequestParam(value = "tsOrderNo", required = true) String tsOrderNo){
		ResultMsgBean result=new ResultMsgBean();
		try {
			TytTransportWaybill waybill = waybillService.getWaybillDetails(tsOrderNo);
			result.setCode(200);
			result.setData(waybill);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
		}
		return result;
		
	}
	/**
	 * @Description  获取信息费列表详情数据
	 * <AUTHOR>
	 * @Date  2019/1/8 17:16
	 * @Param [tsOrderNo]
	 * @return com.tyt.model.ResultMsgBean
	 **/
	@RequestMapping(value="/getInfoFeeDetails")
	@ResponseBody
	public ResultMsgBean getInfoFeeDetails(
			@RequestParam(value = "tsOrderNo", required = true) String tsOrderNo){
		ResultMsgBean result=new ResultMsgBean();
		StringBuffer sb = new StringBuffer();
		try {
			//信息费详情对象
			TytTransportInfoFeeBean tytTransportInfoFeeBean = new TytTransportInfoFeeBean();

			//运单信息对象
			TytTransportWaybill waybill = waybillService.getById(tsOrderNo);
			if(waybill != null){
				BeanUtils.copyProperties(tytTransportInfoFeeBean, waybill);
			}
            //信息费列表对象
			List<TytTransportOrders> transportOrders = new ArrayList<TytTransportOrders>();
			if(StringUtils.isNotBlank(tsOrderNo))
			{
				sb.append(" entity.tsOrderNo='"+tsOrderNo+"' and entity.costStatus >= 15 "); //信息费支付状态>=15
				transportOrders = transportOrdersService.getList(sb.toString(), null);
				if(transportOrders != null && transportOrders.size()>0)
				{
					tytTransportInfoFeeBean.setTransportOrders(transportOrders);
					tytTransportInfoFeeBean.setInfoFeeNum(transportOrders.size());
				}
			}
			result.setCode(200);
			result.setData(tytTransportInfoFeeBean);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
		}
		return result;
	}

	@RequestMapping("/updatePayNum")
	@ResponseBody
	public ResultMsgBean updatePayNum(String tsOrderNo,Integer payNum,
			HttpServletRequest request,HttpServletResponse response){
			ResultMsgBean result = new ResultMsgBean();
	        try {
				//登陆验证
				EmployeeQueryBean curUser = getCurrentUser(request);
				if (curUser==null) {
					result.setCode(100);
					result.setMsg("请重新登陆!");
					response.sendRedirect(request.getContextPath() + "/back/jsp/admin_login.jsp");
					return result;
				}
				if(StringUtils.isBlank(tsOrderNo)){
					result.setCode(400);
					result.setMsg("数据错误，请重新填写!");
					return result;
				}
				if(payNum == null || Integer.parseInt(payNum.toString())<=0){
					result.setCode(400);
					result.setMsg("支付人数不能为空或者0！");
					return result;
				}
				//更新数据并记录更改
				String sql = "update tyt.tyt_transport_waybill set pay_number =? where ts_order_no=? ";
				boolean isUpdate = waybillService.executeUpdateSql(sql, new Object[]{payNum,tsOrderNo})>0;
				if(isUpdate){
					result.setCode(200);
					result.setMsg("更改成功！");
					return result;
				}else{
					result.setCode(400);
					result.setMsg("更改失败！");
					return result;
				}
			} catch (Exception e) {
				result.setCode(400);
				result.setMsg("数据处理异常更改失败！");
				return result;
			}
	        
	}
	@RequestMapping("/getPayList")
	@ResponseBody
	public ResultMsgBean getPayList(String tsOrderNo,
			HttpServletRequest request,HttpServletResponse response){
			ResultMsgBean result = new ResultMsgBean();
	        try {
				//登陆验证
				EmployeeQueryBean curUser = getCurrentUser(request);
				if (curUser==null) {
					result.setCode(100);
					result.setMsg("请重新登陆!");
					response.sendRedirect(request.getContextPath() + "/back/jsp/admin_login.jsp");
					return result;
				}
				if(StringUtils.isBlank(tsOrderNo)){
					result.setCode(400);
					result.setMsg("数据错误，请重新填写!");
					return result;
				}
				//获取支付列表
				
				PageBean pageBean = new PageBean();
				pageBean.setCurrentPage(1);
				Integer pageSize = Constant.DEFAULT_PAGE_SIZE;
				pageBean.setPageSize(pageSize);
				
				StringBuffer sql = new StringBuffer();
				sql.append("entity.orderNum ='").append(tsOrderNo).append("'");
				List<Order> orders = orderService.getList(sql.toString(), pageBean);
				result.setCode(200);
				result.setMsg("查询成功！");
				result.setData(orders);
				return result;
			} catch (Exception e) {
				result.setCode(400);
				result.setMsg("数据处理异常更改失败！");
				return result;
			}
	        
	}
	
}
