package com.tyt.web.back.internal;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytSyncgoodsRule;
import com.tyt.manager.service.dictionary.TytSyncgoodsRuleService;
import com.tyt.manager.service.dictionary.TytUserempowerSyncgoodsService;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.CsvWriter;
import com.tyt.web.back.internal.bean.SyncgoodsRuleVO;
import com.tyt.web.back.internal.bean.TytSyncgoodsRuleBean;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.GroupUserlistQueryBean;
import com.tyt.web.qbean.TytUserempowerQueryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName UserEmpowerController
 * @Description 用户同步货源授权
 * <AUTHOR> Lion
 * @Date 2022/10/14 9:38
 * @Verdion 1.0
 **/
@RestController
@RequestMapping("/boss/user/syncgoods")
public class UserEmpowerController extends BaseController {

    @Autowired
    private TytUserempowerSyncgoodsService tytUserempowerSyncgoodsService;

    @Autowired
    private TytSyncgoodsRuleService tytSyncgoodsRuleService;


    /**
     * @return void
     * <AUTHOR> Lion
     * @Description 权益用户导入接口  V6260
     * @Param [request, response, fileField]
     * @Date 2022/10/14 16:48
     */
    @RequestMapping(value = "/importList", method = RequestMethod.POST)
    public ResultMsgBean importList(HttpServletRequest request, HttpServletResponse response,
                                    @RequestParam(value = "fileField", required = true) MultipartFile fileField) {
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
        ResultMsgBean msgBean = new ResultMsgBean(200, "导入成功");
        try {

            msgBean = tytUserempowerSyncgoodsService.saveExcel(fileField, curUser, msgBean);
        } catch (Exception e) {

            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("导入失败,请重新导入");
            logger.error("权益用户导入接口错误：", e);
        }
        return msgBean;
    }

    /**
     * 下载模板
     *
     * @param request  request
     * @param response response
     */
    @RequestMapping(value = "/excelExportModel", method = RequestMethod.POST)
    public void excelExportModel(HttpServletRequest request, HttpServletResponse response) {
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

        try {
            String header = "userId" + "\r\n";
            CsvWriter.exportCsv("权益用户导入标准模板", header.toString(), response);
            header = null;
        } catch (Exception e) {
            logger.error("excelExportModel错误：", e);
        }
    }


    /**
     * @return com.tyt.model.ResultMsgBean
     * <AUTHOR> Lion
     * @Description 用户列表查询接口(带条件 ， 数量等)  V6260
     * @Param [pageBean, tytUserempowerQueryBean, request, response]
     * @Date 2022/10/14 16:49
     */
    @RequestMapping("/getlist")
    @ResponseBody
    public ResultMsgBean getList(PageBean pageBean, TytUserempowerQueryBean tytUserempowerQueryBean, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (pageBean.getCurrentPage() == 0 || pageBean.getPageSize() == 0) {
                result.setMsg("页数及每页条数不可为空");
                result.setCode(401);
                return result;
            }
            HashMap<String, Object> resultMap = tytUserempowerSyncgoodsService.getUserEmpowerList(pageBean, tytUserempowerQueryBean);
            result.setData(resultMap);
        } catch (Exception e) {
            logger.error("用户列表查询接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /***
     * <AUTHOR> Lion
     * @Description 货源同步配置模块'查询'接口
     * @Param [id, request, response]
     * @return com.tyt.model.ResultMsgBean
     * @Date 2022/10/17 14:40
     */
    @RequestMapping("/getrule")
    @ResponseBody
    public ResultMsgBean getrule(Long ruleGroupId, Integer excellentGoods, Integer publishType) {
        ResultMsgBean result = new ResultMsgBean(200, "查询成功");
        try {
            SyncgoodsRuleVO rule = tytSyncgoodsRuleService.findSyncGoodsRule(ruleGroupId, excellentGoods, publishType);
            result.setData(rule);
        } catch (Exception e) {
            logger.error("获取同步配置错误，入参{}，{}，{}：", ruleGroupId, excellentGoods, publishType, e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /**
     * @return com.tyt.model.ResultMsgBean
     * <AUTHOR> Lion
     * @Description 权益用户删除
     * @Param [id, request, response]
     * @Date 2022/10/14 16:49
     */
    @RequestMapping("/delete")
    @ResponseBody
    public ResultMsgBean deleteUserId(@RequestParam(value = "idlist", required = true) List<Long> idlist, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "删除成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (idlist == null || idlist.isEmpty()) {
                result.setMsg("idlist参数错误");
                result.setCode(402);
                return result;
            }
            result = tytUserempowerSyncgoodsService.deleteEmpowerUser(idlist, result);
        } catch (Exception e) {
            logger.error("权益用户删除接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /***
     * <AUTHOR> Lion
     * @Description 货源同步配置模块'修改启用/停用'接口
     * @Param [tytSyncgoodsRuleBean, request, response]
     * @return com.tyt.model.ResultMsgBean
     * @Date 2022/10/17 16:21
     */
    @RequestMapping("/update")
    @ResponseBody
    public ResultMsgBean updateRule(TytSyncgoodsRuleBean tytSyncgoodsRuleBean, HttpServletRequest request) {
        ResultMsgBean result = new ResultMsgBean(200, "操作成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            tytSyncgoodsRuleService.updateSyncgoodsRule(tytSyncgoodsRuleBean);
        } catch (Exception e) {
            logger.error("货源同步配置模块接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /**
     * @return void
     * <AUTHOR> Lion
     * @Description 分组用户导入接口
     * @Param [request, response, fileField, id]
     * @Date 2022/12/19 15:39
     */
    @RequestMapping(value = "/importGroupList", method = RequestMethod.POST)
    public void importGroupList(HttpServletRequest request, HttpServletResponse response,
                                @RequestParam(value = "fileField", required = true) MultipartFile fileField,
                                @RequestParam(value = "id", required = true) Long id) {
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
        ResultMsgBean msgBean = new ResultMsgBean(200, "导入成功");
        try {
            msgBean = tytUserempowerSyncgoodsService.saveGroupExcel(fileField, curUser, msgBean, id);
        } catch (Exception e) {
            logger.error("分组用户导入接口错误：", e);
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("导入失败,请重新导入");
        }
        printJSON(request, response, msgBean);
    }


    @RequestMapping("/groupUserdelete")
    @ResponseBody
    public ResultMsgBean groupUserdelete(@RequestParam(value = "idlist", required = true) List<Long> idlist, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "删除成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (idlist == null || idlist.isEmpty()) {
                result.setMsg("idlist参数错误");
                result.setCode(402);
                return result;
            }
            result = tytUserempowerSyncgoodsService.deleteGroupUser(idlist, result);
        } catch (Exception e) {
            logger.error("分组用户删除接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }

    @RequestMapping("/getGroupUserlist")
    @ResponseBody
    public ResultMsgBean getGroupUserlist(PageBean pageBean, GroupUserlistQueryBean groupUserlistQueryBean, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (pageBean.getCurrentPage() == 0 || pageBean.getPageSize() == 0) {
                result.setMsg("页数及每页条数不可为空");
                result.setCode(401);
                return result;
            }
            if (groupUserlistQueryBean.getRuleGroupId() == null) {
                result.setMsg("ruleGroupId不可为空");
                result.setCode(402);
                return result;
            }
            HashMap<String, Object> resultMap = tytUserempowerSyncgoodsService.getGroupUserlist(pageBean, groupUserlistQueryBean);
            result.setData(resultMap);
        } catch (Exception e) {
            logger.error("分组用户查询接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }

    @RequestMapping("/getDefinitionRuleList")
    @ResponseBody
    public ResultMsgBean getDefinitionRuleList(Long ruleGroupId, Integer excellentGoods, Integer publishType) {
        ResultMsgBean result = new ResultMsgBean(200, "查询成功");
        try {
            SyncgoodsRuleVO rule = tytSyncgoodsRuleService.findSyncGoodsRule(ruleGroupId, excellentGoods, publishType);
            result.setData(rule);
        } catch (Exception e) {
            logger.error("获取同步配置错误，入参{}，{}，{}：", ruleGroupId, excellentGoods, publishType, e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


}
