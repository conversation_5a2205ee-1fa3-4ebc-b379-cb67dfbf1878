package com.tyt.web.back.internal.bean;

import java.util.Date;
/**
 * 推广用户管理bean
 *
 */
public class TytRecommenderBean {

	private String beginTime;
	private String endTime;
	private Long id;//编号
	private Long userId;//用户Id
	private String userName;//用户名
	private String cellPhone;//手机号
	private String deliverTypeOne;//审核身份一级
	private String deliverType;//审核身份二级
	private Integer newAddNum;//拉新总人数
	private Integer totalAuthNum;//总认证人数
	private Integer authCarNum;//认证车方人数
	private Integer authTransNum;//认证货方人数
	private Integer moneyTotalNum;//付费总人数
	private Integer moneyOneYearNum;//付费一年总人数
	private Integer moneyTwoYearNum;//付费两年总人数
	private Integer moneyThreeYearNum;//付费三年总人数
	private Long getMoneyTotalPrice;//已获得奖金总金额 '存储单位：分   5元存储为500分',
	private Long realGetMoneyPrice;//已转入钱包金额
	private String nullifyReason;//无效原因
	private Integer isRecommend;//'是否开通推荐好友    1:开通  2：关闭    3：无效       ',
	private Date ctime;//'推荐好友功能开通时间',
	private Date mtime;//'推荐好友功能状态更新时间',
	private Long operaUserid;//操作人员
	private Integer activeState;//
	public String getBeginTime() {
		return beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public Long getId() {
		return id;
	}
	public Long getUserId() {
		return userId;
	}
	public String getUserName() {
		return userName;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public String getDeliverTypeOne() {
		return deliverTypeOne;
	}
	public String getDeliverType() {
		return deliverType;
	}
	public Integer getNewAddNum() {
		return newAddNum;
	}
	public Integer getTotalAuthNum() {
		return totalAuthNum;
	}
	public Integer getAuthCarNum() {
		return authCarNum;
	}
	public Integer getAuthTransNum() {
		return authTransNum;
	}
	public Integer getMoneyTotalNum() {
		return moneyTotalNum;
	}
	public Integer getMoneyOneYearNum() {
		return moneyOneYearNum;
	}
	public Integer getMoneyTwoYearNum() {
		return moneyTwoYearNum;
	}
	public Integer getMoneyThreeYearNum() {
		return moneyThreeYearNum;
	}
	public Long getGetMoneyTotalPrice() {
		return getMoneyTotalPrice;
	}
	public Long getRealGetMoneyPrice() {
		return realGetMoneyPrice;
	}
	public String getNullifyReason() {
		return nullifyReason;
	}
	public Integer getIsRecommend() {
		return isRecommend;
	}
	public Date getCtime() {
		return ctime;
	}
	public Date getMtime() {
		return mtime;
	}
	public Long getOperaUserid() {
		return operaUserid;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	public void setDeliverTypeOne(String deliverTypeOne) {
		this.deliverTypeOne = deliverTypeOne;
	}
	public void setDeliverType(String deliverType) {
		this.deliverType = deliverType;
	}
	public void setNewAddNum(Integer newAddNum) {
		this.newAddNum = newAddNum;
	}
	public void setTotalAuthNum(Integer totalAuthNum) {
		this.totalAuthNum = totalAuthNum;
	}
	public void setAuthCarNum(Integer authCarNum) {
		this.authCarNum = authCarNum;
	}
	public void setAuthTransNum(Integer authTransNum) {
		this.authTransNum = authTransNum;
	}
	public void setMoneyTotalNum(Integer moneyTotalNum) {
		this.moneyTotalNum = moneyTotalNum;
	}
	public void setMoneyOneYearNum(Integer moneyOneYearNum) {
		this.moneyOneYearNum = moneyOneYearNum;
	}
	public void setMoneyTwoYearNum(Integer moneyTwoYearNum) {
		this.moneyTwoYearNum = moneyTwoYearNum;
	}
	public void setMoneyThreeYearNum(Integer moneyThreeYearNum) {
		this.moneyThreeYearNum = moneyThreeYearNum;
	}
	public void setGetMoneyTotalPrice(Long getMoneyTotalPrice) {
		this.getMoneyTotalPrice = getMoneyTotalPrice;
	}
	public void setRealGetMoneyPrice(Long realGetMoneyPrice) {
		this.realGetMoneyPrice = realGetMoneyPrice;
	}
	public void setNullifyReason(String nullifyReason) {
		this.nullifyReason = nullifyReason;
	}
	public void setIsRecommend(Integer isRecommend) {
		this.isRecommend = isRecommend;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	public void setOperaUserid(Long operaUserid) {
		this.operaUserid = operaUserid;
	}
	public Integer getActiveState() {
		return activeState;
	}
	public void setActiveState(Integer activeState) {
		this.activeState = activeState;
	}
	@Override
	public String toString() {
		return "TytRecommenderBean [beginTime=" + beginTime + ", endTime=" + endTime + ", id=" + id + ", userId="
				+ userId + ", userName=" + userName + ", cellPhone=" + cellPhone + ", deliverTypeOne=" + deliverTypeOne
				+ ", deliverType=" + deliverType + ", newAddNum=" + newAddNum + ", totalAuthNum=" + totalAuthNum
				+ ", authCarNum=" + authCarNum + ", authTransNum=" + authTransNum + ", moneyTotalNum=" + moneyTotalNum
				+ ", moneyOneYearNum=" + moneyOneYearNum + ", moneyTwoYearNum=" + moneyTwoYearNum
				+ ", moneyThreeYearNum=" + moneyThreeYearNum + ", getMoneyTotalPrice=" + getMoneyTotalPrice
				+ ", realGetMoneyPrice=" + realGetMoneyPrice + ", nullifyReason=" + nullifyReason + ", isRecommend="
				+ isRecommend + ", ctime=" + ctime + ", mtime=" + mtime + ", operaUserid=" + operaUserid
				+ ", activeState=" + activeState + "]";
	}

	
}
