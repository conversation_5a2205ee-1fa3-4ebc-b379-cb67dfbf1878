package com.tyt.web.back.internal.bean;

import org.codehaus.jackson.map.annotate.JsonSerialize;

@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UserEnterpriseAuthBeanInfo implements java.io.Serializable {

	private static final long serialVersionUID = -2728906339303217233L;
	/**
	 *
	 */

	private Long id;
	private Long userId;
	private Integer deliverTypeOne;
	private Integer deliverTypeTwo;
	private String enterpriseAuthCompanyName;
	private String enterpriseAuthCreditCode;
	private Integer enterpriseAuthStatus;
	private String enterpriseAuthFailureReason;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getDeliverTypeOne() {
		return deliverTypeOne;
	}

	public void setDeliverTypeOne(Integer deliverTypeOne) {
		this.deliverTypeOne = deliverTypeOne;
	}

	public Integer getDeliverTypeTwo() {
		return deliverTypeTwo;
	}

	public void setDeliverTypeTwo(Integer deliverTypeTwo) {
		this.deliverTypeTwo = deliverTypeTwo;
	}

	public Integer getEnterpriseAuthStatus() {
		return enterpriseAuthStatus;
	}

	public void setEnterpriseAuthStatus(Integer enterpriseAuthStatus) {
		this.enterpriseAuthStatus = enterpriseAuthStatus;
	}


	public void setEnterpriseAuthCompanyName(String enterpriseAuthCompanyName) {
		this.enterpriseAuthCompanyName = enterpriseAuthCompanyName;
	}

	public String getEnterpriseAuthCreditCode() {
		return enterpriseAuthCreditCode;
	}

	public void setEnterpriseAuthCreditCode(String enterpriseAuthCreditCode) {
		this.enterpriseAuthCreditCode = enterpriseAuthCreditCode;
	}

	public String getEnterpriseAuthCompanyName() {
		return enterpriseAuthCompanyName;
	}

	public String getEnterpriseAuthFailureReason() {
		return enterpriseAuthFailureReason;
	}

	public void setEnterpriseAuthFailureReason(String enterpriseAuthFailureReason) {
		this.enterpriseAuthFailureReason = enterpriseAuthFailureReason;
	}
}
