package com.tyt.web.back.internal.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class AcceptOrderLimitBean {

    /**
     * 车方用户ID
     */
    @ExcelProperty("车方用户ID")
    private String userId;

    /**
     * 处罚开始时间
     */
    @ExcelProperty("处罚开始时间")
    private String acceptOrderLimitStartTime;

    /**
     * 处罚天数(单位天)
     */
    @ExcelProperty("处罚天数(单位天)")
    private String acceptOrderLimitNum;

    /**
     * 处罚项:限制接单(单位秒)
     */
    @ExcelProperty("处罚项:限制接单(单位秒)")
    private String acceptOrderLimitItem;

}