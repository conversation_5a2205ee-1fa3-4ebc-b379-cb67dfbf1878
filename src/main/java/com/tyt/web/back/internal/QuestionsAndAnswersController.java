package com.tyt.web.back.internal;

import com.alibaba.fastjson.JSON;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.QuestionsAnswersManage;
import com.tyt.model.ResultMsgBean;
import com.tyt.peopleorders.bean.CallCenterResultMsgBean;
import com.tyt.service.config.TytConfigService;
import com.tyt.util.service.callcenter.PublicCallCenterServiceHttpUtil;
import com.tyt.util.service.callcenter.RemoteResultMsgBean;
import com.tyt.web.back.internal.bean.QuestionsAnswersBean;
import com.tyt.web.base.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Controller
@RequestMapping("/questionsAndAnswers")
public class QuestionsAndAnswersController extends BaseController {
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    /**
     * manage Q&A 查询接口
     * @param bean
     * @param pageSize
     * @param currentPage
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/getList")
    @ResponseBody
    public ResultMsgBean getList(@RequestBody QuestionsAnswersBean bean, Integer pageSize, Integer currentPage,
                                 HttpServletRequest request, HttpServletResponse response){
        ResultMsgBean result = new ResultMsgBean();
        result.setCode(ResultMsgBean.ERROR);
        try {

            TreeMap<String, String> paramsTreeMap = new TreeMap<String, String>();
            paramsTreeMap.put("keywordSearch", bean.getKeywordSearch());
            if (pageSize == null) {
                int size = tytConfigService.getIntValue("tyt_manage_contact_record_page_size", 50);
                paramsTreeMap.put("pageSize", String.valueOf(size));
            } else
                paramsTreeMap.put("pageSize", pageSize.toString());
            if (currentPage == null) {
                paramsTreeMap.put("currentPage", "1");
            } else
                paramsTreeMap.put("currentPage", currentPage.toString());

            String returnString = PublicCallCenterServiceHttpUtil.callServiceToJson("questionsAndAnswers/forManage/getList", paramsTreeMap);
            CallCenterResultMsgBean callCenterResultMsgBean = JSON.parseObject(returnString, CallCenterResultMsgBean.class);
            if (callCenterResultMsgBean != null && callCenterResultMsgBean.getListData() != null) {
                Map<String,Object> dataMap = new HashMap<String ,Object>();
                dataMap.put("data", callCenterResultMsgBean.getListData());
                dataMap.put("pageNo", callCenterResultMsgBean.getData().getCurrentPage());
                dataMap.put("maxPage", callCenterResultMsgBean.getData().getMaxPage());
                dataMap.put("pageSize",callCenterResultMsgBean.getData().getPageSize());
                dataMap.put("rowCount",callCenterResultMsgBean.getData().getRowCount());
                result.setData(dataMap);
                result.setCode(ResultMsgBean.OK);
                result.setMsg("查询成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setMsg(e.getMessage());
        }

        return result;
    }


    /**
     *  manage Q&A 新增/编辑接口
     * @param bean
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/update")
    @ResponseBody
    public ResultMsgBean update(@RequestBody QuestionsAnswersManage bean, HttpServletRequest request, HttpServletResponse response) {

        ResultMsgBean result = new ResultMsgBean();
        result.setCode(ResultMsgBean.ERROR);
        try {

            TreeMap<String, String> paramsTreeMap = new TreeMap<String, String>();
            if(bean.getId()!=null && bean.getId().intValue()>0){
                paramsTreeMap.put("id", String.valueOf(bean.getId()));
            }
            paramsTreeMap.put("question", bean.getQuestion());
            paramsTreeMap.put("answer", bean.getAnswer());
            EmployeeQueryBean employeeQueryBean = getCurrentUser(request);
            if (employeeQueryBean != null) {
                paramsTreeMap.put("opUserName", employeeQueryBean.getUserName());
            }

            String returnString = PublicCallCenterServiceHttpUtil.callServiceToJson("questionsAndAnswers/forManage/update", paramsTreeMap);
            RemoteResultMsgBean remoteResultMsgBean = JSON.parseObject(returnString, RemoteResultMsgBean.class);

            if (remoteResultMsgBean == null || remoteResultMsgBean.getCode() == null || remoteResultMsgBean.getCode().intValue() == 500) {
                result.setCode(ResultMsgBean.ERROR);
                result.setMsg("操作失败");
            } else {
                result.setCode(ResultMsgBean.OK);
                result.setMsg("操作成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setMsg(e.getMessage());
        }

        return result;
    }

    /**
     *  manage Q&A 详情接口
     * @param id
     * @return
     */
    @RequestMapping(value = "/getInfo")
    @ResponseBody
    public ResultMsgBean getInfo(@RequestParam(value="id", required=true) Long id) {

        ResultMsgBean result = new ResultMsgBean();
        result.setCode(ResultMsgBean.ERROR);
        try {

            TreeMap<String, String> paramsTreeMap = new TreeMap<String, String>();
            paramsTreeMap.put("id", String.valueOf(id));
            String returnString = PublicCallCenterServiceHttpUtil.callServiceToJson("questionsAndAnswers/forManage/getInfo", paramsTreeMap);
            RemoteResultMsgBean remoteResultMsgBean = JSON.parseObject(returnString, RemoteResultMsgBean.class);

            if (remoteResultMsgBean!= null || remoteResultMsgBean.getData()!=null) {
               result.setData(remoteResultMsgBean.getData());
               result.setCode(ResultMsgBean.OK);
               result.setMsg("查询成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setMsg(e.getMessage());
        }

        return result;
    }

    /**
     * manage Q&A 删除接口
     * @param bean
     * @param request
     * @return
     */
    @RequestMapping(value = "/remove")
    @ResponseBody
    public ResultMsgBean remove(@RequestBody QuestionsAnswersManage bean, HttpServletRequest request) {

        ResultMsgBean result = new ResultMsgBean();
        result.setCode(ResultMsgBean.ERROR);
        try {

            TreeMap<String, String> paramsTreeMap = new TreeMap<String, String>();
            paramsTreeMap.put("id", String.valueOf(bean.getId()));
            EmployeeQueryBean employeeQueryBean = getCurrentUser(request);
            if (employeeQueryBean != null) {
                paramsTreeMap.put("opUserName", employeeQueryBean.getUserName());
            }
            String returnString = PublicCallCenterServiceHttpUtil.callServiceToJson("questionsAndAnswers/forManage/remove", paramsTreeMap);
            RemoteResultMsgBean remoteResultMsgBean = JSON.parseObject(returnString, RemoteResultMsgBean.class);

            if (remoteResultMsgBean == null || remoteResultMsgBean.getCode() == null || remoteResultMsgBean.getCode() == 500) {
                result.setCode(ResultMsgBean.ERROR);
                result.setMsg("删除失败");
            } else {
                result.setCode(ResultMsgBean.OK);
                result.setMsg("删除成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setMsg(e.getMessage());
        }

        return result;
    }
}
