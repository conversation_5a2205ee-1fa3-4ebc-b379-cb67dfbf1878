package com.tyt.web.back.internal.bean;

import java.io.Serializable;
import java.util.Date;

public class TytTransportWaybillBean implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1412722966097247569L;

	private String tsOrderNo; //运单号
	private Long tsId; //货源ID
	private String isInfoFee;//是否收取信息费
	private Long userId;//发布人user_id
	private String uploadCellphone; //发布人电话
	private String linkman; //发布人
	private Integer payNumber; //支付人数
	private Long payAmount; //支付金额
	private Date createTime; //创建时间
	private Date mtime; //修改时间
	
	private String beginCtime;//创建开始时间
	private String endCtime;//创建结束时间

	private String tecServiceFee;//技术服务费

	public String getTsOrderNo() {
		return tsOrderNo;
	}
	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}
	public Long getTsId() {
		return tsId;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public String getIsInfoFee() {
		return isInfoFee;
	}
	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUploadCellphone() {
		return uploadCellphone;
	}
	public void setUploadCellphone(String uploadCellphone) {
		this.uploadCellphone = uploadCellphone;
	}
	public String getLinkman() {
		return linkman;
	}
	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}
	public Integer getPayNumber() {
		return payNumber;
	}
	public void setPayNumber(Integer payNumber) {
		this.payNumber = payNumber;
	}
	public Long getPayAmount() {
		return payAmount;
	}
	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getMtime() {
		return mtime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	public String getBeginCtime() {
		return beginCtime;
	}
	public void setBeginCtime(String beginCtime) {
		this.beginCtime = beginCtime;
	}
	public String getEndCtime() {
		return endCtime;
	}
	public void setEndCtime(String endCtime) {
		this.endCtime = endCtime;
	}

	public String getTecServiceFee() {
		return tecServiceFee;
	}

	public void setTecServiceFee(String tecServiceFee) {
		this.tecServiceFee = tecServiceFee;
	}

	@Override
	public String toString() {
		return "TytTransportWaybillBean [tsOrderNo=" + tsOrderNo + ", tsId="
				+ tsId + ", isInfoFee=" + isInfoFee + ", uploadCellphone="
				+ uploadCellphone + ", linkman=" + linkman + ", payNumber="
				+ payNumber + ", payAmount=" + payAmount + ", createTime="
				+ createTime + ", mtime=" + mtime + ", beginCtime="
				+ beginCtime + ", endCtime=" + endCtime + "]";
	}
	
	
	
}
