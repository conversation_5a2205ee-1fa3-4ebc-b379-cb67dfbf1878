package com.tyt.web.back.internal.bean;

/**
 * 车头品牌和挂车品牌的实体类
 * 
 * <AUTHOR>
 * @date 2016-7-12下午1:52:13
 * @description
 */
public class CarBrand {
	private Integer id;
	/* 品牌的名称 */
	private String brandName;
	/* 品牌的类型1：车头品牌 2: 挂车品牌 */
	private Integer brandType;
	private String updateTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public Integer getBrandType() {
		return brandType;
	}

	public void setBrandType(Integer brandType) {
		this.brandType = brandType;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
}
