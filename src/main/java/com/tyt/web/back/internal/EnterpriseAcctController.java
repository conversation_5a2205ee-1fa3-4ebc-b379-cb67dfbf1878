package com.tyt.web.back.internal;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.tyt.model.Resp;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.AppConfig;
import com.tyt.web.back.internal.bean.EnterpriseAcctExportModel;
import com.tyt.web.back.internal.bean.EnterpriseAcctReqVO;
import com.tyt.web.back.internal.bean.EnterpriseRegPhoneBindReqVO;
import com.tyt.web.back.internal.bean.GetRandomReqVO;
import com.tyt.web.base.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 企业账户管理相关api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/enterprise/acct")
public class EnterpriseAcctController extends BaseController {

    private final String userCenterUrl = AppConfig.getProperty("user.center.api.url");


    /**
     * 注册绑定申请验证码
     *
     * @param regPhone
     * @return
     */
    @RequestMapping(value = "/verifyCode/send", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean sendVerifyCode(@RequestParam String regPhone) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("regPhone", regPhone);
            String result = HttpUtil.get(userCenterUrl + "/enterprise/verifyCode/send", paramMap);
            Resp resp = JSON.parseObject(result, Resp.class);
            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("发送验证码异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("发送验证码出错");
            return resultMsgBean;
        }
    }


    /**
     * 绑定手机号
     *
     * @param vo
     * @return
     */
    @RequestMapping(value = "/regPhone/bind", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean bindRegPhone(EnterpriseRegPhoneBindReqVO vo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            String post = HttpRequest.post(userCenterUrl + "/enterprise/regPhone/bind")
                    .body(JSON.toJSONString(vo))
                    .execute()
                    .body();
            Resp resp = JSON.parseObject(post, Resp.class);
            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("绑定异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("绑定出错");
            return resultMsgBean;
        }
    }


    /**
     * 企业开户初始化
     *
     * @return
     */
    @RequestMapping(value = "/init", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean init() {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            String post = HttpUtil.get(userCenterUrl + "/enterprise/init");
            Resp resp = JSON.parseObject(post, Resp.class);
            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("初始化异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("初始化出错");
            return resultMsgBean;
        }
    }


    /**
     * 企业开户申请
     *
     * @return
     */
    @RequestMapping(value = "/apply", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean apply(@RequestBody EnterpriseAcctReqVO vo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            String post = HttpRequest.post(userCenterUrl + "/enterprise/apply")
                    .body(JSON.toJSONString(vo))
                    .execute()
                    .body();
            Resp resp = JSON.parseObject(post, Resp.class);
            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("企业开户异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("企业开户申请出错");
            return resultMsgBean;
        }
    }

    /**
     * 密码随机因子
     *
     * @return
     */
    @RequestMapping(value = "/random/get", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getRandom(GetRandomReqVO vo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            String post = HttpRequest.post(userCenterUrl + "/openacct/random/get")
                    .body(JSON.toJSONString(vo))
                    .execute()
                    .body();
            Resp resp = JSON.parseObject(post, Resp.class);
            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取随机因子异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取随机因子出错");
            return resultMsgBean;
        }
    }

    /**
     * 企业开户申请
     *
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean getEnterpriseAccts(@RequestParam int page, @RequestParam int size) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("page", page);
            params.put("size", size);
            String result = HttpUtil.get(userCenterUrl + "/enterprise/acct/list", params);
            Resp resp = JSON.parseObject(result, Resp.class);
            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取企业账户列表异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取企业账户列表出错");
            return resultMsgBean;
        }
    }

    /**
     * 企业开户申请
     *
     * @return
     */
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsgBean getEnterpriseAcctInfo(@RequestParam int acctId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("acctId", acctId);
            String result = HttpUtil.get(userCenterUrl + "/enterprise/acct/info", params);
            Resp resp = JSON.parseObject(result, Resp.class);
            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取企业账户详情异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取企业账户详情出错");
            return resultMsgBean;
        }
    }


    /**
     * 企业开户申请
     *
     * @return
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean exportAcctList(HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            String result = HttpUtil.get(userCenterUrl + "/enterprise/acct/export/list");
            Resp resp = JSON.parseObject(result, Resp.class);

            List<EnterpriseAcctExportModel> list = JSON.parseObject(JSON.toJSONString(resp.getData()),
                    new TypeReference<List<EnterpriseAcctExportModel>>() {
                    });

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("企业账户列表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EnterpriseAcctExportModel.class)
                    .sheet("企业账户列表").doWrite(list);

            resultMsgBean.setCode(resp.getCode());
            resultMsgBean.setMsg(resp.getMsg());
            resultMsgBean.setData(resp.getData());
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("导出企业账户列表异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("导出企业账户列表出错");
            return resultMsgBean;
        }
    }

}
