
package com.tyt.web.back.internal;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.adposition.service.AdPositionService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.csMaintainCustom.service.MaintainCustomNewService;
import com.tyt.enums.CarInvoiceStatusEnum;
import com.tyt.enums.DocumentTypeEnum;
import com.tyt.internal.client.user.ApiInvoiceCapacityClient;
import com.tyt.internal.client.user.dto.CarCheckDTO;
import com.tyt.internal.client.user.vo.CarCheckRpcVO;
import com.tyt.internal.config.InternalClientUtil;
import com.tyt.internal.config.InternalWebResult;
import com.tyt.invoice.enums.InvoiceServiceProviderCodeEnum;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.location.bean.MqSyncAuthCarMsg;
import com.tyt.manager.entity.base.TytInvoiceDriver;
import com.tyt.manager.entity.base.TytSuperiorCarSign;
import com.tyt.manager.service.base.SuperiorCarSignService;
import com.tyt.manager.vo.base.SuperiorCarSignUpdateStatusReqVo;
import com.tyt.model.*;
import com.tyt.ocr.bean.RoadTransportBackOcrResp;
import com.tyt.ocr.bean.VehicleLicenseDeputyPageBackOcrResp;
import com.tyt.service.car.CarLoadRecordService;
import com.tyt.service.car.CarLogService;
import com.tyt.service.car.CarService;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.user.TytUserSubService;
import com.tyt.service.user.UserService;
import com.tyt.util.*;
import com.tyt.web.back.internal.bean.CarBrand;
import com.tyt.web.back.internal.bean.CarExportExcelBean;
import com.tyt.web.back.internal.bean.CarType;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.CarQueryBean;
import com.tyt.web.qbean.CarStatusResultBean;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestOperations;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/boss/car")
public class CarController extends BaseController {

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name = "carLogService")
    private CarLogService carLogService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService tytUserSubService;


    @Resource(name = "adPositionService")
    private AdPositionService adPositionService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "scRestTemplate")
    private RestOperations restOperations;
    @Resource(name = "carLoadRecordService")
    private CarLoadRecordService carLoadRecordService;

    @Resource(name = "superiorCarSignService")
    private SuperiorCarSignService superiorCarSignService;

    @Autowired
    private MaintainCustomNewService maintainCustomNewService;

    @Autowired
    private PictureUtil pictureUtil;

    public static String key = AppConfig.getProperty("tyt.private.key");
    @RequestMapping(value = "/list")
    public String list(CarQueryBean car, Integer pageNo, Integer pageSize, HttpServletRequest request) {
        if (!checkLogin(request)) {
            request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
            return "back/jsp/admin_login";
        }
        // User curUser=getCurrentUser(request);

        if (pageNo == null || pageNo.intValue() <= 0)
            pageNo = 1;
        PageBean pageBean = new PageBean();
        pageBean.setCurrentPage(pageNo);
        if (pageSize == null || pageSize.intValue() <= 0)
            pageSize = Constant.DEFAULT_PAGE_SIZE;
        pageBean.setPageSize(pageSize);
        List<CarQueryBean> carList = carService.getCarList(car, pageBean);
        //遍历循环 列表数据原手机号字段置空，跟随该手机号配套对应的Base64加密的userId同时返回
        if (carList!=null) {
            for(CarQueryBean carBean:carList){
                carBean.setCellPhone(Base64.getEncoder().encodeToString(carBean.getUserId().toString().getBytes()));
            }
        }

        request.setAttribute("car", car);
        request.setAttribute("carList", carList);
        request.setAttribute("rowCount", pageBean.getRowCount());
        request.setAttribute("pageNo", pageBean.getCurrentPage());
        request.setAttribute("pageSize", pageSize);
        // request.setAttribute("saleList", saleList);
        //设置menuId在会话
        request.getSession().setAttribute("menuId", request.getParameter("menuId"));
        request.setAttribute("maxPage", pageBean.getMaxPage());
        // if (pageBean.getMaxPage() > 0L) {
        // List<Integer> pageNoList = new ArrayList<Integer>();
        // for (int i = 1; i <= pageBean.getMaxPage(); i++) {
        // pageNoList.add(i);
        // }
        // request.setAttribute("pageNoList", pageNoList);
        // pageNoList=null;
        // }
        //
        // User opUser = getCurrentUser(request);
        // if (opUser != null)
        // request.setAttribute("userSign", opUser.getUserSign());
        List<CarBrand> headBrands = carService.getCarBrandList("1");
        List<CarBrand> tailBrands = carService.getCarBrandList("2");
        request.setAttribute("headBrands", headBrands);
        request.setAttribute("tailBrands", tailBrands);
        return "back/jsp/car_list";
    }

    /**
     * 车辆信息详情
     *
     * @param id
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/detail")
    public String getIdentityDetail(@RequestParam(value = "id", defaultValue = "") Long id, @RequestParam(value = "msg", defaultValue = "2") Integer msg, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (!checkLogin(request)) {
                request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
                return "back/jsp/admin_login";
            }

            EmployeeQueryBean curUser = getCurrentUser(request);
            CarQueryBean car = carService.getDetailById(id);
            car.setHaveUploadButton(2);

            if(car == null){
                return "/back/jsp/car";
            }
            String button = tytConfigService.getStringValue("car_driver_upload_document_button");
            if (StringUtils.isNotBlank(button)){
                String[] curIds = button.split(",");
                if (Arrays.asList(curIds).contains(curUser.getId().toString())){
                    car.setHaveUploadButton(1);
                }
            }
            //如果挂车车辆为临牌，挂车整备质量为10000kg
            if (car.getTailNo().contains("超") && StringUtils.isBlank(car.gettCurbWeight())){
                car.settCurbWeight("10000");
            }

            //车辆图片链接兼容
            carUrlCompat(car);

            Long userId = car.getUserId();

            request.setAttribute("car", car);
            List<CarType> carTypeList1Level = carService.getCarTypeList();


            List<CarType> tailCarTypeList1Level = carTypeList1Level.stream().filter(carType -> carType.getCarType().endsWith("挂车")).collect(Collectors.toList());
            carTypeList1Level.removeAll(tailCarTypeList1Level);

            request.setAttribute("carTypeList1Level", carTypeList1Level);
            request.setAttribute("tailCarTypeList1Level", tailCarTypeList1Level);

            //车辆类型OCR识别专门
            List<String> carTypeIdStringList = carTypeList1Level.stream().filter(carType -> carType.getCarClassify() == 1).map(carType -> "'" + carType.getCarType() + "@#&" + carType.getId() + "'").collect(Collectors.toList());
            request.setAttribute("carTypeIdStringList", carTypeIdStringList);

            List<TytSource> carTypeList = TytSourceUtil.getSourceList("tail_car_type");
            request.setAttribute("carTypeList", carTypeList);
            List<TytSource> carStyleList = TytSourceUtil.getSourceList("tail_car_style");
            request.setAttribute("carStyleList", carStyleList);
            List<TytSource> carEnergyType = TytSourceUtil.getSourceList("car_energy_type");
            request.setAttribute("carEnergyType", carEnergyType);
            List<TytSource> vehicleColorCode = TytSourceUtil.getSourceList("vehicle_color_code");
            request.setAttribute("vehicleColorCode", vehicleColorCode);
            List<TytSource> carDriveList = TytSourceUtil.getSourceList("head_drive_style");
            request.setAttribute("carDriveList", carDriveList);
            List<TytSource> dischargeStandard = TytSourceUtil.getSourceList("discharge_standard");
            request.setAttribute("dischargeStandard", dischargeStandard);
            List<CarBrand> headBrands = carService.getCarBrandList("1");
            List<CarBrand> tailBrands = carService.getCarBrandList("2");
            request.setAttribute("headBrands", headBrands);
            request.setAttribute("tailBrands", tailBrands);

            List<TytInvoiceDriver> userDriverList = carService.getUserDriverList(userId);

            request.setAttribute("userDriverList", userDriverList);

            //获取车挂牌照相同的数量
            int tailCount = carService.getTailCount(car.getTailCity(), car.getTailNo());
            request.setAttribute("tailCount", tailCount);
            //获取车头牌照相同的数量
            int headCount = carService.getHeadCount(car.getHeadCity(), car.getHeadNo());
            request.setAttribute("headCount", headCount);
            //获取车头牌照+车挂牌照相同的数量
            int tailAndHeadCount = carService.getTailAndHeadCount(car.getHeadCity(), car.getHeadNo(), car.getTailCity(), car.getTailNo());
            request.setAttribute("tailAndHeadCount", tailAndHeadCount);

            //获取修改或者删除记录信息
            List<CarLog> carLogs = carLogService.getLogListByCarId(id);
            // request.setAttribute("msg", msg==1?"修改成功!":msg==0?"请稍后重试":"");
            request.setAttribute("carLogs", carLogs);
            request.setAttribute("memuId", request.getSession().getAttribute("menuId"));

            //挂车车辆类型与轴数、最大轴荷的对应关系配置
            List<TytSource> tailCarTypeAxlesAndMaximumAxleLoadList = TytSourceUtil.getSourceList("tail_car_type_axles_and_maximum_axle_load");
            request.setAttribute("tailCarTypeAxlesAndMaximumAxleLoadList", tailCarTypeAxlesAndMaximumAxleLoadList);

            return "/back/jsp/car";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 车辆图片链接兼容
     */
    private void carUrlCompat(CarQueryBean car) {
        car.setHeadDrivingUrl(pictureUtil.getPictureUrl(car.getHeadDrivingUrl()));
        car.setTailDrivingUrl(pictureUtil.getPictureUrl(car.getTailDrivingUrl()));
        car.setRoadCardPositiveUrl(pictureUtil.getPictureUrl(car.getRoadCardPositiveUrl()));
        car.setRoadCardOtherSideUrl(pictureUtil.getPictureUrl(car.getRoadCardOtherSideUrl()));
        car.setRoadLicenseNoUrl(pictureUtil.getPictureUrl(car.getRoadLicenseNoUrl()));
        car.setHeadDrivingSubpagePicUrl(pictureUtil.getPictureUrl(car.getHeadDrivingSubpagePicUrl()));
        car.setHeadTransportHomepageUrl(pictureUtil.getPictureUrl(car.getHeadTransportHomepageUrl()));
        car.setHeadTransportSubpagePicUrl(pictureUtil.getPictureUrl(car.getHeadTransportSubpagePicUrl()));
        car.setTailDrivingOtherSideUrl(pictureUtil.getPictureUrl(car.getTailDrivingOtherSideUrl()));
        car.setTailDrivingSubpagePicUrl(pictureUtil.getPictureUrl(car.getTailDrivingSubpagePicUrl()));
        car.setTailTransportHomepagePicUrl(pictureUtil.getPictureUrl(car.getTailTransportHomepagePicUrl()));
        car.setTailTransportSubpagePicUrl(pictureUtil.getPictureUrl(car.getTailTransportSubpagePicUrl()));
        car.setTailPhotoUrl(pictureUtil.getPictureUrl(car.getTailPhotoUrl()));
        car.setRoadCardPositiveUrl(pictureUtil.getPictureUrl(car.getRoadCardPositiveUrl()));
        car.setRoadCardOtherSideUrl(pictureUtil.getPictureUrl(car.getRoadCardOtherSideUrl()));
        car.setRoadLicenseNoUrl(pictureUtil.getPictureUrl(car.getRoadLicenseNoUrl()));
    }

    /**
     * 根据车主USERiD查找车主维护人。（车辆认证失败时使用发送站内信）
     *
     * @param userId
     * @return
     */
    @RequestMapping("/getMaintainMan")
    @ResponseBody
    public ResultMsgBean getMaintainMan(Long userId) {
        ResultMsgBean result = new ResultMsgBean();
        try {
            if (userId != null && userId.longValue() > 0) {
                CsMaintainedCustom csMaintainedCustom = new CsMaintainedCustom();
                csMaintainedCustom.setCustomId(userId);
                csMaintainedCustom.setStatus((short) 1);
                CsMaintainedCustom maintainedCustom = maintainCustomNewService.find(csMaintainedCustom);
                if (maintainedCustom != null && maintainedCustom.getMaintainerId() != null && StringUtil.isNotEmpty(maintainedCustom.getMaintainerName())) {
                    result.setCode(200);
                    TytUserSub tytUserSub = new TytUserSub();
                    tytUserSub.setMaintainMan(maintainedCustom.getMaintainerName());
                    result.setData(tytUserSub);
                }
            } else {
                result.setCode(500);
                result.setMsg("用户ID为空");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("查询失败");
        }
        return result;

    }

    /**
     * 修改车辆认证信息
     *
     * @param id
     * @param failureReason
     * @param remark
     * @param status
     * @param request
     * @param response
     */
    @RequestMapping("/update")
    public String updateCar(CarQueryBean car, String carAuthOld,
                            HttpServletRequest request, HttpServletResponse response) {
        logger.info("------------carUpdate start-------------------");
        Long startTime = System.currentTimeMillis();
        try {
            //校验车头和挂车总质量、整备质量、核定载质量、准牵引总质量只能输入数字，不能输入kg
            String hTotalWeight = car.gethTotalWeight();
            String hCurbWeight = car.gethCurbWeight();
            String hCheckWeight = car.gethCheckWeight();

            String tTotalWeight = car.gettTotalWeight();
            String tCurbWeight = car.gettCurbWeight();
            String tCheckWeight = car.gettCheckWeight();
            
            if(!isNumeric(hTotalWeight)|| !isNumeric(hCurbWeight) || !isNumeric(hCheckWeight)
            || !isNumeric(tTotalWeight)|| !isNumeric(tCurbWeight) || !isNumeric(tCheckWeight)){
                request.setAttribute("message", "车头或者挂车质量参数有误，只能输入纯数字");
            }
            EmployeeQueryBean user = getCurrentUser(request);
            Map<String, String> params = new HashMap<String, String>();
            params.put("headCity", car.getHeadCity());
            params.put("headNo", car.getHeadNo());
            params.put("tailCity", car.getTailCity());
            params.put("tailNo", car.getTailNo());
            params.put("userId", car.getUserId().toString());
            if (user == null) {
                request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
                return "back/jsp/admin_login";
            } else if (null == car.getSex() || "".equals(car.getSex())) {
                request.setAttribute("car", car);
                request.setAttribute("message", "车主性别不能为空");
            }
            car.setHeadNo(car.getHeadNo().toUpperCase());
            car.setTailNo(car.getTailNo().toUpperCase());

            if ("1".equals(car.getHeadAuthStatus()) && car.getHeadTransportAuthStatus() == 1 && (StringUtils.isNotBlank(car.getHeadCarNo()))){
                String headNo = car.getHeadCity()+car.getHeadNo();
                if (!car.getHeadCarNo().equals(headNo)){
                    request.setAttribute("message", "车头牌照号与车头道路运输证牌照号不一致");
                    return getIdentityDetail(car.getId(), null, request, response);
                }
            }

            if ("1".equals(car.getTailAuthStatus()) && car.getTailTransportAuthStatus() == 1 && (StringUtils.isNotBlank(car.getTailCarNo()))){
                String tailNo = car.getTailCity()+car.getTailNo();
                if (!car.getTailCarNo().equals(tailNo)){
                    request.setAttribute("message", "挂车牌照号与挂车道路运输证牌照号不一致");
                    return getIdentityDetail(car.getId(), null, request, response);
                }
            }

            //车牌号字母纠正
            formatCarNo(car);

            // btt5910 车辆认证二期
            //-- zgz：20190627 修复之前的线上bug,车辆认证成功方式authType为null导致的空指针异常
            Car oldCar = carService.getById(car.getId());
            Integer oldCarDegree=oldCar.getCarDegree();
            String oldCarAuth=oldCar.getAuth();
            //认证成功方式 1：快速认证成功 2：正常认证成功
            int authType = 0;
            if(oldCar != null){
                if(oldCar.getAuthType() != null){
                    authType = oldCar.getAuthType().intValue();
                }
            }
            //-- zgz：20190627 btt5910提出的做判断的需求和作用是什么？前目不清楚,但是确实有空指针异常
            if (("1".equals(car.getTailAuthStatus())) && ("1".equals(car.getHeadAuthStatus())) && carService.get(params) && "1".equals(carAuthOld) && (authType == 2)) {
                request.setAttribute("car", car);
                logger.error("该车辆认证已认证过");
                request.setAttribute("message", "该车辆已认证，请勿重复认证");
            }
            if ("1".equals(car.getIsDispatch())){
                String roadCardPositiveUrl = upload(car.getRoadCardPositivePic(), car.getRoadCardPositivePicName());
                if (StringUtils.isNotEmpty(roadCardPositiveUrl)){
                    car.setRoadCardPositiveUrl(roadCardPositiveUrl);
                }
                String roadCardOtherSideUrl = upload(car.getRoadCardOtherSidePic(), car.getRoadCardOtherSidePicName());
                if (StringUtils.isNotEmpty(roadCardOtherSideUrl)){
                    car.setRoadCardOtherSideUrl(roadCardOtherSideUrl);
                }
                String roadLicenseNoUrl = upload(car.getRoadLicenseNoPic(), car.getRoadLicenseNoPicName());
                if (StringUtil.isNotEmpty(roadLicenseNoUrl)){
                    car.setRoadLicenseNoUrl(roadLicenseNoUrl);
                }
            }
            car = carService.saveCarDetail(car, carAuthOld, user);

            logger.info("--------carUpdate saveCarDetail，耗时："+(System.currentTimeMillis()-startTime));
            //同步数据到车辆定位表发mq
            MqSyncAuthCarMsg mqBean = carService.updateLocation(car, carAuthOld);
            // btt5910修改 车辆认证成功 修改用户权益信息
            // app5930对分配用户权益进行修改
            tytUserSubService.updateRight(oldCar.getUserId(), car.getAuth(), carAuthOld);
            logger.info("--------carUpdate updateRight，耗时："+(System.currentTimeMillis()-startTime));
            logger.info("updateLocation mqBean: " + mqBean);
            if (mqBean != null) {
                tytMqMessageService.sendMqMessage(mqBean.getMessageSerailNum(), JSON.toJSONString(mqBean));
            }
            //调度车辆通知调度接口
            String controlDomin = AppConfig.getProperty("control.server.domin");
            String secret = AppConfig.getProperty("tyt.private.key");
            if (("3".equals(oldCarDegree+"")||"3".equals(car.getCarDegree()+""))&&("1".equals(car.getAuth())||"1".equals(oldCarAuth))){
                String informStatus="1";
                try {
                    if (("3".equals(oldCarDegree+"")&&!"3".equals(car.getCarDegree()+""))||(!"1".equals(car.getAuth())&&"1".equals(oldCarAuth))){
                        informStatus="2";
                    }
                    if (!"3".equals(oldCarDegree+"")&&"3".equals(car.getCarDegree()+"")){
                        //发送短信
                        tytMqMessageService.addSendSms(car.getCellPhone(), null, null,car.getHeadCity()+car.getHeadNo());
                    }
                    // 3/26 要下线特运通大件运输智能调度系统，先注释掉下面代码，过三个月可以删除
                    /*if (informStatus!=null){
                        TreeMap<String, Object> paramMap=new TreeMap<>();
                        paramMap.put("oldCarId",car.getId());
                        paramMap.put("ownerPhone",car.getCellPhone());
                        paramMap.put("type","2");
                        paramMap.put("informStatus",informStatus);
                        paramMap.put("authStatus",car.getAuth());
                        String sign = SignUtil.sign(paramMap, secret);
                        paramMap.put("sign",sign);
                        logger.info("通知调度车辆审核通过接口，参数json:{}",paramMap);
                        String result = HttpUtil.post(controlDomin + "/app/vehicle/informSyncCar", JSON.toJSONString(paramMap),3000);
                        logger.info("通知调度车辆审核通过接口，返回信息{}",result);
                    }*/
                }catch (Exception e){
                    e.printStackTrace();
                    logger.info("通知调度车辆审核通过接口失败，异常{}",e.getMessage());
                }
            }
//            if ("1".equals(oldCar.getIsDispatch())&&"1".equals(car.getAuth())){
            if ("1".equals(oldCar.getIsDispatch()) && ("1".equals(car.getAuth())||"2".equals(car.getAuth()))){ // 只要是调度用户，认证成功或失败时将通知调度系统
                try {
                    TreeMap<String, Object> paramMap=new TreeMap<>();
                    paramMap.put("oldCarId",car.getId());
                    paramMap.put("ownerPhone",car.getCellPhone());
                    paramMap.put("authStatus",car.getAuth());
                    String sign = SignUtil.sign(paramMap, secret);
                    paramMap.put("sign",sign);
                    logger.info("通知调度车辆审核通过接口，参数json:{}",paramMap);
                    String result = HttpUtil.post(controlDomin + "/app/vehicle/informSyncCar", JSON.toJSONString(paramMap),3000);
                    logger.info("通知调度车辆审核通过接口，返回信息{}",result);
                }catch (Exception e){
                    e.printStackTrace();
                    logger.info("通知调度车辆审核通过接口失败，异常{}",e.getMessage());
                }
            }
            request.setAttribute("car", car);
            //TODO modify by tianjw on 20180205 所有后台日志均记入tyt_manage_cud_log表中，不往log中记录
            /* 日志记录 */
//			createOpLog(user.getId(), OpLog.OP_CAR_AUTH, user.getLoginPhoneNo(), "无", "无", "无", Constant.PLAT_WEB, request, "修改编号" + car.getId() + "为" + (car.getAuth().equals("1") ? "认证通过" : car.getAuth().equals("2") ? "认证失败" : "认证中"));
            request.setAttribute("message", "操作成功");

        } catch (Exception e) {
            e.toString();
            logger.error("车辆认证审核", e);
            request.setAttribute("message", "操作错误请重试");
        }
        // request.setAttribute("car", car);
        logger.info("carUpdate end, 耗时："+ (System.currentTimeMillis()-startTime));
        return getIdentityDetail(car.getId(), null, request, response);

    }

    private void formatCarNo(CarQueryBean car) {
        car.setHeadCarNo(formatCarNo(car.getHeadCarNo()));
        car.setHeadNo(formatCarNo(car.getHeadNo()));
        car.setTailCarNo(formatCarNo(car.getTailCarNo()));
        car.setTailNo(formatCarNo(car.getTailNo()));
        car.setRoadCardCarNo(formatCarNo(car.getRoadCardCarNo()));
        car.sethCarNo(formatCarNo(car.gethCarNo()));
        car.settCarNo(formatCarNo(car.gettCarNo()));
    }

    /**
     * 车牌号字母I和O矫正为1和0
     */
    private String formatCarNo(String carNo) {
        if (StringUtils.isBlank(carNo) || carNo.length() < 2) {
            return carNo;
        }
        try {
            String substring = carNo.substring(1);
            substring = substring.replace("I", "1");
            substring = substring.replace("i", "1");
            substring = substring.replace("o", "0");
            substring = substring.replace("O","0");
            return carNo.charAt(0) + substring;
        } catch (Exception e) {
            logger.error("format car no error", e);
            return carNo;
        }
    }

    //判断传入的字段值是否为纯数字
    public static boolean isNumeric(String params){
        if(StringUtils.isBlank(params)){
            return true;
        }
        Pattern pattern = Pattern.compile("[0-9]*");
        boolean result = pattern.matcher(params).matches();
        return result;
    }

    @RequestMapping(value = "/saveInit")
    public String saveInit(Long userId, HttpServletRequest request) {
        if (!checkLogin(request)) {
            request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
            return "back/jsp/admin_login";
        }
        if (userId == null || 0 == userId.longValue()) {

            request.setAttribute("message", "操作错误请重试");
            return "back/jsp/car_save";
        }
        User nowUser = userService.getById(userId);
        request.setAttribute("userId", userId);
        request.setAttribute("phone", nowUser.getCellPhone());
        request.setAttribute("userName", nowUser.getUserName());
        List<TytSource> carTypeList = TytSourceUtil.getSourceList("tail_car_type");
        request.setAttribute("carTypeList", carTypeList);
        return "back/jsp/car_save";
    }

    /**
     * 客服代用户添加车辆信息
     */

    @RequestMapping(value = "/addCarByCs", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean addCarByCs(@RequestBody CarQueryBean carQueryBean, HttpServletRequest request) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        EmployeeQueryBean user = getCurrentUser(request);
        if (user == null) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("无法获取当前操作人信息！");
            return resultMsgBean;
        }
        //是否已经认证
        Map<String, String> params = new HashMap<String, String>() {{
            this.put("userId", String.valueOf(carQueryBean.getUserId()));
            this.put("headCity", carQueryBean.getHeadCity());
            this.put("headNo", carQueryBean.getHeadNo());
            this.put("tailCity", carQueryBean.getTailCity());
            this.put("tailNo", carQueryBean.getTailNo());
        }};
        //todo:事物原子性
        try {
            if (carService.get(params)) {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("车辆已认证，不能重复添加！");
                return resultMsgBean;
            }

            Car car = composeCarByCs(carQueryBean);
            car.setCreateBy(user.getUserName());
            long carId = carService.addCarByCs(car);
            carQueryBean.setId(carId);
            carService.saveCarDetailByCs(carQueryBean, user);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("查询车辆认证信息失败！");
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("操作异常，请重试");
            return resultMsgBean;
        }

    }


    private Car composeCarByCs(CarQueryBean carQueryBean) {
        Car car = new Car();
        car.setUserId(carQueryBean.getUserId());
        car.setHeadCity(carQueryBean.getHeadCity());
        car.setHeadNo(carQueryBean.getHeadNo());
        car.setTailCity(carQueryBean.getTailCity());
        car.setTailNo(carQueryBean.getTailNo());
        car.setAuth("1");//客服添加直接设置为认证通过
        car.setHasLadder(carQueryBean.getHasLadder());
        // 2017-07-19 车头车挂审核排序等默认信息设置
        car.setHeadAuthStatus("1");
        car.setTailAuthStatus("1");
        car.setFindGoodOnOff("0");
        BigInteger maxSort = carService.getMaxSort(carQueryBean.getUserId());
        if (maxSort == null) {
            maxSort = new BigInteger("0");
        }
        car.setSort(maxSort.longValue() + 1);
        car.setCreateTime(new Date());
        car.setUpdateTime(new Date());
        /* 图片处理 */
        car.setHeadDrivingUrl("");
        return car;
    }

    /**
     * 车辆认证信息
     *
     * @param id
     * @param failureReason
     * @param remark
     * @param status
     * @param request
     * @param response
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public String save(Long userId, CarQueryBean car, HttpServletRequest request, HttpServletResponse response
            , @RequestParam MultipartFile headDrivingPic, @RequestParam MultipartFile tailDrivingPic, @RequestParam MultipartFile tailDrivingOtherPic) {
        try {
            EmployeeQueryBean user = getCurrentUser(request);
            if (user == null) {
                request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
                return "back/jsp/admin_login";
            }
            List<TytSource> carTypeList = TytSourceUtil.getSourceList("tail_car_type");
            if (userId == null || 0 == userId.longValue()) {
                request.setAttribute("car", car);
                request.setAttribute("message", "操作错误请重试");
                request.setAttribute("carTypeList", carTypeList);
                return "back/jsp/car_save";
            }
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);

            User nowUser = userService.getById(userId);
            params.put("userId", nowUser.getId().toString());
            String condition = "userId_" + userId + "车辆认证 ";

            request.setAttribute("phone", nowUser.getCellPhone());
            request.setAttribute("userName", nowUser.getUserName());
            request.setAttribute("userId", userId);
            if (null == car.getHeadNo() || "".equals(car.getHeadNo())) {
                logger.error(condition + "车头车牌号码为空");
                request.setAttribute("car", car);
                request.setAttribute("message", "车头车牌号码不能为空");
                request.setAttribute("carTypeList", carTypeList);
                return "back/jsp/car_save";
            }
            if (headDrivingPic.isEmpty()) {
                logger.error(condition + "图片为空");
                request.setAttribute("car", car);
                request.setAttribute("message", "图片为空");
                request.setAttribute("carTypeList", carTypeList);
                return "back/jsp/car_save";
            }
            if (car.getCarType() == null) {
                logger.error(condition + "挂车型号为空");
                request.setAttribute("car", car);
                request.setAttribute("message", "挂车型号为空");
                request.setAttribute("carTypeList", carTypeList);
                return "back/jsp/car_save";
            }
            /* 验证重复信息？ */
            if (carService.get(params)) {
                logger.error(condition + "该车辆已存在并已认证通过");
                // request.setAttribute("car", car);
                request.setAttribute("message", "该车辆已存在并已认证通过");
                request.setAttribute("carTypeList", carTypeList);
                return "back/jsp/car_save";
            }

            Car carbean = createCar(params, headDrivingPic, tailDrivingPic, tailDrivingOtherPic);
            headDrivingPic.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + carbean.getHeadDrivingUrl()));
            if (!tailDrivingPic.isEmpty()) {
                tailDrivingPic.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + carbean.getTailDrivingUrl()));
            }
            if (!tailDrivingOtherPic.isEmpty()) {
                String lastTailNoChar = params.get("tailNo").substring(params.get("tailNo").length() - 1, params.get("tailNo").length());
                if ("超".equals(lastTailNoChar)) {
                    tailDrivingOtherPic.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + carbean.getTailDrivingOtherSideUrl()));
                } else {
                    tailDrivingOtherPic.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + carbean.getTailDrivingSubpagePicUrl()));
                }
            }
            if (nowUser.getIsDispatch()!=null && nowUser.getIsDispatch().equals(1)){
                carbean.setIsDispatch("1");
                carbean.setHeadFailReason(Constant.CAR_FAILURE_HEAD_REASON);
                carbean.setAuth("2");
                carbean.setHeadAuthStatus("2");
                carbean.setTailAuthStatus("2");
                carbean.setTailFailReason(Constant.CAR_FAILURE_TAIL_REASON);
                carbean.setCarType(0);
            }else {
                carbean.setIsDispatch("0");
            }
            carService.addCar(carbean);
            // backResponse(request, response, ReturnCodeConstant.OK,
            // "车辆信息添加成功",null,0);
            logger.info(condition + "添加成功");
            request.setAttribute("car", null);

            request.setAttribute("message", "车辆添加成功");
        } catch (Exception e) {
            logger.error("添加车辆认证异常", e);
            request.setAttribute("message", "操作错误请重试");
        }
        List<TytSource> carTypeList = TytSourceUtil.getSourceList("tail_car_type");
        request.setAttribute("carTypeList", carTypeList);
        return "back/jsp/car_save";
    }

    /**
     * 创建Car
     *
     * @param params
     * @param headDrivingPic
     * @param tailDrivingPic
     * @param headPic
     * @return
     * @throws Exception
     */
    private Car createCar(Map<String, String> params, MultipartFile headDrivingPic, MultipartFile tailDrivingPic,MultipartFile tailDrivingOtherPic) throws Exception {
        Car car = new Car();
        car.setUserId(Long.parseLong(params.get("userId")));
        car.setHeadCity(params.get("headCity"));
        car.setHeadNo(params.get("headNo"));
        car.setTailCity(params.get("tailCity"));
        car.setTailNo(params.get("tailNo"));
        car.setSource(StringUtils.isNotBlank(params.get("source")) ? Integer.valueOf(params.get("source")):null);
        // car.setType(params.get("type"));
        // car.setTypeCode(params.get("typeCode"));
        // car.setLength(params.get("length"));
        // car.setLengthCode(params.get("lengthCode"));
        // car.setCarry(params.get("load"));
        // car.setCarryCode(params.get("loadCode"));
        // car.setInsuranceCode(params.get("insuranceCode"));
        // car.setInsurance(params.get("insurance"));
        // car.setExpireTime(TimeUtil.parseString(params.get("expireTime")));
        car.setAuth("0");
        //2017-11-03车头车挂审核排序等默认信息设置
        car.setHeadAuthStatus("0");
        car.setTailAuthStatus("0");
        car.setFindGoodOnOff("0");
        BigInteger maxSort = carService.getMaxSort(Long.parseLong(params.get("userId")));
        if (maxSort == null) {
            maxSort = new BigInteger("0");
        }
        car.setSort(maxSort.longValue() + 1);
        car.setCreateTime(new Date());
        car.setUpdateTime(new Date());

        /* 图片处理 */
        if (!tailDrivingOtherPic.isEmpty()) {
            String lastTailNoChar = params.get("tailNo").substring(params.get("tailNo").length() - 1, params.get("tailNo").length());
            if ("超".equals(lastTailNoChar)) {
                car.setTailDrivingOtherSideUrl(renamePic(tailDrivingOtherPic, "car"));
            } else {
                car.setTailDrivingSubpagePicUrl(renamePic(tailDrivingOtherPic, "car"));
            }
        }
        car.setHeadDrivingUrl(renamePic(headDrivingPic, "car"));
        if (!tailDrivingPic.isEmpty()) {
            car.setTailDrivingUrl(renamePic(tailDrivingPic, "car"));
        }
        car.setCarType(Integer.valueOf(params.get("carType")));

        return car;
    }

    @RequestMapping("/deleteCar")
    @ResponseBody
    public void deleteCar(HttpServletRequest request, HttpServletResponse response, String id) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "删除车辆");
        try {
            EmployeeQueryBean user = getCurrentUser(request);
            if (user == null) {
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("删除失败");
                return;
            }
            carService.deleteCar(id, user);
            //车辆删除同步数据到车辆定位表发mq
            MqSyncAuthCarMsg mqBean = carService.updateLocationByCarId(id);
            logger.info("updateLocation mqBean: " + mqBean);
            if (mqBean != null) {
                tytMqMessageService.sendMqMessage(mqBean.getMessageSerailNum(), JSON.toJSONString(mqBean));
            }
        } catch (Exception e) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("删除失败");
            e.printStackTrace();
        }
        msgBean.setCode(ResultMsgBean.OK);
        msgBean.setMsg("删除成功");
        printJSON(request, response, msgBean);
        return;
    }

    private void updateSuperiorCarSignStatus(String id, EmployeeQueryBean user) {
        Car car = carService.getById(Long.valueOf(id));
        if (car!=null) {
            TytSuperiorCarSign signByUserId = superiorCarSignService.getSignByUserId(car.getUserId());
            if (signByUserId!=null&&signByUserId.getSignStatus()!=null&&signByUserId.getSignStatus()==1) {
                CarQueryBean carQueryBean=new CarQueryBean();

                carQueryBean.setUserId(car.getUserId());
                PageBean pageBean = new PageBean();
                pageBean.setCurrentPage(1);
                pageBean.setPageSize(100);
                List<CarQueryBean> carQueryBeanList = carService.getCarQueryBeanList(carQueryBean,pageBean);
                if (carQueryBeanList!=null&&carQueryBeanList.size()>0) {
                    List<CarQueryBean> collect = carQueryBeanList.stream().filter(c -> c.getId()!=car.getId()&&c.getIsDelete().equals("1")).collect(Collectors.toList());
                    if (collect.size()==0) {
                        //正常状态删除车辆则更新为签约未审核
                        SuperiorCarSignUpdateStatusReqVo signDao = new SuperiorCarSignUpdateStatusReqVo();
                        signDao.setSignId(signByUserId.getId());
//                        signDao.setSignStatus(0);
                        signDao.setActionUserId(user.getId());
                        signDao.setActionUserName(user.getUserName());
                        superiorCarSignService.updateStatus(signDao);
                    }
                }
            }

        }
    }

    @RequestMapping(value = "/editBrand")
    public String editBrand(HttpServletRequest request) {
        if (!checkLogin(request)) {
            request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
            return "back/jsp/admin_login";
        }
        List<CarBrand> carBrandsList = carService.getCarBrandList();
        request.setAttribute("carBrandsList", carBrandsList);
        return "back/jsp/brand/car_brand";
    }

    @RequestMapping("/addBrand")
    @ResponseBody
    public void addBrand(HttpServletRequest request, HttpServletResponse response, String brandName, String brandType) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "更新数据");
        // 检查信息是否已经存在
        boolean isExist = carService.isBrandNameExist(brandName, brandType);
        if (isExist) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("同一类型的品牌名称不能重复");
        } else {
            // 保存品牌信息
            carService.addBrand(brandName, brandType);
            msgBean.setCode(ResultMsgBean.OK);
            msgBean.setMsg("添加成功");
        }
        printJSON(request, response, msgBean);
        return;
    }

    @RequestMapping("/deleteBrand")
    @ResponseBody
    public void deleteBrand(HttpServletRequest request, HttpServletResponse response, String brandName, String brandType, String id) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "更新数据");
        // 检查当前品牌信息是否已经被使用
        boolean isAlreadyUsed = carService.isBrandAlreadyUsed(brandName, brandType);
        if (isAlreadyUsed) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("此品牌已关联内容不能删除，请取消关联后再删除。");
        } else {
            // 删除品牌信息
            carService.deleteBrand(id);
            msgBean.setCode(ResultMsgBean.OK);
            msgBean.setMsg("删除成功");
        }
        printJSON(request, response, msgBean);
        return;
    }

    @RequestMapping("/checkBrand")
    @ResponseBody
    public void checkBrand(HttpServletRequest request, HttpServletResponse response, String brandName) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "更新数据");
        msgBean.setCode(ResultMsgBean.OK);
        // 检查挂车品牌是否存在
        boolean isTailBrandExits = carService.isTailBrandExits(brandName, 2);
        if (!isTailBrandExits) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("挂车品牌不存在");
        }
        printJSON(request, response, msgBean);
        return;
    }

    /**
     * 检查车头车挂信息是否存在
     *
     * @param request
     * @param response
     * @param brandName
     */
    @RequestMapping("/checkTailHead")
    @ResponseBody
    public void checkTailHead(HttpServletRequest request, HttpServletResponse response,
                              String headNo, String headCity, String tailNo, String tailCity, String userId, Long carId) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "更新数据");
        msgBean.setCode(ResultMsgBean.OK);
        //检查车头车挂在该用户下是否存在

        int counts = carService.isTailHeadExits(headCity, headNo, tailCity, tailNo, userId, carId);
        if (counts > 0) {
            msgBean.setCode(400);
            msgBean.setMsg("该车辆已认证，请勿重复认证");
            printJSON(request, response, msgBean);
            return;
        }
        // 检查车头挂车是否存在
        int count = carService.isTailHeadExits(headCity, headNo, tailCity, tailNo, carId);
        if (count > 0) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("【" + headCity + headNo + "+" + tailCity + tailNo + "】在平台已有" + count + "条通过认证的记录"
                    + "\n您确定要继续通过认证吗？");
        }
        printJSON(request, response, msgBean);
        return;
    }

    /**
     * 添加车辆类型页面
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/carTypeAdd")
    public String carTypeAdd(HttpServletRequest request) {
        if (!checkLogin(request)) {
            request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
            return "back/jsp/admin_login";
        }
        List<CarType> carTypeList = carService.getCarTypeListNew();
        request.setAttribute("carTypeList", carTypeList);
        return "back/jsp/carType/car_type";
    }

    /**
     * 保存当前车辆类型操作
     */
    @RequestMapping("/carTypeSave")
    @ResponseBody
    public void carTypeSave(HttpServletRequest request, HttpServletResponse response,Integer carClassify, String carType) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "更新数据");
        // 检查信息是否已经存在
        int counter = carService.countCarType(carClassify,carType);
        //车辆类型名称
        String classifyName = "";
        if (counter > 0) {
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("车辆类型名称不能重复");
        } else {
            // 保存车辆类型
            if(carClassify != null){
               if(carClassify.intValue() == 1){
                   classifyName = "车辆一级类型";
               }else if(carClassify.intValue() == 2){
                   classifyName = "车辆二级类型";
               }
            }
            carService.saveCarType(carClassify,carType);
            msgBean.setCode(ResultMsgBean.OK);
            msgBean.setMsg("["+classifyName+"]["+carType + "]添加成功");
        }
        printJSON(request, response, msgBean);
    }

    /**
     * 导出
     *
     * @param car
     * @param request
     * @param response
     */
    @RequestMapping(value = "/carExcelExport")
    @ResponseBody
    public ResultMsgBean recordExcelExport(CarQueryBean car, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean();
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            logger.info("未登录");
            result.setCode(500);
            result.setMsg("用户未登录");
            return result;
        }
        try {
//            // 标题栏
//            String[] header = {"ID", "用户ID", "手机号", "真是姓名", "车头牌照号", "挂车牌照号", "车头品牌", "挂车品牌", "认证时间", "车辆认证状态", "最新审核时间", "审核人"};
             String[] header = {"ID", "用户ID", "真实姓名", "车头牌照号", "挂车牌照号", "车头品牌", "挂车品牌", "认证时间", "车辆认证状态", "最新审核时间", "审核人"};

            PageBean pageBean = new PageBean();
            pageBean.setPageSize(Integer.MAX_VALUE);
            if ((car.getStartDate() == null || "".equals(car.getStartDate())) && (car.getStartExamineTime() == null || "".equals(car.getStartExamineTime()))) {
                car.setStartExamineTime(TimeUtil.formatDateTime(TimeUtil.dateDiff(-1)));
            }
            List<CarQueryBean> carList = carService.getCarList(car, pageBean);
            List<CarExportExcelBean> csvStr = carService.getStringCsv(carList);
            ExportExcel<CarExportExcelBean> exportExcel = new ExportExcel<CarExportExcelBean>();
            response.setContentType("application/vnd.ms-excell;charset=GB2312");
            try {
                response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("特运通车辆认证列表_" + TimeUtil.formatDateYMDHMS(new Date()), "utf-8") + ".xls");
            } catch (UnsupportedEncodingException e) {
                logger.info("admin:export info history stat Exception." + e.toString());
                e.printStackTrace();
            }
            OutputStream ouputStream;
            try {
                ouputStream = response.getOutputStream();
                exportExcel.exportExcel("特运通车辆认证列表_" + TimeUtil.formatDateYMDHMS(new Date()), header, csvStr, ouputStream);
                ouputStream.flush();
                ouputStream.close();
            } catch (IOException e) {
                logger.info("admin:export user Exception. " + e);
                e.printStackTrace();
            }
            if (carList != null) {
                carList.clear();
            }
            if (csvStr != null) {
                csvStr.clear();
            }
            result.setCode(200);
            result.setMsg("导出成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 初始化车辆偏好表new
     */
    @RequestMapping("/carinit")
    @ResponseBody
    public void carinit(HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "更新数据");
        List<Car> list = carService.getList(" entity.isDelete=1", null);
        for (Car car : list) {
            carService.insertPreferNew(car.getId());
        }
        printJSON(request, response, msgBean);
    }

    /**
     * 车辆类型删除功能
     * @param id 车辆类型id
     * @return
     */
    @RequestMapping(value = "/deleteCarType")
    @ResponseBody
    public ResultMsgBean deleteCarType(@RequestParam(value = "id", required = true) Integer id) {
        ResultMsgBean result = new ResultMsgBean();
        try {
            carService.deleteCarType(id);
            result.setCode(ResultMsgBean.OK);
            result.setMsg("车辆类型删除成功");

        }catch (Exception e){
            e.printStackTrace();
            result.setCode(ResultMsgBean.ERROR);
            result.setMsg("服务器异常");
        }
        return result;
    }

    /**
     * 快速认证车辆接口
     * @param carId 车辆id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/carQuickAuth")
    public ResultMsgBean carQuickAuth(Long carId, HttpServletRequest request) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try{
            Car car = carService.getById(carId);
            String carAuthOld = car.getAuth();
            if (car != null) {
                if ("1".equals(carAuthOld)) {
                    resultMsgBean.setCode(201);
                    resultMsgBean.setMsg("车辆已经是认证成功状态");
                } else {
                    long userId = car.getUserId();
                    EmployeeQueryBean user = getCurrentUser(request);
                    // 修改车辆为认证成功状态
                    carService.updateCarQuickAuth(carId, user);
                    // 修改用户车辆认证状态
                    userService.updateCarAuth(userId);
                    // btt5910修改 车辆快速认证成功 修改用户权益信息
                    tytUserSubService.updateRight(userId, "1", carAuthOld);
                    User byUserId = userService.getByUserId(car.getUserId());
                    System.out.println(byUserId.getIsDispatch()+"+++++++++++++++++++++++++++++++++++++++++++++");
                    // 3/26 要下线特运通大件运输智能调度系统,先注释掉下面代码，过三个月可以删除
                    /*if (byUserId.getIsDispatch()!=null&&"1".equals(byUserId.getIsDispatch().toString())){
                        String controlDomin = AppConfig.getProperty("control.server.domin");
                        String secret = AppConfig.getProperty("tyt.private.key");
                        try {
                            TreeMap<String, Object> paramMap=new TreeMap<>();
                            paramMap.put("oldCarId",car.getId());
                            paramMap.put("ownerPhone",byUserId.getCellPhone());
                            paramMap.put("authStatus",car.getAuth());
                            String sign = SignUtil.sign(paramMap, secret);
                            paramMap.put("sign",sign);
                            logger.info("通知调度车辆审核通过接口，参数json:{}",paramMap);
                            String result = HttpUtil.post(controlDomin + "/app/vehicle/informSyncCar", JSON.toJSONString(paramMap),3000);
                            logger.info("通知调度车辆审核通过接口，返回信息{}",result);
                        }catch (Exception e){
                            e.printStackTrace();
                            logger.info("通知调度车辆审核通过接口失败，异常{}",e.getMessage());
                        }
                    }*/
//                    carService.savePushShortForSuperiorCarSign(car,user);
                }
            } else {
                resultMsgBean.setCode(201);
                resultMsgBean.setMsg("参数错误，无法获取车辆信息");
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg(e.getMessage());
            return resultMsgBean;
        }
    }
    /**
     * 重命名图片
     *
     * @param pic 待上传的图片
     * @param typeName 文件保存分目录名称
     * @return
     */
    @Override
    protected String renamePic(MultipartFile pic, String typeName) {
        // String fileSeparator=System.getProperty("file.separator");//获取系统文件分隔符
        String domainurl = "/data/pictures/" + typeName + "/";// 获取文件路径
        CreateFileUtil.createDir(AppConfig.getProperty("picture.file.path.domain") + domainurl);
        return domainurl + ImageUtil.renameFile(pic.getOriginalFilename());
    }

    /**
     * 上传图片
     *
     * @param base64Str
     * @return
     */
    private String upload(String base64Str, String originalFilename) {
        if (StringUtils.isNotEmpty(base64Str) ) {
            MultipartFile multipartFile = Base64Util.base64ToMultipart(base64Str);
            String url = renamePic(multipartFile, "car");
            try {
                multipartFile.transferTo(new File(AppConfig.getProperty("picture.file.path.domain")+url));
                return url;
            } catch (IOException e) {
                e.printStackTrace();
            }
            return null;
        }
        return null;
    }


    @RequestMapping("/getCarLoadRecord")
    @ResponseBody
    public ResultMsgBean getCarLoadRecord(){
        List<TytCarLoadRecord> records = carLoadRecordService.findList(new TytCarLoadRecord());
        Map<Object, Object> map = new HashMap<>();
        map.put("record", records);
        return new ResultMsgBean(200,"查询成功", map);
    }

    @RequestMapping("/OCRDriverlicense")
    @ResponseBody
    public ResultMsgBean OCRDriverLicense(Long carId,String type){
        if (carId==null||StringUtils.isBlank(type)){
            return new ResultMsgBean(500,"参数异常");
        }
        return carService.createDriverLicense(carId, type);
    }

    @PostMapping("/OCRDriverLicenseNew")
    @ResponseBody
    public ResultMsgBean OCRDriverLicenseNew(Long carId, String type){
        if (carId == null || StringUtils.isBlank(type) || ("1".equals(type) && "3".equals(type) && "4".equals(type))){
            return new ResultMsgBean(500,"参数异常");
        }
        return carService.createDriverLicenseNew(carId, type);
    }

    /**
     * 车辆信息验真
     * @param carId 车辆id
     * @param type 0 车头信息验真 1 挂车信息验真 2车头道路运输证验真 3 挂车道路运输证验真
     * @return 6430 目前为假验真 每次验真都返回验真成功
     */
    @RequestMapping(value = "carInfoTest")
    @ResponseBody
    public ResultMsgBean carInfoTest(Long carId,Integer type) {
        if (Objects.isNull(carId) || Objects.isNull(type)) {
            return new ResultMsgBean(500, "参数异常");
        }
        try {
            carService.carInfoTest(carId, type);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            logger.error("车辆信息验真异常:",e);
        }
        return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "验真异常");
    }

    /**
     * 校验开票要求
     * @param car
     * @param flag 1大件宝要求 2三方要求
     * @return
     */
    @RequestMapping(value = "checkInvoice")
    @ResponseBody
    public ResultMsgBean checkInvoice(CarQueryBean car, Integer flag) {
        try {
            if (Objects.isNull(car)) {
                ResultMsgBean.failResponse(TypedResultMsgBean.ERROR, "参数异常");
            }
            Car carOld = carService.getById(car.getId());
            if (Objects.isNull(carOld)) {
                ResultMsgBean.failResponse(TypedResultMsgBean.ERROR, "参数异常");
            }
            StringBuilder msg = new StringBuilder();
            int carStatus = CarInvoiceStatusEnum.INVOICE.getCode();
            if (StringUtils.isBlank(carOld.getHeadDrivingUrl())) {
                carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                msg.append("缺少车头行驶证主页；");
            }
            Integer carTailType = car.getCarType();
            if (null == carTailType || 15 != carTailType){
                if (StringUtils.isBlank(carOld.getHeadTransportHomepageUrl())) {
                    carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                    msg.append("缺少车头道路运输证主页；");
                }
            }
            int headCarType = Constant.TRACTOR; //1牵引车 2 单机板
            //判断车辆是否为单机板
            if (StringUtils.isNotBlank(car.gethCarType())) {
                CarType carType = carService.getCarTypeById(Integer.parseInt(car.gethCarType()));
                if (carType != null && !carType.getCarType().contains("牵引")) {
                    headCarType = Constant.SINGLE_CAR;
                }
            }
            if (null != car.getHeadDrivingExpiredTime()
                    && TimeUtil.weeHours(car.getHeadDrivingExpiredTime(), 1).before(new Date())
                    && StringUtils.isBlank(carOld.getHeadDrivingSubpagePicUrl())) {
                carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                msg.append("缺少车头行驶证副页；");
            }
            if (headCarType == Constant.TRACTOR && (null == carTailType || 15 != carTailType)) {
                if (StringUtils.isBlank(carOld.getTailDrivingUrl())) {
                    carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                    msg.append("缺少挂车行驶证主页；");
                }
                if (car.getTailNo().contains("超") && StringUtils.isBlank(carOld.getTailDrivingOtherSideUrl())) {
                    carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                    msg.append("缺少挂车行驶证副页；");
                }
                if(null == car.getTailDrivingExpiredTime() || TimeUtil.weeHours(car.getTailDrivingExpiredTime(), 1).before(new Date())){
                    carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                    msg.append("挂车行驶证已过期；");
                }

                if (!car.getTailNo().contains("超")){
                    if (car.getTailDrivingExpiredTime() != null
                            && TimeUtil.weeHours(car.getTailDrivingExpiredTime(), 1).before(new Date())
                            && StringUtils.isBlank(carOld.getTailDrivingSubpagePicUrl())) {
                        carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                        msg.append("缺少挂车行驶证副页；");
                    }
                    if (flag == 1 && StringUtils.isBlank(carOld.getTailTransportHomepagePicUrl())){
                        carStatus = CarInvoiceStatusEnum.DEFICIENCY.getCode();
                        msg.append("缺少挂车道路运输证主页；");
                    }

                }
            }
            if (carStatus == CarInvoiceStatusEnum.DEFICIENCY.getCode()) {
                return ResultMsgBean.successResponse(CarStatusResultBean.builder().carStatus(CarInvoiceStatusEnum.DEFICIENCY.getCode()).msg(msg.toString()).build());
            }
            //不符合开票规则判断
            if (flag == 2){
                carStatus = carService.checkThirdPartyParamNull(car, headCarType);
            }else {
                carStatus = carService.checkParamNull(car, headCarType);
            }
            if (carStatus == CarInvoiceStatusEnum.NO_INVOICE.getCode()) {
                msg.append("字段缺失；");
            }

            if (StringUtils.isNotBlank(car.getHeadCarNo()) && !car.getHeadCarNo().equals(car.getHeadCity().concat(car.getHeadNo()))){
                carStatus = CarInvoiceStatusEnum.NO_INVOICE.getCode();
                msg.append("车头信息车牌号与车头道路运输证车牌号不一致；");
            }

            if (null != car.getHeadDrivingExpiredTime() && TimeUtil.weeHours(car.getHeadDrivingExpiredTime(), 1).before(new Date())) {
                carStatus = CarInvoiceStatusEnum.NO_INVOICE.getCode();
                msg.append("车头检验有效期过期；");
            }
            if (flag == 1 && headCarType == Constant.TRACTOR && !car.getTailNo().contains("超") && StringUtils.isNotBlank(car.getTailCarNo()) && !car.getTailCarNo().equals(car.getTailCity().concat(car.getTailNo()))){
                carStatus = CarInvoiceStatusEnum.NO_INVOICE.getCode();
                msg.append("挂车信息车牌号与挂车道路运输证车牌号不一致；");
            }
            if (null != car.getTailDrivingExpiredTime() && headCarType == Constant.TRACTOR && TimeUtil.weeHours(car.getTailDrivingExpiredTime(), 1).before(new Date())) {
                carStatus = CarInvoiceStatusEnum.NO_INVOICE.getCode();
                msg.append("挂车检验有效期过期；");
            }
            return ResultMsgBean.successResponse(CarStatusResultBean.builder().carStatus(carStatus).msg(msg.toString()).build());
        } catch (Exception e) {
            logger.error("校验是否满足大件宝要求:", e);
            return ResultMsgBean.failResponse(TypedResultMsgBean.ERROR, "校验是否满足大件宝要求异常");
        }
    }

    @RequestMapping(value = "checkCarInvoice")
    @ResponseBody
    public ResultMsgBean checkCarInvoice(CarQueryBean carQueryBean) {

        CarCheckRpcVO carCheckRpcVO = carService.checkCarInvoice(carQueryBean);
        return ResultMsgBean.successResponse(CarStatusResultBean.builder().carStatus(carCheckRpcVO.getCarCheckStatus()).msg(carCheckRpcVO.getCarCheckRemark()).build());
    }

    /**
     * 车辆上传证件
     * @param file 文件
     * @param documentType 类型
     * @param carId 车辆id
     * @return rm
     */
    @RequestMapping(value = "uploadDocument")
    @ResponseBody
    public ResultMsgBean uploadDocument(@RequestParam(value = "uploadFile")MultipartFile uploadFile,
                                        @RequestParam(value = "documentType")Integer documentType,
                                        @RequestParam(value = "carId")Long carId,
                                        HttpServletRequest request) {
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            return ResultMsgBean.failResponse(10001, "操作用户未登录");
        }
        try {
            //根据carId获取车辆信息
            Car car = carService.getById(carId);
            if (Objects.isNull(car)){
                return ResultMsgBean.failResponse(TypedResultMsgBean.ERROR, "车辆不存在");
            }
            String url = renamePic(uploadFile, "car");
            String newUrl = carService.updateDocument(car, documentType, uploadFile, url, curUser);
            return ResultMsgBean.successResponse(newUrl);
        } catch (Exception e) {
            logger.error("文件上传失败，", e);
            return ResultMsgBean.failResponse(TypedResultMsgBean.ERROR, "文件上传失败");
        }
    }




}
