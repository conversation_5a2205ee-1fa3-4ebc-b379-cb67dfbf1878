package com.tyt.web.back.internal;

import java.sql.Timestamp;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tyt.model.Blocker;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.blocker.BlockerService;
import com.tyt.web.base.BaseController;

@Controller
@RequestMapping("/blocker")
public class BlockerController extends BaseController {
	
	 @Resource(name = "blockerService")
	 private BlockerService blockerService;
	 
    @RequestMapping(value = "/query")
    public void query(@RequestParam(value = "id", defaultValue = "-1") int id,
                      @RequestParam(value = "isBlack", defaultValue = "2") int isBlack,
                      @RequestParam(value = "blockType", defaultValue = "1") int blockType,
                      @RequestParam(value = "page",defaultValue = "1") int pageNo,
                      @RequestParam(value = "size",defaultValue = "5000") int pageSize,
                      @RequestParam(value = "token", defaultValue = "") String token,
                      @RequestParam(value = "version",defaultValue = "") String version,
                      @RequestParam(value = "platId", defaultValue = "1") Integer platId,
                      HttpServletRequest request,
                      HttpServletResponse response) {

    	if(!validateToken(""+id,token,request,response)) {
            return;
        }
    	
    	 
        StringBuffer sql = new StringBuffer();
        boolean hasFirst = false;
        if(blockType != 0) { 
          sql.append(" entity.blockType=").append(blockType);
          hasFirst = true;
        }
        if(isBlack != 0) {
          if(hasFirst) sql.append(" and ");
          sql.append(" entity.isBlack=").append(isBlack);
          hasFirst = true;
        }  
        if(hasFirst) sql.append(" and ");
        sql.append(" entity.id >").append(id);	
        sql.append(" order by entity.id asc ");

        PageBean pageBean = new PageBean();
        pageBean.setCurrentPage(pageNo);
        pageBean.setPageSize(pageSize);
        
        ResultMsgBean msgBean  = null;
        try {
          List<Blocker> blockList = blockerService.getList(sql.toString(), pageBean);
          msgBean = new ResultMsgBean(ResultMsgBean.OK,"1");
          msgBean.setData(blockList);
        } catch (Exception e) {
          logger.error("blocker qeury:"+e.toString());
          msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"");        	
        }
        printJSON(request, response, msgBean);
        
    }
    
    
    @RequestMapping(value = "/save")
    public void save(
    	              String blockContent,
                      @RequestParam(value = "isBlack", defaultValue = "2") int isBlack,
                      @RequestParam(value = "blockType", defaultValue = "1") int blockType,
                      @RequestParam(value = "token", defaultValue = "") String token,
                      @RequestParam(value = "version",defaultValue = "") String version,
                      @RequestParam(value = "platId", defaultValue = "1") Integer platId,
                      HttpServletRequest request,
                      HttpServletResponse response) {
    	
    	if(!validateToken(""+blockContent,token,request,response)) {
            return;
        }
    	
    	Blocker block = new Blocker();
    	block.setIsBlack(isBlack);
    	block.setBlockType(blockType);
    	blockContent = (null == blockContent) ? "": blockContent.trim();
    	block.setBlockContent(blockContent);
    	//block.setPlatId(platId);
    	Timestamp ctime = new Timestamp(System.currentTimeMillis());
    	block.setCtime(ctime);
    	
    	
        
        ResultMsgBean msgBean  = null;
        try {
          blockerService.add(block);
          msgBean = new ResultMsgBean(ResultMsgBean.OK,"1");
        } catch (Exception e) {
          logger.error("blocker save:"+e.toString());
          msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"");        	
        }
        printJSON(request, response, msgBean);
        
    }
    
    
    @RequestMapping(value = "/delete")
    public void delete(
    		          @RequestParam(value = "id", defaultValue = "-1") long id,
                      @RequestParam(value = "token", defaultValue = "") String token,
                      @RequestParam(value = "version",defaultValue = "") String version,
                      @RequestParam(value = "platId", defaultValue = "1") Integer platId,
                      HttpServletRequest request,
                      HttpServletResponse response) {
    	
    	 if(!validateToken(""+id,token,request,response)) {
             return;
         }
    	 
        ResultMsgBean msgBean  = null;
        try {
          blockerService.delete(id);
          msgBean = new ResultMsgBean(ResultMsgBean.OK,"1");
        } catch (Exception e) {
          logger.error("blocker save:"+e.toString());
          msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"");        	
        }
        printJSON(request, response, msgBean);
        
    }
}
