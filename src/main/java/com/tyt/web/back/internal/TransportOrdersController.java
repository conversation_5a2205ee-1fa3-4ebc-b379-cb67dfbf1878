package com.tyt.web.back.internal;

import com.tyt.equipment.service.TytTransportWaybillExService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytTechRefundFinanceAudit;
import com.tyt.manager.entity.base.TytTransportOrderSnapshot;
import com.tyt.manager.entity.base.TytTransportTechnicalOrder;
import com.tyt.manager.mapper.base.TytTransportTechnicalOrderMapper;
import com.tyt.manager.vo.order.TransportOrdersVo;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.model.TytTransportWaybillEx;
import com.tyt.techRefund.service.TechRefundService;
import com.tyt.util.Constant;
import com.tyt.web.back.internal.bean.OrdersQueryBean;
import com.tyt.web.base.BaseController;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 车主接单查询
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/manage/transportOrders")
public class TransportOrdersController extends BaseController{
	
	@Resource(name = "transportOrdersService")
	TransportOrdersService transportOrdersService;

	@Autowired
	private TytTransportTechnicalOrderMapper transportTechnicalOrderMapper;

	@Autowired
	private TechRefundService techRefundService;

	@Autowired
	private TytTransportWaybillExService transportWaybillExService;

	/**
	 * 查询车主接单列表
	 * @param orders
	 * @param pageBean
	 * @param pageNo
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/list")
	public String getWayList(OrdersQueryBean orders,PageBean pageBean, Integer pageNo, 
			HttpServletRequest request,HttpServletResponse response) {
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			pageBean.setCurrentPage(pageNo == null ? 1 : pageNo);
			List<OrdersQueryBean> list = transportOrdersService.getOrders(pageBean, orders);
			if (list!=null) {
				list.stream().forEach(o -> {
					o.setUploadCellphone(Base64.getEncoder().encodeToString(o.getUserId().toString().getBytes()));
					o.setPayCellPhone(Base64.getEncoder().encodeToString(o.getPayUserId().toString().getBytes()));
				});
			}
			request.setAttribute("orderList", list);
			request.setAttribute("pageNo", pageBean.getCurrentPage());
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("maxPage", pageBean.getMaxPage());
			request.setAttribute("orders", orders);
			return "back/jsp/waybillOrderQuery/ordersList";
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/waybillOrderQuery/ordersList";
	}
	
	/**
	 * 查询详情
	 * @param id
	 * @return
	 */
	@RequestMapping("/getDetail")
	@ResponseBody
	public ResultMsgBean getOrdersDetail(@RequestParam(value="id", required=true)Long id){
		ResultMsgBean msg=new ResultMsgBean();
		try {
			TytTransportOrders detail = transportOrdersService.getDetail(id);
			if (detail==null) {
				msg.setCode(201);
				msg.setMsg("id错误");
				return msg;
			}

			TransportOrdersVo ordersVo = new TransportOrdersVo();
			BeanUtils.copyProperties(detail, ordersVo);

			TytTransportOrderSnapshot orderSnapshot = transportOrdersService.getOrderSnapshot(id);

			ordersVo.setTransportSnapshot(orderSnapshot);

			Long refundCarServiceAmount = 0L;
			Long dispatchRefundAmount = 0L;
			Long exRefundAmount = 0L;
			Long completeRefundAmount = 0L;
			Long offlineRefundAmount = 0L;

			//技术服务费退款状态 0未退款 1.退款中 2.退款成功 3.退款失败
			Integer tecFeeRefundStatus = 0;
			//技术服务费退款时间
			Date tecFeeRefundTime = null;
			//订金状态
			Integer costStatus = detail.getCostStatus();
			if(costStatus != null){
				String technicalServiceNo = detail.getTechnicalServiceNo();
				if(StringUtils.isNotBlank(technicalServiceNo)){
					TytTransportTechnicalOrder transportTechnicalOrder = transportTechnicalOrderMapper.selectByTechnicalServiceNo(technicalServiceNo);
					if(transportTechnicalOrder != null){
						Integer refundType = transportTechnicalOrder.getRefundType();
						//已退款
						if(35 == costStatus && refundType != null && 1 == refundType){
							dispatchRefundAmount = transportTechnicalOrder.getRefundAmount();
							if(dispatchRefundAmount == null){
								dispatchRefundAmount = 0L;
							}
							tecFeeRefundTime = transportTechnicalOrder.getRefundTime();
					   }
						//如果是线下退款，返回技术服务费线下退款金额
						if(refundType != null && 2 == refundType){
							offlineRefundAmount = transportTechnicalOrder.getRefundAmount();
							if(offlineRefundAmount == null){
								offlineRefundAmount = 0L;
							}
							tecFeeRefundTime = transportTechnicalOrder.getRefundTime();
						}
				   }
			    }
				if(50 == costStatus){ //异常上报处理完成
					exRefundAmount = detail.getCarServiceAmount();
					if(exRefundAmount == null){
						exRefundAmount = 0L;
					}
					tecFeeRefundTime = detail.getMtime();
				}
				//完单退还技术服务费(投诉如口)
				TytTransportWaybillEx transportWaybillEx = transportWaybillExService.getTransportWaybillExByOrderIdAndOrderType(id, 1);
				if (Objects.nonNull(transportWaybillEx)){
					TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditByExId(transportWaybillEx.getId());
					if (Objects.nonNull(techRefundFinanceAudit)){
						BigDecimal refundAmount = techRefundFinanceAudit.getRefundAmount();
						if(refundAmount != null){
							completeRefundAmount = refundAmount.longValue();
							tecFeeRefundTime = techRefundFinanceAudit.getAuditTime();
						}
					}
				}
				//完单退还技术服务费(客服新增入口)
				TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditByOrderId(detail.getId());
				if (Objects.nonNull(techRefundFinanceAudit)){
					BigDecimal refundAmount = techRefundFinanceAudit.getRefundAmount();
					if(refundAmount != null){
						completeRefundAmount = refundAmount.longValue();
						tecFeeRefundTime = techRefundFinanceAudit.getAuditTime();
					}
				}
			}
			refundCarServiceAmount = dispatchRefundAmount + exRefundAmount/100 + completeRefundAmount + offlineRefundAmount;
			if(refundCarServiceAmount > 0L){
				//技术服务费退款汇总信息
				String refundTecServiceFeeInfo = refundCarServiceAmount + "元（业务退回" + dispatchRefundAmount + "元）（异常上报退回" + exRefundAmount/100 + "元）（完单后退回" + completeRefundAmount + "元）（线下退款"+ offlineRefundAmount + "元）";

				ordersVo.setRefundTecServiceFeeInfo(refundTecServiceFeeInfo);
				ordersVo.setRefundTecServiceFeeTime(tecFeeRefundTime);
			}

			msg.setCode(200);
			msg.setMsg("查询成功");
			msg.setData(ordersVo);
		} catch (Exception e) {
			e.printStackTrace();
			msg.setCode(500);
			msg.setMsg("异常错误信息");
		}
		return msg;
	}

}
