package com.tyt.web.back.internal;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUpgradeTask;
import com.tyt.service.cache.CacheService;
import com.tyt.service.user.TytUpgradeTaskService;
import com.tyt.util.Constant;
import com.tyt.web.base.BaseController;

@Controller
@RequestMapping("/upgrade/task")
public class TytUpgradeTaskController extends BaseController{
	
	@Resource(name="tytUpgradeTaskService")
	TytUpgradeTaskService tytUpgradeTaskService;
	
	//MemCached工具类
    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;
	/**
	 * 定向升级列表
	 * @param tytUpgradeTask
	 * @param pageNo
	 * @param pageSize
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/getlist")
	public String getlist(TytUpgradeTask tytUpgradeTask, Integer pageNo, Integer pageSize, HttpServletRequest request,HttpServletResponse response){
		try {
			if (!checkLogin(request)) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			if (pageNo == null || pageNo.intValue() <= 0)
				pageNo = 1;
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(pageNo);
			if (pageSize == null || pageSize.intValue() <= 0)
				pageSize = Constant.DEFAULT_PAGE_SIZE;
			pageBean.setPageSize(pageSize);
			List<TytUpgradeTask> upgradeTask = tytUpgradeTaskService.getlist(tytUpgradeTask, pageBean);
			if (upgradeTask!=null) {
				for (TytUpgradeTask task : upgradeTask) {
					if (new Date().getTime() > task.getUpgradeEndtime().getTime()) {
						task.setStatus(3);//已结束
					}else{
						if (task.getStatus()==1) {
							if (new Date().getTime() < task.getUpgradeBegintime().getTime()) {
								task.setStatus(4);//未开始
							}else if(new Date().getTime() >= task.getUpgradeBegintime().getTime() 
									&& new Date().getTime() <= task.getUpgradeEndtime().getTime()){
								task.setStatus(5);//进行中
							}
						}
					}
				}
			}
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("pageNo", pageNo);
			request.setAttribute("pageSize", pageSize);
			request.setAttribute("task", tytUpgradeTask);
			request.setAttribute("upgradeTask", upgradeTask);
			request.setAttribute("maxPage", pageBean.getMaxPage());
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/upgrade_task_list";
	}
	/**
	 * 任务暂停转换按钮
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/pause")
	@ResponseBody
	public ResultMsgBean pause(
			@RequestParam(value="id", required = true) Long id,
			HttpServletRequest request,
			HttpServletResponse response){
		ResultMsgBean result = new ResultMsgBean();
		try {
			EmployeeQueryBean operaUser = getCurrentUser(request);
			if (operaUser == null) {
				result.setCode(100);
				result.setMsg("请重新登陆!");
				response.sendRedirect(request.getContextPath()+"/back/jsp/admin_login.jsp");
				return result;
			}
			TytUpgradeTask task = tytUpgradeTaskService.findTaskById(id);
			Date date=new Date();
			if (task.getUpgradeEndtime().getTime()<=date.getTime()) {
				result.setCode(201);
				result.setMsg("该任务已结束，不能修改");
				return result;
			}
			tytUpgradeTaskService.updateStatus(id,operaUser,task);
			result.setCode(200);
			result.setMsg("修改成功");
			String  obj= cacheService.getString(Constant.SPECIFIED_USER_UPGRADE_ISUPDATE_FLAG);
			if(StringUtils.isEmpty(obj) || "1".equals(obj)){
				cacheService.setString(Constant.SPECIFIED_USER_UPGRADE_ISUPDATE_FLAG, "2");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
	}
	/**
	 * 查看任务详情
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/getdetail")
	@ResponseBody
	public ResultMsgBean getdetail(
			@RequestParam(value="id", required = true) Long id,
			HttpServletRequest request,
			HttpServletResponse response){
		ResultMsgBean result=new ResultMsgBean();
		try {
			TytUpgradeTask task = tytUpgradeTaskService.findTaskById(id);
			result.setCode(200);
			result.setMsg("查询成功");
			result.setData(task);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
	}
	/**
	 * 定向升级--保存按钮
	 * @param tytUpgradeTask
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/save")
	@ResponseBody
	public ResultMsgBean saveTask(
			@RequestBody TytUpgradeTask tytUpgradeTask,
			HttpServletRequest request,
			HttpServletResponse response){
		ResultMsgBean result=new ResultMsgBean();
		try {
			EmployeeQueryBean operaUser = getCurrentUser(request);
			if (operaUser == null) {
				result.setCode(100);
				result.setMsg("请重新登陆!");
				//response.sendRedirect(request.getContextPath()+"/back/jsp/admin_login.jsp");
				return result;
			}
			if (tytUpgradeTask.getUpgradeEndtime()!=null) {
				Date date=new Date();
				if (tytUpgradeTask.getUpgradeEndtime().getTime()<=date.getTime()) {
					result.setCode(202);
					result.setMsg("定向升级任务结束时间不能小于当前时间,保存失败");
					return result;
				}
			}
			tytUpgradeTask.setCreateEmp(operaUser.getUserName());
			tytUpgradeTask.setUpdateTime(new Date());
			if (tytUpgradeTask.getId()==null) {
				tytUpgradeTask.setStatus(1);
				tytUpgradeTask.setCtime(new Date());
				tytUpgradeTaskService.save(tytUpgradeTask);
			}else{
				Long endtime = tytUpgradeTaskService.getEndtimeById(tytUpgradeTask.getId());
				Date date=new Date();
				if (endtime<=date.getTime()) {
					result.setCode(201);
					result.setMsg("该任务已结束，不能修改");
					return result;
				}
				tytUpgradeTaskService.updateTask(tytUpgradeTask);
			}
			result.setCode(200);
			result.setMsg("保存成功");
			String  obj= cacheService.getString(Constant.SPECIFIED_USER_UPGRADE_ISUPDATE_FLAG);
			if(StringUtils.isEmpty(obj) || "1".equals(obj)){
				cacheService.setString(Constant.SPECIFIED_USER_UPGRADE_ISUPDATE_FLAG, "2");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
	}
	/**
	 * 判断时间是否重叠
	 * @param upgradeBegintime //任务开始时间
	 * @param upgradeEndtime //任务结束时间
	 * @param clientType //客户端类型
	 * @param id //任务id
	 * @return
	 */
	@RequestMapping("/ajax_isAvilTime")
	@ResponseBody
	public ResultMsgBean isAvilTime(Timestamp upgradeBegintime,Timestamp upgradeEndtime,
			@RequestParam(value="clientType", required = true) Integer clientType, Long id,String appType){
		ResultMsgBean result=new ResultMsgBean(200,"成功");
		try {
			if (upgradeEndtime!=null) {
				Date date=new Date();
				if (upgradeEndtime.getTime()<=date.getTime()) {
					result.setCode(201);
					result.setMsg("定向升级任务结束时间不能小于当前时间");
					return result;
				}
			}
			List<TytUpgradeTask> list = tytUpgradeTaskService.getTaskByType(clientType, id,appType);
			if (list!=null && list.size()>0) {
				if (upgradeBegintime!=null && upgradeEndtime==null) {
					Long begintime = upgradeBegintime.getTime();
					for (TytUpgradeTask task : list) {
						if (task.getUpgradeBegintime().getTime()<=begintime && task.getUpgradeEndtime().getTime()>begintime){
							result.setCode(202);
							result.setMsg("开始时间重叠");
							break;
						}
					}
				}
				if (upgradeEndtime!=null && upgradeBegintime==null) {
					Long endtime = upgradeEndtime.getTime();
					for (TytUpgradeTask task : list) {
						if (task.getUpgradeBegintime().getTime()<endtime && task.getUpgradeEndtime().getTime()>=endtime){
							result.setCode(203);
							result.setMsg("结束时间重叠");
							break;
						}
					}
				}
				if (upgradeEndtime!=null && upgradeBegintime!=null) {
					Long begintime = upgradeBegintime.getTime();
					Long endtime = upgradeEndtime.getTime();
					for (TytUpgradeTask task : list) {
						if ((task.getUpgradeBegintime().getTime()<endtime && task.getUpgradeEndtime().getTime()>=endtime) 
								||(task.getUpgradeBegintime().getTime()<=begintime && task.getUpgradeEndtime().getTime()>begintime)
								||(task.getUpgradeBegintime().getTime()>=begintime && task.getUpgradeEndtime().getTime()<=endtime)) {
							result.setCode(204);
							result.setMsg("时间重叠");
							break;
						}
					}
				}
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
			return result;
		}
	}
}
