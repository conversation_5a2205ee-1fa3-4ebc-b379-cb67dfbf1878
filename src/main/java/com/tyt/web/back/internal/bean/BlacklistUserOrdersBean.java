package com.tyt.web.back.internal.bean;

import com.tyt.mybatis.mapper.model.BlacklistUserOrders;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 黑名单用户订单处理类
 * @date 2023/10/13 14:21
 */
@Data
public class BlacklistUserOrdersBean extends BlacklistUserOrders {

    /**
     * 能否解除黑名单处罚 0:否，1：是
     */
    private Integer isLiftBlackList;

    /**
     * 能否解除发货限制处罚 0:否，1：是
     */
    private Integer isLiftGoodsLimit;

    /**
     * 能否解除找货限制处罚 0:否，1：是
     */
    private Integer isLiftCarLimit;

    /**
     * 异常上报处理状态: 0初始化 1处理中 2处理完成 3用户撤销
     */
    private String exStatus;
}
