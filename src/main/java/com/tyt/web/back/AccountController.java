package com.tyt.web.back;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.tpayAccount.TpayAccountFlowService;
import com.tyt.util.AppConfig;
import com.tyt.util.CsvWriter;
import com.tyt.web.back.internal.UserController;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.account.AccountAssetBean;
import com.tyt.web.qbean.account.GuaranteeAccountFlowBean;
import com.tyt.web.qbean.account.GuaranteeAccountFlowStatisticBean;
import com.tyt.web.qbean.account.GuaranteeAccountReqBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AccountCommonController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/10 17:37
 * @Version 1.0
 */
@Controller
@RequestMapping("/account/account")
public class AccountController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss"
        ;
    /**
     * 账务系统url
     */

    private final String tpayAccountUrl = AppConfig.getProperty("tyt.account.api.url");
//    private final String tpayAccountUrl = "http://************:8091/account/";

    @Autowired
    private TpayAccountFlowService tpayAccountFlowService;
    /**
     * 获取担保流水
     * @return
     */
    @RequestMapping(value = "queryGuaranteeAccountFlow")
    @ResponseBody
    public ResultMsgBean queryGuaranteeAccountFlow(GuaranteeAccountReqBean reqBean) {
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("businessNo", reqBean.getBusinessNo());
            paramMap.put("startTime", getFormat(reqBean.getStartTime()));
            paramMap.put("endTime", getEndFormat(reqBean.getEndTime()));
            paramMap.put("businessOrderNo", reqBean.getBusinessOrderNo());
            paramMap.put("flowType",reqBean.getFlowType());
            paramMap.put("flowNumber",reqBean.getFlowNumber());
            paramMap.put("currentPage", reqBean.getCurrentPage());
            paramMap.put("pageSize", reqBean.getPageSize());
            paramMap.put("userPhone",reqBean.getUserPhone());
            if(reqBean.getBusinessType() != null && !reqBean.getBusinessType().equals(0)){
                paramMap.put("businessType",reqBean.getBusinessType());
            }
            String result = HttpUtil.get(tpayAccountUrl + "/account/platformTransactionFlow/queryGuaranteeAccountFlow", paramMap);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("pageInfo",resultMsgBean.getData());
            String statisticsResult = HttpUtil.get(tpayAccountUrl + "/account/platformTransactionFlow/queryGuaranteeAccountFlowStatistics", paramMap);
            ResultMsgBean statisticsResultMsgBean = JSON.parseObject(statisticsResult, ResultMsgBean.class);
            GuaranteeAccountFlowStatisticBean statisticInfo = new GuaranteeAccountFlowStatisticBean();
            if(statisticsResultMsgBean.getData() != null){
                statisticInfo = JSON.parseObject(statisticsResultMsgBean.getData().toString(), GuaranteeAccountFlowStatisticBean.class);
            }
            resultMap.put("statisticsInfo", statisticInfo);
            resultMsgBean.setData(resultMap);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取担保账户流水出错：", e);
            ResultMsgBean resultMsgBean = new ResultMsgBean();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取担保账户流水出错");
            return resultMsgBean;
        }
    }

    /**
     * 获取信息费收支统计
     * @return
     */
    @RequestMapping(value = "queryGuaranteeAccountFlowStatistics")
    @ResponseBody
    public ResultMsgBean queryGuaranteeAccountFlowStatistics(GuaranteeAccountReqBean reqBean) {
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("businessNo", reqBean.getBusinessNo());
            paramMap.put("startTime", getFormat(reqBean.getStartTime()));
            paramMap.put("endTime", getEndFormat(reqBean.getEndTime()));
            paramMap.put("businessOrderNo", reqBean.getBusinessOrderNo());
            paramMap.put("flowType",reqBean.getFlowType());
            paramMap.put("flowNumber",reqBean.getFlowNumber());
            paramMap.put("userPhone",reqBean.getUserPhone());
            if(reqBean.getBusinessType() != null && !reqBean.getBusinessType().equals(0)){
                paramMap.put("businessType",reqBean.getBusinessType());
            }
            String result = HttpUtil.get(tpayAccountUrl + "/account/platformTransactionFlow/queryGuaranteeAccountFlowStatistics", paramMap);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取信息费收支统计出错：", e);
            ResultMsgBean resultMsgBean = new ResultMsgBean();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取信息费收支统计出错");
            return resultMsgBean;
        }
    }

    /**
     * 担保流水导出
     * @return
     */
    @RequestMapping(value = "exportGuaranteeAccountFlow")
    @ResponseBody
    public void exportGuaranteeAccountFlow(HttpServletResponse response, GuaranteeAccountReqBean reqBean) {
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("businessNo", reqBean.getBusinessNo());
            paramMap.put("startTime", getFormat(reqBean.getStartTime()));
            paramMap.put("endTime", getEndFormat(reqBean.getEndTime()));
            paramMap.put("businessOrderNo", reqBean.getBusinessOrderNo());
            paramMap.put("flowType",reqBean.getFlowType());
            paramMap.put("flowNumber",reqBean.getFlowNumber());
            paramMap.put("userPhone",reqBean.getUserPhone());
            if(reqBean.getBusinessType() != null && !reqBean.getBusinessType().equals(0)){
                paramMap.put("businessType",reqBean.getBusinessType());
            }
            String result = HttpUtil.get(tpayAccountUrl + "/account/platformTransactionFlow/queryExportGuaranteeAccountFlowData", paramMap);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            List<GuaranteeAccountFlowBean> data = JSON.parseObject(JSON.toJSONString(resultMsgBean.getData()),
                new TypeReference<List<GuaranteeAccountFlowBean>>() {});
//            ServletOutputStream out = response.getOutputStream();
//            response.setContentType("application/vnd.ms-excel");
//            response.setCharacterEncoding("utf-8");
//            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
//            String fileName = URLEncoder.encode("担保账户流水导出", "UTF-8");
//            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
//            EasyExcel.write(response.getOutputStream(), GuaranteeAccountFlowBean.class).sheet("担保账户流水").doWrite(data);
//            out.flush();
            StringBuffer csvStr = tpayAccountFlowService.exportGuaranteeAccountFlow(data);
            CsvWriter.exportCsv("担保流水导出_", csvStr.toString(), response);
        } catch (Exception e) {
            logger.error("导出担保流水出错：", e);
        }
    }

    /**
     * 获取平台资产
     * @return
     */
    @RequestMapping(value = "queryPlatformAssets")
    @ResponseBody
    public ResultMsgBean queryPlatformAssets(Date startTime, Date endTime, Integer currentPage, Integer pageSize) {
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("startTime", getFormat(startTime));
            paramMap.put("endTime", getEndFormat(endTime));
            paramMap.put("currentPage", currentPage);
            paramMap.put("pageSize", pageSize);
            String result = HttpUtil.get(tpayAccountUrl + "/account/platformAssets/queryPlatformAssets", paramMap);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取平台资产出错：", e);
            ResultMsgBean resultMsgBean = new ResultMsgBean();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取平台资产出错");
            return resultMsgBean;
        }
    }

    private String getFormat(Date date) {
        return DateUtil.format(date,YYYY_MM_DD_HH_MM_SS);
    }
    private String getEndFormat(Date date) {
        if(null != date){
            date = new Date(date.getTime() + 24*60*60*1000L);
        }
        return DateUtil.format(date,YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 平台资产导出
     * @return
     */
    @RequestMapping(value = "exportPlatformAssets")
    @ResponseBody
    public void exportPlatformAssets(HttpServletResponse response, Date startTime, Date endTime) {
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("startTime", getFormat(startTime));
            paramMap.put("endTime", getEndFormat(endTime));
            String result = HttpUtil.get(tpayAccountUrl + "/account/platformAssets/queryExportPlatformAssetsData", paramMap);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            List<AccountAssetBean> data = JSON.parseObject(JSON.toJSONString(resultMsgBean.getData()),
                new TypeReference<List<AccountAssetBean>>() {});
//            ServletOutputStream out = response.getOutputStream();
//            response.setContentType("application/vnd.ms-excel");
//            response.setCharacterEncoding("utf-8");
//            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
//            String fileName = URLEncoder.encode("平台资产统计导出", "UTF-8");
//            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
//            EasyExcel.write(response.getOutputStream(), AccountAssetBean.class).sheet("平台资产统计").doWrite(data);
//            out.flush();
            StringBuffer csvStr = tpayAccountFlowService.exportPlatformAssets(data);
            CsvWriter.exportCsv("资金汇总表导出_", csvStr.toString(), response);
        } catch (Exception e) {
            logger.error("导出资产统计异常：", e);
        }
    }


}
