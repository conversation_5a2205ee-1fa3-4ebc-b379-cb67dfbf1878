package com.tyt.service.complaint;


import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.dao.base.BaseDao;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.message.bean.TytUserMsgBean;
import com.tyt.message.bean.TytUserNotifyBean;
import com.tyt.message.service.MessageService;
import com.tyt.message.service.TytNotifyService;
import com.tyt.message.service.TytUserMsgService;
import com.tyt.message.service.TytUserNotifyService;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.ComplaintRecordMapper;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.push.service.PushService;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.transport.TransportMainService;
import com.tyt.service.user.TytUserSubService;
import com.tyt.service.user.UserService;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import com.tyt.web.back.internal.bean.ComplaintRecordCheckPhoneVO;
import com.tyt.web.back.internal.bean.ComplaintRecordSaveVO;
import com.tyt.web.back.internal.bean.ComplaintRecordTransportInfoVO;
import com.tyt.web.qbean.complaint.*;
import lombok.SneakyThrows;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service("complaintRecordService")
public class ComplaintRecordServiceImpl extends BaseServiceImpl<TytComplaintRecord, Long> implements ComplaintRecordService {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private ComplaintRecordMapper complaintRecordMapper;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    @Resource(name = "tytComplaintRecordDao")
    public void setBaseDao(BaseDao<TytComplaintRecord, Long> tytComplaintRecordDao) {
        super.setBaseDao(tytComplaintRecordDao);
    }
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name="tytNotifyService")
    private TytNotifyService tytNotifyService;
    @Autowired
    private TytUserSubService tytUserSubService;
    @Autowired
    private UserService userService;
    @Resource(name="tytUserNotifyService")
    private TytUserNotifyService tytUserNotifyService;
    @Resource(name="pushService")
    private PushService pushService;
    @Resource(name = "messageService")
    private MessageService messageService;
    @Resource(name="tytUserMsgService")
    TytUserMsgService tytUserMsgService;
    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Autowired
    private TytMqMessageService tytMqMessageService;
    private static Comparator comparator=Collator.getInstance(java.util.Locale.CHINA);


    @Override
    public void addComplaintHandle(Long id, String result, Integer isEffective, Integer isFinal, EmployeeQueryBean curUser,Long quesOne,String quesOneName,Long quesTwo,String quesTwoName,Long quesThree,String quesThreeName,
                                   Long quesFour,String quesFourName,String replenishPictureUrl) {
        //修改投诉记录表相关处理字段
        complaintRecordMapper.updateComplaintRecordById(curUser.getId(),curUser.getName(),isEffective,isFinal,id,quesOne,quesOneName,quesTwo,quesTwoName,quesThree,quesThreeName,quesFour,quesFourName,replenishPictureUrl);
        //新增处理记录
        ComplaintHandleListBean complaintHandle=new ComplaintHandleListBean();
        complaintHandle.setComplaintId(id);
        complaintHandle.setHandlerId(curUser.getId());
        complaintHandle.setHandlerName(curUser.getName());
        complaintHandle.setIsEffective(isEffective);
        complaintHandle.setIsFinal(isFinal);
        complaintHandle.setResult(result);
        complaintRecordMapper.insertComplaintHandle(complaintHandle);
        List<HashMap<String, Object>> handlerByHandleId = complaintRecordMapper.findHandlerByHandleId(complaintHandle.getHandlerId());
        if(CollectionUtils.isEmpty(handlerByHandleId)){
            complaintRecordMapper.insertComplaintHandlePerson(complaintHandle);
        }
        if (isEffective.equals(1)){
            TytComplaintRecord complaintRecord = this.getById(id);
            TransportMain transportMain;
            if (complaintRecord.getMsgId()!=null) {
                transportMain = transportMainService.getById(complaintRecord.getMsgId());
            } else {
                transportMain = new TransportMain();
                transportMain.setStartPoint(complaintRecord.getStartAddress());
                transportMain.setDestPoint(complaintRecord.getEndAddress());
                transportMain.setTaskContent(complaintRecord.getTransportName());
            }

            try {
                Long aLong = savePushMsgNews("投诉信息", "投诉信息", "您收到一条投诉信息", "您发布的" + transportMain.getStartPoint() + "→" + transportMain.getDestPoint() + transportMain.getTaskContent() + "货已被投诉，请您规范使用平台，以免发货受限", complaintRecord.getRespondentId());
                PushNotifyNews("您收到一条投诉信息","您发布的"+transportMain.getStartPoint()+"→"+transportMain.getDestPoint()+transportMain.getTaskContent()+"货已被投诉，请您规范使用平台，以免发货受限",complaintRecord.getRespondentId(),aLong);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /**
     *	发送push消息
     * @param title		标题
     * @param content	内容
     * @param userId	用户ID
     */
    public void PushNotifyNews( String title,String content,Long userId,Long messageId) throws Exception {
        logger.info("push消息==========userId=="+userId+"------content="+content);
        /**
         * 保存数据 TytNotify
         */
        TytNotify notify = new TytNotify();
        //设置说明
        notify.setRemarks("货源投诉");
        //设置标题android用 ios 客户端程序名
        notify.setTitle(title);
        //设置内容
        notify.setContent(content);
        // 设置打开方式为1打开链接 0打开应用
        notify.setOpenType("1");
        TytConfig url = configService.getValue("app_notify_url");
        notify.setLinkUrl(url.getValue()+messageId);
        // 设置有效时长
        notify.setTimeLong(1);
        // 设置推送状态为已推送
        notify.setSendStatus("2");
        // 设置推送范围为指定用户
        notify.setSendRange("1");
        // 设置发送时间
        notify.setSendTime(new Date());
        // 设置创建时间
        notify.setCtime(new Date());
        notify.setMtime(new Date());
        // 设置状态为有效
        notify.setStatus("0");
        //只发送货方
        notify.setOriginPush(0);
        notify.setCarPush(0);
        notify.setGoodsPush(1);
        // 保存推送消息
        tytNotifyService.add(notify);
        logger.info("notify save success notifyId="+notify.getId());
        String[] usersArr = new String[] { userId + "" };
        // 用户关联表记录信息
        List<TytUserSub> subs = tytUserSubService.getByIds(usersArr);
        List<TytUserNotifyBean> users = userService.getTytNotifyUser(usersArr);
        //只发送货站
        tytUserNotifyService.saveUsers(notify.getId(), users,2);
        logger.info("notify-user save success notifyId="+notify.getId()+",userid="+userId);
        // 推送货方
        pushService.savePushNotify(notify, subs,0,0,1);

    }
    /**
     * 发送站内信
     * @param remarks	说明
     * @param summary	摘要
     * @param title		标题
     * @param content	内容
     * @param userId	用户ID
     * @return
     */
    @SneakyThrows
    public Long savePushMsgNews(String remarks, String summary, String title, String content, Long userId) {
        /*
         * 生成并保存消息信息
         */
        TytMessage message=new TytMessage();
        message.setRemarks(remarks);
        message.setTitle(title);
        message.setType("0");
        message.setSummary(summary);
        message.setDetails("0");
        message.setDetailsContent(content);
        message.setStatus("0");
        message.setSendTime(new Date());
        message.setEndTime(TimeUtil.addDay(30));
        message.setCtime(new Date());
        message.setMtime(new Date());
        message.setIconType("1");
        message.setSendRange("1");
        message.setMsgType("2");
        message.setSendMode("0");
        message.setSendStatus("2");
        message.setOriginPush(0);
        message.setCarPush(0);
        message.setGoodsPush(1);
        messageService.add(message);

        /*
         * 保存用户-消息信息
         */
        List<TytUserMsgBean> users=new ArrayList<TytUserMsgBean>();
        User byUserId = userService.getByUserId(userId);
        TytUserMsgBean bean=new TytUserMsgBean();
        bean.setId(userId);
        bean.setCellPhone(byUserId.getCellPhone());
        bean.setTrueName(byUserId.getTrueName());
        users.add(bean);
        tytUserMsgService.saveUsers(message.getId(),users,2);


        String[] usersArr = new String[] { userId + "" };
        List<TytUserSub> userSubs = tytUserSubService.getByIds(usersArr);
        try {
            pushService.savePushNewMessage( message, userSubs,0,0,1);
        }catch (Exception exception){
            logger.error("pushService exception is {}", ExceptionUtils.getStackTrace(exception));
        }
        logger.info("save push message completed!");

        return message.getId();
    }
    @Override
    public Map<String,Object> queryRecordList(Integer pageNo, int pageSize, ComplaintRecordQueryBean queryBean){

        List<Object> listObject = new ArrayList<Object>();

        StringBuffer sbCount = new StringBuffer("SELECT count(*) FROM tyt_complaint_record t" +
            " LEFT JOIN tyt_complaint_user u ON t.respondent_id = u.user_id " +
            " LEFT JOIN tyt_complaint_transport tt ON tt.msg_id = t.msg_id  where 1=1 ");

        StringBuffer sb = new StringBuffer();
        if(queryBean.getId() != null){
            sb.append(" AND t.id = ? ");
            listObject.add(queryBean.getId());
        }
        if (!StringUtils.isEmpty(queryBean.getComplainantsPhone())) {
            sb.append(" AND t.complainants_phone like ? ");
            listObject.add("%".concat(queryBean.getComplainantsPhone()).concat("%"));
        }
        if (!StringUtils.isEmpty(queryBean.getMsgId())) {
            sb.append(" AND t.msg_id like ? ");
            listObject.add("%".concat(queryBean.getMsgId()).concat("%"));
        }
        if (!StringUtils.isEmpty(queryBean.getRespondentPhone())) {
            sb.append(" AND t.respondent_phone like ? ");
            listObject.add("%".concat(queryBean.getRespondentPhone()).concat("%"));
        }
        if (!StringUtils.isEmpty(queryBean.getReason())) {
            sb.append(" AND t.reason like ? ");
            listObject.add("%" + queryBean.getReason() + "%");
        }
        if (!StringUtils.isEmpty(queryBean.getComplaintBeginTime())) {
            sb.append(" AND t.create_time >= ? ");
            listObject.add(queryBean.getComplaintBeginTime());
        }
        if (!StringUtils.isEmpty(queryBean.getComplaintEndTime())) {
            sb.append(" AND t.create_time <= ? ");
            listObject.add(queryBean.getComplaintEndTime().concat(" 23:59:59"));
        }
        if (!StringUtils.isEmpty(queryBean.getHandleBeginTime())) {
            sb.append(" AND t.handle_time >= ? ");
            listObject.add(queryBean.getHandleBeginTime());
        }
        if (!StringUtils.isEmpty(queryBean.getHandleEndTime())) {
            sb.append(" AND t.handle_time <= ? ");
            listObject.add(queryBean.getHandleEndTime().concat(" 23:59:59"));
        }
        if(queryBean.getShipperComplainedRangeStart() != null){
            sb.append(" AND u.frequency >= ? ");
            listObject.add(queryBean.getShipperComplainedRangeStart());
        }
        if(queryBean.getShipperComplainedRangeEnd() != null){
            sb.append(" AND u.frequency <= ? ");
            listObject.add(queryBean.getShipperComplainedRangeEnd());
        }
        if(queryBean.getSourceComplainedRangeStart() != null){
            sb.append(" AND tt.frequency >= ? ");
            listObject.add(queryBean.getSourceComplainedRangeStart());
        }
        if(queryBean.getSourceComplainedRangeEnd() != null){
            sb.append(" AND tt.frequency <= ? ");
            listObject.add(queryBean.getSourceComplainedRangeEnd());
        }
        if(queryBean.getHandlerId() != null){
            sb.append(" AND t.handler_id = ? ");
            listObject.add(queryBean.getHandlerId());
        }
        if(queryBean.getIsEffective() != null){
            sb.append(" AND t.is_effective = ? ");
            listObject.add(queryBean.getIsEffective());
        }
        if(queryBean.getIsFinal() != null){
            sb.append(" AND t.is_final = ? ");
            listObject.add(queryBean.getIsFinal());
        }
        if(queryBean.getComplainantSource() != null){
            sb.append(" AND t.complainant_source = ? ");
            listObject.add(queryBean.getComplainantSource());
        }
        if(queryBean.getQuestionTypeId() != null){
            switch (queryBean.getQuestionGrade()) {
                case 1:
                    sb.append(" AND t.ques_one = ? ");
                    listObject.add(queryBean.getQuestionTypeId());
                    break;
                case 2:
                    sb.append(" AND t.ques_two = ? ");
                    listObject.add(queryBean.getQuestionTypeId());
                    break;
                case 3:
                    sb.append(" AND t.ques_three = ? ");
                    listObject.add(queryBean.getQuestionTypeId());
                    break;
                case 4:
                    sb.append(" AND t.ques_four = ? ");
                    listObject.add(queryBean.getQuestionTypeId());
                    break;
            }
        }
        sbCount.append(sb);
        Map<String, Object> resultMap = new HashMap<>();
        PageBean page = new PageBean(pageNo, pageSize);
        BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), listObject.toArray());
        page.setRowCount(rowCount.intValue());
        resultMap.put("pageBean",page);
        if (rowCount != null && rowCount.longValue() > 0) {
            StringBuffer sbSql = new StringBuffer("SELECT t.id,t.complainant_version AS complainantVersion,t.complainant_id AS complainantId,t.complainants_phone AS complainantsPhone,t.respondent_version AS respondentVersion,t.respondent_id AS respondentId,t.respondent_phone AS respondentPhone,\n" +
                " t.msg_id AS msgId,t.reason,t.picture_url AS pictureUrl,t.is_effective AS isEffective,t.is_final AS isFinal,t.handler_id AS handlerId,t.is_make_workorder isMakeWorkorder\n" +
                " ,t.handler_name AS handlerName,t.create_time AS createTime,t.handle_time AS handleTime,t.complainant_source complainantSource,"
                    + "t.type_desc AS typeDesc,t.ques_one_name quesOneName,t.ques_two_name quesTwoName,t.ques_three_name quesThreeName," +
                    "t.ques_four_name quesFourName, u.frequency AS shipperComplainedCount,tt.frequency AS sourceComplainedCount\n" +
                "FROM tyt_complaint_record t\n" +
                "LEFT JOIN tyt_complaint_user u ON t.respondent_id = u.user_id\n" +
                "LEFT JOIN tyt_complaint_transport tt ON tt.msg_id = t.msg_id WHERE 1=1 ");
            Map<String, Type> map = new HashMap<String, Type>();
            map.put("id", Hibernate.LONG);
            map.put("complainantVersion", Hibernate.STRING);
            map.put("complainantId", Hibernate.LONG);
            map.put("complainantsPhone", Hibernate.STRING);
            map.put("respondentVersion", Hibernate.STRING);
            map.put("respondentId", Hibernate.LONG);
            map.put("respondentPhone", Hibernate.STRING);
            map.put("msgId", Hibernate.LONG);
            map.put("reason", Hibernate.STRING);
            map.put("pictureUrl", Hibernate.STRING);
            map.put("isEffective", Hibernate.INTEGER);
            map.put("isFinal", Hibernate.INTEGER);
            map.put("handlerId", Hibernate.LONG);
            map.put("handlerName", Hibernate.STRING);
            map.put("createTime", Hibernate.TIMESTAMP);
            map.put("handleTime", Hibernate.TIMESTAMP);
            map.put("shipperComplainedCount", Hibernate.INTEGER);
            map.put("sourceComplainedCount", Hibernate.INTEGER);
            map.put("isMakeWorkorder", Hibernate.INTEGER);
            map.put("typeDesc", Hibernate.STRING);
            map.put("complainantSource", Hibernate.INTEGER);
            map.put("quesOneName", Hibernate.STRING);
            map.put("quesTwoName", Hibernate.STRING);
            map.put("quesThreeName", Hibernate.STRING);
            map.put("quesFourName", Hibernate.STRING);
            sbSql.append(sb);
            sbSql.append(" order by t.id desc ");
            List<ComplaintRecordBean> list = this.getBaseDao().search(sbSql.toString(), map, ComplaintRecordBean.class, listObject.toArray(), pageNo, pageSize);
            if (!CollectionUtils.isEmpty(list)) {
                list.stream().forEach(record -> {
                    if (record.getQuesOneName()!=null) {
                        String questionType = record.getQuesOneName();
                        questionType = questionType+(record.getQuesTwoName()!=null?"-"+record.getQuesTwoName():"");
                        questionType = questionType+(record.getQuesThreeName()!=null?"-"+record.getQuesThreeName():"");
                        questionType = questionType+(record.getQuesFourName()!=null?"-"+record.getQuesFourName():"");
                        record.setQuestionType(questionType);
                    }
                });
            }
            resultMap.put("list",list);
        }else {
            resultMap.put("list",new ArrayList<>());
        }
        return resultMap;
    }

    @Override
    public ComplaintRecordDetailsBean findDetails(Long id) {
        ComplaintRecordDetailsBean details = complaintRecordMapper.findById(id);
        List<UserPermission> complainantsMember = userPermissionService.getAllPermissionByUserId(details.getComplainantId());
        Map<String, UserPermission> complainantsMap = complainantsMember.stream().collect(Collectors.toMap(a -> a.getServicePermissionTypeId(), a -> a, (b, c) -> b));
        UserPermission complainantsPermissionCar = complainantsMap.get("100101");
        UserPermission complainantsPermissionGoods = complainantsMap.get("100201");
        String complainantMemberInfo = null;
        if(complainantsPermissionCar != null){
            if(Objects.equals(complainantsPermissionCar.getStatus(),1)){
                String exTime = TimeUtil.formatDate(complainantsPermissionCar.getEndTime());
                complainantMemberInfo = complainantsPermissionCar.getServicePermissionTypeName().concat("(").concat(exTime).concat("到期)");
            }
        }
        if(complainantsPermissionGoods != null){
            if(Objects.equals(complainantsPermissionGoods.getStatus(),1)){
                String exTime = TimeUtil.formatDate(complainantsPermissionGoods.getEndTime());
                if(null != complainantMemberInfo){
                    complainantMemberInfo = complainantMemberInfo + "," + complainantsPermissionGoods.getServicePermissionTypeName().concat("(").concat(exTime).concat("到期)");
                }else {
                    complainantMemberInfo = complainantsPermissionGoods.getServicePermissionTypeName().concat("(").concat(exTime).concat("到期)");
                }
            }
        }

        List<UserPermission> respondentMember = userPermissionService.getAllPermissionByUserId(details.getRespondentId());
        Map<String, UserPermission> respondentMap = respondentMember.stream().collect(Collectors.toMap(a -> a.getServicePermissionTypeId(), a -> a, (b, c) -> b));
        UserPermission respondentPermissionCar = respondentMap.get("100101");
        UserPermission respondentPermissionGoods = respondentMap.get("100201");
        String respondentMemberInfo = null;
        if(respondentPermissionCar != null){
            if(Objects.equals(respondentPermissionCar.getStatus(),1)){
                String exTime = TimeUtil.formatDate(respondentPermissionCar.getEndTime());
                respondentMemberInfo = respondentPermissionCar.getServicePermissionTypeName().concat("(").concat(exTime).concat("到期)");
            }
        }
        if(respondentPermissionGoods != null){
            if(Objects.equals(respondentPermissionGoods.getStatus(),1)){
                String exTime = TimeUtil.formatDate(respondentPermissionGoods.getEndTime());
                if(null != respondentMemberInfo){
                    respondentMemberInfo = respondentMemberInfo + "," + respondentPermissionGoods.getServicePermissionTypeName().concat("(").concat(exTime).concat("到期)");
                }else {
                    respondentMemberInfo = respondentPermissionGoods.getServicePermissionTypeName().concat("(").concat(exTime).concat("到期)");
                }
            }
        }
        //V6380 如果是进线投诉来源 无货源ID时 货源信息为新建投诉时填货源信息
        if (details.getMsgId()==null) {
            String goodsInfo = details.getStartAddress() + "-" + details.getEndAddress() + " " + details.getTransportName();
            details.setSourceInfo(goodsInfo);
        } else {
            TransportMain byId = transportMainService.getById(details.getMsgId());
            if(byId != null){
                String goodsInfo = byId.getStartPoint() + "-" + byId.getDestPoint() + " " + byId.getTaskContent();
                details.setSourceInfo(goodsInfo);
            }
        }

        details.setComplainantsMemberInfo(complainantMemberInfo);

        List<ComplaintHandleListBean> complaintHandleListBeans = complaintRecordMapper.queryHandleListByComplaintId(id);

        if(!CollectionUtils.isEmpty(complaintHandleListBeans)){
            details.setIsEffective(complaintHandleListBeans.get(0).getIsEffective());
            details.setIsFinal(complaintHandleListBeans.get(0).getIsFinal());
            details.setHandleResult(complaintHandleListBeans.get(0).getResult());
        }
        details.setRespondentMemberInfo(respondentMemberInfo);
        return details;
    }

    @Override
    public List<SourceComplaintListBean> queryComplainedRecordListBySourceId(Long msgId) {
        return complaintRecordMapper.queryRecordByMsgId(msgId);
    }

    @Override
    public List<ComplaintHandleListBean> queryHandleListById(Long id) {
        return complaintRecordMapper.queryHandleListByComplaintId(id);
    }

    @Override
    public List<HashMap<String, Object>> queryHandlerList() {
        List<HashMap<String, Object>> list = complaintRecordMapper.queryHandlerList();
        Collections.sort(list, new Comparator<HashMap<String, Object>>() {
            @Override
            public int compare(HashMap<String, Object> o1, HashMap<String, Object> o2) {
                //获取中文环境
                Comparator<Object> com = Collator.getInstance(java.util.Locale.CHINA);
                return com.compare(o1.get("handlerName"),o2.get("handlerName"));
            }
        });
        return list;
    }

    @Override
    public ResultMsgBean createWordOrder(Long id, Long opUserId, String opUser, EmployeeQueryBean curUser,ResultMsgBean rm) {
        TytComplaintRecord complaintRecord = this.getById(id);
        if (complaintRecord == null ){
            rm.setData(500);
            rm.setMsg("该投诉不存在");
            return rm;
        }
        if (complaintRecord.getIsMakeWorkorder() == 2){
            rm.setData(500);
            rm.setMsg("该投诉已生成工单");
            return rm;
        }
        //发送建工单mq
        MqCreateWorkorderBean bean = new MqCreateWorkorderBean();
        bean.setComplainantId(complaintRecord.getComplainantId());
        bean.setComplainantsPhone(complaintRecord.getComplainantsPhone());
        bean.setComplainantsCallPhone(complaintRecord.getComplainantsPhone());
        bean.setRespondentId(complaintRecord.getRespondentId());
        bean.setRespondentPhone(complaintRecord.getRespondentPhone());
        bean.setMsgId(complaintRecord.getMsgId());
        bean.setComplaintSource(3);
        bean.setHisId(id);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(complaintRecord.getReason())){
            bean.setQuesContent(complaintRecord.getReason());
        }
        if (complaintRecord.getQuesOne()!=null){
            bean.setQuesOne(complaintRecord.getQuesOne());
            bean.setQuesOneName(complaintRecord.getQuesOneName());
        }
        if (complaintRecord.getQuesTwo()!=null){
            bean.setQuesTwo(complaintRecord.getQuesTwo());
            bean.setQuesTwoName(complaintRecord.getQuesTwoName());
        }
        if (complaintRecord.getQuesThree()!=null){
            bean.setQuesThree(complaintRecord.getQuesThree());
            bean.setQuesThreeName(complaintRecord.getQuesThreeName());
        }
        if (complaintRecord.getQuesFour()!=null){
            bean.setQuesFour(complaintRecord.getQuesFour());
            bean.setQuesFourName(complaintRecord.getQuesFourName());
        }
        if (null != opUserId){
            bean.setOpUserId(opUserId);
            bean.setOpUser(opUser);
        }
        bean.setCreateUser(curUser.getRealName());
        bean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        bean.setMessageType(MqBaseMessageBean.CREATE_WORK_ORDER);
        // 保存发送mq
        final String messageSerailNum = bean.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(bean);
        final int messageType = bean.getMessageType();
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                //发送并mq信息并保存到数据库
                tytMqMessageService.addMqMessage(messageSerailNum, mqJson, messageType);
                tytMqMessageService.sendMqMessage(messageSerailNum, mqJson);
            }
        });
        // 关闭线程
        executorService.shutdown();
        //修改生成投诉状态
        String sql = "UPDATE tyt_complaint_record SET is_make_workorder = 2 WHERE id=? ";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{id});
        return rm;
    }

    @Override
    @Transactional(value = "mybatisTransactionManager",rollbackFor = Exception.class)
    public void saveRecord(ComplaintRecordSaveVO recordSaveVO) {
        //新增投诉记录
        complaintRecordMapper.insertComplaintRecord(recordSaveVO);
        //投诉次数累计
        addRecordLog(recordSaveVO);
    }

    /**
     * 投诉次数累计
     *
     * @param recordSaveVO
     */
    private void addRecordLog(ComplaintRecordSaveVO recordSaveVO) {
        if (recordSaveVO.getRespondentId()!=null) {
            ComplaintRecordUserBean userBean = complaintRecordMapper.queryUserFrequencyByUserId(recordSaveVO.getRespondentId());
            //之前有过被投诉记录
            if (userBean!=null) {
                complaintRecordMapper.updateComplaintUserByUserId(recordSaveVO.getRespondentId());
            } else {
                complaintRecordMapper.insertComplaintUser(recordSaveVO.getRespondentId());
            }
        }
        if (recordSaveVO.getMsgId()!=null) {
            ComplaintRecordTransportBean transportBean = complaintRecordMapper.queryTransportFrequencyByUserId(recordSaveVO.getMsgId());
            //之前有过被投诉记录
            if (transportBean!=null) {
                complaintRecordMapper.updateComplaintTransportByMsgId(recordSaveVO.getMsgId());
            } else {
                complaintRecordMapper.insertComplaintTransport(recordSaveVO.getMsgId());
            }
        }
    }

    @Override
    public ComplaintRecordCheckPhoneVO checkPhone(String phone) throws Exception{
        User user = userService.getUserByCellphone(phone);
        if (user!=null) {
            ComplaintRecordCheckPhoneVO vo = new ComplaintRecordCheckPhoneVO();
            vo.setUserId(user.getId());
            List<UserPermission> complainantsMember = userPermissionService.getAllPermissionByUserId(user.getId());
            Map<String, UserPermission> complainantsMap = complainantsMember.stream().collect(Collectors.toMap(UserPermission::getServicePermissionTypeId, a -> a, (b, c) -> b));
            UserPermission complainantsPermissionGoods = complainantsMap.get("100201");
            String complainantMemberInfo = null;
            if (complainantsPermissionGoods != null && Objects.equals(complainantsPermissionGoods.getStatus(), 1)) {
                String exTime = TimeUtil.formatDate(complainantsPermissionGoods.getEndTime());
                complainantMemberInfo = complainantsPermissionGoods.getServicePermissionTypeName().concat("(").concat(exTime).concat("到期)");
            }
            vo.setPermissionEndTime(complainantMemberInfo);
            ComplaintRecordUserBean userBean = complaintRecordMapper.queryUserFrequencyByUserId(user.getId());
            vo.setGoodsComplaintCount(userBean == null || userBean.getFrequency() == null ? 0 : userBean.getFrequency());
            return vo;
        }
        return null;
    }

    @Override
    public ComplaintRecordTransportInfoVO transportInfo(Long msgId) {
        TransportMain transportMain = transportMainService.getById(msgId);
        if(transportMain != null){
            ComplaintRecordTransportInfoVO vo = new ComplaintRecordTransportInfoVO();
            vo.setTransportId(msgId);
            vo.setStartAddress(transportMain.getStartPoint());
            vo.setEndAddress(transportMain.getDestPoint());
            vo.setTransportName(transportMain.getTaskContent());
            vo.setGoodsPhone(transportMain.getTel());
            ComplaintRecordTransportBean transportBean = complaintRecordMapper.queryTransportFrequencyByUserId(msgId);
            vo.setComplaintCount(transportBean==null||transportBean.getFrequency()==null?0:transportBean.getFrequency());
            return vo;
        }
        return null;
    }

    @Override
    public List<CsBusinessUserBind> getOpUserList() {
        List<CsBusinessUserBind> opUserList = complaintRecordMapper.getOpUserList();
        if (!CollectionUtils.isEmpty(opUserList)){
            return opUserList;
        }
        return null;
    }
}
