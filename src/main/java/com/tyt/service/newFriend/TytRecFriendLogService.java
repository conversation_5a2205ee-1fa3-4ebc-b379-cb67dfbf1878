package com.tyt.service.newFriend;

import java.util.List;

import com.tyt.model.PageBean;
import com.tyt.web.back.internal.bean.TytRecFriendLogBean;

public interface TytRecFriendLogService {

	List<TytRecFriendLogBean> getFriendLog(TytRecFriendLogBean friendLogBean,
			PageBean pageBean) throws Exception;

	StringBuffer getStringCsv(List<TytRecFriendLogBean> logList);

	TytRecFriendLogBean getFriendLogById(Long id);
}
