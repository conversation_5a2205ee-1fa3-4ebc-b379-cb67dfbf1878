package com.tyt.service.newFriend;

import java.util.List;

import com.tyt.model.PageBean;
import com.tyt.model.TytRecommendRuleMoney;

public interface TytRecRuleService {

	List<TytRecommendRuleMoney> getRuleList(TytRecommendRuleMoney rule,
			PageBean pageBean);
	
	void saveRule(TytRecommendRuleMoney tytRecommendRuleMoney);
	
	void deleteRule(Long id);

	TytRecommendRuleMoney queryRuleById(Long id);

	void updateRule(TytRecommendRuleMoney recRule);
	
	List<TytRecommendRuleMoney> getRuleTitle();



}
