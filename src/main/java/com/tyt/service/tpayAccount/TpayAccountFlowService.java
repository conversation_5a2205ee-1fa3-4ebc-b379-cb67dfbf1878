package com.tyt.service.tpayAccount;

import com.tyt.web.qbean.account.*;

import java.util.List;

/**
 * @description:
 * @author: lizhao
 * @date: 2021-02-03 18:27:17
 */
public interface TpayAccountFlowService {

    StringBuffer exportTransactionFlowCsv(List<AccountTransactionFlowBean> data);

    StringBuffer exportGuaranteeAccountFlow(List<GuaranteeAccountFlowBean> data);

    StringBuffer exportPlatformAssets(List<AccountAssetBean> data);

    StringBuffer exportUserBalanceFlow(List<UserPlatformFlowBean> data);

    StringBuffer exportCompanyBalanceFlow(List<CompanyPlatformFlowBean> data);
}
