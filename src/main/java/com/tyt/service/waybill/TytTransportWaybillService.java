package com.tyt.service.waybill;

import java.util.List;

import com.tyt.model.PageBean;
import com.tyt.model.TytTransportWaybill;
import com.tyt.service.base.BaseService;
import com.tyt.web.back.internal.bean.TytTransportWaybillBean;

public interface TytTransportWaybillService extends BaseService<TytTransportWaybill, String>{
	/**
	 * 查询货主运单表
	 * @param queryBean
	 * @param pageBean
	 * @return
	 */
	List<TytTransportWaybillBean> getList(TytTransportWaybillBean queryBean,
			PageBean pageBean);

	/**
	 * 获取运单信息详情
	 * @param tsOrderNo
	 * @return
	 */
    TytTransportWaybill getWaybillDetails(String tsOrderNo);
}
