package com.tyt.service.blacklist;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.AcceptOrderLimitRecord;
import com.tyt.web.back.internal.bean.ImportResultBean;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 限制接单记录服务层
 * @date 2023/11/28 17:54
 */
public interface AcceptOrderLimitRecordService {

    /**
     * 查询接单限制时间是否存在交集
     * @param userId
     * @param acceptOrderLimitStartTime
     * @param acceptOrderLimitEndTime
     * @return
     */
    Boolean checkLimitTimeIntersection(Long userId, Date acceptOrderLimitStartTime, Date acceptOrderLimitEndTime);

    /**
     * 查询接单限制处理记录
     * @param userId
     * @return
     */
    List<AcceptOrderLimitRecord> getAcceptOrderLimitRecordList(Long userId);

    /**
     * 根据状态查询接单限制处理记录
     * @param userId
     * @param status
     * @return
     */
    List<AcceptOrderLimitRecord> getAcceptOrderLimitRecordList(Long userId, Integer status);

    /**
     * 查询用户限制接单结束最大时间
     * @param userId
     * @param status
     * @return
     */
    Date getMaxEndTimeByUserIdAndStatus(Long userId, Integer status);

    /**
     * 导入批量限制接单模板，校验数据的方法
     * @param acceptOrderLimitFile
     * @return
     */
    ImportResultBean checkAcceptOrderLimitData(MultipartFile acceptOrderLimitFile) throws Exception;

    /**
     * 保存接单限制数据的方法
     * @param acceptOrderLimitRecordList
     * @param operaUser
     * @return
     * @throws Exception
     */
    int saveAcceptOrderLimitRecordList(List<AcceptOrderLimitRecord> acceptOrderLimitRecordList, EmployeeQueryBean operaUser) throws Exception;

    /**
     * 导入批量解除接单模板，校验数据的方法
     * @param liftAcceptOrderLimitFile
     * @return
     */
    ImportResultBean checkLiftAcceptOrderLimitData(MultipartFile liftAcceptOrderLimitFile) throws Exception;

    /**
     * 保存解除接单限制数据的方法
     * @param acceptOrderLimitRecordList
     * @param operaUser
     * @return
     * @throws Exception
     */
    int updateAcceptOrderLimitRecord(List<AcceptOrderLimitRecord> acceptOrderLimitRecordList, EmployeeQueryBean operaUser) throws Exception;


    /**
     * 接单限制，发送短信、PUSH和站内信的方法
     * @param acceptOrderLimitRecord
     */
    void sendMultiMessage(AcceptOrderLimitRecord acceptOrderLimitRecord);
}
