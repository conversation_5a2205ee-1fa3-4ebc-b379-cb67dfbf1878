package com.tyt.service.mobile;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.dao.mobile.MobileDao;
import com.tyt.model.Mobile;
import com.tyt.service.base.BaseServiceImpl;
@Service("mobileService")
public class MobileServiceImpl extends BaseServiceImpl<Mobile, Long> implements
		MobileService {

	@Resource(name="mobileDao")
    public void setBaseDao(BaseDao<Mobile, Long> mobileDao) {
        super.setBaseDao(mobileDao);
    }
	@Override
	public String getByArea(String area) {
		return ((MobileDao)this.getBaseDao()).getByArea(area);
	}

	
}
