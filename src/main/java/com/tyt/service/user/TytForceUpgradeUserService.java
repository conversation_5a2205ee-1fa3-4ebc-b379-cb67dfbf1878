package com.tyt.service.user;

import java.io.IOException;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.tyt.model.PageBean;
import com.tyt.model.TytForceUpgradeUser;
import com.tyt.service.base.BaseService;
import com.tyt.web.qbean.ForceUpgradeUserBean;

public interface TytForceUpgradeUserService extends BaseService<TytForceUpgradeUser, Long> {
	
	/**
	 * 从数据库获取列表
	 * @param upgradeType 强制升级标识:1强制升级;2不升级
	 * @return
	 * @throws Exception
	 */
	public List<ForceUpgradeUserBean> getList(TytForceUpgradeUser tytForceUpgradeUser, PageBean pageBean);
	/**
	 * 导出模板
	 * @param object
	 * @return
	 */
	public StringBuffer getStringCsv(List<TytForceUpgradeUser> object);
	/**
	 * 保存导入文件并将数据存入数据库中
	 * @param excelFile
	 * @param taskId 
	 * @return 
	 * @throws IOException 
	 * @throws IllegalStateException 
	 * @throws Exception 
	 */
	public String saveExcel(MultipartFile excelFile, Long taskId) throws IllegalStateException, IOException, Exception;
	/**
	 * 清空当前升级用户下的所用用户
	 * @param taskId
	 */
	public void deleteAll(Long taskId);
	
	

}
