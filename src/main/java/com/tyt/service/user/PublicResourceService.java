package com.tyt.service.user;

import java.util.List;

import com.tyt.model.PublicResource;
import com.tyt.service.base.BaseService;
import com.tyt.web.back.internal.bean.TurnPicture;

public interface PublicResourceService extends BaseService<PublicResource, Long> {
	/**
	 * 获取所有公共资源
	 * @return
	 * @throws Exception
	 */
	public List<PublicResource> getAll() throws Exception;
	
	/**
	 * 获取轮播图信息
	 * @return
	 * @throws Exception
	 */
	public List<PublicResource> getTurnConfig() throws Exception;
	
	/**
	 * 根据ID修改值
	 * @param id
	 * @param value
	 * @throws Exception
	 */
	public void update(Long id,String value)throws Exception;
	
	/**
	 * 根据ID修改值
	 * @param id
	 * @param value
	 * @throws Exception
	 */
	public void updateTurnpicture(Long id,String value)throws Exception;
	
	/**
	 * 根据ID删除值
	 * @param id
	 * @param value
	 * @throws Exception
	 */
	public void deleteResource(Long id)throws Exception;
	
	/**
	 * 添加值
	 * @param id
	 * @param value
	 * @throws Exception
	 */
	public void addResource(PublicResource resource)throws Exception;

}
