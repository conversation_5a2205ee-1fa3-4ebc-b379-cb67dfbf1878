package com.tyt.service.user;

import java.util.List;
import com.tyt.model.TytCallPhoneLimit;
import com.tyt.service.base.BaseService;

/**
 * 用户一级身份拨打电话限制服务层
 * 
 * <AUTHOR>
 * @date 2017-3-9下午2:45:18
 * @description
 */
public interface TytCallPhoneLimitService extends BaseService<TytCallPhoneLimit, Long> {

	/**
	 * 根据一级身份类型获取
	 * 
	 * @param userClass
	 * @param identityType
	 * @return
	 */
	TytCallPhoneLimit getByUserIdentity(Integer userClass, Integer identityType);

	/**
	 * 获取所有
	 * 
	 * @return
	 */
	List<TytCallPhoneLimit> findAll();

	/**
	 * 
	 * @param curUserClass
	 * @param curIdentityType
	 * @return
	 */
	boolean isCallPhoneLimitExits(Integer curUserClass, Integer curIdentityType);

	/**
	 * 
	 * @param curUserClass
	 * @param curIdentityType
	 * @param id
	 * @return
	 */
	boolean isCallPhoneLimitExits(Integer curUserClass, Integer curIdentityType, Long id);

}
