package com.tyt.service.user;

import java.io.File;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.util.*;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.tyt.dao.base.BaseDao;
import com.tyt.dao.user.UserIdentityDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.jurisdiction.service.EmployeeService;
import com.tyt.model.OpLog;
import com.tyt.model.PageBean;
import com.tyt.model.TytEmployee;
import com.tyt.model.TytSttLimit;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.TytUserIdentityMain;
import com.tyt.model.TytUserSub;
import com.tyt.model.User;
import com.tyt.model.UserIdentity;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.cache.CacheService;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.oplog.OpLogService;
import com.tyt.service.transport.SttLimitService;
import com.tyt.web.back.internal.bean.UserIdentityQueryBean;

@Service("userIdentityMainService")
public class UserIdentityMainServiceImpl extends BaseServiceImpl<TytUserIdentityMain, Long> implements UserIdentityMainService {
	@Resource(name = "userIdentityDao")
	UserIdentityDao userIdentityDao;

	@Resource(name = "userIdentityService")
	UserIdentityService userIdentityService;
	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "userIdentityInfoService")
	private UserIdentityInfoService userIdentityInfoService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "tytUserSubService")
	private TytUserSubService tytUserSubService;

	@Resource(name = "sttLimitService")
	private SttLimitService sttLimitService;

	@Resource(name = "opLogService")
	private OpLogService opLogService;

	@Resource(name = "EmployeeService")
	private EmployeeService employeeService;

	@Resource(name = "userIdentityMainDao")
	public void setBaseDao(BaseDao<TytUserIdentityMain, Long> userIdentityMainDao) {
		super.setBaseDao(userIdentityMainDao);
	}

	@Override
	public TytUserIdentityMain getTytUserIdentityMainForUserId(Long userId) {
		String hql = "from TytUserIdentityMain where userId=?";
		List<TytUserIdentityMain> list = this.getBaseDao().find(hql, userId);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	public boolean isExitForPhoto(Long userId) {

		String hql = "from TytUserIdentityMain where userId=? and verifyPhotoSign in(1,2)";
		List<TytUserIdentityMain> list = this.getBaseDao().find(hql, userId);
		return list != null && list.size() > 0;
	}

	public boolean isExitForInfo(String identity, String trueName) {

		String hql = "from TytUserIdentityMain where identity=? and realName=? and  verifyInfoSign=?";
		List<TytUserIdentityMain> list = this.getBaseDao().find(hql, identity, trueName, new Integer(1));
		return list != null && list.size() > 0;
	}

	public boolean isExitForInfo(Long userId, String identity, String trueName) {

		String hql = "from TytUserIdentityMain where identity=?  and (verifyInfoSign=? or verifyPhotoSign=?) and userId<>?";
		List<TytUserIdentityMain> list = this.getBaseDao().find(hql, identity, new Integer(1), new Integer(1), userId);
		return list != null && list.size() > 0;
	}

	public boolean saveUserIdentityMain(Long userId, String identity, String trueName, MultipartFile mainPic) throws Exception {

		TytUserIdentityMain tytUserIdentityMain = this.getTytUserIdentityMainForUserId(userId);
		String mainUrl = renamePic(mainPic, "user");
		mainPic.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + mainUrl));
		// 保存或修改tytUserIdentityMain
		if (tytUserIdentityMain != null) {
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setIdentity(identity);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setInfoTime(new Date());
			if (tytUserIdentityMain.getVerifyInfoSign().intValue() != 1) {
				tytUserIdentityMain.setVerifyInfoSign(2);
			}
			tytUserIdentityMain.setVerifyPhotoSign(2);
			if (tytUserIdentityMain.getMainUrl() == null || "".equals(tytUserIdentityMain.getMainUrl().trim())) {
				tytUserIdentityMain.setMainUrl(mainUrl);
			} else {
				tytUserIdentityMain.setBackUrl(mainUrl);
			}
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().update(tytUserIdentityMain);
		} else {
			tytUserIdentityMain = new TytUserIdentityMain();
			tytUserIdentityMain.setIdentity(identity);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setInfoTime(new Date());
			tytUserIdentityMain.setUserId(userId);
			tytUserIdentityMain.setCreateTime(new Date());
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setVerifyInfoSign(2);
			tytUserIdentityMain.setVerifyPhotoSign(2);
			tytUserIdentityMain.setMainUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().insert(tytUserIdentityMain);
		}

		// 修改user
		/* 提取用户信息 */
		User user = userService.getByUserId(userId);
		// 信息已经认证通过，或自动实名，不修改用户认证状态
		if (user.getVerifyFlag().longValue() != 1) {
			user.setVerifyFlag(2);
		}
		user.setVerifyPhotoSign(2);
		user.setMtime(new Timestamp(System.currentTimeMillis()));
		/* 更新用户验证信息到数据库 */
		userService.update(user);

		/* 保存流水 */
		/* 先把之前提交的置为无效 */
		userIdentityService.updateDisabled(userId, 0);
		/* 创建UserIdentity */
		UserIdentity userIdentity;
		userIdentity = createUserIdentity(userId, identity, trueName, mainUrl);
		userIdentityService.add(userIdentity);

		/* 删除用户缓存信息 */
		cacheService.del(Constant.CACHE_USER_KEY + userIdentity.getUserId());
		/* 用户信息放缓存 */
		cacheService.setObject(Constant.CACHE_USER_KEY + user.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));

		return true;
	}

	@Override
	public boolean saveUserIdentityMain(Long userId, MultipartFile mainPic) throws Exception {
		TytUserIdentityMain tytUserIdentityMain = this.getTytUserIdentityMainForUserId(userId);
		String mainUrl = renamePic(mainPic, "user");
		mainPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + mainUrl));
		// 保存或修改tytUserIdentityMain
		if (tytUserIdentityMain != null) {
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setVerifyPhotoSign(2);
			tytUserIdentityMain.setBackUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().update(tytUserIdentityMain);
		} else {
			tytUserIdentityMain = new TytUserIdentityMain();
			tytUserIdentityMain.setUserId(userId);
			tytUserIdentityMain.setCreateTime(new Date());
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setVerifyInfoSign(0);
			tytUserIdentityMain.setVerifyPhotoSign(2);
			tytUserIdentityMain.setMainUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().insert(tytUserIdentityMain);
		}

		// 修改user
		/* 提取用户信息 */
		User user = userService.getByUserId(userId);
		// 信息已经认证通过，或自动实名，不修改用户认证状态
		if (user.getVerifyFlag().longValue() != 1) {
			user.setVerifyFlag(2);
		}
		user.setVerifyPhotoSign(2);
		user.setMtime(new Timestamp(System.currentTimeMillis()));
		/* 更新用户验证信息到数据库 */
		userService.update(user);

		/* 保存流水 */
		/* 先把之前提交的置为无效 */
		userIdentityService.updateDisabled(userId, 0);
		/* 创建UserIdentity */
		UserIdentity identity;
		identity = createUserIdentity(userId, null, null, mainUrl);
		userIdentityService.add(identity);

		/* 删除用户缓存信息 */
		cacheService.del(Constant.CACHE_USER_KEY + identity.getUserId());
		/* 用户信息放缓存 */
		cacheService.setObject(Constant.CACHE_USER_KEY + user.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));

		return true;
	}

	private UserIdentity createUserIdentity(Long userId, String identity, String trueName, String mainUrl) {
		UserIdentity userIdentity = new UserIdentity();
		userIdentity.setIdentity((identity == null || "".equals(identity)) ? null : identity);
		userIdentity.setRealName((trueName == null || "".equals(trueName)) ? null : trueName);
		userIdentity.setUserId(userId);
		userIdentity.setMainUrl(mainUrl);
		userIdentity.setStatus("2");
		userIdentity.setCreateTime(new Date());
		userIdentity.setUpdateTime(new Date());
		userIdentity.setEnabled(1);
		return userIdentity;
	}

	/**
	 * 重命名图片
	 * 
	 * @param pic待上传的图片
	 * @param typeName文件保存分目录名称
	 * @return
	 */
	protected String renamePic(MultipartFile pic, String typeName) {
		// String fileSeparator=System.getProperty("file.separator");//获取系统文件分隔符
		String domainurl = "/data/pictures/" + typeName + "/";// 获取文件路径
		CreateFileUtil.createDir(AppConfig.getProperty("picture.file.path.domain") + domainurl);
		return domainurl + ImageUtil.renameFile(pic.getOriginalFilename());
	}

	@Override
	public List<UserIdentityQueryBean> getIdentityList(UserIdentityQueryBean identity, PageBean page, EmployeeQueryBean curUser) {

		List<Object> listObject = new ArrayList<Object>();

		StringBuffer sbCount = new StringBuffer("SELECT count(*) FROM tyt_user_identity_main t left join tyt_user u " + "on t.user_id=u.id where 1=1 ");

		StringBuffer sb = new StringBuffer();

		boolean isHaveAdminRole = curUser.getRoleIdList().contains(User.USER_SIGN_ADMIN_NEW);
		boolean isSaleSpecAttr = false;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		if (identity.getStartDate() != null && !"".equals(identity.getStartDate())) {
			sb.append(" AND UNIX_TIMESTAMP(LEFT(t.`update_time`, 10)) >= ?");
			try {
				listObject.add(sdf.parse(identity.getStartDate()).getTime() / 1000);
			} catch (ParseException e) {
			}
		}
		if (identity.getEndDate() != null && !"".equals(identity.getEndDate())) {
			sb.append(" AND UNIX_TIMESTAMP(LEFT(t.`update_time`, 10)) <= ?");
			try {
				listObject.add(sdf.parse(identity.getEndDate()).getTime() / 1000);
			} catch (ParseException e) {
			}
		}
		if (StringUtils.hasLength(identity.getIdentity())) {
			sb.append(" AND t.identity=? ");
			listObject.add(identity.getIdentity());
			isSaleSpecAttr = true;
		}

		if (StringUtils.hasLength(identity.getCellPhone())) {
			sb.append(" AND u.cell_phone=? ");
			listObject.add(identity.getCellPhone());
			isSaleSpecAttr = true;
		}

		if (identity.getUserId() != null && identity.getUserId().intValue() > 0) {
			sb.append(" AND t.user_id=? ");
			listObject.add(identity.getUserId());
			isSaleSpecAttr = true;
		}

		if (StringUtils.hasLength(identity.getRealName())) {
			sb.append(" AND t.real_name=?  ");
			listObject.add(identity.getRealName());
			isSaleSpecAttr = true;
		}

		if (StringUtils.hasLength(identity.getSex())) {
			sb.append(" AND u.sex=?  ");
			listObject.add(identity.getSex());
		}

		if (null != identity.getVerifyInfoSign()) {
			sb.append(" AND t.verify_info_sign=? ");
			listObject.add(identity.getVerifyInfoSign());
		}
		if (null != identity.getVerifyPhotoSign()) {
			sb.append(" AND t.verify_photo_sign=? ");
			listObject.add(identity.getVerifyPhotoSign());
		}

		// 认证日期查询
		if (identity.getAuthDateBegin() != null && identity.getAuthDateEnd() != null) {
			sb.append(" AND t.update_time>=? AND t.update_time<=?");
			listObject.add(identity.getAuthDateBegin());
			listObject.add(identity.getAuthDateEnd());
		} else {
			if (identity.getAuthDateBegin() != null) {
				sb.append(" AND t.update_time>=? ");
				listObject.add(identity.getAuthDateBegin());
			} else {
				if (identity.getAuthDateEnd() != null) {
					sb.append(" AND t.update_time<=? ");
					listObject.add(identity.getAuthDateEnd());
				}
			}
		}

		// 注册日期查询
		if (identity.getCtimeBegin() != null && identity.getCtimeEnd() != null) {
			sb.append(" AND u.ctime>=? AND u.ctime<=?");
			listObject.add(identity.getCtimeBegin());
			listObject.add(identity.getCtimeEnd());
		} else {
			if (identity.getCtimeBegin() != null) {
				sb.append(" AND u.ctime>=? ");
				listObject.add(identity.getCtimeBegin());
			} else {
				if (identity.getCtimeEnd() != null) {
					sb.append(" AND u.ctime<=? ");
					listObject.add(identity.getCtimeEnd());
				}
			}
		}
		// 续费日期
		if (identity.getRenewalDateBegin() != null && identity.getRenewalDateEnd() != null) {
			sb.append(" AND u.renewal_date>=? AND u.renewal_date<=?");
			listObject.add(identity.getRenewalDateBegin());
			listObject.add(identity.getRenewalDateEnd());
		} else {
			if (identity.getRenewalDateBegin() != null) {
				sb.append(" AND u.renewal_date>=? ");
				listObject.add(identity.getRenewalDateBegin());
			} else {
				if (identity.getRenewalDateEnd() != null) {
					sb.append(" AND u.renewal_date<=? ");
					listObject.add(identity.getRenewalDateEnd());
				}
			}
		}
		// 付费日期
		if (identity.getPayDateBegin() != null && identity.getPayDateEnd() != null) {
			sb.append(" AND u.pay_date>=? AND u.pay_date<=?");
			listObject.add(identity.getPayDateBegin());
			listObject.add(identity.getPayDateEnd());
		} else {
			if (identity.getPayDateBegin() != null) {
				sb.append(" AND u.pay_date>=? ");
				listObject.add(identity.getPayDateBegin());
			} else {
				if (identity.getPayDateEnd() != null) {
					sb.append(" AND u.pay_date<=? ");
					listObject.add(identity.getPayDateEnd());
				}
			}
		}
		// 用户状态
		if (identity.getUserType() != null && identity.getUserType().intValue() >= 0) {
			sb.append(" AND u.user_type=? ");
			listObject.add(identity.getUserType());
		}

		if (identity.getInfoTime() != null) {
			sb.append(" AND t.info_time>=? ");
			listObject.add(TimeUtil.formatDate(identity.getInfoTime()) + " 00:00:00");
			sb.append(" AND t.info_time<=? ");
			listObject.add(TimeUtil.formatDate(identity.getInfoTime()) + " 23:59:59");
		}
		if (identity.getPhotoTime() != null) {
			sb.append(" AND t.photo_time>=? ");
			listObject.add(TimeUtil.formatDate(identity.getPhotoTime()) + " 00:00:00");
			sb.append(" AND t.photo_time<=? ");
			listObject.add(TimeUtil.formatDate(identity.getPhotoTime()) + " 23:59:59");
		}

		if (identity.getDeliverType() != null && StringUtils.hasLength(identity.getDeliverType()) && !identity.getDeliverType().equals("-1")) {
			sb.append(" AND u.deliver_type=? ");
			listObject.add(identity.getDeliverType());
		} else {

			if ("".equals(identity.getDeliverType())) {
				sb.append(" AND (u.deliver_type=? OR u.deliver_type IS NULL) ");
				listObject.add(identity.getDeliverType());
			}
		}

		// if (isSale && !isSaleSpecAttr) {
		// sb.append(" AND u.sales=? ");
		// listObject.add(curUser.getTrueName());
		// }

		sbCount.append(sb);

		BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), listObject.toArray());
		if (rowCount != null && rowCount.longValue() > 0) {
			StringBuffer sbSql = new StringBuffer("SELECT t.id id, t.user_id userId,t.identity identity,t.real_name realName,t.main_url mainUrl,t.create_time createTime," + " t.update_time  updateTime,t.failure_reason failureReason,t.remark remark," + " u.cell_phone cellPhone,u.sex sex,u.deliver_type deliverType" + " ,t.info_time infoTime,t.verify_info_sign verifyInfoSign,t.photo_time photoTime,t.verify_photo_sign verifyPhotoSign" + " FROM tyt_user_identity_main t left join tyt_user u on t.user_id=u.id where 1=1 ");
			Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();

			map.put("id", Hibernate.BIG_INTEGER);
			map.put("userId", Hibernate.BIG_INTEGER);
			map.put("identity", Hibernate.STRING);
			map.put("realName", Hibernate.STRING);
			map.put("mainUrl", Hibernate.STRING);
			map.put("createTime", Hibernate.TIMESTAMP);
			map.put("updateTime", Hibernate.TIMESTAMP);
			// map.put("status", Hibernate.STRING);
			map.put("failureReason", Hibernate.STRING);
			map.put("remark", Hibernate.STRING);
			map.put("cellPhone", Hibernate.STRING);
			map.put("sex", Hibernate.STRING);
			map.put("deliverType", Hibernate.STRING);

			map.put("infoTime", Hibernate.TIMESTAMP);
			map.put("verifyInfoSign", Hibernate.INTEGER);
			map.put("photoTime", Hibernate.TIMESTAMP);
			map.put("verifyPhotoSign", Hibernate.INTEGER);

			sbSql.append(sb);
			if (identity.getSort() == null || identity.getSort().intValue() == 0) {
				sbSql.append(" order by t.photo_time desc ");
			} else
				sbSql.append(" order by t.info_time desc ");

			page.setRowCount(rowCount.longValue());
			List<UserIdentityQueryBean> list = this.getBaseDao().search(sbSql.toString(), map, UserIdentityQueryBean.class, listObject.toArray(), page.getCurrentPage(), page.getPageSize());
			/*
			 * 获取当前系统用户的信息
			 */
			TytEmployee employee = employeeService.getById(curUser.getId());
			/**
			 * 如果没有通过唯一条件过滤查询并且当前用户没有超级管理员角色并且只能查看自己以及下属的数据则根据销售人员信息过滤用户数据
			 */
			if (!isHaveAdminRole && !isSaleSpecAttr && "1".equals(employee.getDataInstitutionType())) {
				String systemName = curUser.getName();
				String allSubordinateNames = employeeService.getAllSubordinateNameBySystemName("'" + systemName + "'", systemName);
				// 过滤不符合要求的数据
				Iterator<UserIdentityQueryBean> iterator = list.iterator();
				UserIdentityQueryBean userIdentityQueryBean;
				String sales = null;
				while (iterator.hasNext()) {
					userIdentityQueryBean = iterator.next();
					sales = userIdentityQueryBean.getSales();
					if (sales == null || allSubordinateNames.indexOf(sales) == -1) {
						iterator.remove();
					}
				}
			}
			return list;
		}

		return null;
	}

	// @Override
	// public int saveUserIdentityMain(Long userId, String identity,
	// String trueName) throws Exception {
	//
	// TytConfig tytConfig=tytConfigService.getValue("verifyNumber");
	// int verifyNumber=Integer.parseInt(tytConfig.getValue());
	// String
	// obj=(String)cacheService.getObject(Constant.CACHE_VERIFY_NUMBER+userId+"_"+TimeUtil.formatDateTime(new
	// Date()));
	// int vn=obj==null?0:Integer.parseInt(obj);
	// //判断第三方认证发送条数
	// if(vn<verifyNumber){
	// //调用第三方认证
	// IDCardAuthResult iDCardAuthResult=IDCardAuth.authIdentity(trueName,
	// identity);
	// //保存发送第三方认证条数
	// cacheService.setObject(Constant.CACHE_VERIFY_NUMBER+userId+"_"+TimeUtil.formatDateTime(new
	// Date()),(vn+1),Constant.CACHE_EXPIRE_TIME_24H);
	// TytUserIdentityInfo userIdentityInfo=new TytUserIdentityInfo();
	// userIdentityInfo.setCtime(new Date());
	// userIdentityInfo.setIdCard(identity);
	// userIdentityInfo.setResult(iDCardAuthResult.getResult());
	// userIdentityInfo.setTrueName(trueName);
	// userIdentityInfo.setUserId(userId);
	// userIdentityInfo.setUtime(new Date());
	// if(iDCardAuthResult.getCode()==0)
	// userIdentityInfo.setStatus(1);
	// else
	// userIdentityInfo.setStatus(3);
	// //保存流水
	// userIdentityInfoService.add(userIdentityInfo);
	//
	//
	// TytUserIdentityMain
	// tytUserIdentityMain=this.getTytUserIdentityMainForUserId(userId);
	// //保存或修改tytUserIdentityMain
	// if(tytUserIdentityMain!=null){
	// tytUserIdentityMain.setUpdateTime(new Date());
	// tytUserIdentityMain.setIdentity(identity);
	// tytUserIdentityMain.setRealName(trueName);
	// tytUserIdentityMain.setInfoTime(new Date());
	// if(iDCardAuthResult.getCode()==0)
	// tytUserIdentityMain.setVerifyInfoSign(1);
	// else
	// tytUserIdentityMain.setVerifyInfoSign(3);
	// this.getBaseDao().update(tytUserIdentityMain);
	//
	// }else{
	// tytUserIdentityMain=new TytUserIdentityMain();
	// tytUserIdentityMain.setIdentity(identity);
	// tytUserIdentityMain.setRealName(trueName);
	// tytUserIdentityMain.setInfoTime(new Date());
	// tytUserIdentityMain.setUserId(userId);
	// tytUserIdentityMain.setCreateTime(new Date());
	// tytUserIdentityMain.setUpdateTime(new Date());
	// if(iDCardAuthResult.getCode()==0)
	// tytUserIdentityMain.setVerifyInfoSign(1);
	// else
	// tytUserIdentityMain.setVerifyInfoSign(3);
	// this.getBaseDao().insert(tytUserIdentityMain);
	// }
	// if(iDCardAuthResult.getCode()==0){
	// //修改user
	// /*提取用户信息*/
	// User user=userService.getByUserId(userId);
	// //信息已经认证通过，或自动实名，不修改用户认证状态
	// if(user.getVerifyFlag().longValue()!=1){
	// user.setVerifyFlag(1);
	// }
	// user.setTrueName(trueName);
	// user.setIdCard(identity);
	// user.setMtime(new Timestamp(System.currentTimeMillis()));
	// /*更新用户验证信息到数据库*/
	// userService.update(user);
	// /*删除用户缓存信息*/
	// cacheService.del(Constant.CACHE_USER_KEY+userId);
	// /*用户信息放缓存*/
	// cacheService.setObject(Constant.CACHE_USER_KEY+user.getId(), user,
	// AppConfig.getIntProperty("tyt.cache.user.time"));
	// return 0;
	// }else{
	// if(iDCardAuthResult.getCode()==101){
	// return 101;
	// }else if(iDCardAuthResult.getCode()==102){
	// return 102;
	// }else return 300;
	// }
	//
	// }else
	// return 400;
	// }

	@Override
	public boolean updateUserIdentityMain(UserIdentityQueryBean userIdentityQueryBean, Long userId, String ip) throws Exception {
		TytUserIdentityMain tytUserIdentityMain = getTytUserIdentityMainForUserId(userIdentityQueryBean.getUserId().longValue());
		boolean ifDeleteTicket = false;// 当实名认证状态改变的时候，删除用户ticket，使用户重新登录
		if (isChanged(userIdentityQueryBean.getInfoStatus() == null ? null : userIdentityQueryBean.getInfoStatus() + "", tytUserIdentityMain.getVerifyInfoSign() == null ? null : tytUserIdentityMain.getVerifyInfoSign() + "") || isChanged(userIdentityQueryBean.getStatus(), tytUserIdentityMain.getVerifyPhotoSign() == null ? null : tytUserIdentityMain.getVerifyPhotoSign() + "")) {
			ifDeleteTicket = true;
		}
		int infoSign = 0;
		int photoSign = 0;
		// 信息没有选择结论
		if (userIdentityQueryBean.getInfoStatus() == null || userIdentityQueryBean.getInfoStatus().intValue() == 0) {
			infoSign = 0;
		} else {
			if (userIdentityQueryBean.getInfoStatus().intValue() == 1) {
				infoSign = 1;
				// //信息选择失败 照片也失败
			} else if (userIdentityQueryBean.getInfoStatus().intValue() == 3) {
				infoSign = 3;
				photoSign = 3;
			}
		}
		// 照片没有选择结论
		if (userIdentityQueryBean.getStatus() == null || userIdentityQueryBean.getStatus().equals("0")) {
			if (photoSign == 3 && tytUserIdentityMain.getVerifyPhotoSign() == 0) {
				photoSign = 0;
			}
		} else {
			// 照片选择成功 信息也成功
			if (userIdentityQueryBean.getStatus().equals("1")) {
				photoSign = 1;
				infoSign = 1;
			} else if (userIdentityQueryBean.getStatus().equals("3")) {
				photoSign = 3;
			}
		}
		int isPhotoFail = 0;
		int isPhonoSuccess = 0;
		// ********************************
		// 修改实名认证主表tytUserIdentityMain
		tytUserIdentityMain.setUpdateTime(new Date());
		tytUserIdentityMain.setIdentity(userIdentityQueryBean.getIdentity());
		tytUserIdentityMain.setRealName(userIdentityQueryBean.getRealName());
		// 原来是失败不发短信
		if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 3) {
			isPhotoFail = 1;
		}
		// 原来是成功不发短信
		if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 1) {
			isPhonoSuccess = 1;
		}
		// 信息认证状态
		if (infoSign != 0) {
			tytUserIdentityMain.setVerifyInfoSign(infoSign);
		}
		// 照片未认证不改
		if (tytUserIdentityMain.getVerifyPhotoSign().intValue() != 0) {
			// 照片状态
			if (photoSign != 0) {
				tytUserIdentityMain.setVerifyPhotoSign(photoSign);
			}
			if (photoSign == 0 && infoSign == 3) {
				tytUserIdentityMain.setVerifyPhotoSign(3);
			}

			if (photoSign == 3) {
				if (userIdentityQueryBean.getFailureReason() == null) {
					tytUserIdentityMain.setFailureReason("");
				} else
					tytUserIdentityMain.setFailureReason(userIdentityQueryBean.getFailureReason());
			}
			if (photoSign == 1) {
				tytUserIdentityMain.setFailureReason("");
			}
		}
		this.getBaseDao().update(tytUserIdentityMain);

		// ********************************
		// 修改user表
		/* 提取用户信息 */
		User user = userService.getByUserId(userIdentityQueryBean.getUserId().longValue());
		// 信息认证
		user.setVerifyFlag(tytUserIdentityMain.getVerifyInfoSign() == 3 ? 0 : tytUserIdentityMain.getVerifyInfoSign());
		// 审核失败
		if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 3) {
			user.setVerifyPhotoSign(3);
		} else if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 1) {
			// user.setVerifyFlag(1);
			user.setVerifyPhotoSign(1);
		}
		// 有一个是成功就修改姓名
		if (user.getVerifyFlag().intValue() == 1 || user.getVerifyPhotoSign().intValue() == 1) {
			// user.setVerifyFlag(1);
			// 真名 和身份证号
			user.setTrueName(tytUserIdentityMain.getRealName());
			user.setIdCard(tytUserIdentityMain.getIdentity());
			// user.setInfoPublishFlag(User.INFO_PUBLISH_ENABLE);

			// /取得姓名和性别
			String userName = user.getUserName();
			String sex = IdCardUtil.getGender(tytUserIdentityMain.getIdentity());
			if (!StringUtils.hasLength(userName)) {
				userName = tytUserIdentityMain.getRealName().charAt(0) + (sex.equals("0") ? "女士" : "先生");
				user.setUserName(userName);
			}
			user.setSex(sex);
		}
		// 销售审核
		user.setDeliverType(userIdentityQueryBean.getDeliverType());

		user.setMtime(new Timestamp(System.currentTimeMillis()));
		if (ifDeleteTicket)
			user.setTicket("0000");// pc的剔除用户需要改ticket
		userService.update(user);
		/* 删除用户缓存信息 */
		cacheService.del(Constant.CACHE_USER_KEY + tytUserIdentityMain.getUserId());
		/* 用户信息放缓存 */
		cacheService.setObject(Constant.CACHE_USER_KEY + tytUserIdentityMain.getUserId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));

		// ********************************
		// 修改用户子表 改限制类型和发货数量
		TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(tytUserIdentityMain.getUserId());
		if (null != tytUserSub) {
			// 认证成功
			if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 1 || tytUserIdentityMain.getVerifyInfoSign().intValue() == 1) {
				TytSttLimit tytSttLimit = sttLimitService.getUserSttLimit(tytUserIdentityMain.getUserId());
				tytUserSub.setSendTptType(tytSttLimit.getType());
				tytUserSub.setSendTptNumber(0);
			}

			tytUserSubService.update(tytUserSub);
		}
		// 删除 子表缓存
		cacheService.del(Constant.CACHE_USERSUB_KEY + tytUserIdentityMain.getUserId() + "_" + TimeUtil.formatDateMonthTime(new Date()));
		// 删除用户ticket
		/* 由于删除操作以后客户端不会跳转到登陆页面，因此采用改变ticket的方式 */
		// if(ifDeleteTicket)cacheService.del(Constant.CACHE_TICKET_KEY+tytUserIdentityMain.getUserId());
		if (ifDeleteTicket) {
			UserTicketUtil.kickOutAllClient(tytUserIdentityMain.getUserId().toString());
		}
		// ********************************
		/* 修改流水表 */
		UserIdentity identity = userIdentityService.getUserIdentityForUserId(tytUserIdentityMain.getUserId());
		if (identity != null) {
			// 审核失败
			if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 3) {
				identity.setFailureReason(userIdentityQueryBean.getFailureReason());
				identity.setStatus("3");
			} else if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 1) {
				identity.setStatus("1");
				identity.setRealName(tytUserIdentityMain.getRealName());
				identity.setIdentity(tytUserIdentityMain.getIdentity());
			}
			identity.setUpdateTime(new Date());
			userIdentityService.update(identity);
		}
		// ********************************
		// 发送短信通知
		String message = null;
		String[] valueArray = new String[2];
		String[] keyArray = new String[] { "${userName}", "${failureReason}" };
		// 发短信
		valueArray[0] = user.getUserName();
		valueArray[1] = userIdentityQueryBean.getFailureReason();
		// 发短信提示用户
		if (isPhonoSuccess == 0 && tytUserIdentityMain.getVerifyPhotoSign().intValue() == 1) {
			message = FormatString.formatFailMessage("tyt.user.identity.success.message", keyArray, valueArray);
			SendMessage.sendMessage(user.getCellPhone(), message);
		}
		if (isPhotoFail == 0 && tytUserIdentityMain.getVerifyPhotoSign().intValue() == 3) {
			message = FormatString.formatFailMessage("tyt.user.identity.fail.message", keyArray, valueArray);
			SendMessage.sendMessage(user.getCellPhone(), message);
		}

		/* 创建日志 */
		//TODO modify by tianjw on 20180205 所有后台日志均记入tyt_manage_cud_log表中，不往log中记录
//		createOpLog(userId, OpLog.OP_USER_AUTH, user.getCellPhone(), "无", "无", "无", Constant.PLAT_WEB, ip, "userId:" + tytUserIdentityMain.getUserId() + "id:" + tytUserIdentityMain.getId() + ",verifyInfoSign:" + tytUserIdentityMain.getVerifyInfoSign() + ",verifyPhotoSign:" + tytUserIdentityMain.getVerifyPhotoSign());

		return true;
	}

	//TODO modify by tianjw on 20180205 所有后台日志均记入tyt_manage_cud_log表中，不往log中记录
//	protected void createOpLog(Long userId, Integer opType, String cellPhone, String token, String pcSign, String version, Integer platId, String ip, String opContent) {
//		OpLog log = new OpLog();
//		log.setUserId(userId);
//		log.setCellPhone(cellPhone);
//		log.setIp(ip);
//		log.setOpType(opType);
//		log.setPcSign(pcSign);
//		log.setPlatId(platId);
//		log.setTicket("token=" + token);
//		log.setVersion(version);
//		log.setOpContent(opContent);
//		log.setOpTime(new Timestamp(System.currentTimeMillis()));
//		opLogService.add(log);
//	}

	@Override
	public void updateIdentityPic(String id) {
		String sql = "UPDATE tyt_user_identity_main tui SET tui.`main_url`=tui.`back_url`, tui.`back_url`=NULL WHERE id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { id });
	}

	@Override
	public boolean isSecondPicEmpty(String id) {
		String sql = "SELECT COUNT(*) FROM tyt_user_identity_main tuim WHERE tuim.`id`=? AND (tuim.`back_url` IS NULL OR tuim.`back_url`='') ";
		BigInteger rowCount = this.getBaseDao().query(sql, new Object[] { id });
		return rowCount.intValue() > 0;
	}

	private boolean isChanged(String newVal, String oldVal) {
		if (!isNotEmpty(newVal)) {
			return isNotEmpty(oldVal);
		} else {
			if (!isNotEmpty(oldVal)) {
				return true;
			} else {
				String newStr = newVal.trim();
				String oldStr = oldVal.trim();
				return !newStr.equals(oldStr);
			}
		}

	}

	/**
	 * 判断是否为空
	 * 
	 * @param value
	 * @return true不为空；false为空
	 */
	private boolean isNotEmpty(String value) {
		// return StringUtils.hasLength(value);
		if (value == null) {
			return false;
		} else if (value.trim().equals("")) {
			return false;
		} else return !value.equals("null");

	}

	@Override
	public boolean saveUserIdentityMain(Long userId, String trueName, String idCard, String mainUrl) {
		TytUserIdentityMain tytUserIdentityMain = this.getTytUserIdentityMainForUserId(userId);

		// String mainUrl=renamePic(mainPic, "user");
		// mainPic.transferTo(new
		// File(AppConfig.getProperty("picture.path.domain")+mainUrl));
		// 保存或修改tytUserIdentityMain
		if (tytUserIdentityMain != null) {
			tytUserIdentityMain.setIdentity(idCard);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setVerifyPhotoSign(2);
			/* 数据库back_url保存用户>=2次上传的照片，每次覆盖；main_url只保存首次的照片，且不变 */
			if (tytUserIdentityMain.getMainUrl() == null || "".equals(tytUserIdentityMain.getMainUrl().trim())) {
				tytUserIdentityMain.setMainUrl(mainUrl);
			} else {
				tytUserIdentityMain.setBackUrl(mainUrl);
			}
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().update(tytUserIdentityMain);
		} else {
			tytUserIdentityMain = new TytUserIdentityMain();
			tytUserIdentityMain.setIdentity(idCard);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setUserId(userId);
			tytUserIdentityMain.setCreateTime(new Date());
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setVerifyInfoSign(0);
			tytUserIdentityMain.setVerifyPhotoSign(2);
			tytUserIdentityMain.setMainUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().insert(tytUserIdentityMain);
		}

		// 修改user
		/* 提取用户信息 */
		// User user=userService.getByUserId(userId);
		// 信息已经认证通过，或自动实名，不修改用户认证状态
		// if(user.getVerifyFlag().longValue()!=1){
		// user.setVerifyFlag(2);
		// }
		// user.setVerifyPhotoSign(2);
		// user.setMtime(new Timestamp(System.currentTimeMillis()));
		/* 更新用户验证信息到数据库 */
		// userService.update(user);

		/* 保存流水 */
		/* 先把之前提交的置为无效 */
		userIdentityDao.updateDisabled(userId, 0);
		/* 创建UserIdentity */
		UserIdentity identity;
		identity = createUserIdentity(userId, idCard, trueName, mainUrl);
		userIdentityDao.insert(identity);

		/* 删除用户缓存信息 */
		// cacheService.del(Constant.CACHE_USER_KEY+identity.getUserId());
		/* 用户信息放缓存 */
		// cacheService.setObject(Constant.CACHE_USER_KEY+user.getId(), user,
		// AppConfig.getIntProperty("tyt.cache.user.time"));

		return true;
	}

	public void updateOldIdentityData(User user, boolean status, TytUserIdentityAuth tuia, String failMsg) throws Exception {
		TytUserIdentityMain tytUserIdentityMain = this.getTytUserIdentityMainForUserId(user.getId());

		tytUserIdentityMain.setUpdateTime(new Date());
		tytUserIdentityMain.setIdentity(tuia.getIdCard());
		tytUserIdentityMain.setRealName(tuia.getTrueName());

		tytUserIdentityMain.setVerifyPhotoSign(status ? 1 : 3);
		tytUserIdentityMain.setFailureReason("");
		tytUserIdentityMain.setVerifyInfoSign(status ? 1 : 3);
		this.getBaseDao().update(tytUserIdentityMain);

		// ********************************
		// 修改用户子表 改限制类型和发货数量
		TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(tytUserIdentityMain.getUserId());
		if (null != tytUserSub) {
			// 认证成功
			if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 1 || tytUserIdentityMain.getVerifyInfoSign().intValue() == 1) {
				TytSttLimit tytSttLimit = sttLimitService.getUserSttLimit(user.getId());
				tytUserSub.setSendTptType(tytSttLimit.getType());
				tytUserSub.setSendTptNumber(0);
			}

			tytUserSubService.update(tytUserSub);
		}
		// 删除 子表缓存
		cacheService.del(Constant.CACHE_USERSUB_KEY + tytUserIdentityMain.getUserId() + "_" + TimeUtil.formatDateMonthTime(new Date()));
		// ********************************
		/* 修改流水表 */
		UserIdentity identity = userIdentityService.getUserIdentityForUserId(user.getId());
		if (identity != null) {
			// 审核失败
			if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 3) {
				identity.setFailureReason(failMsg);
				identity.setStatus("3");
			} else if (tytUserIdentityMain.getVerifyPhotoSign().intValue() == 1) {
				identity.setStatus("1");
				identity.setRealName(tytUserIdentityMain.getRealName());
				identity.setIdentity(tytUserIdentityMain.getIdentity());
			}
			identity.setUpdateTime(new Date());
			userIdentityService.update(identity);
		}
	}
}
