package com.tyt.service.user;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.service.base.BaseService;
import com.tyt.web.back.internal.bean.UserEnterpriseAuthBeanInfo;
import com.tyt.web.back.internal.bean.UserIdentityAuthBeanInfo;
import com.tyt.web.back.internal.bean.UserIdentityAuthFailureDetail;
import com.tyt.web.qbean.TytUserIdentityAuthQueryBean;

import java.util.Date;
import java.util.List;

public interface TytUserIdentityAuthService extends BaseService<TytUserIdentityAuth, Long> {

	/**
	 * 保存身份认证信息
	 *
	 * @param tytUserIdentityAuth
	 * @return ResultMsgBean
	 */
	public ResultMsgBean save(TytUserIdentityAuth tytUserIdentityAuth);

	/**
	 * 通过userId 获得用户身份信息
	 *
	 * @param userId
	 * @return TytUserIdentityAuth
	 */
	public TytUserIdentityAuth getTytUserIdentityAuth(Long userId);

	/**
	 * 通过身份证号获得用户身份信息
	 *
	 * @param idCard 身份证号
	 * @return TytUserIdentityAuth
	 */
	public List<TytUserIdentityAuth> getByIdCard(String idCard);

	/**
	 * 审核身份信息
	 * @param userIdentityAuthBeanInfo
	 */
	public boolean updateExamine(UserIdentityAuthBeanInfo userIdentityAuthBeanInfo,EmployeeQueryBean employee, boolean sendVerifySms)throws Exception;

	public void updateEnterpriseExamine(UserEnterpriseAuthBeanInfo userEnterpriseAuthBeanInfo,EmployeeQueryBean employeeQueryBean);
	/**
	 * 更新身份审核信息
	 *
	 * @param name
	 * @param status
	 * @param id
	 * @param sort
	 */
	public void updateIdentityAuth(String name, String status, String id, String sort);
	/**
	 * 将原纪录初始化为只保留身份
	 * @param oldObj
	 * @param userClass
	 * @param identityType
	 * @throws Exception
	 */
	public TytUserIdentityAuth updateInitIdentityType(TytUserIdentityAuth  oldObj, Long userId, String mobile,
			Integer userClass, Integer identityType, String trueName,
			Date nowDate) throws Exception;

	/**
	 * 保存身份认证信息
	 * @param userId 用户ID
	 * @param mobile 用户手机号码
	 * @param userClass 用户分类1、发货方2、车辆方 见 source  user_class
	 * @param identityType 用户身份见source表
	 * @return
	 * @throws Exception
	 */
	TytUserIdentityAuth saveIdentityAuth(Long userId,String mobile, Integer userClass,Integer identityType,String trueName,Date nowDate)throws Exception;

	public List<TytUserIdentityAuthQueryBean> getList(
			TytUserIdentityAuthQueryBean queryBean, PageBean pageBean);
	/**
	 * 修改用户的手机号码
	 * @param userId
	 * @param mobile
	 * @return
	 * @throws Exception
	 */
	public int updateIdentityAuthMobile(Long userId,String mobile)throws Exception;

	/**
	 * 根据身份证号获取用户列表
	 * @param idcard
	 * @param long1
	 * @return
	 */
	public List<TytUserIdentityAuth> getListByIdcard(String idcard, Long userId);
	Integer getIdcardCount(String idcard);

	/**
	 * 更新初始化的审核人以及审核时间
	 * @param identityAuth
	 * @param employee
	 */
	public void updateInitExamine(TytUserIdentityAuth identityAuth,
			EmployeeQueryBean employee);

	/**
	 * 根据userId获取身份认证失败详情
	 * @param userId
	 * @return
	 */
	public UserIdentityAuthFailureDetail getFailureDetail(Long userId);

	List getIdsByTrueName(String name);

	/**
	 * 新版本（5600）身份认证审核提交
	 * @param userIdentityAuthBeanInfo
	 * @param employee
	 * @throws Exception
	 */
	public void updateNewExamine(UserIdentityAuthBeanInfo userIdentityAuthBeanInfo, EmployeeQueryBean employee) throws Exception;
	/**
	 * 新版本（5600）身份录入保存
	 * @param tytUserIdentityAuth
	 */
	public ResultMsgBean saveNewVersionIdentity(TytUserIdentityAuth tytUserIdentityAuth);


	TytUserIdentityAuth getByUserId(String userId);

	List<TytUserIdentityAuth> getByUserIds(List<Long> userId);

	void updateIdentityAuthAudit(Long id, Integer auditStatus);
	/**
	 * 车方认证成功赠送找货权益
	 * @param platId
	 * @param userId
	 */
	void saveGiveFindGoodsRight(String platId,Long userId,String cellPhone,EmployeeQueryBean employeeQueryBean);


	void addRegisterAndIdentifyMq(Long userId);
}
