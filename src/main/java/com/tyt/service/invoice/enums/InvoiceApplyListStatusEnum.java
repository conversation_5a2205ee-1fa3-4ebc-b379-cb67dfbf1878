package com.tyt.service.invoice.enums;

import lombok.Getter;

/**
 * 开票申请列表查询状态枚举
 *
 * <AUTHOR>
 * @since 2024-4-7 13:38:02
 */
@Getter
public enum InvoiceApplyListStatusEnum {

    ALL(0, "全部"),
    APPLIED(1, "已申请"),
    INVOICING(2, "开票中"),
    INVOICED(3, "已开票"),
    OTHER(4, "其他"),
    RED_CHONG(6, "红冲");

    private final Integer status;
    private final String name;

    InvoiceApplyListStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return this.status;
    }

    public String getName() {
        return this.name;
    }


}
