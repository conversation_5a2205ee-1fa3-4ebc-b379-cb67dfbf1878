package com.tyt.service.chart.chartImpl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.chart.bean.ChartBean;
import com.tyt.chart.bean.MultiLineDataBean;
import com.tyt.chart.bean.TytGeoDict;
import com.tyt.chart.bean.TytGoodsSearch;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytSource;
import com.tyt.model.User;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.chart.UserChartService;
import com.tyt.util.ChartUtil;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;

@Service("userChartService")
public class UserChartServiceImpl extends BaseServiceImpl<User, Long> implements UserChartService {

	private static final long ONE_DAY_IN_MINUTE = 24 * 60 * 60;
	private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	static StringBuilder deliverStr = new StringBuilder();

	//Bean初始化完成后调用初始化方法
	@PostConstruct
	public void init(){
		List<TytSource> checkSignList = TytSourceUtil.getSourceList("deliver_type");
		for (TytSource tytSource : checkSignList) {
			if (!"待定".equals(tytSource.getName())) {
				deliverStr.append(" when ");
				deliverStr.append(tytSource.getValue());
				deliverStr.append(" then ");
				deliverStr.append("'");
				deliverStr.append(tytSource.getName());
				deliverStr.append("'");
			}
		}
		deliverStr.append(" else '待定' end ");
		System.out.println("UserChartServiceImpl初始化成功！==========================");
	}
//	static {
//		List<TytSource> checkSignList = TytSourceUtil.getSourceList("deliver_type");
//		for (TytSource tytSource : checkSignList) {
//			if (!"待定".equals(tytSource.getName())) {
//				deliverStr.append(" when ");
//				deliverStr.append(tytSource.getValue());
//				deliverStr.append(" then ");
//				deliverStr.append("'");
//				deliverStr.append(tytSource.getName());
//				deliverStr.append("'");
//			}
//		}
//		deliverStr.append(" else '待定' end ");
//
//	}

	@Resource(name = "userDao")
	public void setBaseDao(BaseDao<User, Long> userDao) {
		super.setBaseDao(userDao);
	}

	@SuppressWarnings("deprecation")
	@Override
	public Map<String, String> getUserPayMap(String angle, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {

		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, Object> paramMap = new HashMap<String, Object>();
		String str = " COUNT(1)";
		if (angle != null && !"".equals(angle) && "2".equals(angle)) {
			str = " sum(tu.money)";
		}
		StringBuffer sb = new StringBuffer("SELECT " + str + " AS quality, LEFT(tu.pay_date, 10) AS date FROM tyt_success_account tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE tu.`pay_date`<=:endTime AND tu.`pay_date`>=:startTime");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType ");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append("  AND (u.deliver_type=:deliverType OR u.deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND u.province LIKE :province");
			paramMap.put("province", province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city LIKE :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", city.substring(0, city.length() - 1) + "%");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" and u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		if (startDate != null && endDate != null && !"".equals(startDate) && !"".equals(endDate)) {
			startTime = sdf.parse(startDate).getTime() / 1000;
			endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		} else {
			// 获取当前的秒值
			endTime = System.currentTimeMillis() / 1000;
			/*
			 * 计算30天前的秒值
			 */
			Date date = new Date(System.currentTimeMillis());
			String time = sdf.format(date);
			startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		}

		sb.append(" GROUP BY LEFT(tu.`pay_date`, 10);");
		String conditionSQL = sb.toString();
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
//		if (dateType != null && !"day".equals(dateType)) {
//			startDate = ChartUtil.changeByStartDate(startDate, dateType, sdf);
//		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");
		List<ChartBean> charBeans = this.getBaseDao().searchByName(conditionSQL, map, ChartBean.class, paramMap);
		/*
		 * 填充从开始到结束数据为0的日期
		 */
		// charBeans = ChartUtil.addNotExitsDate(charBeans);
		// charBeans = ChartUtil.addBeforeNotExitsDate(startTime, charBeans);
		// charBeans = ChartUtil.addAfterNotExitsDate(endTime -
		// ONE_DAY_IN_MINUTE, charBeans);
		if (dateType != null) {
			/*
			 * 根据按周查询和按月查询做不同的处理
			 */
			if ("week".equals(dateType)) {
				charBeans = ChartUtil.addNotExitsDate(charBeans, sdf.format(ChartUtil.findMonday(startDate)), endDate);
				return ChartUtil.convertToMapByWeek(charBeans);
			} else if ("month".equals(dateType)) {
				charBeans = ChartUtil.addNotExitsDate(charBeans, sdf.format(ChartUtil.findOneDate(startDate)), endDate);
				return ChartUtil.convertToMapByMonth(charBeans);
			}
		}
		charBeans = ChartUtil.addNotExitsDate(charBeans, startDate, endDate);
		return ChartUtil.convertToMap(charBeans);
	}

	public Map<String, String> getUserPayMapByTopArea(String angle, String deliverType, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }

		StringBuffer sb = new StringBuffer("tu.`pay_date`<=:endTime AND tu.`pay_date`>=:startTime");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND EXISTS(SELECT id FROM tyt_user WHERE deliver_type=:deliverType AND id=tu.user_id)");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND EXISTS(SELECT id FROM tyt_user WHERE id=tu.user_id AND (deliver_type=:deliverType OR deliver_type IS NULL))");
			paramMap.put("deliverType", "");
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}

		String str = " COUNT(1)";
		if (angle != null && !"".equals(angle) && "2".equals(angle)) {
			str = " sum(tu.money)";
		}
		StringBuffer sb1 = new StringBuffer("SELECT " + str + " as quality,u.city as name FROM tyt_success_account tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY u.city ORDER BY " + str + " DESC LIMIT 5;");

		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality, LEFT (tu.pay_date, 10) AS date, u.city AS NAME FROM tyt_success_account tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		if (dateType != null && !"day".equals(dateType)) {
			startDate = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", startDate);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);
		map.put("date", Hibernate.STRING);
		if (top != null && top.size() > 0) {
			sb2.append(" AND (");
			for (MultiLineDataBean bean : top) {
//				sb2.append(" u.city like '%" + bean.getName() + "%' or");
				 sb2.append(" u.city = '" + bean.getName() + "' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(") ");
		}
		sb2.append(" GROUP BY LEFT (tu.`pay_date`, 10), u.city;");
		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	public Map<String, String> getUserPayMapByProvinces(String angle, String deliverType, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String selectedProvinces) throws Exception {

		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`pay_date`<=:endTime AND tu.`pay_date`>=:startTime");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND EXISTS(SELECT id FROM tyt_user WHERE deliver_type=:deliverType AND id=tu.user_id)");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND EXISTS(SELECT id FROM tyt_user WHERE id=tu.user_id AND (deliver_type=:deliverType OR deliver_type IS NULL))");
			paramMap.put("deliverType", "");
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		String str = " COUNT(1)";
		if (angle != null && !"".equals(angle) && "2".equals(angle)) {
			str = " sum(tu.money)";
		}
		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality, LEFT (tu.pay_date, 10) AS date, u.province AS NAME FROM tyt_success_account tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		if (dateType != null && !"day".equals(dateType)) {
			startDate = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", startDate);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedProvinces != null && !"".equals(selectedProvinces)) {
			sb2.append(" AND (");
			String[] arr = selectedProvinces.split(",");
			for (String str1 : arr) {
				sb2.append(" u.province like('%" + str1 + "%') or");
			}

			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`pay_date`, 10), u.province;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;

	}

	/**
	 * 
	 * 获取一段时间内付费的人数:按市级查
	 * 
	 * @param deliverType
	 *            身份
	 * @param cTimeStart
	 *            注册时间-开始
	 * @param cTimeEnd
	 *            注册时间-结束
	 * @param startDate
	 *            缴费日期-开始
	 * @param endDate
	 *            缴费日期-结束
	 * @param dateType
	 *            分析类型：日，周，月
	 * @param selectedCities
	 *            选择的市
	 * @return multiLine多线表数据
	 * @throws Exception
	 */
	public Map<String, String> getUserPayMapByCities(String angle, String deliverType, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String selectedCities) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		if (startDate != null && endDate != null && !"".equals(startDate) && !"".equals(endDate)) {
			startTime = sdf.parse(startDate).getTime() / 1000;
			endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		} else {
			// 获取当前的秒值
			endTime = System.currentTimeMillis() / 1000;
			/*
			 * 计算30天前的秒值
			 */
			Date date = new Date(System.currentTimeMillis());
			String time = sdf.format(date);
			startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		}
		StringBuffer sb = new StringBuffer("tu.`pay_date`<=:endTime AND tu.`pay_date`>=:startTime");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND EXISTS(SELECT id FROM tyt_user WHERE deliver_type=:deliverType AND id=tu.user_id)");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND EXISTS(SELECT id FROM tyt_user WHERE id=tu.user_id AND (deliver_type=:deliverType OR deliver_type IS NULL))");
			paramMap.put("deliverType", "");
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		String str = " COUNT(1)";
		if (angle != null && !"".equals(angle) && "2".equals(angle)) {
			str = " sum(tu.money)";
		}
		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality, LEFT (tu.pay_date, 10) AS date, u.city AS NAME FROM tyt_success_account tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		if (dateType != null && !"day".equals(dateType)) {
			startDate = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", startDate);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedCities != null && !"".equals(selectedCities)) {
			sb2.append(" AND (");
			String[] arr = selectedCities.split(",");
			for (String str1 : arr) {
//				sb2.append(" u.city like('%" + str1.replace("市", "") + "%') or");
				// sb2.append(" u.city ='" + str1.replace("市", "") + "' or");
				sb2.append(" u.city ='" + str1 + "' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`pay_date`, 10), u.city;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内付费的人数:近期同比
	 * 
	 * @param angle
	 *            分析角度
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserPayMapByNearTime(String angle, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`pay_date`<=:endTime AND tu.`pay_date`>=:startTime ");
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND u.province LIKE :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city LIKE :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		String str = " COUNT(1)";
		if (angle != null && !"".equals(angle) && "2".equals(angle)) {
			str = " sum(tu.money)";
		}

		StringBuffer sb1 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.pay_date, 10) AS date FROM tyt_success_account tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY LEFT (tu.pay_date, 10);");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");
		List<ChartBean> one = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(startDate, -days);
		// Long newTime = sdf.parse(newStart).getTime() / 1000;
		String newBegin = newStart;
		if (dateType != null && !"day".equals(dateType)) {
			newBegin = ChartUtil.changeByStartDate(newStart, dateType, sdf);
		}
		// paramMap.put("startTime", newTime);
		// paramMap.put("endTime", startTime);
		paramMap.put("startTime", newBegin);
		paramMap.put("endTime", startDate + " 23:59:59");
		List<ChartBean> two = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			if (dateType != null) {
				/*
				 * 根据按周查询和按月查询做不同的处理
				 */
				if ("day".equals(dateType)) {
					resusltMap = ChartUtil.parserDayMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				} else if ("week".equals(dateType)) {
					resusltMap = ChartUtil.parserWeekMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				} else if ("month".equals(dateType)) {
					resusltMap = ChartUtil.parserMonthMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				}
			}
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内付费的人数:时段对比
	 * 
	 * @param angle
	 *            分析角度
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @param timeText
	 *            当前选择的日期
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserPayMapByTime(String angle, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String timeText) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`pay_date`<=:endTime AND tu.`pay_date`>=:startTime ");
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND u.province LIKE :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city LIKE :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		String str = " COUNT(1)";
		if (angle != null && !"".equals(angle) && "2".equals(angle)) {
			str = " sum(tu.money)";
		}

		StringBuffer sb1 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.pay_date, 10) AS date FROM tyt_success_account tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY LEFT (tu.pay_date, 10);");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");
		List<ChartBean> one = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(timeText, -days);
		// long newTime = sdf.parse(newStart).getTime() / 1000;
		// long newEndTime = sdf.parse(timeText).getTime() / 1000 +
		// ONE_DAY_IN_MINUTE;
		String newBegin = newStart;
		if (dateType != null && !"day".equals(dateType)) {
			newBegin = ChartUtil.changeByStartDate(newStart, dateType, sdf);
		}
		// paramMap.put("startTime", newTime);
		// paramMap.put("endTime", newEndTime);
		paramMap.put("startTime", newBegin);
		paramMap.put("endTime", timeText + " 23:59:59");
		List<ChartBean> two = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			if (dateType != null) {
				/*
				 * 根据按周查询和按月查询做不同的处理
				 */
				if ("day".equals(dateType)) {
					resusltMap = ChartUtil.parserDayMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				} else if ("week".equals(dateType)) {
					resusltMap = ChartUtil.parserWeekMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				} else if ("month".equals(dateType)) {
					resusltMap = ChartUtil.parserMonthMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				}
			}
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量
	 * 
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return Line单线
	 */
	public Map<String, String> getUserSourceMap(String deliverType, String province, String city, String source, String sourceRemark, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
//		if (startDate != null && endDate != null && !"".equals(startDate) && !"".equals(endDate)) {
//			try {
//				startTime = sdf.parse(startDate).getTime() / 1000;
//				endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
//			} catch (ParseException e) {
//				e.printStackTrace();
//			}
//		} else {
//			// 获取当前的秒值
//			endTime = System.currentTimeMillis() / 1000;
//			/*
//			 * 计算30天前的秒值
//			 */
//			Date date = new Date(System.currentTimeMillis());
//			String time = sdf.format(date);
//			try {
//				startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
		StringBuffer sb = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date FROM tyt_user tu WHERE tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime");
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}
		sb.append(" GROUP BY LEFT(tu.`ctime`, 10);");
		String conditionSQL = sb.toString();
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");
		List<ChartBean> charBeans = this.getBaseDao().searchByName(conditionSQL, map, ChartBean.class, paramMap);
		if (charBeans != null && charBeans.size() > 0) {
			/*
			 * 填充从开始到结束数据为0的日期
			 */
			// charBeans = ChartUtil.addNotExitsDate(charBeans);
			// charBeans = ChartUtil.addBeforeNotExitsDate(startTime,
			// charBeans);
			// charBeans = ChartUtil.addAfterNotExitsDate(endTime -
			// ONE_DAY_IN_MINUTE, charBeans);
			// if (dateType != null) {
			// /*
			// * 根据按周查询和按月查询做不同的处理
			// */
			// if ("week".equals(dateType)) {
			// try {
			// return ChartUtil.convertToMapByWeek(charBeans);
			// } catch (ParseException e) {
			// }
			// } else if ("month".equals(dateType)) {
			// try {
			// return ChartUtil.convertToMapByMonth(charBeans);
			// } catch (NumberFormatException | ParseException e) {
			// }
			// } else if ("day".equals(dateType)) {
			// return ChartUtil.convertToMap(charBeans);
			// }
			// }
			if (dateType != null) {
				/*
				 * 根据按周查询和按月查询做不同的处理
				 */
				if ("week".equals(dateType)) {
					charBeans = ChartUtil.addNotExitsDate(charBeans, sdf.format(ChartUtil.findMonday(startDate)), endDate);
					return ChartUtil.convertToMapByWeek(charBeans);
				} else if ("month".equals(dateType)) {
					charBeans = ChartUtil.addNotExitsDate(charBeans, sdf.format(ChartUtil.findOneDate(startDate)), endDate);
					return ChartUtil.convertToMapByMonth(charBeans);
				}
			}
			charBeans = ChartUtil.addNotExitsDate(charBeans, startDate, endDate);
			return ChartUtil.convertToMap(charBeans);
		}
		return ChartUtil.convertToMap(charBeans);
	}

	/**
	 * 获取一段时间内用户来源数量:地区前五
	 * 
	 * @param deliverType
	 *            身份
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapByTopArea(String deliverType, String source, String sourceRemark, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime");
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}

		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}

		StringBuffer sb1 = new StringBuffer("SELECT COUNT(1) as quality,tu.city as name FROM tyt_user tu WHERE ");
		sb1.append(sb.toString());
		sb1.append(" and tu.city is not null GROUP BY tu.city ORDER BY COUNT(1) DESC LIMIT 5;");

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date,tu.city as name FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		if (dateType != null && !"day".equals(dateType)) {
			startDate = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", startDate);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);
		map.put("date", Hibernate.STRING);
		sb2.append(" AND (");
		for (MultiLineDataBean bean : top) {
//			sb2.append(" tu.city like '%" + bean.getName().replace("市", "") + "%' or");
			 sb2.append(" tu.city = '" + bean.getName() + "' or");
		}
		sb2.delete(sb2.length() - 2, sb2.length());
		sb2.append(") GROUP BY LEFT (tu.`ctime`, 10), tu.city;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量:按省查
	 * 
	 * @param deliverType
	 *            身份
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapByProvinces(String deliverType, String source, String sourceRemark, String startDate, String endDate, String dateType, String selectedProvinces) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime");
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}
		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date,tu.province as name FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedProvinces != null && !"".equals(selectedProvinces)) {
			sb2.append(" AND (");
			String[] arr = selectedProvinces.split(",");
			for (String str : arr) {
				sb2.append(" tu.province like '%" + str + "%' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), tu.province;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量:按市级查
	 * 
	 * @param deliverType
	 *            身份
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapByCities(String deliverType, String source, String sourceRemark, String startDate, String endDate, String dateType, String selectedCities) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime");
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}
		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date,tu.city as name FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedCities != null && !"".equals(selectedCities)) {
			sb2.append(" AND (");
//			String[] arr = selectedCities.replace("市", "").split(",");
			String[] arr = selectedCities.split(",");
			for (String str : arr) {
//				sb2.append(" tu.city like '%" + str + "%' or");
				sb2.append(" tu.city = '" + str + "' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), tu.city;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量:身份前五
	 * 
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapByTopCardId(String province, String city, String source, String sourceRemark, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime ");
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}

		StringBuffer sb1 = new StringBuffer("SELECT COUNT(1) as quality,CASE tu.deliver_type ");
		sb1.append(deliverStr);
		// + "WHEN 1 THEN '个人货主' WHEN 2 THEN '货站' WHEN 3 THEN '司机' WHEN 4 THEN
		// '车主' WHEN 5 THEN '企业货主' WHEN 6 THEN '内部员工' WHEN 7 THEN '无效' else '待定'
		// END"
		sb1.append(" as name,tu.deliver_type source FROM tyt_user tu WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY tu.deliver_type ORDER BY COUNT(1) DESC LIMIT 5;");

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date,CASE tu.deliver_type ");
		sb2.append(deliverStr);
		// + "WHEN 1 THEN '个人货主' WHEN 2 THEN '货站' WHEN 3 THEN '司机' WHEN 4 THEN
		// '车主' WHEN 5 THEN '企业货主' WHEN 6 THEN '内部员工' WHEN 7 THEN '无效' else '待定'
		// END"
		sb2.append(" as name FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("source", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);
		map.remove("source");
		map.put("date", Hibernate.STRING);
		if (top != null && top.size() > 0) {
			sb2.append(" and (");
			for (MultiLineDataBean bean : top) {
				if (bean.getName() != null && !"null".equals(bean.getName())) {
					// if ("个人货主".equals(bean.getName())) {
					// sb2.append(" tu.deliver_type=1 or");
					// } else if ("货站".equals(bean.getName())) {
					// sb2.append(" tu.deliver_type=2 or");
					// } else if ("司机".equals(bean.getName())) {
					// sb2.append(" tu.deliver_type=3 or");
					// } else if ("车主".equals(bean.getName())) {
					// sb2.append(" tu.deliver_type=4 or");
					// } else if ("企业货主".equals(bean.getName())) {
					// sb2.append(" tu.deliver_type=5 or");
					// } else if ("内部员工".equals(bean.getName())) {
					// sb2.append(" tu.deliver_type=6 or");
					// } else if ("无效".equals(bean.getName())) {
					// sb2.append(" tu.deliver_type=7 or");
					// } else
					if ("待定".equals(bean.getName())) {
						sb2.append(" tu.deliver_type='' or tu.deliver_type is null or");
					} else {
						sb2.append(" tu.deliver_type=" + bean.getSource() + " or");
					}

				} else {
					sb2.append("1=1 or");
				}
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), tu.deliver_type;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量:身份对比
	 * 
	 * @param selectedDeliverType
	 *            选择的身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapByIdCard(String selectedDeliverType, String province, String city, String source, String sourceRemark, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime ");
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date,CASE tu.deliver_type ");
		sb2.append(deliverStr);
		// + "WHEN 1 THEN '个人货主' WHEN 2 THEN '货站' WHEN 3 THEN '司机' WHEN 4 THEN
		// '车主' WHEN 5 THEN '企业货主' WHEN 6 THEN '内部员工' WHEN 7 THEN '无效' WHEN 8
		// THEN '生产型企业货主' WHEN 9 THEN '服务型企业货主' WHEN 10 THEN '应用型企业货主' WHEN 11
		// THEN '专业大件货站' WHEN 12 THEN '综合货站' WHEN 13 THEN '设备持有货主' WHEN 14 THEN
		// '设备买卖货主' WHEN 15 THEN '运输公司' WHEN 16 THEN '运输车队' WHEN 17 THEN '个人车主'
		// WHEN 18 THEN '板车司机' WHEN 19 THEN '工程车司机' WHEN 20 THEN '设备修理' WHEN 21
		// THEN '设备配件' WHEN 22 THEN '其他发货服务方' WHEN 23 THEN '挂车生产' WHEN 24 THEN
		// '车头生产' WHEN 25 THEN '挂车代理' WHEN 26 THEN '车头代理' WHEN 27 THEN '板车修理'
		// WHEN 28 THEN '板车配件' WHEN 29 THEN '设备持有货主' else '待定' END"
		sb2.append(" AS NAME FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedDeliverType != null && !"".equals(selectedDeliverType)) {
			sb2.append(" AND (");
			String[] arr = selectedDeliverType.split(",");
			for (String str : arr) {
				if ("-100".equals(str)) {
					sb2.append("  tu.`deliver_type` IS NULL OR tu.`deliver_type`='' or");
				} else {
					sb2.append("  tu.`deliver_type`='" + str + "' or");
				}
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), tu.deliver_type;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量:来源对比
	 * 
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapBySource(String province, String city, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime ");
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		StringBuffer sb1 = new StringBuffer("select id AS quality,value as name FROM tyt_source WHERE group_code='source' and `status`=0");

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date,tu.source as name FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate);

		if (top != null && top.size() > 0) {
			sb2.append(" AND (");
			for (MultiLineDataBean bean : top) {
				sb2.append(" tu.source ='" + bean.getName() + "' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), tu.source;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量:来源对比
	 * 
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapByTopSourceRemark(String deliverType, String province, String city, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime ");
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		}
		if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}
		StringBuffer sb1 = new StringBuffer("SELECT COUNT(1) AS quality, tu.source as source,tu.source_remark as sourceRemark FROM tyt_user tu WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY tu.source,tu.source_remark ORDER BY count(1) desc LIMIT 5;");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("source", Hibernate.STRING);
		map.put("sourceRemark", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);

		map.put("date", Hibernate.STRING);
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		// String dateBegin = sdf.format(new Date(startTime));
		// paramMap.put("startTime", dateBegin);
		// paramMap.put("endTime", sdf.format(new Date(endTime))+" 23:59:59");

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT (tu.ctime, 10) AS date, tu.source, ( SELECT t.`name` FROM tyt_source t WHERE t.group_code = CONCAT('source_', tu.source) AND t.`value` = tu.source_remark ) AS sourceRemark FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		if (top != null && top.size() > 0) {
			sb2.append(" and (");
			for (MultiLineDataBean bean : top) {
				if (bean.getSourceRemark() == null || "null".equals(bean.getSourceRemark())) {
					sb2.append(" (tu.source='" + bean.getSource() + "' and tu.source_remark='') or");
				} else {
					sb2.append(" (tu.source='" + bean.getSource() + "' and tu.source_remark='" + bean.getSourceRemark() + "') or");
				}
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}

		sb2.append(" GROUP BY LEFT (tu.ctime, 10), tu.source, tu.source_remark;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		List<MultiLineDataBean> two = new ArrayList<MultiLineDataBean>();
		for (MultiLineDataBean mBean : cityRegisterBeans) {
			mBean.setName(mBean.getSource() + "[" + mBean.getSourceRemark() + "]");
			two.add(mBean);
		}
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量:来源备注对比
	 * 
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapBySourceRemark(String deliverType, String province, String city, String startDate, String endDate, String dateType, String selectedSRemark) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer(" tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime ");
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("source", Hibernate.STRING);
		map.put("sourceRemark", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}

		map.put("date", Hibernate.STRING);
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		StringBuffer sb2 = new StringBuffer("SELECT COUNT(1) AS quality, LEFT (tu.ctime, 10) AS date, tu.source, ( SELECT t.`name` FROM tyt_source t WHERE t.group_code = CONCAT('source_', tu.source) AND t.`value` = tu.source_remark ) AS sourceRemark FROM tyt_user tu WHERE ");
		sb2.append(sb.toString());

		if (selectedSRemark != null && !"".equals(selectedSRemark)) {
			sb2.append(" and (");
			String[] sRemark = selectedSRemark.split(",");
			for (String str : sRemark) {
				int i = str.indexOf("_");
				if (i > 0) {
					String source = str.substring(0, i);
					String remark = str.substring(i + 1, str.length());
					if ("".equals(remark)) {
						sb2.append(" (tu.source='" + source + "' and tu.source_remark='') or");
					} else {
						sb2.append(" (tu.source='" + source + "' and tu.source_remark='" + remark + "') or");
					}
				}
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}

		sb2.append(" GROUP BY LEFT (tu.ctime, 10), tu.source, tu.source_remark;");

		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		List<MultiLineDataBean> two = new ArrayList<MultiLineDataBean>();
		for (MultiLineDataBean mBean : cityRegisterBeans) {
			mBean.setName(mBean.getSource() + "[" + mBean.getSourceRemark() + "]");
			two.add(mBean);
		}
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量：近期对比
	 * 
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线图表数据
	 */
	public Map<String, String> getUserSourceMapByNearTime(String deliverType, String province, String city, String source, String sourceRemark, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date FROM tyt_user tu WHERE tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime");
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}
		sb.append(" GROUP BY LEFT(tu.`ctime`, 10);");
		String conditionSQL = sb.toString();
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<ChartBean> one = this.getBaseDao().searchByName(conditionSQL, map, ChartBean.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(startDate, -days);
		// Long newTime = sdf.parse(newStart).getTime() / 1000;
		String newBegin = newStart;
		if (dateType != null && !"day".equals(dateType)) {
			newBegin = ChartUtil.changeByStartDate(newStart, dateType, sdf);
		}
		// paramMap.put("startTime", newTime);
		// paramMap.put("endTime", startTime);
		paramMap.put("startTime", newBegin);
		paramMap.put("endTime", startDate + " 23:59:59");

		List<ChartBean> two = this.getBaseDao().searchByName(conditionSQL, map, ChartBean.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			if (dateType != null) {
				/*
				 * 根据按周查询和按月查询做不同的处理
				 */
				if ("day".equals(dateType)) {
					resusltMap = ChartUtil.parserDayMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				} else if ("week".equals(dateType)) {
					resusltMap = ChartUtil.parserWeekMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				} else if ("month".equals(dateType)) {
					resusltMap = ChartUtil.parserMonthMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				}
			}
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户来源数量：时段对比
	 * 
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param source
	 *            用户来源
	 * @param sourceRemark
	 *            用户来源备注
	 * @param startDate
	 *            注册日期-开始
	 * @param endDate
	 *            注册日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @param timeText
	 *            当前选择的日期
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSourceMapByTime(String deliverType, String province, String city, String source, String sourceRemark, String startDate, String endDate, String dateType, String timeText) throws Exception {
		long startTime = 0;
		long endTime = 0;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Map<String, String> resusltMap = new HashMap<String, String>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// try {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } catch (ParseException e) {
		// e.printStackTrace();
		// }
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// try {
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		// }
		StringBuffer sb = new StringBuffer("SELECT COUNT(1) AS quality, LEFT(tu.ctime, 10) AS date FROM tyt_user tu WHERE tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime");
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND tu.`deliver_type`=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {
			/*
			 * 处理用户核准身份待定的情况
			 */
			sb.append(" AND (tu.`deliver_type` IS NULL OR tu.`deliver_type`=:deliverType)");
			paramMap.put("deliverType", "");
		}
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			// 因为数据库中并非按照固定的要求存储（如河南省存储为河南，香港存储为香港特别行政区等），这里使用like语句
			sb.append(" AND tu.`province` like :province");
			paramMap.put("province", "%" + province + "%");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND tu.`city` like :city");
				// 去除最后一个字符"市"更加的准确的匹配数据
				paramMap.put("city", "%" + city.substring(0, city.length() - 1) + "%");
			}
		}
		if (source != null && !"".equals(source) && !"0".equals(source)) {
			sb.append(" and tu.source = :source");
			paramMap.put("source", TytSourceUtil.getTytSource(Long.valueOf(source)).getName());
		}
		if (sourceRemark != null && !"".equals(sourceRemark) && !"0".equals(sourceRemark)) {
			sb.append(" and tu.source_remark = :sourceRemark");
			paramMap.put("sourceRemark", TytSourceUtil.getTytSource(Long.valueOf(sourceRemark)).getValue());
		}
		sb.append(" GROUP BY LEFT(tu.`ctime`, 10);");
		String conditionSQL = sb.toString();
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<ChartBean> one = this.getBaseDao().searchByName(conditionSQL, map, ChartBean.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(timeText, -days);
		// long newTime = sdf.parse(newStart).getTime() / 1000;
		// long newEndTime = sdf.parse(timeText).getTime() / 1000 +
		// ONE_DAY_IN_MINUTE;
		String newBegin = newStart;
		if (dateType != null && !"day".equals(dateType)) {
			newBegin = ChartUtil.changeByStartDate(newStart, dateType, sdf);
		}
		// paramMap.put("startTime", newTime);
		// paramMap.put("endTime", newEndTime);
		paramMap.put("startTime", newBegin);
		paramMap.put("endTime", timeText + " 23:59:59");

		List<ChartBean> two = this.getBaseDao().searchByName(conditionSQL, map, ChartBean.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			if (dateType != null) {
				/*
				 * 根据按周查询和按月查询做不同的处理
				 */
				if ("day".equals(dateType)) {
					resusltMap = ChartUtil.parserDayMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				} else if ("week".equals(dateType)) {
					resusltMap = ChartUtil.parserWeekMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				} else if ("month".equals(dateType)) {
					resusltMap = ChartUtil.parserMonthMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				}
			}
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况
	 * 
	 * @param angle
	 *            分析角度
	 * @param status
	 *            货源状态
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return Line单线
	 */
	public Map<String, String> getUserSendGoodsMap(String angle, String status, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, Object> paramMap = new HashMap<String, Object>();
		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb = new StringBuffer("SELECT " + str + " AS quality,LEFT(tu.ctime, 10) AS date FROM tyt_transport_main tu left join tyt_user u on u.id=tu.user_id WHERE tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR u.deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}
		/*
		 * 省不为空并且不是所有省才做处理
		 */
//		if (province != null && !"allProvince".equals(province)) {
//			if (city != null && !"allCity".equals(city)) {
//				sb.append(" AND tu.start_city ='" + city + "'");
//			}
//		}
		if (province != null && !"allProvince".equals(province)) {
//			sb.append(" AND u.province ='" + province + "'");
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city like '%" + city + "%'");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}
		sb.append(" GROUP BY LEFT(tu.`ctime`, 10);");
		String conditionSQL = sb.toString();
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<ChartBean> charBeans = this.getBaseDao().searchByName(conditionSQL, map, ChartBean.class, paramMap);
		/*
		 * 填充从开始到结束数据为0的日期
		 */
		// charBeans = ChartUtil.addNotExitsDate(charBeans);
		// charBeans = ChartUtil.addBeforeNotExitsDate(startTime, charBeans);
		// charBeans = ChartUtil.addAfterNotExitsDate(endTime -
		// ONE_DAY_IN_MINUTE, charBeans);
		if (dateType != null) {
			/*
			 * 根据按周查询和按月查询做不同的处理
			 */
			if ("week".equals(dateType)) {
				charBeans = ChartUtil.addNotExitsDate(charBeans, sdf.format(ChartUtil.findMonday(startDate)), endDate);
				return ChartUtil.convertToMapByWeek(charBeans);
			} else if ("month".equals(dateType)) {
				charBeans = ChartUtil.addNotExitsDate(charBeans, sdf.format(ChartUtil.findOneDate(startDate)), endDate);
				return ChartUtil.convertToMapByMonth(charBeans);
			}
		}
		charBeans = ChartUtil.addNotExitsDate(charBeans, startDate, endDate);
		return ChartUtil.convertToMap(charBeans);
	}

	/**
	 * 获取一段时间内用户发货情况:地区前五-按市
	 * 
	 * @param angle
	 *            分析角度
	 * @param status
	 *            货源状态
	 * @param deliverType
	 *            身份
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByTopArea(String angle, String status, String deliverType, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}

		StringBuffer sb1 = new StringBuffer("SELECT " + str + " AS quality,u.city as name FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY u.city ORDER BY " + str + " DESC LIMIT 5;");

		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date, u.city AS NAME FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);
		map.put("date", Hibernate.STRING);
		if (top != null && top.size() > 0) {
			sb2.append(" AND (");
			for (MultiLineDataBean bean : top) {
				sb2.append(" u.city = '" + bean.getName() + "' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(") ");
		}
		sb2.append("GROUP BY LEFT (tu.`ctime`, 10), u.city;");
		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况:地区对比-省
	 * 
	 * @param selectedProvinces
	 *            选择的省份
	 * @param angle
	 *            分析角度
	 * @param status
	 *            货源状态
	 * @param deliverType
	 *            身份
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByProvinces(String angle, String status, String deliverType, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String selectedProvinces) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date, u.province AS NAME FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedProvinces != null && !"".equals(selectedProvinces)) {
			sb2.append(" AND (");
			String[] arr = selectedProvinces.split(",");
			for (String str1 : arr) {
				sb2.append(" u.province like '%" + str1 + "%' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), u.province;");
		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况:地区对比-市
	 * 
	 * @param angle
	 *            分析角度
	 * @param selectedCities
	 *            选择的市
	 * @param status
	 *            货源状态
	 * @param deliverType
	 *            身份
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByCities(String angle, String status, String deliverType, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String selectedCities) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0");

		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date, u.city AS NAME FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedCities != null && !"".equals(selectedCities)) {
			sb2.append(" AND (");
			// String[] arr = selectedCities.replace("市", "").split(",");
			String[] arr = selectedCities.split(",");
			for (String str1 : arr) {
				sb2.append(" u.city = '" + str1 + "' or");
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), u.city;");
		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况:身份前五
	 * 
	 * @param angle
	 *            分析角度
	 * @param status
	 *            货源状态
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByTopCardId(String angle, String status, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0");

		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city ='" + city + "'");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb1 = new StringBuffer("SELECT " + str + " AS quality,CASE u.deliver_type ");
		sb1.append(deliverStr);
		// + "WHEN 1 THEN '个人货主' WHEN 2 THEN '货站' WHEN 3 THEN '司机' WHEN 4 THEN
		// '车主' WHEN 5 THEN '企业货主' WHEN 6 THEN '内部员工' WHEN 7 THEN '无效' else '待定'
		// END"
		sb1.append(" as name,u.deliver_type source FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append("  GROUP BY u.deliver_type ORDER BY " + str + " DESC LIMIT 5;");

		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date, CASE u.deliver_type ");
		sb2.append(deliverStr);
		// + "WHEN 1 THEN '个人货主' WHEN 2 THEN '货站' WHEN 3 THEN '司机' WHEN 4 THEN
		// '车主' WHEN 5 THEN '企业货主' WHEN 6 THEN '内部员工' WHEN 7 THEN '无效' else '待定'
		// END"
		sb2.append(" AS NAME FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("source", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);
		map.remove("source");
		map.put("date", Hibernate.STRING);
		if (top != null && top.size() > 0) {
			sb2.append(" and (");
			for (MultiLineDataBean bean : top) {
				if (bean.getName() != null && !"null".equals(bean.getName())) {
					// if ("个人货主".equals(bean.getName())) {
					// sb2.append(" u.deliver_type=1 or");
					// } else if ("货站".equals(bean.getName())) {
					// sb2.append(" u.deliver_type=2 or");
					// } else if ("司机".equals(bean.getName())) {
					// sb2.append(" u.deliver_type=3 or");
					// } else if ("车主".equals(bean.getName())) {
					// sb2.append(" u.deliver_type=4 or");
					// } else if ("企业货主".equals(bean.getName())) {
					// sb2.append(" u.deliver_type=5 or");
					// } else if ("内部员工".equals(bean.getName())) {
					// sb2.append(" u.deliver_type=6 or");
					// } else if ("无效".equals(bean.getName())) {
					// sb2.append(" u.deliver_type=7 or");
					// } else
					if ("待定".equals(bean.getName())) {
						sb2.append(" u.deliver_type='' or u.deliver_type is null or");
					} else {
						sb2.append(" u.deliver_type=" + bean.getSource() + " or");
					}

				} else {
					sb2.append("1=1 or");
				}
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), u.deliver_type;");
		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况:身份对比
	 * 
	 * @param selectedDeliverType
	 *            选择的身份
	 * @param angle
	 *            分析角度
	 * @param status
	 *            货源状态
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByIdCard(String selectedDeliverType, String angle, String status, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0");

		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city ='" + city + "'");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date, CASE u.deliver_type ");
		sb2.append(deliverStr);
		// + "WHEN 1 THEN '个人货主' WHEN 2 THEN '货站' WHEN 3 THEN '司机' WHEN 4 THEN
		// '车主' WHEN 5 THEN '企业货主' WHEN 6 THEN '内部员工' WHEN 7 THEN '无效' WHEN 8
		// THEN '生产型企业货主' WHEN 9 THEN '服务型企业货主' WHEN 10 THEN '应用型企业货主' WHEN 11
		// THEN '专业大件货站' WHEN 12 THEN '综合货站' WHEN 13 THEN '设备持有货主' WHEN 14 THEN
		// '设备买卖货主' WHEN 15 THEN '运输公司' WHEN 16 THEN '运输车队' WHEN 17 THEN '个人车主'
		// WHEN 18 THEN '板车司机' WHEN 19 THEN '工程车司机' WHEN 20 THEN '设备修理' WHEN 21
		// THEN '设备配件' WHEN 22 THEN '其他发货服务方' WHEN 23 THEN '挂车生产' WHEN 24 THEN
		// '车头生产' WHEN 25 THEN '挂车代理' WHEN 26 THEN '车头代理' WHEN 27 THEN '板车修理'
		// WHEN 28 THEN '板车配件' WHEN 29 THEN '设备持有货主' else '待定' END"
		sb2.append(" AS NAME FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("name", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		if (selectedDeliverType != null && !"".equals(selectedDeliverType)) {
			sb2.append(" AND (");
			String[] arr = selectedDeliverType.split(",");
			for (String str1 : arr) {
				if ("-100".equals(str1)) {
					sb2.append("  (u.`deliver_type` IS NULL OR u.`deliver_type`='') or");
				} else {
					sb2.append("  u.`deliver_type`='" + str1 + "' or");
				}
			}
			sb2.delete(sb2.length() - 2, sb2.length());
			sb2.append(")");
		}
		sb2.append(" GROUP BY LEFT (tu.`ctime`, 10), u.deliver_type;");
		List<MultiLineDataBean> cityRegisterBeans = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况:状态对比
	 * 
	 * @param angle
	 *            分析角度
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByStatus(String angle, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0 ");

		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city ='" + city + "'");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb1 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append(" and (tu.status=1 or tu.status=4) GROUP BY LEFT (tu.ctime, 10);");

		StringBuffer sb2 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date, CASE tu.status WHEN 1 THEN '人工有效' WHEN 4 THEN '人工成交' END AS NAME FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb2.append(sb.toString());

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<MultiLineDataBean> top = this.getBaseDao().searchByName(sb1.toString(), map, MultiLineDataBean.class, paramMap);
		List<MultiLineDataBean> cityRegisterBeans = new ArrayList<MultiLineDataBean>();
		for (MultiLineDataBean bean : top) {
			bean.setName("人工总量");
			cityRegisterBeans.add(bean);
		}
		map.put("name", Hibernate.STRING);
		sb2.append(" and (tu.status=1 or tu.status=4) GROUP BY LEFT (tu.`ctime`, 10), tu.status;");
		List<MultiLineDataBean> second = this.getBaseDao().searchByName(sb2.toString(), map, MultiLineDataBean.class, paramMap);
		for (MultiLineDataBean bean : second) {
			cityRegisterBeans.add(bean);
		}

		if (cityRegisterBeans != null && cityRegisterBeans.size() > 0) {
			resusltMap = ChartUtil.parserToMultiLineData(cityRegisterBeans, startDate, endDate, dateType);
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况:近期同比
	 * 
	 * @param angle
	 *            分析角度
	 * @param status
	 *            货源状态
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByNearTime(String angle, String status, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0 ");
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city ='" + city + "'");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb1 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date FROM tyt_transport_main tu LEFT JOIN tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY LEFT (tu.ctime, 10);");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<ChartBean> one = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(startDate, -days);
		// Long newTime = sdf.parse(newStart).getTime() / 1000;
		String newBegin = newStart;
		if (dateType != null && !"day".equals(dateType)) {
			newBegin = ChartUtil.changeByStartDate(newStart, dateType, sdf);
		}
		// paramMap.put("startTime", newTime);
		// paramMap.put("endTime", startTime);
		paramMap.put("startTime", newBegin);
		paramMap.put("endTime", startDate + " 23:59:59");

		List<ChartBean> two = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			if (dateType != null) {
				/*
				 * 根据按周查询和按月查询做不同的处理
				 */
				if ("day".equals(dateType)) {
					resusltMap = ChartUtil.parserDayMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				} else if ("week".equals(dateType)) {
					resusltMap = ChartUtil.parserWeekMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				} else if ("month".equals(dateType)) {
					resusltMap = ChartUtil.parserMonthMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate, startDate, endDate, newBegin, startDate);
				}
			}
		}
		return resusltMap;
	}

	/**
	 * 获取一段时间内用户发货情况:时段对比
	 * 
	 * @param angle
	 *            分析角度
	 * @param status
	 *            货源状态
	 * @param deliverType
	 *            身份
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param cTimeStart
	 *            注册日期-开始
	 * @param cTimeEnd
	 *            注册日期-结束
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param dateType
	 *            分析类型:日，周，月
	 * @return multiLine多线表数据
	 */
	public Map<String, String> getUserSendGoodsMapByTime(String angle, String status, String deliverType, String province, String city, String cTimeStart, String cTimeEnd, String startDate, String endDate, String dateType, String timeText) throws Exception {
		long startTime = 0;
		long endTime = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> resusltMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();

		/*
		 * 开始时间和结束时间都不为空才做处理，否则默认查询最近三十天，包括当天
		 */
		// if (startDate != null && endDate != null && !"".equals(startDate) &&
		// !"".equals(endDate)) {
		// startTime = sdf.parse(startDate).getTime() / 1000;
		// endTime = sdf.parse(endDate).getTime() / 1000 + ONE_DAY_IN_MINUTE;
		// } else {
		// // 获取当前的秒值
		// endTime = System.currentTimeMillis() / 1000;
		// /*
		// * 计算30天前的秒值
		// */
		// Date date = new Date(System.currentTimeMillis());
		// String time = sdf.format(date);
		// startTime = TimeUtil.addDay(time, -30).getTime() / 1000;
		// }
		StringBuffer sb = new StringBuffer("tu.`ctime`<=:endTime AND tu.`ctime`>=:startTime and tu.source=0 ");
		if (status != null && !"".equals(status) && !"100".equals(status)) {
			sb.append(" AND tu.status = :status");
			paramMap.put("status", status);
		} else if ("100".equals(status)) {
			sb.append(" AND (tu.status = 1 or tu.status = 4)");
		}
		/*
		 * 省不为空并且不是所有省才做处理
		 */
		if (province != null && !"allProvince".equals(province)) {
			if (city != null && !"allCity".equals(city)) {
				sb.append(" AND u.city ='" + city + "'");
			}
		}

		if (cTimeStart != null && cTimeEnd != null && !"".equals(cTimeStart) && !"".endsWith(cTimeEnd)) {
			sb.append(" AND u.ctime >= :cTimeStart and u.ctime <= :cTimeEnd");
			paramMap.put("cTimeStart", cTimeStart);
			paramMap.put("cTimeEnd", cTimeEnd);
		}
		/*
		 * 空不做处理，0代表页面选择的是所有，同样不作处理
		 */
		if (deliverType != null && Integer.valueOf(deliverType) != 0 && !"-100".equals(deliverType)) {
			sb.append(" AND u.deliver_type=:deliverType");
			paramMap.put("deliverType", deliverType);
		} else if ("-100".equals(deliverType)) {// 待定身份
			sb.append(" AND (u.deliver_type=:deliverType OR deliver_type IS NULL)");
			paramMap.put("deliverType", "");
		}

		String str = " COUNT(1) ";
		if ("2".equals(angle)) {
			str = " COUNT(DISTINCT tu.user_id) ";
		} else if ("3".equals(angle)) {
			str = " COUNT(1)/COUNT(DISTINCT tu.user_id) ";
		}
		StringBuffer sb1 = new StringBuffer("SELECT " + str + " AS quality,LEFT (tu.ctime, 10) AS date FROM tyt.tyt_transport_main tu LEFT JOIN tyt.tyt_user u ON tu.user_id = u.id WHERE ");
		sb1.append(sb.toString());
		sb1.append(" GROUP BY LEFT (tu.ctime, 10);");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("quality", Hibernate.STRING);
		map.put("date", Hibernate.STRING);
		String beginTime = startDate;
		if (dateType != null && !"day".equals(dateType)) {
			beginTime = ChartUtil.changeByStartDate(startDate, dateType, sdf);
		}
		// paramMap.put("startTime", startTime);
		// paramMap.put("endTime", endTime);
		paramMap.put("startTime", beginTime);
		paramMap.put("endTime", endDate + " 23:59:59");

		List<ChartBean> one = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(timeText, -days);
		// long newTime = sdf.parse(newStart).getTime() / 1000;
		// long newEndTime = sdf.parse(timeText).getTime() / 1000 +
		// ONE_DAY_IN_MINUTE;
		String newBegin = newStart;
		if (dateType != null && !"day".equals(dateType)) {
			newBegin = ChartUtil.changeByStartDate(newStart, dateType, sdf);
		}
		// paramMap.put("startTime", newTime);
		// paramMap.put("endTime", newEndTime);
		paramMap.put("startTime", newBegin);
		paramMap.put("endTime", timeText + " 23:59:59");

		List<ChartBean> two = this.getBaseDao().searchByName(sb1.toString(), map, ChartBean.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			if (dateType != null) {
				/*
				 * 根据按周查询和按月查询做不同的处理
				 */
				if ("day".equals(dateType)) {
					resusltMap = ChartUtil.parserDayMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				} else if ("week".equals(dateType)) {
					resusltMap = ChartUtil.parserWeekMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				} else if ("month".equals(dateType)) {
					resusltMap = ChartUtil.parserMonthMultiLineDataByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText, startDate, endDate, newBegin, timeText);
				}
			}
		}
		return resusltMap;
	}

	/**
	 * 根据父id获取所有的子id
	 * 
	 * @param pid
	 * @return
	 */
	public List<TytGeoDict> findListTytGeoDictByPid(String pid) throws Exception {
		List<TytGeoDict> list = new ArrayList<TytGeoDict>();
		String sql = "SELECT id,name,px,py,pid FROM tyt_geo_dict where pid=" + pid + " ORDER BY id asc;";
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("id", Hibernate.LONG);
		map.put("name", Hibernate.STRING);
		map.put("px", Hibernate.STRING);
		map.put("py", Hibernate.STRING);
		map.put("pid", Hibernate.STRING);
		list = this.getBaseDao().searchByName(sql, map, TytGeoDict.class, null);
		return list;
	}

	/**
	 * 根据id获取字典
	 * 
	 * @param pid
	 * @return
	 */
	public List<TytGeoDict> findListTytGeoDictById(String id) throws Exception {
		List<TytGeoDict> list = new ArrayList<TytGeoDict>();
		String sql = "SELECT id,name,px,py,pid FROM tyt_geo_dict where id=" + id + ";";
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("id", Hibernate.LONG);
		map.put("name", Hibernate.STRING);
		map.put("px", Hibernate.STRING);
		map.put("py", Hibernate.STRING);
		map.put("pid", Hibernate.STRING);
		list = this.getBaseDao().searchByName(sql, map, TytGeoDict.class, null);
		return list;
	}

	/**
	 * 获取所有的地区
	 * 
	 * @param pid
	 * @return
	 */
	public List<TytGeoDict> findListTytGeoDict() throws Exception {
		List<TytGeoDict> list = new ArrayList<TytGeoDict>();
		String sql = "SELECT id,name,pid,type FROM tyt_geo_dict ORDER BY id asc;";
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("id", Hibernate.LONG);
		map.put("name", Hibernate.STRING);
		map.put("pid", Hibernate.STRING);
		map.put("type", Hibernate.STRING);
		list = this.getBaseDao().searchByName(sql, map, TytGeoDict.class, null);
		return list;
	}

	/**
	 * 获取一段时间内用户发货量和搜索人数
	 * 
	 * @param status
	 *            货源状态
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @return 多柱状图表
	 */
	public Map<String, String> getCarGoodsSearchMap(String status, String province, String city, String startDate, String endDate) throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT abs(sum(goodscount)-SUM(searchcount)) as differ,sum(goodscount) as goodscounts,sum(searchcount) as searchcounts,city FROM tyt_goods_search_count WHERE cdate>=:startDate and cdate<=:endDate");

		if (!"".equals(city) && !"allCity".equals(city) && city != null) {
			sb.append(" and city = :city");
			paramMap.put("city", city);
		}
		sb.append(" GROUP BY city ORDER BY abs(sum(goodscount)-SUM(searchcount)) desc LIMIT 7;");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("goodsCounts", Hibernate.INTEGER);
		map.put("searchCounts", Hibernate.INTEGER);
		map.put("differ", Hibernate.INTEGER);
		map.put("city", Hibernate.STRING);

		paramMap.put("startDate", startDate);
		paramMap.put("endDate", endDate);
		List<TytGoodsSearch> list = this.getBaseDao().searchByName(sb.toString(), map, TytGoodsSearch.class, paramMap);

		if (list != null && list.size() > 0) {
			resultMap = ChartUtil.parserMsColumn2d(list);
		}
		return resultMap;
	}

	/**
	 * 获取一段时间内用户发货量和搜索人数:市级前五
	 * 
	 * @param status
	 *            货源状态
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @return 多柱状图表
	 */
	public Map<String, String> getCarGoodsSearchMapByTop(String status, String province, String city, String startDate, String endDate) throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT abs(sum(goodscount)-SUM(searchcount)) as differ,sum(goodscount) as goodscounts,sum(searchcount) as searchcounts,city FROM tyt_goods_search_count WHERE cdate>=:startDate and cdate<=:endDate");

//		if (!"".equals(city) && !"allCity".equals(city) && city != null) {
//			sb.append(" and city = :city");
//			paramMap.put("city", city);
//		}
		sb.append(" GROUP BY city ORDER BY abs(sum(goodscount)-SUM(searchcount)) desc LIMIT 5;");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("goodsCounts", Hibernate.INTEGER);
		map.put("searchCounts", Hibernate.INTEGER);
		map.put("differ", Hibernate.INTEGER);
		map.put("city", Hibernate.STRING);

		paramMap.put("startDate", startDate);
		paramMap.put("endDate", endDate);
		List<TytGoodsSearch> list = this.getBaseDao().searchByName(sb.toString(), map, TytGoodsSearch.class, paramMap);

		if (list != null && list.size() > 0) {
			resultMap = ChartUtil.parserMsColumn2d(list);
		}
		return resultMap;
	}

	/**
	 * 获取一段时间内用户发货量和搜索人数:近期同比
	 * 
	 * @param status
	 *            货源状态
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @return 多柱状图表
	 */
	public Map<String, String> getCarGoodsSearchMapByNearTime(String status, String province, String city, String startDate, String endDate) throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT abs(sum(goodscount)-SUM(searchcount)) as differ,sum(goodscount) as goodscounts,sum(searchcount) as searchcounts FROM tyt_goods_search_count WHERE cdate>=:startDate and cdate<=:endDate ");
		if (!"".equals(city) && !"allCity".equals(city) && city != null) {
			sb.append(" and city = :city");
			paramMap.put("city", city);
		}
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("goodsCounts", Hibernate.INTEGER);
		map.put("searchCounts", Hibernate.INTEGER);
		map.put("differ", Hibernate.INTEGER);
		// map.put("city", Hibernate.STRING);

		paramMap.put("startDate", startDate);
		paramMap.put("endDate", endDate);
		List<TytGoodsSearch> one = this.getBaseDao().searchByName(sb.toString(), map, TytGoodsSearch.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(startDate, -days);
		paramMap.put("startDate", newStart);
		paramMap.put("endDate", startDate);
		List<TytGoodsSearch> two = this.getBaseDao().searchByName(sb.toString(), map, TytGoodsSearch.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			resultMap = ChartUtil.parserMsColumn2dByTime(one, two, startDate + "至" + endDate, newStart + "至" + startDate);
		}
		return resultMap;
	}

	/**
	 * 获取一段时间内用户发货量和搜索人数:时段对比
	 * 
	 * @param status
	 *            货源状态
	 * @param province
	 *            省
	 * @param city
	 *            市
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @param timeText
	 *            当前选择的日期
	 * @return 多柱状图表
	 */
	public Map<String, String> getCarGoodsSearchMapByTime(String status, String province, String city, String startDate, String endDate, String timeText) throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT abs(sum(goodscount)-SUM(searchcount)) as differ,sum(goodscount) as goodscounts,sum(searchcount) as searchcounts FROM tyt_goods_search_count WHERE cdate>=:startDate and cdate<=:endDate ");
		if (!"".equals(city) && !"allCity".equals(city) && city != null) {
			sb.append(" and city = :city");
			paramMap.put("city", city);
		}
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("goodsCounts", Hibernate.INTEGER);
		map.put("searchCounts", Hibernate.INTEGER);
		map.put("differ", Hibernate.INTEGER);
		// map.put("city", Hibernate.STRING);

		paramMap.put("startDate", startDate);
		paramMap.put("endDate", endDate);
		List<TytGoodsSearch> one = this.getBaseDao().searchByName(sb.toString(), map, TytGoodsSearch.class, paramMap);

		int days = TimeUtil.getDays(startDate, endDate);
		String newStart = TimeUtil.addDayStr(timeText, -days);
		paramMap.put("startDate", newStart);
		paramMap.put("endDate", timeText);
		List<TytGoodsSearch> two = this.getBaseDao().searchByName(sb.toString(), map, TytGoodsSearch.class, paramMap);

		if ((one != null && one.size() > 0) || (two != null && two.size() > 0)) {
			resultMap = ChartUtil.parserMsColumn2dByTime(one, two, startDate + "至" + endDate, newStart + "至" + timeText);
		}
		return resultMap;
	}

	/**
	 * 获取一段时间内用户发货量和搜索人数:按市对比
	 * 
	 * @param status
	 *            货源状态
	 * @param selectedCities
	 *            选择的市
	 * @param startDate
	 *            发货日期-开始
	 * @param endDate
	 *            发货日期-结束
	 * @return 多柱状图表
	 */
	public Map<String, String> getCarGoodsSearchMapByCity(String status, String startDate, String endDate, String selectedCities) throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT abs(sum(goodscount)-SUM(searchcount)) as differ,sum(goodscount) as goodscounts,sum(searchcount) as searchcounts,city FROM tyt_goods_search_count WHERE cdate>=:startDate and cdate<=:endDate");

		if (!"".equals(selectedCities) && selectedCities != null) {
			String[] arr = selectedCities.split(",");
			sb.append(" and city in(");
			for (String str : arr) {
				sb.append("'" + str + "',");
			}
			sb.delete(sb.length() - 1, sb.length());
			sb.append(")");
		}
		sb.append(" GROUP BY city ;");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("goodsCounts", Hibernate.INTEGER);
		map.put("searchCounts", Hibernate.INTEGER);
		map.put("differ", Hibernate.INTEGER);
		map.put("city", Hibernate.STRING);

		paramMap.put("startDate", startDate);
		paramMap.put("endDate", endDate);
		List<TytGoodsSearch> list = this.getBaseDao().searchByName(sb.toString(), map, TytGoodsSearch.class, paramMap);
		String[] arr = selectedCities.split(",");
		int len = arr.length;
		for (int i = 0; i < len; i++) {
			boolean flag = true;
			for (TytGoodsSearch tytGoodsSearch : list) {
				if (arr[i].equals(tytGoodsSearch.getCity())) {
					// 字符串包含
					flag = false;
				}
			}
			if (flag) {
				TytGoodsSearch tyt = new TytGoodsSearch();
				tyt.setCity(arr[i]);
				list.add(tyt);
			}
		}

		if (list != null && list.size() > 0) {
			resultMap = ChartUtil.parserMsColumn2d(list);
		}
		return resultMap;
	}
}
