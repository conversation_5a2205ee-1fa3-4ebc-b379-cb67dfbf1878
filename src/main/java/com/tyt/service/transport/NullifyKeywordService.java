package com.tyt.service.transport;

import java.util.List;

import com.tyt.model.NullifyKeywordBean;
import com.tyt.service.base.BaseService;

/**
 * User: Administrator Date: 17-04-24 Time: 下午3:16
 */
public interface NullifyKeywordService extends
		BaseService<NullifyKeywordBean, Long> {

	/**
	 * 获取NullifyKeywordBean
	 * @param keywordType
	 * @param keywordValue
	 * @return
	 * @throws Exception
	 */
	public NullifyKeywordBean getNullifyKeywordBean(Integer keywordType,
			String keywordValue) throws Exception;

	/**
	 * 添加NullifyKeywordBean
	 * @param keywordType
	 * @param keywordValue
	 * @return
	 * @throws Exception
	 */
	public void addNullifyKeywordBean(Integer keywordType, String keywordValue)
			throws Exception;

	/**
	 * 修改NullifyKeywordBean
	 * @param keywordId
	 * @param keywordValue
	 * @return
	 * @throws Exception
	 */
	public boolean updateNullifyKeywordBean(Long keywordId, String keywordValue)
			throws Exception;
	/**
	 * 删除关键词
	 * @param keywordId
	 * @return
	 * @throws Exception
	 */
	public boolean deleteNullifyKeywordBean(String keywordIds)
			throws Exception;
}
