package com.tyt.service.transport;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.TytCallPhoneLimitNew;
import com.tyt.model.TytSttLimit;
import com.tyt.service.base.BaseServiceImpl;


@Service("tytCallPhoneLimitNewService")
public class TytCallPhoneLimitNewServiceImpl extends BaseServiceImpl<TytCallPhoneLimitNew, Integer> implements TytCallPhoneLimitNewService {

	@Resource(name = "tytCallPhoneLimitNewDao")
	public void setBaseDao(BaseDao<TytCallPhoneLimitNew, Integer> tytCallPhoneLimitNewDao) {
		super.setBaseDao(tytCallPhoneLimitNewDao);
	}

	@Override
	public void updateGoodsNumberJuriadiction(TytCallPhoneLimitNew bean, EmployeeQueryBean employeeBean) {
		if(bean.getId()!=null && bean.getId()>0) {
		//修改操作
			TytCallPhoneLimitNew updateBean = this.getById(bean.getId());
			updateBean.setCarAuthNum(bean.getCarAuthNum());
			updateBean.setIdentityAuthNum(bean.getIdentityAuthNum());
			updateBean.setVipNum(bean.getVipNum());
			updateBean.setUtime(new Date());
			updateBean.setVerifyLevelOne(bean.getVerifyLevelOne());
			if(employeeBean!=null) {
				updateBean.setOperatorId(employeeBean.getId());
				updateBean.setOperatorName(employeeBean.getUserName());
			}
			this.update(updateBean);
		}else {
			bean.setCtime(new Date());
			bean.setUtime(new Date());
			bean.setSTATUS(1);
			if(employeeBean!=null) {
				bean.setOperatorId(employeeBean.getId());
				bean.setOperatorName(employeeBean.getUserName());
			}
			this.add(bean);
		}
		
	}

	@Override
	public void deleteGoodsNumberJuriadiction(Long id) {
		String sql =" UPDATE tyt_call_phone_limit_new SET STATUS=? WHERE id =? ";
		this.executeUpdateSql(sql, new Object[]{3,id});
		
	}

	@Override
	public List<TytCallPhoneLimitNew> getList() {
		String hql="from TytCallPhoneLimitNew n WHERE n.STATUS=? ORDER BY id ";
		List<TytCallPhoneLimitNew> list=this.getBaseDao().find(hql, 1);
		return list;
	}

	@Override
	public Boolean checkIdentity(TytCallPhoneLimitNew bean) {
		String sql="";
		List<TytCallPhoneLimitNew> list=null;
		if(bean.getId()!=null && bean.getId()>0) {
			sql="SELECT * FROM tyt_call_phone_limit_new n WHERE n.verify_level_one=? AND id!= ? AND n.STATUS=? ";
			list=this.getBaseDao().queryForList(sql, new Object[] {bean.getVerifyLevelOne(),bean.getId(),1});
		}else {
			sql="SELECT * FROM tyt_call_phone_limit_new n WHERE n.verify_level_one=? AND n.STATUS=? ";
			list=this.getBaseDao().queryForList(sql, new Object[] {bean.getVerifyLevelOne(),1});
		}
		if(list!=null && list.size()>0) {
			return false;
		}
		return true;
	}
}
