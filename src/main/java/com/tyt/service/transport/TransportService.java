package com.tyt.service.transport;

import com.tyt.model.PageBean;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.model.TytTransportTecServiceFee;
import com.tyt.service.base.BaseService;
import com.tyt.web.qbean.InfoStatBean;

import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;

/**
 * User: Administrator Date: 13-11-10 Time: 下午4:25
 */
public interface TransportService extends BaseService<Transport, Long> {

    public List<Long> getInfoIdList(String clause);

    /**
     * 更新信息状态
     *
     * @param status
     * @param id
     * @param display
     * @throws Exception
     */
    public void updateStatus(Integer status, Long id, Integer display) throws Exception;

    /**
     * 统计运输信息的自动和人工有效成交数量
     *
     * @param sql
     * @return
     */
    public List<InfoStatBean> getHistoryCounts(String sql, PageBean pageBean);

    public HSSFWorkbook exportInfoHistoryStat(List<InfoStatBean> list);

    public HSSFWorkbook export(List<TransportMain> list);

    public boolean isExitHashCode(String hashCode) throws Exception;

    /**
     * 生成CVS内容
     *
     * @param infoStats
     * @return
     */
    public void makeCvsContent(StringBuffer content, InfoStatBean stat);

    /**
     * 查询货源详情
     *
     * @param goodsId :货物ID
     * @return
     * @throws Exception
     */
    public Transport getDetail(Long goodsId) throws Exception;

    public List<Long> getSrcmsgIdsForId(Long[] ids);

    public Long getMaxIdbytransId(Long id);

    /**
     * 查询货源详情
     *
     * @param srcMsgId :货物ID
     * @return
     * @throws Exception
     */
    public Transport getBySrcId(Long srcMsgId) throws Exception;

    /**
     * 通过srcMsgId 查询transport列表
     *
     * @param ids
     * @return
     */
    public List<Transport> getListBySrcMsgIds(Long[] ids);

    /**
     * 查询抽佣信息
     */
    TytTransportTecServiceFee queryTecServiceFeeBySrcMsgId(Long srcMsgId);
}
