package com.tyt.service.transport;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.dao.transport.TransportCollectDao;
import com.tyt.model.PageBean;
import com.tyt.model.TransportCollect;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.TimeUtil;
import com.tyt.web.qbean.CarQueryBean;

@Service("transportCollectService")
public class TransportCollectServiceImpl extends BaseServiceImpl<TransportCollect, Long>
		implements TransportCollectService {

	@Resource(name="transportCollectDao")
    public void setBaseDao(BaseDao<TransportCollect, Long> transportCollectDao) {
        super.setBaseDao(transportCollectDao);
    }

	@Override
	public List<Long> getInfoIdListClient(String sql) {
		return ((TransportCollectDao)(this.getBaseDao())).getInfoIdListClient(sql);
	}
	
	@Override
	public List<Long> getInfoIdList(String cellPhone,PageBean page) {
//		List<Object> listObject = new ArrayList<Object>();
//
//		StringBuffer sbCount = new StringBuffer(
//				"SELECT count(*) FROM tyt_transport_collect c WHERE 1=1 ");

		StringBuffer sb = new StringBuffer();


		if(!StringUtils.isBlank(cellPhone)){
			sb.append(" entity.cellPhone='"+cellPhone+"'");
//			listObject.add(cellPhone);
			sb.append(" and entity.status=1");
//			listObject.add(1);
			sb.append(" and entity.ctime>='"+TimeUtil.formatDate(TimeUtil.dateDiff(-6))+"'");
//			listObject.add(TimeUtil.formatDate(TimeUtil.dateDiff(-6)));
//			sbCount.append(sb);
		}
		List<TransportCollect> collectList=this.getList(sb.toString(), page);
		List<Long> idList = new ArrayList<Long>();
		if(collectList!=null)
			for(TransportCollect collect:collectList){
			idList.add(collect.getInfoId());
		}

//			BigInteger rowCount = this.getBaseDao().query(sbCount.toString(),
//					listObject.toArray());
//			if (rowCount != null && rowCount.longValue() > 0) {
//				StringBuffer sbSql = new StringBuffer(
//						"SELECT c.info_id  FROM tyt_transport_collect c where 1=1 ");
//								
//				Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
//
////				map.put("infoId", Hibernate.LONG);
//
//				page.setRowCount(rowCount.longValue());
//				sbSql.append(sb);
//				sbSql.append(" order by c.id desc ");
//				
//				List<BigInteger> list = this.getBaseDao().search(
//						sbSql.toString(), map, BigInteger.class,
//						listObject.toArray(), page.getCurrentPage(),
//						page.getPageSize());
//				List<Long> idList=new ArrayList<Long>();
//				if(idList!=null)
//					for(BigInteger id:list){
//                	idList.add(id.longValue());
//                }
//				return idList.size()>0?idList:null;
//			}
//
//		}

		
		return idList.size()>=0?idList:null;
	}
	


	@Override
	public void delCollect(String cellPhone, Long infoId) {
		((TransportCollectDao)(this.getBaseDao())).delCollect(cellPhone, infoId);
		
	}

	@Override
	public void updateId(Long oldId, Long newId) {
		((TransportCollectDao)(this.getBaseDao())).updateId(oldId,newId);       		
	}

	@Override
	public List<TransportCollect> getIdList(String sql, PageBean pageBean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void updateStatus(Long infoId, Integer status) {
		((TransportCollectDao)(this.getBaseDao())).updateStatus(infoId, status);
	}

	@Override
	public boolean updateStatusByIds(List<Long> idList,Integer status) {
		try {
			for(Long id:idList){
				this.updateStatus(id, status);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
	}

}
