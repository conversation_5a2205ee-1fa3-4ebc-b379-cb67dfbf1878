package com.tyt.returnMoney.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/7/27 13:42
 * @Version 1.0
 **/
@Data
public class MerchantCashBackReqVO  {


    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 商户申请代发订单号
     */
    private String merchantTransferOrderNo;

    /**
     * 商户用户ID
     */
    private String userId;

    /**
     * 安全的用户ID，与user_id可逆
     */
    private String safeUserId;

    /**
     * 付款方类型,用户：USER 平台商户：MERCHANT
     */
    private String userType;

    /**
     * 用户账户类型，USEROWN 用户账户,MCHOWN 平台商户自有资金账户,MCHASSURE 平台商户担保账户,MCHCOUPON 平台商户优惠券账户,MCHFEE 平台商户手续费账户
     */
    private String userAccttype;

    /**
     * 银行帐号
     */
    private String bankAcctno;

    /**
     * 银行编码，收款方类型为对公银行账户必须purpose
     */
    private String bankCode;

    /**
     * 户名
     */
    private String bankAcctname;

    /**
     * 银行大额行号，收款方类型为对公银行账户必须
     */
    private String cnapsCode;

    /**
     * 垫资标识，标识该笔代发交易是否支持平台商户垫资，适用于代发付款方为用户的业务场景，默认：N,Y：支持垫资,N：不支持垫资
     */
    private String fundsFlag;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 商品名称或主题
     */
    private String subject;

    /**
     * 订单信息
     */
    private String orderInfo;

    /**
     * 交易附言
     */
    private String postScript;

    /**
     * 付款方类型，用户:USER 平台商户:MERCHANT
     */
    private String payerType;

    /**
     * 付款方标识,付款方为用户时设置 user_id 付款方为商户时设置平台商户号
     */
    private String payerId;

    /**
     * 付款方安全标识,与payerId可逆
     */
    private String safePayerId;

    /**
     * 付款方账户类型,付款方类型为商户时需要指定平台商户类型
     */
    private String payerAccttype;

    /**
     * 支付密码，用户提现时必须
     */
    private String password;

    /**
     * 密码随机因子 key,随机因子获 取接口返回，用户提现时必须
     */
    private String randomKey;

    /**
     * 账户加返回的代扣协议号，委托代发时必输，该字段需要 RSA 加密传输
     */
    private String papAgreeNo;

    /**
     * 商户私有域
     */
    private String merchantPriv;

    /**
     * 订单有效期，逾期将会关闭交易，单位：分钟，默认三天
     */
    private String payExpire;

    /**
     * 标识该笔订单是否需要审核，默认N Y:需要提现确认
     */
    private String checkFlag;

    /**
     * 到账类型 默认:实时到账 到账类型：TRANS_THIS_TIME - 实时到账； TRANS_NORMAL - 普通到账(2 小时内)； TRANS_NEXT_TIME - 次日到账
     */
    private String payTimeType;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 手续费金额
     * 手续费金额，单位:元。精确到小数点后两位，会自动收取到商户的自有资金账户，
     * 不允许超过订单总金额的 20%
     */
    private BigDecimal fee;

    /**
     * 代发交易用途：服务费，信息费，修理费，佣金支付，贷款支付，其他
     */
    private String purpose;


    /**
     * 异步通知地址
     */
    //ApiModelProperty("异步通知地址")
    private String notifyUrl;

    /**
     * ip地址
     */
    private String ipAddr;

    //"代付订单类型"
    private String orderType;

    private static final long serialVersionUID = 1L;
}
