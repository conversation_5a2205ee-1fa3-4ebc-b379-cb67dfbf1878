package com.tyt.pcupgrade.enums;

/**
 * PC升级状态
 */
public enum UpgradeTaskStatusEnums {
    NOTSTARTED(0, "任务未开始"),
    UNDERWAY(1, "进行中"),
    FINISHED(2, "已结束"),
    TERMINATED(3, "已终止");

    private Integer status;

    private String msg;

    UpgradeTaskStatusEnums(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }
}
