package com.tyt.pcupgrade.enums;

public enum SpecificUpgradeEnum {
    DIRECTIONAL_UPGRADE(0,"定向升级"),
    UNDIRECTED_UPGRADE(1,"非定向升级");

    private Integer code;

    private String msg;

    SpecificUpgradeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
