package com.tyt.pcupgrade.service.impl;

import com.github.pagehelper.PageHelper;
import com.tyt.mybatis.mapper.PcUpgradeRecordMapper;
import com.tyt.pcupgrade.dto.PcUserUpgradeRecordDto;
import com.tyt.pcupgrade.service.PcUpgradeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PcUpgradeRecordServiceImpl implements PcUpgradeRecordService {
    @Autowired
    private PcUpgradeRecordMapper pcUpgradeRecordMapper;

    @Override
    public List<PcUserUpgradeRecordDto> getUserUpgradeRecordByTaskId(Integer taskId,Integer upgradeStatus, String cellPhone, Integer pageSize,Integer pageNum) {
        PageHelper.startPage(pageNum,pageSize);
        return pcUpgradeRecordMapper.selectByUpgradeTaskId(taskId,upgradeStatus,cellPhone);
    }
}
