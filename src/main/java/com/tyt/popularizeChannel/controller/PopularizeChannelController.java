package com.tyt.popularizeChannel.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.PopularizeChannel;
import com.tyt.model.ResultMsgBean;
import com.tyt.popularizeChannel.controller.bean.PopularizeChannelBean;
import com.tyt.popularizeChannel.service.PopularizeChannelService;
import com.tyt.service.cache.CacheService;
import com.tyt.util.Constant;
import com.tyt.util.CsvWriter;
import com.tyt.util.TytSourceUtil;
import com.tyt.web.base.BaseController;

@Controller
@RequestMapping("/appChannel")
public class PopularizeChannelController extends BaseController{
	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	@Resource(name = "popuChannelService")
	private PopularizeChannelService popuChannelService;
	
	/**
	 * 查询app渠道列表
	 * @param request
	 * @param pageNo
	 * @param pageSize
	 * @param oneId
	 * @param twoId
	 * @return
	 */
	@RequestMapping("/appChannelList")
	public String getMessage(HttpServletRequest request,Integer pageNo, Integer pageSize, Long oneId, Long twoId) {
		// 登陆验证
		EmployeeQueryBean curUser = getCurrentUser(request);
		try {
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			// 分页处理
			if (pageNo == null || pageNo.intValue() <= 0)
				pageNo = 1;
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(pageNo);
			if (pageSize == null || pageSize.intValue() <= 0)
				pageSize = Constant.DEFAULT_PAGE_SIZE;
			pageBean.setPageSize(pageSize);
			List<PopularizeChannelBean> list = popuChannelService.getList(oneId, twoId, pageBean);
			request.setAttribute("pageNo", pageNo);
			request.setAttribute("pageSize", pageSize);
			request.setAttribute("maxPage", pageBean.getMaxPage());
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("oneId", oneId);
			request.setAttribute("twoId", twoId);
			request.setAttribute("list", list);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/popuChannel/app_channel";
	}
	
	/**
	 * 一级渠道列表查询
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/levelOneList")
	public String getLevelOneList(HttpServletRequest request){
		ResultMsgBean msg=new ResultMsgBean();
		EmployeeQueryBean curUser = getCurrentUser(request);
		try {
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			List<PopularizeChannel> oneList = popuChannelService.getLevelOneList();
			msg.setData(oneList);
			request.setAttribute("oneList", oneList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/popuChannel/levelOne_channel";
	}
	
	/**
	 * 二级渠道列表查询
	 * @param request
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value="/levelTwoList")
	public String getLevelTwoList(HttpServletRequest request, Integer pageNo, Integer pageSize){
		// 登陆验证
		EmployeeQueryBean curUser = getCurrentUser(request);
		try {
			if (curUser == null) {
				request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
				return "back/jsp/admin_login";
			}
			// 分页处理
			if (pageNo == null || pageNo.intValue() <= 0)
				pageNo = 1;
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(pageNo);
			if (pageSize == null || pageSize.intValue() <= 0)
				pageSize = Constant.DEFAULT_PAGE_SIZE;
			pageBean.setPageSize(pageSize);
			List<PopularizeChannelBean> twoList = popuChannelService.getLevelTwoList(pageBean);
			List<PopularizeChannel> oneList=popuChannelService.getLevelOneList();
			request.setAttribute("pageNo", pageNo);
			request.setAttribute("pageSize", pageSize);
			request.setAttribute("maxPage", pageBean.getMaxPage());
			request.setAttribute("rowCount", pageBean.getRowCount());
			request.setAttribute("twoList", twoList);
			request.setAttribute("oneList", oneList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "back/jsp/popuChannel/levelTwo_channel";
	}
	/**
	 * 下载渠道模板
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/excelExportModel", method = RequestMethod.POST)
	public void excelExportModel(HttpServletRequest request, HttpServletResponse response) {
		EmployeeQueryBean curUser = getCurrentUser(request);
		if (curUser==null) {
			request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
			return ;
		}
		try {
			String header = "二级渠道(ID),三级渠道" + "\r\n";
			CsvWriter.exportCsv("channel_", header.toString(), response);
			header = null;
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 导出
	 * @param oneId
	 * @param twoId
	 * @param request
	 * @param response
	 */
	@RequestMapping(value="/exportList",method=RequestMethod.POST)
	public void exportList(Long oneId, Long twoId, HttpServletRequest request, HttpServletResponse response){
		EmployeeQueryBean curUser = getCurrentUser(request);
		if (curUser==null) {
			request.setAttribute("msg", Constant.MSG_NOT_LOGIN);
			return ;
		}
		try {
			PageBean pageBean = new PageBean();
			pageBean.setCurrentPage(1);
			pageBean.setPageSize(Integer.MAX_VALUE);
			List<PopularizeChannelBean> list = popuChannelService.getList(oneId, twoId, pageBean);
			StringBuffer csvStr = popuChannelService.getStringCsv(list);
			CsvWriter.exportCsv("APP渠道管理表_", csvStr.toString(), response);
			list.clear();
			csvStr = null;
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 批量导入
	 * @param request
	 * @param response
	 * @param fileField
	 */
	@RequestMapping(value="/importList",method=RequestMethod.POST)
	public void importList(HttpServletRequest request, HttpServletResponse response,@RequestParam(value="fileField",required = true) MultipartFile fileField){
		EmployeeQueryBean curUser = getCurrentUser(request);
		ResultMsgBean msgBean=new ResultMsgBean();
		if (curUser==null) {
			msgBean.setCode(ResultMsgBean.ERROR);
			msgBean.setMsg("导入失败,该用户没有登录");
			printJSON(request, response, msgBean);
			return;
		}
		try {
			msgBean = popuChannelService.saveExcel(fileField,curUser);
		} catch (Exception e) {
			msgBean.setCode(ResultMsgBean.ERROR);
			msgBean.setMsg("导入失败,请重新导入");
			e.printStackTrace();
		}
		printJSON(request, response, msgBean);
		TytSourceUtil.init();
		cacheService.del(Constant.CACHE_TYT_SOURCE_PARENT_SUB_KEY_);
		return;
	}
	/**
	 * 保存渠道
	 * @param request
	 * @param id
	 * @param pid
	 * @param name
	 * @return
	 */
	@RequestMapping(value="/saveChannel")
	@ResponseBody
	public ResultMsgBean saveChannel(HttpServletRequest request,Long id,
			@RequestParam(value="pid",required = true) Long pid,
			@RequestParam(value="name",required = true) String name){
		ResultMsgBean result=new ResultMsgBean(200,"保存成功");
		try {
			EmployeeQueryBean curUser = getCurrentUser(request);
			if (curUser==null) {
				result.setCode(201);
				result.setMsg("用户未登录，请登陆后重试");
				return result;
			}
			Boolean saveChannel = popuChannelService.saveChannel(id,pid,name,curUser);
			if (!saveChannel) {
				result.setCode(202);
				result.setMsg("重复请核对");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
	}
	/**
	 * 二级联动查询
	 * @param pid //父级id
	 * @return
	 */
	@RequestMapping(value="/getByPid")
	@ResponseBody
	public ResultMsgBean getByPid(@RequestParam(value="pid",required = true)Long pid){
		ResultMsgBean result=new ResultMsgBean(200,"查询成功");
		try {
			List<PopularizeChannel> byPid = popuChannelService.getByPid(pid);
			result.setData(byPid);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
	}
	
	@RequestMapping(value="/getById")
	@ResponseBody
	public ResultMsgBean getById(@RequestParam(value="id",required = true)Long id){
		ResultMsgBean result=new ResultMsgBean(200,"查询成功");
		try {
			PopularizeChannel byId = popuChannelService.getChannelById(id);
			result.setData(byId);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
	}
}
