package com.tyt.transportnews.service;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytOwnerUserWhitelist;

import java.util.List;

public interface OwnerUserWhitelistService {

    /**
     * 列表
     * @param pageSize
     * @param pageNum
     * @param cellPhone
     * @return
     */
    List<TytOwnerUserWhitelist> getList(Integer pageSize,Integer pageNum,String cellPhone);

    /**
     * 添加
     * @param cellPhone
     * @param curUser
     */
    void addUserWhitelist(String cellPhone, EmployeeQueryBean curUser) throws Exception;

    /**
     * 删除
     * @param id
     * @param curUser
     */
    void updateUserWhitelist(Long id, EmployeeQueryBean curUser);

    TytOwnerUserWhitelist getWhitelistList(String cellPhone);
}
