package com.tyt.transportnews.service;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytExtendGoodsWhitelist;

import java.util.List;

public interface ExtendGoodsWhitelistService {
    /**
     * 根据id获取白名单信息
     * @param id
     * @return
     */
    TytExtendGoodsWhitelist getById(Long id);

    /**
     * 根据货站id查询
     * @param goodsStationId
     * @return
     */
    TytExtendGoodsWhitelist getByGoodsStationId(Long goodsStationId);

    /**
     * 获取白名单列表数据
     * @param pageSize
     * @param pageNum
     * @param goodsStationId
     * @return
     */
    List<TytExtendGoodsWhitelist> getList(Integer pageSize,Integer pageNum,Long goodsStationId);

    /**
     * 删除白名单信息，包含删除缓存
     * @param extendGoodsWhitelist
     */
    void delete(TytExtendGoodsWhitelist extendGoodsWhitelist);

    /**
     * 添加白名单
     * @param tytExtendGoodsWhitelist
     */
    void addGoodsWhitelist(TytExtendGoodsWhitelist tytExtendGoodsWhitelist);

    /**
     * 从新加载缓存
     * 该接口干两件事
     * 1、如果数据库有的值缓存中没有，则将数据库的值新增至缓存
     * 2、如果缓存中有的值数据库没有，则将该值从缓存中删除
     */
    void reloadGoodsWhiteCache();

    /**
     * 启用/禁用
     * @param id
     * @param status 0 启用 1禁用
     * @return
     */
    void updateGoodsWhiteStatus(Long id,Integer status,Long goodsStationId);
    /**
     * 根据启用/禁用状态统计数量
     * @param status
     * @return
     */
    Integer countByStatus(Integer status);

    /**
     * 更改展示名称
     * @param id id
     * @param showName 名称
     */
    void updateGoodsWhiteName(Long id, String showName, EmployeeQueryBean employee);
}
