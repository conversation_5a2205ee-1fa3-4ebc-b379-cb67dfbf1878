package com.tyt.transportnews.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.tyt.model.HpTransportNews;
import com.tyt.service.base.BaseService;

/**
 * 
 * <AUTHOR>
 * @date 2017年10月11日上午10:54:17
 * @description
 */
public interface HpTransportNewsService extends BaseService<HpTransportNews, Long> {

	void deleteTransportNews(Long id);

	void updateOpenClose(Long id, int openClose);

	List<HpTransportNews> getTransportNewsList(Integer pageNo, HttpServletRequest request);
}
