package com.tyt.transportnews.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;

/**
 * 货站接单mq push+短信
 */
public class TransportNewsMessageBean extends MqBaseMessageBean {

    /**
     * 操作方式 1：发布货源，等待货站签单; 2:货源下架
     */
    private Integer opStatus;
    /**
     * 接单人id
     */
    private Long userId;
    /**
     *出发地(省市区以减号-分割开)
     */
    private String startPoint;
    /**
     *目的地(省市区以减号-分割开)
     */
    private String destPoint;
    /**
     *  货物内容
     */
    private String taskContent;
    /**
     * 接单人手机号
     */
    private String cellPhone;
    /**
     * 真实姓名
     */
    private String trueName;
    /**
     * backend表id
     */
    private Long backendId;
    /**
     *  后台维护人id
     */
    private Long employeeUserId;

    public Integer getOpStatus() {
        return opStatus;
    }

    public void setOpStatus(Integer opStatus) {
        this.opStatus = opStatus;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getTrueName() {
        return trueName;
    }

    public void setTrueName(String trueName) {
        this.trueName = trueName;
    }

    public Long getBackendId() {
        return backendId;
    }

    public void setBackendId(Long backendId) {
        this.backendId = backendId;
    }

    public Long getEmployeeUserId() {
        return employeeUserId;
    }

    public void setEmployeeUserId(Long employeeUserId) {
        this.employeeUserId = employeeUserId;
    }

}
