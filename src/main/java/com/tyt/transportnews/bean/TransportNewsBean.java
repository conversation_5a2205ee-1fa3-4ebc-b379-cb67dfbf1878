package com.tyt.transportnews.bean;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017年10月13日上午9:21:31
 * @description
 */
public class TransportNewsBean {
	private Long id;
	private String newsTitle;
	private String openUrl;
	private String source;
	private String publishTimeStr;
	private String pictureUrlOld;
	public Long getId() {
		return id;
	}

	public String getPictureUrlOld() {
		return pictureUrlOld;
	}

	public void setPictureUrlOld(String pictureUrlOld) {
		this.pictureUrlOld = pictureUrlOld;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getNewsTitle() {
		return newsTitle;
	}

	public void setNewsTitle(String newsTitle) {
		this.newsTitle = newsTitle;
	}

	public String getOpenUrl() {
		return openUrl;
	}

	public void setOpenUrl(String openUrl) {
		this.openUrl = openUrl;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getPublishTimeStr() {
		return publishTimeStr;
	}

	public void setPublishTimeStr(String publishTimeStr) {
		this.publishTimeStr = publishTimeStr;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
