package com.tyt.transportnews.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/17 15:21
 * 订单承运车主vo
 */
@Data
public class TransportBackendCarVO implements Serializable {
    /**
     *车主id
     */
    private Long userId;
    /**
     *车辆id
     */
    private Long carId;
    /**
     *车主展示昵称
     */
    private String userShowName;
    /**
     *车主电话
     */
    private String cellPhone;
    /**
     *车头城市
     */
    private String headCity;
    /**
     *车头编号
     */
    private String headNo;
    /**
     *挂车城市
     */
    private String tailCity;
    /**
     *挂车编号
     */
    private String tailNo;
    /**
     * 平台交易量
     */
    private Integer tradeNums;
    /**
     * 显示货源是否成交车标识 0不是 1是
     */
    private Integer isDealCar;
    /**
     * 派车时间
     */
    private Date dealCarTime;


    @Override
    public String toString() {
        return "TransportBackendCarVO{" +
                "userId=" + userId +
                ", carId=" + carId +
                ", userShowName='" + userShowName + '\'' +
                ", cellPhone='" + cellPhone + '\'' +
                ", headCity='" + headCity + '\'' +
                ", headNo='" + headNo + '\'' +
                ", tailCity='" + tailCity + '\'' +
                ", tailNo='" + tailNo + '\'' +
                ", tradeNums=" + tradeNums +
                ", isDealCar=" + isDealCar +
                '}';
    }
}
