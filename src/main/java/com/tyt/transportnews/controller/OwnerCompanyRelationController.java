package com.tyt.transportnews.controller;

import com.github.pagehelper.PageInfo;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.OwnerCompanyRelation;
import com.tyt.manager.entity.base.TytExtendGoodsWhitelist;
import com.tyt.manager.entity.base.TytWxUserInfo;
import com.tyt.manager.service.base.TytWxUserInfoService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.mybatis.mapper.model.QueryResultModel;
import com.tyt.transportnews.bean.CompanyRelationDTO;
import com.tyt.transportnews.bean.ExtendGoodsWhitelistDTO;
import com.tyt.transportnews.service.ExtendGoodsWhitelistService;
import com.tyt.transportnews.service.OwnerCompanyRelationService;
import com.tyt.web.base.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@RequestMapping(value = "company/relation")
@RestController
public class OwnerCompanyRelationController extends BaseController {
    @Autowired
    private OwnerCompanyRelationService ownerCompanyRelationService;
    @Autowired
    private ExtendGoodsWhitelistService extendGoodsWhitelistService;

    @Autowired
    private TytWxUserInfoService tytWxUserInfoService;

    /**
     * 获取绑定关系列表数据
     * @param companyRelationDTO 查询条件
     * @param pageSize           分页最大页数
     * @param current            分页当前页数
     * @return
     */
    @RequestMapping(value = "get/list")
    public ResultMsgBean getList(CompanyRelationDTO companyRelationDTO,
                                 @RequestParam(value = "pageSize", defaultValue = "20", required = false) Integer pageSize,
                                 @RequestParam(value = "current", defaultValue = "1", required = false) Integer current) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        List<OwnerCompanyRelation> relationServiceList = ownerCompanyRelationService.getList(companyRelationDTO, current, pageSize);
        if (relationServiceList!=null) {
            relationServiceList.stream().forEach(o -> {
                o.setUserPhone("");
            });
        }
        PageInfo<OwnerCompanyRelation> pageInfo = new PageInfo<>(relationServiceList);
        QueryResultModel<OwnerCompanyRelation> queryReuslt = new QueryResultModel<>();
        queryReuslt.setTotal(pageInfo.getTotal());
        queryReuslt.setList(relationServiceList);
        resultMsgBean.setCode(ReturnCodeConstant.OK);
        resultMsgBean.setMsg("查询成功");
        resultMsgBean.setData(queryReuslt);
        return resultMsgBean;
    }

    /**
     * 绑定货站
     * @param extendGoodsWhitelistDTO
     * @return
     */
    @RequestMapping(value = "bind/goods/station")
    public ResultMsgBean bindGoodsStation(ExtendGoodsWhitelistDTO extendGoodsWhitelistDTO, HttpServletRequest request) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        if (Objects.isNull(extendGoodsWhitelistDTO) ||
                Objects.isNull(extendGoodsWhitelistDTO.getGoodsStationId()) ||
                StringUtils.isEmpty(extendGoodsWhitelistDTO.getGoodsStationName()) ||
                Objects.isNull(extendGoodsWhitelistDTO.getId())) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("缺少必要参数");
            return resultMsgBean;
        }
        TytExtendGoodsWhitelist byGoodsStationId = extendGoodsWhitelistService.getByGoodsStationId(extendGoodsWhitelistDTO.getGoodsStationId());
        if (Objects.isNull(byGoodsStationId)) {
            resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            resultMsgBean.setMsg("输入正确货站ID");
            return resultMsgBean;
        }
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
        OwnerCompanyRelation ownerCompanyRelation = new OwnerCompanyRelation();
        ownerCompanyRelation.setCompanyId(extendGoodsWhitelistDTO.getGoodsStationId());
        ownerCompanyRelation.setGoodsStationName(extendGoodsWhitelistDTO.getGoodsStationName());
        ownerCompanyRelation.setUpdateUserId(curUser.getId());
        ownerCompanyRelation.setUpdateUserName(curUser.getUserName());
        ownerCompanyRelation.setId(extendGoodsWhitelistDTO.getId());
        ownerCompanyRelationService.bindGoodsStation(ownerCompanyRelation);
        return resultMsgBean;
    }


    /**
     * 根据手机号查询推广人信息
     * @param phone
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWxUser")
    public ResultMsgBean getWxUser(String phone, HttpServletRequest request) {
        logger.info("查询手机号：{}",phone);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            TytWxUserInfo tytWxUserInfo = tytWxUserInfoService.getByWxPhone(phone);
            if(Objects.isNull(tytWxUserInfo)){
                return new ResultMsgBean(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "当前手机号暂未注册小程序，无法绑定");
            }
            resultMsgBean.setData(tytWxUserInfo);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "操作失败");
        }
        return resultMsgBean;
    }




    @RequestMapping(value = "/bind")
    public ResultMsgBean getWxUser(String cellPhone, Long id,Long userId,HttpServletRequest request) {
        logger.info("手机号：{},绑定id{}，用户id{}",cellPhone,id,userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
           /* OwnerCompanyRelation relation = ownerCompanyRelationService.getById(id);
            if(Objects.isNull(relation)){
                return new ResultMsgBean(ReturnCodeConstant.ERROR, "未查询到该绑定配置");
            }
            relation.setRecommendUserId(userId);
            relation.setRecommendUserPhone(cellPhone);


            relation.setUpdateUserId(curUser.getId());
            relation.setUpdateUserName(curUser.getUserName());
            ownerCompanyRelationService.updateById(relation);*/
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
            OwnerCompanyRelation relation = new OwnerCompanyRelation();
            relation.setRecommendUserId(userId);
            relation.setRecommendUserPhone(cellPhone);
            relation.setUpdateUserId(curUser.getId());
            relation.setUpdateUserName(curUser.getUserName());
            relation.setId(id);
            ownerCompanyRelationService.updateById(relation);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "操作失败");
        }
        return resultMsgBean;
    }
}
