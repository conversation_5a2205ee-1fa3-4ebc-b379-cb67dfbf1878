package com.tyt.transportnews.controller;

import com.github.pagehelper.PageInfo;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytExtendGoodsWhitelist;
import com.tyt.manager.entity.base.TytOwnerUserWhitelist;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.mybatis.mapper.model.QueryResultModel;
import com.tyt.transportnews.service.OwnerUserWhitelistService;
import com.tyt.web.base.BaseController;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping(value = "owner/user/whitelist")
public class OwnerUserWhitelistController extends BaseController {

    @Autowired
    private OwnerUserWhitelistService ownerUserWhitelistService;

    /**
     * 下单用户白名单列表
     * @param pageSize
     * @param pageNum
     * @param cellPhone
     * @return
     */
    @RequestMapping(value = "/get/list")
    public ResultMsgBean getList(@RequestParam(value = "pageSize", defaultValue = "30", required = false) Integer pageSize,
                                 @RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum, String cellPhone){
        ResultMsgBean rm = new ResultMsgBean();
        try {
            List<TytOwnerUserWhitelist> ownerUserWhitelists = ownerUserWhitelistService.getList(pageSize,pageNum,cellPhone);
            PageInfo<TytOwnerUserWhitelist> pageInfo = new PageInfo<>(ownerUserWhitelists);
            QueryResultModel<TytOwnerUserWhitelist> queryReuslt = new QueryResultModel<>();
            queryReuslt.setTotal(pageInfo.getTotal());
            queryReuslt.setList(ownerUserWhitelists);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("查询成功");
            rm.setData(queryReuslt);
        }catch (Exception e){
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 添加用户
     * @param cellPhone
     * @param request
     * @return
     */
    @RequestMapping(value = "/add/userWhitelist")
    public ResultMsgBean addUserWhitelist(String cellPhone, HttpServletRequest request) throws Exception {
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK,"添加成功");
        if (StringUtils.isBlank(cellPhone)){
            rm.setCode(ResultMsgBean.ERROR);
            rm.setMsg("缺少必要的参数");
            return rm;
        }
        TytOwnerUserWhitelist tytOwnerUserWhitelist = ownerUserWhitelistService.getWhitelistList(cellPhone);
        if (tytOwnerUserWhitelist != null){
            rm.setCode(ResultMsgBean.ERROR);
            rm.setMsg("手机号已存在");
            return rm;
        }
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
        if (curUser == null){
            rm.setCode(ResultMsgBean.ERROR);
            rm.setMsg("未登录");
            return rm;
        }
        ownerUserWhitelistService.addUserWhitelist(cellPhone,curUser);
        return rm;
    }

    /**
     * 删除用户
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "/update/userWhitelist")
    public ResultMsgBean updateUserWhitelist(Long id, HttpServletRequest request){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK,"删除成功");
        if (id == null ){
            rm.setCode(ResultMsgBean.ERROR);
            rm.setMsg("缺少必要的参数");
            return rm;
        }
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
        if (curUser == null){
            rm.setCode(ResultMsgBean.ERROR);
            rm.setMsg("未登录");
            return rm;
        }
        ownerUserWhitelistService.updateUserWhitelist(id,curUser);
        return rm;
    }
}
