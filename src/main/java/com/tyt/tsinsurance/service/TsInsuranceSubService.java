package com.tyt.tsinsurance.service;

import java.util.List;

import com.tyt.model.PageBean;
import com.tyt.model.TransportInsuranceSub;
import com.tyt.service.base.BaseService;
import com.tyt.tsinsurance.bean.InsuranceSubBean;

public interface TsInsuranceSubService extends BaseService<TransportInsuranceSub, Long>{

	List<InsuranceSubBean> getList(InsuranceSubBean queryBean, PageBean pageBean);

	StringBuffer getStringCsv(List<InsuranceSubBean> list);

}
