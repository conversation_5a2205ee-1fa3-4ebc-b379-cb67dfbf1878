package com.tyt.tsinsurance.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.tsinsurance.bean.PinganPdfGetBean;
import com.tyt.util.*;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportInsurance;
import com.tyt.model.TransportInsuranceSub;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;
import com.tyt.tsinsurance.bean.TsInsuranceQueryBean;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.tsinsurance.service.TsInsuranceSubService;
import com.tyt.util.service.plat.PlatResultMsgBean;
import com.tyt.util.service.plat.PublicTytServiceHttpUtil;

@Service("tsInsuranceService")
public class TsInsuranceServiceImpl extends BaseServiceImpl<TransportInsurance, Long> implements TsInsuranceService{
	
	public static Logger logger = LoggerFactory.getLogger(TsInsuranceServiceImpl.class);
	
	@Resource(name = "tsInsuranceDao")
	public void setBaseDao(BaseDao<TransportInsurance, Long> tsInsuranceDao) {
		super.setBaseDao(tsInsuranceDao);
	}
	@Resource(name="tsInsuranceSubService")
	private TsInsuranceSubService tsInsuranceSubService;
	
	@Resource(name="userService")
	private UserService userService;

    static PropertiesUtil server = PropertiesUtil.init("server");
	
	@SuppressWarnings("deprecation")
	@Override
	public List<TsInsuranceQueryBean> getInsuranceList(TsInsuranceQueryBean queryBean,PageBean pageBean) {
		Map<String,Object> paramMap = new HashMap<String,Object>();
		StringBuffer conditionSQL=new StringBuffer(" where 1=1 ");
		if(StringUtils.isNotBlank(queryBean.getId())){
			conditionSQL.append(" and s.id=:id");
			paramMap.put("id", queryBean.getId());
		}
		if(StringUtils.isNotBlank(queryBean.getCostumerPhone())){
			Long userId = userService.getUserIdByCellPhone(queryBean.getCostumerPhone());
			if (userId!=null) {
				conditionSQL.append("and (s.user_id=:userId or ");
				paramMap.put("userId", userId);
			}else {
				conditionSQL.append("and ( ");
			}
			conditionSQL.append(" s.applicant_phone=:costumerPhone or s.insured_phone=:costumerPhone) ");
			paramMap.put("costumerPhone", queryBean.getCostumerPhone());
		}
		if (StringUtils.isNotBlank(queryBean.getNumber())) {
			conditionSQL.append(" and s.number like:number");
			paramMap.put("number", "%"+queryBean.getNumber()+"%");
		}
		if (queryBean.getStatus()!=null) {
			String now = TimeUtil.formatDateTime(new Date());
			//待支付   已退保
			if (queryBean.getStatus()==0 || queryBean.getStatus()==2) {
				conditionSQL.append(" and s.status=:status");
				paramMap.put("status", queryBean.getStatus());
			}
			//已生效
			if (queryBean.getStatus()==1) {
				conditionSQL.append(" and s.status=1 and s.start_time<=:now and s.effective_time>=:now");
				paramMap.put("now", now);
			}
			//已过期
			if (queryBean.getStatus()==3) {
				conditionSQL.append(" and s.status=1 and s.effective_time<:now");
				paramMap.put("now", now);
			}
			//待生效（已支付，未到起运日期）
			if (queryBean.getStatus()==4) {
				conditionSQL.append(" and s.status=1 and s.start_time>:now");
				paramMap.put("now", now);
			}
		}
		//保险公司类型 1.华泰 2.人保
        if(queryBean.getType() != null)
		{
			conditionSQL.append(" and s.type=:type");
			paramMap.put("type", queryBean.getType());
		}
		if (StringUtils.isNotBlank(queryBean.getCtimeStart())) {
			conditionSQL.append(" and s.ctime>:ctimeStart");
			paramMap.put("ctimeStart", queryBean.getCtimeStart()+" 00:00:00");
		}
		if (StringUtils.isNotBlank(queryBean.getCtimeEnd())) {
			conditionSQL.append(" and s.ctime<:ctimeEnd");
			paramMap.put("ctimeEnd", queryBean.getCtimeEnd()+" 23:59:59");
		}
		conditionSQL.append(" and (s.del_status=0 or s.del_status=1) ");
		StringBuffer countSQL = new StringBuffer("SELECT COUNT(*) FROM transport_insurance s");
		countSQL.append(conditionSQL);
		BigInteger rowCount = this.getBaseDao().queryByMap(countSQL.toString(), paramMap);
		if(rowCount==null||rowCount.longValue()<=0l){
	 		return null;
	 	}
		pageBean.setRowCount(rowCount.longValue());
		StringBuffer querySQL = new StringBuffer("SELECT s.`id` id,s.`user_id` userId,s.`cell_phone` cellPhone,"
				+ "s.`user_name` userName, s.`applicant_name` applicantName,"
				+ "s.`applicant_phone` applicantPhone,s.`insured_name` insuredName,"
				+ "s.`insured_phone` insuredPhone, s.`type` type, s.`number` number,"
				+ "s.`head_no` headNo,s.`start_time` startTime,s.`effective_time` effectiveTime,"
				+ "s.`status` status,s.`ctime` ctime,s.`pdf_url` pdfUrl,s.img_url imgUrl,s.pay_time payTime,s.premium_currency premiumCurrency,s.company company FROM `transport_insurance` s");
		querySQL.append(conditionSQL);
		querySQL.append(" order by id desc");
		Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
		map.put("id", Hibernate.STRING);
		map.put("userId", Hibernate.LONG);
		map.put("cellPhone", Hibernate.STRING);
		map.put("userName", Hibernate.STRING);
		map.put("applicantName", Hibernate.STRING);
		map.put("applicantPhone", Hibernate.STRING);
		map.put("insuredName", Hibernate.STRING);
		map.put("insuredPhone", Hibernate.STRING);
		map.put("type", Hibernate.INTEGER);
		map.put("number", Hibernate.STRING);
		map.put("headNo", Hibernate.STRING);
		map.put("startTime", Hibernate.TIMESTAMP);
		map.put("effectiveTime", Hibernate.TIMESTAMP);
		map.put("status", Hibernate.INTEGER);
		map.put("ctime", Hibernate.TIMESTAMP);
		map.put("pdfUrl", Hibernate.STRING);
        map.put("imgUrl", Hibernate.STRING);
        map.put("payTime", Hibernate.TIMESTAMP);
        map.put("premiumCurrency", Hibernate.INTEGER);
        map.put("company", Hibernate.STRING);
		return this.getBaseDao().searchByName(querySQL.toString(), map, TsInsuranceQueryBean.class, paramMap, pageBean.getCurrentPage(), pageBean.getPageSize());
	}
	

	@Override
	public void updateRetreatStatus(TransportInsurance insurance,
			String retreatCurrency) {
		Integer curreny = new BigDecimal(retreatCurrency).movePointRight(2).intValue();
		insurance.setRetreatCurrency(curreny);
		insurance.setStatus(2);
		insurance.setUtime(new Date());
		this.update(insurance);
		//添加流水
		TransportInsuranceSub sub=new TransportInsuranceSub();
		sub.setPid(insurance.getId());
		sub.setUserId(insurance.getUserId());
		sub.setCellPhone(insurance.getCellPhone());
		sub.setApplicantName(insurance.getCellPhone());
		sub.setApplicantPhone(insurance.getApplicantPhone());
		sub.setInsuredName(insurance.getInsuredName());
		sub.setInsuredPhone(insurance.getInsuredPhone());
		sub.setCompany(insurance.getCompany());
		sub.setType(insurance.getType());
		sub.setNumber(insurance.getNumber());
		sub.setCurrency(insurance.getRetreatCurrency());
		sub.setCtime(new Date());
		sub.setPayType(insurance.getPayType());
		sub.setRetreatType(2);
		sub.setUtime(new Date());
		tsInsuranceSubService.add(sub);
	}

    @Override
    public String saveTsInsurancePdf(Long insuranceId, String number) {
        PinganPdfGetBean pdfBean = new PinganPdfGetBean();
        pdfBean.setPartnerCode("P_BJBLD_GP");
        pdfBean.setPolicyNo(number);
        //电子保单接口名+时间戳
        String request_id="printGP"+System.currentTimeMillis();
        String access_token= RedisUtil.get("ping_an_token_key");
        if (StringUtils.isBlank(access_token)){
            access_token = PingAnUtil.getPingAnToken();
        }
        String url = server.getString("pingan.insurance.getPDF")+"?access_token="+access_token+"&request_id="+request_id;
        String response = HttpUtil.doPost(url, JSONObject.toJSONString(pdfBean));
        System.out.println(JSONObject.toJSONString(pdfBean));
        System.out.println(response);
        JSONObject jsonObject = JSON.parseObject(response);
        if ("0".equals(jsonObject.getString("ret"))) {
            JSONObject data = jsonObject.getJSONObject("data");
            String resultCode = data.getString("responseCode");
            if ("999999".equals(resultCode)) {
                String pdf = data.getString("returnPdfValue");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(pdf)) {
                    String fileName = PingAnUtil.getSaveFilePathName("htbx", number + ".pdf");
                    logger.info("pabx_pdf_save_path_name=" + fileName);
                    Base64Util.base64StringToPdf(pdf, AppConfig.getProperty("picture.path.domain")+fileName);
                    savePdf(insuranceId,fileName);
                    return fileName;
                }else{
                    logger.info("获取pinganPdf失败,pdf文件为null");
                }
            } else {
                String responseMsg = data.getString("responseMsg");
                logger.info("获取pinganPdf失败，失败原因为：" + responseMsg);
            }
        }else{
            logger.info("pinganPdf通讯失败，失败原因为：" + jsonObject.getString("msg"));
        }
        return null;
    }

    @Override
    public StringBuffer getStringCsv(List<TsInsuranceQueryBean> insuranceList) {
        StringBuffer content = new StringBuffer();
        // 标题栏
        String header = "ID,保险单号,注册手机号,投保人姓名,车头牌照号,交易时间,保险公司,金额,交易类型"+ "\r\n";
        content.append(header);
        if (insuranceList == null || insuranceList.size() <= 0){
            return content;
        }
        for (TsInsuranceQueryBean bean : insuranceList) {
            StringBuffer row = new StringBuffer();
            //ID
            row.append(bean.getId()).append(",");
            // 保险单号
            row.append(bean.getNumber()==null?"":bean.getNumber()+"\t").append(",");
            // 注册手机号
            row.append(StringUtils.isBlank(bean.getCellPhone())?"":bean.getCellPhone()+"\t").append(",");
            // 投保人姓名
            row.append(StringUtils.isBlank(bean.getApplicantName())?"":bean.getApplicantName()).append(",");
            // 车头牌照号
            row.append(StringUtils.isBlank(bean.getHeadNo())?"":bean.getHeadNo()).append(",");
            // 交易时间
            row.append(bean.getPayTime()==null?"":TimeUtil.formatDateTime(bean.getPayTime())).append(",");
            // 保险公司
            row.append(StringUtils.isBlank(bean.getCompany())?"":bean.getCompany()).append(",");
            // 金额
            if(bean.getStatus()==1){
                row.append(bean.getPremiumCurrency()==null?"":new java.text.DecimalFormat("#.00").format((bean.getPremiumCurrency()/(double)100))).append(",");
            }else if(bean.getStatus()==2){
                row.append(bean.getPremiumCurrency()==null?"":"-"+new java.text.DecimalFormat("#.00").format((bean.getPremiumCurrency()/(double)100))).append(",");
            }else{
                row.append(",");
            }
            //交易类型
            if (bean.getStatus()==1){
                row.append("保险购买").append(",");
            }else if (bean.getStatus()==2){
                row.append("保险退回").append(",");
            }else{
                row.append("未支付").append(",");
            }
            content.append(row).append("\r\n");
        }
        return content;
    }

    private void savePdf(Long insuranceId, String fileName){
	    String sql = "UPDATE `transport_insurance` SET `img_url`=? where id=?";
        this.executeUpdateSql(sql, new Object[]{fileName,insuranceId});
    }


}
