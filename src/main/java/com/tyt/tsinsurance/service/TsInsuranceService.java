package com.tyt.tsinsurance.service;

import java.util.List;

import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportInsurance;
import com.tyt.service.base.BaseService;
import com.tyt.tsinsurance.bean.TsInsuranceQueryBean;

public interface TsInsuranceService extends BaseService<TransportInsurance, Long>{

	List<TsInsuranceQueryBean> getInsuranceList(TsInsuranceQueryBean queryBean,
			PageBean pageBean);

	void updateRetreatStatus(TransportInsurance insurance,
			String retreatCurrency);


    String saveTsInsurancePdf(Long insuranceId,String number);

    StringBuffer getStringCsv(List<TsInsuranceQueryBean> insuranceList);
}
