package com.tyt.manager.controller;

import com.github.pagehelper.PageInfo;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.bean.UploadFileReq;
import com.tyt.manager.entity.base.TytApkManage;
import com.tyt.manager.service.base.ApkManageService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.web.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2025/04/09 17:43
 */
@Controller
@RequestMapping("/apk/manage")
@Slf4j
public class ApkManageController extends BaseController {

    @Autowired
    private ApkManageService apkManageService;

    @RequestMapping("/getList")
    @ResponseBody
    public ResultMsgBean getApkList(@RequestParam(value = "page") Integer page,
                                    @RequestParam(value = "size") Integer size,
                                    Integer clientType,
                                    String version) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            PageInfo<TytApkManage> result = apkManageService.getApkList(page, size, clientType, version);
            rm.setData(result);
        } catch (Exception e) {
            log.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping("/uploadChunk")
    @ResponseBody
    public ResultMsgBean uploadChunk(UploadFileReq req) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "上传成功");
        try {
            if (req.getFile() == null || StringUtils.isBlank(req.getUuid()) || StringUtils.isBlank(req.getName()) || req.getChunk() == null){
                return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "参数错误");
            }
            TytApkManage apkManage = apkManageService.getByVersionAndClientType(req.getVersion(), req.getClientType());
            if (apkManage != null && req.getId() == null){
                return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "版本号已存在");
            }
            apkManageService.updateUploadApkChunk(req);
        } catch (Exception e) {
            log.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("分片上传失败");
        }
        return rm;
    }

    @RequestMapping("/mergeChunk")
    @ResponseBody
    public ResultMsgBean mergeChunk(@RequestParam("uuid") String uuid,
                                    @RequestParam("version") String version,
                                    @RequestParam("clientType") Integer clientType,
                                    Long id,
                                    HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "未登录");
            }
            TytApkManage apkManage = apkManageService.getByVersionAndClientType(version, clientType);
            if (apkManage != null && id == null){
                return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "版本号已存在");
            }
            apkManageService.updateMergeChunk(id, uuid, version, clientType,curUser.getRealName());
        } catch (Exception e) {
            log.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

}
