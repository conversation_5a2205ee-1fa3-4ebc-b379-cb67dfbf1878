package com.tyt.manager.controller;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytTransportDictionary;

import com.tyt.manager.service.dictionary.TytTransportDictionaryService;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import com.tyt.web.base.BaseController;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("/dictionary")
public class TytTransportDictionaryController extends BaseController{

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytTransportDictionaryService")
    private TytTransportDictionaryService tytTransportDictionaryService;



    /**
     * 货源分词词典列表
     * @param request 获取登录信息
     * @param page   页数
     * @param size   条数
     * @return
     */
    @RequestMapping(value = "/list")
    public void competeCellphoneList(HttpServletRequest request, Integer page, Integer size, String keyword, HttpServletResponse response){
        // 验证是否已登录
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN));

        }
        if (page == null || size == null){
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"参数异常"));

        }
        try {
            printJSON(request, response, new ResultMsgBean(ResultMsgBean.OK,ResultMsgBean.OK_MSG,tytTransportDictionaryService.list(page,size,keyword)));

        }catch (Exception e){
            e.printStackTrace();
            logger.info("error:{}",e.getMessage());
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"服务器异常"));

        }
    }

    /**
     * 逻辑删除货源分词词典
     * @param request
     * @param id
     * @return
     */
    @RequestMapping("/delete")
    public void deleteMessage(HttpServletRequest request , Long id, HttpServletResponse response) {
        // 验证是否已登录
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN));

        }
        if (id == null){
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"参数异常"));

        }
        try{
            tytTransportDictionaryService.updateDeleteById(id);
             printJSON(request, response,new ResultMsgBean(ResultMsgBean.OK, "删除成功"));

        }catch (Exception e){
            e.printStackTrace();
            logger.info("error:{}",e.getMessage());
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"删除失败"));

        }
    }

    /**
     * 新增货源分词词典
     * @param request
     * @param tytTransportDictionary
     * @return
     */
    @RequestMapping("/save")
    public void save(HttpServletRequest request, TytTransportDictionary tytTransportDictionary, HttpServletResponse response){
        // 验证是否已登录
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN));

        }
        if (tytTransportDictionary == null){
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"参数异常"));

        }
        if(!StringUtil.isNotEmpty(tytTransportDictionary.getKeyword())){
             printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"请输入关键词"));

        }
        if(null == tytTransportDictionary.getRate() || tytTransportDictionary.getRate() > 100 || tytTransportDictionary.getRate() <1){
             printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"请输入正确词频值"));

        }
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            printJSON(request, response,tytTransportDictionaryService.save(tytTransportDictionary,resultMsgBean));

        }catch (Exception e){
            e.printStackTrace();
            logger.info("error:{}",e.getMessage());
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            printJSON(request, response,resultMsgBean);

        }
    }

    /**
     * 根据id查询详情
     * @param request
     * @param id
     * @return
     */
    @RequestMapping("/detail")
    public void detail(HttpServletRequest request, Long id, HttpServletResponse response){
        // 验证是否已登录
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN));

        }
        if (id == null){
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"参数异常"));

        }
        try {
            TytTransportDictionary tytTransportDictionary = tytTransportDictionaryService.getByid(id);
             printJSON(request, response,new ResultMsgBean(ResultMsgBean.OK,ResultMsgBean.OK_MSG,tytTransportDictionary));

        }catch (Exception e){
            e.printStackTrace();
            logger.info("error:{}",e.getMessage());
             printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"服务器异常"));

        }
    }
    /**
     * 根据id更新货源分词词典
     * @param request
     * @param tytTransportDictionary
     * @return
     */
    @RequestMapping("/update")
    public void update(HttpServletRequest request, TytTransportDictionary tytTransportDictionary, HttpServletResponse response) {
        // 验证是否已登录
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR, Constant.MSG_NOT_LOGIN));

        }
        if (tytTransportDictionary == null){
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"参数异常"));

        }
        if(!StringUtil.isNotEmpty(tytTransportDictionary.getKeyword())){
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"请输入关键词"));

        }
        if(null == tytTransportDictionary.getRate() || tytTransportDictionary.getRate() > 100 || tytTransportDictionary.getRate() < 1){
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"请输入正确词频值"));

        }
        try {
            tytTransportDictionaryService.update(tytTransportDictionary);
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.OK,ResultMsgBean.OK_MSG));

        }catch (Exception e){
            e.printStackTrace();
            logger.info("error:{}",e.getMessage());
            printJSON(request, response,new ResultMsgBean(ResultMsgBean.ERROR,"服务器异常"));

        }
    }

}
