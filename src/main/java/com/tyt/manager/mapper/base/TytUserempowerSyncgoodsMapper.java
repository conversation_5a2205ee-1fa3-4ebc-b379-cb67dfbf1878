package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytUserempowerSyncgoods;
import com.tyt.model.PageBean;
import com.tyt.web.back.internal.bean.TytUserempowerSyncgoodsBean;
import com.tyt.web.qbean.TytUserempowerQueryBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytUserempowerSyncgoodsMapper extends CustomBaseMapper<TytUserempowerSyncgoods> {
    Integer findEmpowerUser(@Param("empowerUserId") Long empowerUserId);

    List<TytUserempowerSyncgoodsBean> findPageUserempowerList(@Param("pageBean") TytUserempowerQueryBean pageBean);

    Integer findEmpowerAllUserCount();

    Integer findNoEmpowerUserCount();

    /**
     * 查询已授权用户数量
     * @return
     */
    Integer selectEmpowerAllCount();

    TytUserempowerSyncgoods selectOneById(@Param("id") Long id);

    Integer findYesEmpowerUser(@Param("userId") Long userId);
}
