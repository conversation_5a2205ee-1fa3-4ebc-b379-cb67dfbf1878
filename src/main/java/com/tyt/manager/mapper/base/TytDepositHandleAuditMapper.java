package com.tyt.manager.mapper.base;

import com.tyt.manager.bean.DepositHandleAudit;
import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytDepositHandleAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytDepositHandleAuditMapper extends CustomBaseMapper<TytDepositHandleAudit> {

    /**
     * 查询保证金客服审核列表
     * @param userId
     * @param userApplyStartTimeStr
     * @param userApplyEndTimeStr
     * @param customerServiceApplyStartTimeStr
     * @param customerServiceApplyEndTimeStr
     * @param auditStage
     * @param auditStatus
     * @param tsOrderNo
     * @param pubCellPhone
     * @return
     */
    List<DepositHandleAudit> getDepositHandleAuditList(@Param("userId") Long userId,
                                                       @Param("userApplyStartTimeStr") String userApplyStartTimeStr,
                                                       @Param("userApplyEndTimeStr") String userApplyEndTimeStr,
                                                       @Param("customerServiceApplyStartTimeStr") String customerServiceApplyStartTimeStr,
                                                       @Param("customerServiceApplyEndTimeStr") String customerServiceApplyEndTimeStr,
                                                       @Param("auditStage") Integer auditStage,
                                                       @Param("auditStatus") Integer auditStatus, @Param("handleStatus") Integer handleStatus,
                                                       @Param("tsOrderNo") String tsOrderNo,
                                                       @Param("pubCellPhone") String pubCellPhone);

    /**
     * 根据异常上报ID获取保证金客服审核信息
     * @param exId
     * @return
     */
    DepositHandleAudit getDetailByExId(@Param("exId") Long exId);
}