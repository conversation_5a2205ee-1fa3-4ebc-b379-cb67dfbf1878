package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytCarOwnerAuth;
import com.tyt.web.back.internal.bean.CarOwnerAuthDetailsVO;
import com.tyt.web.back.internal.bean.CarOwnerAuthListVO;
import com.tyt.web.back.internal.bean.CarOwnerAuthReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TytCarOwnerAuthMapper extends CustomBaseMapper<TytCarOwnerAuth> {
    /**
     * 查询列表数据
     * @param cellPhone
     * @return
     */
    List<CarOwnerAuthListVO> selectList(@Param("cellPhone") String cellPhone, @Param("authStatus") Integer authStatus,
                                        @Param("carRelation") Integer carRelation, @Param("authUserName") String authUserName,
                                        @Param("createTimeStart") Date createTimeStart, @Param("createTimeEnd") Date createTimeEnd,
                                        @Param("authTimeStart") Date authTimeStart, @Param("authTimeEnd") Date authTimeEnd);

    /**
     * 查询详情数据
     * @param id
     * @return
     */
    CarOwnerAuthDetailsVO selectCarOwnerAuthDetailsVOById(@Param("id") Long id);

    /**
     * 根据车主认证id查询注册手机号
     * @param id
     * @return
     */
    String selectCellPhoneById(@Param("id") Long id);

    /**
     * 根据用户id及状态统计认证数量
     * @param userId
     * @param authStatus
     * @return
     */
    Integer countByAuthStatus(@Param("userId") Long userId,@Param("authStatus") Integer authStatus);

    void deleteCarOwnerAuthByCarId(@Param("carId") Long carId);

    Long getCarOwnerAuthIdByCarId(@Param("carId") Long carId);

    void deleteCarOwnerAuthProofByCarOwnerAuthId(@Param("carOwnerAuthId") Long carOwnerAuthId);

    /**
     * 审核修改状态信息
     * @param carOwnerAuthReq
     */
    void updateByCarOwnerAuthReq(CarOwnerAuthReq carOwnerAuthReq,@Param("authUserId") Long authUserId,@Param("authUserName") String authUserName);

}