package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.ExposurePermissionGainRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ExposurePermissionGainRecordMapper extends CustomBaseMapper<ExposurePermissionGainRecord> {
    List<ExposurePermissionGainRecord> getExposurePermissionGainRecord(@Param("userId")Long userId, @Param("gainType")Integer gainType, @Param("startTime")
            Date startTime, @Param("endTime") Date endTime, @Param("pageNumber") Integer pageNumber, @Param("pageSize") Integer pageSize);

}