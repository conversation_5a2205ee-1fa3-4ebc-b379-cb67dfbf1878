package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytGoodsRefreshConfig;
import com.tyt.refresh.req.GoodsRefreshConfigReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/11 12:12
 */
@Mapper
public interface TytGoodsRefreshConfigMapper extends CustomBaseMapper<TytGoodsRefreshConfig> {
    /**
     * 查询刷新配置列表
     *
     * @return
     */
    List<TytGoodsRefreshConfig> getRefreshConfigList(GoodsRefreshConfigReq req);

    /**
     * 根据code查询记录
     *
     * @param code
     * @return
     */
    TytGoodsRefreshConfig selectByCode(@Param("code") String code);
}