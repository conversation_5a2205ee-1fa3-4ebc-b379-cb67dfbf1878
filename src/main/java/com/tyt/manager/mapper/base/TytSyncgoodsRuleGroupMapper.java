package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytSyncgoodsRuleGroup;
import com.tyt.web.back.internal.bean.SyncgoodsRuleList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytSyncgoodsRuleGroupMapper extends CustomBaseMapper<TytSyncgoodsRuleGroup> {

    List<SyncgoodsRuleList> selectSyncgoodsRuleList();

    TytSyncgoodsRuleGroup selectRuleByRuleName(@Param("ruleName") String ruleName);

    int countRule();
}
