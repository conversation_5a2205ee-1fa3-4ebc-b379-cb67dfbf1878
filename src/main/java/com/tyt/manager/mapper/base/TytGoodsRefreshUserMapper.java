package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytGoodsRefreshUser;
import com.tyt.refresh.req.GoodsRefreshUserReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/11 12:12
 */
@Mapper
public interface TytGoodsRefreshUserMapper extends CustomBaseMapper<TytGoodsRefreshUser> {
    /**
     * 根据code查询用户列表
     *
     * @param req
     * @return
     */
    List<TytGoodsRefreshUser> selectByRefreshCode(@Param("req") GoodsRefreshUserReq req);

    /**
     * 批量删除
     *
     * @param userIdList
     * @param refreshCode
     */
    void deleteByUserId(@Param("userIdList") List<Long> userIdList, @Param("refreshCode") String refreshCode);

    /**
     * 删除这个配置下的所有用户
     *
     * @param refreshCode
     */
    void deleteByCode(@Param("refreshCode") String refreshCode);
}