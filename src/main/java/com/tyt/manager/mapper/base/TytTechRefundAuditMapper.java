package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytTechRefundAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytTechRefundAuditMapper extends CustomBaseMapper<TytTechRefundAudit> {


    /**
     * 根据多个条件查询技术审核退款列表。
     *
     * 本方法用于查询特定条件下的技术审核退款记录。通过传入不同的参数，可以筛选出符合特定时间范围、订单号、手机号码、审核人员等条件的记录。
     * 这对于跟踪和管理退款审核流程，以及进行相关数据分析具有重要作用。
     *
     * @param userApplyStartTimeStr 用户申请退款的起始时间字符串
     * @param userApplyEndTimeStr 用户申请退款的结束时间字符串
     * @param auditStartTimeStr 审核开始时间字符串
     * @param auditEndTimeStr 审核结束时间字符串
     * @param tsOrderNo 商城订单号
     * @param pubCellPhone 用户公开的手机号码
     * @param payCellPhone 用户支付使用的手机号码
     * @param auditUserName 审核人员的用户名
     * @param finalAuditStatus 最终审核状态，使用整数表示
     * @param intoLoadLocation 是否去过装货地 0-初始化 1是 2否
     * @param intoUnLoadLocation 是否去过卸货地 0-初始化 1是 2否
     * @param isYmmGoods 是否运满满货源：0.否 1.是
     * @return 返回符合条件的技术审核退款列表
     */
    List<TytTechRefundAudit> getTechRefundAuditList(@Param("userApplyStartTimeStr") String userApplyStartTimeStr,
                                                        @Param("userApplyEndTimeStr") String userApplyEndTimeStr,
                                                        @Param("auditStartTimeStr") String auditStartTimeStr,
                                                        @Param("auditEndTimeStr") String auditEndTimeStr,
                                                        @Param("tsOrderNo") String tsOrderNo,
                                                        @Param("pubCellPhone") String pubCellPhone,
                                                        @Param("payCellPhone") String payCellPhone,
                                                        @Param("auditUserName") String auditUserName,
                                                        @Param("finalAuditStatus") Integer finalAuditStatus,
                                                        @Param("refundTecChannel") Integer refundTecChannel,
                                                        @Param("intoLoadLocation") Integer intoLoadLocation,
                                                        @Param("intoUnLoadLocation") Integer intoUnLoadLocation,
                                                        @Param("isYmmGoods") Integer isYmmGoods);
}