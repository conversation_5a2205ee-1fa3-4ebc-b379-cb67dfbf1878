package com.tyt.manager.mapper.base;

import com.tyt.manager.bean.MachineTypeReq;
import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytMachineTypeAudit;
import com.tyt.manager.entity.base.TytMachineTypeBrandNew;
import com.tyt.manager.vo.machine.MachineTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface TytMachineTypeBrandNewMapper extends CustomBaseMapper<TytMachineTypeBrandNew> {
    /**
     * @param req
     * @return
     */
    List<MachineTypeVo> selectMachineType(@Param("req") MachineTypeReq req);

    /**
     * 根据名称查询
     *
     * @param showName
     * @return
     */
    TytMachineTypeBrandNew getByShowName(@Param("showName") String showName);

    /**
     * 获取最大score
     *
     * @return
     */
    BigDecimal selectMaxScore();

    /**
     * 删除
     *
     * @param idList
     * @return
     */
    void delByIdList(@Param("idList") List<Long> idList);

    /**
     * 批量查询
     *
     * @param idList
     * @return
     */
    List<TytMachineTypeBrandNew> selectByIdList(@Param("idList") List<Long> idList);


}