package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytOwnerUserWhitelist;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytOwnerUserWhitelistMapper extends CustomBaseMapper<TytOwnerUserWhitelist> {

    List<TytOwnerUserWhitelist> selectList(@Param("cellPhone") String cellPhone);

    void updateUserWhitelist(@Param("id") Long id ,@Param("userName") String userName);
}