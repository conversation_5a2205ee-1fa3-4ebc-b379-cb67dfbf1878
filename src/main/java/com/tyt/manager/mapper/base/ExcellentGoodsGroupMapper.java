package com.tyt.manager.mapper.base;

import com.tyt.infofee.bean.ExcellentGoodsGroupResultBean;
import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.ExcellentGoodsGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExcellentGoodsGroupMapper extends CustomBaseMapper<ExcellentGoodsGroup> {

    List<ExcellentGoodsGroupResultBean> selectGroup();

    List<ExcellentGoodsGroup> selectGroupByName(String groupName);
    ExcellentGoodsGroup findByGroupId(@Param("groupId") Long groupId1);
}