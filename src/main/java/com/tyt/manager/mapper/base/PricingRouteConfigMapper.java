package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.pricingRoute.bean.PricingRouteConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 发货定价路线配置 mapper
 *
 * <AUTHOR>
 * @since 2024/04/19 19:14
 */
@Mapper
public interface PricingRouteConfigMapper extends CustomBaseMapper<PricingRouteConfig> {

    // 模糊匹配
    List<PricingRouteConfig> queryFuzzyList(PricingRouteConfig param);

    // 精确查询
    List<PricingRouteConfig> list(PricingRouteConfig param);

}
