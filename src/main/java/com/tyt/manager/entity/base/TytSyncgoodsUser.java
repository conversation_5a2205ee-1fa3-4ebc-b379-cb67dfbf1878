package com.tyt.manager.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_syncgoods_user")
public class TytSyncgoodsUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货方姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货源同步配置idtyt_syncgoods_rule id）
     */
    @Column(name = "rule_group_id")
    private Long ruleGroupId;

    private Date ctime;

    private Date mtime;

    /**
     * 操作人
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 是否删除(0否；1 是)
     */
    @Column(name = "is_delete")
    private Integer isDelete;
}