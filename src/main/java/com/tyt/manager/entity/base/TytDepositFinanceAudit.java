package com.tyt.manager.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_deposit_finance_audit")
public class TytDepositFinanceAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 保证金申请ID (tyt_deposit_apply_audit表主键Id)
     */
    @Column(name = "apply_audit_id")
    private Long applyAuditId;

    /**
     * 用户上报时间
     */
    @Column(name = "user_apply_time")
    private Date userApplyTime;

    /**
     * 保证金金额 单位:元
     */
    @Column(name = "deposit_amount")
    private BigDecimal depositAmount;

    /**
     * 保证金类型：1.退还保证金(用户提交的申请类型) 2.扣除保证金(客服提交的申请类型)
     */
    @Column(name = "deposit_amount_type")
    private Integer depositAmountType;

    /**
     * 处理结果 0.待处理 1.批准 2.拒绝
     */
    @Column(name = "audit_status")
    private Integer auditStatus;

    /**
     * 审核人员Id
     */
    @Column(name = "audit_user_id")
    private Long auditUserId;

    /**
     * 审核人员
     */
    @Column(name = "audit_user_name")
    private String auditUserName;

    /**
     * 申请审批时间
     */
    @Column(name = "apply_audit_time")
    private Date applyAuditTime;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}