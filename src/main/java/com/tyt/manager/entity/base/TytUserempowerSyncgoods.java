package com.tyt.manager.entity.base;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName TytUserempowerSyncgoods
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/10/14 10:06
 * @Verdion 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_userempower_syncgoods")
public class TytUserempowerSyncgoods implements Serializable {

    private static final long serialVersionUID = -3675329980168556928L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货方姓名
     */
    @Column(name = "goods_user_name")
    private String goodsUserName;

    /**
     * 状态(0已授权；1 未授权)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 修改人id
     */
    @Column(name = "update_user_id")
    private Long updateUserId;

    /**
     * 操作人
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 授权时间
     */
    @Column(name = "sync_time")
    private Date syncTime;

    /**
     * 创建时间
     */
    @Column(name = "ctime")
    private Date ctime;

    /**
     * 修改时间
     */
    @Column(name = "mtime")
    private Date mtime;

    /**
     * 是否删除(0否；1 是)
     */
    @Column(name = "is_delete")
    private Integer isDelete;
}