package com.tyt.manager.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cover_goods_dial_config")
public class TytCoverGoodsDialConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 是否删除(0未删除，1已删除)
     */
    @Column(name = "del_flag")
    private Boolean delFlag;


    /**
     * 捂货相关配置类型 1 捂货设置 2 抢单豆设置  3 捂货白名单设置
     */
    @Column(name = "config_type")
    private Integer configType;

    /**
     * 操作人ID
     */
    @Column(name = "operate_user_id")
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "operate_user_name")
    private String operateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 配置名
     */
    private String name;

    /**
     * 发货倒计时
     */
    @Column(name = "x_time_in_seconds")
    private Integer xTimeInSeconds;

    /**
     * 详情倒计时
     */
    @Column(name = "y_time_in_seconds")
    private Integer yTimeInSeconds;

    /**
     * 免责次数
     */
    @Column(name = "n_times")
    private Integer nTimes;
    /**
     * 启用标识 0未启用，1启用
     */
    @Column(name = "enable")
    private Boolean enable;
    /**
     * 免责卡发放次数
     */
    @Column(name = "grant_n_times")
    private Integer grantNTimes;
}