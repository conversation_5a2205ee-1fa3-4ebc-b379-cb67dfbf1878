package com.tyt.manager.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "credit_manage_config_user")
public class CreditManageConfigUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置ID 关联credit_manage_config表id
     */
    @Column(name = "config_id")
    private Long configId;

    /**
     * 配置子表ID 关联credit_manage_config_sub表id
     */
    @Column(name = "sub_id")
    private Long subId;

    /**
     * 用户Id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 是否已加入 0未加入 1已加入
     */
    @Column(name = "is_join")
    private Integer isJoin;

    /**
     * 是否删除 0未删除 1已删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人Id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    private Date ctime;

    private Date mtime;
}