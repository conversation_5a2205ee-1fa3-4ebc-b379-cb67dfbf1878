package com.tyt.manager.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_user_security_record")
public class TytUserSecurityRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 来源类型（1活动;）
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 活动id
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 来源类型备注
     */
    @Column(name = "source_desc")
    private String sourceDesc;

    /**
     * 获取金额值
     */
    @Column(name = "money_amount")
    private BigDecimal moneyAmount;

    /**
     * 管理员用户id
     */
    @Column(name = "manager_user_id")
    private Long managerUserId;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private Date expireTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}