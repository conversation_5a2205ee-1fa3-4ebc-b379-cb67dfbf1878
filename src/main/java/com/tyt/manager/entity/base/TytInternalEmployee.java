package com.tyt.manager.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_internal_employee")
public class TytInternalEmployee {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 系统用户名
     */
    private String name;

    /**
     * 登录手机号
     */
    @Column(name = "login_phone_no")
    private String loginPhoneNo;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 机构主键
     */
    @Column(name = "institution_id")
    private Long institutionId;

    /**
     * 部门表ID
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 上级领导系统姓名
     */
    @Column(name = "up_leader_name")
    private String upLeaderName;

    /**
     * 下级转移
     */
    @Column(name = "change_leader_name")
    private String changeLeaderName;

    /**
     * 是否有效(1 有效 0 无效 2删除)
     */
    @Column(name = "is_valid")
    private Integer isValid;

    /**
     * 最后一次登录时间
     */
    @Column(name = "last_login_time")
    private Date lastLoginTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 最后修改人（员工表ID）
     */
    @Column(name = "last_updater")
    private Long lastUpdater;

    /**
     * 职位：配置在数据字典
     */
    @Column(name = "position_id")
    private Long positionId;

    /**
     * 数据权限类型 0 不做限制 1 销售模块查看自己及下级数据，2 销售模块 查看所有数据，如果是多个数字使用逗号分隔如1,2
     */
    @Column(name = "data_institution_type")
    private Byte dataInstitutionType;
}