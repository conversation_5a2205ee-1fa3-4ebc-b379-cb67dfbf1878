package com.tyt.manager.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_tech_refund_finance_audit")
public class TytTechRefundFinanceAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 退还技术服务费审核ID (tyt_tech_refund_audit表主键Id)
     */
    @Column(name = "tech_refund_audit_id")
    private Long techRefundAuditId;

    /**
     * 异常上报表ID
     */
    @Column(name = "ex_id")
    private Long exId;

    /**
     * tyt_transport_orders表主键ID
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户昵称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户手机号
     */
    @Column(name = "user_cell_phone")
    private String userCellPhone;

    /**
     * 用户上报时间
     */
    @Column(name = "user_apply_time")
    private Date userApplyTime;

    /**
     * 运单号
     */
    @Column(name = "ts_order_no")
    private String tsOrderNo;

    /**
     * 退还金额 单位:元
     */
    @Column(name = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 代调、抽佣钱包收款流水号
     */
    @Column(name = "dispatch_in_flow_no")
    private String dispatchInFlowNo;

    /**
     * 代调、抽佣钱包出款流水号
     */
    @Column(name = "dispatch_out_flow_no")
    private String dispatchOutFlowNo;

    /**
     * 财务审批状态 0.待审批 1.审批通过 2.审批拒绝
     */
    @Column(name = "audit_status")
    private Integer auditStatus;

    /**
     * 审核人员Id
     */
    @Column(name = "audit_user_id")
    private Long auditUserId;

    /**
     * 审核人员姓名
     */
    @Column(name = "audit_user_name")
    private String auditUserName;

    /**
     * 审核人员账户
     */
    @Column(name = "audit_cell_phone")
    private String auditCellPhone;

    /**
     * 审批时间
     */
    @Column(name = "audit_time")
    private Date auditTime;

    /**
     * 钱包类型：0.未知 1.代调钱包 2.抽佣钱包
     */
    @Column(name = "wallet_type")
    private Integer walletType;

    /**
     * 退款状态 1.退款中 2.退款成功 3.退款失败
     */
    @Column(name = "refund_status")
    private Integer refundStatus;

    /**
     * 退款失败原因
     */
    @Column(name = "refund_err_msg")
    private String refundErrMsg;

    /**
     * 是否代调：0.否 1.是
     */
    @Column(name = "is_dispatch")
    private Integer isDispatch;

    /**
     * 三方平台类型 
     * 0:特运通货源订单
     * 1:特运通货源，满帮订单
     * 2:满帮货源，特运通订单
     */
    @Column(name = "thirdparty_platform_type")
    private Integer thirdpartyPlatformType;

    /**
     * 退还技术服务费申请渠道 0完单后投诉 1完单后申请退还入口 默认为0
     */
    @Column(name = "refund_tec_channel")
    private Integer refundTecChannel;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}