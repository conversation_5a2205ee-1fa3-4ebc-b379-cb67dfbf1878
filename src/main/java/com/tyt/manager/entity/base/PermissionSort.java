package com.tyt.manager.entity.base;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "permission_sort")
public class PermissionSort {
    /**
     * 权益ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益一级类型(对应service_permission表的id,共8类)
     */
    @Column(name = "service_permission_id")
    private Integer servicePermissionId;

    /**
     * 权益二级类型(1.时间 2.次数 3.布尔值)
     */
    private Byte type;

    /**
     * 权益二级类型子类型
     */
    @Column(name = "sub_type")
    private Byte subType;

    /**
     * 权益类型对应的唯一标识
     */
    @Column(name = "service_permission_type_id")
    private String servicePermissionTypeId;

    /**
     * 权益类型对应名称（货会员、车会员、发货次数、拨打次数）
     */
    @Column(name = "service_permission_type_name")
    private String servicePermissionTypeName;

    /**
     * 优先级
     */
    private Byte priority;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 权益状态(1.有效 2.无效)
     */
    private Byte status;

    /**
     * 有效期
     */
    @Column(name = "effective_time")
    private Integer effectiveTime;

    /**
     * 单位 （day:天; month:月;year:年;boolean:布尔）
     */
    private String unit;

    /**
     * 使用次数
     */
    @Column(name = "use_num")
    private Integer useNum;
}