package com.tyt.manager.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_deposit_handle_audit")
public class TytDepositHandleAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 保证金申请ID (tyt_deposit_apply_audit表主键Id)
     */
    @Column(name = "apply_audit_id")
    private Long applyAuditId;

    /**
     * 审核状态 0.审核中 1.审核通过 2.审核不通过
     */
    @Column(name = "audit_status")
    private Integer auditStatus;

    /**
     * 审核阶段 0处理阶段 1 一级审核 2 二级审核
     */
    @Column(name = "audit_stage")
    private Integer auditStage;

    /**
     * 审核意见
     */
    @Column(name = "audit_opinion")
    private String auditOpinion;

    /**
     * 审核人员Id
     */
    @Column(name = "audit_user_id")
    private Long auditUserId;

    /**
     * 审核人员姓名
     */
    @Column(name = "audit_user_name")
    private String auditUserName;

    /**
     * 审核人员账户
     */
    @Column(name = "audit_cell_phone")
    private String auditCellPhone;

    /**
     * 审核时间
     */
    @Column(name = "audit_time")
    private Date auditTime;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}