package com.tyt.manager.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_superior_car_sign")
public class TytSuperiorCarSign {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 签约状态：1-正常；2-黑名单；
     */
    private Integer signStatus;
    /**
     * 是否永久（1-否；2-是；）
     */
    private Integer isAlways;
    /**
     * 限制天数
     */
    private Integer restrictNum;
    /**
     * 限制开始时间（处理时间）
     */
    private Date restrictStartTime;
    /**
     * 限制结束时间
     */
    private Date restrictEndTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 签约时间
     */
    private Date signTime;
    /**
     * 修改时间
     */
    private Date updateTime;

    private String handleReason;


    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "handle_reason")
    public String getHandleReason() {
        return handleReason;
    }

    public void setHandleReason(String handleReason) {
        this.handleReason = handleReason;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "sign_status")
    public Integer getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(Integer signStatus) {
        this.signStatus = signStatus;
    }

    @Column(name = "is_always")
    public Integer getIsAlways() {
        return isAlways;
    }

    public void setIsAlways(Integer isAlways) {
        this.isAlways = isAlways;
    }

    @Column(name = "restrict_num")
    public Integer getRestrictNum() {
        return restrictNum;
    }

    public void setRestrictNum(Integer restrictNum) {
        this.restrictNum = restrictNum;
    }

    @Column(name = "restrict_start_time")
    public Date getRestrictStartTime() {
        return restrictStartTime;
    }

    public void setRestrictStartTime(Date restrictStartTime) {
        this.restrictStartTime = restrictStartTime;
    }

    @Column(name = "restrict_end_time")
    public Date getRestrictEndTime() {
        return restrictEndTime;
    }

    public void setRestrictEndTime(Date restrictEndTime) {
        this.restrictEndTime = restrictEndTime;
    }

    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "sign_time")
    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
