package com.tyt.manager.entity.base;

import java.util.Date;
import lombok.*;
import javax.persistence.*;

/**
 * <p>
 * 车辆轨迹校验数据表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-01-24
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "car_track_check_data")
public class CarTrackCheckDataDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源Id（tyt_transport_main表主键Id）
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 完单退款技术服务费审核表Id（tyt_tech_refund_audit表主键Id）
     */
    @Column(name = "tech_refund_audit_id")
    private Long techRefundAuditId;

    /**
     * 车头牌照号码
     */
    @Column(name = "car_head_no")
    private String carHeadNo;

    /**
     * 出发地经度
     */
    @Column(name = "start_longitude")
    private Integer startLongitude;

    /**
     * 出发地纬度
     */
    @Column(name = "start_latitude")
    private Integer startLatitude;

    /**
     * 目的地经度
     */
    @Column(name = "dest_longitude")
    private Integer destLongitude;

    /**
     * 目的地纬度
     */
    @Column(name = "dest_latitude")
    private Integer destLatitude;

    /**
     * 校验开始时间
     */
    @Column(name = "check_begin_time")
    private Date checkBeginTime;

    /**
     * 校验结束时间
     */
    @Column(name = "check_end_time")
    private Date checkEndTime;

    /**
     * 数据来源 1完单后退还技术服务费 2轨迹验证
     */
    @Column(name = "data_source")
    private Integer dataSource;

    /**
     * 是否去过装货地 0-初始化 1是 2否
     */
    @Column(name = "into_load_location")
    private Integer intoLoadLocation;

    /**
     * 是否去过卸货地 0-初始化 1是 2否
     */
    @Column(name = "into_unLoad_location")
    private Integer intoUnloadLocation;

    /**
     * 操作人ID
     */
    @Column(name = "opera_user_id")
    private Long operaUserId;

    /**
     * 操作人名称
     */
    @Column(name = "opera_user_name")
    private String operaUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
