package com.tyt.manager.vo.base;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 信用管理配置实体类
 * 
 * <AUTHOR>
 * @date 2023-04-25下午1:52:13
 * @description
 */
@Data
public class CreditConfigBean {

	private Long id;
	//配置开始时间
	private Date startTime;
	//配置结束时间
	private Date endTime;
	//活动状态 1.启用 2.停用
	private Integer status;
    //通道数量
	private Integer channelNumber;
	//操作人名称
	private String operatorName;
	 //操作人Id
	private Long operatorId;
   //更新时间
	private Date mtime;

	//更新时间
	private Date ctime;

	//是否删除 0未删除 1已删除
	private Integer isDelete;

	//子配置Id
	private Long subId;

	List<CreditConfigSubBean> CreditSubList;



}
