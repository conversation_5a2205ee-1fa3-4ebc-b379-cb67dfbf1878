package com.tyt.manager.service.base.impl;


import com.tyt.manager.bean.ImportSuperiorCarBlackListUserBean;
import com.tyt.manager.bean.SuperiorCarBlackListPushBean;
import com.tyt.manager.entity.base.TytSuperiorCarSign;
import com.tyt.manager.entity.base.TytSuperiorCarSignBlackListLog;
import com.tyt.manager.entity.base.TytSuperiorCarSignLog;
import com.tyt.manager.mapper.base.TytSuperiorCarSignBlackListLogMapper;
import com.tyt.manager.mapper.base.TytSuperiorCarSignLogMapper;
import com.tyt.manager.mapper.base.TytSuperiorCarSignMapper;
import com.tyt.manager.service.base.SuperiorCarSignService;
import com.tyt.manager.service.mq.MessageCenterPushService;
import com.tyt.manager.vo.base.*;
import com.tyt.messagecenter.core.enums.NotifyOpenTypeEnum;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.model.User;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.user.UserService;
import com.tyt.util.TimeUtil;
import com.tyt.util.page.CustomPageHelper;
import com.tyt.util.page.PageData;
import com.tyt.util.page.PageParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

import static com.tyt.common.bean.UserConstants.*;

@Slf4j
@Service("superiorCarSignService")
public class SuperiorCarSignServiceImpl implements SuperiorCarSignService {

    @Autowired
    private TytSuperiorCarSignMapper tytSuperiorCarSignMapper;

    @Autowired
    private TytSuperiorCarSignLogMapper tytSuperiorCarSignLogMapper;

    @Autowired
    private TytSuperiorCarSignBlackListLogMapper tytSuperiorCarSignBlackListLogMapper;

    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Autowired
    private MessageCenterPushService pushService;

    @Autowired
    private UserService userService;

    @Autowired
    private TytConfigService tytConfigService;

    @Override
    public TytSuperiorCarSign getSignByUserId(Long userId) {
        return tytSuperiorCarSignMapper.selectOne(TytSuperiorCarSign.builder().userId(userId).build());
    }

    @Override
    public PageData<SuperiorCarSignListResVo> getSignList(SuperiorCarSignListReqVo reqVo){
        CustomPageHelper pageHelper = CustomPageHelper.startPage(new PageParameter(reqVo.getPageNum(), reqVo.getPageSize()));
        List<SuperiorCarSignListResVo> signList = tytSuperiorCarSignMapper.getSignList(reqVo);
        signList.forEach(sign -> {
            SuperiorCarSignListResVo breakCount = tytSuperiorCarSignMapper.getBreakCount(sign.getUserId());
            SuperiorCarSignListResVo orderCount = tytSuperiorCarSignMapper.getOrderCount(sign.getUserId());
            if (breakCount!=null) {
                sign.setBreakCount(breakCount.getBreakCount());
            }
            if (orderCount!=null) {
                sign.setOrderCount(orderCount.getOrderCount());
            }
        });
        return pageHelper.endPage(signList);
    }

    @Override
    @Transactional(value = "mybatisTransactionManager")
    public void updateStatus(SuperiorCarSignUpdateStatusReqVo reqVo) {
        TytSuperiorCarSign tytSuperiorCarSign = tytSuperiorCarSignMapper.selectByPrimaryKey(reqVo.getSignId());
        if (tytSuperiorCarSign==null||tytSuperiorCarSign.getId()==null)
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "该记录不存在"));

        Date updateTime = new Date();
        ImportSuperiorCarBlackListUserBean bean = new ImportSuperiorCarBlackListUserBean();
        BeanUtils.copyProperties(reqVo,bean);
        bean.setUserId(tytSuperiorCarSign.getUserId());
        TytSuperiorCarSign dao = buildBlackListUserDao(bean,updateTime);
        dao.setId(tytSuperiorCarSign.getId());
        dao.setSignTime(tytSuperiorCarSign.getSignTime());
        //更新拉黑
        tytSuperiorCarSignMapper.updateByPrimaryKey(dao);
        //记录拉黑日志
        TytSuperiorCarSignBlackListLog blackListLogDao = buildBlackListLogDao(dao, updateTime, reqVo.getActionUserName());
        tytSuperiorCarSignBlackListLogMapper.insert(blackListLogDao);
        //记录操作日志
        TytSuperiorCarSignLog logDao = buildCarSignLogDao(dao,reqVo,updateTime);
        tytSuperiorCarSignLogMapper.insert(logDao);
        User user;
        try {
            user = userService.getByUserId(bean.getUserId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        SuperiorCarBlackListPushBean pushBean = buildPushBean(dao, user.getCellPhone());
        //发送push消息
        sendBlackListPush(pushBean);
    }

    @Override
    public SuperiorCarSignDetailResVo getSignDetail(Long signId) {
        SuperiorCarSignListResVo tytSuperiorCarSign = tytSuperiorCarSignMapper.getSignDetailById(signId);
        if (tytSuperiorCarSign==null||tytSuperiorCarSign.getId()==null)
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "该记录不存在"));
        SuperiorCarSignListResVo breakCount = tytSuperiorCarSignMapper.getBreakCount(tytSuperiorCarSign.getUserId());
        SuperiorCarSignListResVo orderCount = tytSuperiorCarSignMapper.getOrderCount(tytSuperiorCarSign.getUserId());
        List<BreakOrderResVo> orders = tytSuperiorCarSignMapper.getBreakOrdersByUserId(tytSuperiorCarSign.getUserId());
        return SuperiorCarSignDetailResVo.builder()
                .id(tytSuperiorCarSign.getId())
                .userId(tytSuperiorCarSign.getUserId())
                .signStatus(tytSuperiorCarSign.getSignStatus())
                .breakCount(breakCount.getBreakCount())
                .orderCount(orderCount.getOrderCount())
                .identityStatus(tytSuperiorCarSign.getIdentityStatus())
                .breakOrderList(orders).build();
    }

    /**
     * @description 优车拉黑处理记录列表查询
     * <AUTHOR>
     * @date 2023-9-8 15:27:16
     * @version 1.0
     */
    @Override
    public PageData<SuperiorCarSignBlackListLogResVo> getBlackListLog(SuperiorCarSignBlackListLogReqVo reqVo) {
        CustomPageHelper pageHelper = CustomPageHelper.startPage(new PageParameter(reqVo.getPageNum(), reqVo.getPageSize()));
        List<SuperiorCarSignBlackListLogResVo> signList = tytSuperiorCarSignBlackListLogMapper.getBlackListLog(reqVo.getUserId());
        return pageHelper.endPage(signList);
    }

    /**
     * @description 批量更新拉黑用户和拉黑记录
     * <AUTHOR>
     * @date 2023-9-11 16:13:48
     * @version 1.0
     */
    @Override
    @Transactional(value = "mybatisTransactionManager")
    public void saveBlackList(List<ImportSuperiorCarBlackListUserBean> userBeans, String operatorName,Date importTime) {
        log.info("saveBlackList method args :{}",userBeans);
        if (CollectionUtils.isNotEmpty(userBeans)) {
            List<TytSuperiorCarSignBlackListLog> logList = new ArrayList<>(userBeans.size());
            List<SuperiorCarBlackListPushBean> pushList = new ArrayList<>(userBeans.size());
            User user;
            SuperiorCarBlackListPushBean pushBean;
            for (ImportSuperiorCarBlackListUserBean bean:userBeans) {
                try {
                    user = userService.getByUserId(bean.getUserId());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                if (user!=null) {
                    TytSuperiorCarSign tytSuperiorCarSign = tytSuperiorCarSignMapper.selectOne(TytSuperiorCarSign.builder().userId(bean.getUserId()).build());
                    TytSuperiorCarSign dao = buildBlackListUserDao(bean,importTime);
                    if (tytSuperiorCarSign!=null) {
                        dao.setId(tytSuperiorCarSign.getId());
                        dao.setSignTime(tytSuperiorCarSign.getSignTime());
                        tytSuperiorCarSignMapper.updateByPrimaryKey(dao);
                    } else {
                        dao.setSignTime(importTime);
                        tytSuperiorCarSignMapper.insertSelective(dao);
                    }
                    pushBean =  buildPushBean(dao,user.getCellPhone());
                    pushList.add(pushBean);
                    logList.add(buildBlackListLogDao(dao,importTime,operatorName));
                }
            }
            sendBlackListPush(pushList);
            tytSuperiorCarSignBlackListLogMapper.insertList(logList);
        }
    }

    private SuperiorCarBlackListPushBean buildPushBean(TytSuperiorCarSign dao,String phone) {
        return SuperiorCarBlackListPushBean.builder().
                userId(dao.getUserId()).
                restrictNum(dao.getRestrictNum()).
                signStatus(dao.getSignStatus()).
                isAlways(dao.getIsAlways()).
                phone(phone).
                build();
    }

    /**
     * @description 黑名单处理发送push消息
     * <AUTHOR>
     * @date 2023-9-12 14:55:24
     * @version 1.0
     */
    private void sendBlackListPush(List<SuperiorCarBlackListPushBean> pushList) {
        threadPoolExecutor.execute(() -> {
            if (CollectionUtils.isNotEmpty(pushList)) {
                for (SuperiorCarBlackListPushBean sign:pushList) {
                    sendPush(sign);
                }
            }
        });
    }

    private void sendPush(SuperiorCarBlackListPushBean pushBean) {
        String content;
        String title;
        if (pushBean.getSignStatus()==SUPERIOR_CAR_SIGN_STATUS_NORMAL) {
            title = "优车接单权益已恢复";
            content = "您（"+pushBean.getPhone()+"）的优车接单权益已恢复，目前可以接取优车货源！为保障您的权益，请您遵守优车服务管控规则。";
        } else if (pushBean.getSignStatus()==SUPERIOR_CAR_SIGN_STATUS_BLACK_LIST
                &&pushBean.getIsAlways()==SUPERIOR_CAR_SIGN_BLACK_LIST_IS_ALWAYS_YES) {
            title = "优车接单权益已受限";
            content = "因您（"+pushBean.getPhone()+"）违反优车服务管控规则，被限制接取优车货源，如有问题请联系客服。";
        } else {
            title = "优车接单权益已受限";
            content = "因您（"+pushBean.getPhone()+"）违反优车服务管控规则，被限制接取优车货源，限制时间为"+pushBean.getRestrictNum()+"天，请您遵守平台规则，避免权益受限，如有问题请联系客服。";
        }

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.setUserIdList(Collections.singletonList(pushBean.getUserId()));
        messagePushBase.setTitle(title);
        messagePushBase.setContent(content);
        messagePushBase.setRemarks(content);
        messagePushBase.setCarPush((short) 1);

        //push消息
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
        notifyMessage.setOpenType(NotifyOpenTypeEnum.app.getCode());
        //站内信
        NewsMessagePush newsMessagePush = NewsMessagePush.createByPushBase(messagePushBase);
        newsMessagePush.setSummary(content);
        pushService.sendMultiMessage(null, newsMessagePush, notifyMessage);
    }

    private void sendBlackListPush(SuperiorCarBlackListPushBean superiorCarSign) {
        ArrayList<SuperiorCarBlackListPushBean> pushList = new ArrayList<>();
        pushList.add(superiorCarSign);
        sendBlackListPush(pushList);
    }

    /**
     * @description 基于拉黑配置组装拉黑DAO实体
     * <AUTHOR>
     * @date 2023-9-11 15:18:02
     * @version 1.0
     */
    private TytSuperiorCarSign buildBlackListUserDao(ImportSuperiorCarBlackListUserBean bean,Date importTime) {
        int status = SUPERIOR_CAR_SIGN_STATUS_BLACK_LIST;
        int isAlways = SUPERIOR_CAR_SIGN_BLACK_LIST_IS_ALWAYS_NO;
        Integer restrictNum = bean.getRestrictNum();
        if (restrictNum == null || restrictNum < -1) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "当前导入异常，请检查导入数据后重试"));
        }
        Date restrictEndTime = null;
        if (restrictNum == -1) {
            isAlways = SUPERIOR_CAR_SIGN_BLACK_LIST_IS_ALWAYS_YES;
        } else if (restrictNum == 0) {
            status = SUPERIOR_CAR_SIGN_STATUS_NORMAL;
        } else {
            restrictEndTime = TimeUtil.addDay(restrictNum);
        }
        return TytSuperiorCarSign.builder().
                userId(bean.getUserId()).
                signStatus(status).
                isAlways(isAlways).
                restrictNum(restrictNum).
                restrictStartTime(importTime).
                restrictEndTime(restrictEndTime).
                remark(bean.getRemark()).
                updateTime(importTime).handleReason(bean.getHandleReason()).
                build();
    }

    /**
     * @description 基于拉黑配置组装拉黑记录DAO实体
     * <AUTHOR>
     * @date 2023-9-11 15:18:02
     * @version 1.0
     */
    private TytSuperiorCarSignBlackListLog buildBlackListLogDao(TytSuperiorCarSign dao,Date importTime, String operatorName) {
        return TytSuperiorCarSignBlackListLog.builder().
                userId(dao.getUserId()).
                isAlways(dao.getIsAlways()).
                restrictNum(dao.getRestrictNum()).
                restrictStartTime(dao.getRestrictStartTime()).
                restrictEndTime(dao.getRestrictEndTime()).
                remark(dao.getRemark()).
                operatorName(operatorName).
                createTime(importTime).
                modifyTime(importTime).
                build();
    }

    /**
     * @description 基于拉黑配置组装管理操作记录DAO实体
     * <AUTHOR>
     * @date 2023-9-12 11:01:51
     * @version 1.0
     */
    private TytSuperiorCarSignLog buildCarSignLogDao(TytSuperiorCarSign dao, SuperiorCarSignUpdateStatusReqVo reqVo,Date updateTime) {
        return TytSuperiorCarSignLog.builder()
                .signId(reqVo.getSignId())
                .action(dao.getSignStatus() == SUPERIOR_CAR_SIGN_STATUS_NORMAL ? SUPERIOR_CAR_SIGN_ACTION_RELIEVE : SUPERIOR_CAR_SIGN_ACTION_BLACKLIST)
                .actionUserId(reqVo.getActionUserId())
                .actionUserName(reqVo.getActionUserName())
                .ctime(updateTime)
                .build();
    }
}
