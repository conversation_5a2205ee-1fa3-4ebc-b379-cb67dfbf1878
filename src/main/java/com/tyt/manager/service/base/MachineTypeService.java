package com.tyt.manager.service.base;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.bean.MachineTypeReq;
import com.tyt.manager.enums.MachineTypeEnum;
import com.tyt.manager.enums.ManageResponseEnum;
import com.tyt.manager.vo.machine.MachineTypeVo;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.page.PageData;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @author: helian
 * @since: 2023/11/04 10:27
 */
public interface MachineTypeService {

    /**
     * 获取类型
     *
     * @return
     */
    MachineTypeEnum getMachineTypeEnum();

    /**
     * 列表
     *
     * @param req
     * @return
     */
    PageData<MachineTypeVo> selectMachineType(MachineTypeReq req);

    /**
     * 添加
     *
     * @param req
     */
    void addMachineType(MachineTypeReq req, EmployeeQueryBean user);

    /**
     * 变更状态
     *
     * @param req
     * @param user
     */
    void updateStatus(MachineTypeReq req, EmployeeQueryBean user);

    /**
     * 删除记录
     *
     * @param req
     */
    void del(MachineTypeReq req);

    /**
     * 编辑
     */
    default void edit(MachineTypeReq req, EmployeeQueryBean user) {
        throw TytException.createException(ManageResponseEnum.request_error.info("该功能暂不支持"));
    }

    /**
     * 导入数据
     */
    default void importExcel(MultipartFile file, EmployeeQueryBean user) throws IOException {
        throw TytException.createException(ManageResponseEnum.request_error.info("该功能暂不支持"));
    }
}
