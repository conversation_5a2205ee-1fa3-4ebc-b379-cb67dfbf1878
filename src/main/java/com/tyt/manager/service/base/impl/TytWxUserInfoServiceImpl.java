package com.tyt.manager.service.base.impl;

import com.tyt.manager.entity.base.TytWxUserInfo;
import com.tyt.manager.mapper.base.TytWxUserInfoMapper;
import com.tyt.manager.service.base.TytWxUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TytWxUserInfoServiceImpl implements TytWxUserInfoService {


    @Autowired
    private TytWxUserInfoMapper tytWxUserInfoMapper;

    @Override
    public TytWxUserInfo getByWxPhone(String phnoe){
        return tytWxUserInfoMapper.getByWxPhone(phnoe);
    }
}
