package com.tyt.manager.service.dictionary.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tyt.infofee.bean.ExcellentGoodsGroupDefaultConfig;
import com.tyt.infofee.bean.ExcellentGoodsGroupRequestBean;
import com.tyt.infofee.bean.ExcellentGoodsGroupResultBean;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.ExcellentGoodsGroup;
import com.tyt.manager.mapper.base.ExcellentGoodsGroupMapper;
import com.tyt.manager.service.dictionary.ExcellentGoodsGroupService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.config.TytConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/6/20
 */
@Service("excellentGoodsGroupService")
public class ExcellentGoodsGroupServiceImpl implements ExcellentGoodsGroupService {

    public static final String EXCELLENT_GOODS_GROUP_DEFAULT_CONFIG = "excellent_goods_group_default_config";

    @Autowired
    private ExcellentGoodsGroupMapper excellentGoodsGroupMapper;

    @Autowired
    private TytConfigService tytConfigService;

    @Override
    public void addGroup(ExcellentGoodsGroupRequestBean requestBean, EmployeeQueryBean curUser) {
        List<ExcellentGoodsGroup> excellentGoodsGroupList = excellentGoodsGroupMapper.selectGroupByName(requestBean.getGroupName());
        if (CollUtil.isNotEmpty(excellentGoodsGroupList)) {
            throw TytException.createException(ResponseEnum.sys_error.info("分组名称不能重复"));
        }
        ExcellentGoodsGroup excellentGoodsGroup = ExcellentGoodsGroup.builder().groupName(requestBean.getGroupName())
                .goodsCount(requestBean.getGoodsCount())
                .type(requestBean.getType())
                .operateId(curUser.getId())
                .operateName(curUser.getName())
                .enabled(0)
                .isDel(1)
                .ctime(new Date())
                .mtime(new Date())
                .callPriceCount(requestBean.getCallPriceCount())
                .callNoPriceCount(requestBean.getCallNoPriceCount())
                .fixedPriceCount(requestBean.getFixedPriceCount()).build();
        excellentGoodsGroupMapper.insert(excellentGoodsGroup);
    }

    @Override
    public PageInfo<ExcellentGoodsGroupResultBean> listGroup(Integer currentPage, Integer pageSize) {
        PageHelper.startPage(currentPage, pageSize);
        List<ExcellentGoodsGroupResultBean> groupResultBeanList = excellentGoodsGroupMapper.selectGroup();
        return new PageInfo<>(groupResultBeanList);
    }

    @Override
    public void editGroup(ExcellentGoodsGroupRequestBean requestBean, EmployeeQueryBean curUser) {
        ExcellentGoodsGroup excellentGoodsGroup = excellentGoodsGroupMapper.selectByPrimaryKey(requestBean.getId());
        if (excellentGoodsGroup != null) {
            if (StringUtils.isNotBlank(requestBean.getGroupName())) {
                excellentGoodsGroup.setGroupName(requestBean.getGroupName());
            }
            if (requestBean.getGoodsCount() != null) {
                excellentGoodsGroup.setGoodsCount(requestBean.getGoodsCount());
            }
            if (requestBean.getType() != null) {
                excellentGoodsGroup.setType(requestBean.getType());
            }

            if (requestBean.getCallPriceCount() != null) {
                excellentGoodsGroup.setCallPriceCount(requestBean.getCallPriceCount());
            }
            if (requestBean.getCallNoPriceCount() != null) {
                excellentGoodsGroup.setCallNoPriceCount(requestBean.getCallNoPriceCount());
            }
            excellentGoodsGroup.setFixedPriceCount(requestBean.getFixedPriceCount());

            excellentGoodsGroup.setOperateId(curUser.getId());
            excellentGoodsGroup.setOperateName(curUser.getName());
            excellentGoodsGroup.setMtime(new Date());
            excellentGoodsGroupMapper.updateByPrimaryKey(excellentGoodsGroup);
        }
    }

    @Override
    public void deleteGroup(Long id, EmployeeQueryBean curUser) {
        ExcellentGoodsGroup excellentGoodsGroup = excellentGoodsGroupMapper.selectByPrimaryKey(id);
        if (excellentGoodsGroup != null) {
            excellentGoodsGroup.setOperateId(curUser.getId());
            excellentGoodsGroup.setOperateName(curUser.getName());
            excellentGoodsGroup.setMtime(new Date());
            excellentGoodsGroup.setIsDel(0);
            excellentGoodsGroupMapper.updateByPrimaryKey(excellentGoodsGroup);
        }
    }

    @Override
    public void usingGroup(Long id, Integer enabled, EmployeeQueryBean curUser) {
        ExcellentGoodsGroup excellentGoodsGroup = excellentGoodsGroupMapper.selectByPrimaryKey(id);
        if (excellentGoodsGroup != null) {
            excellentGoodsGroup.setOperateId(curUser.getId());
            excellentGoodsGroup.setOperateName(curUser.getName());
            excellentGoodsGroup.setMtime(new Date());
            excellentGoodsGroup.setEnabled(enabled);
            excellentGoodsGroupMapper.updateByPrimaryKey(excellentGoodsGroup);
        }
    }

    @Override
    public void updateDefault(ExcellentGoodsGroupRequestBean requestBean, EmployeeQueryBean curUser) {
        ExcellentGoodsGroupDefaultConfig excellentGoodsGroupDefaultConfig = new ExcellentGoodsGroupDefaultConfig();
        BeanUtils.copyProperties(requestBean, excellentGoodsGroupDefaultConfig);
        tytConfigService.updateByKey(JSON.toJSONString(excellentGoodsGroupDefaultConfig), "优车用户分组默认配置", "String", EXCELLENT_GOODS_GROUP_DEFAULT_CONFIG);
    }

    @Override
    public ExcellentGoodsGroupDefaultConfig getDefaultInfo() {
        String excellentGoodsGroupDefaultConfigJsonString = tytConfigService.getStringValue(EXCELLENT_GOODS_GROUP_DEFAULT_CONFIG);
        return JSON.parseObject(excellentGoodsGroupDefaultConfigJsonString, ExcellentGoodsGroupDefaultConfig.class);
    }

}
