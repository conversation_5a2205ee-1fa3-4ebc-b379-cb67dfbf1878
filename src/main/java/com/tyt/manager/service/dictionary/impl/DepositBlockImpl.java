package com.tyt.manager.service.dictionary.impl;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.bean.DepositBlockBranchReq;
import com.tyt.manager.bean.DepositReleaseBranchReq;
import com.tyt.manager.bean.DepositTemplateBean;
import com.tyt.manager.entity.base.TytDepositAuthorization;
import com.tyt.manager.entity.base.TytDepositBlock;
import com.tyt.manager.entity.base.TytDepositBlockLog;
import com.tyt.manager.mapper.base.TytDepositBlockLogMapper;
import com.tyt.manager.mapper.base.TytDepositBlockMapper;
import com.tyt.manager.service.dictionary.DepositAuthorizeService;
import com.tyt.manager.service.dictionary.DepositBlockService;
import com.tyt.manager.service.mq.MessageCenterPushService;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.User;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.user.UserService;
import com.tyt.util.page.CustomPageHelper;
import com.tyt.util.page.PageData;
import com.tyt.util.page.PageParameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/09/07 13:43
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class DepositBlockImpl implements DepositBlockService {

    /**
     * 永封解封时间:2100年月1日
     */
    protected static final Date PERMANENT_BLOCK_END_TIME = new Date(4105147923476L);

    private final TytDepositBlockMapper depositBlockMapper;
    private final TytDepositBlockLogMapper depositBlockLogMapper;
    private final MessageCenterPushService pushService;
    private final UserService userService;
    private final DepositAuthorizeService depositAuthorizeService;

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "mybatisTransactionManager")
    public void blockBatch(DepositBlockBranchReq req, EmployeeQueryBean curUser) {
        blockBatch(req, curUser, true);
    }

    private void blockBatch(DepositBlockBranchReq req, EmployeeQueryBean curUser, boolean sendPush) {
        log.info("优货用户封禁 req: {}, curUser: {}", req, curUser);
        if (Boolean.FALSE.equals(req.getPermanentBlock()) && req.getBlockDuration() == null) {
            throw TytException.createException(ResponseEnum.sys_error.info(), "阶段封禁需要提供封禁天数");
        }

        if (Boolean.TRUE.equals(req.getDelayBlock()) && req.getBlockBeginTime() == null) {
            throw TytException.createException(ResponseEnum.sys_error.info(), "延迟封禁需要提供封禁开始时间");
        }

        for (Long userId : req.getUserIds()) {
            TytDepositBlock existBlock = depositBlockMapper.selectByUserId(userId);
            if (existBlock != null && !Objects.equals(existBlock.getBlockStatus(),
                    BlockStatusEnum.UN_BLOCK.getValue())) {
                // 已被封禁，跳过
                continue;
            }

            TytDepositBlock depositBlock = buildDepositBlock(existBlock, userId, req, curUser);
            saveOrUpdateDeposit(depositBlock, () -> {
                TytDepositBlock newDepositBlock = depositBlockMapper.selectByPrimaryKey(depositBlock.getId());
                if (sendPush) {
                    sendBlockPush(newDepositBlock);
                }
                depositBlockLogMapper.insertSelective(buildDepositBlockLog(newDepositBlock, curUser));
            });
        }
    }

    private void sendBlockPush(TytDepositBlock depositBlock) {
        if (!Objects.equals(BlockStatusEnum.BLOCK.getValue(), depositBlock.getBlockStatus())) {
            // 只有封禁状态才发
            return;
        }

        Boolean permanentBlock = depositBlock.getPermanentBlock();
        int blockDuration =
                (int) TimeUnit.MILLISECONDS.toDays(depositBlock.getBlockEndTime().getTime() - depositBlock.getBlockBeginTime().getTime());

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日HH点");
        Integer blockType = depositBlock.getBlockType();
        String pushSummary = "";
        String smsSummary = "";
        // 永封
        if (Boolean.TRUE.equals(permanentBlock)) {
            if (Objects.equals(blockType, BlockTypeEnum.BUSINESS.getValue())) {
                pushSummary = String.format("用户您好：您因违反平台规则，被限制发布优货权益%s天，权益恢复时间%s，请您遵守平台交易规则，避免违约以确保优车服务正常使用，如您有问题请联系客服：4006688998"
                        , blockDuration, dateFormat.format(depositBlock.getBlockEndTime()));
/*
                smsSummary = "【封禁提醒】用户您好：因违反平台服务管控规则被限制发布优货权益1天，权益恢复时间%s，请您遵守平台规则避免线下交易、客诉纠纷，确保优车服务正常使用，如您有问题请联系客服：4006688998 或请点击特运通货主APP-我的-平台规则-查看《特运通优车服务管控规则》";
*/
                smsSummary = String.format("用户您好：您因违反平台规则，被限制发布优货权益%s天，权益恢复时间%s，请您遵守平台交易规则，避免违约以确保优车服务正常使用，如您有问题请联系客服：4006688998"
                        , blockDuration, dateFormat.format(depositBlock.getBlockEndTime()));
            } else if (Objects.equals(blockType, BlockTypeEnum.VIOLATION.getValue())) {
                pushSummary = "特运通用户您好 ：您因客诉量较高，被限制发布优货，请您遵守平台规则避免客诉纠纷，确保优车服务正常使用，如您有问题请联系客服。";
                smsSummary = String.format("【封禁提醒】用户您好：因违反平台服务管控规则被限制发布优货权益%s天，权益恢复时间%s，请您遵守平台规则避免线下交易、客诉纠纷，确保优车服务正常使用，如您有问题请联系客服：4006688998 或请点击特运通货主APP-我的-平台规则-查看《特运通优车服务管控规则》"
                        ,blockDuration, dateFormat.format(depositBlock.getBlockEndTime()));
            }
        } else {
            if (Objects.equals(blockType, BlockTypeEnum.BUSINESS.getValue())) {
                pushSummary = String.format("用户您好：您因违反平台规则，被限制发布优货权益%s天，权益恢复时间%s，请您遵守平台交易规则，避免违约以确保优车服务正常使用，如您有问题请联系客服：4006688998"
                        , blockDuration, dateFormat.format(depositBlock.getBlockEndTime()));
                smsSummary = String.format("用户您好：您因违反平台规则，被限制发布优货权益%s天，权益恢复时间%s，请您遵守平台交易规则，避免违约以确保优车服务正常使用，如您有问题请联系客服：4006688998"
                        , blockDuration, dateFormat.format(depositBlock.getBlockEndTime()));
            } else if (Objects.equals(blockType, BlockTypeEnum.VIOLATION.getValue())) {
                pushSummary = String.format("特运通用户您好 ：您因客诉量较高，被限制发布优货权益%d天，权益恢复时间%s" +
                                "，请您遵守平台规则避免客诉纠纷，确保优车服务正常使用，如您有问题请联系客服。"
                        , blockDuration, dateFormat.format(depositBlock.getBlockEndTime()));
                smsSummary = String.format("【封禁提醒】用户您好：因违反平台服务管控规则被限制发布优货权益%s天，权益恢复时间%s，请您遵守平台规则避免线下交易、客诉纠纷，确保优车服务正常使用，如您有问题请联系客服：4006688998 或请点击特运通货主APP-我的-平台规则-查看《特运通优车服务管控规则》"
                        ,blockDuration, dateFormat.format(depositBlock.getBlockEndTime()));
            }
        }

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.setUserIdList(Collections.singletonList(depositBlock.getUserId()));
        messagePushBase.setTitle("封禁提醒");
        messagePushBase.setContent(pushSummary);
        messagePushBase.setRemarks(pushSummary);
        messagePushBase.setGoodsPush((short) 1);

        //push消息
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
        //站内信
        NewsMessagePush newsMessagePush = NewsMessagePush.createByPushBase(messagePushBase);
        newsMessagePush.setSummary(pushSummary);
        // 短信
        ShortMessageBean shortMessageBean = null;
        if (Boolean.TRUE.equals(depositBlock.getSmsNotify())) {
            shortMessageBean = new ShortMessageBean();
            shortMessageBean.setContent(smsSummary);
            User user = userService.getCellPhoneByUserId(depositBlock.getUserId());
            if (user != null && StringUtils.isNotBlank(user.getCellPhone())) {
                shortMessageBean.setCellPhone(user.getCellPhone());
            }
        }
        pushService.sendMultiMessage(shortMessageBean, newsMessagePush, notifyMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "mybatisTransactionManager")
    public void releaseBatch(DepositReleaseBranchReq req, EmployeeQueryBean curUser) {
        log.info("优货用户解封 req: {}, curUser: {}", req, curUser);

        req.getUserIds().forEach(it -> {
            TytDepositBlock existBlock = depositBlockMapper.selectByUserId(it);
            if (existBlock == null || Objects.equals(existBlock.getBlockStatus(),
                    BlockStatusEnum.UN_BLOCK.getValue())) {
                throw TytException.createException(ResponseEnum.sys_error.info(), "用户未被封禁");
            }

            String reason = req.getReason();

            if (Objects.equals(BlockStatusEnum.BLOCK.getValue(), existBlock.getBlockStatus())) {
                if (StringUtils.isBlank(reason)) {
                    throw TytException.createException(ResponseEnum.sys_error.info(), "解封原因必填");
                }
                if (StringUtils.length(reason) > 500) {
                    throw TytException.createException(ResponseEnum.sys_error.info(), "解封原因最多500字");
                }
            }

            TytDepositBlock build = TytDepositBlock.builder()
                    .id(existBlock.getId())
                    .reason(reason == null ? "" : reason)
                    .updateTime(new Date())
                    .operateUserId(curUser.getId())
                    .operateUserName(curUser.getUserName())
                    .version(existBlock.getVersion() + 1)
                    .blockStatus(BlockStatusEnum.UN_BLOCK.getValue()).build();
            saveOrUpdateDeposit(build,
                    () -> depositBlockLogMapper.insertSelective(buildDepositBlockLog(depositBlockMapper.selectByUserId(it), curUser)));

            sendReleasePush(it);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "mybatisTransactionManager")
    public int uploadBatch(List<DepositTemplateBean> depositTemplateBean, EmployeeQueryBean curUser) {

        log.info("优货用户导入 req: {}, curUser: {}", depositTemplateBean, curUser);

        int successCount = 0;
        for (DepositTemplateBean templateBean : depositTemplateBean) {

            String blockTypeStr = templateBean.getBlockType();
            String userIdStr = templateBean.getUserId();
            if (!NumberUtils.isNumber(userIdStr) || !NumberUtils.isNumber(blockTypeStr)) {
                continue;
            }

            Integer blockType = Integer.valueOf(blockTypeStr);
            Long userId = Long.valueOf(userIdStr);

            if (Arrays.stream(BlockTypeEnum.values()).noneMatch(it -> Objects.equals(it.getValue(),
                    blockType))) {
                continue;
            }

            try {
                // 不存在的用户不导入
                if (userService.getByUserId(userId) == null) {
                    continue;
                }
            } catch (Exception e) {
                // ignore
            }

            Date now = new Date();
            TytDepositBlock existBlock = depositBlockMapper.selectByUserId(userId);
            TytDepositBlock.TytDepositBlockBuilder builder = TytDepositBlock.builder()
                    .blockStatus(BlockStatusEnum.UN_BLOCK.getValue())
                    .operateUserId(curUser.getId())
                    .blockType(blockType)
                    .reason("")
                    .operateUserName(curUser.getUserName())
                    .updateTime(now);
            if (existBlock == null) {
                // 没有则插入
                builder.userId(userId)
                        .createTime(now)
                        .version(1);
            } else {
                // 更新
                builder.id(existBlock.getId())
                        .version(existBlock.getVersion() + 1);
            }
            saveOrUpdateDeposit(builder.build(),
                    () -> depositBlockLogMapper.insertSelective(buildDepositBlockLog(depositBlockMapper.selectByUserId(userId), curUser)));
            successCount++;
        }

        return successCount;
    }

    @Override
    public PageData<TytDepositBlock> getDepositBlockList(Integer pageNum, Integer pageSize, Integer userType, Long userId) {
        Example example = new Example(TytDepositBlock.class);
        Example.Criteria criteria = example.createCriteria();
        if (userType != null) {
            criteria.andNotEqualTo("blockStatus", BlockStatusEnum.UN_BLOCK.getValue());
            switch (userType) {
                case 1000:
                    // 近90天封禁
                    criteria.andGreaterThan("updateTime", DateUtils.addDays(new Date(), -90));
                    break;
                case 1001:
                    // 运营类封禁
                    criteria.andEqualTo("blockType", BlockTypeEnum.BUSINESS.getValue());
                    break;
                case 1002:
                    // 违约类封禁
                    criteria.andEqualTo("blockType", BlockTypeEnum.VIOLATION.getValue());
                    break;
                default:
                    break;

            }
        }

        if (userId != null) {
            criteria.andEqualTo("userId", userId);
        }

        example.orderBy("updateTime").desc();
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));

        return customPageHelper.endPage(depositBlockMapper.selectByExample(example));
    }

    @Override
    public PageData<TytDepositBlockLog> getDepositBlockLogList(Long depositBlockId, Integer pageNum, Integer pageSize) {
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));

        return customPageHelper.endPage(depositBlockLogMapper.selectByDepositBlockIdDesc(depositBlockId));
    }

    @Override
    public boolean isBlock(Integer blockStatus, Boolean isPermanentBlock, Date blockBeginTime, Date blockEndTime) {
        if (blockStatus == null) {
            return false;
        }

        // 未封禁
        if (DepositBlockService.BlockStatusEnum.UN_BLOCK.getValue() == blockStatus) {
            return false;
        }

        // 永封
        if (Boolean.TRUE.equals(isPermanentBlock)) {
            return true;
        }

        // 没有封禁时间，默认未封禁
        if (blockBeginTime == null || blockEndTime == null) {
            return false;
        }

        Date now = new Date();
        // 在封禁范围内才算被封禁
        return now.before(blockEndTime) && now.after(blockBeginTime);
    }

    @Override
    public List<TytDepositBlock> getScheduleDelayBlockList() {
        Date now = new Date();
        Example example = new Example(TytDepositBlock.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("blockStatus", BlockStatusEnum.DELAY_BLOCK.getValue());
        criteria.andLessThanOrEqualTo("blockBeginTime", now);
        criteria.andGreaterThan("blockBeginTime", DateUtils.addDays(now, -1));

        example.selectProperties("id");

        return depositBlockMapper.selectByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scheduleDelayBlock(Long id) {
        TytDepositBlock depositBlock = depositBlockMapper.selectByPrimaryKey(id);
        Date now = new Date();
        if (depositBlock == null
                || !Objects.equals(depositBlock.getBlockStatus(), BlockStatusEnum.DELAY_BLOCK.getValue())
                || depositBlock.getBlockBeginTime() == null
                || now.before(depositBlock.getBlockBeginTime())) {
            // 只处理到期的延迟封禁
            return;
        }

        TytDepositBlock update = TytDepositBlock.builder().id(id)
                .blockStatus(BlockStatusEnum.BLOCK.getValue())
                .updateTime(now)
                .reason("到达封禁时间，系统自动封禁")
                .version(depositBlock.getVersion() + 1).build();

        saveOrUpdateDeposit(update, () -> {
            TytDepositBlock newDepositBlock = depositBlockMapper.selectByPrimaryKey(id);
            sendBlockPush(newDepositBlock);
            depositBlockLogMapper.insertSelective(buildDepositBlockLog(newDepositBlock, null));
        });
    }

    @Override
    public List<TytDepositBlock> getScheduleReleaseList() {
        Date now = new Date();
        Example example = new Example(TytDepositBlock.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("blockStatus", BlockStatusEnum.BLOCK.getValue());
        criteria.andLessThanOrEqualTo("blockEndTime", now);
        criteria.andGreaterThan("blockEndTime", DateUtils.addDays(now, -1));

        example.selectProperties("id");

        return depositBlockMapper.selectByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "mybatisTransactionManager")
    public void scheduleRelease(Long id) {
        TytDepositBlock depositBlock = depositBlockMapper.selectByPrimaryKey(id);
        Date now = new Date();
        if (depositBlock == null
                || !Objects.equals(depositBlock.getBlockStatus(), BlockStatusEnum.BLOCK.getValue())
                || Boolean.TRUE.equals(depositBlock.getPermanentBlock())
                || depositBlock.getBlockEndTime() == null
                || now.before(depositBlock.getBlockEndTime())) {
            // 只处理到期的延迟封禁
            return;
        }

        TytDepositBlock update = TytDepositBlock.builder().id(id)
                .blockStatus(BlockStatusEnum.UN_BLOCK.getValue())
                .updateTime(now)
                .reason("封禁到期，系统自动解封")
                .version(depositBlock.getVersion() + 1).build();

        saveOrUpdateDeposit(update,
                () -> depositBlockLogMapper.insertSelective(buildDepositBlockLog(depositBlockMapper.selectByPrimaryKey(id), null)));

        sendReleasePush(depositBlock.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "mybatisTransactionManager")
    public Integer syncOldData(EmployeeQueryBean curUser) throws Exception {
        List<TytDepositAuthorization> oldblockList = depositAuthorizeService.getDepositAuthorizes(1);

        if (!oldblockList.isEmpty()) {
            DepositBlockBranchReq req = new DepositBlockBranchReq();
            req.setUserIds(oldblockList.stream().map(TytDepositAuthorization::getUserId).collect(Collectors.toList()));
            req.setReason("黑名单导入");
            req.setBlockType(DepositBlockService.BlockTypeEnum.VIOLATION.getValue());
            req.setPermanentBlock(true);
            req.setDelayBlock(false);
            req.setSmsNotify(false);

            // 封禁
            blockBatch(req, curUser, false);
            // 更新原数据状态
            for (TytDepositAuthorization oldList : oldblockList) {
                depositAuthorizeService.updateBlackStatusById(oldList.getId(), 0, curUser);
            }
        }

        return oldblockList.size();
    }

    private void sendReleasePush(Long userId) {
        String pushSummary = "【优车发货权益恢复通知】您的优车使用权益已恢复，为保障您的权益，请您遵循平台优车使用规则，确保优车服务正常使用";
        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.setUserIdList(Collections.singletonList(userId));
        messagePushBase.setTitle("优车发货权益恢复通知");
        messagePushBase.setContent(pushSummary);
        messagePushBase.setRemarks(pushSummary);
        messagePushBase.setGoodsPush((short) 1);

        //push消息
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
        //站内信
        NewsMessagePush newsMessagePush = NewsMessagePush.createByPushBase(messagePushBase);
        newsMessagePush.setSummary(pushSummary);
        pushService.sendMultiMessage(null, newsMessagePush, notifyMessage);
    }

    private TytDepositBlockLog buildDepositBlockLog(TytDepositBlock depositBlock, EmployeeQueryBean curUser) {
        TytDepositBlockLog depositBlockLog = new TytDepositBlockLog();
        depositBlockLog.setUserId(depositBlock.getUserId());
        depositBlockLog.setDepositBlockId(depositBlock.getId());
        depositBlockLog.setBlockStatus(depositBlock.getBlockStatus());
        depositBlockLog.setBlockType(depositBlock.getBlockType());
        depositBlockLog.setPermanentBlock(depositBlock.getPermanentBlock());
        if (curUser != null) {
            depositBlockLog.setOperateUserId(curUser.getId());
            depositBlockLog.setOperateUserName(curUser.getUserName());
        }
        depositBlockLog.setReason(depositBlock.getReason());
        depositBlockLog.setCreateTime(depositBlock.getUpdateTime());
        depositBlockLog.setVersion(depositBlock.getVersion());
        depositBlockLog.setBlockBeginTime(depositBlock.getBlockBeginTime());
        depositBlockLog.setBlockEndTime(depositBlock.getBlockEndTime());
        return depositBlockLog;
    }

    private void saveOrUpdateDeposit(TytDepositBlock depositBlock,
                                     Runnable actionAfterSaveOrUpdate) {
        if (depositBlock.getId() != null) {
            depositBlockMapper.updateByPrimaryKeySelective(depositBlock);
        } else {
            depositBlockMapper.insertSelective(depositBlock);
        }
        if (actionAfterSaveOrUpdate != null) {
            actionAfterSaveOrUpdate.run();
        }
    }

    private TytDepositBlock buildDepositBlock(TytDepositBlock existBlock, Long userId, DepositBlockBranchReq req,
                                              EmployeeQueryBean curUser) {
        TytDepositBlock depositBlock = new TytDepositBlock();

        BlockStatusEnum blockStatus = Boolean.TRUE.equals(req.getDelayBlock()) ? BlockStatusEnum.DELAY_BLOCK :
                BlockStatusEnum.BLOCK;
        Date now = new Date();

        if (existBlock != null) {
            depositBlock.setVersion(existBlock.getVersion() + 1);
            depositBlock.setId(existBlock.getId());
        } else {
            depositBlock.setVersion(1);
            depositBlock.setCreateTime(now);
            depositBlock.setUserId(userId);
            depositBlock.setDelFlag(false);
        }

        depositBlock.setBlockStatus(blockStatus.getValue());
        depositBlock.setBlockType(req.getBlockType());
        depositBlock.setPermanentBlock(req.getPermanentBlock());
        depositBlock.setOperateUserId(curUser.getId());
        depositBlock.setOperateUserName(curUser.getUserName());
        depositBlock.setBlockBeginTime(req.getBlockBeginTime() == null ? now : req.getBlockBeginTime());
        depositBlock.setBlockEndTime(Boolean.TRUE.equals(req.getPermanentBlock()) ? PERMANENT_BLOCK_END_TIME :
                DateUtils.addDays(depositBlock.getBlockBeginTime(), req.getBlockDuration()));
        depositBlock.setSmsNotify(req.getSmsNotify());
        depositBlock.setReason(req.getReason());
        depositBlock.setUpdateTime(now);

        return depositBlock;
    }

}
