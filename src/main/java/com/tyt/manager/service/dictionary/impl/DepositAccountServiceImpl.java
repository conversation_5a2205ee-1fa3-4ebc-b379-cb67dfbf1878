package com.tyt.manager.service.dictionary.impl;

import com.github.pagehelper.PageHelper;
import com.tyt.enums.AccountTypeEnum;
import com.tyt.enums.AuditStatusEnum;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.bean.DepositHandleAudit;
import com.tyt.manager.bean.DepositHandleAuditDetail;
import com.tyt.manager.entity.base.TytDepositAccount;
import com.tyt.manager.entity.base.TytDepositApplyAudit;
import com.tyt.manager.entity.base.TytDepositFinanceAudit;
import com.tyt.manager.entity.base.TytDepositHandleAudit;
import com.tyt.manager.mapper.base.TytDepositAccountMapper;
import com.tyt.manager.mapper.base.TytDepositApplyAuditMapper;
import com.tyt.manager.mapper.base.TytDepositFinanceAuditMapper;
import com.tyt.manager.mapper.base.TytDepositHandleAuditMapper;
import com.tyt.manager.service.dictionary.DepositAccountService;
import com.tyt.manager.service.dictionary.DepositFinanceAuditService;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.tools.ant.taskdefs.Concat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service("depositAccountService")
public class DepositAccountServiceImpl implements DepositAccountService {

    @Autowired
    private TytDepositAccountMapper depositAccountMapper;

    @Autowired
    private TytDepositApplyAuditMapper depositApplyAuditMapper;

    @Autowired
    private DepositFinanceAuditService depositFinanceAuditService;

    /**
     * 获取用户保证金列表
     *
     * @param userId
     * @return
     * @throws Exception
     */
    @Override
    public List<TytDepositAccount> selectDepositAccountByUserId(Long userId) throws Exception {
        List<TytDepositAccount> depositAccountList = depositAccountMapper.selectDepositAccountByUserId(userId);
        return depositAccountList;
    }

    /**
     * 获取用户保证金对象
     *
     * @param userId
     * @return
     * @throws Exception
     */
    @Override
    public TytDepositAccount selectDepositAccountByUserIdAndType(Long userId, Integer type) throws Exception {
        TytDepositAccount depositAccount = depositAccountMapper.selectDepositAccountByUserIdAndType(userId, type);
        return depositAccount;
    }

    /**
     * 更新用户保证金账户信息
     *
     * @param depositAccount
     * @return
     * @throws Exception
     */
    @Override
    public int updateDepositAccount(TytDepositAccount depositAccount) throws Exception {
        int result = depositAccountMapper.updateByPrimaryKeySelective(depositAccount);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refuseDepositApply(TytDepositFinanceAudit tytDepositFinanceAudit, Integer operateType, EmployeeQueryBean curUser) {
        //更新财务审批表状态
        depositFinanceAuditService.updateFinance(tytDepositFinanceAudit, operateType, curUser);
        // 获取运单号
        TytDepositApplyAudit tytDepositApplyAudit = depositApplyAuditMapper.selectByPrimaryKey(tytDepositFinanceAudit.getApplyAuditId());
        // 更新申请状态
        tytDepositApplyAudit.setFinalAuditStatus(AuditStatusEnum.拒绝.getId());
        tytDepositApplyAudit.setMtime(new Date());
        depositApplyAuditMapper.updateByPrimaryKeySelective(tytDepositApplyAudit);

        // 更新余额,每个用户最多只有两条记录
        List<TytDepositAccount> depositAccountList = depositAccountMapper.selectDepositAccountByUserId(tytDepositApplyAudit.getUserId());
        if (CollectionUtils.isNotEmpty(depositAccountList)) {
            for (TytDepositAccount d : depositAccountList) {
                if (d.getType() == 1) {
                    d.setBalance(d.getBalance().add(tytDepositFinanceAudit.getDepositAmount()));
                } else if (d.getType() == 2) {
                    d.setBalance(d.getBalance().subtract(tytDepositFinanceAudit.getDepositAmount()));
                } else {
                    continue;
                }
                d.setMtime(new Date());
                depositAccountMapper.updateByPrimaryKeySelective(d);
            }
        }


    }
}
