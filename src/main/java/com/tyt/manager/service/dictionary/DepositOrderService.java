package com.tyt.manager.service.dictionary;

import com.github.pagehelper.PageInfo;
import com.tyt.infofee.bean.FinancialDepositQueryBean;
import com.tyt.infofee.bean.FinancialDepositResultBean;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytDepositFinanceAudit;
import com.tyt.returnMoney.bean.MerchantCashBackReqVO;
import com.tyt.returnMoney.bean.MerchantCashBackRspVO;

/**
 * @ClassName：DepositAuthorizeService
 * @Author: TYT
 * @Date: 2023/6/6 13:32
 * @Description:
 */
public interface DepositOrderService {


    void insertOrder(TytDepositFinanceAudit tytDepositFinanceAudit, Integer operateType, EmployeeQueryBean curUser, String orderNo );
}
