package com.tyt.manager.service.dictionary.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.bean.MachineTypeReq;
import com.tyt.manager.commons.constant.TransactionConstant;
import com.tyt.manager.entity.base.TytMachineTypeAudit;
import com.tyt.manager.enums.MachineTypeEnum;
import com.tyt.manager.enums.ManageResponseEnum;
import com.tyt.manager.mapper.base.TytMachineTypeAuditMapper;
import com.tyt.manager.service.base.MachineTypeService;
import com.tyt.manager.service.dictionary.MachineTypeAuditService;
import com.tyt.manager.service.dictionary.MachineTypeBrandNewService;
import com.tyt.manager.service.dictionary.TytNullifyKeywordService;
import com.tyt.manager.vo.machine.MachineTypeVo;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.page.PageData;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @author: helian
 * @since: 2023/11/03 10:43
 */
@Service("machineTypeAuditService")
public class MachineTypeAuditServiceImpl implements MachineTypeAuditService {

    private static final Integer MAX_AUDIT_COUNTS = 3;

    @Autowired
    private MachineTypeBrandNewService machineTypeBrandNewService;

    @Autowired
    private TytNullifyKeywordService tytNullifyKeywordService;

    @Autowired
    private TytMachineTypeAuditMapper tytMachineTypeAuditMapper;


    @Override
    public PageData<MachineTypeVo> selectMachineType(MachineTypeReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<MachineTypeVo> machineTypeVoList = tytMachineTypeAuditMapper.selectMachineType(req);
        return new PageData<>(machineTypeVoList);
    }

    @Override
    public MachineTypeEnum getMachineTypeEnum() {
        return MachineTypeEnum.AUDIT;
    }

    @Override
    public void addMachineType(MachineTypeReq req, EmployeeQueryBean user) {

        TytMachineTypeAudit machineTypeAudit = tytMachineTypeAuditMapper.getMachineTypeAudit(req.getShowName());
        if (machineTypeAudit == null) {
            machineTypeAudit = TytMachineTypeAudit.builder()
                    .showName(req.getShowName())
                    .brandType(req.getBrandType())
                    .topClass(req.getTopClass())
                    .secondClass(req.getSecondClass())
                    .topType(req.getTopType())
                    .status(MachineTypeEnum.AUDIT.code)
                    .conformanceCounts(0)
                    .illegalCounts(0)
                    .createName(user.getRealName())
                    .createTime(new Date())
                    .modifyName(user.getRealName())
                    .modifyTime(new Date())
                    .build();
            tytMachineTypeAuditMapper.insert(machineTypeAudit);
        } else {
            if (MachineTypeEnum.IGNORE.code == machineTypeAudit.getStatus()) {
                machineTypeAudit.setStatus(MachineTypeEnum.AUDIT.code);
                machineTypeAudit.setModifyTime(new Date());
                machineTypeAudit.setModifyName(user.getRealName());
                tytMachineTypeAuditMapper.updateByPrimaryKey(machineTypeAudit);
            }
        }
    }

    @Override
    @Transactional(value = TransactionConstant.MYBATIS_BASE, rollbackFor = Exception.class)
    public void updateStatus(MachineTypeReq req, EmployeeQueryBean user) {
        if (CollUtil.isEmpty(req.getIdList())) {
            return;
        }
        tytMachineTypeAuditMapper.updateStatusByIdList(req.getIdList(), req.getTransformType());
        if (MachineTypeEnum.IGNORE.code == req.getTransformType()) {

        } else if (MachineTypeEnum.CONFORMANCE.code == req.getTransformType()) {

            transformMachineType(machineTypeBrandNewService, req, user);

        } else if (MachineTypeEnum.ILLEGAL.code == req.getTransformType()) {

            transformMachineType(tytNullifyKeywordService, req, user);

        } else {
            throw TytException.createException(ManageResponseEnum.request_error.info("转换类型错误"));
        }
    }

    private void transformMachineType(MachineTypeService machineTypeService, MachineTypeReq req, EmployeeQueryBean user) {
        List<TytMachineTypeAudit> machineTypeAuditList = tytMachineTypeAuditMapper.selectByIdList(req.getIdList());
        if (CollUtil.isEmpty(machineTypeAuditList)) {
            return;
        }
        machineTypeAuditList.forEach(m -> {
            MachineTypeReq machineTypeReq = new MachineTypeReq();
            BeanUtils.copyProperties(m, machineTypeReq);
            if (MachineTypeEnum.CONFORMANCE.code == req.getTransformType()) {

                if (m.getConformanceCounts() != null && m.getConformanceCounts() >= MAX_AUDIT_COUNTS) {
                    machineTypeService.addMachineType(machineTypeReq, user);
                }
            } else if (MachineTypeEnum.ILLEGAL.code == req.getTransformType()) {

                if (m.getIllegalCounts() != null && m.getIllegalCounts() >= MAX_AUDIT_COUNTS) {
                    machineTypeService.addMachineType(machineTypeReq, user);
                }
            }
        });
    }

    @Override
    public void del(MachineTypeReq req) {
        tytMachineTypeAuditMapper.delByIdList(req.getIdList());
    }
}
