package com.tyt.manager.service.dictionary;

import com.github.pagehelper.PageInfo;
import com.tyt.manager.bean.DepositEnrollAgreeReq;
import com.tyt.manager.bean.DepositEnrollListReq;
import com.tyt.manager.entity.base.TytDepositEnroll;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/31 10:29
 */
public interface DepositEnrollService {
    /**
     * 优车报名列表条件查询
     * @param req 参数bean
     * @return PageInfo<TytDepositEnroll>
     */
    PageInfo<TytDepositEnroll> getDepositEnrollList(DepositEnrollListReq req);

    /**
     * 导出优车报名信息
     * @param req 条件bean
     * @return ResultMsgBean
     */
    List<TytDepositEnroll> getExportList(DepositEnrollListReq req)throws Exception ;

    void getDispatchStringCsv(StringBuilder content, List<TytDepositEnroll> list);

    TytDepositEnroll depositEnrollByUserId(Long userId);

    void agree(DepositEnrollAgreeReq req);

    void unAgree(Long idLong);

}
