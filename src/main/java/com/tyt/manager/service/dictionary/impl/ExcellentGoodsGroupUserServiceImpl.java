package com.tyt.manager.service.dictionary.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.tyt.dao.user.UserDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.bean.ExcellentGoodsUserBean;
import com.tyt.manager.entity.base.ExcellentGoodsGroup;
import com.tyt.manager.entity.base.ExcellentGoodsGroupUser;
import com.tyt.manager.enums.ExcellentGoodsGruopEnum;
import com.tyt.manager.mapper.base.ExcellentGoodsGroupMapper;
import com.tyt.manager.mapper.base.ExcellentGoodsGroupUserMapper;
import com.tyt.manager.mapper.base.TytUserMapperNew;
import com.tyt.manager.service.dictionary.ExcellentGoodsGroupUserService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;

@Service
@Slf4j
public class ExcellentGoodsGroupUserServiceImpl extends BaseServiceImpl<ExcellentGoodsGroupUser,Long> implements ExcellentGoodsGroupUserService {

    private static final int pageSize = 50;
    @Autowired
    private ExcellentGoodsGroupUserMapper excellentGoodsGroupUserMapper;
    @Autowired
    private ExcellentGoodsGroupMapper excellentGoodsGroupMapper ;
    @Autowired
    private TytUserMapperNew tytUserMapperNew;
    @Override
    public ResultMsgBean saveExcel(MultipartFile file, EmployeeQueryBean curUser, Long groupId,int type) throws Exception{
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        String excelUrl = renameExcelBase(file, "excel");
        file.transferTo(new File(AppConfig.getProperty("picture.file.path.domain") + excelUrl));
        List<List<String>> importList = CsvReader.csvToList(AppConfig.getProperty("picture.file.path.domain") + excelUrl);
        if(CollectionUtils.isEmpty(importList) || importList.size() > 5000){
                resultMsgBean.setCode(500);
                resultMsgBean.setMsg("[manager_new-发货次数用户导入] 文件内容为空或文件过大！");
                return resultMsgBean;
            }
            int failCount = 0;
            int successCount = 0;
            Date date = new Date();
            // 过滤 -- 重置用户分租
            checkUser(importList,type);
        for(List<String> list : importList){
            ExcellentGoodsGroupUser.ExcellentGoodsGroupUserBuilder builder = new ExcellentGoodsGroupUser().builder();
            boolean result=list.get(0).matches("[0-9]+");
            if(!result){
                failCount++;
                continue;
            }
            Long lValue = Long.valueOf(list.get(0));
            User user = tytUserMapperNew.queryById(lValue);
            if(user == null){
                failCount++;
                continue;
            }
            if(StringUtil.isNotEmpty(user.getTrueName())){
                builder.userName(user.getTrueName());
            }else if(StringUtil.isNotEmpty(user.getUserName())){
                builder.userName(user.getUserName());
            }else {
                builder.userName("用户"+list.get(0));
            }
            builder.userId(lValue);
            builder.groupId(groupId);
            builder.ctime(date);
            builder.mtime(date);
            builder.operateName(curUser.getName());
            builder.operateId(curUser.getId());
            builder.isDel(1);
            excellentGoodsGroupUserMapper.insert(builder.build());
            successCount++;
            }
        resultMsgBean.setCode(200);
        resultMsgBean.setMsg("导入成功人数："+successCount+",失败人数："+failCount);
        return resultMsgBean;

    }

    @Override
    public ResultMsgBean deleteAll(List<Long> userIds) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            excellentGoodsGroupUserMapper.deleteByIds(userIds);
            resultMsgBean.setCode(200);
            resultMsgBean.setMsg("操作成功！");
        } catch (Exception e) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("系统异常！");
            throw new RuntimeException(e);
        }
        return resultMsgBean;
    }

    @Override
    public ResultMsgBean selectUserByCondition(ExcellentGoodsUserBean excellentGoodsUserBean) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        if(excellentGoodsUserBean == null){
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("参数不合法！为空。");
            return resultMsgBean;
        }
        Map<String,Object> map = new HashMap<>();
        excellentGoodsUserBean.setPageIndex((excellentGoodsUserBean.getPageNum()-1)*excellentGoodsUserBean.getPageSize());;
        List<ExcellentGoodsGroupUser> list = excellentGoodsGroupUserMapper.findAllByCondition(excellentGoodsUserBean);
        int count = excellentGoodsGroupUserMapper.selectTotal(excellentGoodsUserBean.getGroupId());
        map.put("list",list);
        map.put("total",count);
        resultMsgBean.setCode(200);
        resultMsgBean.setMsg("查询成功！");
        resultMsgBean.setData(map);
        return resultMsgBean;
    }

    @Override
    public ResultMsgBean selectByGroupId(Long groupId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        ExcellentGoodsUserBean excellentGoodsUserBean = new ExcellentGoodsUserBean();
        excellentGoodsUserBean.setGroupId(groupId);
        List<ExcellentGoodsGroupUser> allByCondition = excellentGoodsGroupUserMapper.findAllByCondition(excellentGoodsUserBean);
        resultMsgBean.setCode(200);
        resultMsgBean.setMsg("查询成功");
        resultMsgBean.setData(allByCondition);
        return resultMsgBean;
    }

    public  void checkUser(List<List<String>> list,int type){

        List<List<List<String>>> partitions = Lists.partition(list, 200);
        List<Long> listTemp = new ArrayList<>();
        for(List<List<String>> listStr : partitions){
            for(List<String> strings : listStr){
                Long aLong = Long.valueOf(strings.get(0));
                listTemp.add(aLong);
            }
            List<ExcellentGoodsGroupUser> excellentGoodsGroupUsers = excellentGoodsGroupUserMapper.findByIds(listTemp);
            if(CollectionUtils.isNotEmpty(excellentGoodsGroupUsers)){
                List<Long> listDeletes = new ArrayList<>();
                for(ExcellentGoodsGroupUser eggu : excellentGoodsGroupUsers){
                    listDeletes.add(eggu.getUserId());
                }
                excellentGoodsGroupUserMapper.deleteByIds(listDeletes);
            }
            listTemp.clear();
        }

    }


}
