package com.tyt.manager.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/7/27 14:58
 * @Version 1.0
 **/
@Data
public class MerchantDepositBackRsp {
    /**
     * 商户代发订单号
     */
    private String merchantTransferOrderNo;

    /**
     * 内部代发交易号
     */
    private String innerTransferOrderNo;

    /**
     * 三方返回的交易号
     */
    private String txnSeqno;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单手续费金额
     */
    private BigDecimal fee;

    /**
     * 交易 token,支付授权令牌，有效期:30分钟 当交易需要二次验证时，需要通过 token 调用交易二次短信验证接口
     */
    private String token;
}
