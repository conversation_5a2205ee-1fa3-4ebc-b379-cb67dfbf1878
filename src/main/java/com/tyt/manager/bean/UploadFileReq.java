package com.tyt.manager.bean;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2025/04/10 17:14
 */
@Data
public class UploadFileReq {

    private Long id;

    private String version;

    private Integer clientType;

    /**
     * 当前为第几块分片
     */
    private Integer chunk;

    /**
     * 当前分片大小
     */
    private Long size;

    /**
     * 当前文件名称
     */
    private String name;

    /**
     * 当前文件的分片对象
     */
    private MultipartFile file;

    private String uuid;


}
