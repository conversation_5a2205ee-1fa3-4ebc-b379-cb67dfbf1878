package com.tyt.manager.utils;

import com.aliyun.cdn20180510.Client;
import com.aliyun.cdn20180510.models.*;
import com.aliyun.tea.TeaModel;
import com.aliyun.teautil.Common;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云 CDN工具类
 * <link><a href="https://help.aliyun.com/zh/cdn/developer-reference/api-cdn-2018-05-10-dir-refresh-and-prefetch/?spm=a2c4g.11186623.help-menu-27099.d_5_4_4_1.57d6739de0nh2Q&scm=20140722.H_479858._.OR_help-T_cn~zh-V_1">阿里云CDN刷新预热</a></link>
 *
 * <AUTHOR>
 * @since 2025/04/15 16:53
 */
@Slf4j
public class CdnUtil {

    /**
     * 创建client
     */
    public static Client createClient() throws Exception {
        String accessKeyId = SystemEnvironmentUtil.getString("ACCESS_KEY");
        String accessKeySecret = SystemEnvironmentUtil.getString("SECRET_KEY");
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = "cdn.aliyuncs.com";
        return new Client(config);
    }

    /**
     * 预热URL
     */
    public static void pushObjectCache(String objectPath) throws Exception {
        PushObjectCacheRequest req = new PushObjectCacheRequest();
        // 加速的文件位置，wdtest.licai.cn为配置的域名,后加加速的文件名
        req.objectPath = objectPath;
        log.info("预热URL-请求参数：{}", Common.toJSONString(TeaModel.buildMap(req)));
        PushObjectCacheResponse resp = createClient().pushObjectCache(req);
        log.info("预热URL-响应结果：{}", Common.toJSONString(TeaModel.buildMap(resp)));
    }


    /**
     * 刷新缓存
     */
    public static void refreshObjectCaches(String objectType, String objectPath) throws Exception {
        RefreshObjectCachesRequest req = new RefreshObjectCachesRequest();
        // 此参数为刷新的类型， 其值可以为File或Directory。默认值：File。
        req.objectType = objectType;
        // 加速的文件位置，wdtest.licai.cn为配置的域名,后加加速的文件名
        req.objectPath = objectPath;
        RefreshObjectCachesResponse resp = createClient().refreshObjectCaches(req);
        log.info("刷新缓存-请求参数：{}", Common.toJSONString(TeaModel.buildMap(req)));
        System.out.println(Common.toJSONString(TeaModel.buildMap(resp)));
        log.info("刷新缓存-响应结果：{}", Common.toJSONString(TeaModel.buildMap(resp)));
    }

    /**
     * 查询刷新预热任务
     */
    public static void describeRefreshTask(String taskId) throws Exception {
        DescribeRefreshTasksRequest req = new DescribeRefreshTasksRequest();
        req.setTaskId(taskId);
        log.info("查询刷新预热任务-请求参数：{}", Common.toJSONString(TeaModel.buildMap(req)));
        DescribeRefreshTasksResponse resp = createClient().describeRefreshTasks(req);
        log.info("查询刷新预热任务-响应结果：{}", Common.toJSONString(TeaModel.buildMap(resp)));
    }

}
