package com.tyt.manager.utils;

import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/20 18:23
 */
@Slf4j
public class TytLogUtil {

    /**
     * 防止出发报警.
     * @param remark remark
     * @param e e
     */
    public static void printErrorInfo(String remark, Exception e){
        if(remark == null){
            remark = "";
        }

        if(e instanceof TytException){
            TytException tytException = (TytException) e;
            log.warn(remark + " " + tytException.getErrorMsg());
        }else {
            log.error(remark, e);
        }

    }

}
