package com.tyt.manager.utils;

import org.dom4j.io.SAXReader;
import org.xml.sax.SAXException;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

/**
 * xml reader util.
 *
 * <AUTHOR>
 * @date 2023/12/4 10:48
 */
public class XmlReaderUtil {

    private XmlReaderUtil() {
    }

    /**
     * 防止xxe.
     */
    private static final String DISALLOW_DOCTYPE_URL = "http://apache.org/xml/features/disallow-doctype-decl";

    /**
     * dom4j xml reader.
     * @return SAXReader
     * @throws SAXException e
     */
    public static SAXReader getSaxReader() throws SAXException {
        SAXReader saxReader = new SAXReader();
        saxReader.setFeature(DISALLOW_DOCTYPE_URL, true);

        return saxReader;
    }


    /**
     * w3c xml DocumentBuilder.
     * 不建议使用该方法，请使用 getSaxReader.
     * @return DocumentBuilder
     * @throws ParserConfigurationException e
     */
    public static DocumentBuilder getDocumentBuilder() throws ParserConfigurationException {
        DocumentBuilderFactory df = DocumentBuilderFactory.newInstance();
        df.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
        df.setAttribute(XMLConstants.ACCESS_EXTERNAL_SCHEMA, "");
        DocumentBuilder builder = df.newDocumentBuilder();

        return builder;
    }

}
