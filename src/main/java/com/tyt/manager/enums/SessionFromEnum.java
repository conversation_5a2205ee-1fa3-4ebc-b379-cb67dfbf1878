package com.tyt.manager.enums;

import lombok.Getter;

/**
 * session传递方式
 *
 * <AUTHOR>
 * @date 2022/12/22 9:38
 */
public enum SessionFromEnum {

    //cookie获取
    cookie(1),

    //header 获取
    header(2);

    @Getter
    private final int sessionFrom;

    SessionFromEnum(int sessionFrom) {
        this.sessionFrom = sessionFrom;
    }

    /**
     * 判断是否相等
     * @param reqSessionFrom
     * @return
     */
    public boolean equalsFrom(Integer reqSessionFrom){
        if(reqSessionFrom != null && reqSessionFrom == this.sessionFrom){
            return true;
        }
        return false;
    }

}
