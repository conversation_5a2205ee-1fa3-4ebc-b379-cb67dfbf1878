package com.tyt.manager.enums;

import lombok.Getter;

/**
 * 接口返回数据类型
 *
 * <AUTHOR>
 * @date 2022/12/22 9:38
 */
public enum AcceptTypeEnum {

    //返回html
    html(1),

    //返回json
    json(2);

    @Getter
    private final int acceptType;

    AcceptTypeEnum(int acceptType) {
        this.acceptType = acceptType;
    }

    /**
     * 判断返回类型是否相等
     * @param reqAcceptType
     * @return
     */
    public boolean equalsType(Integer reqAcceptType){
        if(reqAcceptType != null && reqAcceptType == this.acceptType){
            return true;
        }
        return false;
    }

}
