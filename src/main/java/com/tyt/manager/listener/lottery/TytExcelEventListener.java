package com.tyt.manager.listener.lottery;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tyt.manager.commons.constant.ManagerConstant;
import com.tyt.manager.vo.base.ExcelImportResultVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public abstract class TytExcelEventListener<T> extends AnalysisEventListener<T> {

    /**
     * easy excel 读取发生异常时，会覆盖原始异常，原始异常用该字段保存.
     */
    @Getter
    protected TytException readException;

    @Getter
    protected ExcelImportResultVo excelImportResultVo = new ExcelImportResultVo();

    public void addSuccess(){
        excelImportResultVo.addSuccess();
    }

    public void addError(){
        excelImportResultVo.addError();
    }

    public void addSuccess(int count){
        excelImportResultVo.addSuccess(count);
    }

    public void addError(int count){
        excelImportResultVo.addError(count);
    }

    /**
     * 读excel 过程中是否发生了异常.
     * @return
     */
    public void throwException(Exception e) throws Exception {
        if(readException != null) {
            throw readException;
        } else {
            throw e;
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        super.invokeHeadMap(headMap, context);

        Integer totalRowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
        if(totalRowNumber == null){
            totalRowNumber = 0;
        }

        if(totalRowNumber > 1){
            //去掉表头
            totalRowNumber = totalRowNumber - 1;
        }

        excelImportResultVo.setTotalRow(totalRowNumber);

        if(totalRowNumber > ManagerConstant.EXCEL_MAX_COUNT){
            this.readException = TytException.createException(ResponseEnum.request_error.info("Excel 最大条数不能超过 100000 条！"));
            throw readException;
        }
    }

}
