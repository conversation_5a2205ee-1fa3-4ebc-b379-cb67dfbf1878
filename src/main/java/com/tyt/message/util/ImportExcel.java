package com.tyt.message.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;
import java.util.List;

@Slf4j
public class ImportExcel {

    private ImportExcel() {
    }

    public static void readUserXml(String fileName, List<Long> userIds) {
        boolean isE2007 = false;    //判断是否是excel2007格式
        if (fileName.endsWith("xlsx")) {
            isE2007 = true;
        }

        try (
                InputStream input = new FileInputStream(fileName);
                Workbook wb = ImportExcel.getWorkbook(isE2007, input)
        ) {

            Sheet sheet = wb.getSheetAt(0);     //获得第一个表单
            Iterator<Row> rows = sheet.rowIterator(); //获得第一个表单的迭代器
            while (rows.hasNext()) {
                Row row = rows.next();  //获得行数据
                Iterator<Cell> cells = row.cellIterator();    //获得第一行的迭代器
                while (cells.hasNext()) {
                    Cell cell = cells.next();
                    switch (cell.getCellType()) {   //根据cell中的类型来输出数据
                        case HSSFCell.CELL_TYPE_NUMERIC:
                            userIds.add((long) cell.getNumericCellValue());
                            break;
                        case HSSFCell.CELL_TYPE_STRING:
                            break;
                        case HSSFCell.CELL_TYPE_BOOLEAN:
                            break;
                        case HSSFCell.CELL_TYPE_FORMULA:
                            break;
                        default:
                            break;
                    }
                }
            }
        } catch (IOException ex) {
            log.error("", ex);
        }
    }

    public static Workbook getWorkbook(boolean isE2007, InputStream input) throws IOException {
        Workbook workbook = null;
        //根据文件格式(2003或者2007)来初始化
        if (isE2007) {
            workbook = new XSSFWorkbook(input);
        } else {
            workbook = new HSSFWorkbook(input);
        }
        return workbook;
    }

}
