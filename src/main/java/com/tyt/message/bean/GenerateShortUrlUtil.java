package com.tyt.message.bean;
import java.util.ArrayList;  
import java.util.List;  

import org.apache.http.HttpResponse;  
import org.apache.http.NameValuePair;  
import org.apache.http.client.entity.UrlEncodedFormEntity;  
import org.apache.http.client.methods.HttpPost;  
import org.apache.http.impl.client.DefaultHttpClient;  
import org.apache.http.message.BasicNameValuePair;  
import org.apache.http.util.EntityUtils;  

import com.alibaba.fastjson.JSON;  
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;  

/**  
 * @Title: GenerateShortUrlUtil.java
 * @Package com.tyt.message.bean
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年12月5日
 */
public class  GenerateShortUrlUtil {  
    public static DefaultHttpClient httpclient;  
    static {  
        httpclient = new DefaultHttpClient();  
       // httpclient = (DefaultHttpClient) HttpClientConnectionManager.getSSLInstance(httpclient); // 接受任何证书的浏览器客户端  
    }  
      
    /** 
     * 生成端连接信息 
     *  
     * @author-cp:wangfeng
     */  
    public static String  generateShortUrl(String url) {  
//        try {
//            //HttpPost httpost = new HttpPost("http://dwz.cn/create.php");
//            HttpPost httpost = new HttpPost("http://api.t.sina.com.cn/short_url/shorten.json");
//
//
//            List<NameValuePair> params = new ArrayList<NameValuePair>();
//            //params.add(new BasicNameValuePair("url", url)); // 用户名称
//            params.add(new BasicNameValuePair("url_long", url)); // 用户名称
//            params.add(new BasicNameValuePair("source", "3271760578")); // 用户名称
//
//            httpost.setEntity(new UrlEncodedFormEntity(params,  "utf-8"));
//            HttpResponse response = httpclient.execute(httpost);
//            String jsonStr = EntityUtils
//                    .toString(response.getEntity(), "utf-8");
//            System.out.println(jsonStr);
//          //  JSONObject object = JSON.parseObject(jsonStr);
//           // System.out.println(object.getString("tinyurl"));
//          //  return object.getString("tinyurl");
//            JSONArray jsonArray = JSON.parseObject(jsonStr, JSONArray.class);
//            JSONObject object = jsonArray.getJSONObject(0);
//            System.out.println(object.getString("url_short"));
//            return object.getString("url_short");
//
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "Error";
//        }
          return "";
    }  
      
    /** 
     * 测试生成端连接 
     * @param args   
     * @author-cp:wangfeng
     */  
    public static void main(String []args){  
        generateShortUrl("https://www.teyuntong.com/info_fee_sms.html");
        generateShortUrl("http://help.baidu.com/index?xx=456");  

    }  
}  




