package com.tyt.message.bean;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;

/**
 * 
 * <AUTHOR>
 * @date 2017-7-4上午11:43:07
 * @description
 */
@SuppressWarnings("serial")
public class SendMessageResBean extends ResultMsgBean {
	private List<FailedItemBean> failedItem;

	public SendMessageResBean(int code, String msg) {
		super(code, msg);
	}

	public List<FailedItemBean> getFailedItem() {
		return failedItem;
	}

	public void setFailedItem(List<FailedItemBean> failedItem) {
		this.failedItem = failedItem;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
