package com.tyt.message.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.message.dao.TytMessageTaskDao;
import com.tyt.model.TytMessageTask;

@Repository("tytMessageTaskDao")
public class TytMessageTaskDaoImpl extends BaseDaoImpl<TytMessageTask,Long> implements TytMessageTaskDao  {
	public TytMessageTaskDaoImpl(){
		   this.setEntityClass(TytMessageTask.class);
	   }

}
