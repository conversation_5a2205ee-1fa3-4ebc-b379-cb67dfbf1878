package com.tyt.message.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.message.bean.TytUserMsgBean;
import com.tyt.message.service.TytUserMsgService;
import com.tyt.model.TytUserMsg;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;

@Service("tytUserMsgService")
public class TytUserMsgServiceImpl extends BaseServiceImpl<TytUserMsg, Long> implements TytUserMsgService {

	@Resource(name = "tytUserMsgDao")
	public void setBaseDao(BaseDao<TytUserMsg, Long> tytUserMsgDao) {
		super.setBaseDao(tytUserMsgDao);
	}

	@Resource(name = "userService")
	UserService userService;

	@Override
	public void saveUsers(Long messageId, List<TytUserMsgBean> users,Integer pushPort) {

		for (TytUserMsgBean user : users) {
			TytUserMsg msg = new TytUserMsg();
			msg.setUserId(user.getId());
			msg.setTrueName(user.getTrueName());
			msg.setCellPhone(user.getCellPhone());
			msg.setMsgId(messageId);
			msg.setCtime(new Date());
			msg.setMtime(new Date());
			msg.setDelStatus("0");
			msg.setReadStatus("0");
			msg.setPushPort(pushPort);
			this.add(msg);
		}

	}

	@Override
	public void saveUserIds(Long messageId, List<String> userIds) {
		for (String userId : userIds) {
			TytUserMsg msg = new TytUserMsg();
			msg.setUserId(Long.valueOf(userId));
			msg.setMsgId(messageId);
			msg.setCtime(new Date());
			msg.setMtime(new Date());
			msg.setDelStatus("0");
			msg.setReadStatus("0");
			msg.setPushPort(0);
			this.add(msg);
		}
	}
}
