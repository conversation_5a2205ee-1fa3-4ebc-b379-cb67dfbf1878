package com.tyt.message.service;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.message.bean.MessageTmplAddBean;
import com.tyt.message.bean.MessageTmplBean;
import com.tyt.message.bean.MessageTmplQueryBean;
import com.tyt.model.PageBean;
import com.tyt.model.TytMsgTmpl;
import com.tyt.service.base.BaseService;

/**
 * 定时推送消息服务层
 * 
 * <AUTHOR>
 * @date 2017-4-28上午9:24:12
 * @description
 */
public interface MsgTmplService extends BaseService<TytMsgTmpl, Long> {
	/**
	 * 分页查询模板消息
	 * 
	 * @param page
	 * @param queryBean
	 * @return
	 */
	List<MessageTmplBean> getList(PageBean page, MessageTmplQueryBean queryBean);

	/**
	 * 
	 * @param id
	 */
	void deleteById(Long id);

	/**
	 * 
	 * @param addBean
	 * @param photoFile
	 * @param excelFile
	 * @param curUser
	 * @throws IOException
	 * @throws IllegalStateException
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 */
	void saveTmpl(MessageTmplAddBean addBean, MultipartFile photoFile, MultipartFile excelFile, EmployeeQueryBean curUser) throws IllegalStateException, IOException, IllegalAccessException, InvocationTargetException;

	/**
	 * 发送模板消息
	 * 
	 * @param id
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 * @throws Exception
	 */
	void saveSendMessage(Long id) throws IllegalAccessException, InvocationTargetException, Exception;

	/**
	 * 更新模板状态
	 * 
	 * @param id
	 *            模板id
	 * @param status
	 *            模版状态 0正常 有效 1是无效
	 */
	void updateTmplStatus(Long id, int status);
}
