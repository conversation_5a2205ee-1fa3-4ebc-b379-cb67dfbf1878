package com.tyt.adposition.controller;

import com.tyt.adposition.bean.StartAdvertListBean;
import com.tyt.adposition.service.StartAdertService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.model.*;
import com.tyt.service.config.TytConfigService;
import com.tyt.util.TytSourceUtil;
import com.tyt.web.base.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

@Controller
@RequestMapping("/startAdvert/")
public class StartAdvertController extends BaseController {
    @Resource(name = "startAdertService")
    StartAdertService startAdertService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    /**
     * 开屏广告列表
     * @param status  状态 1.有效 2.无效
     * @param pageBean  分页
     * @param request request
     * @return msgBean
     */
    @RequestMapping("/list")
    @ResponseBody
    public ResultMsgBean startAdvertList(
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "showPosition", required = false) Integer showPosition,
            PageBean pageBean, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            if (pageBean.getPageSize() > 100) {
                pageBean.setPageSize(100);
            }
            List<TytStartAdvert> list = startAdertService.getList(status,showPosition, pageBean);
            HashMap<Object, Object> map=new HashMap<>();
            map.put("list", list);
            map.put("pageNo", pageBean.getCurrentPage());
            map.put("maxPage", pageBean.getMaxPage());
            map.put("pageSize", pageBean.getPageSize());
            map.put("rowCount", pageBean.getRowCount());
            map.put("status", status);
            map.put("showPosition", showPosition);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 保存广告
     * @param startAdvert 参数
     * @param request request
     * @return rm
     */
    @RequestMapping("/save")
    @ResponseBody
    public ResultMsgBean save(
            @RequestBody TytStartAdvert startAdvert, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"保存成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            startAdvert.setUpdateOpId(curUser.getId());
            startAdvert.setUpdateOpName(curUser.getRealName());
            if (startAdvert.getId() == null){
                startAdvert.setCreateOpId(curUser.getId());
                startAdvert.setCreateOpName(curUser.getRealName());
            }
            if (checkParams(startAdvert,rm)){
                startAdertService.saveStartAdvert(startAdvert);
            }
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    private boolean checkParams(TytStartAdvert startAdvert, ResultMsgBean rm) {
        if (startAdvert.getShowPosition() == null){
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("请选择显示位置");
            return false;
        }
        if (StringUtils.isBlank(startAdvert.getTitle())){
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("请填写活动标题");
            return false;
        }
        if (StringUtils.isBlank(startAdvert.getPicUrl())){
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("请上传图文件");
            return false;
        }
//        if (startAdvert.getStartTime() == null){
//            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//            rm.setMsg("请选择开始时间");
//            return false;
//        }
//        if (startAdvert.getEndTime() == null){
//            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//            rm.setMsg("请选择结束时间");
//            return false;
//        }
//        if (startAdvert.getIsNeedLogin() == null){
//            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//            rm.setMsg("请选择是否需要登录");
//            return false;
//        }
//        long startTime = startAdvert.getStartTime().getTime();
//        long endTime = startAdvert.getEndTime().getTime();
//        if (endTime<=startTime){
//            rm.setCode(202);
//            rm.setMsg("活动结束时间要大于开始时间，请修改活动起止时间");
//            return false;
//        }
//        List<TytStartAdvert> startAdverts = startAdertService.getValidList(startAdvert.getId(),startAdvert.getShowPosition());
//        for (TytStartAdvert advert : startAdverts) {
//            if ((advert.getStartTime().getTime()<endTime && advert.getEndTime().getTime()>=endTime)
//                    ||(advert.getStartTime().getTime()<=startTime && advert.getEndTime().getTime()>startTime)
//                    ||(advert.getStartTime().getTime()>=startTime && advert.getEndTime().getTime()<=endTime)){
//                rm.setCode(201);
//                rm.setMsg("当前活动起止时间与其他活动重合，请核对");
//                return false;
//            }
//        }
        return true;
    }

    /**
     * 回显接口
     * @param id
     * @param request
     * @return
     */
    @RequestMapping("/getById")
    @ResponseBody
    public ResultMsgBean getById(
            @RequestParam(value = "id", required = true) Long id, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            TytStartAdvert startAdvertById = startAdertService.getStartAdById(id);
            rm.setData(startAdvertById);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 置为无效
     * @param id
     * @param request
     * @return
     */
    @RequestMapping("/invalid")
    @ResponseBody
    public ResultMsgBean invalid(
            @RequestParam(value = "id", required = true) Long id, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"保存成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            startAdertService.updateStatusById(id,curUser.getId(),curUser.getRealName());
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 获取广告位位置
     * @param request
     * @return
     */
    @RequestMapping("/getSettings")
    @ResponseBody
    public ResultMsgBean getSettings(HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(500);
                return rm;
            }
            List<TytSource> adPositions = TytSourceUtil.getSourceList("start_ad_position");
            HashMap<String, Object> map=new HashMap<String, Object>();
            map.put("adPositions", adPositions);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

}
