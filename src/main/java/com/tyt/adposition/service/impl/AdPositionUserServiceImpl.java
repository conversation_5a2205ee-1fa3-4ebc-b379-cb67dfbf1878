package com.tyt.adposition.service.impl;

import com.alibaba.excel.EasyExcel;
import com.tyt.adposition.bean.ImportUserBean;
import com.tyt.adposition.service.AdPositionService;
import com.tyt.adposition.service.AdPositionUserService;
import com.tyt.dao.base.BaseDao;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.marketingActivity.bean.DelActivityImportBean;
import com.tyt.marketingActivity.service.impl.MarketingUserDelExcelListener;
import com.tyt.model.AdPosition;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytAdPositionUser;
import com.tyt.mybatis.mapper.LotteryUserMapper;
import com.tyt.probdraw.bean.UserVO;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.ApplicationContextUtils;
import com.tyt.util.easyExcel.ImportAdUserExcelListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service("adPositionUserService")
@Slf4j
public class AdPositionUserServiceImpl extends BaseServiceImpl<TytAdPositionUser, Long> implements AdPositionUserService {

    @Resource(name = "adPositionUserDao")
    public void setBaseDao(BaseDao<TytAdPositionUser, Long> adPositionUserDao) {
        super.setBaseDao(adPositionUserDao);
    }

    @Resource(name = "adPositionService")
    AdPositionService adPositionService;

    @Autowired
    private LotteryUserMapper lotteryUserMapper;


    @Override
    public List<TytAdPositionUser> getList(Long adId, Long userId, PageBean pageBean) {
        // 查询条件
        StringBuilder conditionSql = new StringBuilder();
        List<Object> conditionParams=new ArrayList<Object>();
        conditionSql.append(" and ad_id = ?");
        conditionParams.add(adId);
        if (userId!=null) {
            conditionSql.append(" and user_id = ?");
            conditionParams.add(userId);
        }
        // 统计总数
        StringBuilder countSQL = new StringBuilder("SELECT COUNT(*) FROM tyt_ad_position_user where is_delete = 1 ");
        countSQL.append(conditionSql);
        BigInteger rowCount=this.getBaseDao().query(countSQL.toString(), conditionParams.toArray());
        if(rowCount==null||rowCount.longValue()<=0l){
            return new ArrayList<>();
        }
        pageBean.setRowCount(rowCount.longValue());
        // 查询列表
        StringBuilder sql = new StringBuilder("SELECT * FROM `tyt_ad_position_user` where is_delete = 1 ");
        sql.append(conditionSql);
        sql.append(" order by ctime desc limit ? , ?");
        conditionParams.add(pageBean.getOffset());
        conditionParams.add(pageBean.getPageSize());
        return this.getBaseDao().queryForList(sql.toString(), conditionParams.toArray());
    }

    @Override
    public void updateStatus(Long id, String realName) {
        String sql = "UPDATE `tyt_ad_position_user` SET is_delete=2,operater=?,mtime=NOW() where id=?";
        final Object[] params = {realName,id};
        this.getBaseDao().executeUpdateSql(sql, params);
    }

    @Override
    public void saveUser(List<ImportUserBean> userBeans,Long adId,String operater) {
        if (userBeans!=null && userBeans.size()>0){
            Map<String,String>  userGradeMap= new HashMap<>();
            List<String> userIds = new ArrayList<>();
            for (ImportUserBean userBean : userBeans) {
                if (userBean.getUserId()!=null && !userIds.contains(userBean.getUserId().toString())) {
                    userIds.add(userBean.getUserId().toString());
                }
                if (userBean.getUserGrade()!=null && !userGradeMap.containsKey(userBean.getUserId().toString())) {
                    userGradeMap.put(userBean.getUserId().toString(),userBean.getUserGrade().toString());
                }
            }
            final List<UserVO> userList = lotteryUserMapper.listUserIdGroupById(userIds);
            List<UserVO> alreadyExistUseId = getNumByUserIdListAndAdId(userIds, adId);
            userList.removeAll(alreadyExistUseId);
            if(userGradeMap.size()>0){
                for (UserVO userVO:userList){
                    if(userGradeMap.containsKey(String.valueOf(userVO.getId()))) {
                        userVO.setUserGrade(Integer.valueOf(userGradeMap.get(String.valueOf(userVO.getId()))));
                    }
                }
            }
            for (UserVO userBean : userList) {
                TytAdPositionUser user = new TytAdPositionUser();
                user.setUserId(userBean.getId());
                user.setUserGrade(userBean.getUserGrade());
                user.setAdId(adId);
                user.setIsDelete(1);
                user.setOperater(operater);
                user.setCtime(new Date());
                user.setMtime(new Date());
                this.add(user);
            }
        }
    }

    @Override
    public ResultMsgBean saveExcel(MultipartFile fileField, EmployeeQueryBean curUser, Long adId, ResultMsgBean msgBean) {
        AdPosition adPosition = adPositionService.get(adId);
        if (adPosition ==null || !StringUtils.contains(adPosition.getUserGroup(),"4010")){
            msgBean.setCode(500);
            msgBean.setMsg("导入用户需要广告位登录状态为已登录");
            return msgBean;
        }
        //修改广告位为导入用户
        adPositionService.updateAdScope(adId);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            EasyExcel.read(fileField.getInputStream(),
                                                    ImportUserBean.class,
                                                    new ImportAdUserExcelListener(ApplicationContextUtils.getBean("adPositionUserService"),adId,curUser.getName()))
                                                    .sheet()
                                                    .doRead();
                                        }catch (Exception ex){
                                            ex.printStackTrace();
                                        }
                                    }
                                });
        // 关闭线程
        executorService.shutdown();
        msgBean.setMsg("导入成功");
        return msgBean;
    }

    @Override
    public ResultMsgBean updateBatchDelByExcel(AdPositionUserService adPositionUserService,Long adId, MultipartFile fileField, String operate) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            EasyExcel.read(fileField.getInputStream(),
                            DelActivityImportBean.class,
                            new AdPositionDelExcelListener(adId, operate, resultMsgBean, adPositionUserService))
                    .sheet()
                    .doRead();
            log.info("成功条数:" + resultMsgBean.getData());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultMsgBean;
    }

    private List<UserVO> getNumByUserIdListAndAdId(List<String> userIdList,Long adId){
        StringBuffer querySQL = new StringBuffer("SELECT user_id id FROM `tyt_ad_position_user` WHERE 1=1 and is_delete =1");
        Map<String, Object> params = new HashMap<String, Object>();
        querySQL.append(" and ad_id = :adId");
        params.put("adId", adId);
        if (CollectionUtils.isNotEmpty(userIdList)) {
            querySQL.append(" and user_id in (:userIdList)");
            params.put("userIdList", userIdList);
        }
        Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
        map.put("id", Hibernate.LONG);
        return this.getBaseDao().searchByName(querySQL.toString(), map, UserVO.class, params);
    }

    @Override
    @Transactional(value = "transactionManager",propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public void updateBatchUpdateIsdel(Long adId, String userIds, String operater) {
        String sql = "UPDATE `tyt_ad_position_user` SET is_delete=2,operater=?,mtime=NOW() where user_id in(" + userIds + ") and ad_id=?";
        final Object[] params = {operater, adId};
        this.getBaseDao().executeUpdateSql(sql, params);
    }
}
