package com.tyt.recharge.enums;

import com.tyt.withdraw.enums.WithdrawStatusEnum;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/8/25 15:35
 * @Version 1.0
 **/
public enum RechargeStatusEnum {
    充值中(0,"充值中"),
    充值失败(1,"充值失败"),
    充值成功(2,"充值成功");

    private int code;

    private String desc;

    RechargeStatusEnum(int code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(Integer value) {
        RechargeStatusEnum[] rechargeStatusEnums = values();
        for (RechargeStatusEnum rechargeStatusEnum : rechargeStatusEnums) {
            if (rechargeStatusEnum.code==value) {
                return rechargeStatusEnum.desc;
            }
        }
        return null;
    }
}
