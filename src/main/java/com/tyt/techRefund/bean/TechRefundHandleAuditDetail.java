package com.tyt.techRefund.bean;

import com.tyt.manager.entity.base.TytTechRefundAudit;
import com.tyt.manager.entity.base.TytTechRefundHandleAudit;
import com.tyt.model.TytTransportOrders;
import lombok.Data;

import java.util.List;

/**
 * 完单技术服务费退款审核详情
 *
 * <AUTHOR>
 * @since 2024/06/24 15:44
 */
@Data
public class TechRefundHandleAuditDetail {

    /**
     * 退还技术服务费审核对象
     */
    private TytTechRefundAudit techRefundAudit;

    /**
     * 退还技术服务费审核记录列表
     */
    private List<TytTechRefundHandleAudit> techRefundHandleAuditList;

    /**
     * 运单信息
     */
    private TytTransportOrders transportOrders;
}
