package com.tyt.techRefund.enums;

/**
 * 完单退还技术服务费-退款状态枚举类
 *
 * <AUTHOR>
 * @since 2024/07/04 13:19
 */
public enum RefundStatusEnum {

    REFUND_IN(1, "退款中"),

    REFUND_SUCCESS(2, "退款成功"),

    REFUND_FAIL(3, "退款失败");

    private int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    RefundStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
