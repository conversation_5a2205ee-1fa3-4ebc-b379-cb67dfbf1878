package com.tyt.techRefund.enums;

/**
 * 完单退还技术服务费-钱包类型枚举类
 *
 * <AUTHOR>
 * @since 2024/07/04 13:19
 */
public enum WalletTypeEnum {

    DISPATCH_WALLET(1, "代调钱包"),

    DRAW_WALLET(2, "抽佣钱包");

    private int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    WalletTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
