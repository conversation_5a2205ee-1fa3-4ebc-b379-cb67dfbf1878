package com.tyt.techRefund.enums;

/**
 * 完单退还技术服务费-审核状态枚举类
 *
 * <AUTHOR>
 * @since 2024/07/04 13:19
 */
public enum AuditStatusEnum {

    AUDIT_IN(0, "审核中"),

    AUDIT_SUCCESS(1, "审核通过"),

    AUDIT_FAIL(2, "审核不通过");

    private int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    AuditStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
