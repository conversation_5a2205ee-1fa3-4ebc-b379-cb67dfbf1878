package com.tyt.techRefund.controller;

import com.github.pagehelper.PageInfo;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytTechRefundAudit;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.model.TytJurisdiction;
import com.tyt.techRefund.bean.TechRefundHandleAuditDetail;
import com.tyt.techRefund.service.TechRefundService;
import com.tyt.util.RedisLockUtils;
import com.tyt.web.back.internal.bean.RefundTecVoucherBean;
import com.tyt.web.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 完单退技术服务费审核接口
 *
 * <AUTHOR>
 * @since 2024/06/22 16:41
 */
@RestController
@RequestMapping("/tech/refund/handle")
public class TechRefundHandleController extends BaseController {

    @Autowired
    private TechRefundService techRefundService;

    /**
     * 完单退还技术服务费，业务审核请求锁
     */
    public static final String REDIS_TECH_REFUND_HANDLE_AUDIT_LOCK_KEY = "tech:refund:handle:audit:";

    /**
     * 获取完单退还技术服务费审核列表
     *
     * @param page               页码
     * @param size               每页数量
     * @param userApplyStartTime 上报开始时间
     * @param userApplyEndTime   上报结束时间
     * @param auditStartTime     审核开始时间
     * @param auditEndTime       审核结束时间
     * @param tsOrderNo          运单号
     * @param pubCellPhone       发货人账号
     * @param payCellPhone       车主账号
     * @param auditUserName      审核人姓名
     * @param finalAuditStatus   审核状态
     * @param request            http请求对象
     * @return ResultMsgBean
     * <AUTHOR>
     */
    @RequestMapping("/list")
    public ResultMsgBean getTechRefundAuditList(@RequestParam(value = "page", required = true) Integer page, @RequestParam(value = "size", required = true) Integer size, @RequestParam(value = "userApplyStartTime", required = false) Date userApplyStartTime, @RequestParam(value = "userApplyEndTime", required = false) Date userApplyEndTime, @RequestParam(value = "auditStartTime", required = false) Date auditStartTime, @RequestParam(value = "auditEndTime", required = false) Date auditEndTime, @RequestParam(value = "tsOrderNo", required = false) String tsOrderNo, @RequestParam(value = "pubCellPhone", required = false) String pubCellPhone, @RequestParam(value = "payCellPhone", required = false) String payCellPhone, @RequestParam(value = "auditUserName", required = false) String auditUserName, @RequestParam(value = "finalAuditStatus", required = false) Integer finalAuditStatus,@RequestParam(value = "refundTecChannel", required = false) Integer refundTecChannel,  @RequestParam(value = "isYmmGoods", required = false) Integer isYmmGoods, @RequestParam(value = "intoLoadLocation", required = false) Integer intoLoadLocation, @RequestParam(value = "intoUnLoadLocation", required = false) Integer intoUnLoadLocation, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            // 查询完单退还技术服务费审核列表
            List<TytTechRefundAudit> techRefundAuditList = techRefundService.getTechRefundAuditList(page, size, userApplyStartTime, userApplyEndTime, auditStartTime, auditEndTime, tsOrderNo, pubCellPhone, payCellPhone, auditUserName, finalAuditStatus,refundTecChannel,intoLoadLocation,intoUnLoadLocation, isYmmGoods);
            PageInfo<TytTechRefundAudit> pageInfo = new PageInfo<>(techRefundAuditList);

            Map<String, String> parseRequestParams = this.parseRequestParams(request);
            List<TytJurisdiction> menus = (List<TytJurisdiction>) request.getSession().getAttribute("menus" + curUser.getId() + "_" + parseRequestParams.get("menuId"));

            HashMap<Object, Object> map = new HashMap<Object, Object>();
            map.put("pageInfo", pageInfo);
            map.put("menus", menus);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("获取完单退还技术服务费审核列表异常，异常信息为：", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 完单退还技术服务费审核
     *
     * @param id           审核id
     * @param auditOpinion 审核意见
     * @param auditStage   审核阶段 1 一级审核 2 二级审核
     * @param auditStatus  审核状态  1.审核通过 2.审核不通过
     * @param request      http请求对象
     * @return ResultMsgBean
     * <AUTHOR>
     */
    @RequestMapping("/audit")
    public ResultMsgBean updateAuditStatus(@RequestParam(value = "id", required = true) Long id,
                                           @RequestParam(value = "auditOpinion", required = true) String auditOpinion,
                                           @RequestParam(value = "auditStage", required = true) Integer auditStage,
                                           @RequestParam(value = "auditStatus", required = true) Integer auditStatus,
                                           HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "更新成功");
        try {
            if (RedisLockUtils.acquire(REDIS_TECH_REFUND_HANDLE_AUDIT_LOCK_KEY + id + auditStage, 60)) {
                // 获得当前用户的身份
                EmployeeQueryBean curUser = getCurrentUser(request);
                if (curUser == null) {
                    rm.setMsg("未登录");
                    rm.setCode(ReturnCodeConstant.ERROR);
                    return rm;
                }
                // 更新退还技术服务费审核状态
                int result = techRefundService.updateAuditStatus(id, auditOpinion, auditStage, auditStatus, curUser);
                if (result <= 0) {
                    rm.setMsg("更新退还技术服务费审核状态失败");
                    rm.setCode(ReturnCodeConstant.ERROR);
                    return rm;
                }
                rm.setData(result);
            }
        } catch (Exception e) {
            logger.error("完单退还技术服务费审核异常，异常信息为：", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        } finally {
            RedisLockUtils.release(REDIS_TECH_REFUND_HANDLE_AUDIT_LOCK_KEY + id + auditStage);
        }
        return rm;
    }

    /**
     * 完单退还技术服务费审核详情
     *
     * @param id      审核id
     * @param request http请求对象
     * @return ResultMsgBean
     * <AUTHOR>
     */
    @RequestMapping("/auditDetail")
    @ResponseBody
    public ResultMsgBean auditDetail(@RequestParam(value = "id", required = true) Long id, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            // 查询完单技术服务费退款审核详情
            TechRefundHandleAuditDetail techRefundAuditDetail = techRefundService.getTechRefundAuditDetail(id);
            rm.setData(techRefundAuditDetail);
        } catch (Exception e) {
            logger.error("完单退还技术服务费审核详情异常，异常信息为：", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 查看根据完单后退还技术服务费凭证详情信息
     * <AUTHOR>
     * @param id  凭证表Id
     * @param request http请求对象
     * @return ResultMsgBean
     */
    @RequestMapping("/auditVoucherDetail")
    @ResponseBody
    public ResultMsgBean auditVoucherDetail(@RequestParam(value = "id", required = true) Long id, HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            // 查询完单技术服务费退款审核详情
            RefundTecVoucherBean refundTecVoucherBean = techRefundService.getTechRefundAuditVoucherDetail(id);
            rm.setData(refundTecVoucherBean);
        } catch (Exception e) {
            logger.error("完单退还技术服务费审核查看凭证详情异常，异常信息为：", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

}
