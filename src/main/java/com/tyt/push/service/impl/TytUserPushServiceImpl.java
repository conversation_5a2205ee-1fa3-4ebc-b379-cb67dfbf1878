package com.tyt.push.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytUserPush;
import com.tyt.push.service.TytUserPushService;
import com.tyt.service.base.BaseServiceImpl;
@Service("tytUserPushService")
public class TytUserPushServiceImpl extends BaseServiceImpl<TytUserPush, Long> implements
TytUserPushService {
	
	@Resource(name = "tytUserPushDao")
	public void setBaseDao(BaseDao<TytUserPush, Long> tytUserPushDao) {
		super.setBaseDao(tytUserPushDao);
	}

	@Override
	public void updateTytUserPushToUndo(Long jobId) {
		String sql="update tyt_user_push set utime=now(),push_status='4' where job_id=? and (push_status='1' or push_status='0') ";
		this.getBaseDao().executeUpdateSql(sql, new Object[]{jobId});
	
	}
	
}
