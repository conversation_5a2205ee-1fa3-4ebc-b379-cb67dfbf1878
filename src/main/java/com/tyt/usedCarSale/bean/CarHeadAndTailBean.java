package com.tyt.usedCarSale.bean;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CarHeadAndTailBean
 * @Description 二手车车头和挂车实体类
 * <AUTHOR>
 * @Date 2020-01-19 11:02
 * @Version 1.0
 */
@Data
public class CarHeadAndTailBean extends CarSaleBean {

    //出厂时间
    @NotNull(message = "请填写出厂时间后再做发布")
    private Date headFactoryTime;
    //上牌时间
    @NotNull(message = "请填写上牌时间后再做发布")
    private Date headCheckInTime;
    //车头品牌
    @NotBlank(message = "请填写车头品牌后再做发布")
    private String headBrand;
    //车头型号
    @NotBlank(message = "请填写车头型号后再做发布")
    private String headModel;
    //马力
    @NotNull(message = "请填写马力后再做发布")
    private BigDecimal headHorsepower;
    //驱动形式
    @NotBlank(message = "请填写驱动形式后再做发布")
    private String headDriveStyle;
    //排放标准
    @NotBlank(message = "请填写排放标准后再做发布")
    private String headDischargeStandard;
    //驾驶室
    @NotBlank(message = "请选择驾驶室后再做发布")
    private String headCab;
    //行车公里数(公里)
    @NotNull(message = "请填写行车公里数后再做发布")
    private BigDecimal headDriveKilometers;
    //是否有保险 1.是 2.否
    @NotNull(message = "请选择是否有保险后再做发布")
    private Short headInsurance;
    //车头车源所在地省
    @NotBlank(message = "请选择车头车源所在地省后再做发布")
    private String headSourceProvince;
    //车头车源所在地市
    @NotBlank(message = "请选择车头车源所在地市后再做发布")
    private String headSourceCity;
    //车头车源所在地区
    private String headSourceArea;

    //板车品牌
    @NotBlank(message = "请填写板车品牌后再做发布")
    private String tailBrand;
    //板车上牌时间-板车车龄（年）
    @NotNull(message = "请填写板车上牌时间后再做发布")
    private BigDecimal tailAge;
    //板车长度（米）
    @NotNull(message = "请填写板车长度后再做发布")
    private BigDecimal tailLength;
    //板车类型 （tyt_source表tail_car_type）
    @NotBlank(message = "请填写板车类型后再做发布")
    private String tailType;
    //板车种类
    @NotBlank(message = "请填写板车种类后再做发布")
    private String tailKind;
    //平台长度(米)
    @NotNull(message = "请填写平台长度后再做发布")
    private BigDecimal tailPlatformLength;
    //货台面离地高度(厘米)
    @NotNull(message = "请填写货台面离地高度后再做发布")
    private BigDecimal tailMesaToGround;
    //板车自重(吨)
    @NotNull(message = "请填写板车自重后再做发布")
    private BigDecimal tailDeadWeight;
    //保货吨位(吨)
    @NotNull(message = "请填写保货吨位后再做发布")
    private BigDecimal tailLoad;
    //爬梯样式
    @NotBlank(message = "请填写爬梯样式后再做发布")
    private String tailLadderStyle;
    //是否带抽拉 1.是 2.否
    @NotNull(message = "请选择是否带抽拉后再做发布")
    private Short tailHaveDrawing;
    //板车车源所在地省
    @NotBlank(message = "请选择板车车源所在地省后再做发布")
    private String tailSourceProvince;
    //板车车源所在地市
    @NotBlank(message = "请选择板车车源所在地市后再做发布")
    private String tailSourceCity;
    //板车车源所在地区
    private String tailSourceArea;
    @NotNull(message = "请选择是否可拆开售卖后再做发布")
    private Short isBreakSale;

}
