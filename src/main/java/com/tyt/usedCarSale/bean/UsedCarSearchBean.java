package com.tyt.usedCarSale.bean;

public class UsedCarSearchBean {

    private String userName; //发布人
    private String saleCellPhone; //手机号
    private Long id;
    private String headBrand; //车头品牌
    private Integer headHorsepowerMin; //马力小值
    private Integer headHorsepowerMax; //马力大值
    private Integer infoStatus; //发布状态 1.上架 2.下架 3.审核中 4.审核失败
    private String tailBrand; //板车品牌
    private String tailLength; //板车长度
    private String tailType; //板车类型
    private String tailKind; //板车种类

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSaleCellPhone() {
        return saleCellPhone;
    }

    public void setSaleCellPhone(String saleCellPhone) {
        this.saleCellPhone = saleCellPhone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHeadBrand() {
        return headBrand;
    }

    public void setHeadBrand(String headBrand) {
        this.headBrand = headBrand;
    }

    public Integer getHeadHorsepowerMin() {
        return headHorsepowerMin;
    }

    public void setHeadHorsepowerMin(Integer headHorsepowerMin) {
        this.headHorsepowerMin = headHorsepowerMin;
    }

    public Integer getHeadHorsepowerMax() {
        return headHorsepowerMax;
    }

    public void setHeadHorsepowerMax(Integer headHorsepowerMax) {
        this.headHorsepowerMax = headHorsepowerMax;
    }

    public Integer getInfoStatus() {
        return infoStatus;
    }

    public void setInfoStatus(Integer infoStatus) {
        this.infoStatus = infoStatus;
    }

    public String getTailBrand() {
        return tailBrand;
    }

    public void setTailBrand(String tailBrand) {
        this.tailBrand = tailBrand;
    }

    public String getTailLength() {
        return tailLength;
    }

    public void setTailLength(String tailLength) {
        this.tailLength = tailLength;
    }

    public String getTailType() {
        return tailType;
    }

    public void setTailType(String tailType) {
        this.tailType = tailType;
    }

    public String getTailKind() {
        return tailKind;
    }

    public void setTailKind(String tailKind) {
        this.tailKind = tailKind;
    }
}
