package com.tyt.goods.enums;

/**
 * 商品类型
 *
 * <AUTHOR>
 * @since 2023/11/03 17:39
 */
public enum GoodsTypeEnum {

    GOODS_TIMES_PERMISSION(4, "100202", 10020, 2, "发货次数",2),
    CAR_TIME_PERMISSION(7, "100102", 10010, 2, "拨打次数",2),
    EXPOSURE_PERMISSIN(13, "100302", 10030, 2, "曝光次数",2),
    GOODS_VIP_PERMISSION(1, "100201", 10020, 1, "货会员",1),
    CAR_VIP_PERMISSION(2, "100101", 10010, 1, "车会员",1),

    ;



    GoodsTypeEnum(int goodsType, String servicePermissionTypeId, int servicePermissionId, int type, String servicePermissionTypeName, int priority) {
        this.goodsType = goodsType;
        this.servicePermissionTypeId = servicePermissionTypeId;
        this.servicePermissionId = servicePermissionId;
        this.type = type;
        this.servicePermissionTypeName = servicePermissionTypeName;
        this.priority = priority;
    }

    /*
     * 7.找货次卡 4.发货次卡 13.曝光卡
     */
    private int goodsType;
    /*
     * 权益类型对应的唯一标识
     */
    private String servicePermissionTypeId;
    /*
     * 权益一级类型
     */
    private int servicePermissionId;
    /*
     * 权益二级类型(1.时间 2.次数)
     */
    private int type;
    /*
     * 权益类型对应名称
     */
    private String servicePermissionTypeName;

    /**
     * 优先级
     */
    private int priority;


    public static GoodsTypeEnum getByGoodsType(int goodsType) {
        for (GoodsTypeEnum goodsTypeEnum : GoodsTypeEnum.values()) {
            if (goodsTypeEnum.goodsType == goodsType) {
                return goodsTypeEnum;
            }
        }
        return null;
    }

    public int getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(int goodsType) {
        this.goodsType = goodsType;
    }

    public String getServicePermissionTypeId() {
        return servicePermissionTypeId;
    }

    public void setServicePermissionTypeId(String servicePermissionTypeId) {
        this.servicePermissionTypeId = servicePermissionTypeId;
    }

    public int getServicePermissionId() {
        return servicePermissionId;
    }

    public void setServicePermissionId(int servicePermissionId) {
        this.servicePermissionId = servicePermissionId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getServicePermissionTypeName() {
        return servicePermissionTypeName;
    }

    public void setServicePermissionTypeName(String servicePermissionTypeName) {
        this.servicePermissionTypeName = servicePermissionTypeName;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }
}
