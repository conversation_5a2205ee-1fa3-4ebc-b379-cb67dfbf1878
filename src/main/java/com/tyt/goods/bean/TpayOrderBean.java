package com.tyt.goods.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TpayOrderBean
 * @description Tpay收银台下单返回对象
 * @date 2020-05-08 16:29
 */
@Data
public class TpayOrderBean {
    /**
     * 分配的商户ID
     */
    private Long merchantId;
    /**
     * 版本号,固定值：10
     */
    private Integer version;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 安全的用户ID，与user_id可逆
     */
    private String safeUserId;
    /**
     * 时间戳
     */
    private String timestamp;
    /**
     * 平台交易号
     */
    private String innerTradeNo;
    /**
     * 签名字符串
     */
    private String sign;
    /**
     * Tpay收银台url
     */
    private String tpayUrl;
    /**
     * 支付订单ID
     */
    private String orderId;
    /**
     * 是否启用Tpay收银台版本，1:是 2:否
     */
    private String isTpayVersion;
    /**
     * 客户端标识: 1.APP 2.WEB 3.PC
     */
    private Integer sourceSign;
}
