package com.tyt.goods.bean;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName UserBuyGoodsBean
 * @Description 用户购买商品记录对象
 * <AUTHOR>
 * @Date 2019-07-15 11:29
 * @Version 1.0
 */
@Data
public class UserBuyGoodsBean {

    private Long userId;
    private String userName;
    private String cellPhone;
    private Integer goodsType;
    private Integer ordersStatus;
    private String orderId;
    private Date startCtime;
    private Date endCtime;
    private Date refundTimeStart;
    private Date refundTimeEnd;

    /**
     *  开票状态 1.已提交 2.已开票 3.废弃 6:已红冲
     */
    private Integer status;

}
