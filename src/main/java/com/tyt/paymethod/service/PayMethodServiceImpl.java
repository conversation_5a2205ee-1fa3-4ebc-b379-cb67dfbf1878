package com.tyt.paymethod.service;


import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.PayMethod;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.Constant;

@Service("payMethodService")
public class PayMethodServiceImpl extends BaseServiceImpl<PayMethod,Long> implements PayMethodService{

	@Resource(name="payMethodDao")
	public void setBaseDao(BaseDao<PayMethod, Long> payMethodDao) {
	        super.setBaseDao(payMethodDao);
	}

	@Override
	public List<PayMethod> getEnabledList(Integer platId) {
		StringBuffer sql=new StringBuffer();
		if(platId==Constant.PLAT_PC){
			sql.append(" entity.statusWeb=1");
		}else{
			sql.append(" entity.statusPhone=1");
		}
		return getList(sql.toString(),null);
	}

}
