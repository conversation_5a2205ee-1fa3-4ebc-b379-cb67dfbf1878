package com.tyt.bcar.service.impl;


import java.math.BigInteger;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.bcar.service.BcarRecruitUserService;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytBcarRecruitUser;
import com.tyt.service.base.BaseServiceImpl;

@Service("bCarRecruitUserService")
public class BcarRecruitUserServiceImpl extends BaseServiceImpl<TytBcarRecruitUser, Long> implements
		BcarRecruitUserService {

	@Resource(name="bCarRecruitUserDao")
	public void setBaseDao(BaseDao<TytBcarRecruitUser, Long> bCarRecruitUserDao) {
		super.setBaseDao(bCarRecruitUserDao);
	}

	@Override
	public TytBcarRecruitUser getTytBcarRecruitUser(Long userId, Long brId) {
		String sql="from TytBcarRecruitUser where userId=? and brId=?";
		List<TytBcarRecruitUser> truList= this.getBaseDao().find(sql,userId, brId);
		if(truList!=null&& truList.size()>0){
			return truList.get(0);
		}
		return null;
	}

	@Override
	public boolean saveRecruitUser(Long brId, Long userId, int status) {
		if(this.getRecruitUser(brId, userId, status)==null){
			this.add(new TytBcarRecruitUser(brId, userId, status));
			return true;
		}
		return false;
	}
	
	@Override
	public BigInteger getRecruitUser(Long brId, Long userId, Integer status) {
		String selectSQL="SELECT id from tyt_bcar_recruit_user "
				+ "where br_id=? and user_id=? and status=?";
		Object[] params=new Object[]{brId,userId,status};
		return this.getBaseDao().query(selectSQL, params);
	}
	
}
