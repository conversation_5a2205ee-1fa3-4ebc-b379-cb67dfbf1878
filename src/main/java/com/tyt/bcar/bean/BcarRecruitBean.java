package com.tyt.bcar.bean;

import java.util.Date;

public class BcarRecruitBean {

	private String id;
	private String title;
	private String telName;
	private String telephone;
	private String salaryCode;
	private String dutyCode;
	private String province;
	private String city;
	private String county;
	private String remark;
	private Date publishTimeStart;
	private Date publishTimeEnd;
	private String carTypeCode;
	private String cellPhone;
	private String status;
	private String readNbr;


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTelName() {
		return telName;
	}

	public void setTelName(String telName) {
		this.telName = telName;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSalaryCode() {
		return salaryCode;
	}

	public void setSalaryCode(String salaryCode) {
		this.salaryCode = salaryCode;
	}

	public String getDutyCode() {
		return dutyCode;
	}

	public void setDutyCode(String dutyCode) {
		this.dutyCode = dutyCode;
	}

	public String getCarTypeCode() {
		return carTypeCode;
	}

	public void setCarTypeCode(String carTypeCode) {
		this.carTypeCode = carTypeCode;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getPublishTimeStart() {
		return publishTimeStart;
	}

	public void setPublishTimeStart(Date publishTimeStart) {
		this.publishTimeStart = publishTimeStart;
	}

	public Date getPublishTimeEnd() {
		return publishTimeEnd;
	}

	public void setPublishTimeEnd(Date publishTimeEnd) {
		this.publishTimeEnd = publishTimeEnd;
	}

	public String getReadNbr() {
		return readNbr;
	}

	public void setReadNbr(String readNbr) {
		this.readNbr = readNbr;
	}

	/**
	 * update
	 * <p>
	 * add 2 fields
	 * <p>
	 * `display_status`
	 *
	 * <AUTHOR> Chou
	 * @date 2016年6月22日上午10:31:52
	 */
	private Integer displayStatus;
	private Integer collectionNbr;

	public Integer getCollectionNbr() {
		return collectionNbr;
	}

	public void setCollectionNbr(Integer collectionNbr) {
		this.collectionNbr = collectionNbr;
	}

	public Integer getDisplayStatus() {
		return displayStatus;
	}

	public void setDisplayStatus(Integer displayStatus) {
		this.displayStatus = displayStatus;
	}

	private Integer delStatus;

	public Integer getDelStatus() {
		return delStatus;
	}

	public void setDelStatus(Integer delStatus) {
		this.delStatus = delStatus;
	}
}
