package com.tyt.version.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import com.tyt.web.back.internal.bean.TytVersionQueryBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.tyt.dao.base.BaseDao;
import com.tyt.model.TytVersionNew;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.util.Constant;

@Service("tytVersionNewService")
public class TytVersionNewServiceImpl extends BaseServiceImpl<TytVersionNew,Long> implements TytVersionNewService{
    
	
    @Resource(name="tytVersionNewDao")
    public void setBaseDao(BaseDao<TytVersionNew, Long> versionDao) {
        super.setBaseDao(versionDao);
    }

	@Override
	public TytVersionNew getByClient(Integer clientType, String userVersion,
			Integer status) {
		TytVersionNew version=null;
		/*查询条件*/
		String querySql="select * from tyt_version_new where  "
				+ " client_type=? and status=? and ?>=start_version and ?<=end_version ";
		/*配置参数*/
		List<Object> params=new ArrayList<Object>();
		params.add(clientType);
		params.add(status);
		params.add(userVersion);
		params.add(userVersion);
		/*查询结果*/
		List<TytVersionNew>  list=this.getBaseDao().search(querySql, params.toArray(), 1, 1);
		if(list.size()>0){
			version=list.get(0);
		}
		params=null;
		list=null;//使用完毕提醒垃圾回收
		
		/*特殊情况，未知版本处理*/
		if(version==null){
			String speName="pc";
			if(clientType==Constant.PLAT_ANDROID)speName="android";
			if(clientType==Constant.PLAT_IOS)speName="ios";
			
			querySql="select * from tyt_version_new where"
					+ " client_type=? and start_version=?";
			
			List<Object> speParams=new ArrayList<Object>();
			speParams.add(clientType);
			speParams.add(speName);
			List<TytVersionNew>  lis=this.getBaseDao().search(querySql, speParams.toArray(),1,1);
			if(lis.size()>0){
				version=lis.get(0);
			}
			speParams=null;
			lis=null;//使用完毕提醒垃圾回收
		}
		
		return version;
	}

	@Override
	public void updateVersion(Long id, Integer status) {
		/*变更之前的信息为无效*/
		String sql="update tyt_version_new set status=?,update_time=? where id=? ";
		List<Object> params=new ArrayList<Object>();
		params.add(status);
		params.add(new Date());
		params.add(id);
		this.executeUpdateSql(sql, params.toArray());
		
	}

	@Override
	public void addVersion(TytVersionNew version) {
		/*新增一条信息*/
		version.setCreateTime(new Date());
		version.setUpdateTime(new Date());
		version.setAppMarket(0);
		version.setDownloadType("1");
		this.add(version);
	}

	@Override
	public TytVersionNew getVersionByRange(TytVersionNew version) {
		/*查询条件*/
		String querySql="select * from tyt_version_new where "
				+ " ((?>=start_version and ?<=end_version) or (?>=start_version and ?<=end_version)"
				+ " or (?<=start_version and ?>=end_version))"
				+ "  and status=? and client_type=?";
		/*配置参数*/
		List<Object> params=new ArrayList<Object>();
		
		params.add(version.getStartVersion());
		params.add(version.getStartVersion());
		
		params.add(version.getEndVersion());
		params.add(version.getEndVersion());
		
		params.add(version.getStartVersion());
		params.add(version.getEndVersion());
		
		params.add(1);
		params.add(version.getClientType());
		/*查询结果*/
		List<TytVersionNew>  list=this.getBaseDao().search(querySql, params.toArray(), 1, 1);
		if(!list.isEmpty()){
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<TytVersionNew> getVersionList(String clientType, String status) {
		//sql语句
		StringBuilder sql = new StringBuilder("select * from tyt_version_new where 1=1 ");
		//条件拼接
		List<Object> params = new ArrayList<>();
		//搜索条件
		if (StringUtils.isNotBlank(clientType)) {
			sql.append(" and client_type=?");
			params.add(clientType);
		}
		if (StringUtils.isNotBlank(status)) {
			sql.append(" and status=?");
			params.add(status);
		}
		sql.append(" order by start_version desc");
		//获取结果集
		return this.getBaseDao().search(sql.toString(), params.toArray(), 1, 100);
	}

	@Override
	public void updateMarketStatus(Long id, Integer marketStatus) {
		String sql="update tyt_version_new set app_market=?,update_time=? where id=? ";
		List<Object> params=new ArrayList<Object>();
		params.add(marketStatus);
		params.add(new Date());
		params.add(id);
		this.executeUpdateSql(sql, params.toArray());
	}

}
