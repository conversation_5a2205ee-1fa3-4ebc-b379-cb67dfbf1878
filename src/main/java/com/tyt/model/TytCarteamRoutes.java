package com.tyt.model;


import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

@Entity
@Table(name = "tyt_carteam_routes", catalog = "tyt")
public class TytCarteamRoutes implements java.io.Serializable {
	private static final long serialVersionUID = 891324359245708215L;

	private Long id;
	private Byte routesSource;
	private Long relaId;
	private String startProv;
	private String startCity;
	private String startArea;
	private String startFull;
	private String destProv;
	private String destCity;
	private String destArea;
	private String destFull;
	private Integer carNum;
	private Byte status;
	private Byte isDel;
	private Long createId;
	private String createName;
	private Long modifyId;
	private String modifyName;
	private Date ctime;
	private Date utime;

	public TytCarteamRoutes() {
	}

	public TytCarteamRoutes(Date utime) {
		this.utime = utime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "routes_source")
	public Byte getRoutesSource() {
		return this.routesSource;
	}

	public void setRoutesSource(Byte routesSource) {
		this.routesSource = routesSource;
	}

	@Column(name = "rela_id")
	public Long getRelaId() {
		return this.relaId;
	}

	public void setRelaId(Long relaId) {
		this.relaId = relaId;
	}

	@Column(name = "start_prov")
	public String getStartProv() {
		return this.startProv;
	}

	public void setStartProv(String startProv) {
		this.startProv = startProv;
	}

	@Column(name = "start_city")
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area")
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "start_full")
	public String getStartFull() {
		return this.startFull;
	}

	public void setStartFull(String startFull) {
		this.startFull = startFull;
	}

	@Column(name = "dest_prov")
	public String getDestProv() {
		return this.destProv;
	}

	public void setDestProv(String destProv) {
		this.destProv = destProv;
	}

	@Column(name = "dest_city")
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area")
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "dest_full")
	public String getDestFull() {
		return this.destFull;
	}

	public void setDestFull(String destFull) {
		this.destFull = destFull;
	}

    @Column(name = "car_num")
    public Integer getCarNum() {
        return carNum;
    }

    public void setCarNum(Integer carNum) {
        this.carNum = carNum;
    }

    @Column(name = "status")
	public Byte getStatus() {
		return this.status;
	}

	public void setStatus(Byte status) {
		this.status = status;
	}

	@Column(name = "is_del")
	public Byte getIsDel() {
		return this.isDel;
	}

	public void setIsDel(Byte isDel) {
		this.isDel = isDel;
	}

	@Column(name = "create_id")
	public Long getCreateId() {
		return this.createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	@Column(name = "create_name")
	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	@Column(name = "modify_id")
	public Long getModifyId() {
		return this.modifyId;
	}

	public void setModifyId(Long modifyId) {
		this.modifyId = modifyId;
	}

	@Column(name = "modify_name")
	public String getModifyName() {
		return this.modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", nullable = false)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
