package com.tyt.model;

/**
 * 返回给客户端 code码定义类
 * 
 * <AUTHOR>
 * 
 */
public class ReturnCodeConstant {

	/**** 正确结果 **/
	public static final int OK = 200;

	/**** 错误结果 **/
	public static final int ERROR = 500;

	/**
	 * 基础参数错误
	 */
	public static final int BASIC_PARAMETER_ERROR = 1001;

	/**
	 * 无效签名 code
	 */
	public static final int INVALID_SIGNATURE_CODE = 1002;
	/**
	 * 无效签名 message
	 */
	public static final String INVALID_SIGNATURE_MSG = "无效签名";

	/**
	 * 未登录 code
	 */
	public static final int NOT_LOGGED_IN_CODE = 1003;
	/**
	 * 未登录 message
	 */
	public static final String NOT_LOGGED_IN_MSG = "未登录";
	/**
	 * 注册错误 code
	 */
	public static final int REGISTER_ERROR_CODE = 2001;

	/**
	 * 登陆错误 code
	 */
	public static final Integer LOGIN_ERROR_CODE = 2002;
	/**
	 * 请求参数为空 code
	 */
	public static final int ARGUMENTS_IS_NULL_CODE = 2003;
	/**
	 * 类型错误 code
	 */
	public static final int TYPE_ERROR_CODE = 2004;
	/**
	 * 图片错误 code
	 */
	public static final int IMAGE_ERROR_CODE = 2005;
	/**
	 * 客户端升级 code
	 */
	public static final int VERSION_UPGADE_CODE = 2006;
	/**
	 * 重复信息code
	 */
	public static final int DATA_HAS_EXIT = 2007;
	/**
	 * 全局验证错误
	 */
	public static final Integer INFO_REFUSE_UPLOAD_CODE = 2008;
	/**
	 * 对象不存在code
	 */
	public static final int OBJECT_IS_NOT_EXIT_CODE = 2009;
	/**
	 * 其它错误code
	 */
	public static final int OTHER_ERROR = 3001;
	/**
	 * 超过限制
	 */
	public static final int MORE_THAN_LIMIT = 3002;
	/**
	 * 验证码超时
	 */
	public static final int VERIFICATION_CODE_TIMEOUT = 3003;
	/**
	 * 调用短信平台发送验证码失败
	 */
	public static final int CALL_MESSAGE_PLAT_FAILED = 3004;
	/**
	 * 其他客户端登录code
	 */
	public static final int REMOTE_LOGIN_CODE = 1004;
	/**
	 * 不允许删除
	 */
	public static final int NOT_ALLOWED_DELETE = 4000;
	/**
	 * 不能撤销
	 */
	public static final int INFO_FEE_NOT_ALLOWED_CANCEL = 5000;//货物信息不允许撤销
	public static final int INFO_FEE_NOT_ALLOWED_DEAL = 5001;//货物信息不允许设置成交
	public static final int INFO_FEE_NOT_ALLOWED_TO_PAY = 5002;//货物信息不允许支付
	public static final int INFO_FEE_FAIL_TO_MAKE_ORDER = 5003;//下单失败
	public static final int INFO_FEE_OPERATE_DATA_DISABLED = 5004;//数据无效，不能进行再此操作
	public static final int INFO_FEE_ONLY_AGREE_ONE_CAROWNER = 8000;//数据无效，不能进行再此操作
	/**
	 * 其他客户端登录message
	 */
	public static final String REMOTE_LOGIN_MSG = "其他客户端登录";
	public static final String ARGUMENTS_CELLPHONE_IS_NULL_MSG = "账号不能为空";
	public static final String ARGUMENTS_TEL_IS_NULL_MSG = "联系人电话不能为空";
	public static final String ARGUMENTS_PASSWORD_IS_NULL_MSG = "密码不能为空";
	public static final String ARGUMENTS_USERSIGN_IS_NULL_MSG = "用户身份不能为空";
	public static final String ARGUMENTS_ADVICE_TITLE_IS_NULL_MSG = "反馈标题不能为空";
	public static final String ARGUMENTS_ADVICE_CONTENT_IS_NULL_MSG = "反馈内容不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_IDENTITY_IS_NULL_MSG = "身份证号码不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_REALNAME_IS_NULL_MSG = "用户真实姓名不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_MAINURL_IS_NULL_MSG = "用户身份证正面照不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_BACKURL_IS_NULL_MSG = "用户身份证反面照不能为空";
	public static final String ARGUMENTS_ID_IS_NULL_MSG = "ID不能为空";
	public static final String ARGUMENTS_DISTANCE_IS_NULL_MSG = "两地间得间的距离不能为空号";
	public static final String ARGUMENTS_STARTCOORDX_IS_NULL_MSG = "出发地X坐标为空";
	public static final String ARGUMENTS_STARTCOORDY_IS_NULL_MSG = "出发地Y坐标为空";
	public static final String ARGUMENTS_DESTCOORDX_IS_NULL_MSG = "目的地X坐标为空";
	public static final String ARGUMENTS_DESTCOORDY_IS_NULL_MSG = "目的地Y坐标为空";
	public static final String ARGUMENTS_TASKCONTENT_IS_NULL_MSG = "货物内容不能为空";
	public static final String ARGUMENTS_PUBTIME_IS_NULL_MSG = "pubTime不能为空";
	public static final String ARGUMENTS_PUBDATE_IS_NULL_MSG = "pubDate不能为空";
	public static final String ARGUMENTS_STARTLONGITUDE_IS_NULL_MSG = "出发地经度不能为空";
	public static final String ARGUMENTS_STARTLATITUDE_IS_NULL_MSG = "出发地纬度不能为空";
	public static final String ARGUMENTS_DESTLATITUDE_IS_NULL_MSG = "目的地纬度不能为空";
	public static final String ARGUMENTS_DESTLONGITUDE_IS_NULL_MSG = "目的地经度不能为空";

	public static final int INVALID_VERIFY_CODE = 3005;

	public static final int VISIT_TOO_FREQUENT = 6001;

	public static final Integer NOT_ALLOW = 7001;

	public static final Integer NULLIFY_NOT_ALLOW_PUBLISH = 7002;
	//无效货源备注过滤返回码
	public static final Integer NULLIFY_REMARK_NOT_ALLOW_PUBLISH = 7003;

	//车辆开关开启
	public static final Integer NOT_IS_VIP = 8001;
	
	public static final Integer AUTH_FAIL = 8002;
	
	public static final Integer AUTH_ISGOING = 8003;
	
	public static final Integer NO_SET_PREFER = 8004;

	//定向升级创建任务 excel导入数据异常code
	public static final Integer EXCEL_DATA_ERROR = 9001;
	//定向升级创建任务 用户ID重复
	public static final Integer USER_ID_REPEAT = 9002;

	//该记录和已启用状态的周期时间有交叠
	public static final Integer TIME_ID_REPEAT = 9003;

	//excel导入行数超过最大限制
	public static final Integer EXCEL_ROW_EXCEED_MAXIMUM_LIMIT = 9004;




	

}
