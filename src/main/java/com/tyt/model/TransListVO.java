package com.tyt.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TransListVO extends TransportMain{

    private String enterpriseName;

    /**
     * 开票货源指派车方联系方式
     */
    private String assignCarTel;

    private String priceCount;

    private Long enterpriseId;

    private Long invoiceSubjectId;
    /**
     * 用车类型：1-整车，2-零担
     */
    private String useCarType;
    /**
     * 预付金额
     */
    private BigDecimal prepaidPrice;
    /**
     * 到付金额
     */
    private BigDecimal collectedPrice;
    /**
     * 回单付金额
     */
    private BigDecimal receiptPrice;

    /**
     * 同步YMM结果 已同步、同步失败、未同步
     */
    private String syncYMMResult;


}
