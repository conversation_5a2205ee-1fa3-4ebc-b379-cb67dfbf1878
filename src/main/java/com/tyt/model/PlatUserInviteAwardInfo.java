package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 老用户拉新充值活动model
 * <AUTHOR>
 *
 */
@Entity
@Table(name="plat_user_invite_award_info")
public class PlatUserInviteAwardInfo implements Serializable{

	private static final long serialVersionUID = -4143743200676729039L;
	private Long id;//bigint(20) NOT NULL
	private Long userId;//bigint(20) NULL用户ID
	private Long inviteUserId;  //被邀请人用户ID
	private String inviteCellPhone; //被邀请人电话
	private String inviteUserName;  //被邀请人用户名
	private Integer awardNumber; //奖励次序
	private Integer awardDays;//int(11) NULL会员奖励天数，非会员为0
	private Integer userState;//int(11) NULL1：非会员 2：会员
	private Date awardBeforeDate; //奖励前到期日期
	private Date awardAfterDate; //奖励后到期日期
	private Integer firstMsgState; //首次登录发送消息状态
	private Integer executeCount; //定时任务执行次数
	private String failReason; //定时任务失败原因
	private Date ctime;//datetime NULL
	private Date payTime; //发放奖励日期
	private Date mtime; //修改时间
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="award_days")
	public Integer getAwardDays() {
		return awardDays;
	}
	public void setAwardDays(Integer awardDays) {
		this.awardDays = awardDays;
	}
	@Column(name="user_state")
	public Integer getUserState() {
		return userState;
	}
	public void setUserState(Integer userState) {
		this.userState = userState;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name="invite_user_id")
	public Long getInviteUserId() {
		return inviteUserId;
	}

	public void setInviteUserId(Long inviteUserId) {
		this.inviteUserId = inviteUserId;
	}
	@Column(name="invite_cell_phone")
	public String getInviteCellPhone() {
		return inviteCellPhone;
	}

	public void setInviteCellPhone(String inviteCellPhone) {
		this.inviteCellPhone = inviteCellPhone;
	}
	@Column(name="invite_user_name")
	public String getInviteUserName() {
		return inviteUserName;
	}

	public void setInviteUserName(String inviteUserName) {
		this.inviteUserName = inviteUserName;
	}

	@Column(name="award_number")
	public Integer getAwardNumber() {
		return awardNumber;
	}
	public void setAwardNumber(Integer awardNumber) {
		this.awardNumber = awardNumber;
	}

	@Column(name="award_before_date")
	public Date getAwardBeforeDate() {
		return awardBeforeDate;
	}

	public void setAwardBeforeDate(Date awardBeforeDate) {
		this.awardBeforeDate = awardBeforeDate;
	}
	@Column(name="award_after_date")
	public Date getAwardAfterDate() {
		return awardAfterDate;
	}

	public void setAwardAfterDate(Date awardAfterDate) {
		this.awardAfterDate = awardAfterDate;
	}
	@Column(name="pay_time")
	public Date getPayTime() {
		return payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name="first_msg_state")
	public Integer getFirstMsgState() {
		return firstMsgState;
	}

	public void setFirstMsgState(Integer firstMsgState) {
		this.firstMsgState = firstMsgState;
	}

	@Column(name="execute_count")
	public Integer getExecuteCount() {
		return executeCount;
	}

	public void setExecuteCount(Integer executeCount) {
		this.executeCount = executeCount;
	}
	@Column(name="fail_reason")
	public String getFailReason() {
		return failReason;
	}

	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}
}
