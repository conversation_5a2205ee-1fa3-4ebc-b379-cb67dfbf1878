package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TransportInsuranceRaiders entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "transport_insurance_raiders")
public class TransportInsuranceRaiders implements java.io.Serializable {

	// Fields

	private Long id;
	private String source;
	private String newsTitle;
	private String pictureUrl;
	private String openUrl;
	private Date publishTime;
	private Integer openClose;
	private Integer status;
	private Long userId;
	private Date utime;
	private Date ctime;
	private Integer useful;
	private Integer useless;


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "source")
	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Column(name = "news_title")
	public String getNewsTitle() {
		return this.newsTitle;
	}

	public void setNewsTitle(String newsTitle) {
		this.newsTitle = newsTitle;
	}

	@Column(name = "picture_url")
	public String getPictureUrl() {
		return this.pictureUrl;
	}

	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}

	@Column(name = "open_url")
	public String getOpenUrl() {
		return this.openUrl;
	}

	public void setOpenUrl(String openUrl) {
		this.openUrl = openUrl;
	}

	@Column(name = "publish_time")
	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	@Column(name = "open_close")
	public Integer getOpenClose() {
		return this.openClose;
	}

	public void setOpenClose(Integer openClose) {
		this.openClose = openClose;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "useful")
	public Integer getUseful() {
		return this.useful;
	}

	public void setUseful(Integer useful) {
		this.useful = useful;
	}

	@Column(name = "useless")
	public Integer getUseless() {
		return this.useless;
	}

	public void setUseless(Integer useless) {
		this.useless = useless;
	}

}