package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytMerchant entity. <AUTHOR>
@Entity
@Table(name = "tyt_trade_info")
public class TytTradeinfo implements java.io.Serializable {

	private static final long serialVersionUID = -7121542687263824479L;
	
	private Long id;
	@Column
	private Integer  trade_type;
	@Column
	private Date trade_time;
	@Column
	private Date update_time;
	@Column
	private String trade_account;
	@Column
	private String allow_pay_back_refund;
	@Column
	private String refunding_account;
	@Column
	private Long good_info_fee_id;
	@Column
	private String remark;
	@Column
	private Long payer_account_id;
	@Column
	private Long payer_user_id;
	@Column
	private Long pay_receiver_account_id;
	@Column
	private Long pay_receiver_user_id;
	@Column
	private String order_id;
	
	


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	//@Column(name = "belong_user_id")
	public Integer getTrade_type() {
		return trade_type;
	}

	public void setTrade_type(Integer trade_type) {
		this.trade_type = trade_type;
	}



	public Date getTrade_time() {
		return trade_time;
	}

	public void setTrade_time(Date trade_time) {
		this.trade_time = trade_time;
	}

	public Date getUpdate_time() {
		return update_time;
	}

	public void setUpdate_time(Date update_time) {
		this.update_time = update_time;
	}

	public String getTrade_account() {
		return trade_account;
	}

	public void setTrade_account(String trade_account) {
		this.trade_account = trade_account;
	}

	public String getAllow_pay_back_refund() {
		return allow_pay_back_refund;
	}

	public void setAllow_pay_back_refund(String allow_pay_back_refund) {
		this.allow_pay_back_refund = allow_pay_back_refund;
	}

	public String getRefunding_account() {
		return refunding_account;
	}

	public void setRefunding_account(String refunding_account) {
		this.refunding_account = refunding_account;
	}

	public Long getGood_info_fee_id() {
		return good_info_fee_id;
	}

	public void setGood_info_fee_id(Long good_info_fee_id) {
		this.good_info_fee_id = good_info_fee_id;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getPayer_account_id() {
		return payer_account_id;
	}

	public void setPayer_account_id(Long payer_account_id) {
		this.payer_account_id = payer_account_id;
	}

	public Long getPayer_user_id() {
		return payer_user_id;
	}

	public void setPayer_user_id(Long payer_user_id) {
		this.payer_user_id = payer_user_id;
	}

	public Long getPay_receiver_account_id() {
		return pay_receiver_account_id;
	}

	public void setPay_receiver_account_id(Long pay_receiver_account_id) {
		this.pay_receiver_account_id = pay_receiver_account_id;
	}

	public Long getPay_receiver_user_id() {
		return pay_receiver_user_id;
	}

	public void setPay_receiver_user_id(Long pay_receiver_user_id) {
		this.pay_receiver_user_id = pay_receiver_user_id;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	
}