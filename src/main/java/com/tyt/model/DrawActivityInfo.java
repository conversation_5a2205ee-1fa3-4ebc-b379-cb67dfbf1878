package com.tyt.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;
import lombok.Data;

/**
 * @ClassName DrawActivityInfo
 * @Description 抽奖活动对象
 * <AUTHOR>
 * @Date 2021/8/23 15:22
 * @Verdion 1.0
 **/
@Entity
@Table(name = "draw_activity_info", schema = "tyt", catalog = "")
@Data
public class DrawActivityInfo {
    private Long id;
    private String activityName;
    private Date startTime;
    private Date endTime;
    private Long createUserId;
    private String createUserName;
    private Integer status;
    private Integer limitTimesType;
    private Integer limitTimes;
    private Long updateUserId;
    private String updateUserName;
    private Integer appType;
    private Date createTime;
    private Date updateTime;
    private Integer isDelete;
    /**
     * 用于标识进行哪种业务校验，为空只进行基础校验，不进行业务校验
     */
    private String bizCheckSign;

    private Integer totalTimes;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Basic
    @Column(name = "start_time")
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Basic
    @Column(name = "end_time")
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Basic
    @Column(name = "create_user_id")
    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    @Basic
    @Column(name = "create_user_name")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @Basic
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "limit_times_type")
    public Integer getLimitTimesType() {
        return limitTimesType;
    }

    public void setLimitTimesType(Integer limitTimesType) {
        this.limitTimesType = limitTimesType;
    }

    @Basic
    @Column(name = "limit_times")
    public Integer getLimitTimes() {
        return limitTimes;
    }

    public void setLimitTimes(Integer limitTimes) {
        this.limitTimes = limitTimes;
    }

    @Basic
    @Column(name = "update_user_id")
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Basic
    @Column(name = "update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Basic
    @Column(name = "app_type")
    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    @Basic
    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Basic
    @Column(name = "is_delete")
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Basic
    @Column(name = "biz_check_sign")
    public String getBizCheckSign() {
        return bizCheckSign;
    }

    public void setBizCheckSign(String bizCheckSign) {
        this.bizCheckSign = bizCheckSign;
    }

    @Basic
    @Column(name = "total_times")
    public Integer getTotalTimes() {
        return totalTimes;
    }

    public void setTotalTimes(Integer totalTimes) {
        this.totalTimes = totalTimes;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DrawActivityInfo that = (DrawActivityInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(activityName, that.activityName) &&
                Objects.equals(startTime, that.startTime) &&
                Objects.equals(endTime, that.endTime) &&
                Objects.equals(createUserId, that.createUserId) &&
                Objects.equals(createUserName, that.createUserName) &&
                Objects.equals(status, that.status) &&
                Objects.equals(limitTimesType, that.limitTimesType) &&
                Objects.equals(limitTimes, that.limitTimes) &&
                Objects.equals(updateUserId, that.updateUserId) &&
                Objects.equals(updateUserName, that.updateUserName) &&
                Objects.equals(appType, that.appType) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(updateTime, that.updateTime) &&
                Objects.equals(isDelete, that.isDelete);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, activityName, startTime, endTime, createUserId, createUserName, status, limitTimesType, limitTimes, updateUserId, updateUserName, appType, createTime, updateTime, isDelete);
    }


}
















