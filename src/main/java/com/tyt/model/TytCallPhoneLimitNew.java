package com.tyt.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_call_phone_limit_new", catalog = "tyt")
public class TytCallPhoneLimitNew implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6874848664234404523L;
	private Integer id;//主键
	private Short verifyLevelOne;//销售审核一级身份，具体参看tyt_source中group_code为user_deliver_type_one的数据信息
	private Short carAuthNum;//车辆认证可拨打货源的条数
	private Short identityAuthNum;//身份认证可拨打货源条数
	private Short vipNum;//会员可拨打货源条数
	private Long operatorId;//操作人主键
	private String operatorName;//操作人姓名
	private Integer STATUS;//数据状态 1：有效 2：无效 3：删除
	private Date ctime;//创建时间
	private Date utime;//更新时间
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	@Column(name = "verify_level_one")
	public Short getVerifyLevelOne() {
		return verifyLevelOne;
	}
	public void setVerifyLevelOne(Short verifyLevelOne) {
		this.verifyLevelOne = verifyLevelOne;
	}
	@Column(name = "car_auth_num")
	public Short getCarAuthNum() {
		return carAuthNum;
	}
	public void setCarAuthNum(Short carAuthNum) {
		this.carAuthNum = carAuthNum;
	}
	@Column(name = "identity_auth_num")
	public Short getIdentityAuthNum() {
		return identityAuthNum;
	}
	public void setIdentityAuthNum(Short identityAuthNum) {
		this.identityAuthNum = identityAuthNum;
	}
	@Column(name = "vip_num")
	public Short getVipNum() {
		return vipNum;
	}
	public void setVipNum(Short vipNum) {
		this.vipNum = vipNum;
	}
	@Column(name = "operator_id")
	public Long getOperatorId() {
		return operatorId;
	}
	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}
	@Column(name = "operator_name")
	public String getOperatorName() {
		return operatorName;
	}
	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}
	@Column(name = "STATUS")
	public Integer getSTATUS() {
		return STATUS;
	}
	public void setSTATUS(Integer sTATUS) {
		STATUS = sTATUS;
	}
	@Column(name = "ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name = "utime")
	public Date getUtime() {
		return utime;
	}
	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Override
	public String toString() {
		return "TytCallPhoneLimitNew [id=" + id + ", verifyLevelOne=" + verifyLevelOne + ", carAuthNum=" + carAuthNum
				+ ", identityAuthNum=" + identityAuthNum + ", vipNum=" + vipNum + ", operatorId=" + operatorId
				+ ", operatorName=" + operatorName + ", STATUS=" + STATUS + ", ctime=" + ctime
				+ ", utime=" + utime + "]";
	}
	
	

}
