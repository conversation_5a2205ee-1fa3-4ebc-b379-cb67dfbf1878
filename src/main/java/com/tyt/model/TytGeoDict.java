package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * TytGeoDict entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_geo_dict")
public class TytGeoDict implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 2694889886977317207L;
	private Long id;
	private Long pid;
	private String name;
	private String alias;
	private String rf;
	private Integer type;
	private Long px;
	private Long py;
	private Long longitude;
	private Long latitude;
	private String shortname;
	private String provinc;
	private String city;
	private String area;

	@Id
	@GeneratedValue(generator = "paymentableGenerator")
    @GenericGenerator(name = "paymentableGenerator", strategy = "assigned")
	@Column(name = "id", unique = true, nullable = false, length = 100)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "pid")
	public Long getPid() {
		return this.pid;
	}

	public void setPid(Long pid) {
		this.pid = pid;
	}

	@Column(name = "name", length = 50)
	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "alias", length = 50)
	public String getAlias() {
		return this.alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	@Column(name = "rf", length = 50)
	public String getRf() {
		return this.rf;
	}

	public void setRf(String rf) {
		this.rf = rf;
	}

	@Column(name = "type")
	public Integer getType() {
		return this.type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	@Column(name = "px")
	public Long getPx() {
		return this.px;
	}

	public void setPx(Long px) {
		this.px = px;
	}

	@Column(name = "py")
	public Long getPy() {
		return this.py;
	}

	public void setPy(Long py) {
		this.py = py;
	}

	@Column(name = "longitude")
	public Long getLongitude() {
		return this.longitude;
	}

	public void setLongitude(Long longitude) {
		this.longitude = longitude;
	}

	@Column(name = "latitude")
	public Long getLatitude() {
		return this.latitude;
	}

	public void setLatitude(Long latitude) {
		this.latitude = latitude;
	}

	@Column(name = "shortname", length = 50)
	public String getShortname() {
		return this.shortname;
	}

	public void setShortname(String shortname) {
		this.shortname = shortname;
	}

	@Column(name = "provinc", length = 50)
	public String getProvinc() {
		return this.provinc;
	}

	public void setProvinc(String provinc) {
		this.provinc = provinc;
	}

	@Column(name = "city", length = 50)
	public String getCity() {
		return this.city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "area", length = 50)
	public String getArea() {
		return this.area;
	}

	public void setArea(String area) {
		this.area = area;
	}

}