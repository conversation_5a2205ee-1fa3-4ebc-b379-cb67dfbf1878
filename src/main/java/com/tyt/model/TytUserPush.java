package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytUserPush entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_user_push")
public class TytUserPush implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 451524057163870518L;
	private Long id;
	private Long userId;
	private String cid;
	private Integer clientSign;
	private Long ntfId;
	private Long jobId;
	private Long taskId;
	private String pushStatus;
	private Date ctime;
	private Date utime;
	private String deviceId;
	private String carDeviceId;
	private String goodsDeviceId;
	private Integer pushPort;
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "cid")
	public String getCid() {
		return this.cid;
	}

	public void setCid(String cid) {
		this.cid = cid;
	}

	@Column(name = "client_sign")
	public Integer getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "ntf_id")
	public Long getNtfId() {
		return this.ntfId;
	}

	public void setNtfId(Long ntfId) {
		this.ntfId = ntfId;
	}

	@Column(name = "job_id")
	public Long getJobId() {
		return this.jobId;
	}

	public void setJobId(Long jobId) {
		this.jobId = jobId;
	}

	@Column(name = "task_id")
	public Long getTaskId() {
		return this.taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	@Column(name = "push_status", nullable = false, length = 4)
	public String getPushStatus() {
		return this.pushStatus;
	}

	public void setPushStatus(String pushStatus) {
		this.pushStatus = pushStatus;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "device_id")
	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

    @Column(name = "car_device_id")
    public String getCarDeviceId() {
        return carDeviceId;
    }

    public void setCarDeviceId(String carDeviceId) {
        this.carDeviceId = carDeviceId;
    }

    @Column(name = "goods_device_id")
    public String getGoodsDeviceId() {
        return goodsDeviceId;
    }

    public void setGoodsDeviceId(String goodsDeviceId) {
        this.goodsDeviceId = goodsDeviceId;
    }

    @Column(name = "push_port")
    public Integer getPushPort() {
        return pushPort;
    }

    public void setPushPort(Integer pushPort) {
        this.pushPort = pushPort;
    }
}