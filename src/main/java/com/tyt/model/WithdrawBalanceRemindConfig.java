package com.tyt.model;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @name 提现余额提醒配置
 * @time 2015-10-26
 */
@Entity
@Table(name="withdraw_balance_remind_config")
public class WithdrawBalanceRemindConfig implements Serializable{

	private static final long serialVersionUID = -6974051297035682741L;

	private Long id;
	private BigDecimal limitQuota;//提醒限额 低于限额短信提醒
	private String notifyPhone;//提醒手机号
	private String operator;//操作人
	private Date updateTime;//更新时间
	private Date createTime;//创建时间

	@GeneratedValue
	@Id
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="limit_quota")
	public BigDecimal getLimitQuota() {
		return limitQuota;
	}

	public void setLimitQuota(BigDecimal limitQuota) {
		this.limitQuota = limitQuota;
	}
	@Column(name="notify_phone")
	public String getNotifyPhone() {
		return notifyPhone;
	}

	public void setNotifyPhone(String notifyPhone) {
		this.notifyPhone = notifyPhone;
	}
	@Column(name="operator")
	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}
	@Column(name="update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

}
