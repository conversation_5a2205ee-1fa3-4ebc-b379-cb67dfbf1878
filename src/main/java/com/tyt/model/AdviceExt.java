package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 反馈信息实体
 * <AUTHOR>
 *
 */
public class AdviceExt implements Serializable {

	private static final long serialVersionUID = -9029218285690996546L;
	private Long id;
	private Integer title ;//标题
	private String content;//内容
	private String cellPhone;//发布账号
	private Date ctime;//采集时间
	private Integer platId;//终端来源
	private String  version;//版本
	private String telPhone;//联系方式
	private String dutyMan;//处理责任人
	private Date mtime;
	private Integer suggestType;
	private Integer suggestTypeDetail;
	private String feedbackClientResult;
	private Long userId;
	private Integer userClass;
	private Integer deliverType;
	private Integer deliverTypeOne;
	private Integer identityType;
	private Integer userType;
	private String province;
	private String city;
	private String county;
	private Integer adviceStatus;//状态
	private String createBeginTime;
	private String createEndTime;
	private String modifyBeginTime;
	private String modifyEndTime;

	private String typeStr;
	private String picUrl;
	private Integer isMakeWorkorder;
	//业务附加字段 处理前端不展示手机号业务 此处存加密的userId
	private String telePhoneUserId;

	private Integer source;
	private String cName;

	private Long quesOne;

	private String  quesOneName;

	private Long quesTwo;

	private String quesTwoName;

	private Long quesThree;

	private String quesThreeName;

	private Long quesFour;

	private String quesFourName;

	public String getTypeStr() {
		return typeStr;
	}
	public void setTypeStr(String typeStr) {
		this.typeStr = typeStr;
	}
	public String getPicUrl() {
		return picUrl;
	}
	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getTitle() {
		return title;
	}
	public void setTitle(Integer title) {
		this.title = title;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	public Integer getPlatId() {
		return platId;
	}
	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getTelPhone() {
		return telPhone;
	}
	public void setTelPhone(String telPhone) {
		this.telPhone = telPhone;
	}
	public String getDutyMan() {
		return dutyMan;
	}
	public void setDutyMan(String dutyMan) {
		this.dutyMan = dutyMan;
	}

	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	public Integer getSuggestType() {
		return suggestType;
	}

	public void setSuggestType(Integer suggestType) {
		this.suggestType = suggestType;
	}

	public Integer getSuggestTypeDetail() {
		return suggestTypeDetail;
	}

	public void setSuggestTypeDetail(Integer suggestTypeDetail) {
		this.suggestTypeDetail = suggestTypeDetail;
	}

	public String getFeedbackClientResult() {
		return feedbackClientResult;
	}

	public void setFeedbackClientResult(String feedbackClientResult) {
		this.feedbackClientResult = feedbackClientResult;
	}

	public Integer getUserClass() {
		return userClass;
	}

	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}

	public Integer getDeliverType() {
		return deliverType;
	}

	public void setDeliverType(Integer deliverType) {
		this.deliverType = deliverType;
	}

	public Integer getDeliverTypeOne() {
		return deliverTypeOne;
	}

	public void setDeliverTypeOne(Integer deliverTypeOne) {
		this.deliverTypeOne = deliverTypeOne;
	}

	public Integer getIdentityType() {
		return identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getCreateBeginTime() {
		return createBeginTime;
	}

	public void setCreateBeginTime(String createBeginTime) {
		this.createBeginTime = createBeginTime;
	}

	public String getCreateEndTime() {
		return createEndTime;
	}

	public void setCreateEndTime(String createEndTime) {
		this.createEndTime = createEndTime;
	}

	public String getModifyBeginTime() {
		return modifyBeginTime;
	}

	public void setModifyBeginTime(String modifyBeginTime) {
		this.modifyBeginTime = modifyBeginTime;
	}

	public String getModifyEndTime() {
		return modifyEndTime;
	}

	public void setModifyEndTime(String modifyEndTime) {
		this.modifyEndTime = modifyEndTime;
	}

	public Integer getAdviceStatus() {
		return adviceStatus;
	}

	public void setAdviceStatus(Integer adviceStatus) {
		this.adviceStatus = adviceStatus;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

    public Integer getIsMakeWorkorder() {
        return isMakeWorkorder;
    }

    public void setIsMakeWorkorder(Integer isMakeWorkorder) {
        this.isMakeWorkorder = isMakeWorkorder;
    }

	public String getTelePhoneUserId() {
		return telePhoneUserId;
	}

	public void setTelePhoneUserId(String telePhoneUserId) {
		this.telePhoneUserId = telePhoneUserId;
	}

	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public String getcName() {
		return cName;
	}

	public void setcName(String cName) {
		this.cName = cName;
	}

	public Long getQuesOne() {
		return quesOne;
	}

	public void setQuesOne(Long quesOne) {
		this.quesOne = quesOne;
	}

	public String getQuesOneName() {
		return quesOneName;
	}

	public void setQuesOneName(String quesOneName) {
		this.quesOneName = quesOneName;
	}

	public Long getQuesTwo() {
		return quesTwo;
	}

	public void setQuesTwo(Long quesTwo) {
		this.quesTwo = quesTwo;
	}

	public String getQuesTwoName() {
		return quesTwoName;
	}

	public void setQuesTwoName(String quesTwoName) {
		this.quesTwoName = quesTwoName;
	}

	public Long getQuesThree() {
		return quesThree;
	}

	public void setQuesThree(Long quesThree) {
		this.quesThree = quesThree;
	}

	public String getQuesThreeName() {
		return quesThreeName;
	}

	public void setQuesThreeName(String quesThreeName) {
		this.quesThreeName = quesThreeName;
	}

	public Long getQuesFour() {
		return quesFour;
	}

	public void setQuesFour(Long quesFour) {
		this.quesFour = quesFour;
	}

	public String getQuesFourName() {
		return quesFourName;
	}

	public void setQuesFourName(String quesFourName) {
		this.quesFourName = quesFourName;
	}
}
