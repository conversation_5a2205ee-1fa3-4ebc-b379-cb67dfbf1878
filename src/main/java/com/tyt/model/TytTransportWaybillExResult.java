package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytTransportWaybillExResult entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_transport_waybill_ex_result")
public class TytTransportWaybillExResult implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -7421036597564587374L;
	private Long id;
	private Long exId;
	private String opinion;
	private String isLast;
	private Long userId;
	private String cellPhone;
	private Date ctime;
	private String status;
	private Integer loadingStatus;
	private Integer loadingChildStatus;
	private Integer isBreak;

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "ex_id", nullable = false)
	public Long getExId() {
		return this.exId;
	}

	public void setExId(Long exId) {
		this.exId = exId;
	}

	@Column(name = "opinion", length = 100)
	public String getOpinion() {
		return this.opinion;
	}

	public void setOpinion(String opinion) {
		this.opinion = opinion;
	}

	@Column(name = "is_last", nullable = false, length = 1)
	public String getIsLast() {
		return this.isLast;
	}

	public void setIsLast(String isLast) {
		this.isLast = isLast;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "cell_phone", length = 11)
	public String getCellPhone() {
		return this.cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "status", nullable = false, length = 1)
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "loading_status")
	public Integer getLoadingStatus() {
		return loadingStatus;
	}

	public void setLoadingStatus(Integer loadingStatus) {
		this.loadingStatus = loadingStatus;
	}

	@Column(name = "loading_child_status")
	public Integer getLoadingChildStatus() {
		return loadingChildStatus;
	}

	@Column(name = "is_break")
	public Integer getIsBreak() {
		return isBreak;
	}

	public void setIsBreak(Integer isBreak) {
		this.isBreak = isBreak;
	}

	public void setLoadingChildStatus(Integer loadingChildStatus) {
		this.loadingChildStatus = loadingChildStatus;
	}
}
