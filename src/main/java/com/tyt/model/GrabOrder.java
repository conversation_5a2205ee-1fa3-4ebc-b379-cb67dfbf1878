package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
/**
 * 抢单表
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "tyt_grab_order")
public class GrabOrder  implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 670043462820621036L;
	Long id;
	Long user_id;// 抢单人用户表ID
	Long tsId;// 运输信息ID
	Long carId;// 车辆ID
	String status;// 抢单状态 1已经抢单
	Date createTime;// 抢单时间
	Date updatetime;// 修改时间
    @Id
    @GeneratedValue
    @Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
    @Column(name="user_id")
	public Long getUser_id() {
		return user_id;
	}

	public void setUser_id(Long user_id) {
		this.user_id = user_id;
	}
	@Column(name="ts_id")
	public Long getTsId() {
		return tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	@Column(name="car_id")
	public Long getCarId() {
		return carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}
	@Column(name="status")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="update_time")
	public Date getUpdateTime() {
		return updatetime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updatetime = updateTime;
	}

}
