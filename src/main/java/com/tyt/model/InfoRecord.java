package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="info_record")
public class InfoRecord  implements Serializable {

	 /**
	 * 
	 */
	private static final long serialVersionUID = 6130468522169263638L;
	Long id ;
	 Integer hourPoint ;
	 Date statTime;
	 Integer manuEnable;
	 Integer manuDeal;
	 Integer manuTotal;
	 Integer autoEnable ;
	 Integer infoEnable;
	 Integer clientLogin ;
	 
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="hour_point")
	public Integer getHourPoint() {
		return hourPoint;
	}
	public void setHourPoint(Integer hourPoint) {
		this.hourPoint = hourPoint;
	}
	@Column(name="stat_time")
	public Date getStatTime() {
		return statTime;
	}
	public void setStatTime(Date statTime) {
		this.statTime = statTime;
	}
	@Column(name="manu_enable")
	public Integer getManuEnable() {
		return manuEnable;
	}
	public void setManuEnable(Integer manuEnable) {
		this.manuEnable = manuEnable;
	}
	@Column(name="manu_deal")
	public Integer getManuDeal() {
		return manuDeal;
	}
	public void setManuDeal(Integer manuDeal) {
		this.manuDeal = manuDeal;
	}
	@Column(name="manu_total")
	public Integer getManuTotal() {
		return manuTotal;
	}
	public void setManuTotal(Integer manuTotal) {
		this.manuTotal = manuTotal;
	}
	@Column(name="auto_enable")
	public Integer getAutoEnable() {
		return autoEnable;
	}
	public void setAutoEnable(Integer autoEnable) {
		this.autoEnable = autoEnable;
	}
	@Column(name="info_enable")
	public Integer getInfoEnable() {
		return infoEnable;
	}
	public void setInfoEnable(Integer infoEnable) {
		this.infoEnable = infoEnable;
	}
	@Column(name="client_login")
	public Integer getClientLogin() {
		return clientLogin;
	}
	public void setClientLogin(Integer clientLogin) {
		this.clientLogin = clientLogin;
	}
	 
	 
	
}
