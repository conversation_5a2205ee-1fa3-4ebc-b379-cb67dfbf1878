package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytPushJob entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_push_job")
public class TytPushJob implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -3455634990886976395L;
	private Long id;
	private Long ntfId;
	private String businessType;
	private String title;
	private String content;
	private String pushMode;
	private Date pushTime;
	private String openType;
	private String linkUrl;
	private Integer timeLong;
	private String pushType;
	private String offline;
	private String pushNetWorkType;
	private String dissectStatus;
	private Date dissectTime;
	private String pushStatus;
	private Date pushStartTime;
	private Date pushEndTime;
	private Date ctime;
	private Date mtime;
	private String jobType="0";

	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "ntf_id")
	public Long getNtfId() {
		return this.ntfId;
	}

	public void setNtfId(Long ntfId) {
		this.ntfId = ntfId;
	}

	@Column(name = "business_type", nullable = false, length = 4)
	public String getBusinessType() {
		return this.businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	@Column(name = "title", length = 50)
	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "content", length = 100)
	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Column(name = "push_mode", nullable = false, length = 4)
	public String getPushMode() {
		return this.pushMode;
	}

	public void setPushMode(String pushMode) {
		this.pushMode = pushMode;
	}

	@Column(name = "push_time")
	public Date getPushTime() {
		return this.pushTime;
	}

	public void setPushTime(Date pushTime) {
		this.pushTime = pushTime;
	}

	@Column(name = "open_type", nullable = false, length = 4)
	public String getOpenType() {
		return this.openType;
	}

	public void setOpenType(String openType) {
		this.openType = openType;
	}

	@Column(name = "link_url", length = 200)
	public String getLinkUrl() {
		return this.linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	@Column(name = "time_long")
	public Integer getTimeLong() {
		return this.timeLong;
	}

	public void setTimeLong(Integer timeLong) {
		this.timeLong = timeLong;
	}

	@Column(name = "push_type", nullable = false, length = 4)
	public String getPushType() {
		return this.pushType;
	}

	public void setPushType(String pushType) {
		this.pushType = pushType;
	}

	@Column(name = "offline", nullable = false, length = 4)
	public String getOffline() {
		return this.offline;
	}

	public void setOffline(String offline) {
		this.offline = offline;
	}

	@Column(name = "push_net_work_type", nullable = false, length = 4)
	public String getPushNetWorkType() {
		return this.pushNetWorkType;
	}

	public void setPushNetWorkType(String pushNetWorkType) {
		this.pushNetWorkType = pushNetWorkType;
	}

	@Column(name = "dissect_status", nullable = false, length = 4)
	public String getDissectStatus() {
		return this.dissectStatus;
	}

	public void setDissectStatus(String dissectStatus) {
		this.dissectStatus = dissectStatus;
	}

	@Column(name = "dissect_time")
	public Date getDissectTime() {
		return this.dissectTime;
	}

	public void setDissectTime(Date dissectTime) {
		this.dissectTime = dissectTime;
	}

	@Column(name = "push_status", nullable = false, length = 4)
	public String getPushStatus() {
		return this.pushStatus;
	}

	public void setPushStatus(String pushStatus) {
		this.pushStatus = pushStatus;
	}

	@Column(name = "push_start_time")
	public Date getPushStartTime() {
		return this.pushStartTime;
	}

	public void setPushStartTime(Date pushStartTime) {
		this.pushStartTime = pushStartTime;
	}

	@Column(name = "push_end_time")
	public Date getPushEndTime() {
		return this.pushEndTime;
	}

	public void setPushEndTime(Date pushEndTime) {
		this.pushEndTime = pushEndTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name = "job_type")
	public String getJobType() {
		return jobType;
	}

	public void setJobType(String jobType) {
		this.jobType = jobType;
	}

}