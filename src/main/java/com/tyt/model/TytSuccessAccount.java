package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;
@Entity
@Table(name="tyt_success_account")
public class TytSuccessAccount {
	
	   Long id;//'主键ID',
	   Long userId;//'用户ID',
	   String cellPhone;//'手机号码',
	   String trueName;//'真实姓名',
	   String saleName;//'销售姓名',
	   Date payDate;//'缴费日期',
	   String type;//'缴费类型（1补缴 2缴费）',
	   String channel;//'缴费渠道（1线上支付 2线下支付）',
	   String bank;//'缴费银行',
	   Integer money;//'缴费金额',
	   Integer years;//'开通年限',
	   Date endDate;//'截止日期',
	   Integer number;//'缴费次第'
	   String orderNo;//订单编号
	   
	   public static String  PAY_CHANNEL_ONLINE="1";//线上支付
	   public static String  PAY_CHANNEL_OFFLINE="2";//线下支付
	   
	   public static String  PAY_TYPE_JIAOFEI="1";//缴费
	   public static String  PAY_TYPE_BUJIAO="2";//补缴
	   
	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="true_name")
	public String getTrueName() {
		return trueName;
	}
	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}
	@Column(name="sale_name")
	public String getSaleName() {
		return saleName;
	}
	public void setSaleName(String saleName) {
		this.saleName = saleName;
	}
	@Column(name="pay_date")
	public Date getPayDate() {
		return payDate;
	}
	public void setPayDate(Date payDate) {
		this.payDate = payDate;
	}
	@Column(name="type")
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	@Column(name="channel")
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	@Column(name="bank")
	public String getBank() {
		return bank;
	}
	public void setBank(String bank) {
		this.bank = bank;
	}
	@Column(name="money")
	public Integer getMoney() {
		return money;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	@Column(name="years")
	public Integer getYears() {
		return years;
	}
	public void setYears(Integer years) {
		this.years = years;
	}
	@Column(name="end_date")
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	@Column(name="number")
	public Integer getNumber() {
		return number;
	}
	public void setNumber(Integer number) {
		this.number = number;
	}
	@Column(name="order_no")
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

}
