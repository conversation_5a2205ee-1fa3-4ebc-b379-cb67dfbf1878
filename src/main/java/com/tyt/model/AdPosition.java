package com.tyt.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.tyt.manager.entity.base.TytAdPositionGoodsConfig;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 广告推广位实体
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "tyt_ad_position")
public class AdPosition implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 图片显示位置，参见tyt_source表ad_position
     */
    private Integer showPosition;
    /**
     * 图片url
     */
    private String picUrl;
    /**
     * 图片url
     */
    private String originalFilename;
    /**
     * 缩略图url
     */
    private String smallPicUrl;
    /**
     * 图片链接地址
     */
    private String picLinkUrl;

    /**
     * 文字内容标题
     */
    private String wordContentTitle;
    /**
     * 文字内容
     */
    private String wordContent;
    /**
     * 文字链接
     */
    private String wordLinkUrl;
    /**
     * 审核一级身份，0-全部，1-货方，2-车方
     */
    
    private Integer regIdentity;
    /**
     * 用户分众类型，参见LabelEnum
     */
    
    private String userGroup;
    /**
     * 用户分众类型，参见LabelEnum
     */

    private String userGroupName;
    /**
     * 状态，0-停用，1-启用
     */
    
    private Integer status;

    /**
     * 客户端登录限制 0-无需登录 1 需要登录
     */

    private Integer loginRestriction;


    /**
     * 优先级，数字越大越靠前
     */
    private Integer sort;
    /**
     * 操作人ID
     */
    @JSONField(serialize=false)
    private Long operaUserId;
    /**
     * 操作人名称
     */
    @JSONField(serialize=false)
    private String operaUserName;
    /**
     * 操作人IP地址
     */
    @JSONField(serialize=false)
    private String operaUserIp;
    /**
     * 创建时间
     */
    @JSONField(serialize=false)
    private Date ctime;
    /**
     * 更新时间
     */
    @JSONField(serialize=false)
    private Date mtime;
    /**
     * 是否删除，0-无效，1-有效
     */
    @JSONField(serialize=false)
    private Integer isValid;

    private String linkType;
    private Integer showType;
    private Date startTime;
    private Date endTime;
    private String showProvince;
    private Integer isImportUser;

    /**
     * 0全部 1体验差一手货主
     */
    private Integer userType;

    /**
     * 货主身份 0全部 1个人货主 2企业货主 3货站 4物流公司
     */
    private String goodsIdentity;

    /**
     * 会员状态 0不限 1会员 2非会员
     */
    private Integer vipStatus;

    /**
     * 展示规则 json存储
     */
    private String showRuleContent;



    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "title")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    @Column(name = "show_position")
    public Integer getShowPosition() {
        return showPosition;
    }

    public void setShowPosition(Integer showPosition) {
        this.showPosition = showPosition;
    }
    @Column(name = "pic_url")
    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }
    @Column(name = "small_pic_url")
    public String getSmallPicUrl() {
        return smallPicUrl;
    }

    public void setSmallPicUrl(String smallPicUrl) {
        this.smallPicUrl = smallPicUrl;
    }
    @Column(name = "pic_link_url")
    public String getPicLinkUrl() {
        return picLinkUrl;
    }

    public void setPicLinkUrl(String picLinkUrl) {
        this.picLinkUrl = picLinkUrl;
    }

    @Column(name = "word_content_title")
    public String getWordContentTitle() {
        return wordContentTitle;
    }

    public void setWordContentTitle(String wordContentTitle) {
        this.wordContentTitle = wordContentTitle;
    }

    @Column(name = "word_content")
    public String getWordContent() {
        return wordContent;
    }

    public void setWordContent(String wordContent) {
        this.wordContent = wordContent;
    }
    @Column(name = "word_link_url")
    public String getWordLinkUrl() {
        return wordLinkUrl;
    }

    public void setWordLinkUrl(String wordLinkUrl) {
        this.wordLinkUrl = wordLinkUrl;
    }
    @Column(name = "reg_identity")
    public Integer getRegIdentity() {
        return regIdentity;
    }

    public void setRegIdentity(Integer regIdentity) {
        this.regIdentity = regIdentity;
    }
    @Column(name = "user_group")
    public String getUserGroup() {
        return userGroup;
    }

    public void setUserGroup(String userGroup) {
        this.userGroup = userGroup;
    }
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "login_restriction")
    public Integer getLoginRestriction() {
        return loginRestriction;
    }

    public void setLoginRestriction(Integer loginRestriction) {
        this.loginRestriction = loginRestriction;
    }

    @Column(name = "sort")
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    @Column(name = "opera_user_id")
    public Long getOperaUserId() {
        return operaUserId;
    }

    public void setOperaUserId(Long operaUserId) {
        this.operaUserId = operaUserId;
    }
    @Column(name = "opera_user_name")
    public String getOperaUserName() {
        return operaUserName;
    }

    public void setOperaUserName(String operaUserName) {
        this.operaUserName = operaUserName;
    }
    @Column(name = "opera_user_ip")
    public String getOperaUserIp() {
        return operaUserIp;
    }

    public void setOperaUserIp(String operaUserIp) {
        this.operaUserIp = operaUserIp;
    }
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
    @Column(name = "is_valid")
    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }
    @Transient
    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }
    @Transient
    public String getUserGroupName() {
        return userGroupName;
    }

    public void setUserGroupName(String userGroupName) {
        this.userGroupName = userGroupName;
    }

    @Column(name = "link_type")
    public String getLinkType() {
        return linkType;
    }

    public void setLinkType(String linkType) {
        this.linkType = linkType;
    }

    @Column(name = "show_type")
    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    @Column(name = "start_time")
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Column(name = "end_time")
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Column(name = "show_province")
    public String getShowProvince() {
        return showProvince;
    }

    public void setShowProvince(String showProvince) {
        this.showProvince = showProvince;
    }

    @Column(name = "is_import_user")
    public Integer getIsImportUser() {
        return isImportUser;
    }

    public void setIsImportUser(Integer isImportUser) {
        this.isImportUser = isImportUser;
    }

    @Column(name = "user_type")
    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    @Column(name = "goods_identity")
    public String getGoodsIdentity() {
        return goodsIdentity;
    }

    public void setGoodsIdentity(String goodsIdentity) {
        this.goodsIdentity = goodsIdentity;
    }
    @Column(name = "vip_status")
    public Integer getVipStatus() {
        return vipStatus;
    }

    public void setVipStatus(Integer vipStatus) {
        this.vipStatus = vipStatus;
    }

    @Column(name = "show_rule_content")
    public String getShowRuleContent() {
        return showRuleContent;
    }

    public void setShowRuleContent(String showRuleContent) {
        this.showRuleContent = showRuleContent;
    }


    @Override
    public String toString() {
        return "AdPosition{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", showPosition=" + showPosition +
                ", picUrl='" + picUrl + '\'' +
                ", originalFilename='" + originalFilename + '\'' +
                ", smallPicUrl='" + smallPicUrl + '\'' +
                ", picLinkUrl='" + picLinkUrl + '\'' +
                ", wordContent='" + wordContent + '\'' +
                ", wordLinkUrl='" + wordLinkUrl + '\'' +
                ", regIdentity=" + regIdentity +
                ", userGroup='" + userGroup + '\'' +
                ", userGroupName='" + userGroupName + '\'' +
                ", status=" + status +
                ", sort=" + sort +
                ", operaUserId=" + operaUserId +
                ", operaUserName='" + operaUserName + '\'' +
                ", operaUserIp='" + operaUserIp + '\'' +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                ", isValid=" + isValid +
                '}';
    }
}
