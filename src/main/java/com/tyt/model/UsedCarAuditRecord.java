package com.tyt.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/10 17:18
 */
@Data
public class UsedCarAuditRecord implements Serializable {
    private static final long serialVersionUID = -2509352747345067200L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 二手车id
     */
    private Long usedCarSaleId;
    /**
     * 发布状态 1审核失败 2审核通过 3下架
     */
    private String status;
    /**
     * 操作人名称
     */
    private String userName;
    /**
     * 操作人id
     */
    private Long userId;
    /**
     * 审核失败或下架原因
     */
    private String reason;
    /**
     * 描述
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date ctime;

}
