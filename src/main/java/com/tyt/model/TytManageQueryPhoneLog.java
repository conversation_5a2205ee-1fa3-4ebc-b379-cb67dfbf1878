package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TytManageQueryPhoneLog
 * @description 运营后台手机号查询记录实体类
 * @date 2023-2-13 10:26:33
 */
@Entity
@Table(name = "tyt_manage_queryphone_log")
public class TytManageQueryPhoneLog {

    private Long id;
    private String menuName;
    private Long queryUserId;
    private Long queryId;
    private String userName;
    private Long optUserId;
    private Date optTime;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name="menu_name")
    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }
    @Column(name="query_user_id")
    public Long getQueryUserId() {
        return queryUserId;
    }

    public void setQueryUserId(Long queryUserId) {
        this.queryUserId = queryUserId;
    }
    @Column(name="query_id")
    public Long getQueryId() {
        return queryId;
    }

    public void setQueryId(Long queryId) {
        this.queryId = queryId;
    }

    @Column(name="user_name")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    @Column(name="opt_user_id")
    public Long getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(Long optUserId) {
        this.optUserId = optUserId;
    }
    @Column(name="opt_time")
    public Date getOptTime() {
        return optTime;
    }

    public void setOptTime(Date optTime) {
        this.optTime = optTime;
    }
}
