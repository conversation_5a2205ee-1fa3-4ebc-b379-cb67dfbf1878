package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

/**
 * 客服电话对应关系实体
 * 
 * <AUTHOR>
 * @date 2018年3月29日下午8:42:10
 * @description
 */
@Entity
@Table(name = "tyt_kefu_phone", catalog = "tyt")
public class TytKefuPhone implements java.io.Serializable {
	private static final long serialVersionUID = -3864399550859990131L;

	private Long id;
	private Long kefuId;
	private String kefuName;
	private String kefuPhone;
	private Date ctime;
	private Date utime;
	private Byte status;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "kefu_id")
	public Long getKefuId() {
		return this.kefuId;
	}

	public void setKefuId(Long kefuId) {
		this.kefuId = kefuId;
	}

	@Column(name = "kefu_name", length = 16)
	public String getKefuName() {
		return this.kefuName;
	}

	public void setKefuName(String kefuName) {
		this.kefuName = kefuName;
	}

	@Column(name = "kefu_phone", length = 16)
	public String getKefuPhone() {
		return this.kefuPhone;
	}

	public void setKefuPhone(String kefuPhone) {
		this.kefuPhone = kefuPhone;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "status")
	public Byte getStatus() {
		return this.status;
	}

	public void setStatus(Byte status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
