package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_message_task")
public class TytMessageTask implements java.io.Serializable {

	private static final long serialVersionUID = -3425776546457172934L;
	private Long id;
	private Long msgId;
	private Long maxUserId;
	private Date ctime;
	private Date utime;
	private Date startTime;
	private Date endTime;
	private String status;


	public TytMessageTask() {
		super();
	}

	public TytMessageTask(Long msgId, Long maxUserId, Date ctime,
			Date utime, Date startTime, Date endTime,
			String status) {
		this.msgId = msgId;
		this.maxUserId = maxUserId;
		this.ctime = ctime;
		this.utime = utime;
		this.startTime = startTime;
		this.endTime = endTime;
		this.status = status;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "msg_id")
	public Long getMsgId() {
		return this.msgId;
	}

	public void setMsgId(Long msgId) {
		this.msgId = msgId;
	}

	@Column(name = "max_user_id")
	public Long getMaxUserId() {
		return this.maxUserId;
	}

	public void setMaxUserId(Long maxUserId) {
		this.maxUserId = maxUserId;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "start_time")
	public Date getStartTime() {
		return this.startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	@Column(name = "end_time")
	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	@Column(name = "status")
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}