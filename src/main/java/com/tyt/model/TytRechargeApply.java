package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/8/25 11:01
 * @Version 1.0
 **/
@Entity
@Table(name = "tyt_recharge_apply")
public class TytRechargeApply implements Serializable {
    private static final long serialVersionUID = 4708270929704910335L;

    private Long id;
    private String rechargeNo;
    private Long userId;
    private String bankCardOwner;
    private String getBankCardOwner;
    private String cardBlongedBank;
    private String cardDepositBank;
    private BigDecimal rechargeAmount;
    private BigDecimal serviceAmount;
    private BigDecimal balance;
    private Integer rechargeChannel;
    private String rechargeSubChannel;
    private Integer rechargeStatus;
    private String remark;
    private Date applyTime;
    private String innerRechargeNo;
    private Date rechargeTime;
    private Date updateTime;

    @Id
    @GeneratedValue
    @Column(name="id",nullable=false,unique=true)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name="recharge_no")
    public String getRechargeNo() {
        return rechargeNo;
    }

    public void setRechargeNo(String rechargeNo) {
        this.rechargeNo = rechargeNo;
    }

    @Column(name="user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name="bank_card_owner")
    public String getBankCardOwner() {
        return bankCardOwner;
    }

    public void setBankCardOwner(String bankCardOwner) {
        this.bankCardOwner = bankCardOwner;
    }

    @Column(name="bank_card_number")
    public String getGetBankCardOwner() {
        return getBankCardOwner;
    }

    public void setGetBankCardOwner(String getBankCardOwner) {
        this.getBankCardOwner = getBankCardOwner;
    }

    @Column(name="card_blonged_bank")
    public String getCardBlongedBank() {
        return cardBlongedBank;
    }

    public void setCardBlongedBank(String cardBlongedBank) {
        this.cardBlongedBank = cardBlongedBank;
    }

    @Column(name="card_deposit_bank")
    public String getCardDepositBank() {
        return cardDepositBank;
    }

    public void setCardDepositBank(String cardDepositBank) {
        this.cardDepositBank = cardDepositBank;
    }

    @Column(name="recharge_amount")
    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    @Column(name="service_amount")
    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    @Column(name="balance")
    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    @Column(name="recharge_status")
    public Integer getRechargeStatus() {
        return rechargeStatus;
    }

    public void setRechargeStatus(Integer rechargeStatus) {
        this.rechargeStatus = rechargeStatus;
    }



    @Column(name="remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name="apply_time")
    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    @Column(name="recharge_time")
    public Date getRechargeTime() {
        return rechargeTime;
    }

    public void setRechargeTime(Date rechargeTime) {
        this.rechargeTime = rechargeTime;
    }

    @Column(name="update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name="recharge_channel")
    public Integer getRechargeChannel() {
        return rechargeChannel;
    }

    public void setRechargeChannel(Integer rechargeChannel) {
        this.rechargeChannel = rechargeChannel;
    }

    @Column(name="recharge_sub_channel")
    public String getRechargeSubChannel() {
        return rechargeSubChannel;
    }

    public void setRechargeSubChannel(String rechargeSubChannel) {
        this.rechargeSubChannel = rechargeSubChannel;
    }

    @Column(name="inner_recharge_no")
    public String getInnerRechargeNo() {
        return innerRechargeNo;
    }

    public void setInnerRechargeNo(String innerRechargeNo) {
        this.innerRechargeNo = innerRechargeNo;
    }
}
