package com.tyt.model;


import static javax.persistence.GenerationType.IDENTITY;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * TytStickRule entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_stick_rule", catalog = "tyt")
public class TytStickRule implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -332842727767889394L;
	private Long id;
	private String name;
	private Integer startHour;
	private Integer endHour;
	private Integer intervalMinute;
	private String status;
	private Date ctime;
	private Date mtime;
	private String ruleDesc;

	// Constructors

	/** default constructor */
	public TytStickRule() {
	}


	// Property accessors
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "name")
	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "start_hour")
	public Integer getStartHour() {
		return this.startHour;
	}

	public void setStartHour(Integer startHour) {
		this.startHour = startHour;
	}

	@Column(name = "end_hour")
	public Integer getEndHour() {
		return this.endHour;
	}

	public void setEndHour(Integer endHour) {
		this.endHour = endHour;
	}

	@Column(name = "interval_minute")
	public Integer getIntervalMinute() {
		return this.intervalMinute;
	}

	public void setIntervalMinute(Integer intervalMinute) {
		this.intervalMinute = intervalMinute;
	}

	@Column(name = "status")
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "rule_desc")
	public String getRuleDesc() {
		return this.ruleDesc;
	}

	public void setRuleDesc(String ruleDesc) {
		this.ruleDesc = ruleDesc;
	}
	 @Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
}