package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 流水记录，该表所有记录都是以用户钱包为中心进行记录
 */
@Entity
@Table(name = "tyt_financial_flow")
public class FinancialFlow implements java.io.Serializable {

	private static final long serialVersionUID = 7361853184909988180L;
	private Integer id;
	private Date createTime;
	private Integer userId;
	private Short flowType;
	private Short transfterBank;
	private String waybillNumber;
	private Integer applyId;
	private String moneyAmount;
	private Integer tradeId;
	private Integer accountId;
	private Long beforeReaminging;
	private Long afterRemaining;
	private String bankName;
	private String ordeNumber;
	private String remark;


	public FinancialFlow() {
	}


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Integer getUserId() {
		return this.userId;
	}
	
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	
	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	

	@Column(name = "flow_type")
	public Short getFlowType() {
		return this.flowType;
	}

	public void setFlowType(Short flowType) {
		this.flowType = flowType;
	}

	@Column(name = "transfter_bank")
	public Short getTransfterBank() {
		return this.transfterBank;
	}

	public void setTransfterBank(Short transfterBank) {
		this.transfterBank = transfterBank;
	}

	@Column(name = "waybill_number")
	public String getWaybillNumber() {
		return this.waybillNumber;
	}

	public void setWaybillNumber(String waybillNumber) {
		this.waybillNumber = waybillNumber;
	}

	@Column(name = "apply_id")
	public Integer getApplyId() {
		return this.applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	@Column(name = "money_amount")
	public String getMoneyAmount() {
		return this.moneyAmount;
	}

	public void setMoneyAmount(String moneyAmount) {
		this.moneyAmount = moneyAmount;
	}

	@Column(name = "trade_id")
	public Integer getTradeId() {
		return this.tradeId;
	}

	public void setTradeId(Integer tradeId) {
		this.tradeId = tradeId;
	}

	@Column(name = "account_id")
	public Integer getAccountId() {
		return this.accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	@Column(name = "before_reaminging")
	public Long getBeforeReaminging() {
		return this.beforeReaminging;
	}

	public void setBeforeReaminging(Long beforeReaminging) {
		this.beforeReaminging = beforeReaminging;
	}

	@Column(name = "after_remaining")
	public Long getAfterRemaining() {
		return this.afterRemaining;
	}

	public void setAfterRemaining(Long afterRemaining) {
		this.afterRemaining = afterRemaining;
	}

	@Column(name = "bank_name")
	public String getBankName() {
		return this.bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	@Column(name = "orde_number")
	public String getOrdeNumber() {
		return this.ordeNumber;
	}

	public void setOrdeNumber(String ordeNumber) {
		this.ordeNumber = ordeNumber;
	}

    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}