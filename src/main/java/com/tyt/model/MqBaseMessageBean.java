package com.tyt.model;

/**  
 * @Title: MqBaseMessageBean.java
 * @Package com.tyt.model
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年12月3日
 */
public class MqBaseMessageBean {

	/*
	 * 消息的类型
	 */
	public int messageType;
	/*
	 * 消息的序列号，每个消息唯一
	 */
	public String messageSerailNum;

	/*
	 * 微信支付成功回调
	 */
	public static int MESSAGETYPE_INFOFEE_WEIXIN_PAYSUC_CALLBACK = 1;
	/*
	 * 支付宝支付成功回调
	 */
	public static int MESSAGETYPE_INFOFEE_ZHIFUBAO_PAYSUC_CALLBACK = 2;
	/*
	 * 医保支付成功回调
	 */
	public static int MESSAGETYPE_INFOFEE_YIBAO_PAYSUC_CALLBACK = 3;
	/*
	 * 退款
	 */
	public static int MESSAGETYPE_RERUND = 4;
	/*
	 * 提现
	 */
	public static int MESSAGETYPE_WITHDRAW = 5;
	/*
	 * 装货完成
	 */
	public static int MESSAGETYPE_AGREE_LOADING = 6;
	/*
	 * 异常上报退款
	 */
	public static int MESSAGETYPE_EXCEPTION_REPORT = 7;
	/*
	 * 发送短信
	 */
	public static int MESSAGETYPE_SEDN_MESSAGE = 8;
	//异常
	public static int MESSAGETYPE_EXCEPTION_MESSAGE = 10;

	//信息费异常(新)
	public static int MESSAGETYPE_INFOFEE_EXCEPTION_MESSAGE = 5;

	//账户数据迁移金额确认
	public static int MESSAGETYPE_ACCOUNT_DATA_MIGRATE_MESSAGE=77;
	
	public int getMessageType() {
		return messageType;
	}

	public void setMessageType(int messageType) {
		this.messageType = messageType;
	}

	public String getMessageSerailNum() {
		return messageSerailNum;
	}

	public void setMessageSerailNum(String messageSerailNum) {
		this.messageSerailNum = messageSerailNum;
	}

	@Override
	public String toString() {
		return "MqBaseMessageBean [messageType=" + messageType + ", messageSerailNum=" + messageSerailNum + "]";
	}
}

