package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

@Entity
@Table(name="tyt_log")
public class OpLog implements Serializable {
	
	private static final long serialVersionUID = 4794588810798707801L;
	private Long id;
	private Long userId;
	private Integer opType;
    private Timestamp opTime;
	private String opContent;
	private String ip;
	private Integer platId;
	private String version;
	private String cellPhone;
	private String ticket;
	private String pcSign;
	
		
	public static final Integer OP_CLIENT_REG = 100;//客户端注册
	public static final Integer OP_CLIENT_LOGIN = 101;//客户端登陆
	public static final Integer OP_CLIENT_USER_EDIT = 102;//用户编辑
	public static final Integer OP_CLIENT_PUBLISH = 103;//客户端发布信息
	public static final Integer OP_CLIENT_INFO_EDIT = 104;//客户端编辑信息
	public static final Integer OP_CLIENT_USER_UPDATE_PASSWORD = 105;//客户端更改密码
	public static final Integer OP_CLIENT_USER_IMPORT = 106;//后台系统导入车主

	public static final Integer OP_WEBSITE_LOGIN = 200;//后台用户登录
	public static final Integer OP_WEBSITE_LOGOUT = 201;//后台用户退出
	public static final Integer OP_WEBSITE_USER_EDIT = 202;//后台用户编辑
	public static final Integer OP_WEBSITE_USER_PAY = 203; //后台用户收费操作
	public static final Integer OP_WEBSITE_USER_RENEWAL = 204; //后台用户续费操作
	public static final Integer UPDATE_PUBLIC_RESOURCE = 205;//后台修改公共资源
	public static final Integer GET_PUBLIC_RESOURCE = 206;//后台公共资源查看
	public static final Integer ADD_PUBLIC_RESOURCE = 207;//后台公共资源添加
	public static final Integer DELETE_PUBLIC_RESOURCE = 208;//后台删除公共资源
	public static final Integer UPDATE_SYSTEM_RESOURCE = 209;//后台系统参数修改
	public static final Integer GET_SYSTEM_RESOURCE = 2010;//后台系统参数查看
	
	public static final Integer OP_USER_AUTH = 300; //后台实名认证修改
	public static final Integer OP_USER_AUTH_ADD = 301; //后台实名认证录入
	public static final Integer OP_CAR_AUTH = 400;//后台车辆认证修改
	public static final Integer OP_CAR_AUTH_ADD = 401;//后台车辆认证录入
	
	public static final Integer OP_VERSION_EDIT = 501;//版本升级修改
//	public static final Integer OP_STICK_RULE_EDIT = 601;//至顶方案修改
	public static final Integer OP_STICK_RULE_ADD = 602;//新增置顶方案
	public static final Integer OP_STICK_RULE_DELETE = 603;

	public static final Integer OP_CONFIG_EDIT = 701;//开关参数修改
	
	public static final Integer BACK_PAY_JIAOFEI = 801;//后台缴费
	public static final Integer BACK_PAY_BUJIAO = 802;//后台补缴
	
	public static final Integer BACK_PAY_ACCOUNT_EXPORT = 901;//后台流水导出
	public static final Integer BACK_PAY_ACCOUNT_USER_EXPORT = 902;//后台缴费用户导出
	
	public static final Integer UPDATE_STT_LIMIT = 1010;//发货限制修改
	
	

    public OpLog() {
    	this.opTime = new Timestamp(System.currentTimeMillis());
    }
	
	
	
	/*public OpLog(Long userId, Integer opType) {
		super();
		this.userId = userId;
		this.opType = opType;
	}
	


	public OpLog(Long userId, Integer opType, String opContent) {
		super();
		this.userId = userId;
		this.opType = opType;
		this.opContent = opContent;
	}

	public OpLog(Long userId, Integer opType, String opContent,String ip) {
		super();
		this.userId = userId;
		this.opType = opType;
		this.opContent = opContent;
		this.ip = ip;
	}

	public OpLog(Long userId, Integer opType, String opContent,String ip,Integer platId) {
		super();
		this.userId = userId;
		this.opType = opType;
		this.opContent = opContent;
		this.ip = ip;
		this.platId = platId;
	}*/
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="op_type")
	public Integer getOpType() {
		return opType;
	}

	public void setOpType(Integer opType) {
		this.opType = opType;
	}
	
	@Column(name="op_time")
	public Timestamp getOpTime() {
		return opTime;
	}

	public void setOpTime(Timestamp opTime) {
		this.opTime = opTime;
	}

	@Column(name="op_content")
	public String getOpContent() {
		return opContent;
	}

	public void setOpContent(String opContent) {
		this.opContent = opContent;
	}

	@Column(name="ip")
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}

	@Column(name="version")
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="ticket")
	public String getTicket() {
		return ticket;
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}
	@Column(name="pc_sign")
	public String getPcSign() {
		return pcSign;
	}

	public void setPcSign(String pcSign) {
		this.pcSign = pcSign;
	}
	
	@Override
	public String toString() {
		return "OpLog [id=" + id + ", userId=" + userId + ", opType=" + opType
				+ ", opTime=" + opTime + ", opContent=" + opContent + ", ip="
				+ ip + ", platId=" + platId + "]";
	}



	
	

}
