package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName TytConventionActivity
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/9/5 13:45
 * @Verdion 1.0
 **/
@Entity
@Table(name = "tyt_convention_activity")
public class TytConventionActivity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     *  id
     */
    private Long id;
    /**
	 *  活动id
	 */
    private Long activityId;
    /**
	 *  用户id
	 */
    private Long userId;
    /**
	 *  注册手机号
	 */
    private String userCellPhone;
    /**
	 *  订单数
	 */
    private Integer orderNum;
    /**
	 *  目标奖品
	 */
    private Integer targetPrize;
    /**
	 *  奖品
	 */
    private Integer prize;
    /**
	 *  创建时间
	 */
    private Date createTime;
    /**
	 *  修改时间
	 */
    private Date updateTime;
    /**
	 *  0 未删除 1已删除
	 */
    private Integer isDeleted;
    /**
	 *  每天最后完成订单的时间
	 */
    private Date lastFinishTime;
    /**
	 *  是否领取标识: 0-未领取 1-已领取
	 */
    private Integer receiveFlag;


    @Id
    @GeneratedValue
    @Column(name="id",nullable=false,unique=true)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    @Column(name="activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }


    @Column(name="user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name="user_cell_phone")
    public String getUserCellPhone() {
        return userCellPhone;
    }

    public void setUserCellPhone(String userCellPhone) {
        this.userCellPhone = userCellPhone;
    }

    @Column(name="order_num")
    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Column(name="target_prize")
    public Integer getTargetPrize() {
        return targetPrize;
    }

    public void setTargetPrize(Integer targetPrize) {
        this.targetPrize = targetPrize;
    }

    @Column(name="prize")
    public Integer getPrize() {
        return prize;
    }

    public void setPrize(Integer prize) {
        this.prize = prize;
    }

    @Column(name="create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name="update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name="is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Column(name="last_finish_time")
    public Date getLastFinishTime() {
        return lastFinishTime;
    }

    public void setLastFinishTime(Date lastFinishTime) {
        this.lastFinishTime = lastFinishTime;
    }

    @Column(name="receive_flag")
    public Integer getReceiveFlag() {
        return receiveFlag;
    }

    public void setReceiveFlag(Integer receiveFlag) {
        this.receiveFlag = receiveFlag;
    }


    @Override
    public String toString() {
        return "TytConventionActivity{" +
                "id=" + id +
                ", activityId=" + activityId +
                ", userId=" + userId +
                ", userCellPhone='" + userCellPhone + '\'' +
                ", orderNum=" + orderNum +
                ", targetPrize=" + targetPrize +
                ", prize=" + prize +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", isDeleted=" + isDeleted +
                ", lastFinishTime=" + lastFinishTime +
                ", receiveFlag=" + receiveFlag +
                '}';
    }
}
