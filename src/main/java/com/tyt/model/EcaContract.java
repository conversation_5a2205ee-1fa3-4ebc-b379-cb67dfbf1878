package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 人工派单运输合同表
 * <AUTHOR>
 *
 */
@Entity
@Table(name="eca_contract")
public class EcaContract implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -2821860621186691095L;
	private Long id;
	/**
	 * 货物原信息ID
	 */
	private Long srcMsgId;
	/**
	 * 合同编号
	 */
	private String contractNum;
	/**
	 * 托运方企业名称
	 */
	private String shipperName;
	/**
	 * 托运方货主类型，1-个人货主，2-企业货主
	 */
	private Integer shipperType;
	/**
	 * 托运方用户ID
	 */
	private Long shipperUserId;
	/**
	 * 托运方真实姓名
	 */
	private String shipperUserName;
	/**
	 * 托运方电话
	 */
	private String shipperPhone;
	/**
	 * 托运方身份证号
	 */
	private String shipperIdCard;
	/**
	 * 托运方签名时间
	 */
	private Date shipperSignTime;
	/**
	 * 状态 1未签 2签约
	 */
	private Integer shipperSignStatus;
	/**
	 * 承运方货主类型，1-个人货主，2-企业货主
	 */
	private Integer carryType;
	/**
	 * 承运方用户ID
	 */
	private Long carryUserId;
	/**
	 * 承运方企业名称
	 */
	private String carryName;
	/**
	 * 承运方真实姓名
	 */
	private String carryUserName;
	/**
	 * 承运方电话
	 */
	private String carryPhone;
	/**
	 * 承运方身份证号
	 */
	private String carryIdCard;
	/**
	 * 承运方签名时间
	 */
	private Date carrySignTime;
	/**
	 * 状态 1未签 2签约
	 */
	private Integer carrySignStatus;
	/**
	 * 承运方作废合同原因
	 */
	private String carryCancelReason;
	/**
	 * 托运方作废合同原因
	 */
	private String shipperCancelReason;
	/**
	 * 运单号
	 */
	private String tsOrderNo;
	/**
	 * 协议运价
	 */
	private Integer price;

	/**
	 * 现金
	 */
	private float cashPrice;
	/**
	 * 油卡
	 */
	private float oilCard;
	/**
	 * 出发地(省市区以减号-分割开)
	 */
	private String startPoint;
	/**
	 * 目的地(省市区以减号-分割开)
	 */
	private String destPoint;
	/**
	 * 货物内容
	 */
	private String taskContent;
	/**
	 * 规格
	 */
	private String specification;
	/**
	 * 运输车头车辆，车牌照
	 */
	private String headLicensePlate;
	/**
	 * 运输挂车车辆，车牌照
	 */
	private String tailLicensePlate;
	/**
	 * 起运日期
	 */
	private Date startDate;
	/**
	 * 送达日期
	 */
	private Date endDate;
	/**
	 * 结算方式
	 */
	private String settleAccountsType;
	/**
	 * 状态 1新建 2签约中，3已生效，4已作废
	 */
	private Integer status;
	/**
	 * 空白合同路径
	 */
	private String blankPdfPath;
	/**
	 * 签章合同路径
	 */
	private String signPdfPath;
	/**
	 * 创建人员
	 */
	private String createUserName;
	/**
	 * 创建人员ID
	 */
	private Long createUserId;
	/**
	 * 人工派单表ID
	 */
	private Long mtId;
	/**
	 * 创建时间
	 */
	private Date ctime;
	/**
	 * 修改时间
	 */
	private Date mtime;
	/**
	 * 出发信息地址
	 */
	private String startDetailAdd;
	
	/**
	 * 目的详细地址
	 */
	private String destDetailAdd;
	
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="src_msg_id")
	public Long getSrcMsgId() {
		return srcMsgId;
	}
	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}
	@Column(name="contract_num")
	public String getContractNum() {
		return contractNum;
	}
	public void setContractNum(String contractNum) {
		this.contractNum = contractNum;
	}
	@Column(name="shipper_name")
	public String getShipperName() {
		return shipperName;
	}
	public void setShipperName(String shipperName) {
		this.shipperName = shipperName;
	}
	@Column(name="shipper_type")
	public Integer getShipperType() {
		return shipperType;
	}
	public void setShipperType(Integer shipperType) {
		this.shipperType = shipperType;
	}
	@Column(name="shipper_user_id")
	public Long getShipperUserId() {
		return shipperUserId;
	}
	public void setShipperUserId(Long shipperUserId) {
		this.shipperUserId = shipperUserId;
	}
	@Column(name="shipper_user_name")
	public String getShipperUserName() {
		return shipperUserName;
	}
	public void setShipperUserName(String shipperUserName) {
		this.shipperUserName = shipperUserName;
	}
	@Column(name="shipper_phone")
	public String getShipperPhone() {
		return shipperPhone;
	}
	public void setShipperPhone(String shipperPhone) {
		this.shipperPhone = shipperPhone;
	}
	@Column(name="shipper_id_card")
	public String getShipperIdCard() {
		return shipperIdCard;
	}
	public void setShipperIdCard(String shipperIdCard) {
		this.shipperIdCard = shipperIdCard;
	}
	@Column(name="shipper_sign_time")
	public Date getShipperSignTime() {
		return shipperSignTime;
	}
	public void setShipperSignTime(Date shipperSignTime) {
		this.shipperSignTime = shipperSignTime;
	}
	@Column(name="shipper_sign_status")
	public Integer getShipperSignStatus() {
		return shipperSignStatus;
	}
	public void setShipperSignStatus(Integer shipperSignStatus) {
		this.shipperSignStatus = shipperSignStatus;
	}
	@Column(name="carry_type")
	public Integer getCarryType() {
		return carryType;
	}
	public void setCarryType(Integer carryType) {
		this.carryType = carryType;
	}
	@Column(name="carry_name")
	public String getCarryName() {
		return carryName;
	}
	public void setCarryName(String carryName) {
		this.carryName = carryName;
	}
	@Column(name="carry_user_id")
	public Long getCarryUserId() {
		return carryUserId;
	}
	public void setCarryUserId(Long carryUserId) {
		this.carryUserId = carryUserId;
	}
	@Column(name="carry_user_name")
	public String getCarryUserName() {
		return carryUserName;
	}
	public void setCarryUserName(String carryUserName) {
		this.carryUserName = carryUserName;
	}
	@Column(name="carry_phone")
	public String getCarryPhone() {
		return carryPhone;
	}
	public void setCarryPhone(String carryPhone) {
		this.carryPhone = carryPhone;
	}
	@Column(name="carry_id_card")
	public String getCarryIdCard() {
		return carryIdCard;
	}
	public void setCarryIdCard(String carryIdCard) {
		this.carryIdCard = carryIdCard;
	}
	@Column(name="carry_sign_time")
	public Date getCarrySignTime() {
		return carrySignTime;
	}
	public void setCarrySignTime(Date carrySignTime) {
		this.carrySignTime = carrySignTime;
	}
	@Column(name="carry_sign_status")
	public Integer getCarrySignStatus() {
		return carrySignStatus;
	}
	public void setCarrySignStatus(Integer carrySignStatus) {
		this.carrySignStatus = carrySignStatus;
	}
	@Column(name="carry_cancel_reason")
	public String getCarryCancelReason() {
		return carryCancelReason;
	}
	public void setCarryCancelReason(String carryCancelReason) {
		this.carryCancelReason = carryCancelReason;
	}
	@Column(name="shipper_cancel_reason")
	public String getShipperCancelReason() {
		return shipperCancelReason;
	}
	public void setShipperCancelReason(String shipperCancelReason) {
		this.shipperCancelReason = shipperCancelReason;
	}
	@Column(name="ts_order_no")
	public String getTsOrderNo() {
		return tsOrderNo;
	}
	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}
	@Column(name="price")
	public Integer getPrice() {
		return price;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}

	@Column(name = "cash_price")
	public float getCashPrice() {
		return cashPrice;
	}

	public void setCashPrice(float cashPrice) {
		this.cashPrice = cashPrice;
	}
	@Column(name = "oil_card")
	public float getOilCard() {
		return oilCard;
	}

	public void setOilCard(float oilCard) {
		this.oilCard = oilCard;
	}

	@Column(name="start_point")
	public String getStartPoint() {
		return startPoint;
	}
	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	@Column(name="dest_point")
	public String getDestPoint() {
		return destPoint;
	}
	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	@Column(name="task_content")
	public String getTaskContent() {
		return taskContent;
	}
	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}
	@Column(name="specification")
	public String getSpecification() {
		return specification;
	}
	public void setSpecification(String specification) {
		this.specification = specification;
	}
	@Column(name="head_license_plate")
	public String getHeadLicensePlate() {
		return headLicensePlate;
	}

	public void setHeadLicensePlate(String headLicensePlate) {
		this.headLicensePlate = headLicensePlate;
	}
	@Column(name="tail_license_plate")
	public String getTailLicensePlate() {
		return tailLicensePlate;
	}
	public void setTailLicensePlate(String tailLicensePlate) {
		this.tailLicensePlate = tailLicensePlate;
	}
	@Column(name="start_date")
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	@Column(name="end_date")
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	@Column(name="settle_accounts_type")
	public String getSettleAccountsType() {
		return settleAccountsType;
	}
	public void setSettleAccountsType(String settleAccountsType) {
		this.settleAccountsType = settleAccountsType;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="blank_pdf_path")
	public String getBlankPdfPath() {
		return blankPdfPath;
	}
	public void setBlankPdfPath(String blankPdfPath) {
		this.blankPdfPath = blankPdfPath;
	}
	@Column(name="sign_pdf_path")
	public String getSignPdfPath() {
		return signPdfPath;
	}
	public void setSignPdfPath(String signPdfPath) {
		this.signPdfPath = signPdfPath;
	}
	@Column(name="create_user_name")
	public String getCreateUserName() {
		return createUserName;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	@Column(name="create_user_id")
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	@Column(name="mt_id")
	public Long getMtId() {
		return mtId;
	}
	public void setMtId(Long mtId) {
		this.mtId = mtId;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name="start_detail_add")
	public String getStartDetailAdd() {
		return startDetailAdd;
	}
	public void setStartDetailAdd(String startDetailAdd) {
		this.startDetailAdd = startDetailAdd;
	}
	@Column(name="dest_detail_add")
	public String getDestDetailAdd() {
		return destDetailAdd;
	}
	public void setDestDetailAdd(String destDetailAdd) {
		this.destDetailAdd = destDetailAdd;
	}
	
	
}
