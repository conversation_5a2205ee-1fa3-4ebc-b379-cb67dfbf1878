package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "`orders_refund`", schema = "tpay_pay", catalog = "")
public class OrdersRefund {
    /**
     * 自增ID
     */
    @Id
    @Column(name = "`id`")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台生成的退款订单号
     */
    @Column(name = "`refund_order_no`")
    private String refundOrderNo;

    /**
     * 原订单号
     */
    @Column(name = "`old_order_no`")
    private String oldOrderNo;

    /**
     * 商户id
     */
    @Column(name = "`merchant_id`")
    private Long merchantId;

    /**
     * 商户退款订单号
     */
    @Column(name = "`merchant_refund_order_no`")
    private String merchantRefundOrderNo;

    /**
     * 商户原订单号
     */
    @Column(name = "`old_merchant_order_no`")
    private String oldMerchantOrderNo;

    /**
     * 退款交易号，来源于第三方交易平台
     */
    @Column(name = "`third_refund_trade_no`")
    private String thirdRefundTradeNo;

    /**
     * 支付渠道：1000-表示非资金托管渠道 000001-表示使用连连渠道，xxx 表示使用xxx
     */
    @Column(name = "`channel_code`")
    private String channelCode;

    /**
     * 商户用户ID
     */
    @Column(name = "`user_id`")
    private String userId;

    /**
     * 安全的用户ID，与user_id可逆
     */
    @Column(name = "`safe_user_id`")
    private String safeUserId;

    /**
     * 版本号
     */
    @Column(name = "`version`")
    private Integer version;

    /**
     * 商品名称或主题
     */
    @Column(name = "`subject`")
    private String subject;

    /**
     * 退款金额，单位：元(如果需要退手续费，则包含手续费金额)
     */
    @Column(name = "`amount`")
    private BigDecimal amount;

    /**
     * 手续费金额，单位：元
     */
    @Column(name = "`refund_fee`")
    private BigDecimal refundFee;

    /**
     * 是否需要退手续费，0-不需要，1-需要
     */
    @Column(name = "`refund_fee_status`")
    private Integer refundFeeStatus;

    /**
     * 退款单类型 信息费 会员费 提现 充值 保证金 活动返现
     */
    @Column(name = "`order_type`")
    private String orderType;

    /**
     * 受理状态：1-待处理,2-受理成功,3-受理失败
     */
    @Column(name = "`accept_status`")
    private Integer acceptStatus;

    /**
     * 退款状态：1-待处理,2-退款成功,3-退款失败
     */
    @Column(name = "`status`")
    private Integer status;

    /**
     * 第三方网关（支付宝-alipay、微信-weixin、连连-lianlian）
     */
    @Column(name = "`pay_channel`")
    private String payChannel;

    /**
     * ip地址
     */
    @Column(name = "`ip_addr`")
    private String ipAddr;

    /**
     * 异常信息
     */
    @Column(name = "`err_msg`")
    private String errMsg;

    /**
     * 退款原因
     */
    @Column(name = "`reason`")
    private String reason;

    /**
     * 扩展信息
     */
    @Column(name = "`ext`")
    private String ext;

    /**
     * 备注扩展信息
     */
    @Column(name = "`remark`")
    private String remark;

    /**
     * 退款订单创建时间
     */
    @Column(name = "`ctime`")
    private Date ctime;

    /**
     * 退款订单变更时间
     */
    @Column(name = "`mtime`")
    private Date mtime;

    /**
     * 获取自增ID
     *
     * @return id - 自增ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置自增ID
     *
     * @param id 自增ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取平台生成的退款订单号
     *
     * @return refund_order_no - 平台生成的退款订单号
     */
    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    /**
     * 设置平台生成的退款订单号
     *
     * @param refundOrderNo 平台生成的退款订单号
     */
    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo == null ? null : refundOrderNo.trim();
    }

    /**
     * 获取原订单号
     *
     * @return old_order_no - 原订单号
     */
    public String getOldOrderNo() {
        return oldOrderNo;
    }

    /**
     * 设置原订单号
     *
     * @param oldOrderNo 原订单号
     */
    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo == null ? null : oldOrderNo.trim();
    }

    /**
     * 获取商户id
     *
     * @return merchant_id - 商户id
     */
    public Long getMerchantId() {
        return merchantId;
    }

    /**
     * 设置商户id
     *
     * @param merchantId 商户id
     */
    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * 获取商户退款订单号
     *
     * @return merchant_refund_order_no - 商户退款订单号
     */
    public String getMerchantRefundOrderNo() {
        return merchantRefundOrderNo;
    }

    /**
     * 设置商户退款订单号
     *
     * @param merchantRefundOrderNo 商户退款订单号
     */
    public void setMerchantRefundOrderNo(String merchantRefundOrderNo) {
        this.merchantRefundOrderNo = merchantRefundOrderNo == null ? null : merchantRefundOrderNo.trim();
    }

    /**
     * 获取商户原订单号
     *
     * @return old_merchant_order_no - 商户原订单号
     */
    public String getOldMerchantOrderNo() {
        return oldMerchantOrderNo;
    }

    /**
     * 设置商户原订单号
     *
     * @param oldMerchantOrderNo 商户原订单号
     */
    public void setOldMerchantOrderNo(String oldMerchantOrderNo) {
        this.oldMerchantOrderNo = oldMerchantOrderNo == null ? null : oldMerchantOrderNo.trim();
    }

    /**
     * 获取退款交易号，来源于第三方交易平台
     *
     * @return third_refund_trade_no - 退款交易号，来源于第三方交易平台
     */
    public String getThirdRefundTradeNo() {
        return thirdRefundTradeNo;
    }

    /**
     * 设置退款交易号，来源于第三方交易平台
     *
     * @param thirdRefundTradeNo 退款交易号，来源于第三方交易平台
     */
    public void setThirdRefundTradeNo(String thirdRefundTradeNo) {
        this.thirdRefundTradeNo = thirdRefundTradeNo == null ? null : thirdRefundTradeNo.trim();
    }

    /**
     * 获取支付渠道：1000-表示非资金托管渠道 000001-表示使用连连渠道，xxx 表示使用xxx
     *
     * @return channel_code - 支付渠道：1000-表示非资金托管渠道 000001-表示使用连连渠道，xxx 表示使用xxx
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * 设置支付渠道：1000-表示非资金托管渠道 000001-表示使用连连渠道，xxx 表示使用xxx
     *
     * @param channelCode 支付渠道：1000-表示非资金托管渠道 000001-表示使用连连渠道，xxx 表示使用xxx
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * 获取商户用户ID
     *
     * @return user_id - 商户用户ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置商户用户ID
     *
     * @param userId 商户用户ID
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取安全的用户ID，与user_id可逆
     *
     * @return safe_user_id - 安全的用户ID，与user_id可逆
     */
    public String getSafeUserId() {
        return safeUserId;
    }

    /**
     * 设置安全的用户ID，与user_id可逆
     *
     * @param safeUserId 安全的用户ID，与user_id可逆
     */
    public void setSafeUserId(String safeUserId) {
        this.safeUserId = safeUserId == null ? null : safeUserId.trim();
    }

    /**
     * 获取版本号
     *
     * @return version - 版本号
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * 设置版本号
     *
     * @param version 版本号
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * 获取商品名称或主题
     *
     * @return subject - 商品名称或主题
     */
    public String getSubject() {
        return subject;
    }

    /**
     * 设置商品名称或主题
     *
     * @param subject 商品名称或主题
     */
    public void setSubject(String subject) {
        this.subject = subject == null ? null : subject.trim();
    }

    /**
     * 获取退款金额，单位：元(如果需要退手续费，则包含手续费金额)
     *
     * @return amount - 退款金额，单位：元(如果需要退手续费，则包含手续费金额)
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置退款金额，单位：元(如果需要退手续费，则包含手续费金额)
     *
     * @param amount 退款金额，单位：元(如果需要退手续费，则包含手续费金额)
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取手续费金额，单位：元
     *
     * @return refund_fee - 手续费金额，单位：元
     */
    public BigDecimal getRefundFee() {
        return refundFee;
    }

    /**
     * 设置手续费金额，单位：元
     *
     * @param refundFee 手续费金额，单位：元
     */
    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    /**
     * 获取是否需要退手续费，0-不需要，1-需要
     *
     * @return refund_fee_status - 是否需要退手续费，0-不需要，1-需要
     */
    public Integer getRefundFeeStatus() {
        return refundFeeStatus;
    }

    /**
     * 设置是否需要退手续费，0-不需要，1-需要
     *
     * @param refundFeeStatus 是否需要退手续费，0-不需要，1-需要
     */
    public void setRefundFeeStatus(Integer refundFeeStatus) {
        this.refundFeeStatus = refundFeeStatus;
    }

    /**
     * 获取退款单类型 信息费 会员费 提现 充值 保证金 活动返现
     *
     * @return order_type - 退款单类型 信息费 会员费 提现 充值 保证金 活动返现
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * 设置退款单类型 信息费 会员费 提现 充值 保证金 活动返现
     *
     * @param orderType 退款单类型 信息费 会员费 提现 充值 保证金 活动返现
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    /**
     * 获取受理状态：1-待处理,2-受理成功,3-受理失败
     *
     * @return accept_status - 受理状态：1-待处理,2-受理成功,3-受理失败
     */
    public Integer getAcceptStatus() {
        return acceptStatus;
    }

    /**
     * 设置受理状态：1-待处理,2-受理成功,3-受理失败
     *
     * @param acceptStatus 受理状态：1-待处理,2-受理成功,3-受理失败
     */
    public void setAcceptStatus(Integer acceptStatus) {
        this.acceptStatus = acceptStatus;
    }

    /**
     * 获取退款状态：1-待处理,2-退款成功,3-退款失败
     *
     * @return status - 退款状态：1-待处理,2-退款成功,3-退款失败
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置退款状态：1-待处理,2-退款成功,3-退款失败
     *
     * @param status 退款状态：1-待处理,2-退款成功,3-退款失败
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取第三方网关（支付宝-alipay、微信-weixin、连连-lianlian）
     *
     * @return pay_channel - 第三方网关（支付宝-alipay、微信-weixin、连连-lianlian）
     */
    public String getPayChannel() {
        return payChannel;
    }

    /**
     * 设置第三方网关（支付宝-alipay、微信-weixin、连连-lianlian）
     *
     * @param payChannel 第三方网关（支付宝-alipay、微信-weixin、连连-lianlian）
     */
    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel == null ? null : payChannel.trim();
    }

    /**
     * 获取ip地址
     *
     * @return ip_addr - ip地址
     */
    public String getIpAddr() {
        return ipAddr;
    }

    /**
     * 设置ip地址
     *
     * @param ipAddr ip地址
     */
    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr == null ? null : ipAddr.trim();
    }

    /**
     * 获取异常信息
     *
     * @return err_msg - 异常信息
     */
    public String getErrMsg() {
        return errMsg;
    }

    /**
     * 设置异常信息
     *
     * @param errMsg 异常信息
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg == null ? null : errMsg.trim();
    }

    /**
     * 获取退款原因
     *
     * @return reason - 退款原因
     */
    public String getReason() {
        return reason;
    }

    /**
     * 设置退款原因
     *
     * @param reason 退款原因
     */
    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    /**
     * 获取扩展信息
     *
     * @return ext - 扩展信息
     */
    public String getExt() {
        return ext;
    }

    /**
     * 设置扩展信息
     *
     * @param ext 扩展信息
     */
    public void setExt(String ext) {
        this.ext = ext == null ? null : ext.trim();
    }

    /**
     * 获取备注扩展信息
     *
     * @return remark - 备注扩展信息
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注扩展信息
     *
     * @param remark 备注扩展信息
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 获取退款订单创建时间
     *
     * @return ctime - 退款订单创建时间
     */
    public Date getCtime() {
        return ctime;
    }

    /**
     * 设置退款订单创建时间
     *
     * @param ctime 退款订单创建时间
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    /**
     * 获取退款订单变更时间
     *
     * @return mtime - 退款订单变更时间
     */
    public Date getMtime() {
        return mtime;
    }

    /**
     * 设置退款订单变更时间
     *
     * @param mtime 退款订单变更时间
     */
    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", refundOrderNo=").append(refundOrderNo);
        sb.append(", oldOrderNo=").append(oldOrderNo);
        sb.append(", merchantId=").append(merchantId);
        sb.append(", merchantRefundOrderNo=").append(merchantRefundOrderNo);
        sb.append(", oldMerchantOrderNo=").append(oldMerchantOrderNo);
        sb.append(", thirdRefundTradeNo=").append(thirdRefundTradeNo);
        sb.append(", channelCode=").append(channelCode);
        sb.append(", userId=").append(userId);
        sb.append(", safeUserId=").append(safeUserId);
        sb.append(", version=").append(version);
        sb.append(", subject=").append(subject);
        sb.append(", amount=").append(amount);
        sb.append(", refundFee=").append(refundFee);
        sb.append(", refundFeeStatus=").append(refundFeeStatus);
        sb.append(", orderType=").append(orderType);
        sb.append(", acceptStatus=").append(acceptStatus);
        sb.append(", status=").append(status);
        sb.append(", payChannel=").append(payChannel);
        sb.append(", ipAddr=").append(ipAddr);
        sb.append(", errMsg=").append(errMsg);
        sb.append(", reason=").append(reason);
        sb.append(", ext=").append(ext);
        sb.append(", remark=").append(remark);
        sb.append(", ctime=").append(ctime);
        sb.append(", mtime=").append(mtime);
        sb.append("]");
        return sb.toString();
    }
}