package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_cooperation_info")
public class TytCooperationInfo implements java.io.Serializable {

	private static final long serialVersionUID = 1682233978881760935L;
	private Long id;
	private String companyName;
	private String teleName;
	private String telePhone;
	private String sourceType;
	private Date ctime;


	

	public TytCooperationInfo() {
		super();
		// TODO Auto-generated constructor stub
	}

	public TytCooperationInfo(String companyName, String teleName,
			String telePhone, String sourceType, Date ctime) {
		super();
		this.companyName = companyName;
		this.teleName = teleName;
		this.telePhone = telePhone;
		this.sourceType = sourceType;
		this.ctime = ctime;
	}


	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "company_name")
	public String getCompanyName() {
		return this.companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	@Column(name = "tele_name")
	public String getTeleName() {
		return this.teleName;
	}

	public void setTeleName(String teleName) {
		this.teleName = teleName;
	}

	@Column(name = "tele_phone")
	public String getTelePhone() {
		return this.telePhone;
	}

	public void setTelePhone(String telePhone) {
		this.telePhone = telePhone;
	}

	@Column(name = "source_type")
	public String getSourceType() {
		return this.sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

}