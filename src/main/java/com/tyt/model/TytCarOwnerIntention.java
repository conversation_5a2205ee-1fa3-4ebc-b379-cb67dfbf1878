package com.tyt.model;

import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/**
 * TytCarOwnerIntention entity. <AUTHOR> Persistence Tools
 */
@JsonSerialize(include=JsonSerialize.Inclusion.NON_NULL)
@Entity
@Table(name = "tyt_car_owner_intention", catalog = "tyt")
public class TytCarOwnerIntention implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 9081328664662772450L;
	private Long id;
	private Long srcMsgId;
	private Long userId;
	private String userSource;
	private String cellPhone;
	private Long carUserId;
	private String carPhone;
	private Date carRegTime;
	private Integer identityType;
	private String identityName;
	private Long carId;
	private Integer status;
	private int infoFeeType;
	private Integer payAmount;
	private String payPhone;
	private Date lastLinkTime;
	private Date ctime;
	private Date utime;
	private String carUserName;
	//意向信息
	private String intentionInfo;

	private int offerType;
	private float cashOffer;
	private float oilOffer;


	private Integer minIntentionPrice;
		
	private Integer maxIntentionPrice;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "src_msg_id")
	public Long getSrcMsgId() {
		return this.srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "user_source")
	public String getUserSource() {
		return userSource;
	}

	public void setUserSource(String userSource) {
		this.userSource = userSource;
	}

	@Column(name = "cell_phone")
	public String getCellPhone() {
		return this.cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "car_user_id")
	public Long getCarUserId() {
		return this.carUserId;
	}

	public void setCarUserId(Long carUserId) {
		this.carUserId = carUserId;
	}

	@Column(name = "car_phone")
	public String getCarPhone() {
		return this.carPhone;
	}

	public void setCarPhone(String carPhone) {
		this.carPhone = carPhone;
	}

	@Column(name = "car_reg_time")
	public Date getCarRegTime() {
		return this.carRegTime;
	}

	public void setCarRegTime(Date carRegTime) {
		this.carRegTime = carRegTime;
	}

	@Column(name = "identity_type")
	public Integer getIdentityType() {
		return this.identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	@Column(name = "identity_name")
	public String getIdentityName() {
		return this.identityName;
	}

	public void setIdentityName(String identityName) {
		this.identityName = identityName;
	}

	@Column(name = "car_id")
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "info_fee_type")
	public int getInfoFeeType() {
		return infoFeeType;
	}

	public void setInfoFeeType(int infoFeeType) {
		this.infoFeeType = infoFeeType;
	}

	@Column(name = "pay_amount")
	public Integer getPayAmount() {
		return this.payAmount;
	}

	public void setPayAmount(Integer payAmount) {
		this.payAmount = payAmount;
	}

	@Column(name = "pay_phone")
	public String getPayPhone() {
		return this.payPhone;
	}

	public void setPayPhone(String payPhone) {
		this.payPhone = payPhone;
	}

	@Column(name = "last_link_time")
	public Date getLastLinkTime() {
		return this.lastLinkTime;
	}

	public void setLastLinkTime(Date lastLinkTime) {
		this.lastLinkTime = lastLinkTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "car_user_name")
	public String getCarUserName() {
		return carUserName;
	}

	public void setCarUserName(String carUserName) {
		this.carUserName = carUserName;
	}
	@Column(name = "intention_info")
	public String getIntentionInfo() {
		return intentionInfo;
	}

	public void setIntentionInfo(String intentionInfo) {
		this.intentionInfo = intentionInfo;
	}
	@Column(name = "min_intention_price")
	public Integer getMinIntentionPrice() {
		return minIntentionPrice;
	}

	public void setMinIntentionPrice(Integer minIntentionPrice) {
		this.minIntentionPrice = minIntentionPrice;
	}
	@Column(name = "max_intention_price")
	public Integer getMaxIntentionPrice() {
		return maxIntentionPrice;
	}

	public void setMaxIntentionPrice(Integer maxIntentionPrice) {
		this.maxIntentionPrice = maxIntentionPrice;
	}

	@Column(name = "offer_type")
	public int getOfferType() {
		return offerType;
	}

	public void setOfferType(int offerType) {
		this.offerType = offerType;
	}

	@Column(name = "cash_offer")
	public float getCashOffer() {
		return cashOffer;
	}

	public void setCashOffer(float cashOffer) {
		this.cashOffer = cashOffer;
	}

	@Column(name = "oil_offer")
	public float getOilOffer() {
		return oilOffer;
	}

	public void setOilOffer(float oilOffer) {
		this.oilOffer = oilOffer;
	}
}
