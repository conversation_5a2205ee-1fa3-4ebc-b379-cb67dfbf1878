package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
/**
 * 反馈信息实体
 * <AUTHOR>
 *
 */
@Entity
@Table(name="tyt_advice")
public class Advice implements Serializable {
	
	   private static final long serialVersionUID = 1L;
	   Long id ;
	   Integer title ;//标题
	   String content;//内容
	   String cellPhone;//发布账号
	   Timestamp ctime;//采集时间
	   Integer platId;//终端来源
	   String  version;//版本
	   Integer status;//状态
	   String telPhone;//联系方式
	   String dutyMan;//处理责任人
	private Integer source;
	private String cName;
	private Date mtime;
	private Integer suggestType;
	private Integer suggestTypeDetail;
	private String feedbackClientResult;
	private Integer isMakeWorkorder;

	private Long quesOne;

	private String  quesOneName;

	private Long quesTwo;

	private String quesTwoName;

	private Long quesThree;

	private String quesThreeName;

	private Long quesFour;

	private String quesFourName;
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="title")
	public Integer getTitle() {
		return title;
	}
	public void setTitle(Integer title) {
		this.title = title;
	}
	@Column(name="content")
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}
	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	@Column(name="version")
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	@Column(name="source")
	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}
	@Column(name="cname")
	public String getcName() {
		return cName;
	}

	public void setcName(String cName) {
		this.cName = cName;
	}

	@Column(name="advice_status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="tel_phone")
	public String getTelPhone() {
		return telPhone;
	}
	public void setTelPhone(String telPhone) {
		this.telPhone = telPhone;
	}
	@Column(name="duty_man")
	public String getDutyMan() {
		return dutyMan;
	}
	public void setDutyMan(String dutyMan) {
		this.dutyMan = dutyMan;
	}
	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name="suggest_type")
	public Integer getSuggestType() {
		return suggestType;
	}

	public void setSuggestType(Integer suggestType) {
		this.suggestType = suggestType;
	}
	@Column(name="suggest_type_detail")
	public Integer getSuggestTypeDetail() {
		return suggestTypeDetail;
	}

	public void setSuggestTypeDetail(Integer suggestTypeDetail) {
		this.suggestTypeDetail = suggestTypeDetail;
	}
	@Column(name="feedback_client_result")
	public String getFeedbackClientResult() {
		return feedbackClientResult;
	}

	public void setFeedbackClientResult(String feedbackClientResult) {
		this.feedbackClientResult = feedbackClientResult;
	}

    @Column(name="is_make_workorder")
    public Integer getIsMakeWorkorder() {
        return isMakeWorkorder;
    }

    public void setIsMakeWorkorder(Integer isMakeWorkorder) {
        this.isMakeWorkorder = isMakeWorkorder;
    }

	@Column(name="ques_one")
	public Long getQuesOne() {
		return quesOne;
	}

	public void setQuesOne(Long quesOne) {
		this.quesOne = quesOne;
	}
	@Column(name="ques_one_name")
	public String getQuesOneName() {
		return quesOneName;
	}

	public void setQuesOneName(String quesOneName) {
		this.quesOneName = quesOneName;
	}
	@Column(name="ques_two")
	public Long getQuesTwo() {
		return quesTwo;
	}

	public void setQuesTwo(Long quesTwo) {
		this.quesTwo = quesTwo;
	}
	@Column(name="ques_two_name")
	public String getQuesTwoName() {
		return quesTwoName;
	}

	public void setQuesTwoName(String quesTwoName) {
		this.quesTwoName = quesTwoName;
	}
	@Column(name="ques_three")
	public Long getQuesThree() {
		return quesThree;
	}

	public void setQuesThree(Long quesThree) {
		this.quesThree = quesThree;
	}
	@Column(name="ques_three_name")
	public String getQuesThreeName() {
		return quesThreeName;
	}

	public void setQuesThreeName(String quesThreeName) {
		this.quesThreeName = quesThreeName;
	}
	@Column(name="ques_four")
	public Long getQuesFour() {
		return quesFour;
	}

	public void setQuesFour(Long quesFour) {
		this.quesFour = quesFour;
	}
	@Column(name="ques_four_name")
	public String getQuesFourName() {
		return quesFourName;
	}

	public void setQuesFourName(String quesFourName) {
		this.quesFourName = quesFourName;
	}

	/**
     * 反馈类型枚举
     *
     */
    public enum AdviceTitleEnum {
    	
    	诚信问题(1),软件错误(2),改进建议(3),其他问题(4),功能建议(5);
    	public int code;
    	AdviceTitleEnum(int code) {
              this.code = code;
          }
        public static AdviceTitleEnum getClientSignEnum(int code) {
        	AdviceTitleEnum[] adviceTitleEnums = AdviceTitleEnum.values();
            for (AdviceTitleEnum adviceTitleEnum : adviceTitleEnums) {
                if (adviceTitleEnum.code==code) {
                    return adviceTitleEnum;
                }
            }
            return AdviceTitleEnum.诚信问题;
        } 
        /**
         * 判读code是否正确
         * @param code
         * @return
         */
        public static boolean isAdviceTitleEnumcode( int code) {
        	AdviceTitleEnum[] adviceTitleEnums = AdviceTitleEnum.values();
        	for (AdviceTitleEnum adviceTitleEnum : adviceTitleEnums) {
                if (adviceTitleEnum.code==code) {
                    return true;
                }
            }
            return false;
        }
        
        
    }
}
