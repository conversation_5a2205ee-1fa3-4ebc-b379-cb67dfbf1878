package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="tyt_user_mac")
public class TytUserMac implements Serializable{

/**
	 * 
	 */
	private static final long serialVersionUID = -5383277253417658439L;

	private Long id;//
	private String mac;//终端唯一标识
	private Long userId;//用户ID
	private String userName;//用户姓名
	private String cellPhone;//手机号(账号)
	private String idcard;//身份证号
	private String clientType;//终端类型
	private Integer clientSign;//客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
	private Date ctime;//创建时间
	private Date mtime;//更新时间
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="mac")
	public String getMac() {
		return mac;
	}
	public void setMac(String mac) {
		this.mac = mac;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="user_name")
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="idcard")
	public String getIdcard() {
		return idcard;
	}
	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
	@Column(name="client_type")
	public String getClientType() {
		return clientType;
	}
	public void setClientType(String clientType) {
		this.clientType = clientType;
	}
	@Column(name="client_sign")
	public Integer getClientSign() {
		return clientSign;
	}
	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Override
	public String toString() {
		return "TytUserMac [id=" + id + ", mac=" + mac + ", userId=" + userId
				+ ", userName=" + userName + ", cellPhone=" + cellPhone
				+ ", idcard=" + idcard + ", clientType=" + clientType
				+ ", clientSign=" + clientSign + ", ctime=" + ctime
				+ ", mtime=" + mtime + "]";
	}
	
	
}
