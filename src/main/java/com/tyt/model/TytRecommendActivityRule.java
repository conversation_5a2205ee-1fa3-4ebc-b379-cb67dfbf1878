package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 *推荐活动规则关系表 
 */
@Entity
@Table(name = "tyt_recommend_activity_rule")
public class TytRecommendActivityRule implements java.io.Serializable{

	private static final long serialVersionUID = 5067854725561853397L;
	private Long id;//编号
	private Long recaId;//活动ID
	private String recTitle;//规则名称
	private Integer recIdentity;//使用身份
	private String recProvince;//使用省份
	private String recCity;//适用城市
	private Long ruleId;//'规则ID',
	private Date ruleBegintime;//'关联活动开始时间-绑定规则, 在时间段内，不允许再关联其他活动',
	private Date ruleEndtime;//'关联活动结束时间-绑定规则',
	private Date ctime;//
	private Date mtime;//
	private Integer state;//状态   备用
	private String ruleContent;
	
	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="reca_id")
	public Long getRecaId() {
		return recaId;
	}
	public void setRecaId(Long recaId) {
		this.recaId = recaId;
	}
	@Column(name="rec_title")
	public String getRecTitle() {
		return recTitle;
	}
	public void setRecTitle(String recTitle) {
		this.recTitle = recTitle;
	}
	@Column(name="rec_identity")
	public Integer getRecIdentity() {
		return recIdentity;
	}
	public void setRecIdentity(Integer recIdentity) {
		this.recIdentity = recIdentity;
	}
	@Column(name="rec_province")
	public String getRecProvince() {
		return recProvince;
	}
	public void setRecProvince(String recProvince) {
		this.recProvince = recProvince;
	}
	@Column(name="rec_city")
	public String getRecCity() {
		return recCity;
	}
	public void setRecCity(String recCity) {
		this.recCity = recCity;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name="state")
	public Integer getState() {
		return state;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	@Column(name="rule_content")
	public String getRuleContent() {
		return ruleContent;
	}
	public void setRuleContent(String ruleContent) {
		this.ruleContent = ruleContent;
	}
	@Column(name="rule_id")
	public Long getRuleId() {
		return ruleId;
	}
	public void setRuleId(Long ruleId) {
		this.ruleId = ruleId;
	}
	@Column(name="rule_begintime")
	public Date getRuleBegintime() {
		return ruleBegintime;
	}
	public void setRuleBegintime(Date ruleBegintime) {
		this.ruleBegintime = ruleBegintime;
	}
	@Column(name="rule_endtime")
	public Date getRuleEndtime() {
		return ruleEndtime;
	}
	public void setRuleEndtime(Date ruleEndtime) {
		this.ruleEndtime = ruleEndtime;
	}
	
	

}
