package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Entity
@Table(name="tyt_transport_dispatch")
public class TransportDispatch implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id; // ID
	private Long srcMsgId;
	private Long userId;
	private Long publishUserId;
	private String publishUserName;
	private Long dispatcherId;
	private String dispatcherName;
	private BigDecimal ownerFreight;
	private Date createTime;
	private Date modifyTime;
	private String giveGoodsPhone;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="src_msg_id")
	public Long getSrcMsgId() {
		return srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="publish_user_id")
	public Long getPublishUserId() {
		return publishUserId;
	}

	public void setPublishUserId(Long publishUserId) {
		this.publishUserId = publishUserId;
	}
	@Column(name="publish_user_name")
	public String getPublishUserName() {
		return publishUserName;
	}

	public void setPublishUserName(String publishUserName) {
		this.publishUserName = publishUserName;
	}
	@Column(name="dispatcher_id")
	public Long getDispatcherId() {
		return dispatcherId;
	}

	public void setDispatcherId(Long dispatcherId) {
		this.dispatcherId = dispatcherId;
	}
	@Column(name="dispatcher_name")
	public String getDispatcherName() {
		return dispatcherName;
	}

	public void setDispatcherName(String dispatcherName) {
		this.dispatcherName = dispatcherName;
	}
	@Column(name="owner_freight")
	public BigDecimal getOwnerFreight() {
		return ownerFreight;
	}

	public void setOwnerFreight(BigDecimal ownerFreight) {
		this.ownerFreight = ownerFreight;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="modify_time")
	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}
	@Column(name="give_goods_phone")
	public String getGiveGoodsPhone() {
		return giveGoodsPhone;
	}

	public void setGiveGoodsPhone(String giveGoodsPhone) {
		this.giveGoodsPhone = giveGoodsPhone;
	}
}
