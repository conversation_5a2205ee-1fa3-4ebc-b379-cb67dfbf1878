package com.tyt.repeat.dao.impl;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TransportRepeat;
import com.tyt.repeat.dao.RepeatTransportDao;
import org.springframework.stereotype.Repository;

@Repository("repeatTransportDao")
public class RepeatTransportDaoImpl extends BaseDaoImpl<TransportRepeat, Long> implements RepeatTransportDao {

	public RepeatTransportDaoImpl() {
		this.setEntityClass(TransportRepeat.class);
	}
}
