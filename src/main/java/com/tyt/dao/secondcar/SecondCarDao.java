package com.tyt.dao.secondcar;

import java.sql.Timestamp;
import java.util.List;

import com.tyt.dao.base.BaseDao;
import com.tyt.model.SecondCar;

public interface SecondCarDao extends BaseDao<SecondCar,Long> {
	public SecondCar findById(int id);
	public List<SecondCar> QueryByDistinguish(int distinguish);
    public void insertCollect( final Integer visiteStatus,final Long userId,final Long secondCarId,final Timestamp ctime);
    public void decreServeDays();
}
