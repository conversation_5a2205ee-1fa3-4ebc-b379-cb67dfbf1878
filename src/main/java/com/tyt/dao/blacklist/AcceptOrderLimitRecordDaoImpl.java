package com.tyt.dao.blacklist;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.AcceptOrderLimitRecord;
import org.springframework.stereotype.Repository;

@Repository("acceptOrderLimitRecordDao")
public class AcceptOrderLimitRecordDaoImpl extends BaseDaoImpl<AcceptOrderLimitRecord, Long> implements AcceptOrderLimitRecordDao{
	public AcceptOrderLimitRecordDaoImpl() {
		this.setEntityClass(AcceptOrderLimitRecord.class);
	}

}
