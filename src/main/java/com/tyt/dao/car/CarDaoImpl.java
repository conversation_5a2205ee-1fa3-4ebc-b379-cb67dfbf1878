package com.tyt.dao.car;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.Car;
import com.tyt.util.TimeUtil;
import com.tyt.web.fore.querybean.PersonalCenterCar;
@Repository("carDao")
public class CarDaoImpl extends BaseDaoImpl<Car, Long> implements CarDao {
	  public CarDaoImpl() {
	        this.setEntityClass(Car.class);
	    }

	@Override
	public void updateInfo(final Long id, final String failureReason,final String remark,
			final String status) {
		getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("update tyt_car set failure_reason=?,remark=?,auth=?,update_time=? where id=?");
                query.setParameter(0, failureReason).setParameter(1, remark)
                .setParameter(2, status).setParameter(3, TimeUtil.formatDateTime(new Date()))
                .setParameter(4, id).executeUpdate();
                return null;
            }
        });
		
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<PersonalCenterCar> getQueryCarList(final Long userId,final String status) {
		
		HibernateTemplate tmpl = getHibernateTemplate();
		return (List<PersonalCenterCar>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            	SQLQuery query = session.createSQLQuery("select id,head_city as headCity,head_no as headNo,auth as auth,is_delete isDelete from tyt_car where user_id=? and auth in(?) order by id desc");
                return query.setResultTransformer(Transformers.aliasToBean(PersonalCenterCar.class)).setParameter(0, userId).setParameter(1, status).list(); 
                
            }
        });
	}
//	@SuppressWarnings("unchecked")
//	@Override
//	public boolean isExitEnabled(final Long userId) throws Exception {
//		List<String> list=(List<String>) getHibernateTemplate().execute(new HibernateCallback<Object>() {
//            @Override
//            public Object doInHibernate(Session session)
//                    throws HibernateException, SQLException {
//                SQLQuery query = session.createSQLQuery("select * from tyt_car where user_id=? and auth=1");
//                return query.setParameter(0, userId).list();
//            }
//        });
//		return list.size()>0;
//	}
}
