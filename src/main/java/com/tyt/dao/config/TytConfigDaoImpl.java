package com.tyt.dao.config;

import java.sql.SQLException;
import java.util.Date;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytConfig;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("tytConfigDao")
public class TytConfigDaoImpl   extends BaseDaoImpl<TytConfig, Long>  implements  TytConfigDao   {
    public TytConfigDaoImpl() {
        this.setEntityClass(TytConfig.class);
    }

	@Override
	public void update(final String key,final String value) {
		 HibernateTemplate tmpl = getHibernateTemplate();
	        tmpl.execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	            	SQLQuery query = session.createSQLQuery("update tyt_config set value = ?,update_time=?  where name=?");
	            	query.setParameter(0, value).setParameter(1,TimeUtil.formatDateTime(new Date()))
	            	.setParameter(2, key).executeUpdate();
	                return null;
	            }
	        });
		
	}
}
