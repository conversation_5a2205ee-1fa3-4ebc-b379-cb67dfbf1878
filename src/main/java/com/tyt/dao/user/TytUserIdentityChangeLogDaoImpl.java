package com.tyt.dao.user;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytUserIdentityChangeLog;
import org.springframework.stereotype.Repository;

@Repository("tytUserIdentityChangeLogDao")
public class TytUserIdentityChangeLogDaoImpl extends BaseDaoImpl<TytUserIdentityChangeLog, Long> implements TytUserIdentityChangeLogDao {
    public TytUserIdentityChangeLogDaoImpl() {
        this.setEntityClass(TytUserIdentityChangeLog.class);
    }
}
