package com.tyt.dao.user;

import java.util.Date;

import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytUserIdentityAuth;

@Repository("tytUserIdentityAuthDao")
public class TytUserIdentityAuthDaoImpl extends
		BaseDaoImpl<TytUserIdentityAuth, Long> implements
		TytUserIdentityAuthDao {
	public TytUserIdentityAuthDaoImpl() {
		this.setEntityClass(TytUserIdentityAuth.class);
	}

	@Override
	public TytUserIdentityAuth saveIdentityAuth(Long userId, String mobile,
			Integer userClass, Integer identityType,String trueName,
			Date nowDate) throws Exception {
		TytUserIdentityAuth userIdentityAuth = new TytUserIdentityAuth();
		userIdentityAuth.setUserId(userId);
		userIdentityAuth.setMobile(mobile);
		userIdentityAuth.setTrueName(trueName);
		userIdentityAuth.setUserClass(userClass);
		userIdentityAuth.setIdentityType(identityType);
		userIdentityAuth.setCtime(nowDate);
		userIdentityAuth.setUtime(nowDate);
		this.insert(userIdentityAuth);
		return userIdentityAuth;
	}

}
