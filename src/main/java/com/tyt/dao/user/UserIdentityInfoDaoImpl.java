package com.tyt.dao.user;

import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.TytUserIdentityInfo;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("userIdentityInfoDao")
public class UserIdentityInfoDaoImpl   extends BaseDaoImpl<TytUserIdentityInfo, Long>  implements  UserIdentityInfoDao   {
    public UserIdentityInfoDaoImpl() {
        this.setEntityClass(TytUserIdentityInfo.class);
    }
}
