package com.tyt.dao.user;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.UserPhoneImport;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/04/12 17:05
 */
@Repository("userPhoneImportDao")
public class UserPhoneImportDaoImpl extends BaseDaoImpl<UserPhoneImport, Long> implements UserPhoneImportDao {

    public UserPhoneImportDaoImpl() {
        setEntityClass(UserPhoneImport.class);
    }
}
