package com.tyt.dao.inforecord;

import java.math.BigInteger;
import java.sql.SQLException;
import java.util.Date;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.InfoRecord;
import com.tyt.util.TimeUtil;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("infoRecordDao")
public class InfoRecordDaoImpl   extends BaseDaoImpl<InfoRecord, Long>  implements  InfoRecordDao   {
    public InfoRecordDaoImpl() {
        this.setEntityClass(InfoRecord.class);
    }
    
    /**
     * 得到记录条数
     */
    	@Override
    	public int getCounts(final Date startDate, final Date endDate) {
    		HibernateTemplate tmpl = getHibernateTemplate();
            return  ((BigInteger)tmpl.execute(new HibernateCallback<Object>() {
                @Override
                public Object doInHibernate(Session session)
                        throws HibernateException, SQLException {
                	String sqlCondition="select count(*) from info_record where stat_time>='"
                            +TimeUtil.formatDate(startDate)
                            +"' AND stat_time<='"+TimeUtil.formatDate(endDate)+"'";
                    SQLQuery query = session.createSQLQuery(sqlCondition);
                    
                    return query.list().get(0);
                }
            })).intValue();
    	}
}
