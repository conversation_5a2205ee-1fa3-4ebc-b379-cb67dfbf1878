package com.tyt.datamigrate.service;

import com.tyt.model.AccountDataMigrate;
import com.tyt.model.Order;
import com.tyt.model.TytUserAccountMigrate;
import com.tyt.service.base.BaseService;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AccDataMigrateService extends BaseService<AccountDataMigrate, Long> {

    /**
     * 可用余额迁移
     *
     * @param vipUserIds
     * @param order
     * @throws Exception
     */
    Pair<Boolean,BigDecimal>  saveBalanceDataConfirm(List<Long> vipUserIds,  Order order, BigDecimal orderBalance, Map<String, BigDecimal> confirmMap, int count) throws Exception;

    /**
     * 冻结余额迁移
     *
     * @param order
     * @param orderBalance
     * @param confirmMap
     * @param tytUserAccountMigrate
     * @throws Exception
     */
    Pair<Boolean,BigDecimal> saveFrozenBalanceDataConfirm(Order order, BigDecimal orderBalance, Map<String, BigDecimal> confirmMap, TytUserAccountMigrate tytUserAccountMigrate) throws Exception;
}
