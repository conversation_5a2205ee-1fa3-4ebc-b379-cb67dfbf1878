package com.tyt.exception;
/**
* @description excel导入异常
* <AUTHOR>
* @date 2023/8/24 15:11
* @version 1.0
*/
public class ExcelImportException extends RuntimeException {
    private Integer code;


    public ExcelImportException(Integer code, String errorMessage) {
        super(errorMessage);
        this.code = code;
    }

    public ExcelImportException(String errorMessage){
        super(errorMessage);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

}
