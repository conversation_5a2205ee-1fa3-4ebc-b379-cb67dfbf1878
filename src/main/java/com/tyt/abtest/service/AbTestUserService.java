package com.tyt.abtest.service;

import com.tyt.abtest.bean.AbTestUserReq;
import com.tyt.abtest.bean.ImportUserData;
import com.tyt.abtest.bean.PageData;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.mybatis.mapper.model.TytAbtestConfigUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AbTestUserService {

    /**
     * 保存AB测试用户
     *
     * @param importUserData 用户信息
     * @param abTestConfigId AB测试配置ID
     */
    void saveUser(ImportUserData importUserData, Long abTestConfigId, EmployeeQueryBean employee);

    /**
     * 保存AB测试用户Excel
     *
     * @param fileField      文件
     * @param abTestConfigId AB测试配置ID
     * @return
     */
    void importUser(MultipartFile fileField, Long abTestConfigId, Employee<PERSON><PERSON>yBean curUser, Integer operateType);


    /**
     * 获取配置列表
     *
     * @param req
     * @return
     */
    PageData<TytAbtestConfigUser> getABTestUserList(AbTestUserReq req);

    /**
     * 批量删除用户
     *
     * @param abTestConfigId
     * @param userIdList
     * @return
     */
    int delByConfigAndUser(Long abTestConfigId, List<Long> userIdList);

    /**
     * 批量导入用户
     *
     * @param abTestUserService
     * @param importUserDataList
     * @param abTestConfigId
     * @param employee
     */
    void importUserAddBatches(AbTestUserService abTestUserService, List<ImportUserData> importUserDataList, Long abTestConfigId, EmployeeQueryBean employee);

}
