package com.tyt.insurance.bean;

import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;

/**
 * 封装保险信息的实体
 * 
 * <AUTHOR>
 * @date 2016-9-1上午10:38:37
 * @description
 */
public class InsuranceBean {
	private Integer id;
	/*
	 * 数据来源：1轮播点击;2预约上传
	 */
	private Integer source;
	/*
	 * 处理状态：1待跟踪2跟踪无效3已处理
	 */
	private Integer dealStatus;
	/*
	 * 用户ID
	 */
	private Integer userId;
	/*
	 * 注册手机号
	 */
	private String registerTel;
	/*
	 * 联系人
	 */
	private String linkman;
	/*
	 * 业务员
	 */
	private String salesMan;
	/*
	 * 联系电话：用逗号分隔
	 */
	private String telephones;
	/*
	 * 下次联系时间：默认空值
	 */
	private String nextContactTime;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 创建时间
	 */
	private String createTime;
	/*
	 * 修改时间
	 */
	private String updateTime;



	public String getTel1() {
		return singleTel(1);
	}

	public String getTel2() {
		return singleTel(2);
	}

	public String getTel3() {
		return singleTel(3);
	}

	private String singleTel(int i) {
		if (StringUtils.isNotEmpty(telephones) && telephones.split(",").length >= i) {
			return telephones.split(",")[i - 1];
		} else {
			return "";
		}
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public Integer getDealStatus() {
		return dealStatus;
	}

	public void setDealStatus(Integer dealStatus) {
		this.dealStatus = dealStatus;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getRegisterTel() {
		return registerTel;
	}

	public void setRegisterTel(String registerTel) {
		this.registerTel = registerTel;
	}

	public String getLinkman() {
		return linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	public String getSalesMan() {
		return salesMan;
	}

	public void setSalesMan(String salesMan) {
		this.salesMan = salesMan;
	}

	public String getTelephones() {
		return telephones;
	}

	public void setTelephones(String telephones) {
		this.telephones = telephones;
	}

	public String getNextContactTime() {
		return dealTime(nextContactTime);
	}

	private String dealTime(String time) {
		if (StringUtils.isNotEmpty(time) && time.length() == 21) {
			return time.substring(0, time.lastIndexOf("."));
		} else {
			return time;
		}
	}

	public void setNextContactTime(String nextContactTime) {
		this.nextContactTime = nextContactTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreateTime() {
		return dealTime(createTime);
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return dealTime(updateTime);
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}


	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
