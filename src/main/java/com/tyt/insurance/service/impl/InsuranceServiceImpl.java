package com.tyt.insurance.service.impl;

import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.dao.base.BaseDao;
import com.tyt.insurance.bean.InsuranceBean;
import com.tyt.insurance.bean.InsuranceQueryBean;
import com.tyt.insurance.service.InsuranceService;
import com.tyt.model.PageBean;
import com.tyt.model.TytSource;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.user.UserService;

@Service("insuranceService")
public class InsuranceServiceImpl extends BaseServiceImpl<TytSource, Long> implements InsuranceService {
	private static final long ONE_DAY_IN_SECOND = 23 * 60 * 60;

    public static Logger logger = LoggerFactory.getLogger(InsuranceServiceImpl.class);

	@Resource(name = "insuranceDao")
	public void setBaseDao(BaseDao<TytSource, Long> insuranceDao) {
		super.setBaseDao(insuranceDao);
	}
	@Resource(name="userService")
	private UserService userService;



	@SuppressWarnings("deprecation")
	@Override
	public List<InsuranceBean> getInsuranceList(InsuranceQueryBean queryBean, PageBean pageBean) throws ParseException {
		SimpleDateFormat nextContactTimeSdf = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat createTimeSdf = new SimpleDateFormat("yyyy-MM-dd");
		List<Object> params = new ArrayList<Object>();
		StringBuilder sbCount = new StringBuilder("select count(*) from tyt_booking_insurance tbi where 1=1 ");
		StringBuffer sb = new StringBuffer();
		if (StringUtils.isNotEmpty(queryBean.getSource()) && !"0".equals(queryBean.getSource())) {
			sb.append(" and tbi.source=?");
			params.add(queryBean.getSource());
		}
		if (StringUtils.isNotEmpty(queryBean.getContactPerson())) {
			sb.append(" and tbi.linkman=?");
			params.add(queryBean.getContactPerson());
		}
		if (StringUtils.isNotEmpty(queryBean.getContactPhone())) {
			sb.append(" and tbi.telephones LIKE ?");
			params.add("%" + queryBean.getContactPhone() + "%");
		}
		if (StringUtils.isNotEmpty(queryBean.getCreateTimeEnd())) {
			sb.append(" and UNIX_TIMESTAMP(tbi.create_time) <= ?");
			// 默认获取的时间是一天的开始需要通过加时间修改为一天的结束
			params.add((createTimeSdf.parse(queryBean.getCreateTimeEnd()).getTime() / 1000) + ONE_DAY_IN_SECOND);
		}
		if (StringUtils.isNotEmpty(queryBean.getCreateTimeStart())) {
			sb.append(" and UNIX_TIMESTAMP(tbi.create_time) >= ?");
			params.add(createTimeSdf.parse(queryBean.getCreateTimeStart()).getTime() / 1000);
		}
		if (StringUtils.isNotEmpty(queryBean.getDealStatus()) && !"0".equals(queryBean.getDealStatus())) {
			sb.append(" and tbi.deal_status = ?");
			params.add(queryBean.getDealStatus());
		}
		if (StringUtils.isNotEmpty(queryBean.getNextContactTimeEnd())) {
			sb.append(" and UNIX_TIMESTAMP(tbi.next_contact_time) <= ?");
			params.add((nextContactTimeSdf.parse(queryBean.getNextContactTimeEnd()).getTime() / 1000) + ONE_DAY_IN_SECOND);
		}
		if (StringUtils.isNotEmpty(queryBean.getNextContactTimeStart())) {
			sb.append(" and UNIX_TIMESTAMP(tbi.next_contact_time) >= ?");
			params.add(nextContactTimeSdf.parse(queryBean.getNextContactTimeStart()).getTime() / 1000);
		}
		if (StringUtils.isNotEmpty(queryBean.getSalesMan())) {
			sb.append(" and tbi.sales_man=?");
			params.add(queryBean.getSalesMan());
		}
		if (StringUtils.isNotEmpty(queryBean.getRegisterTel())) {
			Long userId = userService.getUserIdByCellPhone(queryBean.getRegisterTel());
			sb.append("and tbi.user_id=? ");
			if (userId!=null) {
				params.add(userId);
			}else {
				return null;
			}
		}
		sbCount.append(sb);

		BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), params.toArray());
		List<InsuranceBean> insuranceList = new ArrayList<InsuranceBean>();
		if (rowCount != null && rowCount.longValue() > 0) {
			StringBuffer sbSql = new StringBuffer("SELECT tbi.`id`, tbi.`create_time` AS 'createTime', tbi.`deal_status` AS 'dealStatus', tbi.`linkman`, tbi.`next_contact_time` AS 'nextContactTime', tbi.`user_id` AS 'userId', tbi.`register_tel` AS 'registerTel', tbi.`remark`, tbi.`sales_man` AS 'salesMan', tbi.`source`, tbi.`telephones`, tbi.`update_time` AS 'updateTime', tbi.`user_id` AS 'userId' FROM tyt_booking_insurance tbi WHERE 1=1");
			Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
			map.put("id", Hibernate.INTEGER);
			map.put("createTime", Hibernate.STRING);
			map.put("dealStatus", Hibernate.INTEGER);
			map.put("linkman", Hibernate.STRING);
			map.put("nextContactTime", Hibernate.STRING);
			map.put("userId", Hibernate.LONG);
			map.put("registerTel", Hibernate.STRING);
			map.put("remark", Hibernate.STRING);
			map.put("salesMan", Hibernate.STRING);
			map.put("source", Hibernate.INTEGER);
			map.put("telephones", Hibernate.STRING);
			map.put("updateTime", Hibernate.STRING);
			map.put("userId", Hibernate.INTEGER);
			sbSql.append(sb);
			if ("2".equals(queryBean.getSortType())) {
				sbSql.append(" ORDER BY tbi.next_contact_time IS NULL, tbi.next_contact_time ASC ");
			} else if ("1".equals(queryBean.getSortType())) {
				sbSql.append(" order by tbi.create_time desc ");
			}
            logger.info("getInsuranceList 【sbSql:{}】",sbSql.toString());
			if (pageBean == null) {
				insuranceList = this.getBaseDao().search(sbSql.toString(), map, InsuranceBean.class, params.toArray(), 1, rowCount.intValue());
			} else {
				pageBean.setRowCount(rowCount.longValue());
				insuranceList = this.getBaseDao().search(sbSql.toString(), map, InsuranceBean.class, params.toArray(), pageBean.getCurrentPage(), pageBean.getPageSize());
			}
		}
        logger.info("getInsuranceList 【insuranceList:{}】",insuranceList.size());
		return insuranceList;
	}

	@Override
	public InsuranceBean getInsuranceById(Long id) {
	    //组装sql
		StringBuffer sbSql = new StringBuffer("SELECT tbi.`id`, tbi.`user_id` AS 'userId', tbi.`register_tel` AS 'registerTel',  tbi.`telephones` FROM tyt_booking_insurance tbi WHERE  tbi.id=?");
        //请求参数
        List<Object> params = new ArrayList<Object>();
        params.add(id);
        //返回参数
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("id", Hibernate.INTEGER);
		map.put("userId", Hibernate.INTEGER);
		map.put("registerTel", Hibernate.STRING);
		map.put("telephones", Hibernate.STRING);
        List<InsuranceBean> insuranceBeans = this.getBaseDao().search(sbSql.toString(), map, InsuranceBean.class, params.toArray());
        if(insuranceBeans!=null&&insuranceBeans.size()>0){
            return insuranceBeans.get(0);
        }
        return null;
    }

	@Override
	public void updateInsurance(String linkman, String registerTel, String salesMan, String nextContactTime, String remark, String dealStatus, String id, String telephones) {
		String sql = null;
		Object[] params = null;
		if (!StringUtils.isNotEmpty(nextContactTime)) {
			sql = "UPDATE tyt_booking_insurance tbi SET tbi.`linkman`=?, tbi.`register_tel`=?,tbi.`sales_man`=?,tbi.`remark`=?,tbi.`deal_status`=?,tbi.`telephones`=? WHERE tbi.`id`=?";
			params = new Object[] { linkman, registerTel, salesMan, remark, dealStatus, telephones, id };
		} else {
			sql = "UPDATE tyt_booking_insurance tbi SET tbi.`linkman`=?, tbi.`register_tel`=?,tbi.`sales_man`=?,tbi.`next_contact_time`=?,tbi.`remark`=?,tbi.`deal_status`=?,tbi.`telephones`=? WHERE tbi.`id`=?";
			params = new Object[] { linkman, registerTel, salesMan, nextContactTime, remark, dealStatus, telephones, id };
		}
		this.getBaseDao().executeUpdateSql(sql, params);
	}

//	@Override
//	public void objectInsuranceToCvsString(StringBuilder content, InsuranceBean insurance) {
//		content.append(insurance.getId());
//		content.append(",");
//		content.append(insurance.getSource() == 1 ? "轮播点击" : "预约上传");
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getCreateTime())) {
//			content.append(insurance.getCreateTime());
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getNextContactTime())) {
//			content.append(insurance.getNextContactTime());
//		}
//		content.append(",");
//		switch (insurance.getDealStatus()) {
//		case 1:
//			content.append("待跟踪");
//			break;
//		case 2:
//			content.append("跟踪无效");
//			break;
//		case 3:
//			content.append("已处理");
//			break;
//		case 4:
//			content.append("二次跟踪");
//			break;
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getLinkman())) {
//			content.append(insurance.getLinkman());
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getRegisterTel())) {
//			content.append(insurance.getRegisterTel());
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getTel1())) {
//			content.append(insurance.getTel1());
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getTel2())) {
//			content.append(insurance.getTel2());
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getTel3())) {
//			content.append(insurance.getTel3());
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getSalesMan())) {
//			content.append(insurance.getSalesMan());
//		}
//		content.append(",");
//		if (StringUtils.isNotEmpty(insurance.getRemark())) {
//			content.append(insurance.getRemark());
//		}
//		content.append(",");
//	}
}
