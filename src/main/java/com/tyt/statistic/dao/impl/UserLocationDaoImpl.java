package com.tyt.statistic.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.dao.base.BaseDaoImpl;
import com.tyt.model.UserLocation;
import com.tyt.statistic.dao.UserLocationDao;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("userLocationDao")
public class UserLocationDaoImpl   extends BaseDaoImpl<UserLocation, Long>  implements  UserLocationDao   {
    public UserLocationDaoImpl() {
        this.setEntityClass(UserLocation.class);
    }
}
