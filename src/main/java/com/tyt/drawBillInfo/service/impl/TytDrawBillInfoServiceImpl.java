package com.tyt.drawBillInfo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tyt.dao.base.BaseDao;
import com.tyt.drawBillInfo.bean.DrawBillSearchBean;
import com.tyt.drawBillInfo.service.TytDrawBillInfoService;
import com.tyt.goods.bean.UserBuyGoodsForSaveDrawBill;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.model.PageBean;
import com.tyt.model.TytDrawBillInfo;
import com.tyt.model.TytUserBuyGoods;
import com.tyt.service.base.BaseServiceImpl;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.service.config.TytConfigService;
import com.tyt.util.AppConfig;
import com.tyt.util.Constant;
import com.tyt.util.Encoder;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

@Service("tytDrawBillInfoService")
public class TytDrawBillInfoServiceImpl extends BaseServiceImpl<TytDrawBillInfo, Long> implements TytDrawBillInfoService {

    @Resource(name = "tytDrawBillInfoDao")
    public void setBaseDao(BaseDao<TytDrawBillInfo, Long> tytDrawBillInfoDao) {
        super.setBaseDao(tytDrawBillInfoDao);
    }

    @Resource(name="tytConfigService")
    TytConfigService tytConfigService;
    @Resource(name="userBuyGoodsService")
    private UserBuyGoodsService userBuyGoodsService;

    private static final String DRAWBILL_BASIC_LINK = "drawBill_basic_link";
    private static final String DRAWVILL_LINK_VALID_TIME = "drawBill_link_valid_time";

    @Override
    public List<TytDrawBillInfo> getDrawBillList(DrawBillSearchBean drawBillSearchBean, PageBean pageBean) {
        StringBuffer countBuffer = new StringBuffer("SELECT count(*) FROM tyt_draw_bill_info d where 1=1");
        StringBuffer conditionBuffer = new StringBuffer();
        List<Object> params = new ArrayList<Object>();
        //抬头类型
        if (drawBillSearchBean.getReceiptType()!=null){
            conditionBuffer.append(" and d.receipt_type=?");
            params.add(drawBillSearchBean.getReceiptType());
        }
        //用户账号
        if (StringUtils.isNotBlank(drawBillSearchBean.getCellPhone())){
            conditionBuffer.append(" and d.cell_phone=?");
            params.add(drawBillSearchBean.getCellPhone());
        }
        //发票状态
        if (drawBillSearchBean.getStatus()!=null){
            conditionBuffer.append(" and d.status=?");
            params.add(drawBillSearchBean.getStatus());
        }
        //订单号
        if (StringUtils.isNotBlank(drawBillSearchBean.getGoodsOrderId())){
            conditionBuffer.append(" and d.goods_order_id=?");
            params.add(drawBillSearchBean.getGoodsOrderId());
        }
        //商品类型
        if (StringUtils.isNotBlank(drawBillSearchBean.getGoodsType())){
            conditionBuffer.append(" and d.goods_type like ? ");
            params.add("%"+drawBillSearchBean.getGoodsType()+"%");
        }
        //提交时间
        if (StringUtils.isNotBlank(drawBillSearchBean.getCtimeStart())){
            conditionBuffer.append(" and d.ctime>=?");
            params.add(drawBillSearchBean.getCtimeStart()+" 00:00:00");
        }
        if (StringUtils.isNotBlank(drawBillSearchBean.getCtimeEnd())){
            conditionBuffer.append(" and d.ctime<=?");
            params.add(drawBillSearchBean.getCtimeEnd()+" 23:59:59");
        }

        BigInteger count = this.getBaseDao().query(countBuffer.append(conditionBuffer).toString(), params.toArray());
        if (count == null || count.longValue() <= 0) {
            return null;// 没有记录，直接返回null
        }
        StringBuffer selectBuffer = new StringBuffer("SELECT * FROM tyt_draw_bill_info d where 1=1");
        selectBuffer.append(conditionBuffer).append(" order by d.id desc");
        if (pageBean == null){
            return this.getBaseDao().search(selectBuffer.toString(),params.toArray(),1,count.intValue());
        }else{
            pageBean.setRowCount(count.longValue());
            return this.getBaseDao().search(selectBuffer.toString(),params.toArray(),pageBean.getCurrentPage(),pageBean.getPageSize());
        }
    }

    @Override
    public void getDrawBillCvsString(StringBuilder content, List<TytDrawBillInfo> drawBillInfoList) {
        for (TytDrawBillInfo tytDrawBillInfo : drawBillInfoList) {
            // ID
            content.append(tytDrawBillInfo.getId()).append(",");
            // 用户账号
            content.append(tytDrawBillInfo.getCellPhone()).append("\t").append(",");
            // 收件人姓名
            content.append(tytDrawBillInfo.getRecipientName()).append(",");
            // 联系电话
            content.append(tytDrawBillInfo.getRecipientPhone()).append("\t").append(",");
            // 收票地址
            content.append(tytDrawBillInfo.getRecipientAdress()).append(",");
            // 抬头类型
            String receiptType = "";
            if (tytDrawBillInfo.getReceiptType() == 1){
                receiptType = "个人";
            }else if (tytDrawBillInfo.getReceiptType() == 2){
                receiptType = "公司";
            }
            content.append(receiptType).append(",");
            // 发票类型
            String billType = "";
            if (tytDrawBillInfo.getBillType() == 1){
                billType = "增值税专用发票";
            }else if (tytDrawBillInfo.getBillType() == 2){
                billType = "增值税普通发票";
            }
            content.append(billType).append(",");
            // 名称
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getBuyerUserName())?tytDrawBillInfo.getBuyerUserName():"").append(",");
            // 身份证号
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getBuyerCardNo())?tytDrawBillInfo.getBuyerCardNo()+"\t":"").append(",");
            // 公司名称
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getCompanyName())?tytDrawBillInfo.getCompanyName():"").append(",");
            // 纳税人识别号
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getTaxpayerIdentityNumber())?tytDrawBillInfo.getTaxpayerIdentityNumber()+"\t":"").append(",");
            // 企业开户行
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getCompanyBank())?tytDrawBillInfo.getCompanyBank():"").append(",");
            // 开户行账号
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getBankNumber())?tytDrawBillInfo.getBankNumber()+"\t":"").append(",");
            // 企业注册地址
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getCompanyRegisterAddress())?tytDrawBillInfo.getCompanyRegisterAddress():"").append(",");
            // 企业电话
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getCompanyPhone())?tytDrawBillInfo.getCompanyPhone()+"\t":"").append(",");
            // 商品类型
            content.append(tytDrawBillInfo.getGoodsName().replaceAll(",", "，")).append(",");
            // 订单号
            content.append(tytDrawBillInfo.getGoodsOrderId()).append("\t").append(",");
            // 支付金额
            content.append(tytDrawBillInfo.getPayAmount()).append(",");
            // 返现金额
            content.append(tytDrawBillInfo.getReturnCash()).append(",");
            // 开票金额
            content.append(tytDrawBillInfo.getPayAmount().subtract(tytDrawBillInfo.getReturnCash())).append(",");
            // 商品购买时间
            content.append(TimeUtil.formatDateTime(tytDrawBillInfo.getBuyGoodsTime())).append(",");
            // 提交时间
            content.append(TimeUtil.formatDateTime(tytDrawBillInfo.getCtime())).append(",");
            // 更新时间
            content.append(TimeUtil.formatDateTime(tytDrawBillInfo.getMtime())).append(",");
            // 状态
            String status = "";
            if (tytDrawBillInfo.getStatus() == 1){
                status = "正常";
            }else if (tytDrawBillInfo.getStatus() == 2){
                status = "已开票";
            }else if (tytDrawBillInfo.getStatus() == 3){
                status = "已废弃";
            }
            content.append(status).append(",");
            // 操作人
            content.append(StringUtils.isNotBlank(tytDrawBillInfo.getOpUserName())?tytDrawBillInfo.getOpUserName():"");
            content.append("\r\n");
        }
    }

    @Override
    public void updateReturnCash(Long id, String returnCash, Long opUserId, String opUserName) {
        String sql = "UPDATE `tyt_draw_bill_info` SET `return_cash`=?,`op_user_id`=?,`op_user_name`=?,`mtime`=NOW() WHERE id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{returnCash, opUserId, opUserName, id});
    }

    @Override
    public void updateStatusForDiscard(Long id, Long opUserId, String opUserName) {
        String sql = "UPDATE `tyt_draw_bill_info` SET `status`=3,`op_user_id`=?,`op_user_name`=?,`mtime`=NOW() WHERE id=? and status=1";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{ opUserId, opUserName, id});
    }

    @Override
    public void updateStatusForDrawBill(String ids, Long opUserId, String opUserName) {
        String sql = "UPDATE `tyt_draw_bill_info` SET `status`=2,`op_user_id`=?,`op_user_name`=?,`mtime`=NOW() WHERE id in("+ids+") and status=1";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{ opUserId, opUserName});
    }

    @Override
    public List<String> getOrderIdsForDrawBill(String cellPhone){
        String sql = "SELECT * FROM `tyt_draw_bill_info` WHERE status<>3 ";
        List<Object> params = new ArrayList<Object>();
        sql = sql +" and cell_phone=? order by id desc";
        params.add(cellPhone);
        List<TytDrawBillInfo> list = this.getBaseDao().search(sql,params.toArray(),1,1000);
        if (list!=null && list.size()>0){
            List<String> orderIds = new ArrayList<>();
            for (TytDrawBillInfo tytDrawBillInfo : list) {
                orderIds.add(tytDrawBillInfo.getGoodsOrderId());
            }
            return orderIds;
        }
        return null;
    }

    @Override
    public String getDrawBillAdress(String orderId) {
        String time = TimeUtil.formatTimeMin(new Date());
        String basicLink = tytConfigService.getStringValue(DRAWBILL_BASIC_LINK,"https://www.teyuntong.com/fp/fp.html");
        Integer validTime = tytConfigService.getIntValue(DRAWVILL_LINK_VALID_TIME,10800);
        StringBuffer url = new StringBuffer(basicLink);
        url.append("?orderId=").append(orderId).append(time);
        String sign = Encoder.md5(url.toString() + AppConfig.getProperty("tyt.private.key"));
        url.append("&sign=").append(sign);
        RedisUtil.set(Constant.CACHE_DRAW_BILL_LINK+orderId+time,url.toString(),validTime);
        return url.toString();
    }

    @Override
    public boolean  getByOrderId(String orderId) {
        String sql = "SELECT COUNT(*) FROM `tyt_draw_bill_info` WHERE goods_order_id="+orderId+" AND STATUS<>3";
        BigInteger rowCount = this.getBaseDao().query(sql, null);
        if (rowCount.intValue()>0){
            return true;
        }
        return false;
    }

    @Override
    public void saveDrawBillInfo(TytDrawBillInfo info) throws Exception{
        String orderId = info.getGoodsOrderId().substring(0,info.getGoodsOrderId().length()-4);
        UserBuyGoodsForSaveDrawBill orders = userBuyGoodsService.getOrdersById(orderId);
        info.setGoodsOrderId(orderId);
        info.setUserId(orders.getUserId());
        info.setBuyGoodsTime(orders.getUtime());
        info.setGoodsType(orders.getGoodsTypes());
        info.setPayAmount(orders.getTotalFee());
        info.setGoodsName(orders.getGoodsName());
        info.setReturnCash(new BigDecimal(0));
        info.setStatus((short)1);
        info.setCtime(new Date());
        info.setMtime(new Date());
        this.add(info);
    }

    @Override
    public boolean getByIds(String ids) {
        String sql = "SELECT COUNT(*) FROM `tyt_draw_bill_info` WHERE `status`=3 AND id IN ("+ids+")";
        BigInteger count = this.getBaseDao().query(sql, null);
        if (count!=null && count.intValue()>0) {
            return false;
        }
        return true;
    }

    @Override
    public TytDrawBillInfo getLatestTytDrawBillInfo(String orderId) {
        String sql = "select * from tyt_draw_bill_info where goods_order_id = "+orderId+" order by id desc limit 1 ";
        List<TytDrawBillInfo> tytDrawBillInfoList = this.getBaseDao().queryForList(sql, null);
        if(CollectionUtil.isEmpty(tytDrawBillInfoList)){
            return new TytDrawBillInfo();
        }
        return tytDrawBillInfoList.get(0);
    }

    @Override
    public void updateStatusForRedChong(String ids, Long opUserId, String opUserName) {
        String sql = "UPDATE `tyt_draw_bill_info` SET `status`=6,`op_user_id`=?,`op_user_name`=?,`mtime`=NOW() WHERE id in("+ids+") and status=2";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{ opUserId, opUserName});
    }
}
