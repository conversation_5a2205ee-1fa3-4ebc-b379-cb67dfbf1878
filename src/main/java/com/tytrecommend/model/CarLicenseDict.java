package com.tytrecommend.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR> @date 2017年11月4日下午2:18:37
 * @description
 */
@Entity
@Table(name = "car_license_dict", catalog = "tyt_recommend")
public class CarLicenseDict implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7958539894659693184L;
	
	private Long id;
	private String carKey;
	private String province;
	private String city;


	@Id
	@Column(name = "id", unique = true, nullable = false)
	public long getId() {
		return this.id;
	}
	
	@Column(name = "car_key")
	public String getCarKey() {
		return carKey;
	}
	
	@Column(name = "province")
	public String getProvince() {
		return province;
	}
	@Column(name = "city")
	public String getCity() {
		return city;
	}

	public void setCarKey(String carKey) {
		this.carKey = carKey;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
