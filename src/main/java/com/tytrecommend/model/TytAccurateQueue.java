package com.tytrecommend.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytAccurateQueue entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_accurate_queue", catalog = "tyt_recommend")
public class TytAccurateQueue implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 6298776007625638088L;
	private Long id;
	private Long tsId;
	private Long carUserId;
	private Long carId;
	private String md5;
	private Integer matching;
	private Integer carIndex;
	private Integer startDistance;
	private Integer destDistance;
	private Integer status;
	private Date ctime;
	private Date utime;

	// Constructors

	/** default constructor */
	public TytAccurateQueue() {
	}

	/** minimal constructor */
	public TytAccurateQueue(Long tsId, Long carUserId, Long carId, String md5,
			Integer matching, Integer status) {
		this.tsId = tsId;
		this.carUserId = carUserId;
		this.carId = carId;
		this.md5 = md5;
		this.matching = matching;
		this.status = status;
	}

	/** full constructor */
	public TytAccurateQueue(Long tsId, Long carUserId, Long carId, String md5,
			Integer matching, Integer carIndex, Integer startDistance,
			Integer destDistance, Integer status, Date ctime, Date utime) {
		this.tsId = tsId;
		this.carUserId = carUserId;
		this.carId = carId;
		this.md5 = md5;
		this.matching = matching;
		this.carIndex = carIndex;
		this.startDistance = startDistance;
		this.destDistance = destDistance;
		this.status = status;
		this.ctime = ctime;
		this.utime = utime;
	}


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "ts_id", nullable = false)
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "car_user_id", nullable = false)
	public Long getCarUserId() {
		return this.carUserId;
	}

	public void setCarUserId(Long carUserId) {
		this.carUserId = carUserId;
	}

	@Column(name = "car_id", nullable = false)
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "md5", nullable = false, length = 32)
	public String getMd5() {
		return this.md5;
	}

	public void setMd5(String md5) {
		this.md5 = md5;
	}

	@Column(name = "matching", nullable = false)
	public Integer getMatching() {
		return this.matching;
	}

	public void setMatching(Integer matching) {
		this.matching = matching;
	}

	@Column(name = "car_index")
	public Integer getCarIndex() {
		return this.carIndex;
	}

	public void setCarIndex(Integer carIndex) {
		this.carIndex = carIndex;
	}

	@Column(name = "start_distance")
	public Integer getStartDistance() {
		return this.startDistance;
	}

	public void setStartDistance(Integer startDistance) {
		this.startDistance = startDistance;
	}

	@Column(name = "dest_distance")
	public Integer getDestDistance() {
		return this.destDistance;
	}

	public void setDestDistance(Integer destDistance) {
		this.destDistance = destDistance;
	}

	@Column(name = "status", nullable = false)
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

}