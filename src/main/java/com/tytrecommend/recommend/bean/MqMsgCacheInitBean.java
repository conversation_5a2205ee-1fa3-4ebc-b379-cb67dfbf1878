package com.tytrecommend.recommend.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;

/**
 * 异步缓存车辆偏好偏好车型 与3个基础消息表bean
 * 
 *
 */
public class MqMsgCacheInitBean extends MqBaseMessageBean {
	private Long carId; //车辆ID
	private Long userId; //用户ID
	public Long getCarId() {
		return carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

}
