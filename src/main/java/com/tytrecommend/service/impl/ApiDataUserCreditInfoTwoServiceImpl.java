package com.tytrecommend.service.impl;

import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.ApiDataUserCreditInfoTwo;
import com.tytrecommend.service.ApiDataUserCreditInfoTwoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("apiDataUserCreditInfoTwoService")
public class ApiDataUserCreditInfoTwoServiceImpl extends BaseServiceImpl<ApiDataUserCreditInfoTwo, Long> implements ApiDataUserCreditInfoTwoService {

    @Resource(name = "apiDataUserCreditInfoTwoDao")
    public void setBaseDao(BaseDao<ApiDataUserCreditInfoTwo, Long> apiDataUserCreditInfo) {
        super.setBaseDao(apiDataUserCreditInfo);
    }

    @Override
    public ApiDataUserCreditInfoTwo getApiDataUserCreditInfoTwoById(Long id) {
        return this.getBaseDao().findById(id);
    }
}
