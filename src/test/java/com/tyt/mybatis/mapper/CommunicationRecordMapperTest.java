package com.tyt.mybatis.mapper;

import com.tyt.AbstracTest;
import com.tyt.peopleorders.bean.RecordStatisticBean;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;


public class CommunicationRecordMapperTest extends AbstracTest {

    @Autowired
    private CommunicationRecordMapper communicationRecordMapper;
    @Test
    public void getLatestOffer() {
        Map<String, String> map = communicationRecordMapper.getLatestOffer("33781940");
        assertNotNull(map);
    }

    @Test
    public void doContractRecordStatisticByMsgID() {
        List<RecordStatisticBean> recordStatisticBeanList = communicationRecordMapper.doContractRecordStatisticByMsgID("33781940");
        assertEquals(recordStatisticBeanList.size(), 1);
        assertEquals(recordStatisticBeanList.get(0).getContactStatus(), 2);
    }


}