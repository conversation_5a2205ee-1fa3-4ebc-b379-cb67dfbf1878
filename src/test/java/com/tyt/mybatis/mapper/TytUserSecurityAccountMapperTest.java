package com.tyt.mybatis.mapper;

import com.tyt.AbstracTest;
import com.tyt.manager.mapper.base.TytUserSecurityAccountMapper;
import com.tyt.manager.service.base.TytUserSecurityAccountService;
import com.tyt.manager.vo.base.TytUserSecurityAccountVo;
import com.tyt.model.ResultMsgBean;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

public class TytUserSecurityAccountMapperTest  extends AbstracTest {
    @Autowired
    private TytUserSecurityAccountMapper tytUserSecurityAccountMapper;
    @Autowired
    private TytUserSecurityAccountService tytUserSecurityAccountService;


    @Test
    public void selectSecurityMoneyInfoByUserIdtest(){

        TytUserSecurityAccountVo tytUserSecurityAccountVo = tytUserSecurityAccountMapper.selectSecurityMoneyInfoByUserId(100009L);
        System.out.println(tytUserSecurityAccountVo);
    }


    @Test
    public void increaseSecurityMoneyTest() throws Exception {
        tytUserSecurityAccountService.increaseSecurityMoney(1002000313L,new BigDecimal("700"),1,81L,1111L,"2023-05-","");
    }

    @Test
    public void deductSecurityMoneyTest(){
        ResultMsgBean resultMsgBean = tytUserSecurityAccountService.deductSecurityMoney(1111L,22222L,3333L,new BigDecimal("33"),666L,"");
        System.out.println(resultMsgBean);
    }
}
