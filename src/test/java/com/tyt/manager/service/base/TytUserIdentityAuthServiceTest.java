package com.tyt.manager.service.base;

import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.manager.base.TytTestBase;
import com.tyt.model.TytUserBuyGoods;
import com.tyt.permission.enums.GoodsType;
import com.tyt.service.user.TytUserIdentityAuthService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TytUserIdentityAuthServiceTest extends TytTestBase {
    @Autowired
    private TytUserIdentityAuthService tytUserIdentityAuthService;
    @Autowired
    private UserBuyGoodsService userBuyGoodsService;

    @Test
    public void giveFindGoodsRightTest() throws Exception {
        tytUserIdentityAuthService.saveGiveFindGoodsRight("21",1000001097L,"12000009999",null);
       //TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(1000001094L, GoodsType.车体验.getId(), "login", 2, null);
//        System.out.println(userBuyGoods);
    }
}
