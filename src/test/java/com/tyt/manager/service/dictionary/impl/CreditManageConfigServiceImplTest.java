package com.tyt.manager.service.dictionary.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.CreditManageConfigSub;
import com.tyt.manager.entity.base.CreditManageConfigUser;
import com.tyt.manager.service.dictionary.CreditManageConfigService;
import com.tyt.manager.vo.base.CreditConfigBean;
import com.tyt.manager.vo.base.CreditConfigRequestBean;
import com.tyt.manager.vo.base.CreditConfigSubBean;
import com.tyt.manager.vo.base.CreditConfigSubUserBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ReturnCodeConstant;
import com.tyt.mybatis.mapper.model.QueryResultModel;
import com.tyt.util.AppConfig;
import com.tyt.util.CsvReader;
import com.tyt.util.FileUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.File;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @create 2023/4/26 15:03
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {
        "classpath:/config/spring/spring-common.xml", "classpath:/config/spring/spring-tytrecommend.xml"})
public class CreditManageConfigServiceImplTest {

    @Resource(name = "creditManageConfigService")
    private CreditManageConfigService creditManageConfigService;

    @Test
    public void save() {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "保存成功");

        //------------------------------首次插入测试---------------------------------------
        CreditConfigRequestBean configBean = new CreditConfigRequestBean();
        configBean.setStartTime("2023-05-16 00:00:00");
        configBean.setEndTime("2023-05-17 23:59:59");
        configBean.setId(185l);

        CreditConfigSubBean configSub01 = new CreditConfigSubBean();
        configSub01.setTargetNumber(25);
        configSub01.setLevel("A");
        configSub01.setIsEnable(1);
        configSub01.setFilePath("111");

        CreditConfigSubBean configSub02 = new CreditConfigSubBean();
        configSub02.setTargetNumber(50);
        configSub02.setLevel("B");
        configSub02.setIsEnable(1);
        configSub02.setFilePath("2222");


        List<CreditConfigSubBean> configSubList = new ArrayList<>();
        configSubList.add(configSub01);
        configSubList.add(configSub02);
        configBean.setCreditSubList(configSubList);


        EmployeeQueryBean cur = new EmployeeQueryBean();
        cur.setRealName("苑小美");
        cur.setId(1000000071l);

        //------------------------------再次更新测试---------------------------------------

//        CreditConfigRequestBean configBean = new CreditConfigRequestBean();
//        configBean.setStartTime("2023-04-01 00:00:00");
//        configBean.setEndTime("2023-04-05 23:59:59");
//        configBean.setId(11l);
//
//        CreditConfigSubBean configSub = new CreditConfigSubBean();
//        configSub.setTargetNumber(40);
//        configSub.setLevel("B");
//        configSub.setIsEnable(2);
//        configSub.setConfigId(1l);
//        configSub.setId(6l);
////      configSub.setFileField();
//
//        List<CreditConfigSubBean> configSubList = new ArrayList<>();
//        configSubList.add(configSub);
//        configBean.setCreditSubList(configSubList);
//
//        EmployeeQueryBean cur = new EmployeeQueryBean();
//        cur.setRealName("刘大帅");
//        cur.setId(10000000711l);
//
        try {
            System.out.println(JSON.toJSONString(configBean));
            ResultMsgBean resultMsgBean = creditManageConfigService.saveCreditManageConfig(configBean, cur, rm);
            System.out.println(resultMsgBean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getCreditConfigList() {
        ResultMsgBean rm = new ResultMsgBean();
        List<CreditConfigBean> creditConfigList = creditManageConfigService.getCreditConfigList(1, 1, 10);
        System.out.println(creditConfigList.size());
        PageInfo<CreditConfigBean> pageInfo = new PageInfo<>(creditConfigList);
        QueryResultModel<CreditConfigBean> queryReuslt = new QueryResultModel<>();
        queryReuslt.setTotal(pageInfo.getTotal());
        queryReuslt.setList(creditConfigList);
        rm.setData(queryReuslt);
        System.out.println(rm.toString());
    }

    @Test
    public void getById() {
        ResultMsgBean rm = new ResultMsgBean();
        CreditConfigBean creditManageConfig = creditManageConfigService.getCreditConfigById(18l);
        rm.setData(creditManageConfig);
        System.out.println(rm.toString());
    }

    @Test
    public void update() {
        ResultMsgBean rm = new ResultMsgBean();
        creditManageConfigService.updateCreditManageConfig(rm,1l, null, 1, "苑秀霞", 12l);
        System.out.println(rm);
    }


    @Test
    public void getCreditConfigUserList() {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        List<CreditManageConfigSub> subIdsByConfigSubs = creditManageConfigService.getCreditSubByConfigId(29l);
        List<CreditConfigSubUserBean>  creditConfigSubUserList = new ArrayList<>();
        for (CreditManageConfigSub creditConfigsub : subIdsByConfigSubs) {

            List<CreditManageConfigUser> creditConfigUserList = creditManageConfigService.getCreditUserList(creditConfigsub.getId(), null, 10, 30);

            System.out.println(JSON.toJSONString(creditConfigUserList));
            PageInfo<CreditManageConfigUser> pageInfo = new PageInfo<>(creditConfigUserList);

            QueryResultModel<CreditManageConfigUser> queryReuslt = new QueryResultModel<>();
            queryReuslt.setTotal(pageInfo.getTotal());
            queryReuslt.setList(creditConfigUserList);

            CreditConfigSubUserBean creditConfigSubUserBean= new CreditConfigSubUserBean();
            creditConfigSubUserBean.setTargetNumber(creditConfigsub.getTargetNumber());
            creditConfigSubUserBean.setLevel(creditConfigsub.getLevel());
            creditConfigSubUserBean.setCreditManageConfigReuslt(queryReuslt);

            creditConfigSubUserList.add(creditConfigSubUserBean);
        }
        rm.setData(creditConfigSubUserList);
        System.out.println(rm);
    }

    @Test
    public void updateCreditUser() {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        creditManageConfigService.updateCreditUser(1l, null, 1, "张三", 12l);
        System.out.println(rm);
    }

    @Test
    public void deleteUser() {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        creditManageConfigService.deleteUserBySubId(1l, "李四三", 12l);
        System.out.println(rm);
    }

    @Test
    public void saveExcelByUserId(){
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        EmployeeQueryBean cur = new EmployeeQueryBean();
        cur.setRealName("刘大帅");
        cur.setId(10000000711l);
        try {
            creditManageConfigService.saveExcelByUserId(1,0,"2323",cur,22l,23l,rm);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




    @Test
    public void getCreditUserList() {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        List<CreditManageConfigUser> creditConfigUserList = creditManageConfigService.getCreditUserList(25l, 1000000268l, 1, 10);
        PageInfo<CreditManageConfigUser> pageInfo = new PageInfo<>(creditConfigUserList);
        QueryResultModel<CreditManageConfigUser> queryReuslt = new QueryResultModel<>();
        queryReuslt.setTotal(pageInfo.getTotal());
        queryReuslt.setList(creditConfigUserList);
        rm.setData(queryReuslt);
        System.out.println(rm);
    }


}

