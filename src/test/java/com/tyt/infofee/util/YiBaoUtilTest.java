package com.tyt.infofee.util;

import junit.framework.TestCase;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/12/1 17:30
 */
public class YiBaoUtilTest extends TestCase {

    @Test
    public void testMapToXml() {

        Map<String, String> data = new HashMap<>();

        data.put("userName", "tes<>t001");
        data.put("age", "1 2");
        data.put("email", "aa<><EMAIL>");

        String xmlTxt = YiBaoUtil.mapToXml(data);

        System.out.println(xmlTxt);

        assertNotNull(xmlTxt);

    }

    @Test
    public void testXml() {
        Map<String, String> result = new HashMap<>();
        String cmd = "TransferSingle";
        String fee_Type = "SOURCR";
        String urgency = "1";
        result.put("cmd", cmd);
        result.put("fee_Type", fee_Type);
        result.put("urgency", urgency);

        String s = YiBaoUtil.mapToXml(result);

        System.out.println(s);

        assertNotNull(s);

    }
}