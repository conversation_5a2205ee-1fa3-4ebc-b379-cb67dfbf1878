package com.tyt.infofee.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.AbstracTest;
import com.tyt.infofee.bean.TransportOrderQueryBean;
import com.tyt.infofee.bean.TransportOrderResultBean;
import com.tyt.infofee.controller.TransportOrderController;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.assertEquals;

public class TransportOrdersServiceImplTest extends AbstracTest {

    @Autowired
    private TransportOrdersService ordersService;

    @Autowired
    private TransportOrderController transportOrderController;

    @Test
    public void moneyIsRecieved() {
        int srcMsgId = 33784613;
        boolean isRecieved = ordersService.moneyIsRecieved((long) srcMsgId);
        assertEquals(isRecieved, true);
    }

    @Test
    public void getOrderList() {

        PageBean pageBean = new PageBean(1, 25);

        TransportOrderQueryBean conditionBean = new TransportOrderQueryBean();
        conditionBean.setEndCreateTime("2021-07-01 23:59:59");
        conditionBean.setStartCreateTime("2024-06-28 00:00:00");
        try {
            List<TransportOrderResultBean> list = ordersService.getOrderList(pageBean, conditionBean);
            System.out.println(JSON.toJSONString(list));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void getInfoFeeListNew(){
        PageBean pageBean = new PageBean();
        Integer pageNo = 1 ;
        pageBean.setPageSize(25);
        TransportOrderQueryBean conditionBean = new TransportOrderQueryBean();
        conditionBean.setEndCreateTime("2024-09-23 00:00:00");
        conditionBean.setStartCreateTime("2021-07-01 23:59:59");
        conditionBean.setTsOrderNo("24091400000002");
        ResultMsgBean infoFeeListNew = transportOrderController.getInfoFeeListNew(pageBean, pageNo, conditionBean, null ,null);
        System.out.println(JSON.toJSONString(infoFeeListNew));

    }
}