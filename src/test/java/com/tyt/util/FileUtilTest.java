package com.tyt.util;

import com.tyt.model.TransportQueryCondition;
import junit.framework.TestCase;
import org.junit.Test;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/12/8 17:22
 */
public class FileUtilTest extends TestCase {

    @Test
    public void testReadFileByLines() throws Exception{

        String filePath = "D:/source-files/test/excel/base64.txt";
        List<TransportQueryCondition> transportQueryConditions = FileUtil.readFileByLines(filePath);

        System.out.println("finished ... ");

        assertTrue(true);

    }

}